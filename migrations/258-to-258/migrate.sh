#!/bin/bash
# migrate a set of projects from one program to another using the project/migrate endpoint

# get current folder of script
script_folder="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
# show DIR

host=""

if [ -z "${1}" ]; then
  echo "provide environment for 1st parameter [local | dev | prod]"
  exit 1
fi

if [ "${1}" == "local" ]; then
  host="http://localhost:8000"
elif [ "${1}" == "dev" ]; then
  host="http://api.dev.internal:9089/mrv-service"
elif [ "${1}" == "dev2" ]; then
  host="http://api.dev2.internal:9089/mrv-service"
elif [ "${1}" == "dev6" ]; then
  host="http://api.dev6.internal:9089/mrv-service"
elif [ "${1}" == "prod" ]; then
  host="http://api.prod.internal:9089/mrv-service"
else
  echo "provide environment for 1st parameter [local | dev | prod]"
  exit 1
fi

echo "host = ${host}"
source_program_id=258
echo "source_program_id = ${source_program_id}"
target_program_id=258
echo "target_program_id = ${target_program_id}"

transformer_name="${source_program_id}to${target_program_id}"

folder="${script_folder}/results"

# create results folder if it does not exist
if [ ! -d ${folder} ]; then
  mkdir ${folder}
fi


migrated_project_id=""

function inspect_project {
   projectid=$1
   type=$2
   userid=$3
   program_id=$4

   # if projectid is empty then return
   if [ -z "${projectid}" ]; then
     return
   fi

   inspect_folder="${folder}/${userid}"
   if [ ! -d ${inspect_folder} ]; then
     mkdir ${inspect_folder}
   fi

   endpoint="${host}/projects/migration/inspect/${projectid}"

   echo endpoint=${endpoint}

   output_json="${inspect_folder}/${projectid}_${type}_${program_id}.json"
   curl -s -H 'accept: application/json' -H 'fs-user-id: 13' -H 'Content-Type: application/json' ${endpoint} | jq > ${output_json}
   echo "$(date -Iseconds) Inspect user_id:${userid} [${projectid}][${type} ] -> ${output_json}"

   output_text="${inspect_folder}/${projectid}_${type}_${program_id}.txt"
   cat ${output_json} | jq -r '.text_block' > ${output_text}
   echo "$(date -Iseconds) Inspect user_id:${userid} [${projectid}][${type} ] -> ${output_text}"
}


function migrate_project {

    projectid=$1
    # if projectid is empty then return
    if [ -z "${projectid}" ]; then
      return
    fi

    type=$2
    userid=$3
    program_id=$4
    remove_existing=$5

    echo type=${type}
    echo userid=${userid}
    echo program_id=${program_id}
    echo remove_existing=${remove_existing}

    inspect_folder="${folder}/${userid}"
    if [ ! -d ${inspect_folder} ]; then
      mkdir ${inspect_folder}
    fi

    endpoint="${host}/projects/migration"

    request_filename="${inspect_folder}/${projectid}_request_${program_id}.json"

    echo "$(date -Iseconds) Migrate user_id:${userid} [${projectid}][request] -> ${request_filename}"
    echo "{" > ${request_filename}
    echo "  \"project_id\": ${projectid}," >> ${request_filename}
    echo "  \"transformer_name\": \"${transformer_name}\"," >> ${request_filename}
    echo "  \"target_program_id\": ${target_program_id}, " >> ${request_filename}
    echo "  \"update_existing\": true, " >> ${request_filename}
    echo "  \"remove_existing\": ${remove_existing}, " >> ${request_filename}
    echo "  \"overwrite_value\": true, " >> ${request_filename}
    echo "  \"repair_target_program\": true " >> ${request_filename}
    echo "}" >> ${request_filename}
    echo "" >> ${request_filename}

    output_raw="${inspect_folder}/${projectid}_migrate_${program_id}.raw"
    echo "$(date -Iseconds) Migrate user_id:${userid} [${projectid}][raw    ] -> ${output_raw}"
    curl -s -X POST \
        -H 'accept: application/json' \
        -H 'fs-user-id: 13' \
        -H 'Content-Type: application/json' \
        -d @${request_filename} ${endpoint} > ${output_raw}

    output_json="${inspect_folder}/${projectid}_migrate_${program_id}.json"
    echo "$(date -Iseconds) Migrate user_id:${userid} [${projectid}][json   ] -> ${output_json}"
    cat ${output_raw} | jq > ${output_json}

    output_text="${inspect_folder}/${projectid}_migrate_${program_id}.txt"
    echo "$(date -Iseconds) Migrate user_id:${userid} [${projectid}][text   ] -> ${output_text}"
    cat ${output_json} | jq -r '.text_block' > ${output_text}

    # need to get the created project id from output_file_name
    migrated_project_id=$(cat ${output_json} | jq -r '.project | .destination')

    echo "$(date -Iseconds) Migrate user_id:${userid} [${projectid}][done   ] -> migrated_project_id: ${migrated_project_id}"

}

function main() {

  echo "Migration of ${source_program_id} to ${target_program_id}"

  for project_set in $(cat ${script_folder}/migrate.txt)
  do

    user_id="$(echo "${project_set}" | cut -d',' -f1)"
    source_program_id="$(echo "${project_set}" | cut -d',' -f2)"
    source_project_id="$(echo "${project_set}" | cut -d',' -f3)"
    remove_existing="$(echo "${project_set}" | cut -d',' -f4)"

    echo user_id=${user_id}
    echo source_program_id=${source_program_id}
    echo source_project_id=${source_project_id}
    echo remove_existing=${remove_existing}

    echo "$(date -Iseconds) Migrate user_id:${user_id} [${source_project_id}][start  ] using ${transformer_name}"

    inspect_project ${source_project_id} "source" ${user_id} ${source_program_id}

    migrated_project_id=""
    migrate_project ${source_project_id} "migrate" ${user_id} ${source_program_id} ${update_existing} ${remove_existing} ${overwrite_value}

    if [ "${migrated_project_id}" == "null" ]; then
        echo ""
        echo "$(date -Iseconds) Migrate user_id:${user_id} [${source_project_id}][retry  ] using ${transformer_name}"
        migrate_project ${source_project_id} "migrate" ${user_id} ${source_program_id} ${update_existing} ${remove_existing} ${overwrite_value}
    fi

    # if migrated_project_id is not empty
    if [ ! -z "${migrated_project_id}" ]; then
      echo "$(date -Iseconds) Migrate user_id:${user_id} [${source_project_id}][inspect] using ${transformer_name}"
      inspect_project ${migrated_project_id} "migrated" ${user_id} ${target_program_id}
    fi


    # we also want to track source and destination project ids
    echo "${source_project_id} (${source_program_id})-> ${migrated_project_id}" >> ${folder}/migrated_projects.txt
    echo "$(date -Iseconds) Migrate user_id:${user_id} [${source_project_id}][done   ] using ${transformer_name} to ${migrated_project_id}"
    echo ""

  done
}

main
