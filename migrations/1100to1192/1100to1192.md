# Migration 1100 to 1192


For this migration, we need to look at and collect data from program 1100.
This means that we will need to perform multiple migrations into the same program.
This should be on a per field basis. If there is every any missing data in any of the
following migrations, just leave it for the producer to fill in.

Step 1: For all producers that have a contract and not a regrow email in program 1100,
migrate name and Cargill ID.


https://regrow.atlassian.net/browse/MRV-2715


```SQL
select
*
from mrv_service.mrv_programs
limit 10;
```


<!-- Todo: Update project ids on these. -->
# Inspect a project

## using curl - source program strcuture
```
curl 'http://api.dev.internal:9089/mrv-service/projects/migration/inspect/14294' \
-H 'accept: application/json' \
-H 'fs-user-id: 13' \
-H 'Content-Type: application/json' \
| jq -r '.text_block'
```

## using curl - target program structure
```
curl 'http://api.dev.internal:9089/mrv-service/projects/migration/inspect/14869' \
-H 'accept: application/json' \
-H 'fs-user-id: 13' \
-H 'Content-Type: application/json' \
| jq -r '.text_block'
```




# Query to list all projects to migrate
For all producers that have a contract and not a regrow.ag email…

see file `migrate_full.txt`, with a list of all id's that will be used in the migration

```SQL local
select
    mp.id as project_id,
    mupp.user as user_id,
    u.email as user_email,
    mpc.id as contract_id,
    mpc.docusign_status as contract_status
from mrv_projects mp
left join mrv_user_project_permissions mupp on mupp.project = mp.id
left join users u on u.id = mupp.user
left join mrv_project_contracts mpc on mpc.project = mp.id
where mp.program_id = 1100
and mp.deleted_at is null
and mupp.deleted_at is null
and (u.email not like "%@regrow.ag" and  u.email not like "%flurosat%")
and mpc.deleted_at is null
and mpc.docusign_status = 'completed'
order by mp.id
```
