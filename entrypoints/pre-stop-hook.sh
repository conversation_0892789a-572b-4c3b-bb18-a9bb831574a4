#!/bin/bash

# k8s pre-stop hook: blocks k8s from sending SIGTER<PERSON>, until no more tasks are running.

set -e

echo "# k8s pre-stop hook: checking for active + scheduled on the current worker..."

until celery -A celery_worker inspect -t 120 active --json --destination "celery@$(hostname)" | tee | grep -F "[]" &&
    celery -A celery_worker inspect -t 120 scheduled --json --destination "celery@$(hostname)" | tee | grep -F "[]"; do
    echo "# waiting for celery worker (active+scheduled) to finish..."
    sleep 60
done

echo "# safe to stop container for worker celery@$(hostname)"
