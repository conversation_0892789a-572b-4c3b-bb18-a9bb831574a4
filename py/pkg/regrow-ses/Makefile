# Semantic versioning targets (auto-detects branch type)

# Version bumping (auto-detects branch type)
bump-patch:
	@echo "Bumping patch version (auto-detects branch type)"
	@./bump-version.sh patch

bump-minor:
	@echo "Bumping minor version (auto-detects branch type)"
	@./bump-version.sh minor

bump-major:
	@echo "Bumping major version (auto-detects branch type)"
	@./bump-version.sh major

bump-dev:
	@echo "Incrementing development version number"
	@./bump-version.sh dev

bump-prod:
	@echo "Converting development version to production release"
	@./bump-version.sh prod

# Utility targets
version:
	@echo "Current version: $$(grep 'current_version' .bumpversion.cfg | cut -d' ' -f3)"
	@echo "Current branch: $$(git branch --show-current)"

help-version:
	@echo "Smart Version Bumping (auto-detects branch type):"
	@echo ""
	@echo "  make bump-patch     # Bug fixes (0.1.60 → 0.1.61 or 0.1.61.dev1)"
	@echo "  make bump-minor     # New features (0.1.60 → 0.2.0 or 0.2.0.dev1)"
	@echo "  make bump-major     # Breaking changes (0.1.60 → 1.0.0 or 1.0.0.dev1)"
	@echo "  make bump-dev       # Increment dev number (0.1.61.dev1 → 0.1.61.dev2)"
	@echo "  make bump-prod      # Convert dev to production (0.1.61.dev1 → 0.1.61)"
	@echo ""
	@echo "Branch Detection:"
	@echo "  • main branch    → Production tag (no file commits - branch protection compatible)"
	@echo "  • other branches → Development version (commits files)"
	@echo ""
	@echo "Production Release Process:"
	@echo "  • Creates tag only on main branch (no file changes)"
	@echo "  • GitHub workflow handles version updates during publishing"
	@echo "  • Compatible with branch protection rules"
	@echo ""
	@echo "Direct script usage:"
	@echo "  ./bump-version.sh patch    # Same as make bump-patch"
	@echo "  ./bump-version.sh --help   # Show detailed help"

.PHONY: bump-dev bump-prod bump-patch bump-minor bump-major version help-version
