#!/bin/bash

# Smart Version Bumping Script for ses-client
# Automatically detects branch type and creates appropriate version
# Usage: ./bump-version.sh --type [patch|minor|major|dev] [--force]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [patch|minor|major|dev|prod]"
    echo ""
    echo "Arguments:"
    echo "  patch    Bump patch version (bug fixes)"
    echo "  minor    Bump minor version (new features)"
    echo "  major    Bump major version (breaking changes)"
    echo "  dev      Increment development version number (dev branches only)"
    echo "  prod     Convert development version to production (main branch only)"
    echo ""
    echo "Examples:"
    echo "  $0 patch    # Auto-detects branch type"
    echo "  $0 minor    # Auto-detects branch type"
    echo "  $0 dev      # Increment dev number (feature branches only)"
    echo "  $0 prod     # Convert dev to production (main branch only)"
    echo ""
    echo "Branch Detection:"
    echo "  • main branch    → Production version (1.2.0 → 1.2.1)"
    echo "  • other branches → Development version (1.2.0 → 1.2.1.dev1)"
}

# Parse command line arguments
if [[ $# -eq 0 ]]; then
    print_error "Missing required argument"
    show_usage
    exit 1
fi

if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    show_usage
    exit 0
fi

VERSION_TYPE="$1"

if [[ ! "$VERSION_TYPE" =~ ^(major|minor|patch|dev|prod)$ ]]; then
    print_error "Invalid version type: $VERSION_TYPE"
    print_error "Must be one of: major, minor, patch, dev, prod"
    show_usage
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f ".bumpversion.cfg" ]]; then
    print_error "This script must be run from the regrow-ses directory (py/pkg/regrow-ses)"
    exit 1
fi

# Check if bump2version is installed
if ! command -v bump2version &> /dev/null; then
    print_error "bump2version is not installed. Install it with: poetry install"
    exit 1
fi

# Get current branch and version
CURRENT_BRANCH=$(git branch --show-current)
CURRENT_VERSION=$(grep "current_version" .bumpversion.cfg | cut -d' ' -f3)
IS_MAIN_BRANCH=false
IS_DEV_VERSION=false

if [[ "$CURRENT_BRANCH" == "main" ]]; then
    IS_MAIN_BRANCH=true
fi

if [[ "$CURRENT_VERSION" == *".dev"* ]]; then
    IS_DEV_VERSION=true
fi

print_info "Current branch: $CURRENT_BRANCH"
print_info "Current version: $CURRENT_VERSION"

# Determine operation mode
if [[ "$IS_MAIN_BRANCH" == true ]]; then
    # Main branch - production releases only
    if [[ "$VERSION_TYPE" == "dev" ]]; then
        if [[ "$IS_DEV_VERSION" == false ]]; then
            print_error "Cannot create development versions on main branch"
            print_info "Use 'make bump-dev' on feature branches to increment dev versions"
        else
            print_error "Cannot increment development versions on main branch"
            print_info "Use 'make bump-prod' on main branch to convert dev version to production"
        fi
        exit 1
    fi

    if [[ "$VERSION_TYPE" == "prod" ]]; then
        if [[ "$IS_DEV_VERSION" == false ]]; then
            print_error "Cannot convert to production - current version is not a development version"
            print_info "Current version: $CURRENT_VERSION"
            print_info "Use 'make bump-patch/minor/major' to create a new production version"
            exit 1
        fi
    fi

    # Check if main is up to date
    git fetch origin main
    if [[ $(git rev-parse HEAD) != $(git rev-parse origin/main) ]]; then
        print_error "Your main branch is not up to date with origin/main"
        print_info "Please pull the latest changes: git pull origin main"
        exit 1
    fi

    OPERATION_MODE="production"
    if [[ "$VERSION_TYPE" == "prod" || ("$IS_DEV_VERSION" == true && "$VERSION_TYPE" != "prod") ]]; then
        print_info "Converting development version to production release"
        BUMP_TYPE="dev"  # This will remove the .devX suffix
    else
        BUMP_TYPE="$VERSION_TYPE"
    fi
else
    # Feature branch - development releases
    if [[ "$VERSION_TYPE" == "prod" ]]; then
        print_error "Cannot create production releases on feature branches"
        print_info "Use 'make bump-prod' on main branch to convert dev version to production"
        exit 1
    fi

    if [[ "$VERSION_TYPE" == "dev" ]]; then
        if [[ "$IS_DEV_VERSION" == false ]]; then
            print_error "Current version is not a development version"
            print_info "Use patch/minor/major to create a new development version"
            exit 1
        fi
        OPERATION_MODE="development-increment"
        BUMP_TYPE="devnum"
    else
        OPERATION_MODE="development-new"
        BUMP_TYPE="$VERSION_TYPE"
    fi
fi

# Show what will happen
echo ""
case $OPERATION_MODE in
    "production")
        print_info "🚀 PRODUCTION RELEASE MODE (Tag-Only)"
        if [[ "$IS_DEV_VERSION" == true ]]; then
            print_info "Converting development version to production release"
            print_info "Will create tag: py-regrow-ses-v$(echo "$CURRENT_VERSION" | sed 's/\.dev[0-9]*$//')"
        else
            case $VERSION_TYPE in
                "patch") print_info "Creating patch release (bug fixes)" ;;
                "minor") print_info "Creating minor release (new features)" ;;
                "major") print_warning "Creating major release (breaking changes)" ;;
            esac
        fi
        print_info "Will publish to: PRODUCTION environment"
        print_info "Note: Creates tag only - no file commits (branch protection compatible)"
        ;;
    "development-new")
        print_info "🧪 DEVELOPMENT VERSION MODE"
        case $VERSION_TYPE in
            "patch") print_info "Creating development patch version" ;;
            "minor") print_info "Creating development minor version" ;;
            "major") print_warning "Creating development major version" ;;
        esac
        print_info "Will publish to: DEVELOPMENT environment"
        ;;
    "development-increment")
        print_info "🧪 DEVELOPMENT INCREMENT MODE"
        print_info "Incrementing development version number"
        print_info "Will publish to: DEVELOPMENT environment"
        ;;
esac

# Confirmation
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Version bump cancelled"
    exit 0
fi

# Check working directory cleanliness for production releases
if [[ "$OPERATION_MODE" == "production" ]] && [[ -n $(git status --porcelain) ]]; then
    print_warning "Working directory is not clean. The following files have changes:"
    git status --porcelain
    echo ""
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Version bump cancelled"
        exit 0
    fi
fi

# Execute version bump
if [[ "$OPERATION_MODE" == "production" ]]; then
    # Production release - create tag only (no file commits)
    print_info "Creating production tag without file commits..."

    if [[ "$IS_DEV_VERSION" == true ]]; then
        # Convert dev version to production (e.g., 0.1.61.dev1 → 0.1.61)
        NEW_VERSION=$(echo "$CURRENT_VERSION" | sed 's/\.dev[0-9]*$//')
    else
        # Calculate new production version
        case $VERSION_TYPE in
            "patch")
                NEW_VERSION=$(bump2version --dry-run --list patch | grep new_version= | cut -d= -f2)
                ;;
            "minor")
                NEW_VERSION=$(bump2version --dry-run --list minor | grep new_version= | cut -d= -f2)
                ;;
            "major")
                NEW_VERSION=$(bump2version --dry-run --list major | grep new_version= | cut -d= -f2)
                ;;
        esac
    fi

    # Create tag directly without committing file changes
    TAG_NAME="py-regrow-ses-v$NEW_VERSION"
    if git tag "$TAG_NAME"; then
        print_success "Production tag created: $CURRENT_VERSION → $NEW_VERSION"
        print_info "Tag: $TAG_NAME"
    else
        print_error "Failed to create tag $TAG_NAME"
        exit 1
    fi

elif [[ "$OPERATION_MODE" == "development-new" ]]; then
    # Create new development version
    print_info "Running bump2version $BUMP_TYPE..."
    NEW_BASE_VERSION=$(bump2version --dry-run --list "$BUMP_TYPE" | grep new_version= | cut -d= -f2)
    if bump2version "$BUMP_TYPE" --new-version="${NEW_BASE_VERSION}.dev1"; then
        NEW_VERSION=$(grep "current_version" .bumpversion.cfg | cut -d' ' -f3)
        print_success "Development version created: $CURRENT_VERSION → $NEW_VERSION"
    else
        print_error "bump2version failed"
        exit 1
    fi
else
    # Standard bump (development increment)
    print_info "Running bump2version $BUMP_TYPE..."
    if bump2version "$BUMP_TYPE"; then
        NEW_VERSION=$(grep "current_version" .bumpversion.cfg | cut -d' ' -f3)
        print_success "Version bumped: $CURRENT_VERSION → $NEW_VERSION"
    else
        print_error "bump2version failed"
        exit 1
    fi
fi

# Show what was changed
echo ""
if [[ "$OPERATION_MODE" == "production" ]]; then
    print_info "Production release summary:"
    echo "  - Tag created: py-regrow-ses-v$NEW_VERSION"
    echo "  - No file changes committed (GitHub workflow will handle version updates)"

    print_info "Next steps:"
    echo "  1. Push the tag: git push origin py-regrow-ses-v$NEW_VERSION"
    echo "  2. GitHub Actions will publish to PRODUCTION environment"
    echo "  3. The workflow will automatically set version $NEW_VERSION in pyproject.toml during build"
else
    print_info "Files updated:"
    echo "  - .bumpversion.cfg"
    echo "  - pyproject.toml"
    echo "  - README.md"

    print_info "Git commit created with tag: py-regrow-ses-v$NEW_VERSION"

    print_info "Next steps:"
    echo "  1. Push changes and tag: git push --follow-tags"
    echo "  2. GitHub Actions will publish to DEVELOPMENT environment"
    echo "  3. Create a PR to merge into main"
    echo "  4. After PR approval, create production release from main"
fi
