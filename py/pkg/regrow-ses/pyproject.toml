[tool.poetry]
name = "regrow-ses"
version = "0.1.63.dev2"
description = ""
authors = ["<PERSON> <mi<PERSON><PERSON>@regrow.ag>"]

# Include the main directories explicitly
packages = [
    { include = "regrow" }
]

# Include the generated Protobuf files
include = [
    "regrow/ses/**/*",
    "buf/validate/**/*"
]

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
grpcio = "*"
geojson = "*"
grpcio-health-checking = "*"

[tool.poetry.group.dev.dependencies]
bump2version = "^1.0.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
