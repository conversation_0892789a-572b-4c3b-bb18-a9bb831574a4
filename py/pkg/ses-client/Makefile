
unit-test:
	@echo "Running unit tests"
	@poetry run pytest -m "not integration"

unit-tests: unit-test

clean-docker:
	@echo "Tearing down ses stack..."
	@docker compose down --volumes

docker-clean: clean-docker

clean: clean-docker

integration-test: clean unit-test
	@echo "Running integration tests"
	@echo "Bringing up ses stack..."
	@docker compose up -d
	@echo "Running integration tests..."
	@poetry run pytest -s -m integration
	@echo "Tearing down ses stack..."
	@docker compose down --volumes

integration-tests: clean integration-test

integration-test-rebuild: unit-test
	@echo "Running integration tests"
	@echo "Bringing up ses stack..."
	@docker compose up --build -d
	@echo "Running integration tests..."
	@poetry run pytest -s -m integration
	@echo "Tearing down ses stack..."
	@docker compose down --volumes

integration-tests-rebuild: clean integration-test-rebuild

test: integration-test

test-rebuild: integration-test-rebuild

# Semantic versioning targets (auto-detects branch type)

# Version bumping (auto-detects branch type)
bump-patch:
	@echo "Bumping patch version (auto-detects branch type)"
	@./bump-version.sh patch

bump-minor:
	@echo "Bumping minor version (auto-detects branch type)"
	@./bump-version.sh minor

bump-major:
	@echo "Bumping major version (auto-detects branch type)"
	@./bump-version.sh major

bump-dev:
	@echo "Incrementing development version number"
	@./bump-version.sh dev

bump-prod:
	@echo "Converting development version to production release"
	@./bump-version.sh prod

# Utility targets
version:
	@echo "Current version: $$(grep 'current_version' .bumpversion.cfg | cut -d' ' -f3)"
	@echo "Current branch: $$(git branch --show-current)"

help-version:
	@echo "Smart Version Bumping (auto-detects branch type):"
	@echo ""
	@echo "  make bump-patch     # Bug fixes (1.2.0 → 1.2.1 or 1.2.1.dev1)"
	@echo "  make bump-minor     # New features (1.2.0 → 1.3.0 or 1.3.0.dev1)"
	@echo "  make bump-major     # Breaking changes (1.2.0 → 2.0.0 or 2.0.0.dev1)"
	@echo "  make bump-dev       # Increment dev number (1.2.1.dev1 → 1.2.1.dev2)"
	@echo "  make bump-prod      # Convert dev to production (1.2.1.dev1 → 1.2.1)"
	@echo ""
	@echo "Branch Detection:"
	@echo "  • main branch    → Production tag (no file commits - branch protection compatible)"
	@echo "  • other branches → Development version (commits files)"
	@echo ""
	@echo "Production Release Process:"
	@echo "  • Creates tag only on main branch (no file changes)"
	@echo "  • GitHub workflow handles version updates during publishing"
	@echo "  • Compatible with branch protection rules"
	@echo ""
	@echo "Direct script usage:"
	@echo "  ./bump-version.sh patch    # Same as make bump-patch"
	@echo "  ./bump-version.sh --help   # Show detailed help"

.PHONY: bump-dev bump-prod bump-patch bump-minor bump-major version help-version
