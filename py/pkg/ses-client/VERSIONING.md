# Semantic Versioning Guide for ses-client

This document outlines the semantic versioning strategy and best practices for the `ses-client` package.

## Overview

We use [Semantic Versioning (SemVer)](https://semver.org/) for the `ses-client` package. Version numbers follow the format `MAJOR.MINOR.PATCH`:

- **MAJOR**: Incremented for incompatible API changes
- **MINOR**: Incremented for backward-compatible functionality additions
- **PATCH**: Incremented for backward-compatible bug fixes

## Current Setup

### Version Types
- **Development versions**: `1.2.1.dev1` - For testing in feature branches
- **Production versions**: `1.2.1` - For releases from main branch

### Tools Used
- **bump2version**: Automates version bumping across multiple files
- **GitHub Actions**: Automatically publishes to Google Artifact Registry on tag push
- **Poetry**: Package management and publishing

### Files Tracked
The following files are automatically updated when bumping versions:
- `.bumpversion.cfg` - Configuration and current version
- `pyproject.toml` - Package version
- `README.md` - Documentation version references

### Environment Mapping
- **Development tags** (`*.dev*`): Published to `dev` environment
- **Production tags**: Published to `prod` environment

## Usage

### Smart Version Bumping (Auto-Detection)
The script automatically detects your branch and creates the appropriate version:

```bash
# Auto-detects branch type and creates appropriate version
make bump-patch        # main: 1.2.0 → 1.2.1 | feature: 1.2.0 → 1.2.1.dev1
make bump-minor        # main: 1.2.0 → 1.3.0 | feature: 1.2.0 → 1.3.0.dev1
make bump-major        # main: 1.2.0 → 2.0.0 | feature: 1.2.0 → 2.0.0.dev1

# Development-specific commands
make bump-dev          # Increment dev number: 1.2.1.dev1 → 1.2.1.dev2
```

### Manual Script Usage
Use the unified script directly with simple arguments:

```bash
# Auto-detects branch type
./bump-version.sh patch
./bump-version.sh minor
./bump-version.sh major

# Development increment (feature branches only)
./bump-version.sh dev

# Show help
./bump-version.sh --help
```

## Version Bump Guidelines

### When to Bump PATCH (1.2.0 → 1.2.1)
- Bug fixes
- Documentation updates
- Internal refactoring without API changes
- Performance improvements without API changes

### When to Bump MINOR (1.2.0 → 1.3.0)
- New features that are backward compatible
- New methods or classes
- New optional parameters
- Deprecating features (but not removing them)

### When to Bump MAJOR (1.2.0 → 2.0.0)
- Breaking API changes
- Removing deprecated features
- Changing method signatures
- Changing return types
- Changing behavior of existing methods

## Release Process

### Development Workflow (Feature Branches)

1. **Create and set up feature branch**:
   ```bash
   git checkout -b feature/your-feature
   git push -u origin feature/your-feature  # -u sets up tracking
   ```

2. **Make your changes** and commit normally:
   ```bash
   # Make code changes, then commit
   git add .
   git commit -m "Add new feature functionality"
   git push  # Simple push to tracking branch
   ```

3. **Create development version** when ready for testing:
   ```bash
   make bump-patch  # Auto-detects branch, creates dev version
   git push --follow-tags  # Pushes commit + tag together
   ```

4. **Development version published to dev environment** automatically
5. **Create PR** to merge into main
6. **After PR approval and merge**, create production release

### Production Release (Main Branch Only)

1. **Switch to main and update**:
   ```bash
   git checkout main
   git pull  # Pull latest changes after PR merge
   ```

2. **Create production version**:
   ```bash
   make bump-patch  # Auto-detects main branch, creates prod version
   git push --follow-tags  # Pushes commit + tag together
   ```

3. **Production version published to prod environment** automatically

### Understanding Git Tags vs Branches

**What happens during version bumping:**
- `make bump-patch` creates a **git commit** with version changes
- It also creates a **git tag** (e.g., `py-ses-client-v1.2.1.dev1`) pointing to that commit
- **Tags** are like bookmarks for specific versions
- **Branches** are moving pointers for ongoing development

**Why we push tags:**
- GitHub workflows trigger on **tag pushes**, not branch pushes
- Tag push → Triggers artifact publishing to GAR
- Branch push → Enables code review via PR

**Push options:**
```bash
# Option 1: Push separately (more control)
git push                                    # Push branch commits
git push origin py-ses-client-v1.2.1.dev.1 # Push specific tag

# Option 2: Push together (simpler)
git push --follow-tags                      # Push branch + related tags

# Option 3: Push all tags (use with caution)
git push --tags                             # Push branch + all tags
```

### Key Benefits of This Workflow

- **Tracking branches** - simple `git push` without specifying remote/branch
- **No separate commands** for dev vs prod - same command works everywhere
- **Branch validation** - prevents mistakes like dev versions on main
- **Automatic environment targeting** - dev versions go to dev, prod to prod
- **Smart version handling** - converts dev versions to prod when merging
- **One command publishing** - `git push --follow-tags` handles everything

## Complete Example Workflow

### Scenario: Adding a new feature with development testing

```bash
# 1. Create feature branch with tracking
git checkout -b feature/add-user-auth
git push -u origin feature/add-user-auth

# 2. Develop normally
# ... make code changes ...
git add .
git commit -m "Implement user authentication"
git push

# 3. Ready for dev testing? Create dev version
make bump-patch
# ✅ Creates: py-ses-client-v1.2.1.dev1 (development version)

git push --follow-tags
# ✅ Publishes to DEV environment automatically

# 4. Need another iteration? Just increment dev version
# ... make more changes ...
git add .
git commit -m "Fix auth validation"
git push

make bump-dev  # Increment dev number
# ✅ Creates: py-ses-client-v1.2.1.dev2

git push --follow-tags
# ✅ Publishes new dev version

# 5. Create PR when ready
# ... PR review and approval ...

# 6. After PR merge - create production release
git checkout main
git pull

make bump-patch
# ✅ Creates: py-ses-client-v1.2.1 (production tag only - no file commits)

git push origin py-ses-client-v1.2.1
# ✅ Publishes to PRODUCTION environment
```

### Result Summary

- **Development versions**: `1.2.1.dev1`, `1.2.1.dev2` → Published to DEV (commits files)
- **Production version**: `1.2.1` → Published to PROD (tag only)
- **Same commands** work on any branch - script auto-detects context
- **Branch protection compatible** - production releases don't commit files

## Automation

### GitHub Workflow Integration
The `publish-py-ses-client.yml` workflow is triggered by:
- Tags matching `py-ses-client-v*.*.*` pattern
- Automatically publishes to production environment

### Tag Format
Tags follow the format: `py-ses-client-v<version>`
- **Production**: `py-ses-client-v1.2.1`
- **Development**: `py-ses-client-v1.2.1.dev1`

This allows for component-specific versioning in the monorepo.

## Branch Protection Compatibility

### The Problem
GitHub branch protection rules prevent direct commits to the `main` branch, but traditional bump2version workflows require committing version file changes. This creates a conflict for production releases.

### The Solution
Our production release workflow creates **tags only** without committing file changes:

1. **Feature branches**: Normal bump2version behavior (commits + tags)
2. **Main branch**: Tag-only releases (no file commits)
3. **GitHub workflows**: Dynamically set versions during publishing

### How It Works

**Development Workflow (Feature Branches)**:
```bash
# On feature branch - commits files and creates tag
make bump-patch  # Creates 1.2.1.dev1, commits .bumpversion.cfg and pyproject.toml
git push --follow-tags
```

**Production Workflow (Main Branch)**:
```bash
# On main branch - creates tag only (no file commits)
make bump-prod   # Creates py-ses-client-v1.2.1 tag, no file changes
git push origin py-ses-client-v1.2.1
```

**Publishing Process**:
- GitHub workflow detects the tag
- Workflow dynamically sets version in `pyproject.toml` during build
- Package is published with correct version to Google Artifact Registry

### Benefits
- ✅ Compatible with branch protection rules
- ✅ No merge conflicts from version file changes
- ✅ Maintains semantic versioning consistency
- ✅ Preserves existing development workflow
- ✅ Leverages existing GitHub workflow logic

## Best Practices

### Pre-Release Checklist
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] Breaking changes are documented
- [ ] CHANGELOG.md is updated (if you maintain one)

### Commit Messages
bump2version automatically creates commits with the format:
```
chore(ses-client): bump version from 1.2.0 to 1.2.1
```

### Branch Strategy
- Develop on feature branches with development versions (commits files)
- Merge to `main` via pull requests (no version changes in PR)
- Production releases on `main` branch create tags only (no file commits)
- Branch protection rules are fully supported

## Troubleshooting

### Common Issues

**bump2version fails with "working directory not clean"**
- Commit or stash your changes first
- Script will prompt for confirmation on dirty working directory

**Tag already exists**
- Check existing tags: `git tag -l "py-ses-client-v*"`
- Delete local tag: `git tag -d py-ses-client-v1.2.1`
- Delete remote tag: `git push origin :refs/tags/py-ses-client-v1.2.1`

**Workflow doesn't trigger**
- Ensure tag was pushed: `git ls-remote --tags origin`
- Check tag format matches workflow pattern
- Verify tag includes dev suffix for dev versions

**Branch not tracking remote**
- Set up tracking: `git push -u origin your-branch-name`
- Or set tracking for existing branch: `git branch --set-upstream-to=origin/your-branch`

**Forgot to push tag**
- Check unpushed tags: `git tag -l` vs `git ls-remote --tags origin`
- Push specific tag: `git push origin py-ses-client-v1.2.1`
- Push all tags: `git push --tags` (use with caution)

### Manual Recovery
If something goes wrong, you can manually fix:

1. **Reset version in files**:
   ```bash
   # Edit .bumpversion.cfg, pyproject.toml, README.md manually
   ```

2. **Remove unwanted tag**:
   ```bash
   git tag -d ses-client-v1.2.1
   git push origin :refs/tags/ses-client-v1.2.1
   ```

3. **Re-run bump2version**:
   ```bash
   poetry run bump2version patch --allow-dirty
   ```

## Configuration Reference

The `.bumpversion.cfg` file contains:
- Current version tracking
- File patterns to update
- Commit and tag settings
- Version format parsing

Key settings:
- `commit = True`: Automatically create git commits
- `tag = True`: Automatically create git tags
- `tag_name = ses-client-v{new_version}`: Tag naming format
