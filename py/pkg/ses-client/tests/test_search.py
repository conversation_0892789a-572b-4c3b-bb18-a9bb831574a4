import asyncio
from datetime import datetime, timezone

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from ses_client import client, event, pb_value, search
from ses_client.application import ApplicationBroadcast
from ses_client.crop import HarvestedCrop, SownCrop
from ses_client.event import StructuredEvent
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.sowing import SowingActivity
from ses_client.tillage import TillageActivity

from tests import testdata
from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_search_addr,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


@pytest.mark.integration
async def test_fetch_single_event():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_id = event.new_event_id()
    event_datetime = random_datetime()
    field_geom = random_polygon()
    ta = (
        TillageActivity(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_centimetres(3))
    )
    _ = await event_client.upsert_event(ta)

    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_search_single_event():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    search_client = search.Search(event_search_addr)

    # create a new event
    event_id = event.new_event_id()
    event_datetime = random_datetime()
    field_geom = random_polygon()
    ta = (
        TillageActivity(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_centimetres(3))
    )

    # Upsert an event and add an entry to the search cache
    res = await event_client.upsert_event(ta)
    short_event = event._short_event(res.event)
    field_md5 = testdata.random_md5()
    await search_client._upsert_field_events(field_md5, [short_event])

    # search for the event (via search cache)
    search_results = await search_client.field_events(
        field_ids=[field_md5], return_type=search.Search.RETURN_EVENT_IDS
    )
    assert len(search_results[field_md5]) == 1
    assert field_md5 in search_results


@pytest.mark.integration
async def test_search_archive_filter():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    search_client = search.Search(event_search_addr)

    # create a new event
    event_id = event.new_event_id()
    event_datetime = random_datetime()
    field_geom = random_polygon()
    ta = (
        TillageActivity(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_centimetres(3))
    )
    _ = await event_client.upsert_event(ta)
    _ = await event_client.archive_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )

    # refetch it so we can add to the cache
    se = await event_client.fetch_event(event_id)
    short_event = event._short_event(se)
    field_md5 = testdata.random_md5()
    await search_client._upsert_field_events(field_md5, [short_event])

    # search for the event (via search cache) with archive filter
    search_filter = search.Filter(exclude_archived=False)
    search_results = await search_client.field_events(
        field_ids=[field_md5],
        search_filter=search_filter,
        return_type=search.Search.RETURN_EVENT_IDS,
    )
    assert len(search_results[field_md5]) == 1
    assert field_md5 in search_results

    # search for the event (via search cache) excluding archived records
    search_filter = search.Filter(exclude_archived=True)
    search_results = await search_client.field_events(
        field_ids=[field_md5],
        search_filter=search_filter,
        return_type=search.Search.RETURN_EVENT_IDS,
    )
    assert len(search_results[field_md5]) == 0


@pytest.mark.integration
async def test_search_event_sequence():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    search_client = search.Search(event_search_addr)

    # a single field
    field_md5 = testdata.random_md5()
    field_geom = random_polygon()

    # a tillage activity
    tillage_activity_id = event.new_event_id()
    tillage_activity_datetime = datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc)
    tillage_activity = (
        TillageActivity(event_id=tillage_activity_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(tillage_activity_datetime)
        .depth(pb_value.depth_centimetres(3))
        .validate()
    )
    res = await event_client.upsert_event(tillage_activity)
    tillage_short_event = event._short_event(res.event)

    # an application activity
    application_activity_id = event.new_event_id()
    application_activity_datetime = datetime(2021, 1, 15, 0, 0, tzinfo=timezone.utc)
    application_activity = (
        ApplicationBroadcast(event_id=application_activity_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(application_activity_datetime)
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )
    res = await event_client.upsert_event(application_activity)
    application_short_event = event._short_event(res.event)

    # a sowing activity
    sowing_activity_id = event.new_event_id()
    sowing_activity_datetime = datetime(2021, 1, 30, 0, 0, tzinfo=timezone.utc)
    sowing_activity = (
        SowingActivity(event_id=sowing_activity_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(sowing_activity_datetime)
        .crop(SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA))
        .validate()
    )
    res = await event_client.upsert_event(sowing_activity)
    sowing_short_event = event._short_event(res.event)

    # a harvest activity
    harvest_activity_id = event.new_event_id()
    harvest_activity_datetime = datetime(2021, 2, 15, 0, 0, tzinfo=timezone.utc)
    harvest_activity = (
        HarvestActivity(event_id=harvest_activity_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(harvest_activity_datetime)
        .crop(HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA))
        .validate()
    )
    res = await event_client.upsert_event(harvest_activity)
    harvest_short_event = event._short_event(res.event)

    # add short events to search cache
    short_events = [
        tillage_short_event,
        application_short_event,
        sowing_short_event,
        harvest_short_event,
    ]
    await search_client._upsert_field_events(field_md5, short_events)

    # search for the events and verify return order in time sequence
    search_results = await search_client.field_events(
        field_ids=[field_md5], return_type=search.Search.RETURN_EVENT_IDS
    )
    assert search_results[field_md5] == [
        tillage_activity_id,
        application_activity_id,
        sowing_activity_id,
        harvest_activity_id,
    ]

    # apply a search filter to return only events in January
    search_filter = search.Filter(
        interval_from=datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc),
        interval_to=datetime(2021, 1, 31, 23, 59, tzinfo=timezone.utc),
    )
    search_results = await search_client.field_events(
        field_ids=[field_md5],
        search_filter=search_filter,
        return_type=search.Search.RETURN_EVENT_IDS,
    )
    assert len(search_results[field_md5]) == 3
    assert search_results[field_md5] == [
        tillage_activity_id,
        application_activity_id,
        sowing_activity_id,
    ]

    # apply a search filter to return only sowing and harvest
    search_filter = search.Filter(
        event_types=[
            StructuredEvent.TYPE_SOWING_ACTIVITY,
            StructuredEvent.TYPE_HARVEST_ACTIVITY,
        ]
    )
    search_results = await search_client.field_events(
        field_ids=[field_md5],
        search_filter=search_filter,
        return_type=search.Search.RETURN_EVENT_IDS,
    )
    assert search_results[field_md5] == [sowing_activity_id, harvest_activity_id]


@pytest.mark.integration
async def test_events_for_fields():
    """Integration test for fetching events for fields."""
    event_client = client.Client(testdata.event_srvc_addr, testdata.event_search_addr)
    search_client = search.Search(event_search_addr)
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    fallow_id = event.new_event_id()
    fallow_start_date = datetime(2020, 1, 1, 0, 0)
    fallow_end_date = datetime(2020, 12, 31, 23, 59)
    fallow = testdata.fallow_period(
        fallow_id, field_geom, fallow_start_date, fallow_end_date
    )
    res = await event_client.upsert_event(fallow)
    fallow_short_event = event._short_event(res.event)

    tillage_id = event.new_event_id()
    tillage_date = datetime(2021, 1, 1, 0, 0)
    tillage = testdata.tillage_activity(
        tillage_id, field_geom, tillage_date, depth_cm=3.5
    )
    res = await event_client.upsert_event(tillage)
    tillage_short_event = event._short_event(res.event)

    sowing_id = event.new_event_id()
    sowing_date = datetime(2021, 1, 2, 0, 0)
    sowing = testdata.sowing_alfalfa(sowing_id, field_geom, sowing_date)
    res = await event_client.upsert_event(sowing)
    sowing_short_event = event._short_event(res.event)

    irrigation_id = event.new_event_id()
    irrigation_date = datetime(2021, 1, 3, 0, 0)
    irrigation = testdata.irrigation_activity(
        irrigation_id, field_geom, irrigation_date, depth_mm=10
    )
    res = await event_client.upsert_event(irrigation)
    irrigation_short_event = event._short_event(res.event)

    harvest_id = event.new_event_id()
    harvest_date = datetime(2021, 6, 1, 0, 0)
    harvest = testdata.harvest_alfalfa(harvest_id, field_geom, harvest_date)
    res = await event_client.upsert_event(harvest)
    harvest_short_event = event._short_event(res.event)

    await search_client._upsert_field_events(
        field_md5=field_md5,
        field_events=[
            fallow_short_event,
            tillage_short_event,
            sowing_short_event,
            irrigation_short_event,
            harvest_short_event,
        ],
    )

    # Fetch events for the field
    res = await event_client.fetch_events_for_fields(
        field_ids=[field_md5],
        search_filter=search.Filter(
            interval_from=datetime(2020, 1, 1, 0, 0),
            interval_to=datetime(2021, 12, 31, 23, 59),
        ),
    )
    assert field_md5 in res
    assert len(res[field_md5]) == 5


@pytest.mark.integration
async def test_events_for_fields_with_context():
    """Integration test for fetching events for fields including their context."""
    event_client = client.Client(testdata.event_srvc_addr, testdata.event_search_addr)
    search_client = search.Search(event_search_addr)
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    fallow_id = event.new_event_id()
    fallow_start_date = datetime(2020, 1, 1, 0, 0)
    fallow_end_date = datetime(2020, 12, 31, 23, 59)
    fallow = testdata.fallow_period(
        fallow_id, field_geom, fallow_start_date, fallow_end_date
    )
    res = await event_client.upsert_event(fallow)
    fallow_short_event = event._short_event(res.event)

    tillage_id = event.new_event_id()
    tillage_date = datetime(2021, 1, 1, 0, 0)
    tillage = testdata.tillage_activity(
        tillage_id, field_geom, tillage_date, depth_cm=3.5
    )
    res = await event_client.upsert_event(tillage)
    tillage_short_event = event._short_event(res.event)

    sowing_id = event.new_event_id()
    sowing_date = datetime(2021, 1, 2, 0, 0)
    sowing = testdata.sowing_alfalfa(sowing_id, field_geom, sowing_date)
    res = await event_client.upsert_event(sowing)
    sowing_short_event = event._short_event(res.event)

    irrigation_id = event.new_event_id()
    irrigation_date = datetime(2021, 1, 3, 0, 0)
    irrigation = testdata.irrigation_activity(
        irrigation_id, field_geom, irrigation_date, depth_mm=10
    )
    res = await event_client.upsert_event(irrigation)
    irrigation_short_event = event._short_event(res.event)

    harvest_id = event.new_event_id()
    harvest_date = datetime(2021, 6, 1, 0, 0)
    harvest = testdata.harvest_alfalfa(harvest_id, field_geom, harvest_date)
    res = await event_client.upsert_event(harvest)
    harvest_short_event = event._short_event(res.event)

    await search_client._upsert_field_events(
        field_md5=field_md5,
        field_events=[
            fallow_short_event,
            tillage_short_event,
            sowing_short_event,
            irrigation_short_event,
            harvest_short_event,
        ],
    )

    # Fetch events for the field
    res = await event_client.fetch_events_for_fields_with_context(
        field_ids=[field_md5],
        search_filter=search.Filter(
            interval_from=datetime(2020, 1, 1, 0, 0),
            interval_to=datetime(2021, 12, 31, 23, 59),
        ),
    )
    assert field_md5 in res
    assert len(res[field_md5]) == 5
    for event_with_context in res[field_md5]:
        assert event_with_context.event is not None
        assert event_with_context.context is not None


@pytest.mark.integration
async def test_last_update_for_field():
    """Integration test for getting last update time for a field"""

    event_client = client.Client(testdata.event_srvc_addr, testdata.event_search_addr)
    search_client = search.Search(event_search_addr)
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    assert await event_client.get_last_update_for_field(field_md5) is None

    # search api doesn't return microsecond precision
    start_time = datetime.now(tz=timezone.utc).replace(microsecond=0)

    fallow_id = event.new_event_id()
    fallow_start_date = datetime(2020, 1, 1, 0, 0)
    fallow_end_date = datetime(2020, 12, 31, 23, 59)
    fallow = testdata.fallow_period(
        fallow_id, field_geom, fallow_start_date, fallow_end_date
    )
    res = await event_client.upsert_event(fallow)
    fallow_short_event = event._short_event(res.event)
    await search_client._upsert_field_events(
        field_md5=field_md5, field_events=[fallow_short_event]
    )

    # Fetch last update time for the field
    initial_update_time = await event_client.get_last_update_for_field(field_md5)
    assert initial_update_time >= start_time

    # wait 1 second to ensure the next update is after the initial time
    await asyncio.sleep(1)

    # add a second event and check the last update time again
    tillage_id = event.new_event_id()
    tillage_date = datetime(2021, 1, 1, 0, 0)
    tillage = testdata.tillage_activity(
        tillage_id, field_geom, tillage_date, depth_cm=3.5
    )
    res = await event_client.upsert_event(tillage)
    tillage_short_event = event._short_event(res.event)
    await search_client._upsert_field_events(
        field_md5=field_md5,
        field_events=[tillage_short_event],
    )

    update_time_after_new_event = await event_client.get_last_update_for_field(
        field_md5
    )
    assert update_time_after_new_event > initial_update_time

    # wait 1 second to ensure the next update is after the initial time
    await asyncio.sleep(1)

    # update an existing event and check the last update time again
    fallow.end(datetime(2020, 12, 30, 0, 0))
    res = await event_client.upsert_event(fallow)
    fallow_short_event = event._short_event(res.event)
    await search_client._upsert_field_events(
        field_md5=field_md5, field_events=[fallow_short_event]
    )

    update_time_after_update_event = await event_client.get_last_update_for_field(
        field_md5
    )
    assert update_time_after_update_event > update_time_after_new_event


@pytest.mark.integration
async def test_cropping_sequences_for_fields():
    """Integration test for fetching cropping sequences for fields."""
    event_client = client.Client(testdata.event_srvc_addr, testdata.event_search_addr)
    search_client = search.Search(event_search_addr)
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    season_start = datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc)
    sowing1_date = datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc)
    just_before_harvest_date = datetime(2021, 5, 31, 23, tzinfo=timezone.utc)
    harvest1_date = datetime(2021, 6, 1, 0, 0, tzinfo=timezone.utc)
    season_end = datetime(2021, 12, 31, 23, 59, tzinfo=timezone.utc)

    # Upsert sowing
    sowing1_id = event.new_event_id()
    sowing1 = testdata.sowing_alfalfa(sowing1_id, field_geom, sowing1_date)
    res = await event_client.upsert_event(sowing1)
    sowing1_short_event = event._short_event(res.event)

    # Upsert harvest
    harvest1_id = event.new_event_id()
    harvest1 = testdata.harvest_alfalfa(harvest1_id, field_geom, harvest1_date)
    res = await event_client.upsert_event(harvest1)
    harvest1_short_event = event._short_event(res.event)

    # create search cache entries
    await search_client._upsert_field_events(
        field_md5, [sowing1_short_event, harvest1_short_event]
    )

    # Fetch cropping sequence - the response is a dictionary with field_md5 as key and a list of tuples as value
    res = await event_client.fetch_cropping_sequences_for_fields(
        field_ids=[field_md5],
        from_date=season_start,
        to_date=season_end,
        user_ids=[],
    )

    assert len(res) == 1
    assert len(res[field_md5]) == 1  # one tuple pair in the list
    sowing_res, harvest_res, termination_res = res[field_md5][0]
    assert sowing_res.id == sowing1_id
    assert harvest_res.id == harvest1_id
    assert termination_res is None

    # If we narrow the season to exclude the harvest date, we should get only a sowing event
    res = await event_client.fetch_cropping_sequences_for_fields(
        field_ids=[field_md5],
        from_date=season_start,
        to_date=just_before_harvest_date,
        user_ids=[],
    )

    sowing_res, harvest_res, termination_res = res[field_md5][0]
    assert sowing_res.id == sowing1_id
    assert harvest_res is None
    assert termination_res is None


@pytest.mark.integration
async def test_cropping_sequences_for_fields_with_context():
    """Integration test for fetching cropping sequences for fields including event context."""
    event_client = client.Client(testdata.event_srvc_addr, testdata.event_search_addr)
    search_client = search.Search(event_search_addr)
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    season_start = datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc)
    sowing1_date = datetime(2021, 1, 1, 0, 0, tzinfo=timezone.utc)
    harvest1_date = datetime(2021, 6, 1, 0, 0, tzinfo=timezone.utc)
    season_end = datetime(2021, 12, 31, 23, 59, tzinfo=timezone.utc)

    # Upsert sowing
    sowing1_id = event.new_event_id()
    sowing1 = testdata.sowing_alfalfa(sowing1_id, field_geom, sowing1_date)
    res = await event_client.upsert_event(sowing1)
    sowing1_short_event = event._short_event(res.event)

    # Upsert harvest
    harvest1_id = event.new_event_id()
    harvest1 = testdata.harvest_alfalfa(harvest1_id, field_geom, harvest1_date)
    res = await event_client.upsert_event(harvest1)
    harvest1_short_event = event._short_event(res.event)

    # Upsert termination
    termination1_id = event.new_event_id()
    termination1_date = harvest1_date
    termination1 = testdata.termination_activity(
        termination1_id, field_geom, termination1_date
    )
    res = await event_client.upsert_event(termination1)
    termination1_short_event = event._short_event(res.event)

    # create search cache entries
    await search_client._upsert_field_events(
        field_md5=field_md5,
        field_events=[
            sowing1_short_event,
            harvest1_short_event,
            termination1_short_event,
        ],
    )

    # Fetch cropping sequence - the response is a dictionary with field_md5 as key and a list of tuples as value
    res = await event_client.fetch_cropping_sequences_for_fields_with_context(
        field_ids=[field_md5],
        from_date=season_start,
        to_date=season_end,
        user_ids=[],
    )

    # debug print
    # print_cropping_sequence(res)

    assert len(res) == 1
    # assert len(res[field_md5]) == 1  # one tuple in the result for original crop_mismatch()
    assert (
        len(res[field_md5]) == 2
    )  # termination appears separately for modified crop_mismatch()
    sowing_res1, harvest_res1, termination_res1 = res[field_md5][
        0
    ]  # this is a tuple of EventWithContext objects
    assert sowing_res1.event.id == sowing1_id
    assert sowing_res1.context is not None
    assert harvest_res1.event.id == harvest1_id
    assert harvest_res1.context is not None
    # This is not in first tuple for modified crop_mismatch()
    # assert termination_res1.event.id == termination1_id
    # assert termination_res1.context is not None


def test_filter_cropping_sequences_by_crop_purpose():
    field_geom = testdata.random_polygon()
    field_md5 = testdata.random_md5()

    t1 = datetime(2021, 1, 1, 0, 0)
    t2 = datetime(2021, 1, 2, 0, 0)
    t3 = datetime(2021, 2, 1, 0, 0)
    t4 = datetime(2021, 2, 2, 0, 0)

    # a tillage activity
    tillage_id = "tillage-id"
    tillage_datetime = t1
    tillage_pb = testdata.tillage_activity(
        tillage_id, field_geom, tillage_datetime, 3
    ).pb()

    # commodity corn
    corn_sowing_id = "commodity-corn-sowing-id"
    corn_sowing_datetime = t1
    corn_sowing_pb = testdata.sowing_corn_purpose_commodity(
        corn_sowing_id, field_geom, corn_sowing_datetime
    ).pb()

    # cover alfalfa
    alfalfa_sowing_id = "cover-alfalfa-sowing-id"
    alfalfa_sowing_datetime = t2
    alfalfa_sowing_pb = testdata.sowing_alfalfa_purpose_cover(
        alfalfa_sowing_id, field_geom, alfalfa_sowing_datetime
    ).pb()
    # rice with no purpose
    rice_planting_id = "rice-planting-id"
    rice_planting_datetime = t3
    rice_planting_pb = testdata.planting_rice(
        rice_planting_id, field_geom, rice_planting_datetime
    ).pb()

    # harvest for fodder
    fodder_harvest_id = "fodder-harvest-id"
    fodder_harvest_datetime = t4
    fodder_harvest_pb = testdata.harvest_alfalfa_purpose_fodder(
        fodder_harvest_id, field_geom, fodder_harvest_datetime
    ).pb()

    ev_list = {
        field_md5: [
            tillage_pb,
            corn_sowing_pb,
            alfalfa_sowing_pb,
            rice_planting_pb,
            fodder_harvest_pb,
        ]
    }

    # filter by commodity
    crop_purpose_filter = [
        crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST,
    ]
    filtered_list = client.filter_event_list_by_crop_purpose(
        ev_list, crop_purpose_filter
    )

    assert field_md5 in filtered_list
    assert len(filtered_list[field_md5]) == 2
    assert tillage_pb in filtered_list[field_md5]
    assert corn_sowing_pb in filtered_list[field_md5]

    # filter by cover
    crop_purpose_filter = [
        crop_pb2.CROP_PURPOSE_COVER,
    ]
    filtered_list = client.filter_event_list_by_crop_purpose(
        ev_list, crop_purpose_filter
    )
    assert field_md5 in filtered_list
    assert len(filtered_list[field_md5]) == 2
    assert tillage_pb in filtered_list[field_md5]
    assert alfalfa_sowing_pb in filtered_list[field_md5]

    # filter by both
    crop_purpose_filter = [
        crop_pb2.CROP_PURPOSE_COVER,
        crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST,
    ]
    filtered_list = client.filter_event_list_by_crop_purpose(
        ev_list, crop_purpose_filter
    )
    assert field_md5 in filtered_list
    assert len(filtered_list[field_md5]) == 3
    assert tillage_pb in filtered_list[field_md5]
    assert corn_sowing_pb in filtered_list[field_md5]
    assert alfalfa_sowing_pb in filtered_list[field_md5]

    # filter for fodder harvest
    crop_purpose_filter = [
        crop_pb2.CROP_PURPOSE_FODDER,
    ]
    filtered_list = client.filter_event_list_by_crop_purpose(
        ev_list, crop_purpose_filter
    )
    assert field_md5 in filtered_list
    assert len(filtered_list[field_md5]) == 2
    assert tillage_pb in filtered_list[field_md5]
    assert fodder_harvest_pb in filtered_list[field_md5]

    # No purpose filter - should get everything
    filtered_list = client.filter_event_list_by_crop_purpose(ev_list, [])
    assert field_md5 in filtered_list
    assert len(filtered_list[field_md5]) == 5
    assert tillage_pb in filtered_list[field_md5]
    assert corn_sowing_pb in filtered_list[field_md5]
    assert alfalfa_sowing_pb in filtered_list[field_md5]
    assert rice_planting_pb in filtered_list[field_md5]
    assert fodder_harvest_pb in filtered_list[field_md5]


def print_cropping_sequence(res):
    """Print the cropping sequence for each field in the result."""

    def id_date(e):
        """Return the event id and date."""
        if not e:
            return None
        return f"{e.id} ({e.interval.start_time.ToDatetime().strftime('%Y-%m-%d')})"

    print("\n-------------------------------------")
    # convert res dict to JSON
    for item in res.items():
        field_md5, crop_sequence = item
        print(f"cropping sequences for field: {field_md5}:")
        for tup in crop_sequence:
            sowing = tup[0].event
            harvest = tup[1].event
            termination = tup[2].event
            print(
                f"(sowing: {id_date(sowing)}, harvest: {id_date(harvest)}, termination: {id_date(termination)})"
            )
    print("-------------------------------------")
