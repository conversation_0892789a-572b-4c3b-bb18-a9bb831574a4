from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2
from ses_client import event, pb_value
from ses_client.application import ApplicationActivity
from ses_client.crop import HarvestedCrop, PlantedCrop, SownCrop
from ses_client.event import StructuredEvent
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.planting import PlantingActivity
from ses_client.sowing import SowingActivity
from ses_client.tillage import TillageActivity

from tests import testdata


def test_new_event_with_id():
    event_id = event.new_event_id()
    se = StructuredEvent(event_id=event_id, user_id="abc-123")
    assert se.pb_event.id == event_id
    with pytest.raises(ValueError):
        se.validate()


def test_new_event_with_user():
    se = StructuredEvent(event_id=event.new_event_id(), user_id="abc-123")
    assert se.pb_context.creation["regrowUserId"] == "abc-123"
    assert se.pb_context.source["regrowUserId"] == "abc-123"
    with pytest.raises(ValueError):
        se.validate()


def test_new_event_with_geom():
    test_geojson = testdata.random_polygon()
    se = StructuredEvent(event_id=event.new_event_id()).geom(test_geojson)
    assert se.pb_event.geojson == test_geojson
    with pytest.raises(ValueError):
        se.validate()


def test_new_event_start():
    se = StructuredEvent(event_id=event.new_event_id()).start(
        datetime(2021, 1, 1, 0, 0)
    )
    assert se.pb_event.interval.start_time.seconds == 1609459200
    with pytest.raises(ValueError):
        se.validate()


def test_new_event_call_end_only():
    with pytest.raises(ValueError):
        StructuredEvent(event_id=event.new_event_id()).end(datetime(2022, 1, 1, 0, 0))


def test_new_event_call_end_before_start():
    with pytest.raises(ValueError):
        (
            StructuredEvent(event_id=event.new_event_id())
            .end(datetime(2022, 1, 1, 0, 0))
            .start(datetime(2021, 1, 1, 0, 0))
        )


def test_new_event_valid():
    (
        StructuredEvent(event_id=event.new_event_id(), user_id="abc-123")
        .start(datetime(2021, 1, 1, 0, 0))
        .end(datetime(2022, 1, 1, 0, 0))
        .geom(testdata.random_polygon())
        .validate()
    )


def test_short_event():
    @dataclass
    class Case:
        arg: StructuredEvent
        want: Dict[str, Any]

    field_geom = testdata.random_polygon()
    ids = []
    for _ in range(6):
        ids.append(event.new_event_id())

    # Note: the created date is a zero timestamp because it is usually set by the db server

    cases = [
        Case(
            arg=(
                TillageActivity(event_id=ids[0], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .depth(pb_value.depth_centimetres(3))
            ),
            want={
                "event_id": ids[0],
                "type": StructuredEvent.TYPE_TILLAGE_ACTIVITY,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2021-01-01T00:00:00Z",
            },
        ),
        Case(
            arg=(
                SowingActivity(event_id=ids[1], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .crop(sown_crop=SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA))
            ),
            want={
                "event_id": ids[1],
                "type": StructuredEvent.TYPE_SOWING_ACTIVITY,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2021-01-01T00:00:00Z",
            },
        ),
        Case(
            arg=(
                PlantingActivity(event_id=ids[2], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .crop(
                    planted_crop=PlantedCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA)
                )
            ),
            want={
                "event_id": ids[2],
                "type": StructuredEvent.TYPE_PLANTING_ACTIVITY,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2021-01-01T00:00:00Z",
            },
        ),
        Case(
            arg=(
                HarvestActivity(event_id=ids[3], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .crop(
                    harvested_crop=HarvestedCrop(
                        crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA
                    )
                )
            ),
            want={
                "event_id": ids[3],
                "type": StructuredEvent.TYPE_HARVEST_ACTIVITY,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2021-01-01T00:00:00Z",
            },
        ),
        Case(
            arg=(
                ApplicationActivity(event_id=ids[4], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .input(
                    BasicFertiliser(input_name="npk")
                    .mass_rate(pb_value.pounds_per_acre(150))
                    .pb()
                )
            ),
            want={
                "event_id": ids[4],
                "type": StructuredEvent.TYPE_APPLICATION_ACTIVITY,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2021-01-01T00:00:00Z",
            },
        ),
        Case(
            arg=(
                FallowPeriod(event_id=ids[5], user_id=testdata.TEST_USER_ID)
                .geom(field_geom)
                .start(datetime(2021, 1, 1, 0, 0))
                .end(datetime(2022, 1, 1, 0, 0))
            ),
            want={
                "event_id": ids[5],
                "type": StructuredEvent.TYPE_FALLOW_PERIOD,
                "created": "1970-01-01T00:00:00Z",
                "revision": 0,
                "overlap": 1.0,
                "start_time": "2021-01-01T00:00:00Z",
                "end_time": "2022-01-01T00:00:00Z",
            },
        ),
    ]

    for case in cases:
        short_event = event._short_event(case.arg.pb_event)
        assert short_event == case.want


def test_sort_list_by_interval_start_time():
    field_geom = testdata.random_polygon()
    t0 = datetime(2021, 1, 1, 0, 0)
    t1 = datetime(2021, 1, 2, 0, 0)
    t2 = datetime(2021, 2, 1, 0, 0)
    t3 = datetime(2021, 2, 2, 0, 0)
    t4 = datetime(2021, 3, 1, 0, 0)
    t5 = datetime(2021, 3, 2, 0, 0)

    sowing1_id = event.new_event_id()
    sowing1_datetime = t0
    sowing1_pb = testdata.sowing_alfalfa(sowing1_id, field_geom, sowing1_datetime).pb()

    sowing2_id = event.new_event_id()
    sowing2_datetime = t1
    sowing2_pb = testdata.sowing_alfalfa(sowing2_id, field_geom, sowing2_datetime).pb()

    harvest1_id = event.new_event_id()
    harvest1_datetime = t2
    harvest1_pb = testdata.harvest_alfalfa(
        harvest1_id, field_geom, harvest1_datetime
    ).pb()

    harvest2_id = event.new_event_id()
    harvest2_datetime = t3
    harvest2_pb = testdata.harvest_alfalfa(
        harvest2_id, field_geom, harvest2_datetime
    ).pb()

    tillage1_id = event.new_event_id()
    tillage1_datetime = t4
    tillage1_pb = testdata.tillage_activity(
        tillage1_id, field_geom, tillage1_datetime, 3
    ).pb()

    tillage2_id = event.new_event_id()
    tillage2_datetime = t5
    tillage2_pb = testdata.tillage_activity(
        tillage2_id, field_geom, tillage2_datetime, 3
    ).pb()

    unsorted_events = [
        tillage2_pb,
        harvest2_pb,
        sowing1_pb,
        harvest1_pb,
        tillage1_pb,
        sowing2_pb,
    ]
    want_sorted = [
        sowing1_pb,
        sowing2_pb,
        harvest1_pb,
        harvest2_pb,
        tillage1_pb,
        tillage2_pb,
    ]
    got_sorted = event.sort_list_by_interval_start_time(unsorted_events)
    assert got_sorted == want_sorted
