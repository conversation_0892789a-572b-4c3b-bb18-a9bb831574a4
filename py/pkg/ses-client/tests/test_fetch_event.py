from datetime import datetime

import pytest
from regrow.ses.event.v1 import event_service_pb2

from ses_client import client, event, pb_value
from ses_client.tillage import TillageActivity
from tests.health import health_check
from tests.testdata import TEST_USER_ID, event_srvc_addr, random_polygon


async def set_up_events(event_client):
    field_geom_1 = random_polygon()
    event_id_1 = event.new_event_id()
    await event_client.upsert_event(
        TillageActivity(event_id=event_id_1, user_id=TEST_USER_ID)
        .geom(field_geom_1)
        .start(datetime(2025, 1, 1))
        .depth(pb_value.depth_centimetres(1))
    )
    await event_client.upsert_event(
        TillageActivity(event_id=event_id_1, user_id=TEST_USER_ID)
        .geom(field_geom_1)
        .start(datetime(2025, 1, 2))
        .depth(pb_value.depth_centimetres(2))
    )

    field_geom_2 = random_polygon()
    event_id_2 = event.new_event_id()
    await event_client.upsert_event(
        TillageActivity(event_id=event_id_2, user_id=TEST_USER_ID)
        .geom(field_geom_2)
        .start(datetime(2025, 1, 1))
        .depth(pb_value.depth_centimetres(1))
    )
    await event_client.upsert_event(
        TillageActivity(event_id=event_id_2, user_id=TEST_USER_ID)
        .geom(field_geom_2)
        .start(datetime(2025, 1, 2))
        .depth(pb_value.depth_centimetres(2))
    )

    return event_id_1, event_id_2


@pytest.mark.integration
async def test_fetch_event_with_context():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    event_id_1, _ = await set_up_events(event_client)

    res = await event_client.fetch_event_with_context(event_id=event_id_1)
    assert res.event.revision == 2


@pytest.mark.integration
async def test_fetch_event_revision_with_context():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    event_id_1, _ = await set_up_events(event_client)

    res = await event_client.fetch_event_revision_with_context(
        event_id=event_id_1, revision="1"
    )
    assert res.event.revision == 1

    res = await event_client.fetch_event_revision_with_context(
        event_id=event_id_1, revision="2"
    )
    assert res.event.revision == 2


@pytest.mark.integration
async def test_fetch_events_with_context():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    event_id_1, event_id_2 = await set_up_events(event_client)

    res = await event_client.fetch_events_with_context(
        event_ids=[event_id_1, event_id_2]
    )
    assert len(res) == 2
    revisions = [single_res.event.revision for single_res in res]
    assert revisions == [2, 2]


@pytest.mark.integration
async def test_fetch_event_revisions_with_context():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    event_id_1, event_id_2 = await set_up_events(event_client)

    res = await event_client.fetch_event_revisions_with_context(
        fetch_event_requests=[
            event_service_pb2.FetchEventWithContextRequest(id=event_id_1, revision="1"),
            event_service_pb2.FetchEventWithContextRequest(id=event_id_2, revision="2"),
        ]
    )
    assert len(res) == 2
    revisions = [single_res.event.revision for single_res in res]
    assert revisions == [1, 2]
