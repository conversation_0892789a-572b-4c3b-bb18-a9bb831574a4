import pytest
from regrow.ses.application.v1 import application_input_id_pb2, application_pb2
from ses_client.input import (
    ApplicationInput,
    ApplicationInputOther,
    BasicFertiliser,
    EENFFertiliser,
    FertiliserAdditive,
    OrganicAmendment,
)


def test_input_without_id_or_label_raises_err():
    with pytest.raises(ValueError):
        ApplicationInput()
    with pytest.raises(ValueError):
        BasicFertiliser()
    with pytest.raises(ValueError):
        EENFFertiliser()
    with pytest.raises(ValueError):
        FertiliserAdditive()
    with pytest.raises(ValueError):
        OrganicAmendment()
    with pytest.raises(ValueError):
        ApplicationInputOther()


def test_input_with_invalid_id_raises_err():
    invalid_id = 9999999
    with pytest.raises(ValueError):
        BasicFertiliser(input_id=invalid_id)
    with pytest.raises(ValueError):
        EENFFertiliser(input_id=invalid_id)
    with pytest.raises(ValueError):
        FertiliserAdditive(input_id=invalid_id)
    with pytest.raises(ValueError):
        OrganicAmendment(input_id=invalid_id)


def test_basic_fertiliser_with_valid_id_sets_name():
    ai = BasicFertiliser(
        input_id=application_input_id_pb2.BasicFertiliserId.BASIC_FERTILISER_ID_AMMONIUM_BICARBONATE
    )
    assert ai.pb_input.basic_fertiliser is not None
    assert ai.pb_input.basic_fertiliser.id == 1
    assert ai.pb_input.basic_fertiliser.name == "ammonium bicarbonate"


def test_basic_fertiliser_with_valid_name_sets_id():
    ai = BasicFertiliser(input_name="ammonium bicarbonate")
    assert ai.pb_input.basic_fertiliser is not None
    assert ai.pb_input.basic_fertiliser.id == 1
    assert ai.pb_input.basic_fertiliser.name == "ammonium bicarbonate"


def test_basic_fertiliser_with_unknown_label_does_not_set_id():
    ai = BasicFertiliser(input_name="anything")
    assert ai.pb_input.basic_fertiliser.id == 0
    assert ai.pb_input.basic_fertiliser.name == "anything"


def test_eenf_fertiliser_with_valid_id_sets_name():
    ai = EENFFertiliser(
        input_id=application_input_id_pb2.EENFFertiliserId.EENF_FERTILISER_ID_OSMOCOTE_CLASSIC
    )
    assert ai.pb_input.eenf_fertiliser is not None
    assert ai.pb_input.eenf_fertiliser.id == 8
    assert ai.pb_input.eenf_fertiliser.name == "osmocote classic"


def test_eenf_fertiliser_with_valid_name_sets_id():
    ai = EENFFertiliser(input_name="osmocote classic")
    assert ai.pb_input.eenf_fertiliser is not None
    assert ai.pb_input.eenf_fertiliser.id == 8
    assert ai.pb_input.eenf_fertiliser.name == "osmocote classic"


def test_eenf_fertiliser_with_unknown_label_does_not_set_id():
    ai = EENFFertiliser(input_name="anything")
    assert ai.pb_input.eenf_fertiliser.id == 0
    assert ai.pb_input.eenf_fertiliser.name == "anything"


def test_fertiliser_additive_with_valid_id_sets_name():
    ai = FertiliserAdditive(
        input_id=application_input_id_pb2.FertiliserAdditiveId.FERTILISER_ADDITIVE_ID_AGROTAIN
    )
    assert ai.pb_input.fertiliser_additive is not None
    assert ai.pb_input.fertiliser_additive.id == 1
    assert ai.pb_input.fertiliser_additive.name == "agrotain"


def test_fertiliser_additive_with_valid_name_sets_id():
    ai = FertiliserAdditive(input_name="agrotain")
    assert ai.pb_input.fertiliser_additive.id == 1
    assert ai.pb_input.fertiliser_additive.name == "agrotain"


def test_fertiliser_additive_with_unknown_label_does_not_set_id():
    ai = FertiliserAdditive(input_name="anything")
    assert ai.pb_input.fertiliser_additive.id == 0
    assert ai.pb_input.fertiliser_additive.name == "anything"


def test_organic_amendment_with_valid_id_sets_name():
    ai = OrganicAmendment(
        input_id=application_input_id_pb2.OrganicAmendmentId.ORGANIC_AMENDMENT_ID_BONE_MEAL
    )
    assert ai.pb_input.organic_amendment is not None
    assert ai.pb_input.organic_amendment.id == 13
    assert ai.pb_input.organic_amendment.name == "bone meal"


def test_organic_amendment_with_valid_name_sets_id():
    ai = OrganicAmendment(input_name="bone meal")
    assert ai.pb_input.organic_amendment.id == 13
    assert ai.pb_input.organic_amendment.name == "bone meal"


def test_organic_amendment_with_label():
    ai = OrganicAmendment(input_name="anything")
    assert ai.pb_input.organic_amendment.id == 0
    assert ai.pb_input.organic_amendment.name == "anything"


def test_other_input_type():
    ai = ApplicationInputOther(
        input_type=application_pb2.ApplicationInputOtherType.APPLICATION_INPUT_OTHER_TYPE_HERBICIDE,
        input_name="roundup",
    )
    assert ai.pb_input.other_input is not None
    assert ai.pb_input.other_input.type == 70
    assert ai.pb_input.other_input.name == "roundup"
