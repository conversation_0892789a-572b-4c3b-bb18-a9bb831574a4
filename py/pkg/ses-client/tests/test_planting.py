from datetime import datetime

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.planting.v1 import planting_pb2
from ses_client import client, event, planting
from ses_client.crop import PlantedCrop, crop_id_from_label
from ses_client.planting import PlantingActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


@pytest.mark.integration
async def test_upsert_planting_activity_validation():
    """Test that the planting activity event is validated correctly."""
    await health_check(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    with pytest.raises(ValueError):
        """Missing event id"""
        PlantingActivity(event_id="").geom(field_geom).start(event_datetime).validate()

    with pytest.raises(ValueError):
        """Missing event time"""
        PlantingActivity(event_id=event.new_event_id()).geom(field_geom).validate()

    with pytest.raises(ValueError):
        """Missing event geom"""
        PlantingActivity(event_id=event.new_event_id()).start(event_datetime).validate()


@pytest.mark.integration
async def test_upsert_planting_activity_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no crop so expect revision = 0 (draft)
    event_id = event.new_event_id()
    pa = (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(pa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_planting_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    pa = (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(PlantedCrop(crop_id=crop_id))
    )
    _ = await event_client.upsert_event(pa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


def test_planting_activity():
    event_id = event.new_event_id()
    pa = (
        PlantingActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .crop(
            PlantedCrop(crop_id_enum_pb2.CROP_ID_RICE).planting_rate_plants_per_sqm(10)
        )
        .method(planting_method=planting.method_dry())
        .validate()
    )
    assert (
        pa.pb().planting_activity.method
        == planting_pb2.PlantingMethod.PLANTING_METHOD_DRY
    )


@pytest.mark.integration
async def test_upsert_planting_activity_revision_2():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    event_id = event.new_event_id()
    pa = (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(PlantedCrop(crop_id=crop_id_from_label("rye")))
    )
    _ = await event_client.upsert_event(pa)

    # update the event
    pa = (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(PlantedCrop(crop_id=crop_id_from_label("rice")))
    )
    _ = await event_client.upsert_event(pa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 2


@pytest.mark.integration
async def test_upsert_new_planting_activity_detailed():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rice")
    cropping_period_id = event.new_event_id()
    pa = (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(
            PlantedCrop(crop_id=crop_id)
            .planting_rate_plants_per_sqm(64)
            .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
        )
        .cropping_period_identifier(cropping_period_id)
        .validate()
    )

    _ = await event_client.upsert_event(pa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1
    assert len(se.planting_activity.crops) == 1
    assert se.planting_activity.crops[0].crop.id == crop_id
    assert (
        se.planting_activity.crops[0].crop.purpose[0]
        == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
    )
    assert se.cropping_period_identifier == cropping_period_id
