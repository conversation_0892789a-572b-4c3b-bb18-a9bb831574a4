from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Tuple
from tests.health import health_check

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.event.v1.event_pb2 import Event
from ses_client import client, event, sequence
from ses_client.crop import HarvestedCrop, SownCrop, TerminatedCrop

from tests import testdata
from tests.testdata import (
    event_srvc_addr,
    harvest_activity,
    sowing_activity,
    termination_activity,
    tillage_activity,
)


@pytest.mark.integration
async def test_cropping_sequence_with_termination():
    """Contains multiple candidate events for sowing and harvest, unrelated events, unbalanced pairs AND
    termination activities."""
    field_geom = testdata.random_polygon()

    # corn 2021
    d = datetime(2021, 8, 1)
    tillage_2021 = f"tillage-{d:%b-%Y}"
    tillage_2021_pb = testdata.tillage_activity(tillage_2021, field_geom, d, 3).pb()

    d = datetime(2021, 9, 1)
    corn_sowing_2021 = f"corn-sowing-{d:%b-%Y)}"
    corn_sowing_2021_pb = testdata.sowing_corn(corn_sowing_2021, field_geom, d).pb()

    d = datetime(2021, 9, 1)
    corn_sowing_commodity_2021 = f"corn-sowing-commodity-{d:%b-%Y)}"
    corn_sowing_commodity_2021_pb = testdata.sowing_corn_purpose_commodity(
        corn_sowing_commodity_2021, field_geom, d
    ).pb()

    d = datetime(2021, 12, 1)
    corn_harvest_2021 = f"corn-harvest-{d:%b-%Y}"
    corn_harvest_2021_pb = testdata.harvest_corn(corn_harvest_2021, field_geom, d).pb()

    # alfalfa 2021 (1) - same field
    d = datetime(2021, 3, 1)
    alfalfa_sowing1_2021 = f"alfalfa-sowing1-{d:%b-%Y)}"
    alfalfa_sowing1_2021_pb = testdata.sowing_alfalfa(
        alfalfa_sowing1_2021, field_geom, d
    ).pb()

    # alfalfa 2021 (2) - same field, after the first planting but before harvest to mess with things
    d = datetime(2021, 5, 1)
    alfalfa_sowing2_2021 = f"alfalfa-sowing1-{d:%b-%Y)}"
    alfalfa_sowing2_2021_pb = testdata.sowing_alfalfa(
        alfalfa_sowing2_2021, field_geom, d
    ).pb()

    # alfalfa 2021 first harvest falls mid corn season to ensure crop matching
    d = datetime(2021, 10, 1)
    alfalfa_harvest1_2021 = f"alfalfa-harvest1-{d:%b-%Y}"
    alfalfa_harvest1_2021_pb = testdata.harvest_alfalfa(
        alfalfa_harvest1_2021, field_geom, d
    ).pb()

    # alfalfa 2021 second harvest falls mid corn season to ensure crop matching
    d = datetime(2021, 11, 1)
    alfalfa_harvest2_2021 = f"alfalfa-harvest2-{d:%b-%Y}"
    alfalfa_harvest2_2021_pb = testdata.harvest_alfalfa(
        alfalfa_harvest2_2021, field_geom, d
    ).pb()

    # corn 2022
    d = datetime(2022, 1, 1)
    tillage_2022 = f"tillage-{d:%b-%Y}"
    tillage_2022_pb = testdata.tillage_activity(tillage_2022, field_geom, d, 3).pb()

    d = datetime(2022, 4, 1)
    corn_sowing_2022 = f"corn-sowing-{d:%b-%Y}"
    corn_sowing_2022_pb = testdata.sowing_corn(corn_sowing_2022, field_geom, d).pb()

    # harvest has no crop id so therefore should not be paired
    d = datetime(2022, 7, 1)
    nocrop_harvest_2022 = f"nocrop-harvest-{d:%b-%Y}"
    nocrop_harvest_2022_pb = testdata.harvest_activity(
        nocrop_harvest_2022, field_geom, d
    ).pb()

    # corn 2022 add a termination activity
    d = datetime(2022, 7, 1)
    corn_termination_2022 = f"corn-termination-{d:%b-%Y}"
    corn_termination_2022_pb = testdata.termination_corn(
        corn_termination_2022, field_geom, d
    ).pb()

    # unspecified crop sequence 2023
    d = datetime(2023, 1, 1)
    tillage_2023 = f"tillage-{d:%b-%Y}"
    tillage_2023_pb = testdata.tillage_activity(tillage_2023, field_geom, d, 3).pb()

    d = datetime(2023, 2, 1)
    nocrop_planting_2023 = f"nocrop-planting-{d:%b-%Y}"
    nocrop_planting_2023_pb = testdata.sowing_activity(
        nocrop_planting_2023, field_geom, d
    ).pb()

    d = datetime(2023, 7, 1)
    nocrop_termination_2023 = f"nocrop-termination-{d:%d-%b-%Y}"
    nocrop_termination_2023_pb = testdata.termination_activity(
        nocrop_termination_2023, field_geom, d
    ).pb()

    d = datetime(2023, 7, 1)
    nocrop_harvest_2023 = f"nocrop-harvest-{d:%d-%b-%Y}"
    nocrop_harvest_2023_pb = testdata.harvest_activity(
        nocrop_harvest_2023, field_geom, d
    ).pb()

    @dataclass
    class Case:
        name: str
        events: List[Event]
        want: List[Tuple[Event, Optional[Event], Optional[Event]]]

    cases = [
        Case(
            name="simple corn season 2021 - ordered",
            events=[
                tillage_2021_pb,
                corn_sowing_2021_pb,
                corn_harvest_2021_pb,
            ],
            want=[
                (corn_sowing_2021_pb, corn_harvest_2021_pb, None),
            ],
        ),
        Case(
            name="simple corn season 2021 - unordered",
            events=[
                corn_harvest_2021_pb,
                tillage_2021_pb,
                corn_sowing_2021_pb,
            ],
            want=[
                (corn_sowing_2021_pb, corn_harvest_2021_pb, None),
            ],
        ),
        Case(
            name="alfalfa and corn 2021",
            events=[
                alfalfa_sowing1_2021_pb,
                corn_harvest_2021_pb,
                tillage_2021_pb,
                corn_sowing_2021_pb,
                alfalfa_harvest1_2021_pb,
                alfalfa_harvest2_2021_pb,
            ],
            want=[
                (alfalfa_sowing1_2021_pb, alfalfa_harvest1_2021_pb, None),
                (None, alfalfa_harvest2_2021_pb, None),
                (corn_sowing_2021_pb, corn_harvest_2021_pb, None),
            ],
        ),
        Case(
            name="corn 2022 - harvest without crop id, with termination",
            events=[
                corn_sowing_2022_pb,
                nocrop_harvest_2022_pb,
                tillage_2022_pb,
                corn_termination_2022_pb,
            ],
            want=[
                (corn_sowing_2022_pb, None, corn_termination_2022_pb),
                (None, nocrop_harvest_2022_pb, None),
            ],
        ),
        Case(
            name="unspecified crop sequence 2023",
            events=[
                nocrop_termination_2023_pb,
                nocrop_harvest_2023_pb,
                tillage_2023_pb,
                nocrop_planting_2023_pb,
            ],
            want=[
                (
                    nocrop_planting_2023_pb,
                    nocrop_harvest_2023_pb,
                    nocrop_termination_2023_pb,
                ),
            ],
        ),
        Case(
            name="conflicting alfalfa harvests",
            events=[
                alfalfa_sowing1_2021_pb,
                alfalfa_sowing2_2021_pb,
                alfalfa_harvest1_2021_pb,
                alfalfa_harvest2_2021_pb,
            ],
            want=[
                (alfalfa_sowing1_2021_pb, alfalfa_harvest1_2021_pb, None),
                (alfalfa_sowing2_2021_pb, alfalfa_harvest2_2021_pb, None),
            ],
        ),
        Case(
            name="unpaired purposes",
            events=[
                tillage_2021_pb,
                corn_sowing_commodity_2021_pb,
                corn_harvest_2021_pb,
            ],
            want=[
                (corn_sowing_commodity_2021_pb, None, None),
                (None, corn_harvest_2021_pb, None),
            ],
        ),
    ]

    # print("\nResults:")
    # for c in cases:
    #     got = sequence.cropping_sequence(c.events)
    #     print("-" * 80)
    #     for i, (s, h, t) in enumerate(got):
    #         print(
    #             f"[{i}]: (sowplant: {s.id if s else None}, harvest: {h.id if h else None}, termination: {t.id if t else None})")

    for c in cases:
        got = sequence.cropping_sequence(c.events)
        assert len(got) == len(c.want)
        for s in got:
            assert s in c.want


@pytest.mark.integration
async def test_cropping_sequence_with_successive_matching_crops():
    """A test for a very specific edge case where the same crop is planted sequentially with different purposes.

    The scenario is:
        winterkill basic cover crop (no harvest):
            actual dates 2021-09-10 to 2021-12-02
        grain harvest basic cover crop:
            actual dates 2022-06-23 to 2022-09-15

        The first crop takes the harvest from the second, so ends up with its harvest date. The second crop still has
        its end date intact from the termination activity but lacks harvest data (empty residue harvested).
    """
    field_geom = testdata.random_polygon()

    # basic cover sowing
    d = datetime(2021, 5, 10)
    basic_cover_sowing_2021_id = f"basic-cover-sowing-{d:%b-%Y)}"
    basic_cover_sowing_2021_pb = (
        sowing_activity(basic_cover_sowing_2021_id, field_geom, d)
        .crop(SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )
    # ----------------------------------------------
    d = datetime(2022, 6, 23)
    basic_cover_sowing_2022_id = f"basic-cover-sowing-{d:%b-%Y)}"
    basic_cover_sowing_2022_pb = (
        sowing_activity(basic_cover_sowing_2022_id, field_geom, d)
        .crop(SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )
    # ----------------------------------------------
    d = datetime(2023, 6, 23)
    basic_cover_sowing_2023_id = f"basic-cover-sowing-{d:%b-%Y)}"
    basic_cover_sowing_2023_pb = (
        sowing_activity(basic_cover_sowing_2023_id, field_geom, d)
        .crop(SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )

    # basic cover harvest
    d = datetime(2022, 9, 15)
    basic_cover_harvest_2022_id = f"basic-cover-harvest-{d:%b-%Y}"
    basic_cover_harvest_2022_pb = (
        harvest_activity(basic_cover_harvest_2022_id, field_geom, d)
        .crop(HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )
    # ----------------------------------------------
    d = datetime(2023, 9, 15)
    basic_cover_harvest_2023_id = f"basic-cover-harvest-{d:%b-%Y}"
    basic_cover_harvest_2023_pb = (
        harvest_activity(basic_cover_harvest_2023_id, field_geom, d)
        .crop(HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )

    # basic cover termination
    d = datetime(2021, 9, 15)
    basic_cover_termination_2021_id = f"basic-cover-termination-{d:%b-%Y}"
    basic_cover_termination_2021_pb = (
        termination_activity(basic_cover_termination_2021_id, field_geom, d)
        .crop(TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )
    # ----------------------------------------------
    d = datetime(2022, 9, 15)
    basic_cover_termination_2022_id = f"basic-cover-termination-{d:%b-%Y}"
    basic_cover_termination_2022_pb = (
        termination_activity(basic_cover_termination_2022_id, field_geom, d)
        .crop(TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP))
        .pb()
    )

    # ----------------------------------------------

    events = [
        basic_cover_sowing_2021_pb,
        basic_cover_sowing_2022_pb,
        basic_cover_sowing_2023_pb,
        basic_cover_harvest_2022_pb,
        basic_cover_harvest_2023_pb,
        basic_cover_termination_2022_pb,
        basic_cover_termination_2021_pb,
    ]

    want = [
        (basic_cover_sowing_2021_pb, None, basic_cover_termination_2021_pb),
        (
            basic_cover_sowing_2022_pb,
            basic_cover_harvest_2022_pb,
            basic_cover_termination_2022_pb,
        ),
        (basic_cover_sowing_2023_pb, basic_cover_harvest_2023_pb, None),
    ]

    got = sequence.cropping_sequence(events)

    # print("\nResults:")
    # print("-" * 80)
    # for i, (s, h, t) in enumerate(got):
    #    print(
    #        f"[{i}]: (sowing: {s.id if s else None}, harvest: {h.id if h else None}, termination: {t.id if t else None})"
    #    )

    assert len(want) == len(got)
    for s in got:
        assert s in want


@pytest.mark.integration
async def test_cropping_sequence_by_event_ids():
    """
    Test fetching cropping sequences by Crop IDs
    """
    field_geom = testdata.random_polygon()

    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    # basic cover sowing
    d = datetime(2022, 6, 23)
    basic_cover_sowing_2022_id = event.new_event_id()
    basic_cover_sowing_2022 = sowing_activity(
        basic_cover_sowing_2022_id, field_geom, d
    ).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(basic_cover_sowing_2022)
    # basic cover harvest
    d = datetime(2022, 9, 15)
    basic_cover_harvest_2022_id = event.new_event_id()
    basic_cover_harvest_2022 = harvest_activity(
        basic_cover_harvest_2022_id, field_geom, d
    ).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(basic_cover_harvest_2022)
    # basic cover termination
    d = datetime(2022, 9, 15)
    basic_cover_termination_2022_id = event.new_event_id()
    basic_cover_termination_2022 = termination_activity(
        basic_cover_termination_2022_id, field_geom, d
    ).crop(
        TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(basic_cover_termination_2022)
    d = datetime(2022, 7, 15)
    tillage_id = event.new_event_id()
    tillage_ignored = tillage_activity(tillage_id, field_geom, d, 3)
    _ = await event_client.upsert_event(tillage_ignored)
    sequences = await event_client.fetch_cropping_sequences(
        [
            basic_cover_sowing_2022_id,
            basic_cover_termination_2022_id,
            basic_cover_harvest_2022_id,
            tillage_id,
        ]
    )
    assert len(sequences) == 1
    assert sequences[0][0].id == basic_cover_sowing_2022_id
    assert sequences[0][1].id == basic_cover_harvest_2022_id
    assert sequences[0][2].id == basic_cover_termination_2022_id


@pytest.mark.integration
async def test_nested_cropping_sequences_with_identified_inner_group():
    field_geom = testdata.random_polygon()

    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    inner_period_id = event.new_event_id()

    # outer basic cover sowing
    d = datetime(2022, 4, 23)
    outer_basic_cover_sowing_2022_id = event.new_event_id()
    outer_basic_cover_sowing_2022 = sowing_activity(
        outer_basic_cover_sowing_2022_id, field_geom, d
    ).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(outer_basic_cover_sowing_2022)

    # inner basic cover sowing
    d = datetime(2022, 5, 23)
    inner_basic_cover_sowing_2022_id = event.new_event_id()
    inner_basic_cover_sowing_2022 = (
        sowing_activity(inner_basic_cover_sowing_2022_id, field_geom, d)
        .crop(
            SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(inner_period_id)
    )
    _ = await event_client.upsert_event(inner_basic_cover_sowing_2022)

    # inner basic cover harvest
    d = datetime(2022, 9, 15)
    inner_basic_cover_harvest_2022_id = event.new_event_id()
    inner_basic_cover_harvest_2022 = (
        harvest_activity(inner_basic_cover_harvest_2022_id, field_geom, d)
        .crop(
            HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(inner_period_id)
    )
    _ = await event_client.upsert_event(inner_basic_cover_harvest_2022)

    # inner basic cover termination
    d = datetime(2022, 9, 15)
    inner_basic_cover_termination_2022_id = event.new_event_id()
    inner_basic_cover_termination_2022 = (
        termination_activity(inner_basic_cover_termination_2022_id, field_geom, d)
        .crop(
            TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(inner_period_id)
    )
    _ = await event_client.upsert_event(inner_basic_cover_termination_2022)

    # outer basic cover harvest
    d = datetime(2022, 10, 15)
    outer_basic_cover_harvest_2022_id = event.new_event_id()
    outer_basic_cover_harvest_2022 = harvest_activity(
        outer_basic_cover_harvest_2022_id, field_geom, d
    ).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(outer_basic_cover_harvest_2022)

    # outer basic cover termination
    d = datetime(2022, 10, 15)
    outer_basic_cover_termination_2022_id = event.new_event_id()
    outer_basic_cover_termination_2022 = termination_activity(
        outer_basic_cover_termination_2022_id, field_geom, d
    ).crop(
        TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(outer_basic_cover_termination_2022)

    sequences = await event_client.fetch_cropping_sequences(
        [
            outer_basic_cover_sowing_2022_id,
            inner_basic_cover_sowing_2022_id,
            inner_basic_cover_termination_2022_id,
            inner_basic_cover_harvest_2022_id,
            outer_basic_cover_termination_2022_id,
            outer_basic_cover_harvest_2022_id,
        ]
    )

    assert len(sequences) == 2
    assert sequences[0][0].id == outer_basic_cover_sowing_2022_id
    assert sequences[0][1].id == outer_basic_cover_harvest_2022_id
    assert sequences[0][2].id == outer_basic_cover_termination_2022_id

    assert sequences[1][0].id == inner_basic_cover_sowing_2022_id
    assert sequences[1][1].id == inner_basic_cover_harvest_2022_id
    assert sequences[1][2].id == inner_basic_cover_termination_2022_id


@pytest.mark.integration
async def test_nested_cropping_sequences_with_identified_outer_group():
    field_geom = testdata.random_polygon()

    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)

    outer_period_id = event.new_event_id()

    # outer basic cover sowing
    d = datetime(2022, 4, 23)
    outer_basic_cover_sowing_2022_id = event.new_event_id()
    outer_basic_cover_sowing_2022 = (
        sowing_activity(outer_basic_cover_sowing_2022_id, field_geom, d)
        .crop(
            SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(outer_period_id)
    )
    _ = await event_client.upsert_event(outer_basic_cover_sowing_2022)

    # inner basic cover sowing
    d = datetime(2022, 5, 23)
    inner_basic_cover_sowing_2022_id = event.new_event_id()
    inner_basic_cover_sowing_2022 = sowing_activity(
        inner_basic_cover_sowing_2022_id, field_geom, d
    ).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(inner_basic_cover_sowing_2022)

    # inner basic cover harvest
    d = datetime(2022, 9, 15)
    inner_basic_cover_harvest_2022_id = event.new_event_id()
    inner_basic_cover_harvest_2022 = harvest_activity(
        inner_basic_cover_harvest_2022_id, field_geom, d
    ).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(inner_basic_cover_harvest_2022)

    # inner basic cover termination
    d = datetime(2022, 9, 15)
    inner_basic_cover_termination_2022_id = event.new_event_id()
    inner_basic_cover_termination_2022 = termination_activity(
        inner_basic_cover_termination_2022_id, field_geom, d
    ).crop(
        TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
    _ = await event_client.upsert_event(inner_basic_cover_termination_2022)

    # outer basic cover harvest
    d = datetime(2022, 10, 15)
    outer_basic_cover_harvest_2022_id = event.new_event_id()
    outer_basic_cover_harvest_2022 = (
        harvest_activity(outer_basic_cover_harvest_2022_id, field_geom, d)
        .crop(
            HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(outer_period_id)
    )
    _ = await event_client.upsert_event(outer_basic_cover_harvest_2022)

    # outer basic cover termination
    d = datetime(2022, 10, 15)
    outer_basic_cover_termination_2022_id = event.new_event_id()
    outer_basic_cover_termination_2022 = (
        termination_activity(outer_basic_cover_termination_2022_id, field_geom, d)
        .crop(
            TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_BASIC_COVER_CROP).purpose(
                crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
            )
        )
        .cropping_period_identifier(outer_period_id)
    )
    _ = await event_client.upsert_event(outer_basic_cover_termination_2022)

    sequences = await event_client.fetch_cropping_sequences(
        [
            outer_basic_cover_sowing_2022_id,
            inner_basic_cover_sowing_2022_id,
            inner_basic_cover_termination_2022_id,
            inner_basic_cover_harvest_2022_id,
            outer_basic_cover_termination_2022_id,
            outer_basic_cover_harvest_2022_id,
        ]
    )

    assert len(sequences) == 2
    assert sequences[0][0].id == outer_basic_cover_sowing_2022_id
    assert sequences[0][1].id == outer_basic_cover_harvest_2022_id
    assert sequences[0][2].id == outer_basic_cover_termination_2022_id

    assert sequences[1][0].id == inner_basic_cover_sowing_2022_id
    assert sequences[1][1].id == inner_basic_cover_harvest_2022_id
    assert sequences[1][2].id == inner_basic_cover_termination_2022_id
