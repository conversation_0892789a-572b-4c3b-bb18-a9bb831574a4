from time import sleep

from ses_client import client


# create a grpc service health check that returns true once the server is up
async def health_check(grpc_srvc_addr: str):
    delay = 1
    attempts = 10
    for i in range(attempts):
        try:
            await client.Client(grpc_srvc_addr).health_check()
        except Exception:
            delay += 1
            print(
                f"Health check {i + 1} failed, waiting {delay} seconds before retrying"
            )  # noqa: T201
            sleep(delay)
