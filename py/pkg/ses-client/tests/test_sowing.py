from datetime import datetime

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2
from regrow.ses.sowing.v1 import sowing_pb2
from ses_client import client, event
from ses_client.crop import SownCrop, crop_id_from_label
from ses_client.sowing import SowingActivity, method_broadcast

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


def test_sowing_activity():
    event_id = event.new_event_id()
    sown_crop_1 = SownCrop(crop_id_enum_pb2.CROP_ID_ALFALFA).sowing_rate_kg_per_ha(10)
    sown_crop_2 = SownCrop(crop_id_enum_pb2.CROP_ID_BARLEY).sowing_rate_kg_per_ha(20)
    sa = (
        SowingActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .crop(sown_crop_1)
        .crop(sown_crop_2)
        .method(method_broadcast())
        .validate()
    )

    assert (
        sa.pb().sowing_activity.method
        == sowing_pb2.SowingMethod.SOWING_METHOD_BROADCAST
    )
    assert len(sa.pb().sowing_activity.crops) == 2


@pytest.mark.integration
async def test_upsert_sowing_activity_validation():
    """Test that the sowing activity event is validated correctly."""
    await health_check(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    with pytest.raises(ValueError):
        """Missing event id"""
        SowingActivity(event_id="").geom(field_geom).start(event_datetime).validate()

    with pytest.raises(ValueError):
        """Missing event time"""
        SowingActivity(event_id=event.new_event_id()).geom(field_geom).validate()

    with pytest.raises(ValueError):
        """Missing event geom"""
        SowingActivity(event_id=event.new_event_id()).start(event_datetime).validate()


@pytest.mark.integration
async def test_upsert_sowing_activity_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    # no crop so expect revision = 0 (draft)
    event_id = event.new_event_id()
    sa = (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(sa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_sowing_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    sa = (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(SownCrop(crop_id=crop_id))
    )
    _ = await event_client.upsert_event(sa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_sowing_activity_revision_2():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    sa = (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(SownCrop(crop_id=crop_id_from_label("rye")))
    )
    _ = await event_client.upsert_event(sa)

    # update the event
    sa = (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(SownCrop(crop_id=crop_id_from_label("rice")))
    )
    _ = await event_client.upsert_event(sa)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 2


@pytest.mark.integration
async def test_upsert_new_sowing_activity_detailed():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    cropping_period_id = event.new_event_id()
    sa = (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(SownCrop(crop_id=crop_id_from_label("rye")).sowing_rate_kg_per_ha(100))
        .cropping_period_identifier(cropping_period_id)
        .validate()
    )

    _ = await event_client.upsert_event(sa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1
    assert se.cropping_period_identifier == cropping_period_id
