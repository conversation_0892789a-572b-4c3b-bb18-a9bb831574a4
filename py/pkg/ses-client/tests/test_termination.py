from datetime import datetime

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2
from regrow.ses.termination.v1 import termination_pb2
from ses_client import client, event
from ses_client.crop import TerminatedCrop
from ses_client.termination import TerminationActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


def test_termination_activity():
    event_id = event.new_event_id()
    terminated_crop_1 = TerminatedCrop(crop_id_enum_pb2.CROP_ID_ALFALFA)
    terminated_crop_2 = TerminatedCrop(crop_id_enum_pb2.CROP_ID_BARLEY)
    ta = (
        TerminationActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .crop(terminated_crop=terminated_crop_1)
        .crop(terminated_crop=terminated_crop_2)
        .reason(termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST)
        .method(termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING)
        .validate()
    )

    assert len(ta.pb().termination_activity.crops) == 2
    assert (
        ta.pb().termination_activity.reason
        == termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST
    )
    assert (
        ta.pb().termination_activity.method
        == termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING
    )


@pytest.mark.integration
async def test_upsert_termination_activity_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    # no crop so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ta = (
        TerminationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_termination_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    cropping_period_id = event.new_event_id()

    event_id = event.new_event_id()
    ta = (
        TerminationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(TerminatedCrop(crop_id_enum_pb2.CROP_ID_ALFALFA))
        .cropping_period_identifier(cropping_period_id)
    )
    assert ta.pb_event.cropping_period_identifier == cropping_period_id
    _ = await event_client.upsert_event(ta)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1
    assert se.termination_activity is not None
    assert se.cropping_period_identifier == cropping_period_id
