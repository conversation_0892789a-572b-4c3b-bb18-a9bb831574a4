from datetime import datetime

import pytest
from regrow.ses.pbtype import line_pb2
from regrow.ses.tillage.v1 import tillage_pb2
from ses_client import client, event, pb_value
from ses_client.tillage import TillageActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


def test_tillage_activity():
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .depth(pb_value.depth_centimetres(3))
        .start(datetime(2021, 1, 21, 0, 0))
        .soil_inversion(True)
        .strip_tillage_percent(55.5)
        .implement(tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW)
        .tillage_practice(tillage_pb2.TillagePractice.TILLAGE_PRACTICE_CONVENTIONAL)
        .validate()
    )

    assert ta.pb_event.id == event_id
    assert ta.pb_event.tillage_activity.depth.value == 3
    assert ta.pb_event.tillage_activity.soil_inversion is True
    assert ta.pb_event.tillage_activity.strip_tillage_fraction.percent == 55.5
    assert (
        ta.pb_event.tillage_activity.implement
        == tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW
    )
    assert (
        ta.pb_event.tillage_activity.tillage_practice
        == tillage_pb2.TillagePractice.TILLAGE_PRACTICE_CONVENTIONAL
    )


@pytest.mark.integration
async def test_upsert_tillage_activity_validation():
    """Test that the tillage activity event is validated correctly. Note some of these tests overlap with unit
    tests for the StructuredEvent and child classes, but it doesn't hurt to check validation via these Client methods.
    """
    await health_check(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    with pytest.raises(ValueError):
        """Missing event id"""
        TillageActivity(event_id="").geom(field_geom).start(event_datetime).validate()

    with pytest.raises(ValueError):
        """Missing event time"""
        TillageActivity(event_id=event.new_event_id()).geom(field_geom).validate()

    with pytest.raises(ValueError):
        """Missing event geom"""
        TillageActivity(event_id=event.new_event_id()).start(event_datetime).validate()


@pytest.mark.integration
async def test_upsert_tillage_activity_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    # no depth so expect revision = 0 (draft)
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_tillage_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_centimetres(3))
    )
    _ = await event_client.upsert_event(ta)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_tillage_activity_revision_2():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_centimetres(3))
    )
    _ = await event_client.upsert_event(ta)

    # update depth - should get revision = 2 and change of units to inches
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_inches(1))
    )
    _ = await event_client.upsert_event(ta)

    se = await event_client.fetch_event(event_id)
    assert se.revision == 2
    assert se.tillage_activity.depth.value == 1
    assert se.tillage_activity.depth.unit == line_pb2.LINE_UNIT_INCH


@pytest.mark.integration
async def test_upsert_new_tillage_activity_detailed():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .depth(pb_value.depth_inches(2.5))
        .soil_inversion(True)
        .strip_tillage_percent(55.5)
        .implement(tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW)
        .tillage_practice(tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED)
        .validate()
    )

    _ = await event_client.upsert_event(ta)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.tillage_activity.depth.value == 2.5
    assert se.tillage_activity.depth.unit == line_pb2.LINE_UNIT_INCH
    assert se.tillage_activity.soil_inversion is True
    assert se.tillage_activity.strip_tillage_fraction.percent == 55.5
    assert (
        se.tillage_activity.implement
        == tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW
    )
    assert (
        se.tillage_activity.tillage_practice
        == tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED
    )
