import hashlib
import json
import random
import string
from datetime import datetime

from geopy.distance import geodesic
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.termination.v1 import termination_pb2
from ses_client import pb_value
from ses_client.crop import HarvestedCrop, PlantedCrop, SownCrop, TerminatedCrop
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.irrigation import IrrigationActivity
from ses_client.planting import PlantingActivity
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity
from shapely.geometry import Polygon

event_srvc_addr = "localhost:50052"
event_search_addr = "http://localhost:8080"
TEST_USER_ID = "user-123"


def random_datetime(from_year=2018, to_year=2024):
    return datetime(
        random.randint(from_year, to_year),
        random.randint(1, 12),
        random.randint(1, 28),
        random.randint(0, 23),
        random.randint(0, 59),
    )


def random_polygon():
    """
    Generates a random 500m x 500m square polygon anywhere on Earth.

    Returns:
    - str: GeoJSON string of the polygon.
    """

    # Generate a random latitude and longitude
    latitude = random.uniform(-90, 90)
    longitude = random.uniform(-180, 180)
    center_point = (latitude, longitude)

    # Define half the side length in meters (250m)
    half_side = 250

    # Calculate the coordinates of the four corners
    # Starting from the center point, move in the cardinal directions
    north = geodesic(meters=half_side).destination(center_point, 0).latitude
    south = geodesic(meters=half_side).destination(center_point, 180).latitude
    east = geodesic(meters=half_side).destination(center_point, 90).longitude
    west = geodesic(meters=half_side).destination(center_point, 270).longitude

    # Create the square polygon coordinates (clockwise order)
    coordinates = [
        (west, north),  # Northwest corner
        (east, north),  # Northeast corner
        (east, south),  # Southeast corner
        (west, south),  # Southwest corner
        (west, north),  # Closing the polygon by returning to the first point
    ]

    # Create a Polygon object
    Polygon(coordinates)

    # Convert the polygon to a GeoJSON-like dictionary
    geojson_dict = {"type": "Polygon", "coordinates": [coordinates]}

    # Convert the dictionary to a GeoJSON string
    geojson_str = json.dumps(geojson_dict)
    return geojson_str


def random_md5() -> str:
    random_string = "".join(random.choices(string.ascii_letters + string.digits, k=32))
    md5_hash = hashlib.md5(random_string.encode("utf-8")).hexdigest()
    return md5_hash


def sowing_activity(
    event_id: str, field_geom: str, sowing_date: datetime
) -> SowingActivity:
    return (
        SowingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(sowing_date)
    )


def planting_activity(
    event_id: str, field_geom: str, planting_date: datetime
) -> PlantingActivity:
    return (
        PlantingActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(planting_date)
    )


def harvest_activity(
    event_id: str, field_geom: str, harvest_date: datetime
) -> HarvestActivity:
    return (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(harvest_date)
    )


def termination_activity(
    event_id: str, field_geom: str, termination_date: datetime
) -> TerminationActivity:
    return (
        TerminationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(termination_date)
    )


def tillage_activity(
    event_id: str, field_geom: str, tillage_date: datetime, depth_cm: float
) -> TillageActivity:
    return (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(tillage_date)
        .depth(pb_value.depth_centimetres(depth_cm))
    )


def irrigation_activity(
    event_id: str, field_geom: str, irrigation_date: datetime, depth_mm: float
) -> IrrigationActivity:
    return (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(irrigation_date)
        .water_applied(total_depth=pb_value.depth_millimetres(depth_mm))
    )


def fallow_period(
    event_id: str, field_geom: str, start_date: datetime, end_date: datetime
) -> FallowPeriod:
    return (
        FallowPeriod(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(start_date)
        .end(end_date)
    )


def sowing_alfalfa(
    event_id: str, field_geom: str, sowing_date: datetime
) -> SowingActivity:
    return sowing_activity(event_id, field_geom, sowing_date).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA)
    )


def sowing_alfalfa_purpose_cover(
    event_id: str, field_geom: str, sowing_date: datetime
) -> SowingActivity:
    return sowing_activity(event_id, field_geom, sowing_date).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA).purpose(
            crop_pb2.CROP_PURPOSE_COVER
        )
    )


def sowing_corn(
    event_id: str, field_geom: str, sowing_date: datetime
) -> SowingActivity:
    return sowing_activity(event_id, field_geom, sowing_date).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_CORN)
    )


def sowing_corn_purpose_commodity(
    event_id: str, field_geom: str, sowing_date: datetime
) -> SowingActivity:
    return sowing_activity(event_id, field_geom, sowing_date).crop(
        SownCrop(crop_id=crop_id_enum_pb2.CROP_ID_CORN).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )


def planting_rice(
    event_id: str, field_geom: str, planting_date: datetime
) -> PlantingActivity:
    return planting_activity(event_id, field_geom, planting_date).crop(
        PlantedCrop(crop_id=crop_id_enum_pb2.CROP_ID_RICE)
    )


def planting_rice_purpose_commodity(
    event_id: str, field_geom: str, planting_date: datetime
) -> PlantingActivity:
    return planting_activity(event_id, field_geom, planting_date).crop(
        PlantedCrop(crop_id=crop_id_enum_pb2.CROP_ID_RICE).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )


def harvest_alfalfa(
    event_id: str, field_geom: str, harvest_date: datetime
) -> HarvestActivity:
    return harvest_activity(event_id, field_geom, harvest_date).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA)
    )


def termination_alfalfa(
    event_id: str, field_geom: str, termination_date: datetime
) -> TerminationActivity:
    return termination_activity(event_id, field_geom, termination_date).crop(
        TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA)
    )


def harvest_alfalfa_purpose_fodder(
    event_id: str, field_geom: str, harvest_date: datetime
) -> HarvestActivity:
    return harvest_activity(event_id, field_geom, harvest_date).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_ALFALFA).purpose(
            crop_pb2.CROP_PURPOSE_FODDER
        )
    )


def harvest_corn(
    event_id: str, field_geom: str, harvest_date: datetime
) -> HarvestActivity:
    return harvest_activity(event_id, field_geom, harvest_date).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_CORN)
    )


def termination_corn(
    event_id: str, field_geom: str, termination_date: datetime
) -> TerminationActivity:
    return (
        termination_activity(event_id, field_geom, termination_date)
        .crop(TerminatedCrop(crop_id=crop_id_enum_pb2.CROP_ID_CORN))
        .reason(termination_pb2.TERMINATION_REASON_GRAIN_HARVEST)
    )


def harvest_corn_purpose_commodity(
    event_id: str, field_geom: str, harvest_date: datetime
) -> HarvestActivity:
    return harvest_activity(event_id, field_geom, harvest_date).crop(
        HarvestedCrop(crop_id=crop_id_enum_pb2.CROP_ID_CORN).purpose(
            crop_pb2.CROP_PURPOSE_COMMODITY_HARVEST
        )
    )
