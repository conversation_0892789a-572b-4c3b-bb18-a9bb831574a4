from ses_client.uuid import create_deterministic_uuid, is_valid_uuid


def test_create_deterministic_uuid_with_dict():
    """Test that the function creates a valid UUID from dictionary input."""
    data = {"user_id": 123, "action": "login", "timestamp": "2023-01-01"}
    result = create_deterministic_uuid(data)

    # Check that result is a valid UUID format
    assert is_valid_uuid(result), f"Result '{result}' is not a valid UUID format"

    # Check that result is exactly 36 characters (32 hex + 4 hyphens)
    assert len(result) == 36


def test_create_deterministic_uuid_with_string():
    """Test that the function creates a valid UUID from string input."""
    data = "test_string_input"
    result = create_deterministic_uuid(data)

    # Check that result is a valid UUID format
    assert is_valid_uuid(result), f"Result '{result}' is not a valid UUID format"

    # Check that result is exactly 36 characters
    assert len(result) == 36


def test_deterministic_behavior_dict():
    """Test that the same dictionary input always produces the same UUID."""
    data = {
        "event_id": "evt_123",
        "type": "user_action",
        "timestamp": "2023-01-01T12:00:00Z",
    }

    uuid1 = create_deterministic_uuid(data)
    uuid2 = create_deterministic_uuid(data)
    uuid3 = create_deterministic_uuid(data)

    assert uuid1 == uuid2 == uuid3, "Same input should always produce the same UUID"


def test_deterministic_behavior_string():
    """Test that the same string input always produces the same UUID."""
    data = "consistent_test_string"

    uuid1 = create_deterministic_uuid(data)
    uuid2 = create_deterministic_uuid(data)
    uuid3 = create_deterministic_uuid(data)

    assert uuid1 == uuid2 == uuid3, "Same input should always produce the same UUID"


def test_different_inputs_produce_different_uuids():
    """Test that different inputs produce different UUIDs."""
    data1 = {"user_id": 123, "action": "login"}
    data2 = {"user_id": 456, "action": "login"}
    data3 = {"user_id": 123, "action": "logout"}

    uuid1 = create_deterministic_uuid(data1)
    uuid2 = create_deterministic_uuid(data2)
    uuid3 = create_deterministic_uuid(data3)

    assert uuid1 != uuid2, "Different user_id should produce different UUIDs"
    assert uuid1 != uuid3, "Different action should produce different UUIDs"
    assert uuid2 != uuid3, "Different combinations should produce different UUIDs"


def test_dict_key_order_independence():
    """Test that dictionary key order doesn't affect the UUID."""
    data1 = {"user_id": 123, "action": "login", "timestamp": "2023-01-01"}
    data2 = {"timestamp": "2023-01-01", "user_id": 123, "action": "login"}
    data3 = {"action": "login", "timestamp": "2023-01-01", "user_id": 123}

    uuid1 = create_deterministic_uuid(data1)
    uuid2 = create_deterministic_uuid(data2)
    uuid3 = create_deterministic_uuid(data3)

    assert (
        uuid1 == uuid2 == uuid3
    ), "Dictionary key order should not affect UUID generation"


def test_namespace_parameter():
    """Test that different namespaces produce different UUIDs for the same data."""
    data = {"event_id": "evt_123", "type": "user_action"}

    uuid_default = create_deterministic_uuid(data)
    uuid_events = create_deterministic_uuid(data, namespace="events")
    uuid_users = create_deterministic_uuid(data, namespace="users")

    assert (
        uuid_default != uuid_events
    ), "Different namespaces should produce different UUIDs"
    assert (
        uuid_default != uuid_users
    ), "Different namespaces should produce different UUIDs"
    assert (
        uuid_events != uuid_users
    ), "Different namespaces should produce different UUIDs"


def test_namespace_consistency():
    """Test that the same namespace always produces the same UUID for the same data."""
    data = {"test": "data"}

    uuid1 = create_deterministic_uuid(data, namespace="test_namespace")
    uuid2 = create_deterministic_uuid(data, namespace="test_namespace")

    assert uuid1 == uuid2, "Same namespace should produce consistent UUIDs"


def test_empty_dict():
    """Test that empty dictionary produces a valid UUID."""
    data = {}
    result = create_deterministic_uuid(data)

    assert is_valid_uuid(result), "Empty dict should produce valid UUID"


def test_empty_string():
    """Test that empty string produces a valid UUID."""
    data = ""
    result = create_deterministic_uuid(data)

    assert is_valid_uuid(result), "Empty string should produce valid UUID"


def test_complex_nested_dict():
    """Test that complex nested dictionaries work correctly."""
    data = {
        "user": {
            "id": 123,
            "profile": {"name": "John Doe", "preferences": ["setting1", "setting2"]},
        },
        "event": {
            "type": "complex_action",
            "metadata": {"timestamp": "2023-01-01T12:00:00Z", "source": "api"},
        },
    }

    result = create_deterministic_uuid(data)
    assert is_valid_uuid(result), "Complex nested dict should produce valid UUID"

    # Test consistency
    result2 = create_deterministic_uuid(data)
    assert result == result2, "Complex nested dict should be deterministic"


def test_special_characters_in_data():
    """Test that special characters in data are handled correctly."""
    data = {
        "message": "Hello, World! @#$%^&*()_+-=[]{}|;':\",./<>?",
        "unicode": "café résumé naïve",
        "numbers": [1, 2.5, -3, 0],
        "boolean": True,
        "null_value": None,
    }

    result = create_deterministic_uuid(data)
    assert is_valid_uuid(result), "Special characters should be handled correctly"


def test_large_data_input():
    """Test that large data inputs work correctly."""
    # Create a large dictionary
    large_data = {}
    for i in range(1000):
        large_data[f"key_{i}"] = f"value_{i}" * 10

    result = create_deterministic_uuid(large_data)
    assert is_valid_uuid(result), "Large data input should produce valid UUID"


def test_numeric_types_consistency():
    """Test that different numeric types with same value produce different UUIDs."""
    data1 = {"value": 42}
    data3 = {"value": "42"}

    uuid1 = create_deterministic_uuid(data1)
    uuid3 = create_deterministic_uuid(data3)

    # Note: JSON serialization will treat 42 and 42.0 the same way
    # but "42" (string) should be different
    assert uuid3 != uuid1, "String '42' should produce different UUID than integer 42"


def test_real_world_event_data():
    """Test with realistic event data that might be used in the application."""
    event_data = {
        "event_id": "evt_20230101_001",
        "user_id": "user_12345",
        "action": "fetch_event_with_context",
        "timestamp": "2023-01-01T12:00:00Z",
        "metadata": {
            "source": "api",
            "version": "1.0",
            "request_id": "req_abcdef123456",
        },
        "context": {"field_id": "field_789", "crop_type": "corn", "season": "2023"},
    }

    result = create_deterministic_uuid(event_data)
    assert is_valid_uuid(result), "Real-world event data should produce valid UUID"

    # Test with event namespace
    result_with_namespace = create_deterministic_uuid(event_data, namespace="event")
    assert (
        result != result_with_namespace
    ), "Event namespace should produce different UUID"


def test_is_valid_uuid_function():
    """Test the is_valid_uuid validation function itself."""
    # Valid UUIDs
    assert is_valid_uuid("550e8400-e29b-41d4-a716-************")
    assert is_valid_uuid("6ba7b810-9dad-11d1-80b4-00c04fd430c8")
    assert is_valid_uuid("00000000-0000-0000-0000-000000000000")

    # Invalid UUIDs
    assert not is_valid_uuid("invalid-uuid")
    assert not is_valid_uuid("550e8400e29b41d4a716************")  # No hyphens
    assert not is_valid_uuid("550e8400-e29b-41d4-a716-44665544000")  # Wrong length
    assert not is_valid_uuid(
        "550e8400-e29b-41d4-a716-44665544000g"
    )  # Invalid character
    assert not is_valid_uuid(
        "550E8400-E29B-41D4-A716-************"
    )  # Uppercase (should be lowercase)
    assert not is_valid_uuid("")  # Empty string
    assert not is_valid_uuid("550e8400-e29b-41d4-a716")  # Too short
    assert not is_valid_uuid("550e8400-e29b-41d4-a716-************-extra")  # Too long

    # Test with None and other types
    assert not is_valid_uuid(None)
    assert not is_valid_uuid(123)
    assert not is_valid_uuid(["not", "a", "string"])
