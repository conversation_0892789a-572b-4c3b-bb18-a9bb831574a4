from datetime import datetime

import pytest
from regrow.ses.pbtype import line_pb2
from regrow.ses.pbtype.depth_pb2 import DepthMeasure
from ses_client import client, event, pb_value
from ses_client.irrigation import IrrigationActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


def test_draft_irrigation_activity():
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .end(datetime(2021, 1, 21, 0, 0))
        .validate()
    )

    assert ia.pb_event.id == event_id


def test_irrigation_activity_furrow():
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .end(datetime(2021, 1, 21, 0, 0))
        .method_furrow(furrow_area_fraction=pb_value.fraction_percent(15))
        .validate()
    )

    assert ia.pb_event.id == event_id
    assert ia.pb_event.irrigation_activity.furrow.furrow_area_fraction.percent == 15


def test_irrigation_activity_sub_surface():
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .end(datetime(2021, 1, 21, 0, 0))
        .method_subsurface(soil_depth=pb_value.depth_centimetres(2))
        .water_applied(total_volume=pb_value.megalitres(100))
        .validate()
    )

    assert ia.pb_event.id == event_id
    assert ia.pb_event.irrigation_activity.subsurface.soil_depth.value == 2
    assert (
        ia.pb_event.irrigation_activity.subsurface.soil_depth.unit
        == line_pb2.LINE_UNIT_CENTIMETRE
    )


@pytest.mark.integration
async def test_upsert_irrigation_activity_no_method_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no irrigation method so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_drip_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_drip()
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_drip_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_drip()
        .water_applied(total_depth=pb_value.depth_inches(5))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_flood_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_flood()
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_flood_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_flood()
        .water_applied(total_volume=pb_value.megalitres(100))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow(
            furrow_area_fraction=pb_value.fraction_of_one(0.5),
            furrow_depth=pb_value.depth_centimetres(10),
        )
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow(
            furrow_area_fraction=pb_value.fraction_percent(15.7),
            furrow_depth=pb_value.depth_inches(15),
        )
        .water_applied(total_depth=pb_value.depth_inches(5))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_mist_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_mist()
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_mist_method_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_mist()
        .water_applied(total_volume=pb_value.megalitres(100))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_sprinkler_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_sprinkler()
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_sprinkler_method_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_sprinkler()
        .water_applied(total_depth=pb_value.depth_millimetres(10))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_subsurface_method_no_water_total_draft():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    # no water amount so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_subsurface(soil_depth=pb_value.depth_inches(3))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_subsurface_method_with_water_total_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_subsurface(soil_depth=pb_value.depth_inches(3))
        .water_applied(total_volume=pb_value.megalitres(100))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_with_no_data():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow()
        .water_applied(total_depth=pb_value.depth_inches(1))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_with_area_fraction_no_water():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow(furrow_area_fraction=pb_value.fraction(0.2))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_with_area_percentage_with_water():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow(furrow_area_fraction=pb_value.percentage(25))
        .water_applied(total_depth=pb_value.depth_inches(1))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1  # <-- Should be enough for a valid revision


@pytest.mark.integration
async def test_upsert_irrigation_activity_furrow_method_with_depth_value_no_unit():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_furrow(
            furrow_area_fraction=pb_value.percentage(25),
            furrow_depth=DepthMeasure(value=6),
        )
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_subsurface_method_with_no_data():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_subsurface()
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_irrigation_activity_subsurface_method_with_soil_depth_value_no_unit():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    event_datetime = random_datetime()
    field_geom = random_polygon()

    event_id = event.new_event_id()
    ia = (
        IrrigationActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .method_subsurface(soil_depth=DepthMeasure(value=6))
        .validate()
    )

    _ = await event_client.upsert_event(ia)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0
