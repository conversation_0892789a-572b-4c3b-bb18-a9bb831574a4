import json

import pytest
from regrow.ses.context.v1 import context_pb2
from ses_client import client, event
from ses_client.event import StructuredEvent
from ses_client.tillage import TillageActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


@pytest.mark.integration
async def test_upsert_tillage_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    e = await event_client.upsert_event(ta)

    se = await event_client.fetch_event_with_context(e.event.id)
    assert se.event.id == event_id
    assert se.context.creation["regrowUserId"] == TEST_USER_ID
    assert se.context.source["regrowUserId"] == TEST_USER_ID

    # new context
    assoc_ctx = json.dumps(
        {
            StructuredEvent.TYPE_TILLAGE_ACTIVITY: False,
            StructuredEvent.TYPE_APPLICATION_ACTIVITY: True,
            StructuredEvent.TYPE_IRRIGATION_ACTIVITY: True,
        }
    )
    new_context = context_pb2.EventContext(
        creation={"regrowUserId": "XYZ-789"},
        source={"regrowUserId": "abc-123"},
        association={
            f"{StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION}": assoc_ctx
        },
    )

    # update context
    _ = await event_client.add_event_context(event_id, new_context)

    se = await event_client.fetch_event_with_context(e.event.id)
    assert se.context.creation["regrowUserId"] == "XYZ-789"
    assert se.context.source["regrowUserId"] == "abc-123"
    assert (
        se.context.association[f"{StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION}"]
        == assoc_ctx
    )
