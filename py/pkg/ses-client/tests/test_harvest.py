import json
from datetime import datetime

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from ses_client import client, event, pb_value
from ses_client.crop import HarvestedCrop, crop_id_from_label
from ses_client.event import StructuredEvent, get_cropping_period_identifier
from ses_client.harvest import HarvestActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


def test_harvest_activity():
    event_id = event.new_event_id()
    harvested_crop_1 = HarvestedCrop(crop_id_enum_pb2.CROP_ID_ALFALFA).mass_yield(
        pb_value.tonnes_per_hectare(1.5)
    )
    HarvestActivity(event_id=event_id, user_id="user-123").geom("{}").start(
        datetime(2021, 1, 21, 0, 0)
    ).crop(harvested_crop_1).validate()


@pytest.mark.integration
async def test_upsert_harvest_activity_validation():
    """Test that the harvest activity event is validated correctly."""
    await health_check(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()

    with pytest.raises(ValueError):
        """Missing event id"""
        HarvestActivity(event_id="", user_id=TEST_USER_ID).geom(field_geom).start(
            event_datetime
        ).validate()

    with pytest.raises(ValueError):
        """Missing event time"""
        HarvestActivity(event_id=event.new_event_id(), user_id=TEST_USER_ID).geom(
            field_geom
        ).validate()

    with pytest.raises(ValueError):
        """Missing event geom"""
        HarvestActivity(event_id=event.new_event_id(), user_id=TEST_USER_ID).start(
            event_datetime
        ).validate()


@pytest.mark.integration
async def test_upsert_harvest_activity_draft_no_crop():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = datetime.now()

    # no crop so expect revision = 0 (draft)
    event_id = event.new_event_id()
    ha = (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_harvest_activity_revision_no_yield():
    """Test that the harvest activity event is validated correctly. A yield is no longer required for a valid revision."""
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    cropping_period_id = event.new_event_id()
    ha = (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(HarvestedCrop(crop_id=crop_id))
        .cropping_period_identifier(cropping_period_id)
    )
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_harvest_activity_revision_1():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    cropping_period_id = event.new_event_id()
    ha = (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(
            harvested_crop=(
                HarvestedCrop(crop_id=crop_id)
                .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
                .mass_yield(pb_value.tonnes_per_hectare(1.5))
            )
        )
        .cropping_period_identifier(cropping_period_id)
    )
    assert ha.pb_event.cropping_period_identifier == cropping_period_id
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 1
    assert len(se.harvest_activity.crops) == 1
    assert se.harvest_activity.crops[0].crop.id == crop_id
    assert (
        se.harvest_activity.crops[0].crop.purpose[0]
        == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
    )
    assert get_cropping_period_identifier(se) == cropping_period_id


@pytest.mark.integration
async def test_upsert_harvest_activity_revision_2():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    ha = (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(
            HarvestedCrop(crop_id=crop_id).volume_yield(pb_value.bushels_per_acre(125))
        )
    )
    _ = await event_client.upsert_event(ha)

    ha = (
        HarvestActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
        .crop(
            HarvestedCrop(crop_id=crop_id).mass_yield(pb_value.short_tons_per_acre(1.5))
        )
    )
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 2


@pytest.mark.integration
async def test_upsert_harvest_activity_with_invalid_association_context_key_raises_error():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")
    cropping_period_id = event.new_event_id()

    ha = (
        HarvestActivity(event_id, TEST_USER_ID)
        .start(event_datetime)
        .geom(field_geom)
        .crop(
            HarvestedCrop(crop_id=crop_id).mass_yield(pb_value.short_tons_per_acre(1.5))
        )
        .cropping_period_identifier(cropping_period_id)
    )
    ha.association_context("invalid-association-context-key", '{"key": "value"}')

    with pytest.raises(ValueError):
        await event_client.upsert_event(ha)


@pytest.mark.integration
async def test_upsert_harvest_activity_with_context():
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    crop_id = crop_id_from_label("rye")

    ha = (
        HarvestActivity(event_id, TEST_USER_ID)
        .start(event_datetime)
        .geom(field_geom)
        .crop(
            HarvestedCrop(crop_id=crop_id).mass_yield(pb_value.short_tons_per_acre(1.5))
        )
    )

    # set the context with a dict
    no_obs_context_dict = {
        "key1": "value1",
        "key2": "value2",
        "key3": "value3",
    }
    ha.no_practice_observation(no_obs_context_dict)
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    res = await event_client.fetch_event_with_context(event_id)
    assert res.event.id == event_id
    assert res.event.revision == 1
    assert (
        res.context.creation[StructuredEvent.CONTEXT_KEY_REGROW_USER_ID] == TEST_USER_ID
    )
    assert (
        res.context.source[StructuredEvent.CONTEXT_KEY_REGROW_USER_ID] == TEST_USER_ID
    )
    assert res.context.association[
        StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION
    ] == json.dumps(no_obs_context_dict)

    # set the context with a string
    no_obs_context_str = '{"key4": "value4", "key5": "value5", "key6": "value6"}'
    ha.no_practice_observation(no_obs_context_str)
    _ = await event_client.upsert_event(ha)

    # re-fetch the event and check stuff
    # Note: here the upsert will be a no-op but the service has been updated to continue with context upsert regardless.
    res = await event_client.fetch_event_with_context(event_id)
    assert res.event.id == event_id
    assert res.event.revision == 1
    assert (
        res.context.creation[StructuredEvent.CONTEXT_KEY_REGROW_USER_ID] == TEST_USER_ID
    )
    assert (
        res.context.source[StructuredEvent.CONTEXT_KEY_REGROW_USER_ID] == TEST_USER_ID
    )
    assert (
        res.context.association[StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION]
        == no_obs_context_str
    )
