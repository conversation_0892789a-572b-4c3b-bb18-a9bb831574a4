import json
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.tillage.v1 import tillage_pb2
from regrow.ses.application.v1 import application_pb2
from regrow.ses.termination.v1 import termination_pb2
from ses_client import event, pb_value
from ses_client.application import ApplicationBroadcast
from ses_client.boundaries import BoundariesClient
from ses_client.client import Client, EventNotFoundError
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from regrow.ses.event.v1.event_pb2 import Event
from ses_client.crop import (
    HarvestedCrop,
    PlantedCrop,
    SownCrop,
    TerminatedCrop,
    crop_id_from_label,
)
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.irrigation import IrrigationActivity
from ses_client.planting import PlantingActivity
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
    random_md5,
)


def create_mock_upsert_response(event_id: str) -> UpsertEventResponse:
    """Helper function to create a mock UpsertEventResponse with the given event ID."""
    from unittest.mock import MagicMock

    # Create a mock UpsertEventResponse
    mock_response = MagicMock(spec=UpsertEventResponse)
    mock_response.event = MagicMock(spec=Event)
    mock_response.event.id = event_id
    mock_response.context = MagicMock()
    return mock_response


@pytest.mark.integration
class TestCopyEventToField:
    """Test cases for the copy_event_to_field functionality."""

    @pytest.mark.asyncio
    async def test_copy_event_to_field_no_boundaries_service(self):
        """Test that copy_event_to_field raises error when boundaries service is not initialized."""
        client = Client("localhost:50051")

        with pytest.raises(ValueError, match="Boundaries service not initialized"):
            await client.copy_event_to_field("test_event_id", "test_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_basic_flow(self):
        """Test the basic flow of copying an event to a different field."""
        # Setup
        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock a simple source event and context (we'll mock the protobuf objects)
        from unittest.mock import MagicMock

        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"

        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}

        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        # Mock fetch_event_with_context to return our source event with context
        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to return a mock structured event
        mock_new_event = MagicMock()
        mock_new_event.pb_event.id = "new_event_id"
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return a response with the deterministic UUID
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("deterministic_uuid")
        )

        # Mock create_deterministic_uuid
        client.create_deterministic_uuid = MagicMock(return_value="deterministic_uuid")

        # Test
        field_id = "test_field_md5_hash"
        new_event_id_result = await client.copy_event_to_field(
            "source_event_id", field_id
        )

        # Verify boundaries service was called
        mock_boundaries.fetch_geometry_str.assert_called_once_with(field_id)

        # Verify fetch_event_with_context was called
        client.fetch_event_with_context.assert_called_once_with("source_event_id")

        # Verify create_deterministic_uuid was called with correct parameters
        client.create_deterministic_uuid.assert_called_once_with(
            "source_event_id", namespace=field_id
        )

        # Verify _create_event_copy was called with correct parameters
        client._create_event_copy.assert_called_once_with(
            mock_source_event,
            mock_source_context,
            "deterministic_uuid",
            target_geometry,
        )

        # Verify upsert_event was called
        client.upsert_event.assert_called_once_with(mock_new_event)

        # Verify the returned event ID
        assert new_event_id_result == "deterministic_uuid"

    def test_deterministic_uuid_behavior(self):
        """Test that deterministic UUID generation works as expected."""
        client = Client("localhost:50051")

        # Test data
        test_data = {"event_type": "tillage", "depth": 5}
        field_id = "test_field_md5_hash"

        # Generate UUID twice with same data
        uuid1 = client.create_deterministic_uuid(test_data, namespace=field_id)
        uuid2 = client.create_deterministic_uuid(test_data, namespace=field_id)

        # Should be the same
        assert uuid1 == uuid2

        # Different field ID should generate different UUID
        different_field_id = "different_field_md5_hash"
        uuid3 = client.create_deterministic_uuid(
            test_data, namespace=different_field_id
        )

        assert uuid1 != uuid3

    @pytest.mark.asyncio
    async def test_copy_event_to_field_boundary_service_error(self):
        """Test that copy_event_to_field handles boundary service errors properly."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event first (since it's fetched before boundaries service)
        mock_source_event = MagicMock()
        mock_source_event.id = "test_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock the boundaries service to raise an exception
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ValueError("Field not found")
        client.boundaries_service = mock_boundaries

        # Test that the exception is propagated
        with pytest.raises(ValueError, match="Field not found"):
            await client.copy_event_to_field("test_event_id", "invalid_field_id")

        # Verify both services were called in the correct order
        client.fetch_event_with_context.assert_called_once_with("test_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("invalid_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_boundary_service_network_error(self):
        """Test that copy_event_to_field handles network errors from boundary service."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event first (since it's fetched before boundaries service)
        mock_source_event = MagicMock()
        mock_source_event.id = "test_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock the boundaries service to raise a connection error
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ConnectionError("Network timeout")
        client.boundaries_service = mock_boundaries

        # Test that the exception is propagated
        with pytest.raises(ConnectionError, match="Network timeout"):
            await client.copy_event_to_field("test_event_id", "test_field_id")

        # Verify both services were called
        client.fetch_event_with_context.assert_called_once_with("test_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_source_event_not_found(self):
        """Test that copy_event_to_field handles EventNotFoundError properly."""
        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to return valid geometry
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps({
            "type": "Polygon",
            "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
        })
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock fetch_event_with_context to raise EventNotFoundError
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("nonexistent_event_id")
        )

        # Test that EventNotFoundError is propagated
        with pytest.raises(EventNotFoundError, match="No result for event_id: nonexistent_event_id"):
            await client.copy_event_to_field("nonexistent_event_id", "test_field_id")

        # Verify only fetch_event_with_context was called (boundaries service not called due to early failure)
        client.fetch_event_with_context.assert_called_once_with("nonexistent_event_id")
        mock_boundaries.fetch_geometry_str.assert_not_called()

    @pytest.mark.asyncio
    async def test_copy_event_to_field_invalid_geometry(self):
        """Test that copy_event_to_field handles invalid/malformed geometry gracefully."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to return invalid geometry
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        invalid_geometry = "invalid json geometry"
        mock_boundaries.fetch_geometry_str.return_value = invalid_geometry
        client.boundaries_service = mock_boundaries

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock create_deterministic_uuid
        client.create_deterministic_uuid = MagicMock(return_value="test_uuid")

        # Mock _create_event_copy to potentially fail with invalid geometry
        client._create_event_copy = MagicMock(
            side_effect=ValueError("Invalid geometry format")
        )

        # Test that the error is propagated when _create_event_copy fails
        with pytest.raises(ValueError, match="Invalid geometry format"):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify all services were called in the correct order
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        client.create_deterministic_uuid.assert_called_once_with("source_event_id", namespace="test_field_id")
        client._create_event_copy.assert_called_once_with(
            mock_source_event, mock_source_context, "test_uuid", invalid_geometry
        )

    @pytest.mark.asyncio
    async def test_copy_event_to_field_upsert_failure(self):
        """Test that copy_event_to_field handles upsert failures properly."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps({
            "type": "Polygon",
            "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
        })
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock create_deterministic_uuid and _create_event_copy to succeed
        client.create_deterministic_uuid = MagicMock(return_value="test_uuid")
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to fail
        client.upsert_event = AsyncMock(
            side_effect=RuntimeError("Database connection failed")
        )

        # Test that the upsert error is propagated
        with pytest.raises(RuntimeError, match="Database connection failed"):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify all services were called up to the upsert failure
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        client.create_deterministic_uuid.assert_called_once_with("source_event_id", namespace="test_field_id")
        client._create_event_copy.assert_called_once_with(
            mock_source_event, mock_source_context, "test_uuid", target_geometry
        )
        client.upsert_event.assert_called_once_with(mock_new_event)

    @pytest.mark.asyncio
    async def test_copy_event_to_field_no_partial_state_on_error(self):
        """Test that no partial state changes occur when errors happen during copy operation."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock fetch_event_with_context to fail early
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("source_event_id")
        )

        # Mock other services that should NOT be called due to early failure
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        client.boundaries_service = mock_boundaries
        client.create_deterministic_uuid = MagicMock()
        client._create_event_copy = MagicMock()
        client.upsert_event = AsyncMock()

        # Test that the event not found error is propagated
        with pytest.raises(EventNotFoundError):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify that only fetch_event_with_context was called, no other operations occurred
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        mock_boundaries.fetch_geometry_str.assert_not_called()
        client.create_deterministic_uuid.assert_not_called()
        client._create_event_copy.assert_not_called()
        client.upsert_event.assert_not_called()

    @pytest.mark.asyncio
    async def test_copy_event_to_field_error_messages_contain_context(self):
        """Test that error messages contain relevant context information."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Test 1: Boundary service error with field ID context
        # First mock a valid event fetch
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ValueError("Field 'test_field_123' not found")
        client.boundaries_service = mock_boundaries

        with pytest.raises(ValueError) as exc_info:
            await client.copy_event_to_field("source_event_id", "test_field_123")

        # Verify error message contains field ID context
        assert "test_field_123" in str(exc_info.value)

        # Test 2: Event not found error with event ID context
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("missing_event_456")
        )

        with pytest.raises(EventNotFoundError) as exc_info:
            await client.copy_event_to_field("missing_event_456", "test_field_id")

        # Verify error message contains event ID context
        assert "missing_event_456" in str(exc_info.value)
        assert exc_info.value.event_id == "missing_event_456"

    @pytest.mark.asyncio
    async def test_copy_event_to_field_returns_actual_upserted_event_id(self):
        """Test that copy_event_to_field returns the actual event ID from upsert response, not the deterministic UUID."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps({
            "type": "Polygon",
            "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
        })
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock create_deterministic_uuid to return one ID
        client.create_deterministic_uuid = MagicMock(return_value="deterministic_uuid_123")

        # Mock _create_event_copy
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return a DIFFERENT event ID (simulating duplicate detection)
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("existing_event_456")
        )

        # Test
        result = await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify the result is the actual event ID from upsert response, NOT the deterministic UUID
        assert result == "existing_event_456"
        assert result != "deterministic_uuid_123"

        # Verify all services were called correctly
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        client.create_deterministic_uuid.assert_called_once_with("source_event_id", namespace="test_field_id")
        client._create_event_copy.assert_called_once_with(
            mock_source_event, mock_source_context, "deterministic_uuid_123", target_geometry
        )
        client.upsert_event.assert_called_once_with(mock_new_event)


@pytest.mark.integration
class TestCopyEventToFieldIntegration:
    """Integration tests for the copy_event_to_field functionality."""

    def _get_mock_target_geometry(self):
        """Helper to get consistent mock target geometry."""
        return json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )

    async def _setup_client_with_mock_boundaries(self):
        """Helper to setup client with mocked boundaries service."""
        # Use a dummy URL since we'll mock the boundaries service
        boundaries_service_url = "http://mock-boundaries-service"
        client = Client(event_srvc_addr, boundaries_service_url=boundaries_service_url)
        return client

    async def test_copy_tillage_activity_to_field(self):
        """Test copying a tillage activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source tillage activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_tillage = (
                TillageActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .depth(pb_value.depth_inches(2.5))
                .soil_inversion(True)
                .strip_tillage_percent(55.5)
                .implement(tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW)
                .tillage_practice(tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_tillage)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_tillage.pb_event.interval.start_time.seconds
            )

            # Verify tillage-specific data was copied
            assert copied_event.tillage_activity.depth.value == 2.5
            assert copied_event.tillage_activity.soil_inversion is True
            assert copied_event.tillage_activity.strip_tillage_fraction.percent == 55.5
            assert (
                    copied_event.tillage_activity.implement
                    == tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW
            )
            assert (
                    copied_event.tillage_activity.tillage_practice
                    == tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED
            )

            # Verify geometry is different (should be from target field)
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_sowing_activity_to_field(self):
        """Test copying a sowing activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source sowing activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            cropping_period_id = event.new_event_id()

            source_sowing = (
                SowingActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    SownCrop(crop_id=crop_id_from_label("rye")).sowing_rate_kg_per_ha(
                        100
                    )
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_sowing)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_sowing.pb_event.interval.start_time.seconds
            )
            assert copied_event.cropping_period_identifier == cropping_period_id

            # Verify sowing-specific data was copied
            assert len(copied_event.sowing_activity.crops) == 1
            assert (
                    copied_event.sowing_activity.crops[0].seed_mass_rate.mass.value == 100
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_planting_activity_to_field(self):
        """Test copying a planting activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source planting activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            crop_id = crop_id_from_label("rice")
            cropping_period_id = event.new_event_id()

            source_planting = (
                PlantingActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    PlantedCrop(crop_id=crop_id)
                    .planting_rate_plants_per_sqm(64)
                    .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_planting)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_planting.pb_event.interval.start_time.seconds
            )
            assert copied_event.cropping_period_identifier == cropping_period_id

            # Verify planting-specific data was copied
            assert len(copied_event.planting_activity.crops) == 1
            assert copied_event.planting_activity.crops[0].crop.id == crop_id
            assert (
                    copied_event.planting_activity.crops[0].planting_rate.count.value == 64
            )
            assert (
                    copied_event.planting_activity.crops[0].crop.purpose[0]
                    == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_application_activity_to_field(self):
        """Test copying an application activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source application activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_application = (
                ApplicationBroadcast(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .input(
                    BasicFertiliser(input_name="npk")
                    .mass_rate(pb_value.pounds_per_acre(150))
                    .pb()
                )
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_application)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_application.pb_event.interval.start_time.seconds
            )

            # Verify application-specific data was copied
            assert (
                    copied_event.application_activity.method
                    == application_pb2.APPLICATION_METHOD_BROADCAST
            )
            assert len(copied_event.application_activity.inputs) == 1
            assert (
                    copied_event.application_activity.inputs[0].basic_fertiliser.name
                    == "npk"
            )
            assert (
                    copied_event.application_activity.inputs[0].mass_rate.mass.value == 150
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_harvest_activity_to_field(self):
        """Test copying a harvest activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source harvest activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            crop_id = crop_id_from_label("rye")
            cropping_period_id = event.new_event_id()

            source_harvest = (
                HarvestActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    HarvestedCrop(crop_id=crop_id)
                    .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
                    .mass_yield(pb_value.tonnes_per_hectare(1.5))
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_harvest)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_harvest.pb_event.interval.start_time.seconds
            )
            assert copied_event.cropping_period_identifier == cropping_period_id

            # Verify harvest-specific data was copied
            assert len(copied_event.harvest_activity.crops) == 1
            assert copied_event.harvest_activity.crops[0].crop.id == crop_id
            assert (
                    copied_event.harvest_activity.crops[0].crop.purpose[0]
                    == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
            )
            assert copied_event.harvest_activity.crops[0].mass_yield.mass.value == 1.5

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_irrigation_activity_to_field(self):
        """Test copying an irrigation activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source irrigation activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_irrigation = (
                IrrigationActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .method_sprinkler()
                .water_applied(total_depth=pb_value.depth_millimetres(10))
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_irrigation)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_irrigation.pb_event.interval.start_time.seconds
            )

            # Verify irrigation-specific data was copied
            assert copied_event.irrigation_activity.water_depth_total.value == 10

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_fallow_period_to_field(self):
        """Test copying a fallow period to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source fallow period
            source_field_geom = random_polygon()
            start_datetime = random_datetime()
            end_datetime = datetime(
                start_datetime.year + 1, start_datetime.month, start_datetime.day
            )
            source_event_id = event.new_event_id()

            source_fallow = (
                FallowPeriod(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(start_datetime)
                .end(end_datetime)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_fallow)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Use a larger tolerance for timestamp comparison due to potential timezone differences
            # Allow up to 24 hours difference to account for timezone handling
            assert (
                    abs(
                        copied_event.interval.start_time.seconds
                        - source_fallow.pb_event.interval.start_time.seconds
                    )
                    <= 86400
            )
            assert (
                    abs(
                        copied_event.interval.end_time.seconds
                        - source_fallow.pb_event.interval.end_time.seconds
                    )
                    <= 86400
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_termination_activity_to_field(self):
        """Test copying a termination activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source termination activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_termination = (
                TerminationActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(terminated_crop=TerminatedCrop(crop_id_enum_pb2.CROP_ID_ALFALFA))
                .reason(
                    termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST
                )
                .method(termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_termination)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            assert (
                    copied_event.interval.start_time.seconds
                    == source_termination.pb_event.interval.start_time.seconds
            )

            # Verify termination-specific data was copied
            assert len(copied_event.termination_activity.crops) == 1
            assert (
                    copied_event.termination_activity.crops[0].crop.id
                    == crop_id_enum_pb2.CROP_ID_ALFALFA
            )
            assert (
                    copied_event.termination_activity.reason
                    == termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST
            )
            assert (
                    copied_event.termination_activity.method
                    == termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_event_deterministic_behavior(self):
        """Test that copying the same event to the same field always produces the same result."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
                client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source event
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_event = (
                TillageActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .depth(pb_value.depth_inches(3))
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_event)

            # Copy to same field twice
            target_field_id = random_md5()
            new_event_id_1 = await client.copy_event_to_field(
                source_event_id, target_field_id
            )
            new_event_id_2 = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called twice
            assert mock_fetch.call_count == 2

            # Should generate the same UUID both times
            assert new_event_id_1 == new_event_id_2


@pytest.mark.integration
class TestBulkCopyEvents:
    """Test cases for the bulk_copy_events functionality."""

    @pytest.mark.asyncio
    async def test_bulk_copy_events_no_boundaries_service(self):
        """Test that bulk_copy_events raises error when boundaries service is not initialized."""
        from ses_client.client import CopyEventsToFieldRequest

        client = Client("localhost:50051")
        requests = [CopyEventsToFieldRequest(["event1"], "field1")]

        with pytest.raises(ValueError, match="Boundaries service not initialized"):
            await client.bulk_copy_events(requests)

    @pytest.mark.asyncio
    async def test_bulk_copy_events_empty_requests(self):
        """Test bulk copy with empty request list."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        client.boundaries_service = mock_boundaries

        results = await client.bulk_copy_events([])
        assert results == []

    @pytest.mark.asyncio
    async def test_copy_event_stable_id_generation(self):
        """Test that copying an event generates the same ID even if the event content changes."""
        from ses_client.client import CopyEventsToFieldRequest
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Create two different mock events with the same ID
        event_id = "test_event_id"
        field_id = "test_field_id"

        # First event version
        mock_event1 = MagicMock()
        mock_event1.id = event_id
        mock_event1.tillage_activity.depth.value = 5

        # Second event version (with different content)
        mock_event2 = MagicMock()
        mock_event2.id = event_id
        mock_event2.tillage_activity.depth.value = 10

        # Mock context
        mock_context = MagicMock()

        # Create response objects
        mock_response1 = MagicMock()
        mock_response1.event = mock_event1
        mock_response1.context = mock_context

        mock_response2 = MagicMock()
        mock_response2.event = mock_event2
        mock_response2.context = mock_context

        # Setup fetch_event_with_context to return different versions
        client.fetch_event_with_context = AsyncMock(side_effect=[mock_response1, mock_response2])

        # Mock other methods
        client._create_event_copy = MagicMock()
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("stable_uuid")
        )

        # Mock create_deterministic_uuid to return a consistent value
        client.create_deterministic_uuid = MagicMock(return_value="stable_uuid")

        # Test first copy
        new_id1 = await client.copy_event_to_field(event_id, field_id)

        # Test second copy (with different event content)
        new_id2 = await client.copy_event_to_field(event_id, field_id)

        # IDs should be the same despite different event content
        assert new_id1 == new_id2

        # Verify deterministic_uuid was called with event_id, not the whole event object
        client.create_deterministic_uuid.assert_called_with(event_id, namespace=field_id)

    @pytest.mark.asyncio
    async def test_bulk_copy_events_single_request(self):
        """Test bulk copy with a single request containing one event."""
        from ses_client.client import CopyEventsToFieldRequest
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )
        client.create_deterministic_uuid = MagicMock(return_value="deterministic_uuid")

        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("deterministic_uuid")
        )

        # Test
        requests = [CopyEventsToFieldRequest(["event1"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 1
        result = results[0]
        assert result.source_event_id == "event1"
        assert result.target_field_id == "field1"
        assert result.new_event_id == "deterministic_uuid"
        assert result.error is None
        assert result.success is True

        # Verify boundaries service was called
        mock_boundaries.fetch_geometry_str.assert_called_once_with("field1")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_multiple_events_to_one_field(self):
        """Test bulk copy with multiple events to a single field."""
        from ses_client.client import CopyEventsToFieldRequest
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock deterministic UUID to return different UUIDs for different events
        def mock_uuid_generator(event_id, namespace):
            return f"uuid_{event_id}_{namespace}"

        client.create_deterministic_uuid = MagicMock(side_effect=mock_uuid_generator)

        # Mock _create_event_copy to return an event with proper pb_event structure
        def mock_create_event_copy(source_event, source_context, new_event_id, target_geometry):
            mock_new_event = MagicMock()
            mock_new_event.pb_event = MagicMock()
            mock_new_event.pb_event.id = new_event_id
            return mock_new_event

        client._create_event_copy = MagicMock(side_effect=mock_create_event_copy)

        # Mock upsert_event to return actual event IDs (simulating potential duplicates)
        upsert_call_count = 0
        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            # Return actual event IDs that may differ from deterministic UUIDs
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test - multiple events to one field
        requests = [CopyEventsToFieldRequest(["event1", "event2", "event3"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 3

        for i, result in enumerate(results):
            expected_event_id = f"event{i + 1}"
            assert result.source_event_id == expected_event_id
            assert result.target_field_id == "field1"
            # Should return the actual event ID from upsert, not the deterministic UUID
            assert result.new_event_id == f"actual_event_{i + 1}"
            assert result.error is None
            assert result.success is True

        # Verify boundaries service was called only once (batched)
        mock_boundaries.fetch_geometry_str.assert_called_once_with("field1")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_many_to_many(self):
        """Test bulk copy with many events to many fields (each field gets different events)."""
        from ses_client.client import CopyEventsToFieldRequest
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock deterministic UUID to return different UUIDs for different event-field combinations
        def mock_uuid_generator(event_id, namespace):
            return f"uuid_{event_id}_{namespace}"

        client.create_deterministic_uuid = MagicMock(side_effect=mock_uuid_generator)

        # Mock _create_event_copy to return an event with proper pb_event structure
        def mock_create_event_copy(source_event, source_context, new_event_id, target_geometry):
            mock_new_event = MagicMock()
            mock_new_event.pb_event = MagicMock()
            mock_new_event.pb_event.id = new_event_id
            return mock_new_event

        client._create_event_copy = MagicMock(side_effect=mock_create_event_copy)

        # Mock upsert_event to return actual event IDs (simulating potential duplicates)
        upsert_call_count = 0
        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            # Return actual event IDs that may differ from deterministic UUIDs
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test - many events to many fields (each field gets different events)
        requests = [
            CopyEventsToFieldRequest(["event1", "event2"], "field1"),
            CopyEventsToFieldRequest(["event3"], "field2"),
            CopyEventsToFieldRequest(["event1", "event4", "event5"], "field3"),
        ]
        results = await client.bulk_copy_events(requests)

        # Verify results - should have 6 total results (2 + 1 + 3)
        assert len(results) == 6

        # Check specific results
        expected_combinations = [
            ("event1", "field1"),
            ("event2", "field1"),
            ("event3", "field2"),
            ("event1", "field3"),
            ("event4", "field3"),
            ("event5", "field3"),
        ]

        for i, (expected_event, expected_field) in enumerate(expected_combinations):
            result = results[i]
            assert result.source_event_id == expected_event
            assert result.target_field_id == expected_field
            # Should return the actual event ID from upsert, not the deterministic UUID
            assert result.new_event_id == f"actual_event_{i + 1}"
            assert result.error is None
            assert result.success is True

        # Verify boundaries service was called for each unique field (batched)
        assert mock_boundaries.fetch_geometry_str.call_count == 3
        mock_boundaries.fetch_geometry_str.assert_any_call("field1")
        mock_boundaries.fetch_geometry_str.assert_any_call("field2")
        mock_boundaries.fetch_geometry_str.assert_any_call("field3")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_with_errors(self):
        """Test bulk copy with some operations failing."""
        from ses_client.client import CopyEventsToFieldRequest
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to fail for field2
        mock_boundaries = AsyncMock(spec=BoundariesClient)

        def mock_fetch_geometry(field_id):
            if field_id == "field2":
                raise ValueError("Field not found")
            return json.dumps(
                {
                    "type": "Polygon",
                    "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
                }
            )

        mock_boundaries.fetch_geometry_str.side_effect = mock_fetch_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies for successful cases
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )
        client.create_deterministic_uuid = MagicMock(return_value="deterministic_uuid")

        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("deterministic_uuid")
        )

        # Test - mix of successful and failing operations
        requests = [
            CopyEventsToFieldRequest(["event1"], "field1"),  # Should succeed
            CopyEventsToFieldRequest(
                ["event2"], "field2"
            ),  # Should fail (geometry fetch fails)
            CopyEventsToFieldRequest(["event3"], "field1"),  # Should succeed
        ]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 3

        # First result should succeed
        assert results[0].source_event_id == "event1"
        assert results[0].target_field_id == "field1"
        assert results[0].new_event_id == "deterministic_uuid"
        assert results[0].error is None
        assert results[0].success is True

        # Second result should fail
        assert results[1].source_event_id == "event2"
        assert results[1].target_field_id == "field2"
        assert results[1].new_event_id is None
        assert "Failed to fetch field geometry" in results[1].error
        assert results[1].success is False

        # Third result should succeed
        assert results[2].source_event_id == "event3"
        assert results[2].target_field_id == "field1"
        assert results[2].new_event_id == "deterministic_uuid"
        assert results[2].error is None
        assert results[2].success is True

    @pytest.mark.asyncio
    async def test_bulk_copy_events_returns_actual_upserted_event_ids(self):
        """Test that bulk_copy_events returns actual event IDs from upsert responses, not deterministic UUIDs."""
        from ses_client.client import CopyEventsToFieldRequest

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps({
            "type": "Polygon",
            "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
        })
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock deterministic UUID generation
        def mock_uuid_generator(event_id, namespace):
            return f"deterministic_{event_id}_{namespace}"

        client.create_deterministic_uuid = MagicMock(side_effect=mock_uuid_generator)

        # Mock _create_event_copy
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return different actual event IDs (simulating some duplicates)
        upsert_call_count = 0
        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            # Return different actual IDs to simulate duplicate detection
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test
        requests = [CopyEventsToFieldRequest(["event1", "event2"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify we got the actual event IDs, not the deterministic ones
        assert len(results) == 2
        assert results[0].new_event_id == "actual_event_1"
        assert results[0].new_event_id != "deterministic_event1_field1"
        assert results[1].new_event_id == "actual_event_2"
        assert results[1].new_event_id != "deterministic_event2_field1"

        # Verify both operations succeeded
        assert results[0].success is True
        assert results[1].success is True
        assert results[0].error is None
        assert results[1].error is None
