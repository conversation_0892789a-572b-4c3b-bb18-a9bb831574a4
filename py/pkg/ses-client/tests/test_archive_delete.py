import pytest
from ses_client import client, event
from ses_client.tillage import TillageActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
)


@pytest.mark.integration
async def test_delete_event():
    """Test deleting a single event."""
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)
    res = await event_client.delete_event(event_id=event_id)
    assert res.id == event_id


@pytest.mark.integration
async def test_archive_event():
    """Test archiving a single event."""
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)
    res = await event_client.archive_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id
    # should be idempotent (a no-op)
    res = await event_client.archive_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id


@pytest.mark.integration
async def test_restore_event():
    """Test restore a single event."""
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)

    # archive it first
    res = await event_client.archive_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id

    # now restore it
    res = await event_client.restore_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id

    # should be idempotent (a no-op)
    res = await event_client.restore_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id


@pytest.mark.integration
async def test_delete_or_archive_event():
    """Test delete or archive method."""
    await health_check(event_srvc_addr)
    event_client = client.Client(event_srvc_addr)
    field_geom = random_polygon()
    event_datetime = random_datetime()
    event_id = event.new_event_id()
    ta = (
        TillageActivity(event_id=event_id, user_id=TEST_USER_ID)
        .geom(field_geom)
        .start(event_datetime)
    )
    _ = await event_client.upsert_event(ta)
    res = await event_client.delete_or_archive_event_for_user(
        event_id=event_id, user_id=TEST_USER_ID
    )
    assert res.id == event_id
