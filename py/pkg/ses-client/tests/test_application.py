from datetime import datetime

import pytest
from regrow.ses.application.v1 import application_input_id_pb2, application_pb2
from regrow.ses.pbtype import area_pb2, depth_pb2, line_pb2, mass_pb2, volume_pb2
from ses_client import client, event, pb_value
from ses_client.application import (
    ApplicationActivity,
    ApplicationBroadcast,
    ApplicationFertigation,
    ApplicationFoliar,
    ApplicationIncorporation,
    ApplicationInjection,
    ApplicationSpray,
    ApplicationSubsurface,
    ApplicationWithoutMethod,
)
from ses_client.input import (
    ApplicationInputOther,
    BasicFertiliser,
    EENFFertiliser,
    FertiliserAdditive,
    OrganicAmendment,
)
from ses_client.pb_value import kilograms_per_hectare

from tests import health, testdata


def test_application_activity_without_method():
    """Test ApplicationActivity base class - ie without a specified application method."""
    event_id = event.new_event_id()
    aa = (
        ApplicationActivity(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_UNSPECIFIED
    )
    assert aa.pb_event.revision == 0


def test_application_activity_broadcast():
    pb_application_input = (
        BasicFertiliser(input_name="calnit")
        .mass_rate(
            mass_pb2.MassAreaMeasure(
                mass=mass_pb2.MassMeasure(
                    value=190, unit=mass_pb2.MassUnit.MASS_UNIT_POUND
                ),
                area=area_pb2.AreaMeasure(
                    value=19, unit=area_pb2.AreaUnit.AREA_UNIT_ACRE
                ),
            )
        )
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_BROADCAST
    )
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert aa.pb_event.application_activity.inputs[0].basic_fertiliser.name == "calnit"
    assert aa.pb_event.application_activity.inputs[0].mass_rate.mass.value == 190
    assert aa.pb_event.application_activity.inputs[0].mass_rate.area.value == 19


def test_application_activity_fertigation():
    chook_poo = (
        BasicFertiliser(input_name="chook poo")
        .mass_rate(pb_value.kilograms_per_hectare(1500))
        .pb()
    )
    cow_poo = (
        BasicFertiliser(input_name="cow poo")
        .mass_rate(pb_value.kilograms_per_hectare(1200))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationFertigation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(chook_poo)
        .input(cow_poo)
        .water_depth_applied(pb_value.depth_millimetres(10))
        .soil_depth(pb_value.depth_centimetres(2))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_FERTIGATION
    )
    assert aa.pb_event.application_activity.inputs.__len__() == 2
    assert (
        aa.pb_event.application_activity.inputs[0].basic_fertiliser.name == "chook poo"
    )
    assert aa.pb_event.application_activity.inputs[0].mass_rate.mass.value == 1500
    assert aa.pb_event.application_activity.inputs[0].mass_rate.area.value == 1
    assert aa.pb_event.application_activity.inputs[1].basic_fertiliser.name == "cow poo"
    assert aa.pb_event.application_activity.inputs[1].mass_rate.mass.value == 1200
    assert aa.pb_event.application_activity.inputs[1].mass_rate.area.value == 1
    assert aa.pb_event.application_activity.fertigation.water_depth_total.value == 10.0
    assert (
        aa.pb_event.application_activity.fertigation.water_depth_total.unit
        == line_pb2.LineUnit.LINE_UNIT_MILLIMETRE
    )
    assert aa.pb_event.application_activity.fertigation.soil_depth.value == 2.0
    assert (
        aa.pb_event.application_activity.fertigation.soil_depth.unit
        == line_pb2.LineUnit.LINE_UNIT_CENTIMETRE
    )


def test_application_activity_fertigation_with_method():
    # drip fertigation
    event_id = event.new_event_id()
    drip_aa = (
        ApplicationFertigation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .fertigation_method(application_pb2.FERTIGATION_METHOD_DRIP)
        .water_volume_applied(pb_value.litres(5))
        .validate()
    )

    assert drip_aa.pb_event.id == event_id
    assert (
        drip_aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_FERTIGATION
    )
    assert (
        drip_aa.pb_event.application_activity.fertigation.method
        == application_pb2.FERTIGATION_METHOD_DRIP
    )

    # sprinkler fertigation
    event_id = event.new_event_id()
    sprinkler_aa = (
        ApplicationFertigation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .fertigation_method(application_pb2.FERTIGATION_METHOD_SPRINKLER)
        .water_volume_applied(pb_value.litres(5))
        .validate()
    )

    assert sprinkler_aa.pb_event.id == event_id
    assert (
        sprinkler_aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_FERTIGATION
    )
    assert (
        sprinkler_aa.pb_event.application_activity.fertigation.method
        == application_pb2.FERTIGATION_METHOD_SPRINKLER
    )

    # furrow fertigation
    event_id = event.new_event_id()
    furrow_aa = (
        ApplicationFertigation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .fertigation_method(application_pb2.FERTIGATION_METHOD_FURROW)
        .water_volume_applied(pb_value.litres(5))
        .validate()
    )

    assert furrow_aa.pb_event.id == event_id
    assert (
        furrow_aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_FERTIGATION
    )
    assert (
        furrow_aa.pb_event.application_activity.fertigation.method
        == application_pb2.FERTIGATION_METHOD_FURROW
    )


def test_application_activity_foliar():
    pb_application_input = (
        ApplicationInputOther(
            input_type=application_pb2.ApplicationInputOtherType.APPLICATION_INPUT_OTHER_TYPE_HERBICIDE,
            input_name="roundup",
        )
        .volume_rate(pb_value.litres_per_hectare(7.5))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationFoliar(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_FOLIAR
    )
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert aa.pb_event.application_activity.inputs[0].other_input.name == "roundup"
    assert (
        aa.pb_event.application_activity.inputs[0].other_input.type
        == application_pb2.ApplicationInputOtherType.APPLICATION_INPUT_OTHER_TYPE_HERBICIDE
    )
    assert aa.pb_event.application_activity.inputs[0].volume_rate.volume.value == 7.5
    assert (
        aa.pb_event.application_activity.inputs[0].volume_rate.volume.unit
        == volume_pb2.VolumeUnit.VOLUME_UNIT_LITRE
    )


def test_application_activity_incorporation():
    pb_application_input = (
        OrganicAmendment(
            input_id=application_input_id_pb2.OrganicAmendmentId.ORGANIC_AMENDMENT_ID_MANURE_HORSE_LIQUID,
        )
        .volume_rate(pb_value.gallons_per_acre(200))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationIncorporation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .soil_depth(pb_value.depth_inches(5))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_INCORPORATION
    )
    assert aa.pb_event.application_activity.incorporation.depth.value == 5.0
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert (
        aa.pb_event.application_activity.inputs[0].organic_amendment.name
        == "manure horse liquid"
    )
    assert aa.pb_event.application_activity.inputs[0].volume_rate.volume.value == 200
    assert (
        aa.pb_event.application_activity.inputs[0].volume_rate.volume.unit
        == volume_pb2.VolumeUnit.VOLUME_UNIT_US_GALLON
    )


def test_application_activity_injection():
    pb_application_input = (
        FertiliserAdditive(
            input_id=application_input_id_pb2.FertiliserAdditiveId.FERTILISER_ADDITIVE_ID_AGROTAIN,
        )
        .volume_rate(pb_value.litres_per_hectare(97.52))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationInjection(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .soil_depth(pb_value.depth_centimetres(15))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_INJECTION
    )
    assert aa.pb_event.application_activity.injection.depth.value == 15.0
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert (
        aa.pb_event.application_activity.inputs[0].fertiliser_additive.name
        == "agrotain"
    )
    assert aa.pb_event.application_activity.inputs[
        0
    ].volume_rate.volume.value == pytest.approx(97.52)
    assert (
        aa.pb_event.application_activity.inputs[0].volume_rate.volume.unit
        == volume_pb2.VolumeUnit.VOLUME_UNIT_LITRE
    )


def test_application_method_subsurface():
    pb_application_input = (
        BasicFertiliser(input_name="calcium ammonium nitrate")
        .mass_rate(kilograms_per_hectare(25))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationSubsurface(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .soil_depth(pb_value.depth_centimetres(15))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_SUBSURFACE
    )
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert (
        aa.pb_event.application_activity.inputs[0].basic_fertiliser.id
        == application_input_id_pb2.BASIC_FERTILISER_ID_CALCIUM_AMMONIUM_NITRATE
    )
    assert (
        aa.pb_event.application_activity.inputs[0].basic_fertiliser.name
        == "calcium ammonium nitrate"
    )
    assert aa.pb_event.application_activity.inputs[0].mass_rate.mass.value == 25
    assert (
        aa.pb_event.application_activity.inputs[0].mass_rate.mass.unit
        == mass_pb2.MassUnit.MASS_UNIT_KILOGRAM
    )
    assert aa.pb_event.application_activity.subsurface.depth.value == 15.0


def test_application_method_spray():
    pb_application_input = (
        EENFFertiliser(
            input_id=application_input_id_pb2.EENFFertiliserId.EENF_FERTILISER_ID_ENTEC_26,
            input_name="entec is something you probably cannot spray :)",
        )
        .volume_rate(pb_value.litres_per_hectare(7.5))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationSpray(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_SPRAY
    )
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert (
        aa.pb_event.application_activity.inputs[0].eenf_fertiliser.name == "entec 26"
    )  # name gets 'corrected'


def test_application_organic_with_nitrogen_rate():
    pb_application_input = (
        OrganicAmendment(
            input_id=application_input_id_pb2.OrganicAmendmentId.ORGANIC_AMENDMENT_ID_MANURE_PIG_LIQUID,
        )
        .nitrogen_mass_rate(pb_value.kilograms_per_hectare(150))
        .pb()
    )

    event_id = event.new_event_id()
    aa = (
        ApplicationIncorporation(event_id=event_id, user_id="user-123")
        .geom("{}")
        .start(datetime(2021, 1, 21, 0, 0))
        .input(pb_application_input)
        .soil_depth(pb_value.depth_inches(5))
        .validate()
    )

    assert aa.pb_event.id == event_id
    assert (
        aa.pb_event.application_activity.method
        == application_pb2.APPLICATION_METHOD_INCORPORATION
    )
    assert aa.pb_event.application_activity.incorporation.depth.value == 5.0
    assert aa.pb_event.application_activity.inputs.__len__() == 1
    assert (
        aa.pb_event.application_activity.inputs[0].organic_amendment.name
        == "manure pig liquid"
    )
    assert aa.pb_event.application_activity.inputs[0].mass_rate.mass.value == 150
    assert (
        aa.pb_event.application_activity.inputs[0].mass_rate.mass.unit
        == mass_pb2.MassUnit.MASS_UNIT_KILOGRAM
    )
    assert (
        aa.pb_event.application_activity.inputs[0].fertiliser_rate_type
        == application_pb2.FERTILISER_RATE_TYPE_NITROGEN_MASS
    )


@pytest.mark.integration
async def test_application_activity_no_method():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationWithoutMethod(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(datetime(2021, 1, 21, 0, 0))
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_UNSPECIFIED
    )
    # This is still a valid revision, even though the method is not specified
    assert se.revision == 1


@pytest.mark.integration
async def test_upsert_application_activity_broadcast():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_BROADCAST
    )


@pytest.mark.integration
async def test_upsert_application_activity_fertigation():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationFertigation(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            OrganicAmendment(input_name="slurry")
            .volume_rate(pb_value.litres_per_hectare(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_FERTIGATION
    )


@pytest.mark.integration
async def test_upsert_application_activity_fertigation_with_method():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()

    # Test with drip fertigation method
    event_id = event.new_event_id()
    aa = (
        ApplicationFertigation(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .fertigation_method(application_pb2.FERTIGATION_METHOD_DRIP)
        .water_depth_applied(pb_value.depth_millimetres(10))
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.kilograms_per_hectare(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_FERTIGATION
    )
    assert (
        se.application_activity.fertigation.method
        == application_pb2.FERTIGATION_METHOD_DRIP
    )


@pytest.mark.integration
async def test_upsert_application_activity_foliar():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationFoliar(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            ApplicationInputOther(
                input_type=application_pb2.APPLICATION_INPUT_OTHER_TYPE_HERBICIDE,
                input_name="roundup",
            )
            .volume_rate(pb_value.litres_per_hectare(15))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.application_activity.method == application_pb2.APPLICATION_METHOD_FOLIAR


@pytest.mark.integration
async def test_upsert_application_activity_incorporation():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationIncorporation(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .soil_depth(pb_value.depth_centimetres(10))
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method
        == application_pb2.APPLICATION_METHOD_INCORPORATION
    )


@pytest.mark.integration
async def test_upsert_application_activity_injection():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationInjection(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_INJECTION
    )


@pytest.mark.integration
async def test_upsert_application_subsurface():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationSubsurface(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_SUBSURFACE
    )


@pytest.mark.integration
async def test_upsert_application_activity_spray():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationActivity(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_UNSPECIFIED
    )


@pytest.mark.integration
async def test_upsert_application_activity_broadcast_without_units():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()

    # no mass unit
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(
                mass_pb2.MassAreaMeasure(
                    mass=mass_pb2.MassMeasure(value=150),
                    area=area_pb2.AreaMeasure(
                        value=1, unit=area_pb2.AreaUnit.AREA_UNIT_ACRE
                    ),
                )
            )
            .pb()
        )
        .validate()
    )
    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0

    # no area unit
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(
                mass_pb2.MassAreaMeasure(
                    mass=mass_pb2.MassMeasure(
                        value=150, unit=mass_pb2.MassUnit.MASS_UNIT_POUND
                    ),
                    area=area_pb2.AreaMeasure(value=1),
                )
            )
            .pb()
        )
        .validate()
    )
    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0

    # no mass or area unit
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(
                mass_pb2.MassAreaMeasure(
                    mass=mass_pb2.MassMeasure(value=150),
                    area=area_pb2.AreaMeasure(value=1),
                )
            )
            .pb()
        )
        .validate()
    )
    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_application_activity_fertigation_without_units():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()

    # no mass unit
    aa = (
        ApplicationFertigation(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .mass_rate(
                mass_pb2.MassAreaMeasure(
                    mass=mass_pb2.MassMeasure(value=150),
                    area=area_pb2.AreaMeasure(
                        value=1, unit=area_pb2.AreaUnit.AREA_UNIT_ACRE
                    ),
                )
            )
            .pb()
        )
        .water_depth_applied(
            depth_pb2.DepthMeasure(
                value=10,
                # unit=line_pb2.LineUnit.LINE_UNIT_MILLIMETRE, <-- NO UNIT
            )
        )
        .soil_depth(
            depth_pb2.DepthMeasure(
                value=2,
                # unit=line_pb2.LineUnit.LINE_UNIT_CENTIMETRE, <-- NO UNIT
            )
        )
        .validate()
    )
    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert se.revision == 0


@pytest.mark.integration
async def test_upsert_application_activity_broadcast_with_mass_product_rate_type():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .product_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_BROADCAST
    )
    assert (
        se.application_activity.inputs[0].fertiliser_rate_type
        == application_pb2.FERTILISER_RATE_TYPE_PRODUCT
    )


@pytest.mark.integration
async def test_upsert_application_activity_broadcast_with_volume_product_rate_type():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .product_rate(pb_value.litres_per_hectare(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_BROADCAST
    )
    assert (
        se.application_activity.inputs[0].fertiliser_rate_type
        == application_pb2.FERTILISER_RATE_TYPE_PRODUCT
    )


@pytest.mark.integration
async def test_upsert_application_activity_broadcast_with_nitrogen_mass_rate_type():
    await health.health_check(testdata.event_srvc_addr)
    event_client = client.Client(testdata.event_srvc_addr)
    field_geom = testdata.random_polygon()
    event_id = event.new_event_id()
    aa = (
        ApplicationBroadcast(event_id=event_id, user_id=testdata.TEST_USER_ID)
        .geom(field_geom)
        .start(testdata.random_datetime())
        .input(
            BasicFertiliser(input_name="npk")
            .nitrogen_mass_rate(pb_value.pounds_per_acre(150))
            .pb()
        )
        .validate()
    )

    _ = await event_client.upsert_event(aa)
    se = await event_client.fetch_event(event_id)
    assert se.id == event_id
    assert (
        se.application_activity.method == application_pb2.APPLICATION_METHOD_BROADCAST
    )
    assert (
        se.application_activity.inputs[0].fertiliser_rate_type
        == application_pb2.FERTILISER_RATE_TYPE_NITROGEN_MASS
    )
