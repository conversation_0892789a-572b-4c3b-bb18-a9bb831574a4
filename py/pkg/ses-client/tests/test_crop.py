import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from ses_client.crop import Crop


def test_crop_without_id_or_label():
    with pytest.raises(ValueError):
        Crop()


def test_crop_with_invalid_id():
    with pytest.raises(ValueError):
        Crop(crop_id=9999999)


def test_crop_with_id():
    c = Crop(crop_id=crop_id_enum_pb2.CropId.CROP_ID_RYE)
    assert (
        c.pb_crop.id == 78
    )  # regrow id for rye - http://defaults-service.int.prod.regrow.cloud/tables/crop_translation
    assert c.pb_crop.name == "Rye"


def test_crop_with_label():
    c = Crop(crop_label="anything")
    assert c.pb_crop.id == 0
    assert c.pb_crop.name == "anything"


def test_crop_with_purpose():
    c = Crop(crop_id=crop_id_enum_pb2.CropId.CROP_ID_RYE)
    c.purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COVER)
    assert len(c.pb_crop.purpose) == 1
    assert crop_pb2.CropPurpose.CROP_PURPOSE_COVER in c.pb_crop.purpose


def crop_with_multi_purpose():
    c = Crop(crop_id=crop_id_enum_pb2.CropId.CROP_ID_RYE)
    c.purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COVER)
    c.purpose(crop_pb2.CropPurpose.CROP_PURPOSE_FODDER)
    assert len(c.pb_crop.purpose) == 2
    assert crop_pb2.CropPurpose.CROP_PURPOSE_FODDER in c.pb_crop.purpose
    assert crop_pb2.CropPurpose.CROP_PURPOSE_COVER in c.pb_crop.purpose
