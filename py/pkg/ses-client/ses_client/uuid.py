"""
Deterministic UUID generation utilities.

This module provides functions to generate UUIDs in a deterministic way
based on input data, particularly from dictionary arguments.
"""

import json
import uuid
from typing import Any, Dict, Union


def create_deterministic_uuid(
    data: Union[Dict[str, Any], str], namespace: str = "default"
) -> str:
    """
    Create a deterministic UUID based on input data.

    This function generates a UUID that will always be the same for the same input data.
    It uses Python's built-in uuid.uuid5() which creates a UUID based on the SHA-1 hash
    of a namespace identifier and a name.

    Args:
        data: Dictionary or string to generate UUID from. If dict, it will be
              JSON-serialized with sorted keys for consistency.
        namespace: Optional namespace string to differentiate UUIDs generated
                  from the same data in different contexts.

    Returns:
        A deterministic UUID string in standard format (e.g., "550e8400-e29b-41d4-a716-************")

    Examples:
        >>> data = {"id": 123, "eventType": "tillageActivity", "...": "..."}
        >>> uuid1 = create_deterministic_uuid(data)
        >>> uuid2 = create_deterministic_uuid(data)
        >>> uuid1 == uuid2
        True

        >>> # Different namespace produces different UUID
        >>> uuid3 = create_deterministic_uuid(data, namespace="123456")
        >>> uuid1 != uuid3
        True
    """
    # Create a namespace UUID from the namespace string
    # We use uuid5 with the DNS namespace to create a deterministic namespace UUID
    namespace_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, namespace)

    # Convert data to a consistent string representation
    if isinstance(data, dict):
        # Sort keys to ensure consistent ordering
        name = json.dumps(data, sort_keys=True, separators=(",", ":"))
    else:
        name = str(data)

    # Generate UUID5 using the namespace UUID and the data string
    result_uuid = uuid.uuid5(namespace_uuid, name)

    return str(result_uuid)


def is_valid_uuid(uuid_string: str) -> bool:
    """
    Check if a string is a valid UUID format.

    Uses Python's built-in uuid module to validate the UUID format.
    Only accepts lowercase UUIDs with proper hyphen formatting.

    Args:
        uuid_string: String to validate as UUID.

    Returns:
        True if the string is a valid UUID format, False otherwise.

    Examples:
        >>> is_valid_uuid("550e8400-e29b-41d4-a716-************")
        True
        >>> is_valid_uuid("invalid-uuid")
        False
        >>> is_valid_uuid("550e8400e29b41d4a716************")  # No hyphens
        False
        >>> is_valid_uuid("550E8400-E29B-41D4-A716-************")  # Uppercase
        False
    """
    # First check if input is a string
    if not isinstance(uuid_string, str):
        return False

    try:
        # Try to create a UUID object from the string
        uuid_obj = uuid.UUID(uuid_string)
        # Check that the string representation matches the input exactly
        # This ensures proper formatting (with hyphens, lowercase, etc.)
        return str(uuid_obj) == uuid_string
    except (ValueError, TypeError):
        return False
