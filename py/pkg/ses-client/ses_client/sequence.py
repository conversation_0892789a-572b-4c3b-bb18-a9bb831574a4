from typing import List, Tuple

from regrow.ses.crop.v1 import crop_pb2
from regrow.ses.event.v1 import event_pb2

from ses_client import event
from ses_client.event import StructuredEvent, event_type, get_cropping_period_identifier


def sowing_or_planting_event_has_crop(sowing_planting_event: event_pb2.Event) -> bool:
    """Check if the sowing/planting event has a crop set"""
    return bool(sowing_planting_event.sowing_activity.crops) or bool(
        sowing_planting_event.planting_activity.crops
    )


def sowing_event_has_crop(sowing_event: event_pb2.Event) -> bool:
    """Check if the sowing event has a crop set"""
    if event_type(sowing_event) == StructuredEvent.TYPE_SOWING_ACTIVITY:
        return bool(sowing_event.sowing_activity.crops)
    return False


def planting_event_has_crop(planting_event: event_pb2.Event) -> bool:
    """Check if the planting event has a crop set"""
    if event_type(planting_event) == StructuredEvent.TYPE_PLANTING_ACTIVITY:
        return bool(planting_event.planting_activity.crops)
    return False


def harvest_event_has_crop(harvest_event: event_pb2.Event) -> bool:
    """Check if the harvest event has a crop set"""
    return bool(harvest_event.harvest_activity.crops)


def termination_event_has_crop(termination_event: event_pb2.Event) -> bool:
    """Check if the termination event has a crop set"""
    return bool(termination_event.termination_activity.crops)


def first_crop_id_from_event(ev: event_pb2.Event) -> int | None:
    """Return the crop id from the first crop in the event"""
    if sowing_event_has_crop(ev):
        return ev.sowing_activity.crops[0].crop.id
    elif planting_event_has_crop(ev):
        return ev.planting_activity.crops[0].crop.id
    elif harvest_event_has_crop(ev):
        return ev.harvest_activity.crops[0].crop.id
    elif termination_event_has_crop(ev):
        return ev.termination_activity.crops[0].crop.id
    return None


def first_crop_purpose_from_event(ev: event_pb2.Event) -> crop_pb2.CropPurpose | None:
    purposes: list[crop_pb2.CropPurpose] = []
    if sowing_event_has_crop(ev) and ev.sowing_activity.crops[0].crop.purpose:
        purposes = ev.sowing_activity.crops[0].crop.purpose  # type: ignore
    elif planting_event_has_crop(ev) and ev.planting_activity.crops[0].crop.purpose:
        purposes = ev.planting_activity.crops[0].crop.purpose  # type: ignore
    elif harvest_event_has_crop(ev) and ev.harvest_activity.crops[0].crop.purpose:
        purposes = ev.harvest_activity.crops[0].crop.purpose  # type: ignore
    elif (
        termination_event_has_crop(ev) and ev.termination_activity.crops[0].crop.purpose
    ):
        purposes = ev.termination_activity.crops[0].crop.purpose  # type: ignore

    return purposes[0] if purposes else None


def crop_match(ev1: event_pb2.Event, ev2: event_pb2.Event, strict=False) -> bool:
    """Returns True if ev1 and ev2 contain matching crops. By default will match when both events have no crop."""
    crop_id1 = first_crop_id_from_event(ev1)
    crop_id2 = first_crop_id_from_event(ev2)
    # exact match and not None
    if strict:
        return crop_id1 == crop_id2 and crop_id1 is not None
    # can match when both are None
    return crop_id1 == crop_id2


# John's modified version - treat None as a mismatch
# todo: Revisit this tighter restriction on Crop + None being a mismatch
# Note: modified test for this change: tests/test_search.py:485: test_cropping_sequences_for_fields_with_context()
def crop_mismatch(ev1: event_pb2.Event, ev2: event_pb2.Event) -> bool:
    """Returns True if both events have non-matching crop values."""
    crop1 = first_crop_id_from_event(ev1)
    crop2 = first_crop_id_from_event(ev2)
    if crop1 is None and crop2 is None:
        return False
    if crop1 is None or crop2 is None:
        return True
    return crop1 != crop2


# Original version - absence of a crop is not necessarily a mismatch due to partial events
# def crop_mismatch(ev1: event_pb2.Event, ev2: event_pb2.Event) -> bool:
#     """Returns True if both events have non-matching crop values other than None."""
#     crop1 = first_crop_id_from_event(ev1)
#     crop2 = first_crop_id_from_event(ev2)
#     if crop1 is None or crop2 is None:
#         return False
#     return crop1 != crop2


def purpose_mismatch(ev1: event_pb2.Event, ev2: event_pb2.Event) -> bool:
    purpose1 = first_crop_purpose_from_event(ev1)
    purpose2 = first_crop_purpose_from_event(ev2)
    if purpose1 is None and purpose2 is None:
        return False
    if purpose1 is None or purpose2 is None:
        return True
    return purpose1 != purpose2


def scan_for_matching_crop_period_identifiers(
    ev_list: List[event_pb2.Event], id_to_match: str
) -> list[event_pb2.Event]:
    return [ev for ev in ev_list if get_cropping_period_identifier(ev) == id_to_match]


def next_sowing_or_planting_event_and_index(
    ev_list: List[event_pb2.Event],
) -> Tuple[event_pb2.Event | None, int | None]:
    """Return the next sowing/planting event from the list and its index."""
    for i, ev in enumerate(ev_list):
        if (
            event_type(ev) == StructuredEvent.TYPE_SOWING_ACTIVITY
            or event_type(ev) == StructuredEvent.TYPE_PLANTING_ACTIVITY
        ):
            return ev, i
    return None, None


def next_harvest_event_and_index_match_crop(
    event_list: List[event_pb2.Event], sowing_planting_event: event_pb2.Event
) -> Tuple[event_pb2.Event | None, int | None]:
    """Return the next harvest event from the list and its index that matches the crop id."""
    for i, ev in enumerate(event_list):
        if event_type(ev) == StructuredEvent.TYPE_HARVEST_ACTIVITY:
            if first_crop_id_from_event(ev) == first_crop_id_from_event(
                sowing_planting_event
            ):
                return ev, i
    return None, None


def next_termination_event_and_index_match_crop(
    event_list: List[event_pb2.Event],
    match_crop_event: event_pb2.Event,
) -> Tuple[event_pb2.Event | None, int | None]:
    """Return the next termination event from the list and its index that has a crop that matches with the
    crop in match_crop_event."""
    for i, ev in enumerate(event_list):
        if event_type(ev) == StructuredEvent.TYPE_TERMINATION_ACTIVITY:
            if first_crop_id_from_event(ev) == first_crop_id_from_event(
                match_crop_event
            ):
                return ev, i
    return None, None


def next_harvest_event_and_index(
    ev_list: List[event_pb2.Event],
) -> Tuple[event_pb2.Event | None, int | None]:
    """Return the next harvest event from the list and its index."""
    for i, ev in enumerate(ev_list):
        if event_type(ev) == StructuredEvent.TYPE_HARVEST_ACTIVITY:
            return ev, i
    return None, None


def cropping_sequence(
    candidate_events: List[event_pb2.Event],
) -> List[
    Tuple[event_pb2.Event | None, event_pb2.Event | None, event_pb2.Event | None]
]:
    """
    Cropping sequence will return a list of tuples each containing a set of cropping-related events.

    The sequence of events in each tuple is: sowing/planting, harvest, termination.

    Obviously harvest must come after sowing/planting, however termination could be done prior to typical harvest time,
    at harvest time (because harvest terminates the crop), or after the expected harvest time (eg winterkill).

    In general terms if a crop is terminated for reasons or by means other than harvest then there should not be a
    harvest activity and the order would be: sowing/planting, termination.
    """

    sequences: List[
        Tuple[event_pb2.Event | None, event_pb2.Event | None, event_pb2.Event | None]
    ] = []
    seq_idx = 0

    candidate_events = event.sort_list_by_interval_start_time(candidate_events)
    new_tuple = True
    processed_events_ids = set()

    for i, e in enumerate(candidate_events):
        if e.id in processed_events_ids:
            # print(f"Already processed event {e.id}")
            continue

        # only start a new tuple if the event type is a sowing/planting, harvest or termination activity
        if new_tuple and event_type(e) in (
            StructuredEvent.TYPE_SOWING_ACTIVITY,
            StructuredEvent.TYPE_PLANTING_ACTIVITY,
            StructuredEvent.TYPE_HARVEST_ACTIVITY,
            StructuredEvent.TYPE_TERMINATION_ACTIVITY,
        ):
            sequences.append((None, None, None))

        cropping_period_id = get_cropping_period_identifier(e)
        if cropping_period_id:
            matched_sowplant = None
            matched_harvest = None
            matched_termination = None

            matching_events = scan_for_matching_crop_period_identifiers(
                candidate_events[i:], cropping_period_id
            )

            matched_boundary_event = False
            for matching_event in matching_events:
                processed_events_ids.add(matching_event.id)
                match_type = event_type(matching_event)
                if match_type in [
                    StructuredEvent.TYPE_PLANTING_ACTIVITY,
                    StructuredEvent.TYPE_SOWING_ACTIVITY,
                ]:
                    matched_sowplant = matching_event
                    matched_boundary_event = True
                elif match_type == StructuredEvent.TYPE_HARVEST_ACTIVITY:
                    matched_harvest = matching_event
                    matched_boundary_event = True
                elif match_type == StructuredEvent.TYPE_TERMINATION_ACTIVITY:
                    matched_termination = matching_event
                    matched_boundary_event = True

            if matched_boundary_event:
                sequences[seq_idx] = (
                    matched_sowplant,
                    matched_harvest,
                    matched_termination,
                )
                seq_idx += 1
                new_tuple = True
        elif (
            event_type(e) == StructuredEvent.TYPE_SOWING_ACTIVITY
            or event_type(e) == StructuredEvent.TYPE_PLANTING_ACTIVITY
        ):
            sequences[seq_idx] = (
                e,
                sequences[seq_idx][1],
                sequences[seq_idx][2],
            )  # tuples are immutable, Mike!
            processed_events_ids.add(e.id)
            # print(f"Current event id {e.id} - processed_events_ids: {processed_events_ids}")

            # we have a sowing/planting event in our tuple... let's see if we can find a suitable harvest event

            associated_termination = None
            matched_termination = best_termination_match_for_sowplant(
                e, candidate_events[i + 1 :], processed_events_ids
            )
            if matched_termination:
                sequences[seq_idx] = (e, sequences[seq_idx][1], matched_termination)
                processed_events_ids.add(matched_termination.id)
                associated_termination = matched_termination

            matched_harvest = best_harvest_match_for_sowplant(
                e,
                candidate_events[i + 1 :],
                processed_events_ids,
                associated_termination,
            )
            if matched_harvest:
                sequences[seq_idx] = (e, matched_harvest, sequences[seq_idx][2])
                processed_events_ids.add(matched_harvest.id)

            seq_idx += 1
            new_tuple = True

        elif event_type(e) == StructuredEvent.TYPE_HARVEST_ACTIVITY:
            sequences[seq_idx] = (sequences[seq_idx][0], e, sequences[seq_idx][2])
            processed_events_ids.add(e.id)

            matched_termination = best_termination_match_for_harvest(
                e, candidate_events[i + 1 :]
            )
            if (
                matched_termination
                and matched_termination.id not in processed_events_ids
            ):
                sequences[seq_idx] = (sequences[seq_idx][0], e, matched_termination)
                processed_events_ids.add(matched_termination.id)

            seq_idx += 1
            new_tuple = True

        elif event_type(e) == StructuredEvent.TYPE_TERMINATION_ACTIVITY:
            sequences[seq_idx] = (sequences[seq_idx][0], sequences[seq_idx][1], e)
            processed_events_ids.add(e.id)

            # termination might appear before harvest in the list so we want to check if there is well-matched
            # harvest event for this termination event.
            matched_harvest = best_harvest_match_for_termination(
                e, candidate_events[i + 1 :]
            )
            if matched_harvest and matched_harvest.id not in processed_events_ids:
                sequences[seq_idx] = (sequences[seq_idx][0], matched_harvest, e)
                processed_events_ids.add(matched_harvest.id)

            seq_idx += 1
            new_tuple = True

        # an event type we are not interested in
        else:
            processed_events_ids.add(e.id)
            continue

    return sequences


def best_harvest_match_for_sowplant(
    sowplant_event: event_pb2.Event,
    event_candidates: List[event_pb2.Event],
    processed_events_ids: set[str],
    associated_termination: event_pb2.Event | None,
) -> event_pb2.Event | None:
    """Return the next best harvest event from the list based on a points system."""

    finalists = []
    for i, ev in enumerate(event_candidates):
        if event_type(ev) != StructuredEvent.TYPE_HARVEST_ACTIVITY:
            continue

        # wants to be specifically grouped
        if get_cropping_period_identifier(ev):
            continue

        # a mismatched crop is a deal breaker
        if crop_mismatch(sowplant_event, ev):
            continue

        if purpose_mismatch(sowplant_event, ev):
            continue

        if ev.id in processed_events_ids:
            continue

        if associated_termination is not None and not event_at_or_before_second_event(
            ev, associated_termination
        ):
            continue

        points = 0
        if event_dates_in_sequence(sowplant_event, ev) and event_dates_within_days(
            sowplant_event, ev, 365
        ):
            points += 1
        if event_dates_differ_minimum_days(sowplant_event, ev, 30):
            points += 1
        if event_crops_match(sowplant_event, ev):
            points += 1

        if points > 0:
            finalists.append((i, points))

    if not finalists:
        return None

    best_idx = max(finalists, key=lambda x: x[1])[0]
    return event_candidates[best_idx]


def best_termination_match_for_sowplant(
    sowplant_event: event_pb2.Event,
    event_candidates: List[event_pb2.Event],
    processed_events: set[str],
) -> event_pb2.Event | None:
    """Find the best match termination activity for the given sowing / planting activity."""
    finalists = []
    for i, ev in enumerate(event_candidates):
        if event_type(ev) != StructuredEvent.TYPE_TERMINATION_ACTIVITY:
            continue

        # wants to be specifically grouped
        if get_cropping_period_identifier(ev):
            continue

        # a mismatched crop is a deal breaker
        if crop_mismatch(sowplant_event, ev):
            continue

        if purpose_mismatch(sowplant_event, ev):
            continue

        if ev.id in processed_events:
            continue

        points = 0
        if event_dates_in_sequence(sowplant_event, ev) and event_dates_within_days(
            sowplant_event, ev, 356
        ):
            points += 1
        if event_dates_differ_minimum_days(sowplant_event, ev, 30):
            points += 1
        if event_crops_match(sowplant_event, ev):
            points += 1

        if points > 0:
            finalists.append((i, points))

    if not finalists:
        return None

    best_idx = max(finalists, key=lambda x: x[1])[0]
    return event_candidates[best_idx]


def best_termination_match_for_harvest(
    harvest_event: event_pb2.Event, event_candidates: List[event_pb2.Event]
) -> event_pb2.Event | None:
    """Similar rules to next_best_harvest but for termination events. The difference here is that a termination event
    can have a date that is the same or even just before the harvest date. It's a little hacky but this situation can
    occur."""
    finalists = []
    for i, ev in enumerate(event_candidates):
        if event_type(ev) != StructuredEvent.TYPE_TERMINATION_ACTIVITY:
            continue

        # wants to be specifically grouped
        if get_cropping_period_identifier(ev):
            continue

        # a mismatched crop is a deal breaker
        if crop_mismatch(harvest_event, ev):
            continue

        # instant winner if dates match
        if event_date_match(harvest_event, ev):
            return ev

        points = 0
        if event_dates_within_days(harvest_event, ev, 10) and event_crops_match(
            harvest_event, ev
        ):
            points += 2
        if event_dates_within_days(harvest_event, ev, 10):
            points += 1

        if points > 0:
            finalists.append((i, points))

    if not finalists:
        return None

    best_idx = max(finalists, key=lambda x: x[1])[0]
    return event_candidates[best_idx]


def best_harvest_match_for_termination(
    termination_event: event_pb2.Event, event_candidates: List[event_pb2.Event]
) -> event_pb2.Event | None:
    """Find the best match harvest activity for the given termination activity."""

    finalists = []
    for i, ev in enumerate(event_candidates):
        if event_type(ev) != StructuredEvent.TYPE_HARVEST_ACTIVITY:
            continue

        # wants to be specifically grouped
        if get_cropping_period_identifier(ev):
            continue

        # a mismatched crop is a deal breaker
        if crop_mismatch(termination_event, ev):
            continue

        # instant winner if the dates match
        if event_date_match(termination_event, ev):
            return ev

        points = 0
        if event_dates_within_days(termination_event, ev, 10) and event_crops_match(
            termination_event, ev
        ):
            points += 2
        if event_dates_within_days(termination_event, ev, 10):
            points += 1

        if points > 0:
            finalists.append((i, points))

    if not finalists:
        return None

    best_idx = max(finalists, key=lambda x: x[1])[0]
    return event_candidates[best_idx]


def event_date_match(event1: event_pb2.Event, event2: event_pb2.Event) -> bool:
    """Check if the start dates of the two events match."""
    return (
        event1.interval.start_time.ToDatetime()
        == event2.interval.start_time.ToDatetime()
    )


def event_at_or_before_second_event(
    event1: event_pb2.Event, event2: event_pb2.Event
) -> bool:
    return (
        event1.interval.start_time.ToDatetime()
        <= event2.interval.start_time.ToDatetime()
    )


def event_dates_within_days(
    event1: event_pb2.Event, event2: event_pb2.Event, days: int
) -> bool:
    """Check if the start dates of the two events are within the given number of days."""
    return (
        abs(
            event1.interval.start_time.ToDatetime()
            - event2.interval.start_time.ToDatetime()
        ).days
        <= days
    )


def event_dates_differ_minimum_days(
    event1: event_pb2.Event, event2: event_pb2.Event, days: int
) -> bool:
    """Check if the start dates of the two events differ by at least the given number of days."""
    return (
        abs(
            event1.interval.start_time.ToDatetime()
            - event2.interval.start_time.ToDatetime()
        ).days
        >= days
    )


def event_dates_in_sequence(event1: event_pb2.Event, event2: event_pb2.Event) -> bool:
    """Check if the two events are in chronological order."""
    return (
        event1.interval.end_time.ToDatetime() <= event2.interval.start_time.ToDatetime()
    )


def event_crops_match(event1: event_pb2.Event, event2: event_pb2.Event) -> bool:
    """Check if the crops in the two events match."""
    return crop_match(event1, event2)
