"""
input
=======================

This module provides functions related to agricultural inputs such as fertiliser products, organic amendments,
herbicides and so on. These inputs are used in the SES model to represent the application of these products to a
an area and will appear in ApplicationActivity events.

There are two distinct cases that are relevant to associating an input with an ApplicationActivity:
1. The input type/product is known and has an associated id
2. The input type/product is not known and will not have an id

Both cases are supported by SES and "unknown" input names are very common when data is imported from third-party
systems (eg FMS) that allow free text inputs.

"""

from regrow.ses.application.v1 import application_input_id_pb2, application_pb2
from regrow.ses.pbtype import mass_pb2, volume_pb2


class ApplicationInput:
    def __init__(self, input_id: int | None = None, input_name: str | None = None):
        if not input_id and not input_name:
            raise ValueError("At least one of input_id or input_name must be set.")
        self.pb_input: application_pb2.ApplicationInput | None = None

    def pb(self):
        """Return the protobuf object."""
        return self.pb_input

    def mass_rate(self, mass_area_measure: mass_pb2.MassAreaMeasure):
        if self.pb_input:
            self.pb_input.mass_rate.CopyFrom(mass_area_measure)
        return self

    def volume_rate(self, volume_area_measure: volume_pb2.VolumeAreaMeasure):
        if self.pb_input:
            self.pb_input.volume_rate.CopyFrom(volume_area_measure)
        return self

    def product_rate(
        self, rate_measure: mass_pb2.MassAreaMeasure | volume_pb2.VolumeAreaMeasure
    ):
        """This sets the qualifier for the applied product rate. Needs to work for both mass and volume measures."""
        if self.pb_input:
            if isinstance(rate_measure, mass_pb2.MassAreaMeasure):
                self.mass_rate(rate_measure)
                self.pb_input.fertiliser_rate_type = (
                    application_pb2.FERTILISER_RATE_TYPE_PRODUCT
                )
            elif isinstance(rate_measure, volume_pb2.VolumeAreaMeasure):
                self.volume_rate(rate_measure)
                self.pb_input.fertiliser_rate_type = (
                    application_pb2.FERTILISER_RATE_TYPE_PRODUCT
                )
        return self

    def nitrogen_mass_rate(self, mass_area_measure: mass_pb2.MassAreaMeasure):
        """This sets the qualifier for the applied product rate to nitrogen mass."""
        if self.pb_input:
            self.mass_rate(mass_area_measure)
            self.pb_input.fertiliser_rate_type = (
                application_pb2.FERTILISER_RATE_TYPE_NITROGEN_MASS
            )
        return self


class BasicFertiliser(ApplicationInput):
    enum_prefix = "BASIC_FERTILISER_ID_"

    def __init__(self, input_id: int | None = None, input_name: str | None = None):
        super().__init__(input_id, input_name)

        # If we get an id we need to check that the id is kosher for this input type, and we'll derive a label
        # from the corresponding enum key for that particular id. For example if we get an id of 1 that corresponds to
        # BASIC_FERTILISER_ID_AMMONIUM_BICARBONATE enum we will derive a label: "Ammonium Bicarbonate".
        if input_id:
            if not basic_fertiliser_id_exists(input_id):
                raise ValueError(f"Invalid basic fertiliser id: {input_id}")
            self.pb_input = application_pb2.ApplicationInput(
                basic_fertiliser=application_pb2.ApplicationInputBasicFertiliser(
                    id=basic_fertiliser_enum_for_id(input_id),
                    name=basic_fertiliser_name_from_id(input_id),
                )
            )
        elif input_name:
            self.pb_input = application_pb2.ApplicationInput(
                basic_fertiliser=application_pb2.ApplicationInputBasicFertiliser(
                    id=basic_fertiliser_enum_from_name(input_name),
                    name=input_name,
                )
            )


class EENFFertiliser(ApplicationInput):
    enum_prefix = "EENF_FERTILISER_ID_"

    def __init__(self, input_id: int | None = None, input_name: str | None = None):
        super().__init__(input_id, input_name)

        if input_id:
            if not eenf_fertiliser_id_exists(input_id):
                raise ValueError(f"Invalid eenf fertiliser id: {input_id}")
            self.pb_input = application_pb2.ApplicationInput(
                eenf_fertiliser=application_pb2.ApplicationInputEENFFertiliser(
                    id=eenf_fertiliser_enum_for_id(input_id),
                    name=eenf_fertiliser_name_from_id(input_id),
                )
            )
        elif input_name:
            self.pb_input = application_pb2.ApplicationInput(
                eenf_fertiliser=application_pb2.ApplicationInputEENFFertiliser(
                    id=eenf_fertiliser_enum_from_name(input_name),
                    name=input_name,
                )
            )


class FertiliserAdditive(ApplicationInput):
    enum_prefix = "FERTILISER_ADDITIVE_ID_"

    def __init__(self, input_id: int | None = None, input_name: str | None = None):
        super().__init__(input_id, input_name)

        if input_id:
            if not fertiliser_additive_id_exists(input_id):
                raise ValueError(f"Invalid fertiliser additive id: {input_id}")
            self.pb_input = application_pb2.ApplicationInput(
                fertiliser_additive=application_pb2.ApplicationInputFertiliserAdditive(
                    id=fertiliser_additive_enum_for_id(input_id),
                    name=fertiliser_additive_name_from_id(input_id),
                )
            )
        elif input_name:
            self.pb_input = application_pb2.ApplicationInput(
                fertiliser_additive=application_pb2.ApplicationInputFertiliserAdditive(
                    id=fertiliser_additive_enum_from_name(input_name),
                    name=input_name,
                )
            )


class OrganicAmendment(ApplicationInput):
    enum_prefix = "ORGANIC_AMENDMENT_ID_"

    def __init__(self, input_id: int | None = None, input_name: str | None = None):
        super().__init__(input_id, input_name)

        if input_id:
            if not organic_amendment_id_exists(input_id):
                raise ValueError(f"Invalid organic amendment id: {input_id}")
            self.pb_input = application_pb2.ApplicationInput(
                organic_amendment=application_pb2.ApplicationInputOrganicAmendment(
                    id=organic_amendment_enum_for_id(input_id),
                    name=organic_amendment_name_from_id(input_id),
                )
            )
        elif input_name:
            self.pb_input = application_pb2.ApplicationInput(
                organic_amendment=application_pb2.ApplicationInputOrganicAmendment(
                    id=organic_amendment_enum_from_name(input_name),
                    name=input_name,
                )
            )


class ApplicationInputOther(ApplicationInput):
    """This one works a little differently because we don't have an id for 'other' products."""

    def __init__(
        self,
        input_type: application_pb2.ApplicationInputOtherType | None = None,
        input_name: str | None = None,
    ):
        if not input_type:
            input_type = application_pb2.ApplicationInputOtherType.APPLICATION_INPUT_OTHER_TYPE_UNSPECIFIED

        # there are no ids for 'other' products so we can only set a name and a category
        super().__init__(input_id=None, input_name=input_name)
        self.pb_input = application_pb2.ApplicationInput(
            other_input=application_pb2.ApplicationInputOther(
                type=input_type,
                name=input_name,
            )
        )


def enum_key_from_name(enum_prefix: str, label: str) -> str:
    """Create an enum 'ley' from the prefix and arbitrary label string.
    For example 'CROP_ID_' and 'Rye' -> 'CROP_ID_RYE'."""
    if not label:
        return ""

    s = label.replace(" ", "_").upper()
    if not s.startswith(enum_prefix):
        s = f"{enum_prefix}{s}"

    return s


def basic_fertiliser_id_exists(input_id: int) -> bool:
    """Returns True if the input id exists in the list of basic fertiliser ids."""
    return input_id in application_input_id_pb2.BasicFertiliserId.values()


def basic_fertiliser_enum_exists(enum_key: str) -> bool:
    """Returns True if the input enum exists in the list of basic fertiliser enums."""
    return enum_key in application_input_id_pb2.BasicFertiliserId.keys()


def basic_fertiliser_enum_for_id(input_id: int) -> str:
    """Returns the enum key for the given basic fertiliser id."""
    return application_input_id_pb2.BasicFertiliserId.Name(input_id)


def basic_fertiliser_enum_from_name(input_name: str) -> str | None:
    """Search for a basic fertiliser enum that matches an arbitrary label string."""
    if not input_name:
        return ""

    search_key = enum_key_from_name(BasicFertiliser.enum_prefix, input_name)
    if basic_fertiliser_enum_exists(search_key):
        return search_key

    return None


def basic_fertiliser_name_from_id(input_id: int) -> str:
    """Returns a friendly 'label' for a basic fertiliser id."""
    enum_key = basic_fertiliser_enum_for_id(input_id)
    return enum_key[len(BasicFertiliser.enum_prefix) :].replace("_", " ").lower()


def eenf_fertiliser_id_exists(input_id: int) -> bool:
    """Returns True if the input id exists in the list of eenf fertiliser ids."""
    return input_id in application_input_id_pb2.EENFFertiliserId.values()


def eenf_fertiliser_enum_exists(enum_key: str) -> bool:
    """Returns True if the input enum exists in the list of eenf fertiliser enums."""
    return enum_key in application_input_id_pb2.EENFFertiliserId.keys()


def eenf_fertiliser_enum_for_id(input_id: int) -> str:
    """Returns the enum key for the given eenf fertiliser id."""
    return application_input_id_pb2.EENFFertiliserId.Name(input_id)


def eenf_fertiliser_enum_from_name(input_name: str) -> str | None:
    """Search for an eenf fertiliser enum that matches an arbitrary label string."""
    if not input_name:
        return ""

    search_key = enum_key_from_name(EENFFertiliser.enum_prefix, input_name)
    if eenf_fertiliser_enum_exists(search_key):
        return search_key

    return None


def eenf_fertiliser_name_from_id(input_id: int) -> str:
    """Returns a friendly 'label' for an eenf fertiliser id."""
    enum_key = eenf_fertiliser_enum_for_id(input_id)
    return enum_key[len(EENFFertiliser.enum_prefix) :].replace("_", " ").lower()


def fertiliser_additive_id_exists(input_id: int) -> bool:
    """Returns True if the input id exists in the list of fertiliser additive ids."""
    return input_id in application_input_id_pb2.FertiliserAdditiveId.values()


def fertiliser_additive_enum_exists(enum_key: str) -> bool:
    """Returns True if the input enum exists in the list of fertiliser additive enums."""
    return enum_key in application_input_id_pb2.FertiliserAdditiveId.keys()


def fertiliser_additive_enum_for_id(input_id: int) -> str:
    """Returns the enum key for the given fertiliser additive id."""
    return application_input_id_pb2.FertiliserAdditiveId.Name(input_id)


def fertiliser_additive_enum_from_name(input_name: str) -> str | None:
    """Search for a fertiliser additive enum that matches an arbitrary label string."""
    if not input_name:
        return ""

    search_key = enum_key_from_name(FertiliserAdditive.enum_prefix, input_name)
    if fertiliser_additive_enum_exists(search_key):
        return search_key

    return None


def fertiliser_additive_name_from_id(input_id: int) -> str:
    """Returns a friendly 'label' for a fertiliser additive id."""
    enum_key = fertiliser_additive_enum_for_id(input_id)
    return enum_key[len(FertiliserAdditive.enum_prefix) :].replace("_", " ").lower()


def organic_amendment_id_exists(input_id: int) -> bool:
    """Returns True if the input id exists in the list of organic amendment ids."""
    return input_id in application_input_id_pb2.OrganicAmendmentId.values()


def organic_amendment_enum_exists(enum_key: str) -> bool:
    """Returns True if the input enum exists in the list of organic amendment enums."""
    return enum_key in application_input_id_pb2.OrganicAmendmentId.keys()


def organic_amendment_enum_for_id(input_id: int) -> str:
    """Returns the enum key for the given organic amendment id."""
    return application_input_id_pb2.OrganicAmendmentId.Name(input_id)


def organic_amendment_enum_from_name(input_name: str) -> str | None:
    """Search for an organic amendment enum that matches an arbitrary label string."""
    if not input_name:
        return ""

    search_key = enum_key_from_name(OrganicAmendment.enum_prefix, input_name)
    if organic_amendment_enum_exists(search_key):
        return search_key

    return None


def organic_amendment_name_from_id(input_id: int) -> str:
    """Returns a friendly 'label' for an organic amendment id."""
    enum_key = organic_amendment_enum_for_id(input_id)
    return enum_key[len(OrganicAmendment.enum_prefix) :].replace("_", " ").lower()
