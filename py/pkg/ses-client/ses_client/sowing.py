from regrow.ses.sowing.v1 import sowing_pb2

from ses_client.crop import SownCrop
from ses_client.event import StructuredEvent


class SowingActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.sowing_activity.CopyFrom(sowing_pb2.SowingActivity())

    def pb(self):
        """Return the protobuf object."""
        return self.pb_event

    def crop(self, sown_crop: SownCrop | None = None):
        """Add a sown crop to the activity."""
        if sown_crop is not None:
            self.pb_event.sowing_activity.crops.extend([sown_crop.pb()])
        return self

    def method(self, sowing_method: sowing_pb2.SowingMethod):
        """Set the sowing method"""
        self.pb_event.sowing_activity.method = sowing_method
        return self


def method_broadcast():
    """Return broadcast sowing method"""
    return sowing_pb2.SowingMethod.SOWING_METHOD_BROADCAST


def method_direct_dry():
    """Return direct dry sowing method"""
    return sowing_pb2.SowingMethod.SOWING_METHOD_DIRECT_DRY


def method_direct_wet():
    """Return direct wet sowing method"""
    return sowing_pb2.SowingMethod.SOWING_METHOD_DIRECT_WET
