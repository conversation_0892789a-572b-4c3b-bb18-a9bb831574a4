import json

import httpx


class BoundariesClient:
    def __init__(self, boundaries_service_url: str):
        self.url = boundaries_service_url

    async def fetch_geometry_str(self, md5: str):
        """Fetch geometry from boundaries service"""
        url = f"{self.url}/search?ids={md5}"
        async with httpx.AsyncClient() as httpx_client:
            res = await httpx_client.get(url)
        if res.status_code != 200:
            raise ValueError(f"Failed to fetch geometry with md5 {md5}: {res.text}")

        # Get features[0].geometry, if exists
        features = res.json().get("features", [])
        if not features:
            raise ValueError(f"No features found for md5 {md5}")

        return json.dumps(features[0].get("geometry"))
