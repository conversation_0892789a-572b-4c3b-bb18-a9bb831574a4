from regrow.ses.application.v1 import application_pb2
from regrow.ses.pbtype import depth_pb2, volume_pb2

from ses_client.event import StructuredEvent


class ApplicationActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.CopyFrom(
            application_pb2.ApplicationActivity()
        )

    def input(self, application_input: application_pb2.ApplicationInput):
        """Adds the input to the application activity"""
        self.pb_event.application_activity.inputs.extend([application_input])
        return self


class ApplicationWithoutMethod(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_UNSPECIFIED
        )


class ApplicationBroadcast(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_BROADCAST
        )


class ApplicationFertigation(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_FERTIGATION
        )

    def fertigation_method(self, method: application_pb2.FertigationMethod):
        self.pb_event.application_activity.fertigation.method = method
        return self

    def water_volume_applied(self, volume_measure: volume_pb2.VolumeMeasure):
        self.pb_event.application_activity.fertigation.water_volume_total.CopyFrom(
            volume_measure
        )
        return self

    def water_depth_applied(self, depth_measure: depth_pb2.DepthMeasure):
        self.pb_event.application_activity.fertigation.water_depth_total.CopyFrom(
            depth_measure
        )
        return self

    def soil_depth(self, depth_measure: depth_pb2.DepthMeasure):
        self.pb_event.application_activity.fertigation.soil_depth.CopyFrom(
            depth_measure
        )
        return self


class ApplicationFoliar(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_FOLIAR
        )


class ApplicationIncorporation(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_INCORPORATION
        )

    def soil_depth(self, depth_measure: depth_pb2.DepthMeasure):
        self.pb_event.application_activity.incorporation.depth.CopyFrom(depth_measure)
        return self


class ApplicationInjection(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_INJECTION
        )

    def soil_depth(self, depth_measure: depth_pb2.DepthMeasure):
        self.pb_event.application_activity.injection.depth.CopyFrom(depth_measure)
        return self


class ApplicationSubsurface(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_SUBSURFACE
        )

    def soil_depth(self, depth_measure: depth_pb2.DepthMeasure):
        self.pb_event.application_activity.subsurface.depth.CopyFrom(depth_measure)
        return self


class ApplicationSpray(ApplicationActivity):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.application_activity.method = (
            application_pb2.APPLICATION_METHOD_SPRAY
        )
