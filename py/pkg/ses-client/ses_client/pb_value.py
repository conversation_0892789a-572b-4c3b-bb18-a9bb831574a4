"""
pbtype
=======================

This module provides utility functions for creating values based on protobuf definitions, for various measurements.
It is a convenience module aimed at reducing code clutter in the various places these types are referenced.

Example:
    from pbtype import depth_centimetres, gallons_per_acre

    depth = depth_centimetres(30.5)
    volume_area = gallons_per_acre(15.0, 2.5)

Functions:
    - acres(value: float) -> area_pb2.AreaMeasure:- acres(value: float) -> area_pb2.AreaMeasure:
        Returns an AreaMeasure in acres.
    - bushels(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in bushels.
    - bushels_per_acre(bushels: float, acres: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in bushels and area in acres.
    - count(value: int) -> count_pb2.CountMeasure:
        Returns a CountMeasure with value as a non-negative integer.
    - count_per_acre(count: int, acres: float) -> count_pb2.CountAreaMeasure:
        Returns a CountAreaMeasure with count in count and area in acres.
    - count_per_hectare(count: int, hectares: float) -> count_pb2.CountAreaMeasure:
        Returns a CountAreaMeasure with count in count and area in hectares.
    - count_per_square_feet(count: int, square_feet: float) -> count_pb2.CountAreaMeasure:
        Returns a CountAreaMeasure with count in count and area in square feet.
    - count_per_square_metre(count: int, square_metres: float) -> count_pb2.CountAreaMeasure:
        Returns a CountAreaMeasure with count in count and area in square metres.
    - count_per_square_yard(count: int, square_yards: float) -> count_pb2.CountAreaMeasure:
        Returns a CountAreaMeasure with count in count and area in square yards.
    - depth_centimetres(value: float) -> depth_pb2.DepthMeasure:
        Returns a DepthMeasure in centimetres.
    - depth_inches(value: float) -> depth_pb2.DepthMeasure:
        Returns a DepthMeasure in inches.
    - depth_foot(value: float) -> depth_pb2.DepthMeasure:
        Returns a DepthMeasure in foot.
    - depth_millimetres(value: float) -> depth_pb2.DepthMeasure:
        Returns a DepthMeasure in millimetres.
    - fluid_ounces(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in fluid ounces (Standard US).
    - fluid_ounces_per_acre(fluid_ounces_value: float, acres_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in fluid ounces and area in acres.
    - fraction(value: float) -> fraction_pb2.Fraction:
        Alias for fraction_of_one.
    - fraction_of_one(value: float) -> fraction_pb2.Fraction:
        Returns a Fraction with value as a fraction of one.
    - fraction_percent(value: float) -> fraction_pb2.Fraction:
        Returns a Fraction with value as a percentage.
    - gallons(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in gallons.
    - gallons_per_acre(gallons_value: float, acres_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in gallons and area in acres.
    - hectares(value: float) -> area_pb2.AreaMeasure:
        Returns an AreaMeasure in hectares.
    - kilograms(value: float) -> mass_pb2.MassMeasure:
        Returns a MassMeasure with mass in kilograms.
    - kilograms_per_hectare(value: float, hectares_value: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in kilograms and area in hectares.
    - litres(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in litres.
    - litres_per_hectare(litres_value: float, hectares_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in litres and area in hectares.
    - litres_per_square_metre(litres_value: float, square_metres_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in litres and area in square metres.
    - long_tons(value: float) -> mass_pb2.MassMeasure:
        Returns a MassMeasure with mass in long tons.
    - long_tons_per_acre(tons: float, acres: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in long tons and area in acres.
    - megalitres(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in mega litres.
    - megalitres_per_hectare(megalitres_value: float, hectares_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in mega litres and area in hectares.
    - percentage(value: float) -> fraction_pb2.Fraction:
        Returns a Fraction with value as a percentage (alias for fraction_percent).
    - pints(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in pints (Standard US).
    - pints_per_acre(pints_value: float, acres_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in pints and area in acres.
    - pounds(value: float) -> mass_pb2.MassMeasure:
        Returns a MassMeasure with mass in pounds.
    - pounds_per_acre(value: float, acres: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in pounds and area in acres.
    - quarts(value: float) -> volume_pb2.VolumeMeasure:
        Returns a VolumeMeasure in quarts (Standard US).
    - quarts_per_acre(quarts_value: float, acres_value: float) -> volume_pb2.VolumeAreaMeasure:
        Returns a VolumeAreaMeasure with volume in quarts and area in acres.
    - short_tons(value: float) -> mass_pb2.MassMeasure:
        Returns a MassMeasure with mass in short tons.
    - short_tons_per_acre(tons: float, acres: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in short tons and area in acres.
    - square_feet(value: float) -> area_pb2.AreaMeasure:
        Returns an AreaMeasure in square feet.
    - square_metres(value: float) -> area_pb2.AreaMeasure:
        Returns an AreaMeasure in square metres.
    - square_yards(value: float) -> area_pb2.AreaMeasure:
        Returns an AreaMeasure in square yards.
    - tonnes(value: float) -> mass_pb2.MassMeasure:
        Returns a MassMeasure with mass in tonnes.
    - tonnes_per_hectare(tonnes: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in tonnes and area in hectares.
    - tonnes_per_square_metre(tonnes: float, square_metres: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in tonnes and area in square metres.
    - tons_per_acre(tons: float) -> mass_pb2.MassAreaMeasure:
        Returns a MassAreaMeasure with mass in short tons and area in acres.
"""

from regrow.ses.event.v1 import event_pb2
from regrow.ses.pbtype import (
    area_pb2,
    count_pb2,
    depth_pb2,
    fraction_pb2,
    line_pb2,
    mass_pb2,
    volume_pb2,
)

from ses_client.event import StructuredEvent


def acres(value: float) -> area_pb2.AreaMeasure:
    """Create an AreaMeasure with value in acres"""
    return area_pb2.AreaMeasure(value=value, unit=area_pb2.AreaUnit.AREA_UNIT_ACRE)


def bushels(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in bushels"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_BUSHEL
    )


def bushels_per_acre(
    bushels_value: float, acres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in bushels and area in acres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=bushels(bushels_value),
        area=acres(acres_value),
    )


def count(value: int) -> count_pb2.CountMeasure:
    """Create a CountMeasure with value as a non-negative integer"""
    if value < 0:
        raise ValueError("Count value should be a non-negative integer")
    return count_pb2.CountMeasure(value=value)


def count_per_acre(
    count_value: int, acres_value: float = 1.0
) -> count_pb2.CountAreaMeasure:
    """Create a CountAreaMeasure with count in count and area in acres"""
    return count_pb2.CountAreaMeasure(
        count=count(count_value),
        area=acres(acres_value),
    )


def count_per_hectare(
    count_value: int, hectares_value: float = 1.0
) -> count_pb2.CountAreaMeasure:
    """Create a CountAreaMeasure with count in count and area in hectares"""
    return count_pb2.CountAreaMeasure(
        count=count(count_value),
        area=hectares(hectares_value),
    )


def count_per_square_feet(
    count_value: int, square_feet_value: float = 1.0
) -> count_pb2.CountAreaMeasure:
    """Create a CountAreaMeasure with count in count and area in square feet"""
    return count_pb2.CountAreaMeasure(
        count=count(count_value),
        area=square_feet(square_feet_value),
    )


def count_per_square_metre(
    count_value: int, square_metres_value: float = 1.0
) -> count_pb2.CountAreaMeasure:
    """Create a CountAreaMeasure with count in count and area in square metres"""
    return count_pb2.CountAreaMeasure(
        count=count(count_value),
        area=square_metres(square_metres_value),
    )


def count_per_square_yard(
    count_value: int, square_yards_value: float = 1.0
) -> count_pb2.CountAreaMeasure:
    """Create a CountAreaMeasure with count in count and area in square yards"""
    return count_pb2.CountAreaMeasure(
        count=count(count_value),
        area=square_yards(square_yards_value),
    )


def depth_centimetres(value: float) -> depth_pb2.DepthMeasure:
    """Create a DepthMeasure with value in centimetres"""
    return depth_pb2.DepthMeasure(
        value=value, unit=line_pb2.LineUnit.LINE_UNIT_CENTIMETRE
    )


def depth_inches(value: float) -> depth_pb2.DepthMeasure:
    """Create a DepthMeasure with value in inches"""
    return depth_pb2.DepthMeasure(value=value, unit=line_pb2.LineUnit.LINE_UNIT_INCH)


def depth_foot(value: float) -> depth_pb2.DepthMeasure:
    """Create a DepthMeasure with value in foot"""
    return depth_pb2.DepthMeasure(value=value, unit=line_pb2.LineUnit.LINE_UNIT_FOOT)


def depth_millimetres(value: float) -> depth_pb2.DepthMeasure:
    """Create a DepthMeasure with value in millimetres"""
    return depth_pb2.DepthMeasure(
        value=value, unit=line_pb2.LineUnit.LINE_UNIT_MILLIMETRE
    )


def event_category(event_type: str) -> event_pb2.EventCategory:
    """Create an EventCategory with the given category"""
    if event_type == StructuredEvent.TYPE_TILLAGE_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_TILLAGE
    elif event_type == StructuredEvent.TYPE_SOWING_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_SOWING
    elif event_type == StructuredEvent.TYPE_PLANTING_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_PLANTING
    elif event_type == StructuredEvent.TYPE_HARVEST_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_HARVEST
    # does not exist in protodef, yet
    # elif event_type == StructuredEvent.TYPE_TERMINATION_ACTIVITY:
    #     return event_pb2.EventCategory.EVENT_CATEGORY_TERMINATION
    # does not exist in protodef, yet
    # elif event_type == StructuredEvent.TYPE_FALLOW_PERIOD:
    #     return event_pb2.EventCategory.EVENT_CATEGORY_FALLOW_PERIOD
    elif event_type == StructuredEvent.TYPE_APPLICATION_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_APPLICATION
    elif event_type == StructuredEvent.TYPE_IRRIGATION_ACTIVITY:
        return event_pb2.EventCategory.EVENT_CATEGORY_IRRIGATION
    else:
        return event_pb2.EventCategory.EVENT_CATEGORY_UNSPECIFIED


def fluid_ounces(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in fluid ounces (Standard US)"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_US_FLUID_OUNCE
    )


def fluid_ounces_per_acre(
    fluid_ounces_value: float, acres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in fluid ounces and area in acres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=fluid_ounces(fluid_ounces_value),
        area=acres(acres_value),
    )


def fraction(value: float) -> fraction_pb2.Fraction:
    """Alias for fraction_of_one."""
    return fraction_of_one(value)


def fraction_of_one(value: float) -> fraction_pb2.Fraction:
    """Create a Fraction with value as a fraction of one"""
    if value < 0 or value > 1:
        raise ValueError("Fraction value should be a number between 0 and 1")
    return fraction_pb2.Fraction(fraction=value)


def fraction_percent(value: float) -> fraction_pb2.Fraction:
    """Create a Fraction with value as a percentage"""
    if value < 0 or value > 100:
        raise ValueError("Fraction value should be a number between 0 and 100")
    return fraction_pb2.Fraction(percent=value)


def gallons(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in gallons"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_US_GALLON
    )


def gallons_per_acre(
    gallons_value: float, acres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in gallons and area in acres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=gallons(gallons_value),
        area=acres(acres_value),
    )


def hectares(value: float) -> area_pb2.AreaMeasure:
    """Create an AreaMeasure with value in hectares"""
    return area_pb2.AreaMeasure(value=value, unit=area_pb2.AreaUnit.AREA_UNIT_HECTARE)


def kilograms(value: float) -> mass_pb2.MassMeasure:
    """Create a MassMeasure with value in kilograms"""
    return mass_pb2.MassMeasure(value=value, unit=mass_pb2.MassUnit.MASS_UNIT_KILOGRAM)


def kilograms_per_hectare(
    kilograms_value: float, hectares_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in kilograms and area in hectares"""
    return mass_pb2.MassAreaMeasure(
        mass=kilograms(kilograms_value),
        area=hectares(hectares_value),
    )


def kilograms_per_square_metre(
    kilograms_value: float, square_metres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in kilograms and area in square metres"""
    return mass_pb2.MassAreaMeasure(
        mass=kilograms(kilograms_value),
        area=square_metres(square_metres_value),
    )


def litres(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in litres"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_LITRE
    )


def litres_per_hectare(
    litres_value: float, hectares_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in litres and area in hectares"""
    return volume_pb2.VolumeAreaMeasure(
        volume=litres(litres_value),
        area=hectares(hectares_value),
    )


def litres_per_square_metre(
    litres_value: float, square_metres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in litres and area in square metres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=litres(litres_value),
        area=square_metres(square_metres_value),
    )


def long_tons(value: float) -> mass_pb2.MassMeasure:
    """Create a MassMeasure with value in long tons"""
    return mass_pb2.MassMeasure(value=value, unit=mass_pb2.MassUnit.MASS_UNIT_LONG_TON)


def long_tons_per_acre(
    tons_value: float, acres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in long tons and area in acres"""
    return mass_pb2.MassAreaMeasure(
        mass=long_tons(tons_value),
        area=acres(acres_value),
    )


def megalitres(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in mega litres"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_MEGALITRE
    )


def megalitres_per_hectare(
    megalitres_value: float, hectares_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in mega litres and area in hectares"""
    return volume_pb2.VolumeAreaMeasure(
        volume=megalitres(megalitres_value),
        area=hectares(hectares_value),
    )


def percentage(value: float) -> fraction_pb2.Fraction:
    """Create a Fraction with value as a percentage"""
    return fraction_percent(value)


def pints(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in pints (Standard US)"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_US_PINT
    )


def pints_per_acre(
    pints_value: float, acres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in pints and area in acres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=pints(pints_value),
        area=acres(acres_value),
    )


def pounds(value: float) -> mass_pb2.MassMeasure:
    """Create a MassMeasure with value in pounds"""
    return mass_pb2.MassMeasure(value=value, unit=mass_pb2.MassUnit.MASS_UNIT_POUND)


def pounds_per_acre(
    pounds_value: float, acres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in pounds and area in acres"""
    return mass_pb2.MassAreaMeasure(
        mass=pounds(pounds_value),
        area=acres(acres_value),
    )


def pounds_per_square_feet(
    pounds_value: float, square_feet_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in pounds and area in square feet"""
    return mass_pb2.MassAreaMeasure(
        mass=pounds(pounds_value),
        area=square_feet(square_feet_value),
    )


def pounds_per_square_yard(
    pounds_value: float, square_yards_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in pounds and area in square yards"""
    return mass_pb2.MassAreaMeasure(
        mass=pounds(pounds_value),
        area=square_yards(square_yards_value),
    )


def quarts(value: float) -> volume_pb2.VolumeMeasure:
    """Create a VolumeMeasure with value in quarts (Standard US)"""
    return volume_pb2.VolumeMeasure(
        value=value, unit=volume_pb2.VolumeUnit.VOLUME_UNIT_US_QUART
    )


def quarts_per_acre(
    quarts_value: float, acres_value: float = 1.0
) -> volume_pb2.VolumeAreaMeasure:
    """Create a VolumeAreaMeasure with volume in quarts and area in acres"""
    return volume_pb2.VolumeAreaMeasure(
        volume=quarts(quarts_value),
        area=acres(acres_value),
    )


def short_tons(value: float) -> mass_pb2.MassMeasure:
    """Create a MassMeasure with value in short tons"""
    return mass_pb2.MassMeasure(value=value, unit=mass_pb2.MassUnit.MASS_UNIT_SHORT_TON)


def short_tons_per_acre(
    tons_value: float, acres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in short tons and area in acres"""
    return mass_pb2.MassAreaMeasure(
        mass=short_tons(tons_value),
        area=acres(acres_value),
    )


def square_feet(value: float) -> area_pb2.AreaMeasure:
    """Create an AreaMeasure with value in square feet"""
    return area_pb2.AreaMeasure(
        value=value, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_FOOT
    )


def square_metres(value: float) -> area_pb2.AreaMeasure:
    """Create an AreaMeasure with value in square metres"""
    return area_pb2.AreaMeasure(
        value=value, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_METRE
    )


def square_yards(value: float) -> area_pb2.AreaMeasure:
    """Create an AreaMeasure with value in square yards"""
    return area_pb2.AreaMeasure(
        value=value, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_YARD
    )


def tonnes(value: float) -> mass_pb2.MassMeasure:
    """Create a MassMeasure with value in tonnes"""
    return mass_pb2.MassMeasure(
        value=value,
        unit=mass_pb2.MassUnit.MASS_UNIT_TONNE,
    )


def tonnes_per_hectare(
    tonnes_value: float, hectares_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in tonnes and area in hectares"""
    return mass_pb2.MassAreaMeasure(
        mass=tonnes(tonnes_value),
        area=hectares(hectares_value),
    )


def tonnes_per_square_metre(
    tonnes_value: float, square_metres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in tonnes and area in square metres"""
    return mass_pb2.MassAreaMeasure(
        mass=tonnes(tonnes_value),
        area=square_metres(square_metres_value),
    )


def tons_per_acre(
    tons_value: float, acres_value: float = 1.0
) -> mass_pb2.MassAreaMeasure:
    """Create a MassAreaMeasure with mass in short tons and area in acres - this is a duplicate of short_tons_per_acre"""
    return short_tons_per_acre(tons_value, acres_value)
