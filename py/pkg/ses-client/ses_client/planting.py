from regrow.ses.planting.v1 import planting_pb2

from ses_client.crop import PlantedCrop
from ses_client.event import StructuredEvent


class PlantingActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.planting_activity.CopyFrom(planting_pb2.PlantingActivity())

    def pb(self):
        """Return the protobuf object."""
        return self.pb_event

    def crop(self, planted_crop: PlantedCrop | None = None):
        """Add a planted crop to the activity."""
        if planted_crop is not None:
            self.pb_event.planting_activity.crops.extend([planted_crop.pb()])
        return self

    def method(self, planting_method: planting_pb2.PlantingMethod):
        """Set the planting method"""
        self.pb_event.planting_activity.method = planting_method
        return self


def method_dry():
    """Return dry planting method"""
    return planting_pb2.PlantingMethod.PLANTING_METHOD_DRY


def method_wet():
    """Return wet planting method"""
    return planting_pb2.PlantingMethod.PLANTING_METHOD_WET
