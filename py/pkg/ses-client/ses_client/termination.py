from regrow.ses.termination.v1 import termination_pb2

from ses_client.crop import TerminatedCrop
from ses_client.event import StructuredEvent


class TerminationActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.termination_activity.CopyFrom(
            termination_pb2.TerminationActivity()
        )

    def pb(self):
        """Return the protobuf object."""
        return self.pb_event

    def crop(self, terminated_crop: TerminatedCrop | None = None):
        """Add a terminated crop to the activity."""
        if terminated_crop is not None:
            self.pb_event.termination_activity.crops.extend([terminated_crop.pb()])
        return self

    def reason(self, reason: termination_pb2.TerminationReason):
        """Set the termination reason"""
        if reason is not None:
            self.pb_event.termination_activity.reason = reason
        return self

    def method(self, method: termination_pb2.TerminationMethod):
        """Set the termination method"""
        if method is not None:
            self.pb_event.termination_activity.method = method
        return self
