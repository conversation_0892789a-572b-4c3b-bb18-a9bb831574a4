from regrow.ses.irrigation.v1 import irrigation_pb2
from regrow.ses.pbtype import depth_pb2, fraction_pb2, volume_pb2

from ses_client.event import StructuredEvent


class IrrigationActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.irrigation_activity.CopyFrom(irrigation_pb2.IrrigationActivity())

    def method_drip(self):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_DRIP
        )
        return self

    def method_flood(self):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_FLOOD
        )
        return self

    def method_furrow(
        self,
        *,
        furrow_area_fraction: fraction_pb2.Fraction | None = None,
        furrow_depth: depth_pb2.DepthMeasure | None = None,
    ):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_FURROW
        )
        if furrow_area_fraction:
            self.pb_event.irrigation_activity.furrow.furrow_area_fraction.CopyFrom(
                furrow_area_fraction
            )
        if furrow_depth:
            self.pb_event.irrigation_activity.furrow.furrow_depth.CopyFrom(furrow_depth)
        return self

    def method_mist(self):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_MIST
        )
        return self

    def method_sprinkler(self):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_SPRINKLER
        )
        return self

    def method_subsurface(self, *, soil_depth: depth_pb2.DepthMeasure | None = None):
        self.pb_event.irrigation_activity.method = (
            irrigation_pb2.IrrigationMethod.IRRIGATION_METHOD_SUBSURFACE
        )
        if soil_depth:
            self.pb_event.irrigation_activity.subsurface.soil_depth.CopyFrom(soil_depth)
        return self

    def water_applied(
        self,
        *,
        total_depth: depth_pb2.DepthMeasure | None = None,
        total_volume: volume_pb2.VolumeMeasure | None = None,
    ):
        if bool(total_depth) + bool(total_volume) != 1:
            raise ValueError(
                "Exactly one of total depth or total volume must be provided"
            )

        if total_depth:
            self.pb_event.irrigation_activity.water_depth_total.CopyFrom(total_depth)
        elif total_volume:
            self.pb_event.irrigation_activity.water_volume_total.CopyFrom(total_volume)

        return self
