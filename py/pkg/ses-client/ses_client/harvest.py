from regrow.ses.harvest.v1 import harvest_pb2

from ses_client.crop import HarvestedCrop
from ses_client.event import StructuredEvent


class HarvestActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.harvest_activity.CopyFrom(harvest_pb2.HarvestActivity())

    def pb(self):
        """Return the protobuf object."""
        return self.pb_event

    def crop(self, harvested_crop: HarvestedCrop | None = None):
        """Add a harvested crop to the activity."""
        if harvested_crop is not None:
            self.pb_event.harvest_activity.crops.extend([harvested_crop.pb()])
        return self
