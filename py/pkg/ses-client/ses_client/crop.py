from typing import cast

from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.harvest.v1 import harvest_pb2
from regrow.ses.pbtype import area_pb2, count_pb2, mass_pb2, volume_pb2
from regrow.ses.planting.v1 import planting_pb2
from regrow.ses.sowing.v1 import sowing_pb2
from regrow.ses.termination.v1 import termination_pb2


class Crop:
    enum_prefix = "CROP_ID_"

    def __init__(self, crop_id: int | None = None, crop_label: str | None = None):
        if not crop_id and not crop_label:
            raise ValueError("At least one of crop_id or crop_label must be set.")

        # Crop id is best and label is not necessary if we have the id - make sure id is valid
        if crop_id:
            if not crop_id_exists(crop_id):
                raise ValueError(f"Invalid crop id: {crop_id}")
            # Overwrite the label string with the sanitised enum name
            name = crop_label_from_id(self.enum_prefix, crop_id)
            self.pb_crop = crop_pb2.Crop(id=crop_enum_for_id(crop_id), name=name)
        elif crop_label:
            self.pb_crop = crop_pb2.Crop(name=crop_label)

    def pb(self):
        """Return the protobuf object."""
        return self.pb_crop

    def purpose(self, crop_purpose: crop_pb2.CropPurpose):
        """Add the crop purpose to the list."""
        self.pb_crop.purpose.extend([crop_purpose])
        return self


class SownCrop(Crop):
    def __init__(self, crop_id: int | None = None, crop_label: str | None = None):
        super().__init__(crop_id, crop_label)
        self.pb_sown_crop = sowing_pb2.SownCrop(crop=self.pb_crop)

    def pb(self):
        """Return the protobuf object."""
        return self.pb_sown_crop

    def purpose(self, crop_purpose: crop_pb2.CropPurpose):
        """Add a purpose value to the sown crop's list of purposes."""
        self.pb_sown_crop.crop.purpose.extend([crop_purpose])
        return self

    def sowing_rate_kg_per_ha(self, kg_per_ha: float):
        """Set the mass rate in kg/ha."""
        self.pb_sown_crop.seed_mass_rate.CopyFrom(
            mass_pb2.MassAreaMeasure(
                mass=mass_pb2.MassMeasure(
                    value=kg_per_ha, unit=mass_pb2.MassUnit.MASS_UNIT_KILOGRAM
                ),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_HECTARE
                ),
            )
        )
        return self

    def sowing_rate_lb_per_ac(self, lb_per_ac: float):
        """Set the mass rate in lb/ac."""
        self.pb_sown_crop.seed_mass_rate.CopyFrom(
            mass_pb2.MassAreaMeasure(
                mass=mass_pb2.MassMeasure(
                    value=lb_per_ac, unit=mass_pb2.MassUnit.MASS_UNIT_POUND
                ),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_ACRE
                ),
            )
        )
        return self

    def sowing_rate_seeds_per_sqm(self, seeds_per_sqm: int):
        """Set the count rate in seeds/sqm."""
        self.pb_sown_crop.seed_count_rate.CopyFrom(
            count_pb2.CountAreaMeasure(
                count=count_pb2.CountMeasure(value=seeds_per_sqm, unit_label="seeds"),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_METRE
                ),
            )
        )
        return self

    def sowing_rate_seeds_per_sqft(self, seeds_per_sqft: int):
        """Set the count rate in seeds/sqft."""
        self.pb_sown_crop.seed_count_rate.CopyFrom(
            count_pb2.CountAreaMeasure(
                count=count_pb2.CountMeasure(value=seeds_per_sqft, unit_label="seeds"),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_FOOT
                ),
            )
        )
        return self


class PlantedCrop(Crop):
    def __init__(self, crop_id: int | None = None, crop_label: str | None = None):
        super().__init__(crop_id, crop_label)
        self.pb_planted_crop = planting_pb2.PlantedCrop(crop=self.pb_crop)

    def pb(self):
        """Return the protobuf object."""
        return self.pb_planted_crop

    def purpose(self, crop_purpose: crop_pb2.CropPurpose):
        """Add a purpose value to the planted crop's list of purposes."""
        self.pb_planted_crop.crop.purpose.extend([crop_purpose])
        return self

    def planting_rate_plants_per_sqm(self, count: int):
        """Set the planting rate to n plants per square metre."""
        self.pb_planted_crop.planting_rate.CopyFrom(
            count_pb2.CountAreaMeasure(
                count=count_pb2.CountMeasure(value=count, unit_label="plants"),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_METRE
                ),
            )
        )
        return self

    def planting_rate_plants_per_sqft(self, count: int):
        """Set the planting rate to n plants per square foot."""
        self.pb_planted_crop.planting_rate.CopyFrom(
            count_pb2.CountAreaMeasure(
                count=count_pb2.CountMeasure(value=count, unit_label="plants"),
                area=area_pb2.AreaMeasure(
                    value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_SQUARE_FOOT
                ),
            )
        )
        return self


class HarvestedCrop(Crop):
    def __init__(self, crop_id: int | None = None, crop_label: str | None = None):
        super().__init__(crop_id, crop_label)
        self.pb_harvested_crop = harvest_pb2.HarvestedCrop(crop=self.pb_crop)

    def pb(self):
        """Return the protobuf object."""
        return self.pb_harvested_crop

    def purpose(self, crop_purpose: crop_pb2.CropPurpose):
        """Add a purpose value to the harvested crop's list of purposes."""
        self.pb_harvested_crop.crop.purpose.extend([crop_purpose])
        return self

    def volume_yield(self, volume_yield: volume_pb2.VolumeAreaMeasure):
        """Set the volume yield."""
        self.pb_harvested_crop.volume_yield.CopyFrom(volume_yield)
        return self

    def mass_yield(self, mass_yield: mass_pb2.MassAreaMeasure):
        """Set the mass yield."""
        self.pb_harvested_crop.mass_yield.CopyFrom(mass_yield)
        return self


class TerminatedCrop(Crop):
    def __init__(self, crop_id: int | None = None, crop_label: str | None = None):
        super().__init__(crop_id, crop_label)
        self.pb_terminated_crop = termination_pb2.TerminatedCrop(crop=self.pb_crop)

    def pb(self):
        """Return the protobuf object."""
        return self.pb_terminated_crop

    def purpose(self, crop_purpose: crop_pb2.CropPurpose):
        """Add a purpose value to the terminated crop's list of purposes."""
        self.pb_terminated_crop.crop.purpose.extend([crop_purpose])
        return self


def crop_id_exists(crop_id: int) -> bool:
    """Check if a crop id exists."""
    return crop_id in crop_id_enum_pb2.CropId.values()


def crop_enum_for_id(crop_id: int) -> str:
    """Return the crop enum key for the crop id."""
    return crop_id_enum_pb2.CropId.Name(crop_id)


def crop_enum_from_label(crop_label: str) -> str:
    """Search for a crop enum by label. The label can be a short crop label like "rye" or a string version of the enum
    like "CROP_ID_RYE"."""

    if not crop_label:
        return ""

    # replace spaces with underscores
    crop_label = crop_label.replace(" ", "_")

    # convert label to uppercase, and prepend with CROP_ID_ if it is not already
    crop_enum = crop_label.upper()
    if not crop_label.startswith("CROP_ID_"):
        crop_enum = f"CROP_ID_{crop_enum}"

    return crop_enum


def crop_id_from_label(crop_label: str) -> crop_id_enum_pb2.CropId:
    """Search for a crop id (protobuf enum) by label. The label can be a short crop label like "rye" or a string
    version of the enum like "CROP_ID_RYE"."""

    if not crop_label:
        return crop_id_enum_pb2.CropId.CROP_ID_UNSPECIFIED

    crop_enum = crop_enum_from_label(crop_label)

    # check if the enum exists
    for key in crop_id_enum_pb2.CropId.keys():
        if key == crop_enum:
            return cast(crop_id_enum_pb2.CropId, crop_id_enum_pb2.CropId.Value(key))

    return crop_id_enum_pb2.CropId.CROP_ID_UNSPECIFIED


def crop_label_from_id(enum_prefix: str, crop_id: int):
    """Returns a friendly 'label' from an enum value by stripping away the enum prefix and adding spaces.
    For example 'CROP_ID_RYE' -> 'Rye'."""
    crop_enum = crop_enum_for_id(crop_id)
    return crop_enum[len(enum_prefix) :].replace("_", " ").title()


def crop_purpose_commodity_harvest() -> crop_pb2.CropPurpose:
    """Return a commodity harvest crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST


def crop_purpose_pasture() -> crop_pb2.CropPurpose:
    """Return a pasture crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_PASTURE


def crop_purpose_fodder() -> crop_pb2.CropPurpose:
    """Return a fodder crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_FODDER


def crop_purpose_cover() -> crop_pb2.CropPurpose:
    """Return a cover crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_COVER


def crop_purpose_green_manure() -> crop_pb2.CropPurpose:
    """Return a green manure crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_GREEN_MANURE


def crop_purpose_biofumigation() -> crop_pb2.CropPurpose:
    """Return a biofumigation crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_BIOFUMIGATION


def crop_purpose_biodiversity() -> crop_pb2.CropPurpose:
    """Return a biodiversity crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_BIODIVERSITY


def crop_purpose_terminated_mulch() -> crop_pb2.CropPurpose:
    """Return a terminated mulch crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_TERMINATED_MULCH


def crop_purpose_weed_suppression() -> crop_pb2.CropPurpose:
    """Return a weed suppression crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_WEED_SUPPRESSION


def crop_purpose_bioremediation() -> crop_pb2.CropPurpose:
    """Return a bioremediation crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_BIOREMEDIATION


def crop_purpose_erosion_control() -> crop_pb2.CropPurpose:
    """Return an erosion control crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_EROSION_CONTROL


def crop_purpose_beneficials_habitat() -> crop_pb2.CropPurpose:
    """Return a beneficials habitat crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_BENEFICIALS_HABITAT


def crop_purpose_catch_crop() -> crop_pb2.CropPurpose:
    """Return a catch crop crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_CATCH_CROP


def crop_purpose_companion_crop() -> crop_pb2.CropPurpose:
    """Return a companion crop crop purpose."""
    return crop_pb2.CropPurpose.CROP_PURPOSE_COMPANION_CROP
