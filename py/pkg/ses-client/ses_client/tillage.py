from regrow.ses.pbtype import depth_pb2, fraction_pb2
from regrow.ses.tillage.v1 import tillage_pb2

from ses_client.event import StructuredEvent


class TillageActivity(StructuredEvent):
    def __init__(self, event_id: str, user_id: str | None = None):
        super().__init__(event_id, user_id)
        self.pb_event.tillage_activity.CopyFrom(tillage_pb2.TillageActivity())

    def pb(self):
        """Return the protobuf object."""
        return self.pb_event

    def depth(self, depth_measure: depth_pb2.DepthMeasure | None = None):
        """Set the depth of the tillage activity"""
        if depth_measure is not None:
            self.pb_event.tillage_activity.depth.CopyFrom(depth_measure)
        return self

    def implement(self, implement: tillage_pb2.TillageImplement):
        """Set the tillage implement"""
        self.pb_event.tillage_activity.implement = implement
        return self

    def strip_tillage_percent(self, strip_percentage: float):
        """Set the strip fraction of the tillage activity as a percentage"""
        if strip_percentage < 1 or strip_percentage > 99:
            raise ValueError("Strip percentage should be a number between 1 and 99")
        self.pb_event.tillage_activity.strip_tillage_fraction.CopyFrom(
            fraction_pb2.Fraction(percent=strip_percentage)
        )
        return self

    def soil_inversion(self, soil_inverted: bool):
        """Set the tillage activity soil inversion bool"""
        self.pb_event.tillage_activity.soil_inversion = soil_inverted
        return self

    def tillage_practice(self, practice: tillage_pb2.TillagePractice):
        """Set the tillage practice"""
        self.pb_event.tillage_activity.tillage_practice = practice
        return self
