# SES Client v1.2.6.dev1

The `ses-client` provides a friendly Python API for interacting with SES events—simplifying the process of creating, modifying, archiving, and querying events.

> **Note**: You can still use the original `ses` gRPC methods directly if you prefer.

---

## Quickstart

### Run SES Locally

Start both `event-srvc` (gRPC) and `event-search` (HTTP) locally with Docker:

```shell
docker compose up
```

### Run Tests

**Unit tests**:

```shell
make unit-test
```

**Integration tests** (includes unit tests):

```shell
make test
```

---

## Connecting to the dev SES Service

If you're deploying on a Kubernetes cluster that lacks an ingress for `event-srvc`, port-forward the service:

```shell
kubectl port-forward svc/grpc -n event-srvc 50052:50051
```

---

## Installation

To use the `ses-client` in your project, add the following to your `pyproject.toml`:

```toml
[tool.poetry]
name = "my-ses-client-project"
version = "0.1.0"

[[tool.poetry.source]]
name = "regrow-python"
url = "https://us-west1-python.pkg.dev/flurosat-154904/regrow-python/simple"
priority = "supplemental"

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
grpcio = "^1.60.1"
protobuf = "^5.28.2"
ses-client = { version = "^1.2.6.dev1", source = "regrow-python" }

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
```

---

## Usage Overview

Below is an overview of available client methods.

### `_field_` Methods (Optimized for `event-search`)

Use these methods if you're also interacting with `event-search`—for example, managing events, cropping sequences, and requiring faster synchronization between stored data and the *eventually-consistent* `event-search` cache.

- Enforce **keyword-only** parameters (`*,`).
- Require a `field_id` that corresponds to the `event-search` cache key.
- Block the response until the cache updates.

| Method                                                                   | Description                                                        |
| ------------------------------------------------------------------------ | ------------------------------------------------------------------ |
| `add_field_event_context(*, field_id, event_id, context)`               | Add context to an event and wait for cache update.                 |
| `upsert_field_event(*, field_id, structured_event)`                      | Insert or update an event and wait for cache update.               |
| `archive_field_event(*, field_id, event_id, context)`                    | Archive event revisions by setting the archive date to the current time.       |
| `archive_field_event_for_user(*, field_id, event_id, user_id)`           | Convenience method for archiving with user context.                |
| `restore_field_event(*, field_id, event_id, context)`                    | Restore event revisions by clearing the archive date.              |
| `restore_field_event_for_user(*, field_id, event_id, user_id)`           | Convenience method for restoring with user context.                |
| `delete_field_event(*, field_id, event_id)`                              | Permanently delete an event and update the cache promptly.         |
| `delete_or_archive_field_event_for_user(*, field_id, event_id, user_id)` | Attempt deletion; fallback to archive if deletion fails.           |

---

### Methods for Managing Events

These methods interact with `event-srvc` directly, which in turn triggers cache updates in `event-search`. This process can take slightly longer than the `_field_` methods above.

| Method                                                   | Description                                                                |
| -------------------------------------------------------- | -------------------------------------------------------------------------- |
| `upsert_event(structured_event)`                         | Create or update an event in `event-srvc`.                                 |
| `add_event_context(event_id, context)`                   | Add context to an event in `event-srvc`.                                   |
| `archive_event(event_id, context)`                       | Archive all revisions of an event in `event-srvc`.                         |
| `archive_event_for_user(*, event_id, user_id)`           | Convenience method for archiving with user context.                        |
| `restore_event(event_id, context)`                       | Restore an event’s archived revisions.                                     |
| `restore_event_for_user(*, event_id, user_id)`           | Convenience method for restoring with user context.                        |
| `delete_event(event_id)`                                 | Permanently delete an event; raises an error if it cannot be deleted.      |
| `delete_or_archive_event_for_user(*, event_id, user_id)` | Attempt to delete an event; archive it if deletion is not permitted.       |

---

### Methods for Retrieving and Searching Events

| Method                                              | Description                                                       |
| --------------------------------------------------- | ----------------------------------------------------------------- |
| `fetch_event(event_id)`                             | Retrieve a single event by ID.                                    |
| `fetch_events(event_ids)`                           | Retrieve multiple events by their IDs.                            |
| `fetch_event_with_context(event_id)`                | Retrieve an event along with its latest context.                  |
| `fetch_events_for_fields(field_ids, search_filter)` | Search and retrieve events for specific fields, based on filters. |

---

### Cropping Sequence Methods

| Method                                                  | Description                                                                          |
| ------------------------------------------------------- | ------------------------------------------------------------------------------------ |
| `fetch_cropping_sequences_for_fields(...)`              | Retrieve cropping (sowing/planting-harvest) event sequences for specified fields.    |
| `fetch_cropping_sequences_for_fields_with_context(...)` | Retrieve cropping sequences for fields, including any contextual data for each event.|

---

## Additional Information

- Check the `./tests/` directory for detailed examples and usage.

---
