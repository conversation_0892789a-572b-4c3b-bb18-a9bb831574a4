from __future__ import annotations

import asyncio
import os
from logging.config import fileConfig

from sqlalchemy import engine_from_config, Enum, pool
from sqlalchemy.ext.asyncio import AsyncEngine

from alembic import context
from db.mysql import Base
from helper.autoimport import autoimport_models
from helper.context import ctx

autoimport_models()
config = context.config
fileConfig(config.config_file_name)
target_metadata = Base.metadata


def include_object(object_, name, type_, reflected, compare_to) -> bool:
    return object_.info.get("skip_autogenerate") is not True


def compare_type(context, inspected_column, metadata_column, inspected_type, metadata_type) -> bool | None:
    """Current Alembic version detects changes when enum values are the same, but
    doesn't detect when the values number is less or more.

    Return False if the metadata_type is the same as the inspected_type or None to
    allow the default implementation to compare these types. A return value of True
    means the two types do not match and should result in a type change operation.
    """
    if isinstance(inspected_type, Enum) and isinstance(metadata_type, Enum):
        return set(inspected_type.enums) != set(metadata_type.enums)

    return None


def run_migrations_offline():
    ctx.no_audit_logs = True
    override_db_url()
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
        compare_type=compare_type,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        include_object=include_object,
        compare_type=compare_type,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def override_db_url():
    """
    If environment variable `db_url` is defined, overwrite config `sqlalchemy.url` with value of `db_url`
    """
    if db_url := os.environ.get("db_url"):
        config.set_main_option("sqlalchemy.url", db_url)


async def run_migrations_online():
    ctx.no_audit_logs = True
    override_db_url()
    connectable = AsyncEngine(
        engine_from_config(
            config.get_section(config.config_ini_section),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
            future=True,
        )
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)


if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
