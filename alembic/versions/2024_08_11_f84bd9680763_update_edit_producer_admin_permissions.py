"""update_edit_producer_admin_permissions

Revision ID: f84bd9680763
Revises: d0f6538b6b6f
Create Date: 2024-08-11 13:03:41.440776

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles

# revision identifiers, used by Alembic.
revision = "f84bd9680763"
down_revision = "d0f6538b6b6f"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}
#
roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {},
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
