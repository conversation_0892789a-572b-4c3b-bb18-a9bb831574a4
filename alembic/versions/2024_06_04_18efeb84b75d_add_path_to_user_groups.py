"""add-path-to-user-groups

Revision ID: 18efeb84b75d
Revises: 08e94cc51ab5
Create Date: 2024-06-04 17:45:48.709245

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "18efeb84b75d"
down_revision = "08e94cc51ab5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_user_groups", sa.Column("path", sa.String(length=255), nullable=False))
    op.create_index(op.f("ix_mrv_user_groups_path"), "mrv_user_groups", ["path"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_mrv_user_groups_path"), table_name="mrv_user_groups")
    op.drop_column("mrv_user_groups", "path")
