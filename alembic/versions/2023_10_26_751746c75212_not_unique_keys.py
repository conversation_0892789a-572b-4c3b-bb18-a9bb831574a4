"""Section, row and chart keys are not unique

Revision ID: 751746c75212
Revises: 5db1eed4d89a
Create Date: 2023-10-26 22:19:58.177296

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "751746c75212"
down_revision = "5db1eed4d89a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("key", table_name="mrv_reporting_chart_presets")
    op.drop_index("key", table_name="mrv_reporting_dashboard_rows")
    op.drop_index("key", table_name="mrv_reporting_dashboard_sections")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("key", "mrv_reporting_dashboard_sections", ["key"], unique=False)
    op.create_index("key", "mrv_reporting_dashboard_rows", ["key"], unique=False)
    op.create_index("key", "mrv_reporting_chart_presets", ["key"], unique=False)
    # ### end Alembic commands ###
