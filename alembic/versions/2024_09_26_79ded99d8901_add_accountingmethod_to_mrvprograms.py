"""Add accountingmethod to mrvprograms

Revision ID: 79ded99d8901
Revises: 94d4e111f93c
Create Date: 2024-09-26 15:00:51.441599

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "79ded99d8901"
down_revision = "94d4e111f93c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    op.add_column(
        "mrv_programs",
        sa.Column(
            "accounting_method",
            sa.Enum("intervention", "inventory", "biofuels", name="accountingmethod"),
            nullable=True,
        ),
    )
    op.execute(
        """
        UPDATE mrv_programs
        SET accounting_method = CASE
            WHEN id IN (1118, 1147) THEN 'biofuels'
            WHEN id IN (1123, 1140, 1141, 1192, 1201, 1216, 1224) THEN 'inventory'
            ELSE 'intervention'
        END;
    """
    )

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    op.drop_column("mrv_programs", "accounting_method")

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
