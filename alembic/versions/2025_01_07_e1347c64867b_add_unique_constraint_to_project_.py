"""Add unique constraint to project contract line items

Revision ID: e1347c64867b
Revises: 352abebe379b
Create Date: 2025-01-07 15:51:09.470221

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e1347c64867b"
down_revision = "352abebe379b"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_project_contract_line_items mpcli
        SET mpcli.deleted_at = NOW()
        WHERE mpcli.id NOT IN (
            SELECT latest_id
            FROM (
                SELECT MAX(mpcli2.id) AS latest_id
                FROM mrv_project_contract_line_items mpcli2
                WHERE mpcli2.deleted_at IS NULL
                GROUP BY mpcli2.contract_id, mpcli2.field_id
            ) subquery
        )
            AND mpcli.deleted_at IS NULL;"""
    )
    op.create_index(
        "unique_undeleted_contract_id_field_id",
        "mrv_project_contract_line_items",
        ["contract_id", "field_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )


def downgrade():
    op.drop_index("unique_undeleted_contract_id_field_id", table_name="mrv_project_contract_line_items")
