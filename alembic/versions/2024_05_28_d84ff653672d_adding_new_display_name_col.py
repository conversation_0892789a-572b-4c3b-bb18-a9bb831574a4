"""adding new display name col

Revision ID: d84ff653672d
Revises: c6d9bc7f5be4
Create Date: 2024-05-28 11:38:23.679120

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d84ff653672d"
down_revision = "c6d9bc7f5be4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_user_groups", sa.Column("display_name", sa.Text(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_user_groups", "display_name")
    # ### end Alembic commands ###
