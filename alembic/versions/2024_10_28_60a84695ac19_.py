"""Update baseline years and crediting year for Unilever

Revision ID: 60a84695ac19
Revises: a8b6ae570e67
Create Date: 2024-10-28 12:23:26.055281

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "60a84695ac19"
down_revision = "a8b6ae570e67"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""UPDATE mrv_programs SET crediting_year=2023 WHERE id=1145;""")
    op.execute(
        """UPDATE mrv_fields_baseline fbl
                  JOIN mrv_fields flds on flds.id=fbl.field_id
                  JOIN mrv_projects prjs on prjs.id=flds.parent_project_id
                  SET fbl.baseline_year=2023
                  WHERE prjs.program_id=1145;"""
    )


def downgrade():
    pass
