"""new cropping interventions

Revision ID: 3b0ee43c04fa
Revises: b0c68445a6c5
Create Date: 2024-06-03 13:35:08.861132

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "3b0ee43c04fa"
down_revision = "b0c68445a6c5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            name="practicechange",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            name="practicechange",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
