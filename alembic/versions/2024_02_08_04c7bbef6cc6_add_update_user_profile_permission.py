"""add update user profile  permission

Revision ID: 04c7bbef6cc6
Revises: 2ef2462f209c
Create Date: 2024-02-08 12:04:46.667600

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "04c7bbef6cc6"
down_revision = "2ef2462f209c"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

# Normal program admin does not get this permission
roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.UPDATE_USER_PROFILE.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.UPDATE_USER_PROFILE.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.UPDATE_USER_PROFILE.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.UPDATE_USER_PROFILE.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
