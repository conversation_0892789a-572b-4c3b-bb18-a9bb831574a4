"""add-declined-at-to-user-documents

Revision ID: 3f2e743d7837
Revises: 73508f292008
Create Date: 2024-05-17 11:33:17.954793

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3f2e743d7837"
down_revision = "73508f292008"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_user_documents", sa.Column("declined_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("mrv_user_documents", "declined_at")
