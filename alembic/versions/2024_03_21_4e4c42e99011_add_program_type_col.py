"""Add program type col

Revision ID: 4e4c42e99011
Revises: 545bde36649d
Create Date: 2024-03-21 15:23:47.779821

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "4e4c42e99011"
down_revision = "545bde36649d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_programs",
        sa.Column(
            "program_type",
            sa.Enum("live", "demo", "internal", "automation", name="programtype"),
            server_default="internal",
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_programs", "program_type")
    # ### end Alembic commands ###
