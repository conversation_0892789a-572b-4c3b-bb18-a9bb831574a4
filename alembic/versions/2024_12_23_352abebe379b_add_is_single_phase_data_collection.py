"""Add is_single_phase_data_collection

Revision ID: 352abebe379b
Revises: bd42f1645c1a
Create Date: 2024-12-23 17:29:01.444922

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "352abebe379b"
down_revision = "bd42f1645c1a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_programs", sa.Column("is_single_phase_data_collection", sa.<PERSON>(), server_default="0", nullable=False)
    )


def downgrade():
    op.drop_column("mrv_programs", "is_single_phase_data_collection")
