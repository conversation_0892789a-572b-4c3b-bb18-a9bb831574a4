"""Add field overlap table

Revision ID: 8ae3ad2c655b
Revises: 0b6d7ac636e1
Create Date: 2024-04-09 10:46:30.783857

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "8ae3ad2c655b"
down_revision = "0b6d7ac636e1"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_field_overlap_result",
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("previous_field_id", sa.Integer(), nullable=False),
        sa.Column("field_md5", sa.String(length=32), nullable=False),
        sa.Column("previous_field_md5", sa.String(length=32), nullable=False),
        sa.Column(
            "percentage_overlap",
            sa.DECIMAL(precision=5, scale=2),
            nullable=False,
            comment="Intersection area divided by area of previous field. Percentage range between 0 and 100",
        ),
        sa.Column(
            "area_intersection_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["previous_field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_determine_overlap_task.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="When we call boundaries service to determine the overlap between multiple fields, store results here",
    )
    op.create_index(
        op.f("ix_mrv_field_overlap_result_created_at"), "mrv_field_overlap_result", ["created_at"], unique=False
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_field_overlap_result_created_at"), table_name="mrv_field_overlap_result")
    op.drop_table("mrv_field_overlap_result")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
