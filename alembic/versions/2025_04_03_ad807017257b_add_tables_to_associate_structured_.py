"""Add tables to associate structured events with fields md5s and phases.

Revision ID: ad807017257b
Revises: 729aaf9766e9
Create Date: 2025-04-03 09:50:33.193343

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ad807017257b"
down_revision = "fd6d46ee982d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_field_event_associations",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("ses_event_id", sa.String(length=36), nullable=False),
        sa.Column("field_md5", sa.String(length=32), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("ses_event_id", "field_md5", "field_id", name="_field_event_uc"),
    )
    op.create_index(
        op.f("ix_mrv_field_event_associations_deleted_at"), "mrv_field_event_associations", ["deleted_at"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_field_event_associations_field_id"), "mrv_field_event_associations", ["field_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_field_event_associations_field_md5"), "mrv_field_event_associations", ["field_md5"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_field_event_associations_project_id"), "mrv_field_event_associations", ["project_id"], unique=False
    )

    op.create_table(
        "mrv_phase_event_associations",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_event_association_id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column("revision", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_event_association_id"],
            ["mrv_field_event_associations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("field_event_association_id", "phase_id", name="_event_phase_uc"),
    )
    op.create_index(
        op.f("ix_mrv_phase_event_associations_deleted_at"), "mrv_phase_event_associations", ["deleted_at"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_phase_event_associations_deleted_at"), table_name="mrv_phase_event_associations")
    op.drop_table("mrv_phase_event_associations")
    op.drop_index(op.f("ix_mrv_field_event_associations_project_id"), table_name="mrv_field_event_associations")
    op.drop_index(op.f("ix_mrv_field_event_associations_field_md5"), table_name="mrv_field_event_associations")
    op.drop_index(op.f("ix_mrv_field_event_associations_field_id"), table_name="mrv_field_event_associations")
    op.drop_index(op.f("ix_mrv_field_event_associations_deleted_at"), table_name="mrv_field_event_associations")
    op.drop_table("mrv_field_event_associations")
