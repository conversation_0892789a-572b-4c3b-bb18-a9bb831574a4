"""Increase chart tooltip size

Revision ID: 4a81ab579d67
Revises: 5d5e47df7e28
Create Date: 2024-01-22 20:26:43.685500

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4a81ab579d67"
down_revision = "5d5e47df7e28"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "tooltip",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.String(length=1024),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "tooltip",
        existing_type=sa.String(length=1024),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
