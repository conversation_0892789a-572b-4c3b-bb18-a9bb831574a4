"""add new stage type

Revision ID: 70d6b566ec50
Revises: 6a21b7104790
Create Date: 2023-10-31 10:48:58.659146

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "70d6b566ec50"
down_revision = "6a21b7104790"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_stages",
        "type_",
        existing_type=mysql.ENUM(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLA<PERSON>",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
        ),
        type_=sa.Enum(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
            "CHEMICAL_MANAGEMENT",
            name="stagetypes",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_stages",
        "type_",
        existing_type=sa.Enum(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
            "CHEMICAL_MANAGEMENT",
            name="stagetypes",
        ),
        type_=mysql.ENUM(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
        ),
        existing_nullable=True,
    )
