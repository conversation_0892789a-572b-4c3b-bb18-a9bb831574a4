"""add reconcile project field events permission

Revision ID: 57883723f966
Revises: 8eb827a406e7
Create Date: 2025-05-28 16:41:13.991921

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "57883723f966"
down_revision = "8eb827a406e7"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.RECONCILE_PROJECT_FIELD_EVENTS.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {},
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {},
    DefaultRoles.GROUP_ADMIN.value: {},
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {},
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
