"""Add new rule config type

Revision ID: f8ee039dc2d8
Revises: be86a1792883
Create Date: 2023-08-21 19:01:43.003398

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "f8ee039dc2d8"
down_revision = "be86a1792883"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_boundary_rule_config",
        "rule",
        existing_type=mysql.ENUM("cropland_or_grassland", "roads_or_rail", "hydrography"),
        type_=sa.Enum(
            "cropland_or_grassland", "roads_or_rail", "hydrography", "program_fields", name="fieldboundarycheckrule"
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_boundary_rule_config",
        "rule",
        existing_type=sa.Enum(
            "cropland_or_grassland", "roads_or_rail", "hydrography", "program_fields", name="fieldboundarycheckrule"
        ),
        type_=mysql.ENUM("cropland_or_grassland", "roads_or_rail", "hydrography"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
