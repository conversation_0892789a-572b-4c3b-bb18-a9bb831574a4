"""data-migration meta field in product objects

Revision ID: 9006ee2648e6
Revises: ************
Create Date: 2024-02-27 07:38:33.392149

"""

import json
from pathlib import Path
from typing import Any

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "9006ee2648e6"
down_revision = "************"
branch_labels = None
depends_on = None

MAP_NAMES = {
    "Cropflex S10": "Croplex S10",
    "manure_horse_slurry": "manure_horse_liquid",
    "mes10": "mes 10",
    "mes15": "mes 15",
    "mesz": "mes z",
    "CroplexS10": "Croplex S10",
    "NPK12-32-16": "NPK 12-32-16",
    "NPK8-24-165S": "NPK 8-24-16 5S",
    "NPK15-15-15": "NPK 15-15-15",
    "yarabelasulfan": "yarabela sulfan",
    "agrotaindri-maxx": "agrotain dri-maxx",
    "agrotainadvanced": "agrotain advanced",
    "agrotainultra": "agrotain ultra",
    "Osmocote14-14-14": "Osmocote 14-14-14",
    "Entec46": "Entec 46",
    "Entec26": "Entec 26",
}

UNKNOWN_PRODUCTS = {
    "agrotain plus sc",
    "uan+agrotain+",
    "uan+nitrapyrin",
    "aa+nitrapyrin",
    "urea+nbpt",
    "urea+dcd",
    "urea+agrotain",
    "urea+agrotain+",
    "water",
    "gypsum",
    "dolomite",
    "limestone",
}

APPLICATION_PRODUCT_DEFAULTS = [
    {"label": "ammonium bicarbonate (18, 0, 0)", "value": "ammbic", "category": "fertilizer", "meta": "dry"},
    {"label": "ammonium nitrate (34, 0, 0)", "value": "ammnit", "category": "fertilizer", "meta": "dry"},
    {"label": "di-ammonium phosphate (18, 20, 0)", "value": "dap", "category": "fertilizer", "meta": "dry"},
    {
        "label": "tri-ammonium phosphate trihydrate (21, 15, 0)",
        "value": "tap",
        "category": "fertilizer",
        "meta": "dry",
    },
    {"label": "ammonium sulfate (21, 0, 0)", "value": "ammsulf", "category": "fertilizer", "meta": "dry"},
    {"label": "aqueous ammonia (20, 0, 0)", "value": "aqamm", "category": "fertilizer", "meta": "liquid"},
    {"label": "monoammonium phosphate (11, 21, 0)", "value": "map", "category": "fertilizer", "meta": "dry"},
    {"label": "urea (46, 0, 0)", "value": "urea", "category": "fertilizer", "meta": "dry"},
    {"label": "urea-ammonium nitrate (30, 0, 0)", "value": "uan", "category": "fertilizer", "meta": "liquid"},
    {"label": "anhydrous ammonia (82, 0, 0)", "value": "aa", "category": "fertilizer", "meta": "liquid"},
    {"label": "calcium nitrate (16, 0, 0)", "value": "calnit", "category": "fertilizer", "meta": "dry"},
    {"label": "calcium ammonium nitrate (27, 0, 0)", "value": "can", "category": "fertilizer", "meta": "dry"},
    {"label": "potassium nitrate (13, 0, 37)", "value": "potnit", "category": "fertilizer", "meta": "dry"},
    {
        "label": "ammonium polyphosphate (11, 16, 0)",
        "value": "ammpolyp",
        "category": "fertilizer",
        "meta": "liquid",
    },
    {"label": "NPK 28-12-12 (28, 5, 10)", "value": "28-12-12", "category": "fertilizer", "meta": "dry"},
    {"label": "urea-ammonium sulfate (38, 0, 0)", "value": "uas", "category": "fertilizer", "meta": "dry"},
    {
        "label": "urea-ammonium nitrate at 28% (28, 0, 0)",
        "value": "uan-28",
        "category": "fertilizer",
        "meta": "liquid",
    },
    {
        "label": "urea-ammonium nitrate at 32% (32, 0, 0)",
        "value": "uan-32",
        "category": "fertilizer",
        "meta": "liquid",
    },
    {
        "label": "potassium-chloride-muriate-of-potash (0, 0, 50)",
        "value": "mop",
        "category": "fertilizer",
        "meta": "dry",
    },
    {"label": "sulfonitrate (26, 0, 0)", "value": "sulfonitrate", "category": "fertilizer", "meta": "dry"},
    {"label": "MicroEssentials S10 (12, 18, 0)", "value": "mes 10", "category": "fertilizer", "meta": "dry"},
    {"label": "MicroEssentials S15 (13, 14, 0)", "value": "mes 15", "category": "fertilizer", "meta": "dry"},
    {"label": "MicroEssentials SZ (12, 18, 0)", "value": "mes z", "category": "fertilizer", "meta": "dry"},
    {"label": "Ammonium Thiosulfate (ATS) (12, 0, 0)", "value": "ats", "category": "fertilizer", "meta": "liquid"},
    {"label": "Potassium Sulfate (0, 0, 44)", "value": "pots", "category": "fertilizer", "meta": "dry"},
    {"label": "Superphosphate (SSP) (0, 9, 0)", "value": "ssp", "category": "fertilizer", "meta": "dry"},
    {"label": "Triple Superphosphate (TSP) (0, 20, 0)", "value": "tsp", "category": "fertilizer", "meta": "dry"},
    {"label": "Crystal Green (5, 12, 0)", "value": "cg", "category": "fertilizer", "meta": "dry"},
    {"label": "NPK 20-0-24 (CN) (20, 0, 20)", "value": "20-0-24", "category": "fertilizer", "meta": "dry"},
    {"label": "Yaramilla COMPLEX (12, 5, 15)", "value": "yaramilla", "category": "fertilizer", "meta": "dry"},
    {"label": "Ammo 31 (31, 0, 0)", "value": "ammo31", "category": "fertilizer", "meta": "dry"},
    {"label": "Cropmaster 15 (15, 10, 10)", "value": "cropmaster15", "category": "fertilizer", "meta": "dry"},
    {
        "label": "Croplex 12-40-0 (+10S +Z +B) (12, 18, 0)",
        "value": "Croplex S10",
        "category": "fertilizer",
        "meta": "dry",
    },
    {
        "label": "TriplePro NPK 15-15-15 (15, 7, 12)",
        "value": "NPK 15-15-15",
        "category": "fertilizer",
        "meta": "dry",
    },
    {"label": "Lime ", "value": "lime", "category": "fertilizer", "meta": "dry"},
    {"label": "urea-ammonium phosphate (34, 7, 0)", "value": "uap", "category": "fertilizer", "meta": "dry"},
    {"label": "ESN (44, 0, 0)", "value": "esn", "category": "eenf", "meta": "dry"},
    {"label": "SuperU (46, 0, 0)", "value": "superu", "category": "eenf", "meta": "dry"},
    {"label": "Urea Sulfur Coated (SCU) (38, 0, 0)", "value": "scu", "category": "eenf", "meta": "dry"},
    {"label": "Urea Formaldehyde or Methylene Urea (40, 0, 0)", "value": "uf", "category": "eenf", "meta": "dry"},
    {"label": "Urea Polymer Coated (PCU) (40, 0, 0)", "value": "pcu", "category": "eenf", "meta": "dry"},
    {"label": "nitamin nfusion 22N (22, 0, 0)", "value": "nfusion", "category": "eenf", "meta": "liquid"},
    {
        "label": "Osmocote Classic 14-14-14 Controlled Release (14, 6, 12)",
        "value": "Osmocote 14-14-14",
        "category": "eenf",
        "meta": "dry",
    },
    {"label": "Entec 26 (+13S) (26, 0, 0)", "value": "Entec 26", "category": "eenf", "meta": "dry"},
    {"label": "Entec 46 with DMPP (46, 0, 0)", "value": "Entec 46", "category": "eenf", "meta": "dry"},
    {"label": "poultry_solid (2, 1, 1)", "value": "poultry_solid", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "poultry_liquid (2, 1, 1)",
        "value": "poultry_liquid",
        "category": "organic_amendment",
        "meta": "liquid",
    },
    {"label": "dairy_solid (2, 1, 1)", "value": "dairy_solid", "category": "organic_amendment", "meta": "dry"},
    {"label": "dairy_liquid (2, 1, 1)", "value": "dairy_liquid", "category": "organic_amendment", "meta": "liquid"},
    {"label": "beef_solid (2, 1, 1)", "value": "beef_solid", "category": "organic_amendment", "meta": "dry"},
    {"label": "beef_liquid (2, 1, 1)", "value": "beef_liquid", "category": "organic_amendment", "meta": "liquid"},
    {"label": "swine_solid (1, 0, 0)", "value": "swine_solid", "category": "organic_amendment", "meta": "dry"},
    {"label": "swine_liquid (1, 0, 0)", "value": "swine_liquid", "category": "organic_amendment", "meta": "liquid"},
    {"label": "straw (0, 0, 1)", "value": "straw", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "municipal_waste (5, 3, 0)",
        "value": "municipal_waste",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {"label": "Alfalfa Meal (4, 0, 1)", "value": "alfalfa_meal", "category": "organic_amendment", "meta": "dry"},
    {"label": "Blood Meal (12, 0, 0)", "value": "blood_meal", "category": "organic_amendment", "meta": "dry"},
    {"label": "Bone Meal (2, 7, 0)", "value": "bone_meal", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "Fish Meal/Powder (11, 3, 2)",
        "value": "fish_meal_powder",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {"label": "Hay (Grass) (2, 0, 2)", "value": "hay_grass", "category": "organic_amendment", "meta": "dry"},
    {"label": "Hay (Legume) (3, 0, 2)", "value": "hay_legume", "category": "organic_amendment", "meta": "dry"},
    {"label": "Kelp (1, 0, 3)", "value": "kelp", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "Manure Horse Solid (0, 0, 0)",
        "value": "manure_horse_solid",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {
        "label": "Manure Sheep Slurry (1, 0, 0)",
        "value": "manure_sheep_slurry",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {
        "label": "Manure Sheep Solid (1, 0, 0)",
        "value": "manure_sheep_solid",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {"label": "Peat/Muck (2, 0, 1)", "value": "peat_muck", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "Seabird and Bat Guano (1, 0, 0)",
        "value": "seabird_and_bat_guano",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {
        "label": "Seaweed Ground (1, 0, 0)",
        "value": "seaweed_ground",
        "category": "organic_amendment",
        "meta": "dry",
    },
    {"label": "Soybean Meal (7, 1, 1)", "value": "soybean_meal", "category": "organic_amendment", "meta": "dry"},
    {
        "label": "Manure Horse Liquid ",
        "value": "manure_horse_liquid",
        "category": "organic_amendment",
        "meta": "liquid",
    },
    {
        "label": "manure_compost (2, 0, 1)",
        "value": "manure_compost",
        "category": "organic_amendment",
        "meta": "dry",
    },
]

PRODUCT_BY_VALUE = {}


def format_product_with_npk_ratio(product: dict[str, Any]) -> str:
    name = product["name"]
    # `or 0` because we might have `None`
    n_ratio = product.get("n_ratio", 0) or 0
    p_ratio = product.get("p_ratio", 0) or 0
    k_ratio = product.get("k_ratio", 0) or 0
    # if npk is all 0, return only the name
    if n_ratio == 0 and p_ratio == 0 and k_ratio == 0:
        return f"{name}"
    # otherwise return name (n, p, k) formatted on two lines
    else:
        return f"{name} ({round(n_ratio)}, {round(p_ratio)}, {round(k_ratio)})"


def get_product_dict(product: dict, category: str) -> dict:
    generated = {
        "value": product["type"],
        "label": product["name"] if category == "additive" else format_product_with_npk_ratio(product),
        "category": category,
    }
    if "is_dry" in product:
        generated["meta"] = "dry" if product["is_dry"] else "liquid"
    return generated


def generate_all_product_objects():
    prouct_types = [
        ("basic_fertilizer", "fertilizer"),
        ("eenf", "eenf"),
        ("organic_amendment", "organic_amendment"),
        ("additive", "additive"),
    ]
    for file_name, category in prouct_types:
        try:
            # This is the path on the docker container file system
            filepath = f"{Path(__file__).parent.parent.parent}/defaults/static/prod/{file_name}.json"
            json_file = open(filepath)
        except FileNotFoundError:
            # This should be the path in a local repo
            filepath = f"{Path(__file__).parent.parent.parent}/app/defaults/static/prod/{file_name}.json"
            json_file = open(filepath)
        json_data = json.load(json_file)
        for obj in json_data:
            type_name = obj["type"]
            assert type_name not in PRODUCT_BY_VALUE
            PRODUCT_BY_VALUE[type_name] = get_product_dict(obj, category)


def update_default_application_products(connection: sa.Connection):
    query = sa.sql.text(
        """
UPDATE mrv_attribute_options_defaults
SET options = :app_product
WHERE type = 'application_product'
    """
    )
    connection.execute(query, {"app_product": json.dumps(APPLICATION_PRODUCT_DEFAULTS)})


def update_attribute_options(connection: sa.Connection, attribute_id: int, old_options: list[dict]) -> None:
    new_options = []
    added_values = set()
    for old_option in old_options:
        value = old_option["value"] if isinstance(old_option, dict) else old_option
        value = MAP_NAMES.get(value, value)
        if value in added_values:
            continue
        if value in UNKNOWN_PRODUCTS:
            new_option = old_option
        else:
            new_option = PRODUCT_BY_VALUE[value]
        new_options.append(new_option)
        added_values.add(value)
    update = sa.sql.text(
        """
UPDATE mrv_attributes
SET options = :new_options
WHERE id = :attribute_id
    """
    )
    connection.execute(update, {"attribute_id": attribute_id, "new_options": json.dumps(new_options)})


def update_application_product_attributes(connection: sa.Connection):
    query = sa.sql.text(
        """
SELECT id,
       options
FROM mrv_attributes
WHERE deleted_at IS NULL
      AND type = 'application_product'
    """
    )
    results = connection.execute(query).fetchall()
    for attribute_id, options in results:
        old_options = json.loads(options)
        update_attribute_options(connection, attribute_id, old_options)


def upgrade():
    generate_all_product_objects()
    connection = op.get_bind()
    try:
        update_default_application_products(connection)
        update_application_product_attributes(connection)
    except Exception as e:
        if "NoneType" not in str(e):
            raise e


def downgrade():
    """
    This migration is non-reversible - downgrade does nothing.

    Upgrade changes data, and there's no way to recover it.
    """
