"""new eu 2024 lut

Revision ID: 96321a4550d8
Revises: f9859cf44f47
Create Date: 2024-06-17 16:06:46.065090

"""

from sqlalchemy.orm import Session

from alembic import op
from projects.eligibility.helper import initialise_cargill_eu_lut

# revision identifiers, used by Alembic.
revision = "96321a4550d8"
down_revision = "f9859cf44f47"
branch_labels = None
depends_on = None


def upgrade():
    session = Session(bind=op.get_bind())
    initialise_cargill_eu_lut(session=session, year=2024)


def downgrade():
    # no previous versioning
    pass
