"""create-roles_for_group_managment

Revision ID: b4479874fc41
Revises: f8baf10b061a
Create Date: 2024-06-14 12:31:34.185387

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "b4479874fc41"
down_revision = "39a8fce1e8a8"
branch_labels = None
depends_on = None

# uncomment this in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {
        Permission.GET_PROJECT_USER.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
