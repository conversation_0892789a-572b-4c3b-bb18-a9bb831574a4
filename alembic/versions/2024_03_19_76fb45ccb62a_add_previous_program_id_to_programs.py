"""Add previous_program_id to Programs

Revision ID: 76fb45ccb62a
Revises: 24d53f8dca0f
Create Date: 2024-03-19 18:59:52.090876

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "76fb45ccb62a"
down_revision = "24d53f8dca0f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_programs", sa.Column("previous_program_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "mrv_programs_previous_program_id_mrv_programs_id",
        "mrv_programs",
        "mrv_programs",
        ["previous_program_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_programs_previous_program_id_mrv_programs_id", "mrv_programs", type_="foreignkey")
    op.drop_column("mrv_programs", "previous_program_id")
    # ### end Alembic commands ###
