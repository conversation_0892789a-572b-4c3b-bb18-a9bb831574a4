"""Added outcomes tables

Revision ID: eb3be11f1e55
Revises: 4ed890049ad8
Create Date: 2024-02-01 16:06:04.347680

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "eb3be11f1e55"
down_revision = "4ed890049ad8"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.CREATE_FIELD_OUTCOMES.value,
        Permission.CREATE_CROP_OUTCOMES.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.CREATE_FIELD_OUTCOMES.value,
        Permission.CREATE_CROP_OUTCOMES.value,
    },
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_crop_level_outcomes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("crop_type", sa.String(length=32), nullable=False),
        sa.Column("start_date", sa.Date(), nullable=True),
        sa.Column("end_date", sa.Date(), nullable=True),
        sa.Column("total_yield", sa.Float(), nullable=True),
        sa.Column("number_of_fields", sa.Integer(), nullable=True),
        sa.Column("number_of_acres", sa.Float(), nullable=True),
        sa.Column("total_reversible_credit", sa.Float(), nullable=True),
        sa.Column("total_non_reversible_credit", sa.Float(), nullable=True),
        sa.Column("total_credit", sa.Float(), nullable=True),
        sa.Column("reversible_credit_mean", sa.Float(), nullable=True),
        sa.Column("reversible_credit_standard_deviation", sa.Float(), nullable=True),
        sa.Column("total_reversible_emissions_reductions", sa.Float(), nullable=True),
        sa.Column("non_reversible_credit_mean", sa.Float(), nullable=True),
        sa.Column("non_reversible_credit_standard_deviation", sa.Float(), nullable=True),
        sa.Column("total_non_reversible_emissions_reductions", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("job_id", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_crop_level_outcomes_id"), "mrv_crop_level_outcomes", ["id"], unique=False)
    op.create_table(
        "mrv_field_level_outcomes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("start_date", sa.Date(), nullable=True),
        sa.Column("end_date", sa.Date(), nullable=True),
        sa.Column("crop_name", sa.String(length=32), nullable=True),
        sa.Column("crop_yield", sa.Float(), nullable=True),
        sa.Column("credit_share", sa.Float(), nullable=True),
        sa.Column("reversible_credit_share", sa.Float(), nullable=True),
        sa.Column("reversible_dsoc_baseline", sa.Float(), nullable=True),
        sa.Column("reversible_dsoc_practice_change", sa.Float(), nullable=True),
        sa.Column("reversible_dsoc_preliminary_credit", sa.Float(), nullable=True),
        sa.Column("reversible_dsoc_mean", sa.Float(), nullable=True),
        sa.Column("reversible_dsoc_standard_deviation", sa.Float(), nullable=True),
        sa.Column("non_reversible_credit_share", sa.Float(), nullable=True),
        sa.Column("non_reversible_mean", sa.Float(), nullable=True),
        sa.Column("non_reversible_standard_deviation", sa.Float(), nullable=True),
        sa.Column("non_reversible_direct_n2o_baseline", sa.Float(), nullable=True),
        sa.Column("non_reversible_direct_n2o_practice_change", sa.Float(), nullable=True),
        sa.Column("non_reversible_direct_n2o_preliminary_credit", sa.Float(), nullable=True),
        sa.Column("non_reversible_direct_n2o_mean", sa.Float(), nullable=True),
        sa.Column("non_reversible_direct_n2o_standard_deviation", sa.Float(), nullable=True),
        sa.Column("non_reversible_indirect_n2o_baseline", sa.Float(), nullable=True),
        sa.Column("non_reversible_indirect_n2o_practice_change", sa.Float(), nullable=True),
        sa.Column("non_reversible_indirect_n2o_credit", sa.Float(), nullable=True),
        sa.Column("non_reversible_indirect_n2o_mean", sa.Float(), nullable=True),
        sa.Column("non_reversible_indirect_n2o_standard_deviation", sa.Float(), nullable=True),
        sa.Column("non_reversible_soil_ch4_baseline", sa.Float(), nullable=True),
        sa.Column("non_reversible_soil_ch4_practice_change", sa.Float(), nullable=True),
        sa.Column("non_reversible_soil_ch4_credit", sa.Float(), nullable=True),
        sa.Column("non_reversible_fossil_fuel_co2_baseline", sa.Float(), nullable=True),
        sa.Column("non_reversible_fossil_fuel_co2_practice_change", sa.Float(), nullable=True),
        sa.Column("non_reversible_fossil_fuel_co2_credit", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("job_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_field_level_outcomes_id"), "mrv_field_level_outcomes", ["id"], unique=False)
    op.add_column(
        "mrv_crop_level_outcomes",
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
    )
    op.add_column(
        "mrv_field_level_outcomes",
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
    )
    op.drop_column("mrv_field_level_outcomes", "non_reversible_indirect_n2o_standard_deviation")
    op.drop_column("mrv_field_level_outcomes", "non_reversible_indirect_n2o_mean")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_field_level_outcomes_id"), table_name="mrv_field_level_outcomes")
    op.drop_table("mrv_field_level_outcomes")
    op.drop_index(op.f("ix_mrv_crop_level_outcomes_id"), table_name="mrv_crop_level_outcomes")
    op.drop_table("mrv_crop_level_outcomes")
    op.add_column(
        "mrv_field_level_outcomes", sa.Column("non_reversible_indirect_n2o_mean", mysql.FLOAT(), nullable=True)
    )
    op.add_column(
        "mrv_field_level_outcomes",
        sa.Column("non_reversible_indirect_n2o_standard_deviation", mysql.FLOAT(), nullable=True),
    )
    op.drop_column("mrv_field_level_outcomes", "updated_at")
    op.drop_column("mrv_crop_level_outcomes", "updated_at")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)

    # ### end Alembic commands ###
