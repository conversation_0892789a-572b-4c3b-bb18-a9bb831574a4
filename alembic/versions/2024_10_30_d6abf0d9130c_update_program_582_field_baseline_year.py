"""Update program 582 field baseline year

Revision ID: d6abf0d9130c
Revises: 60a84695ac19
Create Date: 2024-10-30 13:14:34.171910

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d6abf0d9130c"
down_revision = "60a84695ac19"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_fields_baseline mfb
        JOIN mrv_fields mf on mfb.field_id = mf.id
        JOIN mrv_projects mp on mf.parent_project_id = mp.id
        SET mfb.baseline_year=2023
        WHERE mp.program_id=582;"""
    )


def downgrade():
    pass
