"""update response store

Revision ID: e8bfb2059efd
Revises: 744e48a553bf
Create Date: 2024-01-30 15:17:48.456282

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e8bfb2059efd"
down_revision = "744e48a553bf"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            comment="Stores error message from monitor API if request creation failed",
        ),
    )
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column("error_message", sa.Text(), nullable=True, comment="Error message if any from monitor API"),
    )
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column("received_at", sa.TIMESTAMP(), nullable=True, comment="Time when callback is received"),
    )
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column("processed_at", sa.TIMESTAMP(), nullable=True, comment="Time when callback is processed"),
    )
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_monitor_response_store", "updated_at")
    op.drop_column("mrv_monitor_response_store", "processed_at")
    op.drop_column("mrv_monitor_response_store", "received_at")
    op.drop_column("mrv_monitor_response_store", "error_message")
    op.drop_column("mrv_monitor_request_store", "error_message")
    # ### end Alembic commands ###
