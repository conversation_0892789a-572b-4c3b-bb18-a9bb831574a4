"""create outcome approval statuses

Revision ID: ce7455bed080
Revises: 4e0348b24368
Create Date: 2024-10-21 11:45:24.518117

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ce7455bed080"
down_revision = "4e0348b24368"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_outcome_approval_statuses",
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=False),
        sa.Column(
            "outcome_approval_status",
            sa.Enum("APPROVED", "REVOKED", "TEST", name="outcomeapprovalstatustype"),
            nullable=False,
        ),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("program_id", "task_id"),
    )
    op.create_index(
        op.f("ix_mrv_outcome_approval_statuses_program_id"),
        "mrv_outcome_approval_statuses",
        ["program_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_outcome_approval_statuses_task_id"), "mrv_outcome_approval_statuses", ["task_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_outcome_approval_statuses_updated_at"),
        "mrv_outcome_approval_statuses",
        ["updated_at"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_outcome_approval_statuses_updated_at"), table_name="mrv_outcome_approval_statuses")
    op.drop_index(op.f("ix_mrv_outcome_approval_statuses_task_id"), table_name="mrv_outcome_approval_statuses")
    op.drop_index(op.f("ix_mrv_outcome_approval_statuses_program_id"), table_name="mrv_outcome_approval_statuses")
    op.drop_table("mrv_outcome_approval_statuses")
