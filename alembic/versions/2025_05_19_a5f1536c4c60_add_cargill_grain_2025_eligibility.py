"""add-cargill-grain-2025-eligibility

Revision ID: a5f1536c4c60
Revises: 4aa21e70909b
Create Date: 2025-05-19 14:20:06.170303

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a5f1536c4c60"
down_revision = "4aa21e70909b"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
        ),
        type_=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_GRAIN_2025",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
            name="eligibilitytypes",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_GRAIN_2025",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
            name="eligibilitytypes",
        ),
        type_=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
        ),
        existing_nullable=True,
    )
