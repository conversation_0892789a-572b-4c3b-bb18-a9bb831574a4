"""Add background and text color to Section

Revision ID: f03596cb34fe
Revises: e7644d7cb7be
Create Date: 2023-11-03 20:55:26.909270

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f03596cb34fe"
down_revision = "e7644d7cb7be"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_reporting_dashboard_sections", sa.Column("background_color", sa.String(length=50), nullable=True)
    )
    op.add_column("mrv_reporting_dashboard_sections", sa.Column("text_color", sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboard_sections", "text_color")
    op.drop_column("mrv_reporting_dashboard_sections", "background_color")
    # ### end Alembic commands ###
