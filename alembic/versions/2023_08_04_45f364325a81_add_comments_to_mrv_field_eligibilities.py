"""Add comments to mrv_field_eligibilities

Revision ID: 45f364325a81
Revises: 44434ca9c00d
Create Date: 2023-08-04 13:53:06.389075

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "45f364325a81"
down_revision = "44434ca9c00d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_field_eligibilities",
        "phase_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment="Enrolment vs monitoring phase - see table comment",
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "field_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment="The field these practices apply to",
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "stage_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment="Always NULL during a monitoring phase, always set during an enrolment phase",
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "eligible_practices",
        existing_type=mysql.JSON(),
        comment="An array of arrays, e.g. [['no till'], ['reduced till', 'cover crops']]. During the monitoring phase, when we only have the actual practices carried out, there's only one nested array,e.g. [['no till', 'cover crops']]",
        existing_nullable=True,
    )
    op.create_table_comment(
        "mrv_field_eligibilities",
        "When we determine which practices are eligible for a field, we store them in this table. During the enrolment phase, we store all the eligible practices. During the monitoring phase, we store the actual practices that were carried out. During both phases, we might calculate the eligible practices for a field many times and store the result each time. The result for any given field might vary over time (e.g. because the rules have changed). Always use the latest results (created_at) for a given field.",
        existing_comment=None,
        schema=None,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table_comment(
        "mrv_field_eligibilities",
        existing_comment="When we determine which practices are eligible for a field, we store them in this table. During the enrolment phase, we store all the eligible practices. During the monitoring phase, we store the actual practices that were carried out. During both phases, we might calculate the eligible practices for a field many times and store the result each time. The result for any given field might vary over time (e.g. because the rules have changed). Always use the latest results (created_at) for a given field.",
        schema=None,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "eligible_practices",
        existing_type=mysql.JSON(),
        comment=None,
        existing_comment="An array of arrays, e.g. [['no till'], ['reduced till', 'cover crops']]. During the monitoring phase, when we only have the actual practices carried out, there's only one nested array,e.g. [['no till', 'cover crops']]",
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "stage_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment=None,
        existing_comment="Always NULL during a monitoring phase, always set during an enrolment phase",
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "field_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment=None,
        existing_comment="The field these practices apply to",
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_field_eligibilities",
        "phase_id",
        existing_type=mysql.INTEGER(display_width=11),
        comment=None,
        existing_comment="Enrolment vs monitoring phase - see table comment",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
