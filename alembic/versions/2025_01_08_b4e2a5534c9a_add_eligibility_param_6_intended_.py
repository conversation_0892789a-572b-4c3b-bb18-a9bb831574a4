"""add-eligibility-param-6-intended-commodity-crop

Revision ID: b4e2a5534c9a
Revises: e1347c64867b
Create Date: 2025-01-08 11:56:10.307238

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b4e2a5534c9a"
down_revision = "e1347c64867b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_cover_crop_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_grazing_interventions_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_rate_reduction_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_split_application_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_timing_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_rice_irrigation_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_tillage_eligibility_config",
        sa.Column("eligible_intended_commodity_crops", sa.String(length=255), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_tillage_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_rice_irrigation_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_nm_timing_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_nm_split_application_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_nm_rate_reduction_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_nm_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_grazing_interventions_eligibility_config", "eligible_intended_commodity_crops")
    op.drop_column("mrv_cover_crop_eligibility_config", "eligible_intended_commodity_crops")
    # ### end Alembic commands ###
