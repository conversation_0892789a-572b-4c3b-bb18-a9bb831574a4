"""add new columns to simluation request

Revision ID: 6256fd11c2dd
Revises: 4c067961b3c6
Create Date: 2025-07-17 09:24:57.498529

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6256fd11c2dd"
down_revision = "4c067961b3c6"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_dndc_tasks",
        sa.<PERSON>umn(
            "accounting_method",
            sa.Enum("intervention", "inventory", "biofuels", name="accountingmethod"),
            nullable=True,
        ),
    )
    op.add_column(
        "mrv_dndc_tasks",
        sa.Column(
            "protocol", sa.Enum("VERRA", "CAR_SEP", "SUSTAINCERT", "GENERAL_SCOPE_3", name="protocols"), nullable=True
        ),
    )
    op.add_column(
        "mrv_dndc_tasks",
        sa.<PERSON>umn(
            "baseline_method",
            sa.Enum(
                "ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE", name="baselinemethod"
            ),
            nullable=True,
        ),
    )
    op.add_column("mrv_dndc_tasks", sa.Column("dndc_version", sa.String(length=10), nullable=True))


def downgrade():
    op.drop_column("mrv_dndc_tasks", "dndc_version")
    op.drop_column("mrv_dndc_tasks", "baseline_method")
    op.drop_column("mrv_dndc_tasks", "protocol")
    op.drop_column("mrv_dndc_tasks", "accounting_method")
