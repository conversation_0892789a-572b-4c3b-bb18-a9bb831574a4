"""update-user-group-project-unique-key

Revision ID: a2e6bcbe9f2d
Revises: 2e3d6ef22b92
Create Date: 2025-01-31 15:40:40.693712

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a2e6bcbe9f2d"
down_revision = "2e3d6ef22b92"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("group_project_id", table_name="mrv_group_projects")
    op.create_index(
        "unique_undeleted_group_id_project_id",
        "mrv_group_projects",
        ["group_id", "project_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )


def downgrade():
    op.create_index("group_project_id", "mrv_group_projects", ["group_id", "project_id", "deleted_at"], unique=True)
    op.drop_index("unique_undeleted_group_id_project_id", table_name="mrv_group_projects")
