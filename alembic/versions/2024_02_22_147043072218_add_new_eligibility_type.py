"""add new eligibility type for cotton 2024

Revision ID: 147043072218
Revises: a02522ad54dd
Create Date: 2024-02-22 09:54:11.757835

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "147043072218"
down_revision = "a02522ad54dd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CUSTOM",
        ),
        type_=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            name="eligibilitytypes",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            name="eligibilitytypes",
        ),
        type_=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CUSTOM",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
