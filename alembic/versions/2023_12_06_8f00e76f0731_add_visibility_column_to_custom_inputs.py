"""add visibility column to custom inputs

Revision ID: 8f00e76f0731
Revises: 7cd69d75c03b
Create Date: 2023-12-06 15:01:48.516928

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8f00e76f0731"
down_revision = "7cd69d75c03b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_program_custom_reg_inputs", sa.Column("visibility", sa.JSON(), nullable=True))


def downgrade():
    op.drop_column("mrv_program_custom_reg_inputs", "visibility")
