"""Add permission to override project field values

Revision ID: c6d9bc7f5be4
Revises: 043e262d38b0
Create Date: 2024-05-24 15:17:17.706292

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "c6d9bc7f5be4"
down_revision = "043e262d38b0"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.OVERRIDE_PROJECT_FIELD_VALUES.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.OVERRIDE_PROJECT_FIELD_VALUES.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.OVERRIDE_PROJECT_FIELD_VALUES.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
