"""Make mrv_dndc_tasks.scenarios_service_api nonnullable

Revision ID: 3d210e1d817f
Revises: dfd261f5c94e
Create Date: 2024-07-01 14:02:07.486986

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "3d210e1d817f"
down_revision = "dfd261f5c94e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add scenarios_service_api values to existing mrv_dndc_tasks
    op.execute(
        "UPDATE mrv_dndc_tasks SET scenarios_service_api = 'measure_api' WHERE scenarios_service_api is NULL AND phase = 'MONITORING'"
    )
    op.execute(
        "UPDATE mrv_dndc_tasks SET scenarios_service_api = 'explore_api' WHERE scenarios_service_api is NULL AND phase = 'ENROLMENT'"
    )

    op.alter_column(
        "mrv_dndc_tasks",
        "scenarios_service_api",
        existing_type=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_tasks",
        "scenarios_service_api",
        existing_type=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        nullable=True,
    )
    # ### end Alembic commands ###
