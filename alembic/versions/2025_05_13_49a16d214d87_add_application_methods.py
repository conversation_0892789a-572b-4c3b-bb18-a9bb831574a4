"""add-application-methods

Revision ID: 49a16d214d87
Revises: 4e34a9ea8683
Create Date: 2025-05-13 17:43:09.075592

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "49a16d214d87"
down_revision = "4e34a9ea8683"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=mysql.ENUM("BROADCASTED", "FERTIGATION", "INJECTED", "AVIATION", "INCORPORATED", "SUBSURFACE"),
        type_=sa.Enum(
            "BROADCASTED",
            "FERTIGATION",
            "FERTIGATION_FURROW",
            "FERTIGATION_SPRINKLER",
            "FERTIGATION_DRIP",
            "INJECTED",
            "AVIATION",
            "SUBSURFACE",
            "INCORPORATED",
            name="applicationmethod",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=sa.Enum(
            "BROADCASTED",
            "FERTIGATION",
            "FERTIGATION_FURROW",
            "FERTIGATION_SPRINKLER",
            "FERTIGATION_DRIP",
            "INJECTED",
            "AVIATION",
            "SUBSURFACE",
            "INCORPORATED",
            name="applicationmethod",
        ),
        type_=mysql.ENUM("BROADCASTED", "FERTIGATION", "INJECTED", "AVIATION", "INCORPORATED", "SUBSURFACE"),
        existing_nullable=True,
    )
