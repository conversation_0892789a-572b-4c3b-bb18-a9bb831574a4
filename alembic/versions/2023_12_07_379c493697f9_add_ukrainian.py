"""add ukrainian

Revision ID: 379c493697f9
Revises: 8f76a086bf5e
Create Date: 2023-12-07 11:53:08.044479

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "379c493697f9"
down_revision = "8f76a086bf5e"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=mysql.ENUM("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HU"),
        type_=sa.Enum(
            "en_US",
            "en_GB",
            "fr",
            "pl_PL",
            "pt_BR",
            "ro_RO",
            "ru_RU",
            "de_DE",
            "vi_VN",
            "hu_HU",
            "uk_UA",
            name="locale",
        ),
        existing_nullable=False,
    )


def downgrade():  # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=sa.Enum(
            "en_US",
            "en_GB",
            "fr",
            "pl_PL",
            "pt_BR",
            "ro_RO",
            "ru_RU",
            "de_DE",
            "vi_VN",
            "hu_HU",
            "uk_UA",
            name="locale",
        ),
        type_=mysql.ENUM("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HU"),
        existing_nullable=False,
    )
