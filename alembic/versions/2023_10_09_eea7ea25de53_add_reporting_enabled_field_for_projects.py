"""Add reporting_enabled field for projects

Revision ID: eea7ea25de53
Revises: abb85a9ddc35
Create Date: 2023-10-09 21:28:02.268779

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "eea7ea25de53"
down_revision = "abb85a9ddc35"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_projects",
        sa.Column("reporting_enabled", sa.<PERSON>(), server_default="1", nullable=False),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_projects", "reporting_enabled")
    # ### end Alembic commands ###
