"""migrate-client-policies

Revision ID: a7259e7f52d2
Revises: cf5c239d76cd
Create Date: 2024-07-04 20:29:06.193248

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a7259e7f52d2"
down_revision = "cf5c239d76cd"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_documents",
        "description",
        existing_type=mysql.VARCHAR(length=1000),
        type_=sa.Text(),
        existing_nullable=True,
    )
    op.execute(
        """
        INSERT IGNORE INTO `mrv_documents` (`id`, `name`, `category`, `description`, `content`, `active_from`, `is_public`) VALUES
        (68, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (95, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (116, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (117, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (118, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (133, '', 'client', 'TEST ', 'https://www.regrow.ag/privacy-policy', '2024-07-04 08:23:44', 1),
        (150, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (155, '', 'client', 'Cargill Policy', ' https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (253, '', 'client', 'Je comprends que mes informations personnelles sont traitées par Cargill en accord avec la Cargill Business Information Notice et Cargill a donné des instructions à Regrow de traiter mes informations personnelles en accord avec cette Notice. Les informations personnelles collectées par cette plateforme sont utilisées uniquement dans le cadre du programme Cargill RegenConnect.     J’accepte les termes et conditions de Regrow concernant l’usage de la plateforme.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (254, '', 'client', 'Je comprends que mes informations personnelles sont traitées par Cargill en accord avec la Cargill Business Information Notice et Cargill a donné des instructions à Regrow de traiter mes informations personnelles en accord avec cette Notice. Les informations personnelles collectées par cette plateforme sont utilisées uniquement dans le cadre du programme Cargill RegenConnect.     J’accepte les termes et conditions de Regrow concernant l’usage de la plateforme.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (256, '', 'client', 'I understand that my personal information is processed by Cargill in accordance with the Cargill Business Information Notice and Cargill instructs Regrow to process my personal information in accordance with this Notice.  Personal Information collected in this platform is only processed for the purpose of Cargill RegenConnect.     I agree with the terms and conditions from Regrow regarding the use of the platform', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (257, '', 'client', 'Sunt de acord cu procesarea informațiilor comerciale (așa cum sunt definite de și în conformitate cu Notificarea Cargill privind informațiile comerciale, disponibilă la https://www.cargill.com/page/business-notice) de către Regrow și/sau Cargill. Informațiile cu caracter personal colectate pe Portal sunt prelucrate numai în scopul Cargill RegenConnect™.     Sunt de acord cu termenii și condițiile Regrow privind utilizarea platformei.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (258, '', 'client', 'Przyjmuję do wiadomości, że moje dane osobowe są przetwarzane przez firmę Cargill zgodnie z Informacjami biznesowymi firmy Cargill, a firma Cargill zleca firmie Regrow przetwarzanie moich danych osobowych zgodnie z tymi informacjami. Dane osobowe gromadzone na tej platformie są przetwarzane wyłącznie do celów Cargill RegenConnect.      Zgadzam się z warunkami Regrow dotyczącymi korzystania z platformy.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (259, '', 'client', 'Mir ist bekannt, dass meine persönlichen Daten von Cargill in Übereinstimmung mit der Cargill Business Information Notice verarbeitet werden und dass Cargill Regrow anweist, meine persönlichen Daten in Übereinstimmung mit dieser Notice zu verarbeiten. Persönliche Daten, die auf dieser Plattform gesammelt werden, werden nur für den Zweck von Cargill RegenConnect verarbeitet.   Ich bin mit den Bedingungen von Regrow bezüglich der Nutzung der Plattform einverstanden.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (275, '', 'client', 'Business Information Notice', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1119, '', 'client', 'I have read and accept Cargill\\'s privacy policy.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1126, '', 'client', 'Je comprends que mes informations personnelles sont traitées par Cargill en accord avec la Cargill Business Information Notice et Cargill a donné des instructions à Regrow de traiter mes informations personnelles en accord avec cette Notice. Les informations personnelles collectées par cette plateforme sont utilisées uniquement dans le cadre du programme Cargill RegenConnect. J’accepte les termes et conditions de Regrow concernant l’usage de la plateforme', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1127, '', 'client', 'Mir ist bekannt, dass meine persönlichen Daten von Cargill in Übereinstimmung mit der Cargill Business Information Notice verarbeitet werden und dass Cargill Regrow anweist, meine persönlichen Daten in Übereinstimmung mit dieser Notice zu verarbeiten. Persönliche Daten, die auf dieser Plattform gesammelt werden, werden nur für den Zweck von Cargill RegenConnect verarbeitet.   Ich bin mit den Bedingungen von Regrow bezüglich der Nutzung der Plattform einverstanden.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1128, '', 'client', 'Przyjmuję do wiadomości, że moje dane osobowe są przetwarzane przez firmę Cargill zgodnie z Informacjami biznesowymi firmy Cargill, a firma Cargill zleca firmie Regrow przetwarzanie moich danych osobowych zgodnie z tymi informacjami. Dane osobowe gromadzone na tej platformie są przetwarzane wyłącznie do celów Cargill RegenConnect.      Zgadzam się z warunkami Regrow dotyczącymi korzystania z platformy.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1129, '', 'client', 'Sunt de acord cu procesarea informațiilor comerciale (așa cum sunt definite de și în conformitate cu Notificarea Cargill privind informațiile comerciale, disponibilă la https://www.cargill.com/page/business-notice) de către Regrow și/sau Cargill. Informațiile cu caracter personal colectate pe Portal sunt prelucrate numai în scopul Cargill RegenConnect™.     Sunt de acord cu termenii și condițiile Regrow privind utilizarea platformei.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1130, '', 'client', 'Tudomásul veszem, hogy személyes adataimat a Cargill a Cargill Üzleti Információ Tájékoztatójával összhangban dolgozza fel, és a Cargill utasítja a Regrow-t, hogy a személyes adataimat ennek a Tájékoztatónak megfelelően dolgozza fel. Az ezen a platformon gyűjtött személyes adatokat kizárólag a Cargill RegenConnect ® céljára használja fel. Elfogadom a Regrow által a platform használatára vonatkozó feltételeket.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1131, '', 'client', 'Я розумію, що мою особисту інформацію обробляє компанія Cargill відповідно до Повідомлення про бізнес-інформацію Cargill, і Cargill доручає Regrow обробляти мою особисту інформацію згідно з цим Повідомленням. Особиста інформація, зібрана на цій платформі, обробляється лише для цілей Cargill RegenConnect ®. Я погоджуюся з умовами використання платформи від Regrow.', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1143, '', 'client', 'Cargill Policy', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1),
        (1150, '', 'client', 'I understand that my personal information is processed by Cargill in accordance with the Cargill Business Information Notice and Cargill instructs Regrow to process my personal information in accordance with this Notice.  Personal Information collected in this platform is only processed for the purpose of Cargill RegenConnect.     I agree with the terms and conditions from Regrow regarding the use of the platform', 'https://www.cargill.com/page/business-notice', '2024-07-04 08:23:44', 1);
    """
    )
    op.execute(
        """
        INSERT INTO `mrv_program_documents` (`program_id`, `document_id`) VALUES
        (68, 68),
        (95, 95),
        (116, 116),
        (117, 117),
        (118, 118),
        (133, 133),
        (150, 150),
        (155, 155),
        (253, 253),
        (254, 254),
        (256, 256),
        (257, 257),
        (258, 258),
        (259, 259),
        (275, 275),
        (1119, 1119),
        (1126, 1126),
        (1127, 1127),
        (1128, 1128),
        (1129, 1129),
        (1130, 1130),
        (1131, 1131),
        (1143, 1143),
        (1150, 1150);
    """
    )


def downgrade():
    op.alter_column(
        "mrv_documents",
        "description",
        existing_type=sa.Text(),
        type_=mysql.VARCHAR(length=1000),
        existing_nullable=True,
    )
    op.execute(
        "DELETE FROM `mrv_program_documents` WHERE `document_id` IN (68, 95, 116, 117, 118, 133, 150, 155, 253, 254, 256, 257, 258, 259, 275, 1119, 1126, 1127, 1128, 1129, 1130, 1131, 1143, 1150);"
    )
    op.execute(
        "DELETE FROM `mrv_documents` WHERE `id` IN (68, 95, 116, 117, 118, 133, 150, 155, 253, 254, 256, 257, 258, 259, 275, 1119, 1126, 1127, 1128, 1129, 1130, 1131, 1143, 1150);"
    )
