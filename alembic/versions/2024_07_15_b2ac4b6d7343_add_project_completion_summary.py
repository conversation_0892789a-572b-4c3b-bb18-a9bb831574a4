"""add project completion summary

Revision ID: b2ac4b6d7343
Revises: 47b57b153c4c
Create Date: 2024-07-15 10:41:43.002664

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "b2ac4b6d7343"
down_revision = "47b57b153c4c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_project_completion_summary",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=False),
        sa.Column("percentage_complete", sa.Integer(), nullable=False),
        sa.Column("completed", sa.Integer(), nullable=True),
        sa.Column("to_complete", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=False),
        sa.Column("is_up_to_date", sa.Boolean(), server_default="0", nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("project_id", "stage_id"),
        comment="ProjectCompletionSummary is a summary of ProjectCompletion",
    )
    op.create_index(
        op.f("ix_mrv_project_completion_summary_created_at"),
        "mrv_project_completion_summary",
        ["created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_project_completion_summary_percentage_complete"),
        "mrv_project_completion_summary",
        ["percentage_complete"],
        unique=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_mrv_project_completion_summary_percentage_complete"), table_name="mrv_project_completion_summary"
    )
    op.drop_index(op.f("ix_mrv_project_completion_summary_created_at"), table_name="mrv_project_completion_summary")
    op.drop_table("mrv_project_completion_summary")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
