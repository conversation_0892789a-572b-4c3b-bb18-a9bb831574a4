"""update-eu-y3-lut

Revision ID: 04414ba82d76
Revises: 6e68b1684f41
Create Date: 2024-04-04 10:52:48.865026

"""

from sqlalchemy.orm import Session

from alembic import op
from projects.eligibility.helper import initialise_cargill_eu_lut

# revision identifiers, used by Alembic.
revision = "04414ba82d76"
down_revision = "6e68b1684f41"
branch_labels = None
depends_on = None


def upgrade():
    # Initialising LUT table with 2024 data
    session = Session(bind=op.get_bind())
    initialise_cargill_eu_lut(session=session, year=2024)


def downgrade():
    # no need to going back to previous LUT version
    pass
