"""Fix stage optis years

Revision ID: 8cf9a9cb6ebf
Revises: a2e6bcbe9f2d
Create Date: 2025-02-27 15:22:30.721660

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "8cf9a9cb6ebf"
down_revision = "a2e6bcbe9f2d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_stages ms
        SET
            ms.optis_year_start = ms.year_start,
            ms.optis_year_end = ms.year_end
        WHERE ms.optis_prefill = 1;"""
    )


def downgrade():
    pass
