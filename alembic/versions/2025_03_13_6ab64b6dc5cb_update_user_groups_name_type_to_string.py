"""Update user groups name type to string

Revision ID: 6ab64b6dc5cb
Revises: 55c790572e8c
Create Date: 2025-03-13 10:33:52.145278

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "6ab64b6dc5cb"
down_revision = "55c790572e8c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_user_groups", "name", existing_type=mysql.TEXT(), type_=sa.String(length=150), existing_nullable=False
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_user_groups", "name", existing_type=sa.String(length=150), type_=mysql.TEXT(), existing_nullable=False
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
