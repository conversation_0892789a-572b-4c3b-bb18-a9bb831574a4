"""Make phase_id non-null

Revision ID: 44434ca9c00d
Revises: 79600fdf4ad2
Create Date: 2023-08-02 13:14:41.232993

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "44434ca9c00d"
down_revision = "79600fdf4ad2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # To see why we need to add mode STRICT_TRANS_TABLES, see https://bugs.mysql.com/bug.php?id=93838
    op.execute("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'")
    op.alter_column(
        "mrv_field_eligibilities", "phase_id", existing_type=mysql.INTEGER(display_width=11), nullable=False
    )
    # Restore mode to its previous setting
    op.execute("SET sql_mode = 'ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # To see why we need to add mode STRICT_TRANS_TABLES, see https://bugs.mysql.com/bug.php?id=93838
    op.execute("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'")
    op.alter_column("mrv_field_eligibilities", "phase_id", existing_type=mysql.INTEGER(display_width=11), nullable=True)
    # Restore mode to its previous setting
    op.execute("SET sql_mode = 'ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'")
    # ### end Alembic commands ###
