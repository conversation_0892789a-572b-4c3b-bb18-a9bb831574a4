"""Add climate matched baseline

Revision ID: 65b012bb5f94
Revises: 2e9a3e1035f2
Create Date: 2024-09-05 14:29:05.026663

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "65b012bb5f94"
down_revision = "2e9a3e1035f2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED"),
        type_=sa.Enum("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", name="baselinemethod"),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=sa.Enum("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", name="baselinemethod"),
        type_=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED"),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
