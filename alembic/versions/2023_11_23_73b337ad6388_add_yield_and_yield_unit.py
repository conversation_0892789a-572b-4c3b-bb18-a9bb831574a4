"""add yield and yield_unit

Revision ID: 73b337ad6388
Revises: 2315c6d5ed03
Create Date: 2023-11-23 12:23:18.110946

"""

import sqlalchemy as sa

from alembic import op

revision = "73b337ad6388"
down_revision = "2315c6d5ed03"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflict_practice_config", sa.Column("yield_attr_id", sa.Integer(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("yield_unit_attr_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "yield_unit_attr_id_fk", "mrv_conflict_practice_config", "mrv_attributes", ["yield_unit_attr_id"], ["id"]
    )
    op.create_foreign_key(
        "yield_attr_id_fk", "mrv_conflict_practice_config", "mrv_attributes", ["yield_attr_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("yield_unit_attr_id_fk", "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_constraint("yield_attr_id_fk", "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_column("mrv_conflict_practice_config", "yield_unit_attr_id")
    op.drop_column("mrv_conflict_practice_config", "yield_attr_id")
    # ### end Alembic commands ###
