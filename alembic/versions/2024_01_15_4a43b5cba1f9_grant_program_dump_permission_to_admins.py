"""grant-program-dump-permission-to-admins

Revision ID: 4a43b5cba1f9
Revises: 155c19a17237
Create Date: 2024-01-15 17:34:07.235453

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "4a43b5cba1f9"
down_revision = "155c19a17237"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_DUMP_PROGRAM_DATA.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_DUMP_PROGRAM_DATA.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_DUMP_PROGRAM_DATA.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
