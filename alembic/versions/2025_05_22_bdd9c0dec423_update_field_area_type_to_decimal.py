"""Update field area type to decimal

Revision ID: bdd9c0dec423
Revises: a5f1536c4c60
Create Date: 2025-05-22 13:26:07.449069

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "bdd9c0dec423"
down_revision = "a5f1536c4c60"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_fields", sa.Column("area_float", sa.Float(), nullable=True))
    op.execute(
        """UPDATE mrv_fields mf
        SET mf.area_float = mf.area;"""
    )
    op.alter_column(
        "mrv_fields",
        "area",
        existing_type=mysql.FLOAT(),
        type_=sa.DECIMAL(precision=14, scale=6),
        existing_nullable=False,
    )
    op.add_column("mrv_fields_history", sa.Column("area_float", sa.Float(), nullable=True, comment="Area in ha."))
    op.execute(
        """UPDATE mrv_fields_history mfh
        SET mfh.area_float = mfh.area;"""
    )
    op.alter_column(
        "mrv_fields_history",
        "area",
        existing_type=mysql.FLOAT(),
        type_=sa.DECIMAL(precision=14, scale=6),
        existing_comment="Area in ha.",
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_fields_history",
        "area",
        existing_type=sa.DECIMAL(precision=14, scale=6),
        type_=mysql.FLOAT(),
        existing_comment="Area in ha.",
        existing_nullable=False,
    )
    op.execute(
        """UPDATE mrv_fields_history mfh
        SET mfh.area = mfh.area_float;"""
    )
    op.drop_column("mrv_fields_history", "area_float")
    op.alter_column(
        "mrv_fields",
        "area",
        existing_type=sa.DECIMAL(precision=14, scale=6),
        type_=mysql.FLOAT(),
        existing_nullable=False,
    )
    op.execute(
        """UPDATE mrv_fields mf
        SET mf.area = mf.area_float;"""
    )
    op.drop_column("mrv_fields", "area_float")
