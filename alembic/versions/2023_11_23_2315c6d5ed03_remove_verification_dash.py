"""remove verification dash

Revision ID: 2315c6d5ed03
Revises: f9ddec0e02d2
Create Date: 2023-11-23 11:33:32.014832

"""

import sqlalchemy as sa
from sqlalchemy import inspect
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "2315c6d5ed03"
down_revision = "f9ddec0e02d2"
branch_labels = None
depends_on = None


def index_exists(table_name: str, index_name: str) -> bool:
    try:
        bind = op.get_context().bind
        insp = inspect(bind)
        indices = insp.get_columns(table_name)
        return any(i["name"] == index_name for i in indices)
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def upgrade():
    if index_exists("mrv_verification_dashboard_table_fields", "_verification_dashboard_id_table_field_name_uc"):
        op.drop_index(
            "_verification_dashboard_id_table_field_name_uc", table_name="mrv_verification_dashboard_table_fields"
        )
    if index_exists("mrv_verification_dashboard_table_fields", "ix_mrv_verification_dashboard_table_fields_id"):
        op.drop_index(
            "ix_mrv_verification_dashboard_table_fields_id", table_name="mrv_verification_dashboard_table_fields"
        )
    if index_exists(
        "mrv_verification_dashboard_table_fields", "ix_mrv_verification_dashboard_table_fields_verification__7353"
    ):
        op.drop_index(
            "ix_mrv_verification_dashboard_table_fields_verification__7353",
            table_name="mrv_verification_dashboard_table_fields",
        )

    op.drop_table("mrv_verification_dashboard_table_fields")
    if index_exists(
        "mrv_verification_dashboard_conflict_types", "_verification_dashboard_id_conflict_type_attribute_id_uc"
    ):
        op.drop_index(
            "_verification_dashboard_id_conflict_type_attribute_id_uc",
            table_name="mrv_verification_dashboard_conflict_types",
        )
    if index_exists("mrv_verification_dashboard_conflict_types", "ix_mrv_verification_dashboard_conflict_types_id"):
        op.drop_index(
            "ix_mrv_verification_dashboard_conflict_types_id", table_name="mrv_verification_dashboard_conflict_types"
        )

    op.drop_table("mrv_verification_dashboard_conflict_types")
    if index_exists("mrv_verification_dashboard_stat_fields", "_verification_dashboard_id_stat_field_name_uc"):
        op.drop_index(
            "_verification_dashboard_id_stat_field_name_uc", table_name="mrv_verification_dashboard_stat_fields"
        )

    if index_exists("mrv_verification_dashboard_stat_fields", "ix_mrv_verification_dashboard_stat_fields_id"):
        op.drop_index(
            "ix_mrv_verification_dashboard_stat_fields_id", table_name="mrv_verification_dashboard_stat_fields"
        )

    op.drop_table("mrv_verification_dashboard_stat_fields")
    if index_exists("mrv_verification_dashboard", "ix_mrv_verification_dashboard_id"):
        op.drop_index("ix_mrv_verification_dashboard_id", table_name="mrv_verification_dashboard")

    if index_exists("mrv_verification_dashboard", "ix_mrv_verification_dashboard_program_id"):
        op.drop_index("ix_mrv_verification_dashboard_program_id", table_name="mrv_verification_dashboard")
    op.drop_table("mrv_verification_dashboard")


def downgrade():
    op.create_table(
        "mrv_verification_dashboard",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column("program_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column("reporting_year", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(["program_id"], ["mrv_programs.id"], name="mrv_verification_dashboard_ibfk_1"),
        sa.PrimaryKeyConstraint("id"),
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_verification_dashboard_program_id", "mrv_verification_dashboard", ["program_id"], unique=False
    )
    op.create_index("ix_mrv_verification_dashboard_id", "mrv_verification_dashboard", ["id"], unique=False)

    op.create_table(
        "mrv_verification_dashboard_stat_fields",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column("verification_dashboard_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column(
            "stat_field_name",
            mysql.ENUM(
                "conflicts",
                "producer_with_conflicts",
                "fields_with_conflicts",
                "area_with_conflicts",
                "est_payments_with_conflicts",
                "est_yield_impacted_by_conflicts",
            ),
            nullable=False,
        ),
        sa.Column("label", mysql.VARCHAR(length=255), nullable=True),
        sa.Column("active", mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["verification_dashboard_id"],
            ["mrv_verification_dashboard.id"],
            name="mrv_verification_dashboard_stat_fields_ibfk_1",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_verification_dashboard_stat_fields_id", "mrv_verification_dashboard_stat_fields", ["id"], unique=False
    )
    op.create_index(
        "_verification_dashboard_id_stat_field_name_uc",
        "mrv_verification_dashboard_stat_fields",
        ["verification_dashboard_id", "stat_field_name"],
        unique=False,
    )

    op.create_table(
        "mrv_verification_dashboard_conflict_types",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column("verification_dashboard_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column("conflict_type", mysql.ENUM("SUMMER_CROP", "WINTER_CROP", "TILLAGE"), nullable=False),
        sa.Column("attribute_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column("practice_year", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
        sa.Column("active", mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["attribute_id"], ["mrv_attributes.id"], name="mrv_verification_dashboard_conflict_types_ibfk_1"
        ),
        sa.ForeignKeyConstraint(
            ["verification_dashboard_id"],
            ["mrv_verification_dashboard.id"],
            name="mrv_verification_dashboard_conflict_types_ibfk_2",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_verification_dashboard_conflict_types_id",
        "mrv_verification_dashboard_conflict_types",
        ["id"],
        unique=False,
    )
    op.create_index(
        "_verification_dashboard_id_conflict_type_attribute_id_uc",
        "mrv_verification_dashboard_conflict_types",
        ["verification_dashboard_id", "conflict_type", "attribute_id"],
        unique=False,
    )

    op.create_table(
        "mrv_verification_dashboard_table_fields",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column("verification_dashboard_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column(
            "table_field_name",
            mysql.ENUM(
                "conflict_type",
                "status",
                "producer_name_and_id",
                "field_and_size",
                "producer_data",
                "regrow_data",
                "evidence_and_history",
                "baseline",
                "contract_commitment",
                "est_payments",
                "est_outcomes",
            ),
            nullable=False,
        ),
        sa.Column("is_default", mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
        sa.Column("label", mysql.VARCHAR(length=255), nullable=True),
        sa.Column("active", mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["verification_dashboard_id"],
            ["mrv_verification_dashboard.id"],
            name="mrv_verification_dashboard_table_fields_ibfk_1",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_verification_dashboard_table_fields_verification__7353",
        "mrv_verification_dashboard_table_fields",
        ["verification_dashboard_id"],
        unique=False,
    )
    op.create_index(
        "ix_mrv_verification_dashboard_table_fields_id", "mrv_verification_dashboard_table_fields", ["id"], unique=False
    )
    op.create_index(
        "_verification_dashboard_id_table_field_name_uc",
        "mrv_verification_dashboard_table_fields",
        ["verification_dashboard_id", "table_field_name"],
        unique=False,
    )
