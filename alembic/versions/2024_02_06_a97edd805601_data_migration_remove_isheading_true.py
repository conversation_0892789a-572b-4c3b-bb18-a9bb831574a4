"""data-migration remove isHeading true

Revision ID: a97edd805601
Revises: e17c53d3d0c5
Create Date: 2024-02-06 11:10:43.045958

*********************************************************
*** THIS IS A DATA MIGRATION ONLY - NO SCHEMA CHANGES ***
*********************************************************
"""

import json

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a97edd805601"
down_revision = "e17c53d3d0c5"
branch_labels = None
depends_on = None


IS_HEADING_KEY = "isHeading"
APPLICATION_PRODUCT_DEFAULTS = """
[
  {
    "label": "ammonium bicarbonate (dry) (18, 0, 0)",
    "value": "ammbic",
    "category": "fertilizer"
  },
  {
    "label": "ammonium nitrate (dry) (34, 0, 0)",
    "value": "ammnit",
    "category": "fertilizer"
  },
  {
    "label": "di-ammonium phosphate (dry) (18, 20, 0)",
    "value": "dap",
    "category": "fertilizer"
  },
  {
    "label": "tri-ammonium phosphate trihydrate (dry) (21, 15, 0)",
    "value": "tap",
    "category": "fertilizer"
  },
  {
    "label": "ammonium sulfate (dry) (21, 0, 0)",
    "value": "ammsulf",
    "category": "fertilizer"
  },
  {
    "label": "aqueous ammonia (liq) (20, 0, 0)",
    "value": "aqamm",
    "category": "fertilizer"
  },
  {
    "label": "monoammonium phosphate (dry) (11, 21, 0)",
    "value": "map",
    "category": "fertilizer"
  },
  {
    "label": "urea (dry) (46, 0, 0)",
    "value": "urea",
    "category": "fertilizer"
  },
  {
    "label": "urea-ammonium nitrate (liq) (30, 0, 0)",
    "value": "uan",
    "category": "fertilizer"
  },
  {
    "label": "anhydrous ammonia (liq) (82, 0, 0)",
    "value": "aa",
    "category": "fertilizer"
  },
  {
    "label": "calcium nitrate (dry) (16, 0, 0)",
    "value": "calnit",
    "category": "fertilizer"
  },
  {
    "label": "calcium ammonium nitrate (dry) (27, 0, 0)",
    "value": "can",
    "category": "fertilizer"
  },
  {
    "label": "potassium nitrate (dry) (13, 0, 37)",
    "value": "potnit",
    "category": "fertilizer"
  },
  {
    "label": "ammonium polyphosphate (liq) (11, 16, 0)",
    "value": "ammpolyp",
    "category": "fertilizer"
  },
  {
    "label": "NPK 28-12-12 (dry) (28, 5, 10)",
    "value": "28-12-12",
    "category": "fertilizer"
  },
  {
    "label": "urea-ammonium sulfate (dry) (38, 0, 0)",
    "value": "uas",
    "category": "fertilizer"
  },
  {
    "label": "urea-ammonium nitrate at 28% (liq) (28, 0, 0)",
    "value": "uan-28",
    "category": "fertilizer"
  },
  {
    "label": "urea-ammonium nitrate at 32% (liq) (32, 0, 0)",
    "value": "uan-32",
    "category": "fertilizer"
  },
  {
    "label": "potassium-chloride-muriate-of-potash (dry) (0, 0, 50)",
    "value": "mop",
    "category": "fertilizer"
  },
  {
    "label": "sulfonitrate (dry) (26, 0, 0)",
    "value": "sulfonitrate",
    "category": "fertilizer"
  },
  {
    "label": "MicroEssentials S10 (dry) (12, 18, 0)",
    "value": "mes 10",
    "category": "fertilizer"
  },
  {
    "label": "MicroEssentials S15 (dry) (13, 14, 0)",
    "value": "mes 15",
    "category": "fertilizer"
  },
  {
    "label": "MicroEssentials SZ (dry) (12, 18, 0)",
    "value": "mes z",
    "category": "fertilizer"
  },
  {
    "label": "Ammonium Thiosulfate (ATS) (liq) (12, 0, 0)",
    "value": "ats",
    "category": "fertilizer"
  },
  {
    "label": "Potassium Sulfate (dry) (0, 0, 44)",
    "value": "pots",
    "category": "fertilizer"
  },
  {
    "label": "Superphosphate (SSP) (dry) (0, 9, 0)",
    "value": "ssp",
    "category": "fertilizer"
  },
  {
    "label": "Triple Superphosphate (TSP) (dry) (0, 20, 0)",
    "value": "tsp",
    "category": "fertilizer"
  },
  {
    "label": "Crystal Green (dry) (5, 12, 0)",
    "value": "cg",
    "category": "fertilizer"
  },
  {
    "label": "NPK 20-0-24 (CN) (dry) (20, 0, 20)",
    "value": "20-0-24",
    "category": "fertilizer"
  },
  {
    "label": "Yaramilla COMPLEX (dry) (12, 5, 15)",
    "value": "yaramilla",
    "category": "fertilizer"
  },
  {
    "label": "Ammo 31 (dry) (31, 0, 0)",
    "value": "ammo31",
    "category": "fertilizer"
  },
  {
    "label": "Cropmaster 15 (dry) (15, 10, 10)",
    "value": "cropmaster15",
    "category": "fertilizer"
  },
  {
    "label": "Croplex 12-40-0 (+10S +Z +B) (dry) (12, 18, 0)",
    "value": "Croplex S10",
    "category": "fertilizer"
  },
  {
    "label": "TriplePro NPK 15-15-15 (dry) (15, 7, 12)",
    "value": "NPK 15-15-15",
    "category": "fertilizer"
  },
  {
    "label": "Lime (dry)",
    "value": "lime",
    "category": "fertilizer"
  },
  {
    "label": "urea-ammonium phosphate (dry) (34, 7, 0)",
    "value": "uap",
    "category": "fertilizer"
  },
  {
    "label": "ESN (dry) (44, 0, 0)",
    "value": "esn",
    "category": "eenf"
  },
  {
    "label": "SuperU (dry) (46, 0, 0)",
    "value": "superu",
    "category": "eenf"
  },
  {
    "label": "Urea Sulfur Coated (SCU) (dry) (38, 0, 0)",
    "value": "scu",
    "category": "eenf"
  },
  {
    "label": "Urea Formaldehyde or Methylene Urea (dry) (40, 0, 0)",
    "value": "uf",
    "category": "eenf"
  },
  {
    "label": "Urea Polymer Coated (PCU) (dry) (40, 0, 0)",
    "value": "pcu",
    "category": "eenf"
  },
  {
    "label": "nitamin nfusion 22N (liq) (22, 0, 0)",
    "value": "nfusion",
    "category": "eenf"
  },
  {
    "label": "Osmocote Classic 14-14-14 Controlled Release (dry) (14, 6, 12)",
    "value": "Osmocote 14-14-14",
    "category": "eenf"
  },
  {
    "label": "Entec 26 (+13S) (dry) (26, 0, 0)",
    "value": "Entec 26",
    "category": "eenf"
  },
  {
    "label": "Entec 46 with DMPP (dry) (46, 0, 0)",
    "value": "Entec 46",
    "category": "eenf"
  },
  {
    "label": "poultry_solid (dry) (2, 1, 1)",
    "value": "poultry_solid",
    "category": "organic_amendment"
  },
  {
    "label": "poultry_liquid (liq) (2, 1, 1)",
    "value": "poultry_liquid",
    "category": "organic_amendment"
  },
  {
    "label": "dairy_solid (dry) (2, 1, 1)",
    "value": "dairy_solid",
    "category": "organic_amendment"
  },
  {
    "label": "dairy_liquid (liq) (2, 1, 1)",
    "value": "dairy_liquid",
    "category": "organic_amendment"
  },
  {
    "label": "beef_solid (dry) (2, 1, 1)",
    "value": "beef_solid",
    "category": "organic_amendment"
  },
  {
    "label": "beef_liquid (liq) (2, 1, 1)",
    "value": "beef_liquid",
    "category": "organic_amendment"
  },
  {
    "label": "swine_solid (dry) (1, 0, 0)",
    "value": "swine_solid",
    "category": "organic_amendment"
  },
  {
    "label": "swine_liquid (liq) (1, 0, 0)",
    "value": "swine_liquid",
    "category": "organic_amendment"
  },
  {
    "label": "straw (dry) (0, 0, 1)",
    "value": "straw",
    "category": "organic_amendment"
  },
  {
    "label": "municipal_waste (dry) (5, 3, 0)",
    "value": "municipal_waste",
    "category": "organic_amendment"
  },
  {
    "label": "Alfalfa Meal (dry) (4, 0, 1)",
    "value": "alfalfa_meal",
    "category": "organic_amendment"
  },
  {
    "label": "Blood Meal (dry) (12, 0, 0)",
    "value": "blood_meal",
    "category": "organic_amendment"
  },
  {
    "label": "Bone Meal (dry) (2, 7, 0)",
    "value": "bone_meal",
    "category": "organic_amendment"
  },
  {
    "label": "Fish Meal/Powder (dry) (11, 3, 2)",
    "value": "fish_meal_powder",
    "category": "organic_amendment"
  },
  {
    "label": "Hay (Grass) (dry) (2, 0, 2)",
    "value": "hay_grass",
    "category": "organic_amendment"
  },
  {
    "label": "Hay (Legume) (dry) (3, 0, 2)",
    "value": "hay_legume",
    "category": "organic_amendment"
  },
  {
    "label": "Kelp (dry) (1, 0, 3)",
    "value": "kelp",
    "category": "organic_amendment"
  },
  {
    "label": "Manure Horse Solid (dry) (0, 0, 0)",
    "value": "manure_horse_solid",
    "category": "organic_amendment"
  },
  {
    "label": "Manure Sheep Slurry (dry) (1, 0, 0)",
    "value": "manure_sheep_slurry",
    "category": "organic_amendment"
  },
  {
    "label": "Manure Sheep Solid (dry) (1, 0, 0)",
    "value": "manure_sheep_solid",
    "category": "organic_amendment"
  },
  {
    "label": "Peat/Muck (dry) (2, 0, 1)",
    "value": "peat_muck",
    "category": "organic_amendment"
  },
  {
    "label": "Seabird and Bat Guano (dry) (1, 0, 0)",
    "value": "seabird_and_bat_guano",
    "category": "organic_amendment"
  },
  {
    "label": "Seaweed Ground (dry) (1, 0, 0)",
    "value": "seaweed_ground",
    "category": "organic_amendment"
  },
  {
    "label": "Soybean Meal (dry) (7, 1, 1)",
    "value": "soybean_meal",
    "category": "organic_amendment"
  },
  {
    "label": "Manure Horse Liquid (liq)",
    "value": "manure_horse_liquid",
    "category": "organic_amendment"
  },
  {
    "label": "manure_compost (dry) (2, 0, 1)",
    "value": "manure_compost",
    "category": "organic_amendment"
  }
]
"""


def filter_options(options: list) -> tuple[bool, list]:
    """
    Return a copy of `options` list but with objects containing `isHeading == True` filtered out
    """
    new_options = []
    changed = False
    for option in options:
        if isinstance(option, dict) and option.get(IS_HEADING_KEY):
            changed = True
        else:
            new_options.append(option)
    return changed, new_options


def save_new_options(connection: sa.Connection, row_id: int, new_options: list):
    """
    Update the options for a particular mrv_attributes row
    """
    update = sa.sql.text(
        """
update mrv_attributes
set options = :new_options
where id = :row_id
    """
    )
    connection.execute(update, {"row_id": row_id, "new_options": json.dumps(new_options)})


def add_default_application_products(connection: sa.Connection):
    """
    Add a default list of values for type application_product to mrv_attribute_options_defaults
    """
    query = sa.sql.text(
        """
select count(*)
from mrv_attribute_options_defaults
where type = 'application_product'
    """
    )
    count = connection.execute(query).fetchone()[0]
    if count == 0:
        insert = sa.sql.text(
            """
insert into mrv_attribute_options_defaults (type, options)
values ('application_product', :defaults)
        """
        )
        connection.execute(insert, {"defaults": APPLICATION_PRODUCT_DEFAULTS})


def remove_heading_options(connection: sa.Connection):
    """
    Remove all `isHeading==True` objects from __all__ mrv_attributes options
    """
    query = sa.sql.text(
        """
SELECT id,
       options
FROM mrv_attributes
WHERE options IS NOT NULL
      AND options != CAST('[]' AS JSON)
      AND options != CAST('null' AS JSON)
"""
    )
    results = connection.execute(query).fetchall()
    for row_id, options_str in results:
        old_options = json.loads(options_str)
        options_changed, new_options = filter_options(old_options)
        if options_changed:
            save_new_options(connection, row_id, new_options)


def upgrade():
    try:
        connection = op.get_bind()
        add_default_application_products(connection)
        remove_heading_options(connection)
    except Exception as e:
        if "NoneType" not in str(e):
            raise e


def downgrade():
    """
    This migration is non-reversible - downgrade does nothing.

    Upgrade deletes data, and there's no way to recover it.
    """
