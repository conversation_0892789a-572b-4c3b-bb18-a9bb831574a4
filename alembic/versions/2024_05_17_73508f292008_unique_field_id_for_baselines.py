"""unique field id for baselines

Revision ID: 73508f292008
Revises: ba04a2dccede
Create Date: 2024-05-17 08:19:33.708769

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "73508f292008"
down_revision = "ba04a2dccede"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint("_field_baseline_field_id_uc", "mrv_fields_baseline", ["field_id"])
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("_field_baseline_field_id_uc", "mrv_fields_baseline", type_="unique")
    # ### end Alembic commands ###
