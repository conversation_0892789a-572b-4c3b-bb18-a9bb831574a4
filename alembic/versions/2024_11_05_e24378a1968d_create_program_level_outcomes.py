"""create program level outcomes

Revision ID: e24378a1968d
Revises: e997b752eb38
Create Date: 2024-11-05 10:51:58.476986

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e24378a1968d"
down_revision = "e997b752eb38"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_program_level_outcomes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("start_date", sa.Date(), nullable=True),
        sa.Column("end_date", sa.Date(), nullable=True),
        sa.Column("number_of_fields", sa.Integer(), nullable=True),
        sa.Column("number_of_acres", sa.Float(), nullable=True),
        sa.Column("total_reversible_credit", sa.Float(), nullable=True),
        sa.Column("total_non_reversible_credit", sa.Float(), nullable=True),
        sa.Column("total_credit", sa.Float(), nullable=True),
        sa.Column("reversible_credit_mean", sa.Float(), nullable=True),
        sa.Column("reversible_credit_standard_deviation", sa.Float(), nullable=True),
        sa.Column("total_reversible_emissions_reductions", sa.Float(), nullable=True),
        sa.Column("non_reversible_credit_mean", sa.Float(), nullable=True),
        sa.Column("non_reversible_credit_standard_deviation", sa.Float(), nullable=True),
        sa.Column("total_non_reversible_emissions_reductions", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=True),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_program_level_outcomes_id"), "mrv_program_level_outcomes", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_program_level_outcomes_program_id"), "mrv_program_level_outcomes", ["program_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_program_level_outcomes_updated_at"), "mrv_program_level_outcomes", ["updated_at"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_program_level_outcomes_updated_at"), table_name="mrv_program_level_outcomes")
    op.drop_index(op.f("ix_mrv_program_level_outcomes_program_id"), table_name="mrv_program_level_outcomes")
    op.drop_index(op.f("ix_mrv_program_level_outcomes_id"), table_name="mrv_program_level_outcomes")
    op.drop_table("mrv_program_level_outcomes")
