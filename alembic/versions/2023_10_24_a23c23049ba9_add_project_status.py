"""add-project-status

Revision ID: a23c23049ba9
Revises: 2e85f90ae2bf
Create Date: 2023-10-24 18:04:59.467773

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a23c23049ba9"
down_revision = "2e85f90ae2bf"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_projects",
        sa.Column(
            "status",
            sa.Enum("created", "contract_pending", "enrolled", "contract_removed", "deleted", name="projectstatus"),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column("mrv_projects", "status")
