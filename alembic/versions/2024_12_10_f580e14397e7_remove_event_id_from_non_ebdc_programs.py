"""Remove event id from non ebdc programs

Revision ID: f580e14397e7
Revises: 1dfdbdb893e3
Create Date: 2024-12-10 09:49:53.501779

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f580e14397e7"
down_revision = "1dfdbdb893e3"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_values mv
        JOIN mrv_fields mf ON mv.field_id = mf.id
        JOIN mrv_projects mp ON mf.parent_project_id = mp.id
        SET mv.event_id = NULL
        WHERE mp.program_id IN (155, 1119, 1126);"""
    )


def downgrade():
    pass
