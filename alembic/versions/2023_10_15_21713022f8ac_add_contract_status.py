"""add-contract-status

Revision ID: 21713022f8ac
Revises: 21b6b80975cf
Create Date: 2023-10-15 23:46:50.150705

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "21713022f8ac"
down_revision = "21b6b80975cf"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_projects", sa.Column("contract_status", mysql.ENUM("generated", "signed", "voided"), nullable=True)
    )
    op.add_column(
        "mrv_project_contracts", sa.Column("status", mysql.ENUM("generated", "signed", "voided"), nullable=True)
    )


def downgrade():
    op.drop_column("mrv_projects", "contract_status")
    op.drop_column("mrv_project_contracts", "status")
