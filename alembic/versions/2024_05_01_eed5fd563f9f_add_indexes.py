"""Add indexes

Revision ID: eed5fd563f9f
Revises: f90f9bb798ff
Create Date: 2024-05-01 23:15:52.750487

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "eed5fd563f9f"
down_revision = "f90f9bb798ff"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_mrv_projects_reporting_enabled"), "mrv_projects", ["reporting_enabled"], unique=False)
    op.create_index(op.f("ix_mrv_projects_status"), "mrv_projects", ["status"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_projects_status"), table_name="mrv_projects")
    op.drop_index(op.f("ix_mrv_projects_reporting_enabled"), table_name="mrv_projects")
    # ### end Alembic commands ###
