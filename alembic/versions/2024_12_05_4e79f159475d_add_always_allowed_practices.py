"""add-always-allowed-practices

Revision ID: 4e79f159475d
Revises: 87f9837b2871
Create Date: 2024-12-05 18:55:39.493531

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "4e79f159475d"
down_revision = "87f9837b2871"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_program_practice_change", sa.Column("always_eligible", sa.Boolean(), nullable=False))
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
        ),
        type_=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
            name="eligibilitytypes",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_stages",
        "eligibility_method",
        existing_type=sa.Enum(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
            "PARAMETERISED_ELIGIBILITY",
            name="eligibilitytypes",
        ),
        type_=mysql.ENUM(
            "CARGILL_GRAIN_2022",
            "CARGILL_COTTON_2022",
            "ELIGIBILITY_ALWAYS_TRUE",
            "CARGILL_EU_2022_UK",
            "CARGILL_EU_2022_FR",
            "CARGILL_GRAIN_2023",
            "CARGILL_GRAIN_2024",
            "CARGILL_COTTON_2023",
            "CARGILL_EU_2023",
            "CARGILL_EU_2024",
            "CARGILL_COTTON_2024",
            "CUSTOM",
        ),
        existing_nullable=True,
    )
    op.drop_column("mrv_program_practice_change", "always_eligible")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
