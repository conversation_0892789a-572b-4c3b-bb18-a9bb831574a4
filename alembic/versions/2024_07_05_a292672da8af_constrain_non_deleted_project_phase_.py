"""Constrain non-deleted project/phase completions to be unique

Revision ID: a292672da8af
Revises: a7259e7f52d2
Create Date: 2024-07-05 10:57:36.423123

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a292672da8af"
down_revision = "a7259e7f52d2"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "unique_project_phase",
        "mrv_project_phase_completion",
        ["project_id", "phase_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )
    op.create_table_comment(
        "mrv_project_phase_completion",
        "\n            Phase completion (finalization) is tracked for each Project. This table is used to track the completion\n            (finalized) status of each Phase. Phase data becomes non-editable once a Phase is finalized.\n            User will finalize Phase on signing contract or confirming completion with the Finish stage\n            ",
        existing_comment="Phase completion (finalization) is tracked for each Project. This table is used to track the completion (finalized) status of each Phase. Phase data becomes non-editable once a Phase is finalized. User will finalize Phase on signing contract or confirming completion with the Finish stage",
        schema=None,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table_comment(
        "mrv_project_phase_completion",
        "Phase completion (finalization) is tracked for each Project. This table is used to track the completion (finalized) status of each Phase. Phase data becomes non-editable once a Phase is finalized. User will finalize Phase on signing contract or confirming completion with the Finish stage",
        existing_comment="\n            Phase completion (finalization) is tracked for each Project. This table is used to track the completion\n            (finalized) status of each Phase. Phase data becomes non-editable once a Phase is finalized.\n            User will finalize Phase on signing contract or confirming completion with the Finish stage\n            ",
        schema=None,
    )
    op.drop_index("unique_project_phase", table_name="mrv_project_phase_completion")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
