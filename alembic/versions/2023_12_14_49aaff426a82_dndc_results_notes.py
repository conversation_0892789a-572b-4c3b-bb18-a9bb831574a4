"""DNDC results notes

Revision ID: 49aaff426a82
Revises: dfa07419340d
Create Date: 2023-12-14 13:12:51.150614

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "49aaff426a82"
down_revision = "dfa07419340d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_dndc_results",
        sa.Column("notes", sa.JSON(), nullable=True, comment="Various data generated during integration run"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_dndc_results", "notes")
    # ### end Alembic commands ###
