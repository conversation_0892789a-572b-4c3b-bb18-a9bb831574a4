"""add groups projects relation table

Revision ID: 08e94cc51ab5
Revises: 16bf0aec2a5a
Create Date: 2024-06-03 14:48:11.215794

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "08e94cc51ab5"
down_revision = "16bf0aec2a5a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_group_projects",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("group_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIM<PERSON>TA<PERSON>(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["group_id"],
            ["mrv_user_groups.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("group_id", "project_id", name="group_project_id"),
    )
    op.create_index(op.f("ix_mrv_group_projects_deleted_at"), "mrv_group_projects", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_group_projects_group_id"), "mrv_group_projects", ["group_id"], unique=False)
    op.create_index(op.f("ix_mrv_group_projects_id"), "mrv_group_projects", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_group_projects_project_id"), "mrv_group_projects", ["project_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_group_projects_project_id"), table_name="mrv_group_projects")
    op.drop_index(op.f("ix_mrv_group_projects_id"), table_name="mrv_group_projects")
    op.drop_index(op.f("ix_mrv_group_projects_group_id"), table_name="mrv_group_projects")
    op.drop_index(op.f("ix_mrv_group_projects_deleted_at"), table_name="mrv_group_projects")
    op.drop_table("mrv_group_projects")
    # ### end Alembic commands ###
