"""Add unique index to user groups

Revision ID: d314013c176c
Revises: 6ab64b6dc5cb
Create Date: 2025-03-13 11:26:44.476954

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d314013c176c"
down_revision = "6ab64b6dc5cb"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_groups_admins mga
        SET mga.group_id = CASE
            WHEN mga.group_id = 69 THEN 394
            WHEN mga.group_id = 133 THEN 134
            ELSE mga.group_id
        END
        WHERE mga.group_id IN (69, 133);"""
    )
    op.execute(
        """UPDATE mrv_group_projects mgp
        SET mgp.group_id = CASE
            WHEN mgp.group_id = 69 THEN 394
            WHEN mgp.group_id = 133 THEN 134
            WHEN mgp.group_id = 400 THEN 404
            WHEN mgp.group_id = 401 THEN 402
            ELSE mgp.group_id
        END
        WHERE mgp.group_id IN (69, 133, 400, 401);"""
    )
    op.execute(
        """UPDATE mrv_user_groups mug
        SET mug.deleted_at = NOW()
        WHERE mug.id IN (69, 133, 355, 400, 401);"""
    )
    op.create_index(
        index_name="unique_undeleted_program_id_name",
        table_name="mrv_user_groups",
        columns=["program_id", "name", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )


def downgrade():
    op.drop_index(index_name="unique_undeleted_program_id_name", table_name="mrv_user_groups")
