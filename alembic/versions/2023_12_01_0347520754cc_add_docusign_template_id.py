"""add docusign template id

Revision ID: 0347520754cc
Revises: e73b5d59117b
Create Date: 2023-12-01 13:37:43.574801

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "0347520754cc"
down_revision = "e73b5d59117b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_contract_template", sa.Column("docusign_template_id", sa.String(length=255), nullable=True))


def downgrade():
    op.drop_column("mrv_contract_template", "docusign_template_id")
