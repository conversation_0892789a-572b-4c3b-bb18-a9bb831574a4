"""Add mrv_raw_field_events

Revision ID: 87fcec7e9194
Revises: 8fc83ab2906f
Create Date: 2024-04-04 15:51:12.265558

"""

import sqlalchemy as sa
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "87fcec7e9194"
down_revision = "8fc83ab2906f"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.UPSERT_ENTITY_EVENTS.value,
    },
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_raw_field_events",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "event_type",
            sa.Enum(
                "APPLICATION_EVENT",
                "CROPPING_EVENT",
                "GRAZING_EVENT",
                "IRRIGATION_EVENT",
                "TILLAGE_EVENT",
                name="entityeventtype",
            ),
            nullable=False,
        ),
        sa.Column("event_body", sa.JSON(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("row_id", sa.Integer(), nullable=False),
        sa.Column(
            "phase_type",
            sa.Enum(
                "ENROLMENT", "MONITORING", "QA_QC", "SOIL_SAMPLING", "DASHBOARDS", "VERIFICATION", name="phasetypes"
            ),
            nullable=False,
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("field_id", "row_id", "event_type", name="_field_id_row_id_event_type_uc"),
    )
    op.create_index(op.f("ix_mrv_raw_field_events_id"), "mrv_raw_field_events", ["id"], unique=False)
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("mrv_raw_field_events")
    # ### end Alembic commands ###

    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
