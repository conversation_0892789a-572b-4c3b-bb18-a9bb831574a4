"""Update program 25X field is returning

Revision ID: e2b9344e5a3c
Revises: d598c5b727a9
Create Date: 2025-01-28 16:04:26.285076

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "e2b9344e5a3c"
down_revision = "d598c5b727a9"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_fields_baseline mfb
        SET mfb.is_returning = 0
        WHERE mfb.field_id IN (81596, 81906, 81919, 82023, 82342, 84871, 85580, 85715, 177667, 177668, 177812, 177813);"""
    )


def downgrade():
    pass
