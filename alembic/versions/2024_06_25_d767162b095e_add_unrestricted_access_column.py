"""add-unrestricted-access-column

Revision ID: d767162b095e
Revises: 9c0a88a86229
Create Date: 2024-06-25 14:44:13.850707

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "d767162b095e"
down_revision = "9c0a88a86229"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_groups_admins", sa.Column("deleted_at_unix", sa.Integer(), nullable=True))
    op.add_column("mrv_groups_admins", sa.Column("program_id", sa.Integer(), nullable=False))
    op.add_column("mrv_groups_admins", sa.Column("unrestricted_access", sa.Boolean(), nullable=False))
    op.alter_column("mrv_groups_admins", "group_id", existing_type=mysql.INTEGER(), nullable=True)
    op.create_index(op.f("ix_mrv_groups_admins_program_id"), "mrv_groups_admins", ["program_id"], unique=False)
    op.create_foreign_key(None, "mrv_groups_admins", "mrv_programs", ["program_id"], ["id"])
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_groups_admins", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_groups_admins_program_id"), table_name="mrv_groups_admins")
    op.alter_column("mrv_groups_admins", "group_id", existing_type=mysql.INTEGER(), nullable=False)
    op.drop_column("mrv_groups_admins", "unrestricted_access")
    op.drop_column("mrv_groups_admins", "program_id")
    op.drop_column("mrv_groups_admins", "deleted_at_unix")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
