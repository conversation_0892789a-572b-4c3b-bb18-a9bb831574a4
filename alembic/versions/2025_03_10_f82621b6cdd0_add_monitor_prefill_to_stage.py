"""add monitor prefill to stage

Revision ID: f82621b6cdd0
Revises: 95b0561c14ee
Create Date: 2025-03-10 15:14:55.802132

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "f82621b6cdd0"
down_revision = "95b0561c14ee"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_stages", sa.Column("monitor_prefill_enabled", sa.<PERSON>(), server_default="0", nullable=False))
    op.add_column("mrv_stages", sa.Column("monitor_prefill_last_run", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_stages", "monitor_prefill_last_run")
    op.drop_column("mrv_stages", "monitor_prefill_enabled")
    # ### end Alembic commands ###
