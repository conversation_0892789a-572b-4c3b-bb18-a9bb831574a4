"""add new permissions for field values by project(s) overwrite
Revision ID: a2b359b7b5f7
Revises: 8edcf3b114f4
Create Date: 2024-07-22 17:51:51.115032
"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a2b359b7b5f7"
down_revision = "8edcf3b114f4"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.OVERWRITE_MULTIPLE_PROJECTS_FIELD_VALUES.value,
        Permission.OVERWRITE_SINGLE_PROJECT_FIELD_VALUES.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.OVERWRITE_MULTIPLE_PROJECTS_FIELD_VALUES.value,
        Permission.OVERWRITE_SINGLE_PROJECT_FIELD_VALUES.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.OVERWRITE_MULTIPLE_PROJECTS_FIELD_VALUES.value,
        Permission.OVERWRITE_SINGLE_PROJECT_FIELD_VALUES.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.OVERWRITE_SINGLE_PROJECT_FIELD_VALUES.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
