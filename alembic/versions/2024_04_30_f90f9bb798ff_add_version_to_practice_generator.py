"""add version to practice generator

Revision ID: f90f9bb798ff
Revises: c4d9fb968c61
Create Date: 2024-04-30 22:44:44.578244

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f90f9bb798ff"
down_revision = "c4d9fb968c61"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_cover_crop_eligibility_config",
        sa.Column(
            "version",
            sa.Enum("COVER_CROP_GENERATOR_V1", "COVER_CROP_GENERATOR_V2", name="covercropgeneratortype"),
            nullable=False,
        ),
    )
    op.add_column("mrv_cover_crop_eligibility_config", sa.Column("description", sa.Text(), nullable=True))
    op.add_column(
        "mrv_tillage_eligibility_config",
        sa.Column(
            "version",
            sa.Enum("TILLAGE_GENERATOR_V1", "TILLAGE_GENERATOR_V2", name="tillagegeneratortype"),
            nullable=False,
        ),
    )
    op.add_column("mrv_tillage_eligibility_config", sa.Column("description", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_tillage_eligibility_config", "description")
    op.drop_column("mrv_tillage_eligibility_config", "version")
    op.drop_column("mrv_cover_crop_eligibility_config", "description")
    op.drop_column("mrv_cover_crop_eligibility_config", "version")
    # ### end Alembic commands ###
