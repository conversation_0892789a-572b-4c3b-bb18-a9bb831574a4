"""Populate mrv_dndc_lookup_table for EU 2025

Revision ID: 6b7f502b1289
Revises: 8f0d588d5b7a
Create Date: 2025-05-08 15:15:14.590260

"""

from sqlalchemy.orm import Session

from alembic import op
from projects.eligibility.helper import initialise_cargill_eu_lut

# revision identifiers, used by Alembic.
revision = "6b7f502b1289"
down_revision = "8f0d588d5b7a"
branch_labels = None
depends_on = None


def upgrade():
    session = Session(bind=op.get_bind())
    initialise_cargill_eu_lut(session=session, year=2025)


def downgrade():
    # no previous versioning
    pass
