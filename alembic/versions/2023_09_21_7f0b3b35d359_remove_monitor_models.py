"""remove monitor models

Revision ID: 7f0b3b35d359
Revises: 34c4d0f492e1
Create Date: 2023-09-21 13:38:15.303784

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "7f0b3b35d359"
down_revision = "34c4d0f492e1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "mrv_monitor_request_store_jobs_ibfk_1",
        "mrv_monitor_request_store_jobs",
        type_="foreignkey",
    )
    op.drop_index(
        "ix_mrv_monitor_request_store_jobs_request_store_id",
        table_name="mrv_monitor_request_store_jobs",
    )
    op.drop_index(
        "ix_mrv_monitor_request_store_jobs_year",
        table_name="mrv_monitor_request_store_jobs",
    )
    op.drop_constraint(
        "mrv_monitor_response_store_ibfk_2",
        "mrv_monitor_response_store",
        type_="foreignkey",
    )
    op.drop_index(
        "ix_mrv_monitor_response_store_request_store_job_id",
        table_name="mrv_monitor_response_store",
    )
    op.drop_table("mrv_monitor_request_store_jobs")
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column("job_id", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "payload",
            sa.JSON(),
            nullable=True,
            comment="Stores payload of request to recreate monitor api calls via cron",
        ),
    )
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "retry_count",
            sa.Integer(),
            nullable=False,
            comment="Number of times request is retried",
        ),
    )
    op.alter_column(
        "mrv_monitor_request_store",
        "status",
        existing_type=mysql.ENUM("PENDING", "SUCCESS", "FAILED", "PARTIALLY_FAILED", "CANCELLED"),
        type_=sa.Enum(
            "PENDING",
            "SUCCESS",
            "FAILED",
            "PARTIALLY_FAILED",
            "CANCELLED",
            "WAITING_FOR_CALLBACK",
            name="requeststorestatus",
        ),
        existing_comment="Status of request. We keep single request in pending/completed/failed/partially_failed status, rest will be in cancelled state",
        existing_nullable=False,
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_job_id"),
        "mrv_monitor_request_store",
        ["job_id"],
        unique=False,
    )
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column("request_store_id", sa.Integer(), nullable=False),
    )
    op.alter_column(
        "mrv_monitor_response_store",
        "response",
        existing_type=mysql.JSON(),
        comment="Stores only row crop data from monitor api response per field",
        existing_comment="Stores only row crop data from monitor api response per field per year",
        existing_nullable=True,
    )
    op.create_index(
        op.f("ix_mrv_monitor_response_store_request_store_id"),
        "mrv_monitor_response_store",
        ["request_store_id"],
        unique=False,
    )

    op.create_foreign_key(
        None,
        "mrv_monitor_response_store",
        "mrv_monitor_request_store",
        ["request_store_id"],
        ["id"],
    )
    op.drop_column("mrv_monitor_response_store", "request_store_job_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_monitor_response_store",
        sa.Column(
            "request_store_job_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_constraint(None, "mrv_monitor_response_store", type_="foreignkey")
    op.create_foreign_key(
        "mrv_monitor_response_store_ibfk_2",
        "mrv_monitor_response_store",
        "mrv_monitor_request_store_jobs",
        ["request_store_job_id"],
        ["id"],
    )
    op.drop_index(
        op.f("ix_mrv_monitor_response_store_request_store_id"),
        table_name="mrv_monitor_response_store",
    )
    op.create_index(
        "ix_mrv_monitor_response_store_request_store_job_id",
        "mrv_monitor_response_store",
        ["request_store_job_id"],
        unique=False,
    )
    op.alter_column(
        "mrv_monitor_response_store",
        "response",
        existing_type=mysql.JSON(),
        comment="Stores only row crop data from monitor api response per field per year",
        existing_comment="Stores only row crop data from monitor api response per field",
        existing_nullable=True,
    )
    op.drop_column("mrv_monitor_response_store", "request_store_id")
    op.drop_index(
        op.f("ix_mrv_monitor_request_store_job_id"),
        table_name="mrv_monitor_request_store",
    )
    op.alter_column(
        "mrv_monitor_request_store",
        "status",
        existing_type=sa.Enum(
            "PENDING",
            "SUCCESS",
            "FAILED",
            "PARTIALLY_FAILED",
            "CANCELLED",
            "WAITING_FOR_CALLBACK",
            name="requeststorestatus",
        ),
        type_=mysql.ENUM("PENDING", "SUCCESS", "FAILED", "PARTIALLY_FAILED", "CANCELLED"),
        existing_comment="Status of request. We keep single request in pending/completed/failed/partially_failed status, rest will be in cancelled state",
        existing_nullable=False,
    )
    op.drop_column("mrv_monitor_request_store", "retry_count")
    op.drop_column("mrv_monitor_request_store", "payload")
    op.drop_column("mrv_monitor_request_store", "job_id")
    op.create_table(
        "mrv_monitor_request_store_jobs",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column(
            "request_store_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("job_id", mysql.VARCHAR(length=255), nullable=True),
        sa.Column("year", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column(
            "payload",
            mysql.JSON(),
            nullable=True,
            comment="Stores payload of request to recreate monitor api calls via cron",
        ),
        sa.Column(
            "status",
            mysql.ENUM("PENDING", "SUCCESS", "FAILED", "PARTIALLY_FAILED", "CANCELLED"),
            nullable=False,
            comment="Status of request. We keep single request in pending/completed/failed/partially_failed status per project per year, rest will be in cancelled/failed state",
        ),
        sa.Column(
            "retry_count",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
            comment="Number of times request is retried",
        ),
        sa.Column(
            "created_at",
            mysql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            mysql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["request_store_id"],
            ["mrv_monitor_request_store.id"],
            name="mrv_monitor_request_store_jobs_ibfk_1",
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Store job id and status for request store per year",
        mysql_comment="Store job id and status for request store per year",
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_monitor_request_store_jobs_year",
        "mrv_monitor_request_store_jobs",
        ["year"],
        unique=False,
    )
    op.create_index(
        "ix_mrv_monitor_request_store_jobs_request_store_id",
        "mrv_monitor_request_store_jobs",
        ["request_store_id"],
        unique=False,
    )
    # ### end Alembic commands ###
