"""Add index to created_at

Revision ID: d699cb2f903f
Revises: 4efa57026eb8
Create Date: 2024-04-24 08:23:48.436860

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d699cb2f903f"
down_revision = "d5d9af1a3c08"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_mrv_programs_created_at"), "mrv_programs", ["created_at"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_programs_created_at"), table_name="mrv_programs")
    # ### end Alembic commands ###
