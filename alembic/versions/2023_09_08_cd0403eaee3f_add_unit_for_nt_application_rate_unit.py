"""Add unit for NT application rate unit (tons per acre)

Revision ID: cd0403eaee3f
Revises: efa40b22eafd
Create Date: 2023-08-31 12:08:58.064837

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "cd0403eaee3f"
down_revision = "efa40b22eafd"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=mysql.ENUM("KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "KG_SQM", "MT_SQM", "L_SQM"),
        type_=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "LBS_AC",
            "GAL_AC",
            "TN_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "LBS_AC",
            "GAL_AC",
            "TN_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        type_=mysql.ENUM("KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "KG_SQM", "MT_SQM", "L_SQM"),
        existing_nullable=True,
    )
