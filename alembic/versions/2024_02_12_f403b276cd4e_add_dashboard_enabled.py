"""Add dashboard_enabled

Revision ID: f403b276cd4e
Revises: c42c1ff3ff36
Create Date: 2024-02-12 17:05:35.577644

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f403b276cd4e"
down_revision = "c42c1ff3ff36"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_programs", sa.Column("dashboard_enabled", sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_programs", "dashboard_enabled")
    # ### end Alembic commands ###
