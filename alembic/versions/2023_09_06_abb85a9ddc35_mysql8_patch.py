"""mysql8 updates

Revision ID: abb85a9ddc35
Revises: def407304457
Create Date: 2023-09-06 19:51:33.216260

"""

import sqlalchemy as sa
from sqlalchemy import inspect

from alembic import op

# revision identifiers, used by Alembic.
revision = "abb85a9ddc35"
down_revision = "def407304457"
branch_labels = None
depends_on = None


BOUNDARY_TABLE = "mrv_boundary_rule_deviation"
INDEX_NAME = "ix_mrv_boundary_rule_deviation_field_check_error"


def index_exists(table_name: str, index_name: str) -> bool:
    try:
        bind = op.get_context().bind
        insp = inspect(bind)
        indices = insp.get_columns(table_name)
        return any(i["name"] == index_name for i in indices)
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def upgrade():
    if index_exists(BOUNDARY_TABLE, INDEX_NAME):
        op.drop_index(op.f(INDEX_NAME), table_name=BOUNDARY_TABLE)

    op.alter_column(
        BOUNDARY_TABLE,
        "field_check_error",
        existing_type=sa.String(length=1024),
        type_=sa.String(length=255),
        existing_nullable=False,
        existing_comment="If not empty, then the field boundary check API returned an error when it checked this field under this rule. In this case, percent_overlap will be set to 0 and threshold_crossed to false, even though these values were never determined (because of the error)",
    )
    op.create_index(
        op.f(INDEX_NAME),
        BOUNDARY_TABLE,
        ["field_check_error"],
        unique=False,
    )


def downgrade():
    if index_exists(BOUNDARY_TABLE, INDEX_NAME):
        op.drop_index(op.f(INDEX_NAME), table_name=BOUNDARY_TABLE)

    op.alter_column(
        BOUNDARY_TABLE,
        "field_check_error",
        existing_type=sa.String(length=255),
        type_=sa.String(length=1024),
        existing_nullable=False,
        existing_comment="If not empty, then the field boundary check API returned an error when it checked this field under this rule. In this case, percent_overlap will be set to 0 and threshold_crossed to false, even though these values were never determined (because of the error)",
    )
    op.create_index(
        op.f(INDEX_NAME),
        BOUNDARY_TABLE,
        ["field_check_error"],
        unique=False,
    )
