"""add permissions for attrs validation

Revision ID: 971cfb7378f2
Revises: 4efa57026eb8
Create Date: 2024-04-23 12:54:52.106747

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "971cfb7378f2"
down_revision = "4efa57026eb8"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
