"""add cycle counts to field stage completion

Revision ID: c2a22a6c81d7
Revises: 86539d09dc73
Create Date: 2025-04-11 09:26:56.638310

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c2a22a6c81d7"
down_revision = "86539d09dc73"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_field_stage_completion", sa.Column("completed_cycles", sa.Integer(), nullable=True))
    op.add_column("mrv_field_stage_completion", sa.Column("total_cycles", sa.Integer(), nullable=True))


def downgrade():
    op.drop_column("mrv_field_stage_completion", "total_cycles")
    op.drop_column("mrv_field_stage_completion", "completed_cycles")
