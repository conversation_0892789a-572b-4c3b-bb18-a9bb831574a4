"""add-field-event-permissions

Revision ID: 8f0d588d5b7a
Revises: 18ecb2c2cb29
Create Date: 2025-05-05 19:09:37.383803

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "8f0d588d5b7a"
down_revision = "18ecb2c2cb29"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.CREATE_PROJECT_FIELD_EVENTS.value,
        Permission.UPDATE_PROJECT_FIELD_EVENTS.value,
        Permission.DELETE_PROJECT_FIELD_EVENTS.value,
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.CREATE_PROJECT_FIELD_EVENTS.value,
        Permission.UPDATE_PROJECT_FIELD_EVENTS.value,
        Permission.DELETE_PROJECT_FIELD_EVENTS.value,
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.CREATE_PROJECT_FIELD_EVENTS.value,
        Permission.UPDATE_PROJECT_FIELD_EVENTS.value,
        Permission.DELETE_PROJECT_FIELD_EVENTS.value,
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.CREATE_PROJECT_FIELD_EVENTS.value,
        Permission.UPDATE_PROJECT_FIELD_EVENTS.value,
        Permission.DELETE_PROJECT_FIELD_EVENTS.value,
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
