"""Update 100 harvested to all residue harvested

Revision ID: 40ae21285f3b
Revises: 00b72efd94e3
Create Date: 2024-11-04 12:11:00.205738

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "40ae21285f3b"
down_revision = "00b72efd94e3"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_values mv
        SET mv.value = 'All residue harvested'
        WHERE mv.value = '100% Harvested'"""
    )


def downgrade():
    pass
