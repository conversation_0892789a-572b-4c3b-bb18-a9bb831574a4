"""Add disabled to mrv_reporting_dashboards

Revision ID: 372c25dfcdbf
Revises: 971cfb7378f2
Create Date: 2024-04-24 13:52:19.272232

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "372c25dfcdbf"
down_revision = "971cfb7378f2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_dashboards", sa.Column("disabled", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards", "disabled")
    # ### end Alembic commands ###
