"""update dndc_version length

Revision ID: 0915380adb8b
Revises: 6256fd11c2dd
Create Date: 2025-07-17 12:53:25.504244

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "0915380adb8b"
down_revision = "6256fd11c2dd"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_dndc_tasks",
        "dndc_version",
        existing_type=mysql.VARCHAR(length=10),
        type_=sa.String(length=255),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_tasks",
        "dndc_version",
        existing_type=sa.String(length=255),
        type_=mysql.VARCHAR(length=10),
        existing_nullable=True,
    )
