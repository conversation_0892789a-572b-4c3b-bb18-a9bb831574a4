"""Add mrv_cached_nass_responses for caching NASS data for program 155.

Revision ID: 8edcf3b114f4
Revises: 47b57b153c4c
Create Date: 2024-07-10 14:42:37.322562

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8edcf3b114f4"
down_revision = "b2ac4b6d7343"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_cached_nass_responses",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("lat", sa.DECIMAL(precision=20, scale=16), nullable=False),
        sa.Column("lon", sa.DECIMAL(precision=20, scale=16), nullable=False),
        sa.Column("year", sa.Integer(), nullable=False),
        sa.Column("crop_name", sa.String(length=50), nullable=False),
        sa.Column("state", sa.String(length=50), nullable=False),
        sa.Column("years_to_average", sa.Integer(), nullable=False),
        sa.Column("nass_result", sa.JSON(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(["field_id"], ["mrv_fields.id"], name="mrv_cached_nass_responses_ibfk_1"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_cached_nass_responses_lookup1"),
        "mrv_cached_nass_responses",
        ["field_id", "year", "crop_name", "years_to_average"],
        unique=True,
    )
    op.create_index(
        op.f("ix_mrv_cached_nass_responses_lookup2"),
        "mrv_cached_nass_responses",
        ["lat", "lon", "year", "crop_name", "years_to_average"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_cached_nass_responses_field_id"), "mrv_cached_nass_responses", ["field_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_cached_nass_responses_id"), "mrv_cached_nass_responses", ["id"], unique=False)


def downgrade():
    op.drop_constraint("mrv_cached_nass_responses_ibfk_1", table_name="mrv_cached_nass_responses", type_="foreignkey")
    op.drop_index("ix_mrv_cached_nass_responses_lookup2", table_name="mrv_cached_nass_responses")
    op.drop_index("ix_mrv_cached_nass_responses_lookup1", table_name="mrv_cached_nass_responses")
    op.drop_index("ix_mrv_cached_nass_responses_id", table_name="mrv_cached_nass_responses")
    op.drop_index("ix_mrv_cached_nass_responses_field_id", table_name="mrv_cached_nass_responses")
    op.drop_table("mrv_cached_nass_responses")
