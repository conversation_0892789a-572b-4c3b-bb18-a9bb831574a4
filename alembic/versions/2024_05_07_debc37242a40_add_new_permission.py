"""add new permission

Revision ID: debc37242a40
Revises: 6193d0a6e173
Create Date: 2024-05-07 16:25:40.537137

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "debc37242a40"
down_revision = "6193d0a6e173"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}
#
roles_to_permissions_mapping = {
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
