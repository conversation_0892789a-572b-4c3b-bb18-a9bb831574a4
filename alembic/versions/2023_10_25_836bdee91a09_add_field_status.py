"""add-field-status

Revision ID: 836bdee91a09
Revises: a23c23049ba9
Create Date: 2023-10-25 10:54:29.489962

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "836bdee91a09"
down_revision = "a23c23049ba9"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_fields",
        sa.Column(
            "status",
            sa.Enum("registered", "enrolled", "deleted", "field_removed", "contract_voided", name="fieldstatus"),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column("mrv_fields", "status")
