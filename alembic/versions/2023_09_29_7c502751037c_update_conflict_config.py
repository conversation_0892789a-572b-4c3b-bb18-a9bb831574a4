"""update conflict config

Revision ID: 7c502751037c
Revises: cb556896b000
Create Date: 2023-09-29 16:49:02.472872

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7c502751037c"
down_revision = "cb556896b000"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflict_practice_config", sa.Column("tillage_threshold_date", sa.Date(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("tillage_depth_attr_id", sa.Integer(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("tillage_depth_default_value", sa.JSON(), nullable=True))
    op.add_column(
        "mrv_conflict_practice_config", sa.Column("tillage_start_date_default_value", sa.Date(), nullable=True)
    )
    op.add_column("mrv_conflict_practice_config", sa.Column("soil_inversion_attr_id", sa.Integer(), nullable=True))
    op.add_column(
        "mrv_conflict_practice_config", sa.Column("winter_crop_commitment_attr_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        "conflict_practice_config_winter_crop_commitment_attr_id",
        "mrv_conflict_practice_config",
        "mrv_attributes",
        ["winter_crop_commitment_attr_id"],
        ["id"],
    )
    op.create_foreign_key(
        "conflict_practice_config_soil_inversion_attr_id",
        "mrv_conflict_practice_config",
        "mrv_attributes",
        ["soil_inversion_attr_id"],
        ["id"],
    )
    op.create_foreign_key(
        "conflict_practice_config_tillage_depth_attr_id",
        "mrv_conflict_practice_config",
        "mrv_attributes",
        ["tillage_depth_attr_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "conflict_practice_config_winter_crop_commitment_attr_id", "mrv_conflict_practice_config", type_="foreignkey"
    )
    op.drop_constraint(
        "conflict_practice_config_soil_inversion_attr_id", "mrv_conflict_practice_config", type_="foreignkey"
    )
    op.drop_constraint(
        "conflict_practice_config_tillage_depth_attr_id", "mrv_conflict_practice_config", type_="foreignkey"
    )
    op.drop_column("mrv_conflict_practice_config", "winter_crop_commitment_attr_id")
    op.drop_column("mrv_conflict_practice_config", "soil_inversion_attr_id")
    op.drop_column("mrv_conflict_practice_config", "tillage_start_date_default_value")
    op.drop_column("mrv_conflict_practice_config", "tillage_depth_default_value")
    op.drop_column("mrv_conflict_practice_config", "tillage_depth_attr_id")
    op.drop_column("mrv_conflict_practice_config", "tillage_threshold_date")
    # ### end Alembic commands ###
