"""Add csv_import flag to stages

Revision ID: ebc7372d1cd5
Revises: 2507324e28b6
Create Date: 2023-11-22 10:01:14.455985

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ebc7372d1cd5"
down_revision = "2507324e28b6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_stages", sa.Column("csv_import_enabled", sa.<PERSON>(), server_default="0", nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_stages", "csv_import_enabled")
    # ### end Alembic commands ###
