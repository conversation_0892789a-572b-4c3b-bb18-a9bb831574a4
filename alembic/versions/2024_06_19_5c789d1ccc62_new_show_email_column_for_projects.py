"""new show email column for projects

Revision ID: 5c789d1ccc62
Revises: a8d3daeed526
Create Date: 2024-06-19 16:35:43.116773

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5c789d1ccc62"
down_revision = "a8d3daeed526"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_projects", sa.Column("show_email", sa.<PERSON>(), server_default="1", nullable=False))
    op.create_index(op.f("ix_mrv_projects_show_email"), "mrv_projects", ["show_email"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_projects_show_email"), table_name="mrv_projects")
    op.drop_column("mrv_projects", "show_email")
    # ### end Alembic commands ###
