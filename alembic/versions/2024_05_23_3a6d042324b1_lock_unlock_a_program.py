"""lock/unlock a program

Revision ID: 3a6d042324b1
Revises: 8944b644f334
Create Date: 2024-05-23 17:21:33.282596

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3a6d042324b1"
down_revision = "8944b644f334"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_program_lock",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("locked_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("program_id", name="_program_locked_program_id_uc"),
        comment="Global locking of the program, then each user unlocks it separately.",
    )
    op.create_index(op.f("ix_mrv_program_lock_id"), "mrv_program_lock", ["id"], unique=False)
    op.create_table(
        "mrv_program_unlock",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("unlocked_reason", sa.String(length=255), nullable=False),
        sa.Column("unlocked_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("locked_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("status", sa.Enum("locked", "unlocked", name="programunlockstatus"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_program_unlock_id"), "mrv_program_unlock", ["id"], unique=False)
    op.create_index(
        "ix_mrv_program_unlocked_user_id", "mrv_program_unlock", ["program_id", "user_id", "status"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_mrv_program_unlocked_user_id", table_name="mrv_program_unlock")
    op.drop_index(op.f("ix_mrv_program_unlock_id"), table_name="mrv_program_unlock")
    op.drop_table("mrv_program_unlock")
    op.drop_index(op.f("ix_mrv_program_lock_id"), table_name="mrv_program_lock")
    op.drop_table("mrv_program_lock")
    # ### end Alembic commands ###
