"""Add commercial region tier

Revision ID: d3bf45c3fe8a
Revises: a4a41776b73b
Create Date: 2025-07-09 09:06:09.080675

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "d3bf45c3fe8a"
down_revision = "a4a41776b73b"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_commercial_region_tiers",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("commercial_id", sa.Integer(), nullable=False),
        sa.Column("region_tier", sa.Enum("NON_TARGETED", "TARGETED", name="regiontier"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_commercial_region_tiers_deleted_at"), "mrv_commercial_region_tiers", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_commercial_region_tiers_id"), "mrv_commercial_region_tiers", ["id"], unique=False)
    op.add_column("mrv_commercial_regions", sa.Column("phase_id", sa.Integer(), nullable=True))
    op.add_column(
        "mrv_commercial_regions",
        sa.Column("region_tier", sa.Enum("NON_TARGETED", "TARGETED", name="regiontier"), nullable=True),
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_commercial_regions", "region_tier")
    op.drop_column("mrv_commercial_regions", "phase_id")
    op.drop_index(op.f("ix_mrv_commercial_region_tiers_id"), table_name="mrv_commercial_region_tiers")
    op.drop_index(op.f("ix_mrv_commercial_region_tiers_deleted_at"), table_name="mrv_commercial_region_tiers")
    op.drop_table("mrv_commercial_region_tiers")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
