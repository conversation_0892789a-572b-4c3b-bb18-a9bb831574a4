"""Remove field_overlap_threshold

Revision ID: 5d5e47df7e28
Revises: 8ed13b0fed03
Create Date: 2024-01-18 18:36:37.096466

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "5d5e47df7e28"
down_revision = "8ed13b0fed03"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_programs", "field_overlap_threshold")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_programs",
        sa.Column(
            "field_overlap_threshold",
            mysql.INTEGER(),
            server_default=sa.text("'20'"),
            autoincrement=False,
            nullable=True,
        ),
    )
    # ### end Alembic commands ###
