"""adding new groups table

Revision ID: 849fcf3aa998
Revises: 3409f0455c0f
Create Date: 2024-05-27 09:21:23.779964

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "849fcf3aa998"
down_revision = "3409f0455c0f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_user_groups",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("parent_group_id", sa.Integer(), nullable=True),
        sa.<PERSON>umn("color_category_index", sa.Integer(), nullable=True),
        sa.Column("allow_self_assignment", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Entries for group management",
    )
    op.create_index(op.f("ix_mrv_user_groups_id"), "mrv_user_groups", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_user_groups_program_id"), "mrv_user_groups", ["program_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_user_groups_program_id"), table_name="mrv_user_groups")
    op.drop_index(op.f("ix_mrv_user_groups_id"), table_name="mrv_user_groups")
    op.drop_table("mrv_user_groups")
    # ### end Alembic commands ###
