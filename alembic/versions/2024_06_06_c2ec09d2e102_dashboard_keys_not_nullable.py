"""Dashboard keys not nullable

Revision ID: c2ec09d2e102
Revises: 18efeb84b75d
Create Date: 2024-06-06 18:36:15.214097

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "c2ec09d2e102"
down_revision = "18efeb84b75d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("mrv_reporting_chart_presets", "key", existing_type=mysql.VARCHAR(length=255), nullable=False)
    op.alter_column("mrv_reporting_dashboard_rows", "key", existing_type=mysql.VARCHAR(length=255), nullable=False)
    op.alter_column("mrv_reporting_dashboard_sections", "key", existing_type=mysql.VARCHAR(length=255), nullable=False)
    op.alter_column("mrv_reporting_dashboards", "key", existing_type=mysql.VARCHAR(length=255), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("mrv_reporting_dashboards", "key", existing_type=mysql.VARCHAR(length=255), nullable=True)
    op.alter_column("mrv_reporting_dashboard_sections", "key", existing_type=mysql.VARCHAR(length=255), nullable=True)
    op.alter_column("mrv_reporting_dashboard_rows", "key", existing_type=mysql.VARCHAR(length=255), nullable=True)
    op.alter_column("mrv_reporting_chart_presets", "key", existing_type=mysql.VARCHAR(length=255), nullable=True)
    # ### end Alembic commands ###
