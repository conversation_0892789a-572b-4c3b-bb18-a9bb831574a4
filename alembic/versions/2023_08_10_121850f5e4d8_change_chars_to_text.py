"""change chars to text

Revision ID: 121850f5e4d8
Revises: 23788a48d21f
Create Date: 2023-08-10 14:22:05.740491

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "121850f5e4d8"
down_revision = "23788a48d21f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "description",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Text(),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_dashboard_rows",
        "description",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Text(),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_dashboard_sections",
        "description",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Text(),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_dashboards",
        "description",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Text(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_dashboards",
        "description",
        existing_type=sa.Text(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_dashboard_sections",
        "description",
        existing_type=sa.Text(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_dashboard_rows",
        "description",
        existing_type=sa.Text(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_reporting_chart_presets",
        "description",
        existing_type=sa.Text(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
