"""Add unique constraint to mrv dndc contracted results

Revision ID: 1dfdbdb893e3
Revises: 28c9b7116b15
Create Date: 2024-12-09 16:03:28.730797

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "1dfdbdb893e3"
down_revision = "28c9b7116b15"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_dndc_contracted_results mdcr
        SET mdcr.deleted_at = NOW()
        WHERE mdcr.id NOT IN (
            SELECT latest_id
            FROM (
                SELECT MAX(mdcr2.id) AS latest_id
                FROM mrv_dndc_contracted_results mdcr2
                GROUP BY mdcr2.project_id
            ) subquery
        );"""
    )
    op.create_index(
        "unique_undeleted_project_id",
        "mrv_dndc_contracted_results",
        ["project_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )


def downgrade():
    op.drop_index("unique_undeleted_project_id", table_name="mrv_dndc_contracted_results")
