"""Add error code unsupported_practice_intention to ScenariosServiceIntegrationErrorCode

Revision ID: a94f7bb921ca
Revises: 3a6d042324b1
Create Date: 2024-05-22 09:57:19.279942

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a94f7bb921ca"
down_revision = "3a6d042324b1"
branch_labels = None
depends_on = None


class OldCodes(enum.StrEnum):
    unsupported_crop = enum.auto()
    unsupported_product_type = enum.auto()
    scenarios_service_unavailable = enum.auto()
    scenarios_service_validation_failure = enum.auto()
    boundaries_service_unavailable = enum.auto()
    invalid_management_data = enum.auto()
    overlapping_commodity_crop_periods = enum.auto()
    missing_baseline_year = enum.auto()
    generic_scenarios_service_failure = enum.auto()
    event_outside_phase_period = enum.auto()
    no_tillage_events_to_reduce_depth = enum.auto()
    no_boundaries_service_fertilizer_data = enum.auto()
    no_cultivation_cycles_after_baseline_year = enum.auto()
    period_between_commodity_crops_too_short = enum.auto()
    field_boundary_self_intersection = enum.auto()


class NewCodes(enum.StrEnum):
    unsupported_crop = enum.auto()
    unsupported_product_type = enum.auto()
    unsupported_practice_intention = enum.auto()
    scenarios_service_unavailable = enum.auto()
    scenarios_service_validation_failure = enum.auto()
    boundaries_service_unavailable = enum.auto()
    invalid_management_data = enum.auto()
    overlapping_commodity_crop_periods = enum.auto()
    missing_baseline_year = enum.auto()
    generic_scenarios_service_failure = enum.auto()
    event_outside_phase_period = enum.auto()
    no_tillage_events_to_reduce_depth = enum.auto()
    no_boundaries_service_fertilizer_data = enum.auto()
    no_cultivation_cycles_after_baseline_year = enum.auto()
    period_between_commodity_crops_too_short = enum.auto()
    field_boundary_self_intersection = enum.auto()


def upgrade():
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(OldCodes),
        type_=sa.Enum(NewCodes),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(NewCodes),
        type_=sa.Enum(OldCodes),
        existing_nullable=True,
    )
