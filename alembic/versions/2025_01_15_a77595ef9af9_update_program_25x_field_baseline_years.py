"""Update program 25X field baseline years

Revision ID: a77595ef9af9
Revises: cab3ef58ff18
Create Date: 2025-01-15 08:30:21.262008

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "a77595ef9af9"
down_revision = "cab3ef58ff18"
branch_labels = None
depends_on = None

NON_RETURNING_FIELD_IDS = [
    81155,
    80777,
    80912,
    80914,
    81042,
    80788,
    80789,
    81045,
    80791,
    81046,
    81048,
    81176,
    80667,
    80668,
    81053,
    80542,
    80670,
    80544,
    80543,
    80546,
    81055,
    81186,
    80938,
    80686,
    80815,
    80816,
    80690,
    80574,
    80963,
    80964,
    80965,
    80838,
    80839,
    80840,
    80970,
    80587,
    80972,
    80588,
    80974,
    80589,
    80590,
    80978,
    80851,
    80979,
    80980,
    80981,
    80755,
    81140,
    81148,
    81150,
    80664,
    84611,
    84612,
    99530,
]
NON_RETURNING_FIELD_IDS_STR = ",".join(map(str, NON_RETURNING_FIELD_IDS))


def upgrade():
    op.execute(
        f"""UPDATE mrv_fields_baseline mfb
        SET
            mfb.baseline_year = 2023,
            mfb.is_returning = 0
        WHERE mfb.field_id IN ({NON_RETURNING_FIELD_IDS_STR});"""  # nosec
    )


def downgrade():
    pass
