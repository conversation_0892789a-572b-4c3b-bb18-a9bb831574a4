"""remove-role-user-foreign-key-to-avoid-deadlock

Revision ID: d6cf322e88a4
Revises: 7cd119863df7
Create Date: 2024-11-29 20:06:19.228575

"""

from alembic import op
from config import get_settings

# revision identifiers, used by Alembic.
revision = "d6cf322e88a4"
down_revision = "7cd119863df7"
branch_labels = None
depends_on = None


settings = get_settings()


def upgrade():
    if settings.env in ["dev", "prod"]:
        op.drop_constraint("mrv_roles_users_ibfk_1", "mrv_roles_users", type_="foreignkey")


def downgrade():
    if settings.env in ["dev", "prod"]:
        op.create_foreign_key("mrv_roles_users_ibfk_1", "mrv_roles_users", "users", ["fs_user_id"], ["id"])
