"""Add 'CREATE_PROJECT_WITH_NEW_USER' permission to 'GROUP_ADMIN' role

Revision ID: b0d4f9d4cfa1
Revises: d93c775af1a7
Create Date: 2025-07-18 16:36:13.189992

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "b0d4f9d4cfa1"
down_revision = "0915380adb8b"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {},
    DefaultRoles.PRODUCER.value: {},
    DefaultRoles.PROGRAM_ADMIN.value: {},
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {},
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {},
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.CREATE_PROJECT_WITH_NEW_USER.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
