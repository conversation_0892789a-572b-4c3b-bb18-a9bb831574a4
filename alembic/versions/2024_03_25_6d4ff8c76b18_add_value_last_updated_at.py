"""Add value_last_updated_at to <PERSON>, Projects

Revision ID: 6d4ff8c76b18
Revises: 4825205004b6
Create Date: 2024-03-25 23:10:55.567278

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6d4ff8c76b18"
down_revision = "4825205004b6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_fields", sa.Column("value_last_updated_at", sa.DateTime(), nullable=True))
    op.add_column("mrv_projects", sa.Column("value_last_updated_at", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_projects", "value_last_updated_at")
    op.drop_column("mrv_fields", "value_last_updated_at")
    # ### end Alembic commands ###
