"""remove optis v1 model

Revision ID: bab31a555b71
Revises: 23b558725e54
Create Date: 2024-04-26 16:57:48.816269

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "bab31a555b71"
down_revision = "23b558725e54"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("mrv_optis_labels_attributes_mapping")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_optis_labels_attributes_mapping",
        sa.Column("id", mysql.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "optis_label",
            mysql.ENUM("curr_crop_id", "cover_crop", "spring_till_class", "fall_till_class"),
            nullable=False,
        ),
        sa.Column("attribute_id", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("order", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"], ["mrv_attributes.id"], name="mrv_optis_labels_attributes_mapping_ibfk_1"
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_collate="utf8mb4_0900_ai_ci",
        mysql_default_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_optis_labels_attributes_mapping_id", "mrv_optis_labels_attributes_mapping", ["id"], unique=False
    )
    op.create_index(
        "_optis_label_attribute_id_uc",
        "mrv_optis_labels_attributes_mapping",
        ["optis_label", "attribute_id"],
        unique=True,
    )
    # ### end Alembic commands ###
