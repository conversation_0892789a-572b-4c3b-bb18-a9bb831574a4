"""add new cover crop generator

Revision ID: b0c68445a6c5
Revises: 478b4d9c998e
Create Date: 2024-06-03 04:21:11.954520

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "b0c68445a6c5"
down_revision = "478b4d9c998e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_cover_crop_eligibility_config",
        "version",
        existing_type=mysql.ENUM("COVER_CROP_GENERATOR_V1", "COVER_CROP_GENERATOR_V2"),
        type_=sa.Enum(
            "COVER_CROP_GENERATOR_V1",
            "COVER_CROP_GENERATOR_V2",
            "COVER_CROP_GENERATOR_V3",
            name="covercropgeneratortype",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_cover_crop_eligibility_config",
        "version",
        existing_type=sa.Enum(
            "COVER_CROP_GENERATOR_V1",
            "COVER_CROP_GENERATOR_V2",
            "COVER_CROP_GENERATOR_V3",
            name="covercropgeneratortype",
        ),
        type_=mysql.ENUM("COVER_CROP_GENERATOR_V1", "COVER_CROP_GENERATOR_V2"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
