"""Add mrv_ses_migration_result to persist result of SES migrations at the field level.

Revision ID: 53ac3a6a701d
Revises: 8cf9a9cb6ebf
Create Date: 2025-03-05 12:28:59.112526

"""

import sqlalchemy as sa
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "53ac3a6a701d"
down_revision = "8cf9a9cb6ebf"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.MIGRATE_PROGRAM_DATA.value,
    },
    DefaultRoles.PRODUCER.value: {},
    DefaultRoles.PROGRAM_ADMIN.value: {},
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {},
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {},
}


def upgrade():
    op.create_table(
        "mrv_ses_migration_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.String(36), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("enqueued", "started", "failed", "succeeded", name="sesfieldmigrationstatus"),
            nullable=False,
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("task_id", "field_id", name="task_id_field_id_uc"),
    )
    op.create_index(op.f("ix_mrv_ses_migration_results_id"), "mrv_ses_migration_results", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_ses_migration_results_task_id"), "mrv_ses_migration_results", ["task_id"], unique=False
    )

    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    op.drop_index(op.f("ix_mrv_ses_migration_results_task_id"), table_name="mrv_ses_migration_results")
    op.drop_index(op.f("ix_mrv_ses_migration_results_id"), table_name="mrv_ses_migration_results")
    op.drop_table("mrv_ses_migration_results")

    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
