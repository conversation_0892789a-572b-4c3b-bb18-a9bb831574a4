"""Edit csv_import permissions

Revision ID: dfa07419340d
Revises: bf06621645cc
Create Date: 2023-12-12 03:19:53.861903

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "dfa07419340d"
down_revision = "bf06621645cc"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_CSV_IMPORT_TEMPLATE.value,
        Permission.CREATE_VALUES_BASED_ON_FILLED_CSV_TEMPLATE.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_CSV_IMPORT_TEMPLATE.value,
        Permission.CREATE_VALUES_BASED_ON_FILLED_CSV_TEMPLATE.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_CSV_IMPORT_TEMPLATE.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_CSV_IMPORT_TEMPLATE.value,
        Permission.CREATE_VALUES_BASED_ON_FILLED_CSV_TEMPLATE.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_CSV_IMPORT_TEMPLATE.value,
        Permission.CREATE_VALUES_BASED_ON_FILLED_CSV_TEMPLATE.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
