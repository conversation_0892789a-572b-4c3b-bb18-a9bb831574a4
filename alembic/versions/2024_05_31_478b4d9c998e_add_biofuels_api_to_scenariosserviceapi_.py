"""Add biofuels_api to ScenariosServiceApi model

Revision ID: 478b4d9c998e
Revises: dea5824df58d
Create Date: 2024-05-31 11:54:32.428726

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "478b4d9c998e"
down_revision = "dea5824df58d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "ss_api",
        existing_type=mysql.ENUM("measure_api", "explore_api"),
        type_=sa.Enum("measure_api", "explore_api", "biofuels_api", name="scenariosserviceapi"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "ss_api",
        existing_type=sa.Enum("measure_api", "explore_api", "biofuels_api", name="scenariosserviceapi"),
        type_=mysql.ENUM("measure_api", "explore_api"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
