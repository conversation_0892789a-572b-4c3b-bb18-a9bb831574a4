"""add phase_id column to mrv_crop_type_selection table

Revision ID: fe611d4b063c
Revises: 8f00e76f0731
Create Date: 2023-12-06 09:30:54.941824

"""

import sqlalchemy as sa

from alembic import op

revision = "fe611d4b063c"
down_revision = "8f00e76f0731"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_crop_type_selection", sa.Column("phase_id", sa.Integer(), nullable=True))
    op.create_index(op.f("ix_mrv_crop_type_selection_phase_id"), "mrv_crop_type_selection", ["phase_id"], unique=False)
    op.create_foreign_key(
        "_mrv_crop_type_selection_phase_id", "mrv_crop_type_selection", "mrv_phases", ["phase_id"], ["id"]
    )


def downgrade():
    op.drop_constraint("_mrv_crop_type_selection_phase_id", "mrv_crop_type_selection", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_crop_type_selection_phase_id"), table_name="mrv_crop_type_selection")
    op.drop_column("mrv_crop_type_selection", "phase_id")
