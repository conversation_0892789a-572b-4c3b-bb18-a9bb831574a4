"""disable-producer-del-for-non-super-admin

Revision ID: ae40f9408d0c
Revises: fdd0aeeeb57f
Create Date: 2025-01-13 12:39:46.051113

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles

# revision identifiers, used by Alembic.
revision = "ae40f9408d0c"
down_revision = "fdd0aeeeb57f"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
