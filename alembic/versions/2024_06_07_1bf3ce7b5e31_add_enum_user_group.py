"""add enum 'User Group'

Revision ID: 1bf3ce7b5e31
Revises: 8be522c017a4
Create Date: 2024-06-07 11:07:13.243411

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "1bf3ce7b5e31"
down_revision = "8be522c017a4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_program_custom_reg_inputs",
        "type_",
        existing_type=mysql.ENUM("regex", "dropdown"),
        type_=sa.Enum("regex", "dropdown", "user_group", name="customreginputstypes"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_program_custom_reg_inputs",
        "type_",
        existing_type=sa.Enum("regex", "dropdown", "user_group", name="customreginputstypes"),
        type_=mysql.ENUM("regex", "dropdown"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
