"""Add GENERAL_SCOPE_3 protocol to the protocols enum and add a baseline method column to mrv_programs.

Revision ID: d7e2fafb2587
Revises: 3f34ed59d680
Create Date: 2023-12-14 11:44:24.304618

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d7e2fafb2587"
down_revision = "3f34ed59d680"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_programs",
        "protocol",
        existing_type=sa.Enum("VERRA", "CAR_SEP", "SUSTAINCERT", name="protocols"),
        type_=mysql.ENUM("VERRA", "CAR_SEP", "SUSTAINCERT", "GENERAL_SCOPE_3"),
        existing_nullable=True,
    )
    op.add_column(
        "mrv_programs", sa.Column("baseline_method", sa.Enum("ROTATIONAL", "BLENDED", "MATCHED"), nullable=True)
    )


def downgrade():
    op.alter_column(
        "mrv_programs",
        "protocol",
        existing_type=sa.Enum("VERRA", "CAR_SEP", "SUSTAINCERT", "GENERAL_SCOPE_3", name="protocols"),
        type_=mysql.ENUM("VERRA", "CAR_SEP", "SUSTAINCERT"),
        existing_nullable=True,
    )
    op.drop_column("mrv_programs", "baseline_method")
