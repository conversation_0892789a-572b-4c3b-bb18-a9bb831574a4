"""Add more indexes

Revision ID: d5d9af1a3c08
Revises: 3ed886b0ae0d
Create Date: 2024-05-02 21:10:14.006830

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d5d9af1a3c08"
down_revision = "b0d902e5f8ba"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_mrv_field_facts_data_type"), "mrv_field_facts", ["data_type"], unique=False)
    op.create_index(op.f("ix_mrv_field_facts_state"), "mrv_field_facts", ["state"], unique=False)
    op.create_index(op.f("ix_mrv_fields_status"), "mrv_fields", ["status"], unique=False)
    op.create_index(
        op.f("ix_mrv_project_completion_percentage_complete"),
        "mrv_project_completion",
        ["percentage_complete"],
        unique=False,
    )
    op.create_index(op.f("ix_mrv_project_values_key"), "mrv_project_values", ["key"], unique=False)
    op.create_index(op.f("ix_mrv_project_values_value"), "mrv_project_values", ["value"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_project_values_value"), table_name="mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_values_key"), table_name="mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_completion_percentage_complete"), table_name="mrv_project_completion")
    op.drop_index(op.f("ix_mrv_fields_status"), table_name="mrv_fields")
    op.drop_index(op.f("ix_mrv_field_facts_state"), table_name="mrv_field_facts")
    op.drop_index(op.f("ix_mrv_field_facts_data_type"), table_name="mrv_field_facts")
    # ### end Alembic commands ###
