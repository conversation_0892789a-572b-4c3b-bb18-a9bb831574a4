"""add nm eligibility

Revision ID: 378d874f1a34
Revises: ca56a0aa01e8
Create Date: 2024-05-30 03:13:30.856215

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "378d874f1a34"
down_revision = "ca56a0aa01e8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_nm_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.<PERSON>(), server_default="0", nullable=False),
        sa.Column("default_crop_match", sa.String(length=255), nullable=True),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_nm_eligibility_config_id"), "mrv_nm_eligibility_config", ["id"], unique=False)
    op.alter_column(
        "mrv_conflict_practice_config",
        "practice_type",
        existing_type=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        type_=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_conflicts",
        "practice_type",
        existing_type=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        type_=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_phase_practice_config",
        "practice_type",
        existing_type=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        type_=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        existing_nullable=False,
    )
    op.add_column("mrv_stage_eligibility_config", sa.Column("nm_config", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "mrv_stage_eligibility_nm_config_fk",
        "mrv_stage_eligibility_config",
        "mrv_nm_eligibility_config",
        ["nm_config"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_stage_eligibility_nm_config_fk", "mrv_stage_eligibility_config", type_="foreignkey")
    op.drop_column("mrv_stage_eligibility_config", "nm_config")
    op.alter_column(
        "mrv_phase_practice_config",
        "practice_type",
        existing_type=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        type_=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_conflicts",
        "practice_type",
        existing_type=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        type_=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_conflict_practice_config",
        "practice_type",
        existing_type=sa.Enum(
            "TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", "NUTRIENT_MANAGEMENT", name="practicetypes"
        ),
        type_=mysql.ENUM("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP"),
        existing_nullable=False,
    )
    op.drop_index(op.f("ix_mrv_nm_eligibility_config_id"), table_name="mrv_nm_eligibility_config")
    op.drop_table("mrv_nm_eligibility_config")
    # ### end Alembic commands ###
