"""MRVG-205

Revision ID: 744e48a553bf
Revises: 4a81ab579d67
Create Date: 2024-01-23 16:32:14.222552

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "744e48a553bf"
down_revision = "4a81ab579d67"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_documents",
        sa.Column(
            "replaces",
            sa.Integer(),
            nullable=True,
            comment="this document replaces the document with the given id",
        ),
    )
    op.add_column(
        "mrv_documents",
        sa.Column(
            "replaced_by",
            sa.Integer(),
            nullable=True,
            comment="this document is replaced by document with the given id",
        ),
    )


def downgrade():
    op.drop_column("mrv_documents", "replaces")
    op.drop_column("mrv_documents", "replacedBy")
