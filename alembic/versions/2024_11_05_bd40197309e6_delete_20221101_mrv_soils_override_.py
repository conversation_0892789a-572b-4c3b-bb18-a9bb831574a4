"""Delete 20221101 mrv soils override records

Revision ID: bd40197309e6
Revises: e24378a1968d
Create Date: 2024-11-05 16:38:07.151376

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "bd40197309e6"
down_revision = "e24378a1968d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """DELETE FROM mrv_soils_override mso
        WHERE mso.sampling_date='2022-11-01';"""
    )


def downgrade():
    pass
