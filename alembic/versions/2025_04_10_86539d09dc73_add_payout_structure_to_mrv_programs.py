"""Add payout structure to mrv programs

Revision ID: 86539d09dc73
Revises: a2f778488b94
Create Date: 2025-04-10 13:53:22.650714

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "86539d09dc73"
down_revision = "a2f778488b94"
branch_labels = None
depends_on = None

PAY_PER_NET_GHG_REDUCED_PROGRAM_IDS = [
    21,
    68,
    90,
    116,
    117,
    118,
    155,
    190,
    253,
    254,
    256,
    257,
    258,
    259,
    816,
    1067,
    1119,
    1126,
    1127,
    1128,
    1129,
    1130,
    1131,
    1150,
    1178,
    1202,
    1230,
    1621,
    1632,
    1638,
]
PAY_PER_NET_GHG_REDUCED_PROGRAM_IDS_STR = ",".join(map(str, PAY_PER_NET_GHG_REDUCED_PROGRAM_IDS))
PAY_PER_AREA_ENROLLED_PROGRAM_IDS = [95, 133, 150, 164, 260, 275, 1133, 1143, 1159, 1173, 1197, 1199, 1215, 1629]
PAY_PER_AREA_ENROLLED_PROGRAM_IDS_STR = ",".join(map(str, PAY_PER_AREA_ENROLLED_PROGRAM_IDS))


def upgrade():
    op.add_column(
        "mrv_programs",
        sa.Column(
            "payout_structure",
            sa.Enum(
                "no_payment",
                "pay_per_net_ghg_reduced",
                "pay_per_area_enrolled",
                "pay_per_practice",
                name="payoutstructure",
            ),
            nullable=True,
        ),
    )
    op.execute(
        f"""UPDATE mrv_programs mp
        SET mp.payout_structure = CASE
            WHEN mp.id IN ({PAY_PER_NET_GHG_REDUCED_PROGRAM_IDS_STR}) THEN 'pay_per_net_ghg_reduced'
            WHEN mp.id IN ({PAY_PER_AREA_ENROLLED_PROGRAM_IDS_STR}) THEN 'pay_per_area_enrolled'
            ELSE 'no_payment'
        END;"""  # nosec
    )


def downgrade():
    op.drop_column("mrv_programs", "payout_structure")
