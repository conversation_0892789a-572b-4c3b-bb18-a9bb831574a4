"""update-project-group-permissions

Revision ID: b771459cbf48
Revises: 129859c51096
Create Date: 2024-08-15 14:59:52.021938

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "b771459cbf48"
down_revision = "129859c51096"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROJECT_GROUPS.value,
        Permission.UPDATE_PROJECT_GROUPS.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_PROJECT_GROUPS.value,
    },
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.GET_PROJECT_GROUPS.value,
        Permission.UPDATE_PROJECT_GROUPS.value,
    },
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {
        Permission.GET_PROJECT_GROUPS.value,
    },
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.GET_PROJECT_GROUPS.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
