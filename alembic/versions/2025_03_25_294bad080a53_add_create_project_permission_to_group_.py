"""add-create-project-permission-to-group-enabled-program-admin

Revision ID: 294bad080a53
Revises: 32c1db22f583
Create Date: 2025-03-25 14:28:25.722623

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "294bad080a53"
down_revision = "32c1db22f583"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.CREATE_PROJECT.value,
        Permission.CREATE_PROJECT_USERS.value,
        Permission.UPDATE_PROJECT_GROUPS.value,
    },
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {
        Permission.CREATE_PROJECT.value,
        Permission.CREATE_PROJECT_USERS.value,
        Permission.UPDATE_PROJECT_GROUPS.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
