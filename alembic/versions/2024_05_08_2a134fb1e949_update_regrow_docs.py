"""update-regrow-docs

Revision ID: 2a134fb1e949
Revises: 6193d0a6e173
Create Date: 2024-05-07 14:53:53.567931

"""

from sqlalchemy import text
from sqlalchemy.orm import Session

from alembic import op

# revision identifiers, used by Alembic.
revision = "2a134fb1e949"
down_revision = "debc37242a40"
branch_labels = None
depends_on = None


def upgrade():
    try:
        session = Session(bind=op.get_bind())
        with session.begin():
            # Delete unused documents
            session.execute(text("TRUNCATE mrv_documents;"))
            session.execute(
                text(
                    """
                    INSERT INTO mrv_documents (id, name, category, description, content, active_from, active_to, created_at, updated_at, deleted_at, is_public, replaces, replaced_by) VALUES
                    (1, 'Terms and Conditions', 'tandc', 'I have read and agree to the Regrow terms and conditions', 'https://www.regrow.ag/terms', '2024-02-19 23:07:01', NULL, '2024-02-19 23:33:19', '2024-05-03 10:49:31', NULL, 1, NULL, NULL);
                    """
                )
            )
            session.execute(
                text(
                    """
                    INSERT INTO mrv_documents (id, name, category, description, content, active_from, active_to, created_at, updated_at, deleted_at, is_public, replaces, replaced_by) VALUES
                    (2, 'Privacy Policy', 'privacy', 'I have read and agree to the Regrow privacy policy', 'https://www.regrow.ag/privacy-policy', '2024-02-19 23:06:53', NULL, '2024-02-19 23:33:20', '2024-02-19 23:33:20', NULL, 1, NULL, NULL);
                    """
                )
            )

            # Delete unused program document relationships
            session.execute(text("TRUNCATE mrv_program_documents;"))

            # Assign the terms and conditions and privacy policy to all live and demo programs
            session.execute(
                text(
                    """
                    INSERT INTO mrv_program_documents (program_id, document_id)
                    SELECT p.id, :tandcDocId FROM mrv_programs p WHERE p.deleted_at IS NULL
                    """
                ),
                {"tandcDocId": 1},
            )
            session.execute(
                text(
                    """
                    INSERT INTO mrv_program_documents (program_id, document_id)
                    SELECT p.id, :privacyDocId FROM mrv_programs p WHERE p.deleted_at IS NULL
                    """
                ),
                {"privacyDocId": 2},
            )
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    pass
