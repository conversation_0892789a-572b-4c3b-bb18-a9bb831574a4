"""add field lineage

Revision ID: 21d8b71fdde4
Revises: d767162b095e
Create Date: 2024-06-25 18:58:38.357104

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "21d8b71fdde4"
down_revision = "d767162b095e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_fields_lineage",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_year", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        comment="Stores lineage information for fields. This is used to track the history(split, merging, increase in size, decrease in size) of a field.",
    )
    op.create_index(op.f("ix_mrv_fields_lineage_id"), "mrv_fields_lineage", ["id"], unique=False)
    op.add_column(
        "mrv_fields", sa.Column("field_lineage_id", sa.Integer(), nullable=True, comment="The lineage of the field")
    )
    op.create_foreign_key("fk_mrv_fields_lineage_id", "mrv_fields", "mrv_fields_lineage", ["field_lineage_id"], ["id"])
    op.add_column(
        "mrv_fields_history",
        sa.Column("field_lineage_id", sa.Integer(), nullable=True, comment="The lineage of the field"),
    )
    op.create_foreign_key(
        "fk_mrv_fields_history_lineage_id", "mrv_fields_history", "mrv_fields_lineage", ["field_lineage_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_mrv_fields_history_lineage_id", "mrv_fields_history", type_="foreignkey")
    op.drop_column("mrv_fields_history", "field_lineage_id")
    op.drop_constraint("fk_mrv_fields_lineage_id", "mrv_fields", type_="foreignkey")
    op.drop_column("mrv_fields", "field_lineage_id")
    op.drop_index(op.f("ix_mrv_fields_lineage_id"), table_name="mrv_fields_lineage")
    op.drop_table("mrv_fields_lineage")
    # ### end Alembic commands ###
