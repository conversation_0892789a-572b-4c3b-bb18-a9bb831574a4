"""add permissions for boundary service integration

Revision ID: 6358415d6faa
Revises: eb3be11f1e55
Create Date: 2024-02-13 12:08:50.190367

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "6358415d6faa"
down_revision = "eb3be11f1e55"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_BOUNDARY_SERVICE_GEOMETRIES.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_BOUNDARY_SERVICE_GEOMETRIES.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_BOUNDARY_SERVICE_GEOMETRIES.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
