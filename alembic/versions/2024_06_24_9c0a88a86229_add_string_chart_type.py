"""Add string chart type

Revision ID: 9c0a88a86229
Revises: 5c789d1ccc62
Create Date: 2024-06-24 14:45:20.071516

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "9c0a88a86229"
down_revision = "5c789d1ccc62"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
        ),
        type_=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
            name="charttype",
        ),
        existing_nullable=False,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
            name="charttype",
        ),
        type_=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
        ),
        existing_nullable=False,
    )
