"""add-cargill-eulut-2024

Revision ID: e2a9b471935e
Revises: 384839ccc0c3
Create Date: 2024-03-10 15:47:42.049884

"""

from sqlalchemy.orm import Session

from alembic import op
from projects.eligibility.helper import initialise_cargill_eu_lut

# revision identifiers, used by Alembic.
revision = "e2a9b471935e"
down_revision = "384839ccc0c3"
branch_labels = None
depends_on = None


def upgrade():
    # Initialising LUT table with 2024 data
    session = Session(bind=op.get_bind())
    initialise_cargill_eu_lut(session=session, year=2024)


def downgrade():
    # no need to going back to previous LUT version
    pass
