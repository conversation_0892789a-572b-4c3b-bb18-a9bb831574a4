"""Undo phase completion for 1119 M Phase

Revision ID: a59a5661517c
Revises: f2bc81f92375
Create Date: 2025-06-05 14:04:15.408318

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "a59a5661517c"
down_revision = "f2bc81f92375"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_project_phase_completion ppc
        JOIN mrv_projects prj ON prj.id=ppc.project_id
        JOIN mrv_phases phs ON ppc.phase_id=phs.id and phs.type_='MONITORING' AND phs.program_id=1119
        SET ppc.is_completed=0, ppc.updated_at=NOW()
        WHERE ppc.is_completed=1 AND prj.program_id=1119 AND ppc.deleted_at IS NULL;"""
    )


def downgrade():
    # Not reversible, but I've taken a dump of the phase completion table just in case
    pass
