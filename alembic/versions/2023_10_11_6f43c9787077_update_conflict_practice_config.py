"""update conflict practice config

Revision ID: 6f43c9787077
Revises: eea7ea25de53
Create Date: 2023-10-11 15:44:44.001250

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6f43c9787077"
down_revision = "eea7ea25de53"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflict_practice_config", sa.Column("tillage_period_attr_id", sa.Integer(), nullable=True))
    op.alter_column(
        "mrv_conflict_practice_config",
        "tillage_start_date_default_value",
        existing_type=sa.DATE(),
        type_=sa.JSON(),
        existing_nullable=True,
    )
    op.create_foreign_key(
        "mrv_conflict_practice_config_attributes_tillage_period_attr",
        "mrv_conflict_practice_config",
        "mrv_attributes",
        ["tillage_period_attr_id"],
        ["id"],
    )


def downgrade():
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_constraint(
        "mrv_conflict_practice_config_attributes_tillage_period_attr",
        "mrv_conflict_practice_config",
        type_="foreignkey",
    )
    op.alter_column(
        "mrv_conflict_practice_config",
        "tillage_start_date_default_value",
        existing_type=sa.JSON(),
        type_=sa.DATE(),
        existing_nullable=True,
    )
    op.drop_column("mrv_conflict_practice_config", "tillage_period_attr_id")
    # ### end Alembic commands ###
