"""Add signer_info to project contracts

Revision ID: 0e8f04cc9b50
Revises: a59a5661517c
Create Date: 2025-06-09 12:08:29.923750

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "0e8f04cc9b50"
down_revision = "a59a5661517c"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_project_contracts",
        sa.Column(
            "signer_info",
            sa.JSON(),
            nullable=True,
            comment="Signing details: {producer_signed_at, cosigner_signed_at, all_parties_signed_at}",
        ),
    )

    connection = op.get_bind()

    # For programs requiring cosigner - populate signer_info from webhook data
    connection.execute(
        sa.text(
            """
        UPDATE mrv_project_contracts pc
        JOIN mrv_projects p ON p.id = pc.project
        JOIN mrv_programs prog ON prog.id = p.program_id
        JOIN (
            SELECT
                contract_id,
                JSON_ARRAYAGG(created_at) as webhook_times
            FROM (
                SELECT
                    pc2.id as contract_id,
                    dw.created_at
                FROM mrv_project_contracts pc2
                JOIN mrv_projects p2 ON p2.id = pc2.project
                JOIN mrv_programs prog2 ON prog2.id = p2.program_id
                JOIN mrv_docusign_webhook dw ON dw.envelope_id = pc2.docusign_envelope_id
                WHERE dw.event = 'recipient-completed'
                AND prog2.docusign_additional_signature IS NOT NULL
                AND p2.deleted_at IS NULL
                AND pc2.deleted_at IS NULL
                AND pc2.docusign_envelope_id IS NOT NULL
                ORDER BY pc2.id, dw.created_at
            ) ordered_webhooks
            GROUP BY contract_id
        ) webhook_data ON webhook_data.contract_id = pc.id
        SET pc.signer_info = JSON_OBJECT(
            'producer_signed_at', JSON_EXTRACT(webhook_data.webhook_times, '$[0]'),
            'cosigner_signed_at',
                CASE WHEN JSON_LENGTH(webhook_data.webhook_times) >= 2
                     THEN JSON_EXTRACT(webhook_data.webhook_times, '$[1]')
                     ELSE NULL END,
            'all_parties_signed_at',
                CASE WHEN JSON_LENGTH(webhook_data.webhook_times) >= 2
                     THEN JSON_EXTRACT(webhook_data.webhook_times, '$[1]')
                     ELSE NULL END
        )
        WHERE prog.docusign_additional_signature IS NOT NULL
        AND p.deleted_at IS NULL
        AND pc.deleted_at IS NULL
        AND pc.docusign_envelope_id IS NOT NULL
    """
        )
    )

    # For programs requiring producer only - populate signer_info from webhook data
    connection.execute(
        sa.text(
            """
        UPDATE mrv_project_contracts pc
        JOIN mrv_projects p ON p.id = pc.project
        JOIN mrv_programs prog ON prog.id = p.program_id
        JOIN (
            SELECT
                contract_id,
                JSON_ARRAYAGG(created_at) as webhook_times
            FROM (
                SELECT
                    pc2.id as contract_id,
                    dw.created_at
                FROM mrv_project_contracts pc2
                JOIN mrv_projects p2 ON p2.id = pc2.project
                JOIN mrv_programs prog2 ON prog2.id = p2.program_id
                JOIN mrv_docusign_webhook dw ON dw.envelope_id = pc2.docusign_envelope_id
                WHERE dw.event = 'recipient-completed'
                AND prog2.docusign_additional_signature IS NULL
                AND p2.deleted_at IS NULL
                AND pc2.deleted_at IS NULL
                AND pc2.docusign_envelope_id IS NOT NULL
                ORDER BY pc2.id, dw.created_at
            ) ordered_webhooks
            GROUP BY contract_id
        ) webhook_data ON webhook_data.contract_id = pc.id
        SET pc.signer_info = JSON_OBJECT(
            'producer_signed_at', JSON_EXTRACT(webhook_data.webhook_times, '$[0]'),
            'cosigner_signed_at', NULL,
            'all_parties_signed_at', JSON_EXTRACT(webhook_data.webhook_times, '$[0]')
        )
        WHERE prog.docusign_additional_signature IS NULL
        AND p.deleted_at IS NULL
        AND pc.deleted_at IS NULL
        AND pc.docusign_envelope_id IS NOT NULL
    """
        )
    )

    # Fallback for contracts without webhook data - use signed_at
    connection.execute(
        sa.text(
            """
        UPDATE mrv_project_contracts
        SET signer_info = JSON_OBJECT(
            'producer_signed_at', signed_at,
            'cosigner_signed_at', NULL,
            'all_parties_signed_at', signed_at
        )
        WHERE docusign_envelope_id IS NOT NULL
        AND deleted_at IS NULL
        AND docusign_status = 'completed'
        AND signer_info IS NULL
        AND signed_at IS NOT NULL
    """
        )
    )


def downgrade():
    op.drop_column("mrv_project_contracts", "signer_info")
