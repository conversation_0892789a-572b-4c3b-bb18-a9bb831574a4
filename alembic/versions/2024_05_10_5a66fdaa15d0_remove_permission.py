"""remove permission

Revision ID: 5a66fdaa15d0
Revises: 9eb6bd9f29a2
Create Date: 2024-05-10 15:30:05.210317

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "5a66fdaa15d0"
down_revision = "9eb6bd9f29a2"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}
#
roles_to_permissions_mapping = {
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.EXPORT_PROGRAM_CONTRACTS.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)
