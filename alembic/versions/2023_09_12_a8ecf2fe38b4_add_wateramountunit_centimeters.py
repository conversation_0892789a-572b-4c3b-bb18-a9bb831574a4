"""Add WaterAmountUnit Centimeters

Revision ID: a8ecf2fe38b4
Revises: cd0403eaee3f
Create Date: 2023-09-12 13:02:57.949311

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "a8ecf2fe38b4"
down_revision = "cd0403eaee3f"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "water_amount_unit",
        existing_type=mysql.ENUM("MM", "IN", "GAL"),
        type_=sa.Enum("CM", "MM", "IN", "GAL", name="wateramountunit"),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "water_amount_unit",
        existing_type=sa.Enum("CM", "MM", "IN", "GAL", name="wateramountunit"),
        type_=mysql.ENUM("MM", "IN", "GAL"),
        existing_nullable=True,
    )
