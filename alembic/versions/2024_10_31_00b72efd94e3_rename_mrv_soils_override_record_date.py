"""Rename mrv soils override record date

Revision ID: 00b72efd94e3
Revises: 89330ae51273
Create Date: 2024-10-31 15:10:52.923483

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "00b72efd94e3"
down_revision = "89330ae51273"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        table_name="mrv_soils_override",
        column_name="record_date",
        new_column_name="sampling_date",
        existing_type=sa.Date(),
        existing_nullable=False,
    )
    op.execute(
        """UPDATE mrv_soils_override mso
        SET mso.sampling_date='2022-11-01'
        WHERE mso.sampling_date='2023-12-16';"""
    )


def downgrade():
    op.alter_column(
        table_name="mrv_soils_override",
        column_name="sampling_date",
        new_column_name="record_date",
        existing_type=sa.Date(),
        existing_nullable=False,
    )
