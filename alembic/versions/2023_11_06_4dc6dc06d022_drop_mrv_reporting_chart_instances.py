"""Drop mrv_reporting_chart_instances and remove softly deleted dashboards with nested

Revision ID: 4dc6dc06d022
Revises: f03596cb34fe
Create Date: 2023-11-06 18:03:59.628842

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.engine.reflection import Inspector

from alembic import op

# revision identifiers, used by Alembic.
revision = "4dc6dc06d022"
down_revision = "f03596cb34fe"
branch_labels = None
depends_on = None


def upgrade():
    try:
        conn = op.get_bind()
        inspector = Inspector.from_engine(conn)
        for table in inspector.get_table_names():
            if "mrv_reporting_chart_instances" in table:
                op.drop_table("mrv_reporting_chart_instances")
                break
    except Exception as e:
        msg = e  # noqa


def downgrade():
    op.create_table(
        "mrv_reporting_chart_instances",
        sa.Column("id", mysql.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("row_id", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("chart_id", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", mysql.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["chart_id"], ["mrv_reporting_chart_presets.id"], name="mrv_reporting_chart_instances_ibfk_1"
        ),
        sa.ForeignKeyConstraint(
            ["row_id"], ["mrv_reporting_dashboard_rows.id"], name="mrv_reporting_chart_instances_ibfk_2"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_mrv_reporting_chart_instances_id", "mrv_reporting_chart_instances", ["id"], unique=False)
    op.create_index(
        "ix_mrv_reporting_chart_instances_deleted_at", "mrv_reporting_chart_instances", ["deleted_at"], unique=False
    )
