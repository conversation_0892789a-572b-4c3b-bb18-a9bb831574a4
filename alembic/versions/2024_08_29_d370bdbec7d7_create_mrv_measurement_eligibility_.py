"""Create mrv_measurement_eligibility table to track runs of measurement eligibility.

Revision ID: d370bdbec7d7
Revises: 23df836a0b31
Create Date: 2024-08-29 13:58:11.151111

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "d370bdbec7d7"
down_revision = "23df836a0b31"
branch_labels = None
depends_on = None


# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    op.create_table(
        "mrv_measurement_eligibility",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("measurement_eligible", sa.Boolean(), nullable=False),
        sa.Column("ineligibility_reasons", sa.JSON(), nullable=False),
        sa.Column(
            "enrollment_data_entry_complete",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=False,
        ),
        sa.Column(
            "contract_signed",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=False,
        ),
        sa.Column(
            "measurement_data_entry_complete",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=False,
        ),
        sa.Column(
            "monitored_practice_eligibility",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=False,
        ),
        sa.Column("monitored_practices", sa.JSON(), nullable=False),
        sa.Column("eligible_practice_combinations", sa.JSON(), nullable=False),
        sa.Column(
            "credited_crop_type_eligibility",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=True,
        ),
        sa.Column("credited_crop_type", sa.Text(), nullable=True),
        sa.Column(
            "modelable_crop_type_eligibility",
            sa.Enum("ELIGIBLE", "INELIGIBLE", "DISABLED", name="eligibilitydecisionoption"),
            nullable=True,
        ),
        sa.Column("unmodelable_crop_types", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("field_id", "deleted_at", name="field_id_deleted_at_uc"),
    )
    op.create_index(
        op.f("ix_mrv_measurement_eligibility_deleted_at"), "mrv_measurement_eligibility", ["deleted_at"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_measurement_eligibility_deleted_at"), table_name="mrv_measurement_eligibility")
    op.drop_table("mrv_measurement_eligibility")
