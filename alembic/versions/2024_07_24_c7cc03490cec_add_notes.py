"""add notes

Revision ID: c7cc03490cec
Revises: 4f282705b68a
Create Date: 2024-07-24 19:04:35.979810

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c7cc03490cec"
down_revision = "4f282705b68a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflicts", sa.Column("notes", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_conflicts", "notes")
    # ### end Alembic commands ###
