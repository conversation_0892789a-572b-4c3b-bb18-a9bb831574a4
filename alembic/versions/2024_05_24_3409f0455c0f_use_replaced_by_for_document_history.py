"""use-replaced-by-for-document-history

Revision ID: 3409f0455c0f
Revises: 0f614efae056
Create Date: 2024-05-24 17:06:16.422822

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "3409f0455c0f"
down_revision = "0f614efae056"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_documents", "replaces")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_documents",
        sa.Column(
            "replaces",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="this document replaces the document with the given id",
        ),
    )
    # ### end Alembic commands ###
