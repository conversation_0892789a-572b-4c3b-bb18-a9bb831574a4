"""update enum value options

Revision ID: 4825205004b6
Revises: c54cb25f8657
Create Date: 2024-03-25 14:45:06.151902

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4825205004b6"
down_revision = "c54cb25f8657"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=mysql.ENUM(
            "KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "TN_AC", "QT_AC", "KG_SQM", "MT_SQM", "L_SQM"
        ),
        type_=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "ML_HA",
            "T_HA",
            "LBS_<PERSON>",
            "GAL_<PERSON>",
            "TN_AC",
            "QT_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "ML_HA",
            "T_HA",
            "LBS_AC",
            "GAL_AC",
            "TN_AC",
            "QT_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        type_=mysql.ENUM("KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "TN_AC", "QT_AC", "KG_SQM", "MT_SQM", "L_SQM"),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
