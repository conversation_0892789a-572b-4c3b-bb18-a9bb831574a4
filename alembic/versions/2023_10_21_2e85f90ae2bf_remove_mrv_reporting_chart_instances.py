"""Remove mrv_reporting_chart_instances

Revision ID: 2e85f90ae2bf
Revises: 76c9bb570fb5
Create Date: 2023-10-21 22:09:09.544746

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "2e85f90ae2bf"
down_revision = "76c9bb570fb5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_reporting_chart_presets", sa.Column("row_id", sa.Integer(), nullable=False))
    op.create_foreign_key(
        "mrv_reporting_chart_presets_ibfk_1",
        "mrv_reporting_chart_presets",
        "mrv_reporting_dashboard_rows",
        ["row_id"],
        ["id"],
    )


def downgrade():
    op.drop_column("mrv_reporting_chart_presets", "row_id")
    op.drop_constraint("mrv_reporting_chart_presets_ibfk_1", "mrv_reporting_chart_presets", type_="foreignkey")
