"""update_group_mnagament roles

Revision ID: 3254fb0ba36e
Revises: 143f770a595e
Create Date: 2024-07-10 21:57:14.046106

"""

# import sqlalchemy as sa
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "3254fb0ba36e"
down_revision = "143f770a595e"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.GET_USER_GROUPS.value,
        Permission.CREATE_USER_GROUP.value,
        Permission.UPDATE_USER_GROUP.value,
        Permission.DELETE_USER_GROUP.value,
    }
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
