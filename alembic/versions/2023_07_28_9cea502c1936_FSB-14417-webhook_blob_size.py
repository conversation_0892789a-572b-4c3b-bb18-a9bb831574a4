"""FSB-14417-webhook_blob_size

Revision ID: 9cea502c1936
Revises: f77fc0f2970e
Create Date: 2023-07-28 08:59:10.887452

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "9cea502c1936"
down_revision = "f77fc0f2970e"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_docusign_webhook", "payload", existing_type=sa.BLOB(), type_=mysql.LONGBLOB(), existing_nullable=False
    )


def downgrade():
    op.alter_column(
        "mrv_docusign_webhook", "payload", existing_type=mysql.LONGBLOB(), type_=sa.BLOB(), existing_nullable=False
    )
