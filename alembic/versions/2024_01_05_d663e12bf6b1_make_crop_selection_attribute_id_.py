"""make crop selection attribute_id nullable

Revision ID: d663e12bf6b1
Revises: 2de85cdd13e0
Create Date: 2024-01-05 11:28:18.986980

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d663e12bf6b1"
down_revision = "2de85cdd13e0"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("mrv_crop_type_selection", "attribute_id", existing_type=mysql.INTEGER(), nullable=True)


def downgrade():
    op.alter_column("mrv_crop_type_selection", "attribute_id", existing_type=mysql.INTEGER(), nullable=False)
