"""easy crud

Revision ID: d137d7923622
Revises: ced36529ce00
Create Date: 2023-11-10 09:40:27.639561

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d137d7923622"
down_revision = "ced36529ce00"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_lookup",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent", sa.String(length=128), nullable=True),
        sa.Column("label", sa.String(length=128), nullable=True),
        sa.Column("name", sa.String(length=128), nullable=True),
        sa.Column("value", sa.String(length=4096), nullable=True),
        sa.Column("description", mysql.TEXT(), nullable=True),
        sa.Column("payload", mysql.LONGBLOB(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_lookup_id"), "mrv_lookup", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_lookup_label"), "mrv_lookup", ["label"], unique=False)
    op.create_index(op.f("ix_mrv_lookup_name"), "mrv_lookup", ["name"], unique=False)
    op.create_index(op.f("ix_mrv_lookup_parent"), "mrv_lookup", ["parent"], unique=False)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_lookup_parent"), table_name="mrv_lookup")
    op.drop_index(op.f("ix_mrv_lookup_name"), table_name="mrv_lookup")
    op.drop_index(op.f("ix_mrv_lookup_label"), table_name="mrv_lookup")
    op.drop_index(op.f("ix_mrv_lookup_id"), table_name="mrv_lookup")
    op.drop_table("mrv_lookup")
