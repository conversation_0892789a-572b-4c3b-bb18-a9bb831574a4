"""drop column mrv_program_modeling_configuration.method

Revision ID: cb27ff2f97c0
Revises: aa366cf25ed6
Create Date: 2025-06-25 15:48:23.385630

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "cb27ff2f97c0"
down_revision = "aa366cf25ed6"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("mrv_program_modeling_configuration", "method")


def downgrade():
    op.add_column(
        "mrv_program_modeling_configuration",
        sa.Column("method", mysql.ENUM("DNDC", "FDCIC", "LUT"), nullable=False, server_default="DNDC"),
    )
