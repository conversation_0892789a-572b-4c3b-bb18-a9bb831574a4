"""Add explore_result_missing to LutUsageReasons.

Revision ID: 7e1f0ff33945
Revises: 6584d9267170
Create Date: 2024-05-14 21:30:51.509032

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7e1f0ff33945"
down_revision = "9e34a255ba4c"
branch_labels = None
depends_on = None


class OldReasons(enum.StrEnum):
    ghg_too_low = "GHG too low"
    ghg_too_high = "GHG too high"
    mrv_couldnt_build_ss_input = "MRV couldn't build Scenarios Service input"
    ss_error = "Scenarios Service returned an error"
    generic_error = "An error occurred"


class NewReasons(enum.StrEnum):
    explore_result_missing = "Explore result not received"
    ghg_too_low = "GHG too low"
    ghg_too_high = "GHG too high"
    mrv_couldnt_build_ss_input = "MRV couldn't build Scenarios Service input"
    ss_error = "Scenarios Service returned an error"
    generic_error = "An error occurred"


def upgrade():
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=sa.Enum(OldReasons),
        type_=sa.Enum(NewReasons),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=sa.Enum(NewReasons),
        type_=sa.Enum(OldReasons),
        existing_nullable=True,
    )
