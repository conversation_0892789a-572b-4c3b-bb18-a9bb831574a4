"""Remove agrian and productionwise ImportDataSources enums

Revision ID: 384839ccc0c3
Revises: 5c69189a1cae
Create Date: 2024-03-11 14:20:08.394715

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "384839ccc0c3"
down_revision = "5c69189a1cae"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_values",
        "source",
        existing_type=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
        ),
        type_=sa.Enum(
            "user",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_values_history",
        "source",
        existing_type=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
        ),
        type_=sa.Enum(
            "user",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_values_history",
        "source",
        existing_type=sa.Enum(
            "user",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        type_=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_values",
        "source",
        existing_type=sa.Enum(
            "user",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        type_=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
