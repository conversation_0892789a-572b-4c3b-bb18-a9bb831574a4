"""Add stage default no practice observation

Revision ID: d93c775af1a7
Revises: 8396f8d9f168
Create Date: 2025-07-15 13:42:23.822025

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d93c775af1a7"
down_revision = "8396f8d9f168"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_stages", sa.Column("default_no_practice_observation", sa.<PERSON>(), server_default="0", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_stages", "default_no_practice_observation")
    # ### end Alembic commands ###
