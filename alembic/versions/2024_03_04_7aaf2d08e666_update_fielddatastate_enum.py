"""update fielddatastate enum

Revision ID: 7aaf2d08e666
Revises: e99fef773630
Create Date: 2024-03-04 14:57:26.287224

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "7aaf2d08e666"
down_revision = "e99fef773630"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_field_facts",
        "state",
        existing_type=mysql.ENUM("contracted", "eligible", "measured", "baseline"),
        type_=mysql.ENUM("contracted", "eligible", "measured", "baseline", "assigned", name="fielddatastate"),
        existing_comment="The state of the field data",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_field_facts",
        "state",
        existing_type=mysql.ENUM("contracted", "eligible", "measured", "baseline", "assigned", name="fielddatastate"),
        type_=mysql.ENUM("contracted", "eligible", "measured", "baseline"),
        existing_comment="The state of the field data",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
