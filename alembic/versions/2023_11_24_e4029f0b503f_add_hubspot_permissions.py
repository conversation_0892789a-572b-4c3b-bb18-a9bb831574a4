"""Add hubspot permissions

Revision ID: e4029f0b503f
Revises: 73b337ad6388
Create Date: 2023-11-24 01:55:10.999521

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "e4029f0b503f"
down_revision = "73b337ad6388"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER.value: {
        Permission.GET_HUBSPOT_MRV_USER_TYPE.value,
        Permission.CREATE_HUBSPOT_MRV_USER_TYPE.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_HUBSPOT_MRV_USER_TYPE.value,
        Permission.CREATE_HUBSPOT_MRV_USER_TYPE.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
