"""Create table mrv_dndc_simulation_requests to track our requests to Measure and Explore APIs.

Revision ID: e309a4d98b78
Revises: e17c53d3d0c5
Create Date: 2024-02-08 18:02:31.054318

"""

from enum import auto, StrEnum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e309a4d98b78"
down_revision = "f403b276cd4e"
branch_labels = None
depends_on = None


class ScenariosServiceApi(StrEnum):
    measure_api = auto()
    explore_api = auto()


def upgrade():
    op.create_table(
        "mrv_dndc_simulation_requests",
        sa.Column("id", sa.Integer, nullable=False),
        sa.Column("task_id", sa.String(length=36), sa.<PERSON>ey("mrv_dndc_tasks.id"), nullable=False),
        sa.Column("field_id", sa.Integer, sa.<PERSON>("mrv_fields.id"), nullable=False),
        sa.Column("is_error", sa.<PERSON>, nullable=False),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("ss_api", sa.Enum(ScenariosServiceApi), nullable=False),
        sa.Column("ss_field_request_id", sa.Integer, nullable=True),
        sa.Column("ss_session_name", sa.String(length=100), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_dndc_simulation_requests_id"), "mrv_dndc_simulation_requests", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_dndc_simulation_requests_task_id"),
        "mrv_dndc_simulation_requests",
        ["task_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_dndc_simulation_requests_field_id"),
        "mrv_dndc_simulation_requests",
        ["field_id"],
        unique=False,
    )


def downgrade():
    op.drop_table("mrv_dndc_simulation_requests")
