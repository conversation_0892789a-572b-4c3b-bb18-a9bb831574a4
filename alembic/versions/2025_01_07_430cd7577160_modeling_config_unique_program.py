"""Add unique program_id index to modeling configs.

Revision ID: 430cd7577160
Revises: 352abebe379b
Create Date: 2025-01-07 16:39:45.821822

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "430cd7577160"
down_revision = "3d2b1238c444"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """DELETE FROM mrv_program_modeling_configuration
        WHERE id NOT IN (
            SELECT id FROM (
                SELECT MAX(id) AS id
                FROM mrv_program_modeling_configuration
                GROUP BY program_id
            ) AS subquery
    );"""
    )
    op.create_index(
        "unique_program_id",
        "mrv_program_modeling_configuration",
        ["program_id"],
        unique=True,
    )
    op.drop_column("mrv_program_modeling_configuration", "intentions_data_collection_style")


class IntentionsDataCollectionStyle(enum.StrEnum):
    PRACTICES = enum.auto()
    EVENTS = enum.auto()


def downgrade():
    op.drop_index(
        "unique_program_id",
        "mrv_program_modeling_configuration",
    )
    op.add_column(
        "mrv_program_modeling_configuration",
        sa.Column("intentions_data_collection_style", sa.Enum(IntentionsDataCollectionStyle), nullable=True),
    )
