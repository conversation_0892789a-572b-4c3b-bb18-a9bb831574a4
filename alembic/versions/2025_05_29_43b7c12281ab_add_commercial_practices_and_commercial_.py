"""Add commercial practices and commercial regions tables

Revision ID: 43b7c12281ab
Revises: 66ab2f5f84cc
Create Date: 2025-05-29 16:40:56.691594

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "43b7c12281ab"
down_revision = "66ab2f5f84cc"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_commercial_practices",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("commercial_id", sa.Integer(), nullable=False),
        sa.Column("practice_change", sa.String(length=255), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_commercial_practices_id"), "mrv_commercial_practices", ["id"], unique=False)
    op.create_table(
        "mrv_commercial_regions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("commercial_id", sa.Integer(), nullable=False),
        sa.Column("region_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_commercial_regions_id"), "mrv_commercial_regions", ["id"], unique=False)
    op.alter_column(
        "mrv_phase_commercials",
        "payment_for",
        existing_type=mysql.ENUM("FIELD_AREA", "ESTIMATED_GHG"),
        type_=sa.Enum("FIELD_AREA", "FIELD_PRACTICE", "ESTIMATED_GHG", name="commercialspaymentfortype"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_phase_commercials",
        "payment_for",
        existing_type=sa.Enum("FIELD_AREA", "FIELD_PRACTICE", "ESTIMATED_GHG", name="commercialspaymentfortype"),
        type_=mysql.ENUM("FIELD_AREA", "ESTIMATED_GHG"),
        existing_nullable=False,
    )
    op.drop_index(op.f("ix_mrv_commercial_regions_id"), table_name="mrv_commercial_regions")
    op.drop_table("mrv_commercial_regions")
    op.drop_index(op.f("ix_mrv_commercial_practices_id"), table_name="mrv_commercial_practices")
    op.drop_table("mrv_commercial_practices")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
