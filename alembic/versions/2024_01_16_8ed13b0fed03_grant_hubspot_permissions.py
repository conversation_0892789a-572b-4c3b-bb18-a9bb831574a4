"""grant-hubspot-permissions

Revision ID: 8ed13b0fed03
Revises: cb3cb26430a3
Create Date: 2024-01-16 18:44:47.461782

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "8ed13b0fed03"
down_revision = "cb3cb26430a3"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_HUBSPOT_MRV_USER_TYPE.value,
        Permission.CREATE_HUBSPOT_MRV_USER_TYPE.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_HUBSPOT_MRV_USER_TYPE.value,
        Permission.CREATE_HUBSPOT_MRV_USER_TYPE.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
