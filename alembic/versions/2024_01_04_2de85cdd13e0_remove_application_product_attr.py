"""remove application product attr

Revision ID: 2de85cdd13e0
Revises: d7e2fafb2587
Create Date: 2024-01-04 13:52:40.310678

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "2de85cdd13e0"
down_revision = "d7e2fafb2587"
branch_labels = None
depends_on = None


def upgrade():
    op.execute('DELETE FROM mrv_attribute_options_defaults where type = "application_product_type"')
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_attribute_options_defaults",
        "type",
        existing_type=mysql.ENUM(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_product_type",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_product_type_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
        ),
        type_=sa.Enum(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
            name="attributetypes",
        ),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_attributes",
        "type",
        existing_type=mysql.ENUM(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_product_type",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_product_type_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
        ),
        type_=sa.Enum(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
            name="attributetypes",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_attributes",
        "type",
        existing_type=sa.Enum(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
            name="attributetypes",
        ),
        type_=mysql.ENUM(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_product_type",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_product_type_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
        ),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_attribute_options_defaults",
        "type",
        existing_type=sa.Enum(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
            name="attributetypes",
        ),
        type_=mysql.ENUM(
            "summer_crop_type",
            "summer_harvest_date",
            "summer_dry_yield",
            "summer_residue_harvested",
            "summer_planting_date",
            "winter_crop_commitment",
            "winter_crop_type",
            "winter_harvest_date",
            "winter_dry_yield",
            "winter_residue_harvested",
            "winter_planting_date",
            "fall_tillage_practice",
            "fall_tillage_date",
            "fall_tillage_depth",
            "spring_tillage_practice",
            "spring_tillage_date",
            "spring_tillage_depth",
            "planting_season",
            "planting_date",
            "harvest_date",
            "crop_usage",
            "other",
            "string",
            "number",
            "date",
            "bool_",
            "harvest",
            "planting",
            "crop_type",
            "crop_yield",
            "residue_harvested",
            "tillage_period",
            "tillage_practice",
            "tillage_date",
            "tillage_depth",
            "tillage_event",
            "strip_fraction",
            "soil_inversion",
            "spring_tillage",
            "fall_tillage",
            "winter_crop",
            "practice",
            "summer_crop",
            "record_year",
            "winter_crop_termination",
            "application_date",
            "application_product",
            "application_rate",
            "application_rate_type",
            "application_rate_unit",
            "application_area",
            "application_product_type",
            "application_depth",
            "application_method",
            "application_rate_unit_vanity",
            "application_product_type_vanity",
            "application_rate_vanity",
            "yield_rate_unit",
            "application_area_vanity",
            "water_amount",
            "water_amount_unit",
            "additive_one",
            "additive_two",
            "additives",
            "farm_number",
            "cover_crop_mix",
            "start_date",
            "end_date",
            "irrigation_method",
            "irrigation_enabled",
            "irrigation_rate_unit",
            "flood_pct",
            "subsurface_drip_depth",
            "subsurface_drip_depth_unit",
            "energy_source",
            "fuel_type",
            "pesticide_farmprint_gaff",
            "production_system",
            "change_type",
            "head_count",
            "livestock_class",
            "farm_name",
            "area_sown",
            "residue_burnt",
            "area_tilled_total_pct",
            "area_tilled_for_pasture_renewal_pct",
            "nutrient_management_enabled",
            "landscape_modifications_fuel_usage",
            "landscape_modifications_fuel_type",
            "irrigation_fuel_usage",
            "irrigation_fuel_type",
            "irrigation_electricity_usage",
            "irrigation_electricity_source",
            "irrigation_electricity_grid_name",
            "total_area",
            "total_energy_usage",
            "total_fuel_usage",
            "skip_assign_practice",
            "seeding_rate",
            "seeding_rate_unit",
            "seed_variety",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
