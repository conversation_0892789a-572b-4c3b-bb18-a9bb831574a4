"""add-stage-optional-metrics

Revision ID: 24d53f8dca0f
Revises: 4e4c42e99011
Create Date: 2024-03-21 15:47:37.236053

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "24d53f8dca0f"
down_revision = "4e4c42e99011"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_stages", sa.Column("enabled_metrics", sa.JSON(), nullable=True))


def downgrade():
    op.drop_column("mrv_stages", "enabled_metrics")
