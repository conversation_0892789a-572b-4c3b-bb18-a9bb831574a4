"""generalise coop

Revision ID: 8315457a6977
Revises: d663e12bf6b1
Create Date: 2023-12-20 10:59:58.410722

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "8315457a6977"
down_revision = "d663e12bf6b1"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("UPDATE mrv_program_custom_reg_inputs SET input_key = 'Restriction' WHERE input_key = 'Coop'")
    op.execute(
        "UPDATE mrv_program_custom_reg_inputs SET input_key = 'Account ID' WHERE input_key = 'Cargill Account ID'"
    )
    op.alter_column(
        "mrv_role_user_restrictions",
        "restriction_type",
        existing_type=mysql.ENUM("CUSTOM_INPUT_COOP"),
        type_=sa.Enum("CUSTOM_INPUT_COOP", "CUSTOM_INPUT", name="restrictiontypes"),
        existing_nullable=False,
    )
    op.execute(
        "UPDATE mrv_role_user_restrictions SET restriction_type = 'CUSTOM_INPUT' WHERE restriction_type = 'CUSTOM_INPUT_COOP'"
    )
    op.alter_column(
        "mrv_role_user_restrictions",
        "restriction_type",
        existing_type=mysql.ENUM("CUSTOM_INPUT_COOP", "CUSTOM_INPUT"),
        type_=sa.Enum("CUSTOM_INPUT", name="restrictiontypes"),
        existing_nullable=False,
    )
    op.add_column(
        "mrv_role_user_restrictions",
        sa.Column(
            "restriction_source_id",
            sa.Integer(),
            nullable=True,
            comment="id of the data source will be used for restriction e.g. mrv_program_custom_reg_inputs.id if restriction_type is CUSTOM_INPUT",
        ),
    )
    op.execute(
        "UPDATE mrv_role_user_restrictions rs SET rs.restriction_source_id = ("
        "SELECT i.id FROM mrv_roles_users u JOIN mrv_program_custom_reg_inputs i ON i.program_id = u.program_id "
        "WHERE i.input_key = 'Restriction' AND u.id = rs.role_user_id LIMIT 1"
        ") WHERE restriction_type = 'CUSTOM_INPUT'"
    )


def downgrade():
    op.drop_column("mrv_role_user_restrictions", "restriction_source_id")
    op.execute("UPDATE mrv_program_custom_reg_inputs SET input_key = 'Coop'  WHERE input_key = 'Restriction'")
    op.execute(
        "UPDATE mrv_program_custom_reg_inputs SET input_key = 'Cargill Account ID' WHERE input_key = 'Account ID'"
    )
    op.alter_column(
        "mrv_role_user_restrictions",
        "restriction_type",
        existing_type=sa.Enum("CUSTOM_INPUT", name="restrictiontypes"),
        type_=mysql.ENUM("CUSTOM_INPUT_COOP", "CUSTOM_INPUT"),
        existing_nullable=False,
    )
    op.execute(
        "UPDATE mrv_role_user_restrictions SET restriction_type = 'CUSTOM_INPUT_COOP' WHERE restriction_type = 'CUSTOM_INPUT'"
    )
    op.alter_column(
        "mrv_role_user_restrictions",
        "restriction_type",
        existing_type=sa.Enum("CUSTOM_INPUT_COOP", "CUSTOM_INPUT", name="restrictiontypes"),
        type_=mysql.ENUM("CUSTOM_INPUT_COOP"),
        existing_nullable=False,
    )
