"""refactor-role-permission-schema

Revision ID: b19a7031240a
Revises: eb8e231ac7d3
Create Date: 2023-11-16 20:31:45.127018

"""

import sqlalchemy as sa
from sqlalchemy import update
from sqlalchemy.orm import Session

from alembic import op
from permissions.enums import DefaultRoles, Permission
from permissions.model import PermissionType, Roles

# revision identifiers, used by Alembic.
revision = "b19a7031240a"
down_revision = "eb8e231ac7d3"
branch_labels = None
depends_on = None


def upgrade():
    try:
        bind = op.get_bind()
        session = Session(bind=bind)
        session.execute(
            update(Roles).values(restricted=True).where(Roles.name == DefaultRoles.RESTRICTED_USER_ADMIN.value)
        )
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e

    op.alter_column(
        "mrv_roles_permissions",
        "permission",
        existing_type=sa.Enum(Permission),
        type_=PermissionType(length=255),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_roles_permissions",
        "permission",
        existing_type=PermissionType(length=255),
        type_=sa.Enum(Permission),
        existing_nullable=False,
    )
