"""Add MRV Notification Source

Revision ID: 16bf0aec2a5a
Revises: 3b0ee43c04fa
Create Date: 2024-05-29 23:04:48.650301

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "16bf0aec2a5a"
down_revision = "3b0ee43c04fa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=mysql.ENUM("OPTIS", "DNDC"),
        type_=sa.Enum("OPTIS", "DNDC", "MRV", name="notificationsources"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=sa.Enum("OPTIS", "DNDC", "MRV", name="notificationsources"),
        type_=mysql.ENUM("OPTIS", "DNDC"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
