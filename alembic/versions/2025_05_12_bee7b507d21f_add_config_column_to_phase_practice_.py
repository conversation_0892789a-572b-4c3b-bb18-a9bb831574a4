"""add config column to phase_practice_config

Revision ID: bee7b507d21f
Revises: 6b7f502b1289
Create Date: 2025-05-12 19:20:23.934403

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "bee7b507d21f"
down_revision = "6b7f502b1289"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_phase_practice_config",
        sa.Column(
            "config",
            sa.JSON(),
            nullable=True,
            comment="Configuration for the practice, such as cover crop mapping rules",
        ),
    )


def downgrade():
    op.drop_column("mrv_phase_practice_config", "config")
