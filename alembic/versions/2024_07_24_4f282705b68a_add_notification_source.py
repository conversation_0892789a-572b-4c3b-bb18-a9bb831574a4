"""add notification source

Revision ID: 4f282705b68a
Revises: a2b359b7b5f7
Create Date: 2024-07-24 15:33:58.062494

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "4f282705b68a"
down_revision = "a2b359b7b5f7"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=mysql.ENUM("OPTIS", "DNDC", "MRV", "MRV_EXPORT"),
        type_=sa.Enum("OPTIS", "DNDC", "MRV", "MRV_EXPORT", "MRV_VALUES_OVERWRITE", name="notificationsources"),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=sa.Enum("OPTIS", "DNDC", "MRV", "MRV_EXPORT", "MRV_VALUES_OVERWRITE", name="notificationsources"),
        type_=mysql.ENUM("OPTIS", "DNDC", "MRV", "MRV_EXPORT"),
        existing_nullable=False,
    )
