"""Remove field_md5 from field event association unique constraint.

Revision ID: d01ec50d5c6c
Revises: c2a22a6c81d7
Create Date: 2025-04-28 11:18:13.329020

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d01ec50d5c6c"
down_revision = "c2a22a6c81d7"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("_field_event_uc", "mrv_field_event_associations", type_="unique")
    op.create_unique_constraint("_field_event_uc", "mrv_field_event_associations", ["field_id", "ses_event_id"])


def downgrade():
    op.drop_constraint("_field_event_uc", "mrv_field_event_associations", type_="unique")
    op.create_unique_constraint(
        "_field_event_uc", "mrv_field_event_associations", ["ses_event_id", "field_md5", "field_id"]
    )
