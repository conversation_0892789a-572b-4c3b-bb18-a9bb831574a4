"""include programs in program outcomes

Revision ID: 94d4e111f93c
Revises: 740b92f3ed45
Create Date: 2024-09-25 09:21:48.931387

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "94d4e111f93c"
down_revision = "740b92f3ed45"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_crop_level_outcomes", sa.Column("program_id", sa.Integer(), nullable=True))
    op.add_column("mrv_crop_level_outcomes", sa.Column("task_id", sa.String(length=36), nullable=True))
    op.create_index(
        op.f("ix_mrv_crop_level_outcomes_program_id"), "mrv_crop_level_outcomes", ["program_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_crop_level_outcomes_updated_at"), "mrv_crop_level_outcomes", ["updated_at"], unique=False
    )
    op.create_foreign_key(None, "mrv_crop_level_outcomes", "mrv_programs", ["program_id"], ["id"])
    op.create_foreign_key(None, "mrv_crop_level_outcomes", "mrv_dndc_tasks", ["task_id"], ["id"])
    op.drop_column("mrv_crop_level_outcomes", "job_id")
    op.add_column("mrv_field_level_outcomes", sa.Column("program_id", sa.Integer(), nullable=True))
    op.add_column("mrv_field_level_outcomes", sa.Column("task_id", sa.String(length=36), nullable=True))
    op.create_index(
        op.f("ix_mrv_field_level_outcomes_program_id"), "mrv_field_level_outcomes", ["program_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_field_level_outcomes_updated_at"), "mrv_field_level_outcomes", ["updated_at"], unique=False
    )
    op.create_foreign_key(None, "mrv_field_level_outcomes", "mrv_programs", ["program_id"], ["id"])
    op.create_foreign_key(None, "mrv_field_level_outcomes", "mrv_dndc_tasks", ["task_id"], ["id"])
    op.drop_column("mrv_field_level_outcomes", "job_id")


def downgrade():
    op.add_column("mrv_field_level_outcomes", sa.Column("job_id", mysql.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, "mrv_field_level_outcomes", type_="foreignkey")
    op.drop_constraint(None, "mrv_field_level_outcomes", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_field_level_outcomes_updated_at"), table_name="mrv_field_level_outcomes")
    op.drop_index(op.f("ix_mrv_field_level_outcomes_program_id"), table_name="mrv_field_level_outcomes")
    op.drop_column("mrv_field_level_outcomes", "task_id")
    op.drop_column("mrv_field_level_outcomes", "program_id")
    op.add_column("mrv_crop_level_outcomes", sa.Column("job_id", mysql.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, "mrv_crop_level_outcomes", type_="foreignkey")
    op.drop_constraint(None, "mrv_crop_level_outcomes", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_crop_level_outcomes_updated_at"), table_name="mrv_crop_level_outcomes")
    op.drop_index(op.f("ix_mrv_crop_level_outcomes_program_id"), table_name="mrv_crop_level_outcomes")
    op.drop_column("mrv_crop_level_outcomes", "task_id")
    op.drop_column("mrv_crop_level_outcomes", "program_id")
