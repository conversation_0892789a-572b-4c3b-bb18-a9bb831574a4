"""add field level inventory outcomes

Revision ID: fd6d46ee982d
Revises: 528db93d354a
Create Date: 2025-04-03 09:55:14.874856

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "fd6d46ee982d"
down_revision = "528db93d354a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("net_emissions_factor", sa.Float(), nullable=True))
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("ghg_emissions_factor", sa.Float(), nullable=True))
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("soc_emissions_factor", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("mrv_field_level_inventory_outcomes", "soc_emissions_factor")
    op.drop_column("mrv_field_level_inventory_outcomes", "ghg_emissions_factor")
    op.drop_column("mrv_field_level_inventory_outcomes", "net_emissions_factor")
