"""Change ConflictStatus enums

Revision ID: efa40b22eafd
Revises: b44f56d4fe78
Create Date: 2023-09-05 12:06:14.553317

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "efa40b22eafd"
down_revision = "b44f56d4fe78"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_conflict_resolution_history",
        "status",
        existing_type=mysql.ENUM("AGREED_WITH_REGROW", "AGREED_WITH_PRODUCER", "UNRESOLVED"),
        type_=sa.Enum("pending", "resolved", "unresolved", name="conflictstatus"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_conflict_resolution_history",
        "status",
        existing_type=sa.Enum("pending", "resolved", "unresolved", name="conflictstatus"),
        type_=mysql.ENUM("AGREED_WITH_REGROW", "AGREED_WITH_PRODUCER", "UNRESOLVED"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
