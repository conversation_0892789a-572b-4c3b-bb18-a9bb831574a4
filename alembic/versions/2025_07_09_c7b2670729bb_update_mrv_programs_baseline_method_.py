"""update mrv_programs.baseline_method enum values

Revision ID: c7b2670729bb
Revises: a4a41776b73b
Create Date: 2025-07-09 11:29:27.278757

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "c7b2670729bb"
down_revision = "12410be8a7e7"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED"),
        type_=sa.Enum(
            "ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE", name="baselinemethod"
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED_RICE"),
        type_=sa.Enum(
            "ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE", name="baselinemethod"
        ),
        existing_nullable=True,
    )
