"""Update set of SES migration field statuses.

Revision ID: 0115dad6041f
Revises: 95b0561c14ee
Create Date: 2025-03-10 13:26:02.085456

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "0115dad6041f"
down_revision = "f82621b6cdd0"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_ses_migration_results",
        "status",
        existing_type=mysql.ENUM("enqueued", "started", "failed", "succeeded"),
        type_=sa.Enum(
            "enqueued", "preparing_events", "sending_events", "failed", "succeeded", name="sesfieldmigrationstatus"
        ),
        existing_nullable=False,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_ses_migration_results",
        "status",
        existing_type=sa.Enum(
            "enqueued", "preparing_events", "sending_events", "failed", "succeeded", name="sesfieldmigrationstatus"
        ),
        type_=mysql.ENUM("enqueued", "started", "failed", "succeeded"),
        existing_nullable=False,
    )
