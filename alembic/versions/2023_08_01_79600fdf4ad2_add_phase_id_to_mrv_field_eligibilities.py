"""Add phase id to mrv_field_eligibilities

Revision ID: 79600fdf4ad2
Revises: 2e511385f1b9
Create Date: 2023-08-01 17:37:01.227950

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "79600fdf4ad2"
down_revision = "2e511385f1b9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_field_eligibilities", sa.Column("phase_id", sa.Integer(), nullable=True))
    op.alter_column("mrv_field_eligibilities", "stage_id", existing_type=mysql.INTEGER(display_width=11), nullable=True)
    op.create_index(
        op.f("ix_mrv_field_eligibilities_created_at"), "mrv_field_eligibilities", ["created_at"], unique=False
    )
    op.create_foreign_key(None, "mrv_field_eligibilities", "mrv_phases", ["phase_id"], ["id"])

    # Data migration:
    # Set phase_id for each stage id already in table, because we later want to make phase id non-null
    connection = op.get_bind()
    connection.execute(
        sa.sql.text(
            """
            UPDATE mrv_field_eligibilities elig JOIN mrv_stages stage ON elig.stage_id = stage.id
            SET elig.phase_id = stage.phase_id
            """
        )
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_field_eligibilities", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_field_eligibilities_created_at"), table_name="mrv_field_eligibilities")
    op.alter_column(
        "mrv_field_eligibilities", "stage_id", existing_type=mysql.INTEGER(display_width=11), nullable=False
    )
    op.drop_column("mrv_field_eligibilities", "phase_id")
    # ### end Alembic commands ###
