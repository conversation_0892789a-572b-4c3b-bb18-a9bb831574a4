"""Add config col to chart preset

Revision ID: 3cc0c19231f0
Revises: 121850f5e4d8
Create Date: 2023-08-10 15:31:56.291594

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3cc0c19231f0"
down_revision = "121850f5e4d8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_reporting_chart_presets",
        sa.Column("option_components", sa.JSON(), nullable=True),
    )
    op.add_column(
        "mrv_reporting_chart_presets",
        sa.Column("table_components", sa.JSON(), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_chart_presets", "table_components")
    op.drop_column("mrv_reporting_chart_presets", "option_components")
    # ### end Alembic commands ###
