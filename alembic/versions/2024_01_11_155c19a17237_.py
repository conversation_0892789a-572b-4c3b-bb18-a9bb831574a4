"""empty message

Revision ID: 155c19a17237
Revises: 8315457a6977
Create Date: 2024-01-11 10:39:51.217109

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "155c19a17237"
down_revision = "8315457a6977"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_documents",
        sa.Column(
            "is_public",
            sa.Integer(),
            nullable=True,
            comment="flag to indicate if the document is public or not",
        ),
    )


def downgrade():
    op.drop_column("mrv_documents", "is_public")
