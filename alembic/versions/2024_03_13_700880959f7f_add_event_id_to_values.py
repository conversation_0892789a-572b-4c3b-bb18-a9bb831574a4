"""add event_id to values

Revision ID: 700880959f7f
Revises: e2a9b471935e
Create Date: 2024-03-13 13:21:45.833031

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "700880959f7f"
down_revision = "e2a9b471935e"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_values", sa.Column("event_id", sa.Uuid(), nullable=True))
    op.create_index(op.f("ix_mrv_values_event_id"), "mrv_values", ["event_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_mrv_values_event_id"), table_name="mrv_values")
    op.drop_column("mrv_values", "event_id")
