"""add monitor prefill enabled to project

Revision ID: 729aaf9766e9
Revises: 294bad080a53
Create Date: 2025-03-31 15:38:45.766437

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "729aaf9766e9"
down_revision = "294bad080a53"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_projects", sa.Column("monitor_prefill_last_run", sa.DateTime(), nullable=True))
    op.drop_column("mrv_stages", "monitor_prefill_last_run")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_stages", sa.Column("monitor_prefill_last_run", mysql.DATETIME(), nullable=True))
    op.drop_column("mrv_projects", "monitor_prefill_last_run")
    # ### end Alembic commands ###
