"""Add deleted at to commercial practices and regions

Revision ID: 4c65278a88c9
Revises: 0e8f04cc9b50
Create Date: 2025-06-10 08:54:46.413817

"""

import sqlalchemy as sa

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "4c65278a88c9"
down_revision = "0e8f04cc9b50"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_commercial_practices", sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True))
    op.create_index(
        op.f("ix_mrv_commercial_practices_deleted_at"), "mrv_commercial_practices", ["deleted_at"], unique=False
    )
    op.add_column("mrv_commercial_regions", sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True))
    op.create_index(
        op.f("ix_mrv_commercial_regions_deleted_at"), "mrv_commercial_regions", ["deleted_at"], unique=False
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_commercial_regions_deleted_at"), table_name="mrv_commercial_regions")
    op.drop_column("mrv_commercial_regions", "deleted_at")
    op.drop_index(op.f("ix_mrv_commercial_practices_deleted_at"), table_name="mrv_commercial_practices")
    op.drop_column("mrv_commercial_practices", "deleted_at")
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
