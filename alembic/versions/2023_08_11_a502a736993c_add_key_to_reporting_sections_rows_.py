"""Add key to reporting Sections, Rows, Charts

Revision ID: a502a736993c
Revises: c4ae814b93f2
Create Date: 2023-08-11 21:06:17.234983

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a502a736993c"
down_revision = "c4ae814b93f2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_chart_presets", sa.Column("key", sa.String(length=255), nullable=True))
    op.create_unique_constraint(None, "mrv_reporting_chart_presets", ["key"])
    op.add_column("mrv_reporting_dashboard_rows", sa.Column("key", sa.String(length=255), nullable=True))
    op.create_unique_constraint(None, "mrv_reporting_dashboard_rows", ["key"])
    op.add_column("mrv_reporting_dashboard_sections", sa.Column("key", sa.String(length=255), nullable=True))
    op.create_unique_constraint(None, "mrv_reporting_dashboard_sections", ["key"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_reporting_dashboard_sections", type_="unique")
    op.drop_column("mrv_reporting_dashboard_sections", "key")
    op.drop_constraint(None, "mrv_reporting_dashboard_rows", type_="unique")
    op.drop_column("mrv_reporting_dashboard_rows", "key")
    op.drop_constraint(None, "mrv_reporting_chart_presets", type_="unique")
    op.drop_column("mrv_reporting_chart_presets", "key")
    # ### end Alembic commands ###
