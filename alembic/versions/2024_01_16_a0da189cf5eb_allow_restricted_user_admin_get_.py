"""allow-restricted-user-admin-get-restriction

Revision ID: a0da189cf5eb
Revises: 4a43b5cba1f9
Create Date: 2024-01-16 14:53:45.589043

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a0da189cf5eb"
down_revision = "4a43b5cba1f9"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_USER_RESTRICTION.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
