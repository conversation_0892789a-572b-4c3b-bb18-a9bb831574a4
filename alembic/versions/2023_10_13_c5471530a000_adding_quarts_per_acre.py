"""Adding Quarts/Ac

Revision ID: c5471530a000
Revises: 6f43c9787077
Create Date: 2023-10-13 10:18:39.286764

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "c5471530a000"
down_revision = "6f43c9787077"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=mysql.ENUM("KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "TN_AC", "KG_SQM", "MT_SQM", "L_SQM"),
        type_=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "LBS_AC",
            "GAL_AC",
            "TN_AC",
            "QT_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_rate_unit",
        existing_type=sa.Enum(
            "KG_HA",
            "MT_HA",
            "L_HA",
            "LBS_AC",
            "GAL_AC",
            "TN_AC",
            "QT_AC",
            "KG_SQM",
            "MT_SQM",
            "L_SQM",
            name="applicationrateunit",
        ),
        type_=mysql.ENUM("KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "TN_AC", "KG_SQM", "MT_SQM", "L_SQM"),
        existing_nullable=True,
    )
