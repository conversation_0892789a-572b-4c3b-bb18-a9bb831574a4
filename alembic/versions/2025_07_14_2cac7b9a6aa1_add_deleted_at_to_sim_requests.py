"""Add deleted_at to mrv_dndc_simulation_requests.

Revision ID: 2cac7b9a6aa1
Revises: 36f9a4ba6cf6
Create Date: 2025-07-14 11:44:34.652348

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "2cac7b9a6aa1"
down_revision = "36f9a4ba6cf6"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_dndc_simulation_requests", sa.Column("deleted_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("mrv_dndc_simulation_requests", "deleted_at")
