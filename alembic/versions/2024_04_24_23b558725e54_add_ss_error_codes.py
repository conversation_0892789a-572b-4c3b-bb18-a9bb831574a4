"""Add SS integration error codes

Revision ID: 23b558725e54
Revises: 372c25dfcdbf
Create Date: 2024-04-24 23:58:47.607851

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "23b558725e54"
down_revision = "372c25dfcdbf"
branch_labels = None
depends_on = None


class OldErrorCodes(enum.StrEnum):
    unsupported_crop = enum.auto()
    unsupported_product_type = enum.auto()
    scenarios_service_unavailable = enum.auto()
    boundaries_service_unavailable = enum.auto()
    invalid_management_data = enum.auto()
    overlapping_commodity_crop_periods = enum.auto()
    missing_baseline_year = enum.auto()
    generic_scenarios_service_failure = enum.auto()


class NewErrorCodes(enum.StrEnum):
    unsupported_crop = enum.auto()
    unsupported_product_type = enum.auto()
    scenarios_service_unavailable = enum.auto()
    scenarios_service_validation_failure = enum.auto()
    boundaries_service_unavailable = enum.auto()
    invalid_management_data = enum.auto()
    overlapping_commodity_crop_periods = enum.auto()
    missing_baseline_year = enum.auto()
    generic_scenarios_service_failure = enum.auto()
    event_outside_phase_period = enum.auto()


def upgrade():
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(OldErrorCodes),
        type_=sa.Enum(NewErrorCodes),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(NewErrorCodes),
        type_=sa.Enum(OldErrorCodes),
        existing_nullable=True,
    )
