"""Update program 253 field baseline years

Revision ID: 8fdac392b66c
Revises: d314013c176c
Create Date: 2025-03-21 10:23:27.150251

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "8fdac392b66c"
down_revision = "d314013c176c"
branch_labels = None
depends_on = None

RETURNING_FIELD_IDS = [80963, 80964, 80965, 80970, 80972, 80974, 80978, 80979, 80980, 80981]
RETURNING_FIELD_IDS_STR = ",".join(map(str, RETURNING_FIELD_IDS))


def upgrade():
    op.execute(
        f"""UPDATE mrv_fields_baseline mfb
        SET
            mfb.baseline_year = 2022,
            mfb.is_returning = 1
        WHERE mfb.field_id IN ({RETURNING_FIELD_IDS_STR});"""  # nosec
    )


def downgrade():
    pass
