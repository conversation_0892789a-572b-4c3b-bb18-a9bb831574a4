"""new permission for project new user creation

Revision ID: a8d3daeed526
Revises: b4479874fc41
Create Date: 2024-06-19 12:52:31.137286

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a8d3daeed526"
down_revision = "b4479874fc41"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.CREATE_PROJECT_WITH_NEW_USER.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.CREATE_PROJECT_WITH_NEW_USER.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
