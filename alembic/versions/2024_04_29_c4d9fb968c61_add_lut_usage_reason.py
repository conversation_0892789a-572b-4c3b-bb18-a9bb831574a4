"""Add generic_error to lut_usage_reason

Revision ID: c4d9fb968c61
Revises: a4174e4cd80c
Create Date: 2024-04-29 09:16:10.864763

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c4d9fb968c61"
down_revision = "a4174e4cd80c"
branch_labels = None
depends_on = None


class OldLutUsageReasons(enum.StrEnum):
    ghg_too_low = "GHG too low"
    ghg_too_high = "GHG too high"
    mrv_couldnt_build_ss_input = "MRV couldn't build Scenarios Service input"
    ss_error = "Scenarios Service returned an error"


class NewLutUsageReasons(enum.StrEnum):
    ghg_too_low = "GHG too low"
    ghg_too_high = "GHG too high"
    mrv_couldnt_build_ss_input = "MRV couldn't build Scenarios Service input"
    ss_error = "Scenarios Service returned an error"
    generic_error = "An error occurred"


def upgrade():
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=sa.Enum(OldLutUsageReasons),
        type_=sa.Enum(NewLutUsageReasons),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=sa.Enum(NewLutUsageReasons),
        type_=sa.Enum(OldLutUsageReasons),
        existing_nullable=True,
    )
