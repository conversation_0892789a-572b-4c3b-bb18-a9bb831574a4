"""Add required_years_of_history

Revision ID: bd42f1645c1a
Revises: 6ed549e6a190
Create Date: 2024-12-18 14:03:52.669366

"""

import sqlalchemy as sa

from alembic import op
from programs.enums import AccountingMethod, ProgramTemplate

# revision identifiers, used by Alembic.
revision = "bd42f1645c1a"
down_revision = "23b1167c9141"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_programs",
        sa.Column(
            "required_years_of_history", sa.<PERSON>nteger(), nullable=True, comment="Only relevant for EDBC programs"
        ),
    )
    # This constraint says that
    #   required_years_of_history can only be 3, 4, 5 or NULL, but it cannot be NULL if accounting_method is in
    #   ('intervention', 'inventory')
    op.create_check_constraint(
        "ck_required_years_of_history",
        "mrv_programs",
        # This constraint can be confusing. The first condition, `required_years_of_history in (3,4,5)` is
        # true (it passes) even if `required_years_of_history` is NULL. To check that it isn't NULL when
        # `accounting_method` is in ('intervention', 'inventory') and program_template is 'event_based',
        # the second condition is added.
        sa.Column("required_years_of_history").in_([3, 4, 5])
        & (
            (
                sa.Column("accounting_method").is_(None)
                | (sa.Column("accounting_method") == AccountingMethod.biofuels.value)
                | (sa.Column("program_template") == ProgramTemplate.legacy.value)
            )
            | sa.Column("required_years_of_history").isnot(None)
        ),
    )


def downgrade():
    op.drop_constraint("ck_required_years_of_history", "mrv_programs", type_="check")
    op.drop_column("mrv_programs", "required_years_of_history")
