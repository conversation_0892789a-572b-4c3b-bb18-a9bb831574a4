"""create-mrv-groups-admins-table

Revision ID: 8be522c017a4
Revises: c2ec09d2e102
Create Date: 2024-06-06 14:32:38.936563

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8be522c017a4"
down_revision = "c2ec09d2e102"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_groups_admins",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("group_id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["group_id"],
            ["mrv_user_groups.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("group_id", "user_id", "deleted_at", name="group_admin_id"),
    )
    op.create_index(op.f("ix_mrv_groups_admins_deleted_at"), "mrv_groups_admins", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_groups_admins_group_id"), "mrv_groups_admins", ["group_id"], unique=False)
    op.create_index(op.f("ix_mrv_groups_admins_id"), "mrv_groups_admins", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_groups_admins_user_id"), "mrv_groups_admins", ["user_id"], unique=False)
    op.drop_constraint("group_project_id", "mrv_group_projects", type_="unique")
    op.create_unique_constraint("group_project_id", "mrv_group_projects", ["group_id", "project_id", "deleted_at"])


def downgrade():
    op.drop_constraint("group_project_id", "mrv_group_projects", type_="unique")
    op.create_unique_constraint("group_project_id", "mrv_group_projects", ["group_id", "project_id"])
    op.drop_index(op.f("ix_mrv_groups_admins_user_id"), table_name="mrv_groups_admins")
    op.drop_index(op.f("ix_mrv_groups_admins_id"), table_name="mrv_groups_admins")
    op.drop_index(op.f("ix_mrv_groups_admins_group_id"), table_name="mrv_groups_admins")
    op.drop_index(op.f("ix_mrv_groups_admins_deleted_at"), table_name="mrv_groups_admins")
    op.drop_table("mrv_groups_admins")
