"""_MRVG-85-docusign-owner

Revision ID: 2a75f578e08c
Revises: 7c3ba3edbc31
Create Date: 2023-09-22 09:26:37.873323

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "2a75f578e08c"
down_revision = "7f0b3b35d359"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_docusign_access", sa.Column("owner_id", sa.Integer(), nullable=True))
    op.add_column("mrv_docusign_access", sa.Column("owner_name", mysql.VARCHAR(length=255), nullable=True))
    op.add_column("mrv_docusign_access", sa.Column("owner_email", mysql.VARCHAR(length=255), nullable=True))
    op.create_index(op.f("ix_mrv_docusign_access_owner_id"), "mrv_docusign_access", ["owner_id"], unique=False)


def downgrade():
    op.drop_column("mrv_docusign_access", "owner_id")
    op.drop_column("mrv_docusign_access", "owner_name")
    op.drop_column("mrv_docusign_access", "owner_email")
