"""add dashboard config options

Revision ID: c4ae814b93f2
Revises: 3cc0c19231f0
Create Date: 2023-08-11 11:59:56.616254

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c4ae814b93f2"
down_revision = "3cc0c19231f0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_chart_presets", sa.Column("table_config", sa.JSON(), nullable=True))
    op.add_column("mrv_reporting_dashboards", sa.Column("required_params", sa.JSON(), nullable=True))
    op.add_column("mrv_reporting_dashboards_to_programs", sa.Column("configured_attributes", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards_to_programs", "configured_attributes")
    op.drop_column("mrv_reporting_dashboards", "required_params")
    op.drop_column("mrv_reporting_chart_presets", "table_config")
    # ### end Alembic commands ###
