"""add date format config option

Revision ID: fe6f40f92b04
Revises: b0d4f9d4cfa1
Create Date: 2025-07-21 15:22:59.807336

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "fe6f40f92b04"
down_revision = "b0d4f9d4cfa1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_programs", sa.Column("date_format", sa.String(length=10), server_default="YYYY-MM-DD", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    pass
