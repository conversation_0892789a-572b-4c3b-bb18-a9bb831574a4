"""data-migration set region id

Revision ID: ce0eb7b6ea5c
Revises: 3053904a80fe
Create Date: 2024-04-11 09:58:18.797447

"""

from sqlalchemy import text

from alembic import op
from config import get_settings
from logger import get_logger

settings = get_settings()
logger = get_logger(__name__)

# revision identifiers, used by Alembic.
revision = "ce0eb7b6ea5c"
down_revision = "3053904a80fe"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # The following raw SQL can only be executed on dev/prod, not on dev[2-6], because it relies on joins with core
    # tables which don't exist in dev[2-6]
    if settings.env in ["dev", "prod"]:
        connection = op.get_bind()
        query = text(
            """
UPDATE mrv_fields
SET core_region_id = (
    SELECT geom.region_id
    FROM kml_files geom
           JOIN kml_groups field ON field.kml_id = geom.id
    WHERE field.id = mrv_fields.fs_field_id
)"""
        )
        connection.execute(query)
        query = text(
            """
UPDATE mrv_fields_history hist
SET core_region_id = (
    SELECT geom.region_id
    FROM kml_files geom
           JOIN kml_groups field ON field.kml_id = geom.id
    WHERE field.id = hist.fs_field_id
)
            """
        )
        connection.execute(query)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    connection = op.get_bind()
    connection.execute(text("UPDATE mrv_fields SET core_region_id = NULL"))
    connection.execute(text("UPDATE mrv_fields_history SET core_region_id = NULL"))
    # ### end Alembic commands ###
