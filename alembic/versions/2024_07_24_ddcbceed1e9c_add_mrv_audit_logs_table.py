"""Add mrv_audit_logs table

Revision ID: ddcbceed1e9c
Revises: c7cc03490cec
Create Date: 2024-07-24 17:10:13.180505

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ddcbceed1e9c"
down_revision = "c7cc03490cec"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_audit_logs",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("table_name", sa.String(length=50), nullable=False),
        sa.Column("action", sa.Enum("create", "update", "delete", name="actionchoices"), nullable=False),
        sa.Column("data", sa.JSON(), nullable=False),
        sa.Column("fs_user_id", sa.Integer(), nullable=False),
        sa.Column("fs_impersonator_user_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        comment="This table is used to store audit logs for all the changes in the DB. Most of the data comes from the SQLAlchemy event listeners.",
        info={"no_audit_logs": True},
    )
    op.create_index(op.f("ix_mrv_audit_logs_action"), "mrv_audit_logs", ["action"], unique=False)
    op.create_index(op.f("ix_mrv_audit_logs_created_at"), "mrv_audit_logs", ["created_at"], unique=False)
    op.create_index(
        op.f("ix_mrv_audit_logs_fs_impersonator_user_id"), "mrv_audit_logs", ["fs_impersonator_user_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_audit_logs_fs_user_id"), "mrv_audit_logs", ["fs_user_id"], unique=False)
    op.create_index(op.f("ix_mrv_audit_logs_id"), "mrv_audit_logs", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_audit_logs_table_name"), "mrv_audit_logs", ["table_name"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_audit_logs_table_name"), table_name="mrv_audit_logs")
    op.drop_index(op.f("ix_mrv_audit_logs_id"), table_name="mrv_audit_logs")
    op.drop_index(op.f("ix_mrv_audit_logs_fs_user_id"), table_name="mrv_audit_logs")
    op.drop_index(op.f("ix_mrv_audit_logs_fs_impersonator_user_id"), table_name="mrv_audit_logs")
    op.drop_index(op.f("ix_mrv_audit_logs_created_at"), table_name="mrv_audit_logs")
    op.drop_index(op.f("ix_mrv_audit_logs_action"), table_name="mrv_audit_logs")
    op.drop_table("mrv_audit_logs")
    # ### end Alembic commands ###
