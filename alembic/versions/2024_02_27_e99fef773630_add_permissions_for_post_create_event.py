"""Add permissions for post create event

Revision ID: e99fef773630
Revises: 9006ee2648e6
Create Date: 2024-02-27 15:24:20.200944

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "e99fef773630"
down_revision = "9006ee2648e6"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.CREATE_UPDATE_EVENT.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.CREATE_UPDATE_EVENT.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.CREATE_UPDATE_EVENT.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.CREATE_UPDATE_EVENT.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
