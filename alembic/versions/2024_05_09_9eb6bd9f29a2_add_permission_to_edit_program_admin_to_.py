"""add permission to edit_program_admin to update reporting toggle

Revision ID: 9eb6bd9f29a2
Revises: 2a134fb1e949
Create Date: 2024-05-09 17:03:21.003575

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "9eb6bd9f29a2"
down_revision = "2a134fb1e949"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.PATCH_PROJECT_CONFIG.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
