"""Adding FireEvent to EntityEventTypes and fire event dates error

Revision ID: 18260dccad86
Revises: 79ded99d8901
Create Date: 2024-10-01 14:49:13.808587

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "18260dccad86"
down_revision = "79ded99d8901"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
        ),
        type_=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
            name="scenariosserviceintegrationerrorcode",
        ),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_raw_field_events",
        "event_type",
        existing_type=mysql.ENUM(
            "APPLICATION_EVENT", "CROPPING_EVENT", "FALLOW_PERIOD", "GRAZING_EVENT", "IRRIGATION_EVENT", "TILLAGE_EVENT"
        ),
        type_=sa.Enum(
            "APPLICATION_EVENT",
            "CROPPING_EVENT",
            "FALLOW_PERIOD",
            "GRAZING_EVENT",
            "IRRIGATION_EVENT",
            "TILLAGE_EVENT",
            "FIRE_EVENT",
            name="entityeventtype",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_raw_field_events",
        "event_type",
        existing_type=sa.Enum(
            "APPLICATION_EVENT",
            "CROPPING_EVENT",
            "FALLOW_PERIOD",
            "GRAZING_EVENT",
            "IRRIGATION_EVENT",
            "TILLAGE_EVENT",
            "FIRE_EVENT",
            name="entityeventtype",
        ),
        type_=mysql.ENUM(
            "APPLICATION_EVENT", "CROPPING_EVENT", "FALLOW_PERIOD", "GRAZING_EVENT", "IRRIGATION_EVENT", "TILLAGE_EVENT"
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
            name="scenariosserviceintegrationerrorcode",
        ),
        type_=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
