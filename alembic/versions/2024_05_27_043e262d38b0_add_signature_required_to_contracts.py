"""add signature_required to contracts

Revision ID: 043e262d38b0
Revises: 849fcf3aa998
Create Date: 2024-05-27 11:04:05.608569

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "043e262d38b0"
down_revision = "849fcf3aa998"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_contract_template", sa.Column("signature_required", sa.<PERSON>(), server_default="1", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_contract_template", "signature_required")
    # ### end Alembic commands ###
