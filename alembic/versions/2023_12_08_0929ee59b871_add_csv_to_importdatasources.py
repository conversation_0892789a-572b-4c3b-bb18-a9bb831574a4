"""Add csv to ImportDataSources

Revision ID: 0929ee59b871
Revises: 379c493697f9
Create Date: 2023-12-08 03:34:29.936785

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "0929ee59b871"
down_revision = "379c493697f9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_values",
        "source",
        existing_type=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
        ),
        type_=sa.Enum(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_values_history",
        "source",
        existing_type=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
        ),
        type_=sa.Enum(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_values_history",
        "source",
        existing_type=sa.Enum(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        type_=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_values",
        "source",
        existing_type=sa.Enum(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
            "csv",
            name="importdatasources",
        ),
        type_=mysql.ENUM(
            "user",
            "agrian",
            "agworld",
            "agx",
            "climate",
            "efc",
            "john_deere",
            "optis",
            "other_fms",
            "productionwise",
            "terravion",
            "agriwebb",
            "template",
            "smag",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
