"""Add table mrv_boundaries_service_result

Revision ID: 47b57b153c4c
Revises: 3254fb0ba36e
Create Date: 2024-06-26 16:21:49.030885

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "47b57b153c4c"
down_revision = "3254fb0ba36e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_boundaries_service_result",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "params_sha256",
            sa.String(length=64),
            nullable=False,
            comment="SHA256 of the parameters used to call the API",
        ),
        sa.Column("result", sa.JSON(), nullable=True, comment="Cached result of the API call"),
        sa.Column("last_used_at", sa.TIMESTAMP(), nullable=True, comment="Last time this result was used"),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        comment="Cache of Boundaries Service results",
    )
    op.create_index(op.f("ix_mrv_boundaries_service_result_id"), "mrv_boundaries_service_result", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_boundaries_service_result_last_used_at"),
        "mrv_boundaries_service_result",
        ["last_used_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_boundaries_service_result_params_sha256"),
        "mrv_boundaries_service_result",
        ["params_sha256"],
        unique=True,
    )
    op.add_column(
        "mrv_program_overlap_request",
        sa.Column("cache_id", sa.Integer(), nullable=True, comment="Boundaries service result"),
    )
    op.create_foreign_key(
        None, "mrv_program_overlap_request", "mrv_boundaries_service_result", ["cache_id"], ["id"], ondelete="SET NULL"
    )
    op.add_column(
        "mrv_project_overlap_request",
        sa.Column("cache_id", sa.Integer(), nullable=True, comment="Boundaries service result"),
    )
    op.create_foreign_key(
        None, "mrv_project_overlap_request", "mrv_boundaries_service_result", ["cache_id"], ["id"], ondelete="SET NULL"
    )
    op.add_column(
        "mrv_region_overlap_request",
        sa.Column("cache_id", sa.Integer(), nullable=True, comment="Boundaries service result"),
    )
    op.create_foreign_key(
        None, "mrv_region_overlap_request", "mrv_boundaries_service_result", ["cache_id"], ["id"], ondelete="SET NULL"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_region_overlap_request", type_="foreignkey")
    op.drop_column("mrv_region_overlap_request", "cache_id")
    op.drop_constraint(None, "mrv_project_overlap_request", type_="foreignkey")
    op.drop_column("mrv_project_overlap_request", "cache_id")
    op.drop_constraint(None, "mrv_program_overlap_request", type_="foreignkey")
    op.drop_column("mrv_program_overlap_request", "cache_id")
    op.drop_index(op.f("ix_mrv_boundaries_service_result_params_sha256"), table_name="mrv_boundaries_service_result")
    op.drop_index(op.f("ix_mrv_boundaries_service_result_last_used_at"), table_name="mrv_boundaries_service_result")
    op.drop_index(op.f("ix_mrv_boundaries_service_result_id"), table_name="mrv_boundaries_service_result")
    op.drop_table("mrv_boundaries_service_result")
    # ### end Alembic commands ###
