"""add-project-phase-completion

Revision ID: e58cbd64f4f1
Revises: a97edd805601
Create Date: 2024-01-31 13:58:03.748155

"""

import sqlalchemy as sa
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "e58cbd64f4f1"
down_revision = "a97edd805601"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.COMPLETE_PROJECT_PHASE.value,
        Permission.UNDO_COMPLETE_PROJECT_PHASE.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.COMPLETE_PROJECT_PHASE.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.COMPLETE_PROJECT_PHASE.value,
        Permission.UNDO_COMPLETE_PROJECT_PHASE.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.COMPLETE_PROJECT_PHASE.value,
        Permission.UNDO_COMPLETE_PROJECT_PHASE.value,
    },
}


def upgrade():
    op.create_table(
        "mrv_project_phase_completion",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column("is_completed", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            name="mrv_project_phase_completion_phase_id_fkey",
            columns=["phase_id"],
            refcolumns=["mrv_phases.id"],
        ),
        sa.ForeignKeyConstraint(
            name="mrv_project_phase_completion_project_id_fkey",
            columns=["project_id"],
            refcolumns=["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Phase completion (finalization) is tracked for each Project. This table is used to track the completion (finalized) status of each Phase. Phase data becomes non-editable once a Phase is finalized. User will finalize Phase on signing contract or confirming completion with the Finish stage",
    )
    op.create_index(
        op.f("ix_mrv_project_phase_completion_created_at"), "mrv_project_phase_completion", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_project_phase_completion_deleted_at"), "mrv_project_phase_completion", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_project_phase_completion_id"), "mrv_project_phase_completion", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_project_phase_completion_phase_id"), "mrv_project_phase_completion", ["phase_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_project_phase_completion_project_id"), "mrv_project_phase_completion", ["project_id"], unique=False
    )

    # migration of existing signed contracts to mrv_project_phase_completion table
    op.execute(
        """
    INSERT INTO mrv_project_phase_completion (project_id, phase_id, created_at, updated_at, is_completed)
    SELECT
        pc.project AS project_id,
        pc.phase_id,
        pc.signed_at AS created_at,
        pc.signed_at AS updated_at,
        1 AS is_completed
    FROM
        mrv_project_contracts pc
        JOIN mrv_phases p on p.id = pc.phase_id
    WHERE
        pc.docusign_status = 'completed'
        AND pc.deleted_at IS NULL
        AND pc.signed_at IS NOT NULL
    """
    )

    # update permissions
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    op.drop_constraint(
        constraint_name="mrv_project_phase_completion_phase_id_fkey",
        table_name="mrv_project_phase_completion",
        type_="foreignkey",
    )
    op.drop_constraint(
        constraint_name="mrv_project_phase_completion_project_id_fkey",
        table_name="mrv_project_phase_completion",
        type_="foreignkey",
    )
    op.drop_index(op.f("ix_mrv_project_phase_completion_project_id"), table_name="mrv_project_phase_completion")
    op.drop_index(op.f("ix_mrv_project_phase_completion_phase_id"), table_name="mrv_project_phase_completion")
    op.drop_index(op.f("ix_mrv_project_phase_completion_id"), table_name="mrv_project_phase_completion")
    op.drop_index(op.f("ix_mrv_project_phase_completion_deleted_at"), table_name="mrv_project_phase_completion")
    op.drop_index(op.f("ix_mrv_project_phase_completion_created_at"), table_name="mrv_project_phase_completion")
    op.drop_table("mrv_project_phase_completion")

    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
