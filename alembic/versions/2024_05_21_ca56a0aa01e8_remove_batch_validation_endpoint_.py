"""remove batch validation endpoint permissions

Revision ID: ca56a0aa01e8
Revises: d84ff653672d
Create Date: 2024-05-28 14:36:48.934586

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "ca56a0aa01e8"
down_revision = "d84ff653672d"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)
