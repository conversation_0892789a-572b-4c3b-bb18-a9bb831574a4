"""add-practice-rules

Revision ID: b694129f2d17
Revises: f580e14397e7
Create Date: 2024-12-12 11:45:24.382776

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b694129f2d17"
down_revision = "f580e14397e7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_practice_change_rules",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("rule", sa.JSON(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_practice_change_rules_deleted_at"), "mrv_practice_change_rules", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_practice_change_rules_id"), "mrv_practice_change_rules", ["id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_mrv_practice_change_rules_id"), table_name="mrv_practice_change_rules")
    op.drop_index(op.f("ix_mrv_practice_change_rules_deleted_at"), table_name="mrv_practice_change_rules")
    op.drop_table("mrv_practice_change_rules")
