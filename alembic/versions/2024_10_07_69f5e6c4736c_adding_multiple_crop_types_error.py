"""Adding multiple crop types error

Revision ID: 69f5e6c4736c
Revises: 18260dccad86
Create Date: 2024-10-07 11:21:18.674332

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "69f5e6c4736c"
down_revision = "73c291fd7e70"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
        ),
        type_=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
            "multiple_reporting_period_crop_types",
            name="scenariosserviceintegrationerrorcode",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
            "multiple_reporting_period_crop_types",
            name="scenariosserviceintegrationerrorcode",
        ),
        type_=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "unsupported_practice_intention",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            "field_boundary_self_intersection",
            "fire_event_during_cropping_event",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
