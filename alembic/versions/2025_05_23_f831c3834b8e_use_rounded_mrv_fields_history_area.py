"""Use rounded mrv fields history area

Revision ID: f831c3834b8e
Revises: bdd9c0dec423
Create Date: 2025-05-23 11:18:05.378802

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f831c3834b8e"
down_revision = "bdd9c0dec423"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_fields_history mfh
        SET mfh.area = CASE
            WHEN mfh.area_float IS NOT NULL THEN ROUND(mfh.area_float, 2)
            ELSE mfh.area
        END;"""
    )


def downgrade():
    pass
