"""Add ph clay fraction to mrv soils override

Revision ID: a8b6ae570e67
Revises: ce7455bed080
Create Date: 2024-10-24 16:34:10.767014

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a8b6ae570e67"
down_revision = "ce7455bed080"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_soils_override", sa.Column("ph", sa.Float(), nullable=True))
    op.add_column("mrv_soils_override", sa.Column("clay_fraction", sa.Float(), nullable=True))
    op.create_check_constraint("ck_mrv_soils_override_ph_ge_3", "mrv_soils_override", sa.Column("ph") >= 3.0)
    op.create_check_constraint("ck_mrv_soils_override_ph_le_11", "mrv_soils_override", sa.Column("ph") <= 11.0)
    op.create_check_constraint(
        "ck_mrv_soils_override_clay_fraction_ge_0", "mrv_soils_override", sa.Column("clay_fraction") >= 0.0
    )
    op.create_check_constraint(
        "ck_mrv_soils_override_clay_fraction_le_1", "mrv_soils_override", sa.Column("clay_fraction") <= 1.0
    )
    op.alter_column("mrv_soils_override", "md5", existing_type=mysql.VARCHAR(length=32), nullable=False)
    op.alter_column("mrv_soils_override", "record_date", existing_type=sa.DATE(), nullable=False)
    op.alter_column("mrv_soils_override", "pred_soc", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "pred_bd", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "percent_valid", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "lower90_soc", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "upper90_soc", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "lower90_bd", existing_type=mysql.FLOAT(), nullable=False)
    op.alter_column("mrv_soils_override", "upper90_bd", existing_type=mysql.FLOAT(), nullable=False)


def downgrade():
    op.alter_column("mrv_soils_override", "upper90_bd", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "lower90_bd", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "upper90_soc", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "lower90_soc", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "percent_valid", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "pred_bd", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "pred_soc", existing_type=mysql.FLOAT(), nullable=True)
    op.alter_column("mrv_soils_override", "record_date", existing_type=sa.DATE(), nullable=True)
    op.alter_column("mrv_soils_override", "md5", existing_type=mysql.VARCHAR(length=32), nullable=True)
    op.drop_constraint("ck_mrv_soils_override_ph_ge_3", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_ph_le_11", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_clay_fraction_ge_0", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_clay_fraction_le_1", "mrv_soils_override", type_="check")
    op.drop_column("mrv_soils_override", "clay_fraction")
    op.drop_column("mrv_soils_override", "ph")
