"""Remove attribute_id from PhaseCommercials

Revision ID: 7352945240a9
Revises: be86a1792883
Create Date: 2023-08-22 00:13:19.414207

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "7352945240a9"
down_revision = "be86a1792883"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_phase_commercials_ibfk_1", "mrv_phase_commercials", type_="foreignkey")
    op.drop_column("mrv_phase_commercials", "attribute_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_phase_commercials",
        sa.Column("attribute_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "mrv_phase_commercials_ibfk_1", "mrv_phase_commercials", "mrv_attributes", ["attribute_id"], ["id"]
    )
    # ### end Alembic commands ###
