"""Remove commercial regions commercial id

Revision ID: 8396f8d9f168
Revises: 2cac7b9a6aa1
Create Date: 2025-07-15 13:09:50.655979

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "8396f8d9f168"
down_revision = "2cac7b9a6aa1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("mrv_commercial_regions", "phase_id", existing_type=mysql.INTEGER(), nullable=False)
    op.alter_column(
        "mrv_commercial_regions", "region_tier", existing_type=mysql.ENUM("NON_TARGETED", "TARGETED"), nullable=False
    )
    op.drop_column("mrv_commercial_regions", "commercial_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_commercial_regions", sa.Column("commercial_id", mysql.INTEGER(), autoincrement=False, nullable=True)
    )
    op.alter_column(
        "mrv_commercial_regions", "region_tier", existing_type=mysql.ENUM("NON_TARGETED", "TARGETED"), nullable=True
    )
    op.alter_column("mrv_commercial_regions", "phase_id", existing_type=mysql.INTEGER(), nullable=True)
    # ### end Alembic commands ###
