"""Add agroforestry pollinator hotspot enums

Revision ID: bb6011400651
Revises: d01ec50d5c6c
Create Date: 2025-04-30 09:29:52.859509

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "bb6011400651"
down_revision = "d01ec50d5c6c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            "agroforestry",
            "pollinator_hotpot",
            name="practicechange",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            "agroforestry",
            "pollinator_hotpot",
            name="practicechange",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            "agroforestry",
            "pollinator_hotpot",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
            "agroforestry",
            "pollinator_hotpot",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "residue_removal",
            "reduced_planting_density",
            "manure_application",
            "n_inhibitors",
            "catch_crops",
            "companion_crops",
            "phytosanitary_management",
            "field_margin_options",
            "intercropping",
            "relay_cropping",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
