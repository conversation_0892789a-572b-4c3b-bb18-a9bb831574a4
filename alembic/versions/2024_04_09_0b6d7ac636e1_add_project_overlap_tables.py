"""Add project overlap tables

Revision ID: 0b6d7ac636e1
Revises: cc7a559c9d68
Create Date: 2024-04-09 08:35:59.287612

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "0b6d7ac636e1"
down_revision = "cc7a559c9d68"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_project_overlap_request",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("previous_project_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.Integer(), nullable=False),
        sa.Column(
            "job_id",
            sa.String(length=36),
            nullable=True,
            comment="UUID sent by boundaries service to identify async API call",
        ),
        sa.Column(
            "status",
            sa.Enum("accepted", "running", "successful", "failed", "dismissed", name="jobstatus"),
            nullable=True,
            comment="Job status set by boundaries service",
        ),
        sa.Column(
            "result",
            sa.JSON(),
            nullable=True,
            comment="Contains either the result of the API call, or error, from boundaries service",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["previous_project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_determine_overlap_task.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("job_id"),
        comment="Tracks async requests to boundaries service to determine the overlap between two projects",
    )
    op.create_table(
        "mrv_project_overlap_result",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("previous_project_id", sa.Integer(), nullable=False),
        sa.Column("project_area_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"),
        sa.Column(
            "previous_project_area_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column(
            "area_intersection_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["previous_project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_determine_overlap_task.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="When we call boundaries service to determine the overlap between two projects, store results here",
    )
    op.create_index(
        op.f("ix_mrv_project_overlap_result_created_at"), "mrv_project_overlap_result", ["created_at"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_project_overlap_result_created_at"), table_name="mrv_project_overlap_result")
    op.drop_table("mrv_project_overlap_result")
    op.drop_table("mrv_project_overlap_request")
    # ### end Alembic commands ###
