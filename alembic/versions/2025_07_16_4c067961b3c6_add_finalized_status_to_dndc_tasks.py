"""Add finalized status to dndc tasks.

Revision ID: 4c067961b3c6
Revises: d93c775af1a7
Create Date: 2025-07-16 13:42:20.037518

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4c067961b3c6"
down_revision = "d93c775af1a7"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_dndc_tasks",
        "status",
        existing_type=mysql.ENUM("in_progress", "interrupted", "finished"),
        type_=sa.Enum("in_progress", "interrupted", "finished", "finalized", name="dndctaskstatuschoices"),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_dndc_tasks",
        "status",
        existing_type=sa.Enum("in_progress", "interrupted", "finished", "finalized", name="dndctaskstatuschoices"),
        type_=mysql.ENUM("in_progress", "interrupted", "finished"),
        existing_nullable=False,
    )
