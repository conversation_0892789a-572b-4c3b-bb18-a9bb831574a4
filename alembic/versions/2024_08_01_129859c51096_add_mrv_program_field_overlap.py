"""Add mrv_program_field_overlap

Revision ID: 129859c51096
Revises: 250035fe3838
Create Date: 2024-08-01 21:37:20.606903

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "129859c51096"
down_revision = "250035fe3838"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_program_field_overlap",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deviation_id", sa.Integer(), nullable=False),
        sa.Column(
            "field_id",
            sa.Integer(),
            nullable=True,
            comment="Which field does the boundary referred to by deviation_id overlap with?",
        ),
        sa.Column(
            "field_md5",
            sa.String(length=32),
            nullable=False,
            comment="The md5 mrv field with field_id has at the time of the check (in case it changes)",
        ),
        sa.Column(
            "percentage_overlap",
            sa.DECIMAL(precision=5, scale=2),
            nullable=False,
            comment="Intersection area divided by area of either of the fields (max is taken). Percentage range between 0-100",
        ),
        sa.Column(
            "area_intersection_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column(
            "is_self_overlap",
            sa.Boolean(),
            server_default=sa.text("0"),
            nullable=False,
            comment="True if field overlaps with itself, i.e. if mrv_boundary_rule_deviation.md5 == field_md5",
        ),
        sa.ForeignKeyConstraint(["deviation_id"], ["mrv_boundary_rule_deviation.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["field_id"], ["mrv_fields.id"], ondelete="SET NULL"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("deviation_id", "field_id", name="deviation_id_field_id_uc"),
        comment="Not to be confused with mrv_field_overlap_result (used for retention calculations), this table stores the overlap between two boundaries in a field boundary check. The boundary specified by mrv_program_field_overlap.deviation_id (mrv_boundary_rule_deviation.md5) is checked against all registered fields in a program, and for every field it overlaps with, a row is created in this table",
    )
    op.create_index(
        op.f("ix_mrv_program_field_overlap_created_at"), "mrv_program_field_overlap", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_program_field_overlap_field_md5"), "mrv_program_field_overlap", ["field_md5"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_program_field_overlap_is_self_overlap"),
        "mrv_program_field_overlap",
        ["is_self_overlap"],
        unique=False,
    )
    op.add_column(
        "mrv_boundary_rule_deviation",
        sa.Column(
            "includes_self_overlap",
            sa.Boolean(),
            nullable=True,
            comment="For 'Program fields' type boundary checks, this flag is set to true if the field overlaps with itself, i.e. there's another registered field in this program with the same md5. Set to NULL for all other rule types",
        ),
    )
    op.create_index(
        op.f("ix_mrv_boundary_rule_deviation_includes_self_overlap"),
        "mrv_boundary_rule_deviation",
        ["includes_self_overlap"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_mrv_boundary_rule_deviation_includes_self_overlap"), table_name="mrv_boundary_rule_deviation"
    )
    op.drop_column("mrv_boundary_rule_deviation", "includes_self_overlap")
    op.drop_index(op.f("ix_mrv_program_field_overlap_is_self_overlap"), table_name="mrv_program_field_overlap")
    op.drop_index(op.f("ix_mrv_program_field_overlap_field_md5"), table_name="mrv_program_field_overlap")
    op.drop_index(op.f("ix_mrv_program_field_overlap_created_at"), table_name="mrv_program_field_overlap")
    op.drop_table("mrv_program_field_overlap")
    # ### end Alembic commands ###
