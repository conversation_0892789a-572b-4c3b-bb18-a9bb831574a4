"""Add lut_usage_reason to DndcResults

Revision ID: def407304457
Revises: 8f89eeb9241d
Create Date: 2023-09-11 03:22:02.301754

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "def407304457"
down_revision = "8f89eeb9241d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_dndc_results",
        sa.Column(
            "lut_usage_reason",
            sa.Enum("ghg_too_low", "ghg_too_high", "mrv_couldnt_build_ss_input", "ss_error", name="lutusagereasons"),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_dndc_results", "lut_usage_reason")
    # ### end Alembic commands ###
