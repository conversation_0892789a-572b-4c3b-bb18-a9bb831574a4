"""reduce bypass lock permissions

Revision ID: 36f9a4ba6cf6
Revises: c7b2670729bb
Create Date: 2025-07-11 15:30:13.228906

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "36f9a4ba6cf6"
down_revision = "c7b2670729bb"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

# these permissions are being removed
roles_to_permissions_mapping = {
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)
