"""Remove mrv_ses_events.

Revision ID: 4aa21e70909b
Revises: bee7b507d21f
Create Date: 2025-05-15 16:58:48.169752

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4aa21e70909b"
down_revision = "49a16d214d87"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("_ses_id_phase_id_uc", table_name="mrv_ses_events")
    op.drop_table("mrv_ses_events")


def downgrade():
    op.create_table(
        "mrv_ses_events",
        sa.Column("ses_event_id", mysql.VARCHAR(length=36), nullable=False),
        sa.Column("mrv_phase_id", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("revision", mysql.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["mrv_phase_id"], ["mrv_phases.id"], name="mrv_ses_events_ibfk_1"),
        sa.PrimaryKeyConstraint("ses_event_id", "mrv_phase_id"),
        mysql_collate="utf8mb4_0900_ai_ci",
        mysql_default_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_index("_ses_id_phase_id_uc", "mrv_ses_events", ["ses_event_id", "mrv_phase_id"], unique=True)
