"""Add Programs to NotificationEntityTypes

Revision ID: f9859cf44f47
Revises: f8baf10b061a
Create Date: 2024-06-12 21:38:46.793445

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "f9859cf44f47"
down_revision = "f8baf10b061a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_notifications",
        "entity",
        existing_type=mysql.ENUM("Values", "Fields", "Projects"),
        type_=sa.Enum("Values", "Fields", "Projects", "Programs", name="notificationentitytypes"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_notifications",
        "entity",
        existing_type=sa.Enum("Values", "Fields", "Projects", "Programs", name="notificationentitytypes"),
        type_=mysql.ENUM("Values", "Fields", "Projects"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
