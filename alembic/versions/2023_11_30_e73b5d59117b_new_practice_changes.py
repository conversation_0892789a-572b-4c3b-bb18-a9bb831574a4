"""new practice changes

Revision ID: e73b5d59117b
Revises: cd129fe15f23
Create Date: 2023-11-30 11:26:00.925873

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "e73b5d59117b"
down_revision = "cd129fe15f23"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "straw_removal",
            "reduced_planting_density",
            name="practicechange",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
        ),
        type_=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "straw_removal",
            "reduced_planting_density",
            name="practicechange",
        ),
        existing_nullable=False,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "straw_removal",
            "reduced_planting_density",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
        ),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=sa.Enum(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
            "direct_seeding",
            "fertilizer_substitution",
            "straw_removal",
            "reduced_planting_density",
            name="practicechange",
        ),
        type_=mysql.ENUM(
            "cover_crops",
            "tillage_reduction",
            "fertilizer_reduction",
            "integrated_grazing",
            "nutrient_management",
            "conservation_practices",
            "reduced_till",
            "no_till",
            "composting",
            "whole_orchard_recycling",
            "crop_rotation",
            "irrigation_management",
            "basic_cover_crops",
            "premium_cover_crops",
            "conventional_till",
            "no_cover_crop",
            "alternating_wet_dry",
            "furrow_irrigation",
            "stubble_retention",
            "stocking_intensity",
            "stocking_duration",
            "soil_amendments",
            "incorporate_legumes",
            "seeding_or_pasture_cropping",
            "landscape_modification",
            "thinning_woody_vegetation",
            "clear_woody_vegetation",
            "rotation_or_intensive_grazing",
            "tcf_irrigation",
            "drip_irrigation",
            "rate_reduction",
            "split_application",
            "timing",
        ),
        existing_nullable=False,
    )
