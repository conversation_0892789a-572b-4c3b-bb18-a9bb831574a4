"""Squash Migrations

Revision ID: 3b1bfb018219
Revises:
Create Date: 2023-07-24 11:40:14.065779

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op
from helper.custom_models_types import Multipolygon

# revision identifiers, used by Alembic.
revision = "947e1c81d4b6"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_audits",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("user", sa.Integer(), nullable=True),
        sa.Column("data", mysql.JSON(), nullable=False),
        sa.Column("action", sa.Enum("create", "update", "delete", name="actionchoices"), nullable=False),
        sa.Column("table_name", sa.String(length=50), nullable=False),
        sa.Column("fs_user_id", sa.Integer(), nullable=False),
        sa.Column("fs_impersonator_user_id", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_audits_id"), "mrv_audits", ["id"], unique=False)
    op.create_table(
        "mrv_countries",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=False, comment="Country name in English, e.g. 'Germany'"),
        sa.Column(
            "official_name",
            sa.String(length=255),
            nullable=False,
            comment="Official name in English e.g. 'Federal Republic of Germany'",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_countries_deleted_at"), "mrv_countries", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_countries_id"), "mrv_countries", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_countries_name"), "mrv_countries", ["name"], unique=True)
    op.create_table(
        "mrv_cover_crop_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_cover_crop_eligibility_config_id"), "mrv_cover_crop_eligibility_config", ["id"], unique=False
    )
    op.create_table(
        "mrv_dndc_lookup_table",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("year", sa.Integer(), nullable=False),
        sa.Column("scenario", sa.String(length=50), nullable=False),
        sa.Column("region", sa.String(length=50), nullable=False),
        sa.Column("crop", sa.String(length=50), nullable=False),
        sa.Column("cfg", sa.String(length=50), nullable=False),
        sa.Column("crop_season", sa.String(length=50), nullable=False),
        sa.Column("baseline_tillage", sa.String(length=50), nullable=False),
        sa.Column("baseline_cover_crop", sa.String(length=50), nullable=False),
        sa.Column("practice_change", sa.String(length=50), nullable=False),
        sa.Column("ghg", mysql.DECIMAL(precision=18, scale=16), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_dndc_lookup_table_id"), "mrv_dndc_lookup_table", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_dndc_lookup_table_year"), "mrv_dndc_lookup_table", ["year"], unique=False)
    op.create_table(
        "mrv_docusign_access",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("docusign_client_id", sa.String(length=64), nullable=False),
        sa.Column("docusign_secret_id", sa.String(length=64), nullable=True),
        sa.Column("docusign_authorization_server_url", sa.Text(), nullable=True),
        sa.Column("docusign_target_account_id", sa.Text(), nullable=True),
        sa.Column("auth_token", sa.Text(), nullable=True),
        sa.Column("access_token", sa.Text(), nullable=True),
        sa.Column("refresh_token", sa.Text(), nullable=True),
        sa.Column("docusign_response", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("docusign_client_id"),
    )
    op.create_index(op.f("ix_mrv_docusign_access_id"), "mrv_docusign_access", ["id"], unique=False)
    op.create_table(
        "mrv_docusign_webhook",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("envelope_id", sa.String(length=128), nullable=False),
        sa.Column("event", sa.String(length=128), nullable=False),
        sa.Column("payload", sa.BLOB(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_docusign_webhook_id"), "mrv_docusign_webhook", ["id"], unique=False)
    op.create_table(
        "mrv_grazing_interventions_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_grazing_interventions_eligibility_config_id"),
        "mrv_grazing_interventions_eligibility_config",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_nm_rate_reduction_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_nm_rate_reduction_eligibility_config_id"),
        "mrv_nm_rate_reduction_eligibility_config",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_nm_split_application_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_nm_split_application_eligibility_config_id"),
        "mrv_nm_split_application_eligibility_config",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_nm_timing_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_nm_timing_eligibility_config_id"), "mrv_nm_timing_eligibility_config", ["id"], unique=False
    )
    op.create_table(
        "mrv_programs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("crediting_year", sa.Integer(), nullable=False),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("description", sa.Unicode(length=200), nullable=True),
        sa.Column("modified_date", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("carbon_price", sa.Integer(), nullable=False),
        sa.Column("docusign_client_id", sa.String(length=64), nullable=True),
        sa.Column("contract_inbox", sa.String(length=50), nullable=True),
        sa.Column("support_inbox", sa.String(length=50), nullable=True),
        sa.Column("company_name", sa.String(length=255), nullable=True),
        sa.Column("privacy_policy_url", sa.String(length=255), nullable=True),
        sa.Column("registration_policy_message", sa.Text(), nullable=True),
        sa.Column("login_html", mysql.MEDIUMTEXT(), nullable=True),
        sa.Column("logo_image_url", sa.String(length=255), nullable=True),
        sa.Column("signup_vertical_banner_image_url", sa.String(length=255), nullable=True),
        sa.Column("custom_signup_fields", sa.JSON(), nullable=True),
        sa.Column("login_image_url", sa.String(length=255), nullable=True),
        sa.Column("self_enrol", sa.Boolean(), nullable=False),
        sa.Column("program_code", sa.String(length=255), nullable=True),
        sa.Column("config", sa.JSON(), nullable=True),
        sa.Column("currency_char", sa.String(length=1), nullable=True),
        sa.Column("currency_code", sa.String(length=3), nullable=True),
        sa.Column("fallback_coordinate_system", sa.String(length=50), nullable=True),
        sa.Column("external_id_validation_regex", sa.String(length=255), nullable=True),
        sa.Column("external_id_validation_field_name", sa.String(length=255), nullable=True),
        sa.Column("external_id_validation_enabled", sa.Boolean(), nullable=True),
        sa.Column(
            "locale",
            sa.Enum("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", name="locale"),
            nullable=False,
        ),
        sa.Column("support_phone_number", sa.String(length=20), nullable=True),
        sa.Column("units", sa.Enum("US_IMPERIAL", "METRIC", name="unitstypes"), nullable=False),
        sa.Column("protocol", sa.Enum("VERRA", "CAR_SEP", "SUSTAINCERT", name="protocols"), nullable=True),
        sa.Column("verification_imagery_start_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("verification_imagery_end_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("verification_imagery_num", sa.Integer(), nullable=False),
        sa.Column("verification_cc_percent", sa.Integer(), nullable=False),
        sa.Column("is_optis_enabled", sa.Boolean(), server_default=sa.text("1"), nullable=False),
        sa.Column("display_announcement", sa.Boolean(), nullable=True),
        sa.Column("announcement_title", sa.Text(), nullable=True),
        sa.Column("announcement_description", sa.Text(), nullable=True),
        sa.Column("field_overlap_threshold", sa.Integer(), server_default="20", nullable=True),
        sa.Column("docusign_additional_signature", sa.String(length=200), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("program_code"),
        comment="An MRV program, e.g. 'Cargill RegenConnect 2023'",
    )
    op.create_index(op.f("ix_mrv_programs_deleted_at"), "mrv_programs", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_programs_id"), "mrv_programs", ["id"], unique=False)
    op.create_table(
        "mrv_reporting_chart_presets",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column("query", sa.JSON(), nullable=False),
        sa.Column("pivot_config", sa.JSON(), nullable=False),
        sa.Column("title_map", sa.JSON(), nullable=False),
        sa.Column(
            "chart_type",
            sa.Enum(
                "DOUGHNUT",
                "VERTICAL_BAR",
                "HORIZONTAL_BAR",
                "MAP",
                "PIE",
                "STAT",
                "TABLE",
                "LINE",
                "MAP_WITH_BUBBLES",
                "TIMESTAMP_LINE",
                "VERTICAL_BAR_STACKED",
                "HORIZONTAL_BAR_STACKED",
                "VERTICAL_BAR_GROUPED",
                "HORIZONTAL_BAR_GROUPED",
                name="charttype",
            ),
            nullable=False,
        ),
        sa.Column("color", sa.String(length=255), nullable=False),
        sa.Column("tooltip", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_chart_presets_deleted_at"), "mrv_reporting_chart_presets", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_reporting_chart_presets_id"), "mrv_reporting_chart_presets", ["id"], unique=False)
    op.create_table(
        "mrv_reporting_dashboards",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column("is_default", sa.Boolean(), nullable=False),
        sa.Column("key", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("key"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboards_deleted_at"), "mrv_reporting_dashboards", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_reporting_dashboards_id"), "mrv_reporting_dashboards", ["id"], unique=False)
    op.create_table(
        "mrv_rice_irrigation_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_rice_irrigation_eligibility_config_id"),
        "mrv_rice_irrigation_eligibility_config",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_soils_override",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("md5", sa.String(length=32), nullable=True),
        sa.Column("record_date", sa.Date(), nullable=True),
        sa.Column("pred_soc", sa.Float(), nullable=True),
        sa.Column("pred_bd", sa.Float(), nullable=True),
        sa.Column("percent_valid", sa.Float(), nullable=True),
        sa.Column("lower90_soc", sa.Float(), nullable=True),
        sa.Column("upper90_soc", sa.Float(), nullable=True),
        sa.Column("lower90_bd", sa.Float(), nullable=True),
        sa.Column("upper90_bd", sa.Float(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_soils_override_id"), "mrv_soils_override", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_soils_override_md5"), "mrv_soils_override", ["md5"], unique=False)
    op.create_table(
        "mrv_tillage_eligibility_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "local_common_practice_source",
            sa.Enum("CAR_SEP_V1_1", "VERRA_VM0042", name="localcommonpracticesources"),
            nullable=True,
        ),
        sa.Column(
            "practice_comparison_years", sa.Enum("ALL", "ANY", name="practicecomparisonyearschoice"), nullable=False
        ),
        sa.Column("historical_years_to_check", sa.Integer(), nullable=False),
        sa.Column("commodity_crops_check", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("enabled", sa.Boolean(), server_default="1", nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_tillage_eligibility_config_id"), "mrv_tillage_eligibility_config", ["id"], unique=False
    )
    op.create_table(
        "mrv_boundary_rule_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "rule",
            sa.Enum("cropland_or_grassland", "roads_or_rail", "hydrography", name="fieldboundarycheckrule"),
            nullable=False,
        ),
        sa.Column(
            "error_level", sa.Enum("none", "warning", "error", name="fieldboundarycheckerrorlevel"), nullable=False
        ),
        sa.Column("percent_threshold", sa.Integer(), nullable=False),
        sa.Column("message", sa.String(length=255), nullable=False),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="All mrv_fields created under this program will be checked against each of these rules. The field's boundary is checked with our internal field-boundary-check API, and if the percentage overlap of the field in violation is greater than `percent_threshold`, an error of level `error_level` is returned to the user with `message`",
    )
    op.create_index(
        op.f("ix_mrv_boundary_rule_config_deleted_at"), "mrv_boundary_rule_config", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_boundary_rule_config_enabled"), "mrv_boundary_rule_config", ["enabled"], unique=False)
    op.create_table(
        "mrv_conflict_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("in_progress", "success", "partially_failed", "failed", name="conflictresultstatus"),
            nullable=False,
        ),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_conflict_results_id"), "mrv_conflict_results", ["id"], unique=False)
    op.create_table(
        "mrv_county_eligibilities",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("county_code", sa.String(length=8), nullable=False),
        sa.Column(
            "practice",
            sa.Enum(
                "cover_crops",
                "tillage_reduction",
                "fertilizer_reduction",
                "integrated_grazing",
                "nutrient_management",
                "conservation_practices",
                "reduced_till",
                "no_till",
                "composting",
                "whole_orchard_recycling",
                "crop_rotation",
                "irrigation_management",
                "basic_cover_crops",
                "premium_cover_crops",
                "conventional_till",
                "no_cover_crop",
                "alternating_wet_dry",
                "furrow_irrigation",
                "stubble_retention",
                "stocking_intensity",
                "stocking_duration",
                "soil_amendments",
                "incorporate_legumes",
                "seeding_or_pasture_cropping",
                "landscape_modification",
                "thinning_woody_vegetation",
                "clear_woody_vegetation",
                "rotation_or_intensive_grazing",
                "tcf_irrigation",
                "drip_irrigation",
                name="practicechange",
            ),
            nullable=False,
        ),
        sa.Column(
            "uptake_rate", sa.Integer(), nullable=False, comment="Percentage value, e.g. a value of 30 means 30%"
        ),
        sa.Column("eligible", sa.Boolean(), nullable=False),
        sa.Column("country", sa.Integer(), nullable=False),
        sa.Column(
            "other_fields",
            sa.JSON(),
            nullable=False,
            comment="All the other columns in the CSV file which can't be mapped onto to this table's columns will go here",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["country"],
            ["mrv_countries.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "county_code", "practice", "country", name="_eligibilities_county_code_practice_country_uc"
        ),
    )
    op.create_index(
        op.f("ix_mrv_county_eligibilities_county_code"), "mrv_county_eligibilities", ["county_code"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_county_eligibilities_deleted_at"), "mrv_county_eligibilities", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_county_eligibilities_id"), "mrv_county_eligibilities", ["id"], unique=False)
    op.create_table(
        "mrv_enrolments",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("start_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("end_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("limit_program_ha", sa.Integer(), nullable=True),
        sa.Column("limit_project_ha", sa.Integer(), nullable=True),
        sa.Column("outcome_estimation", sa.Boolean(), nullable=False),
        sa.Column("show_contract", sa.Boolean(), nullable=False),
        sa.Column("contract_type", sa.Enum("DOCUSIGN", "CONTRACT_TEMPLATE", name="contracttype"), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_enrolments_deleted_at"), "mrv_enrolments", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_enrolments_id"), "mrv_enrolments", ["id"], unique=False)
    op.create_table(
        "mrv_monitor_events",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "event_type", sa.Enum("COMMODITY_CROP", "COVER_CROP", "TILLAGE", name="monitoreventtype"), nullable=False
        ),
        sa.Column("monitor_api_version", sa.Enum("V1", "V2", name="monitorapiversion"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "program_id", "event_type", "monitor_api_version", name="_program_id_event_type_monitor_api_version_uc"
        ),
        comment="Mapping of program and monitor api event types with monitor api version",
    )
    op.create_index(op.f("ix_mrv_monitor_events_id"), "mrv_monitor_events", ["id"], unique=False)
    op.create_table(
        "mrv_phases",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("start_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("end_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("modified_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("show_contract", sa.Boolean(), nullable=False),
        sa.Column("contract_type", sa.Enum("DOCUSIGN", "CONTRACT_TEMPLATE", name="contracttype"), nullable=True),
        sa.Column("no_contract_message", sa.Text(), nullable=True),
        sa.Column("name", sa.String(length=50), nullable=True),
        sa.Column(
            "type_",
            sa.Enum(
                "ENROLMENT", "MONITORING", "QA_QC", "SOIL_SAMPLING", "DASHBOARDS", "VERIFICATION", name="phasetypes"
            ),
            nullable=True,
        ),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=True),
        sa.Column("params", sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="An MRV program can have one or more phases. Each phase has a type, e.g. enrollment, monitoring, verification. It also has duration defined by a start and end date. We also have the 'DASHBOARDS' phase type, which isn't a true phase (presumably we're using it to make the UI easier to implement?). A Phase is made up of one or more Stages",
    )
    op.create_index(op.f("ix_mrv_phases_deleted_at"), "mrv_phases", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_phases_id"), "mrv_phases", ["id"], unique=False)
    op.create_table(
        "mrv_program_asset_selection",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "asset_type",
            sa.Enum(
                "Scope_3_GHG",
                "Scope_1_WATER",
                "Scope_3_WATER",
                "Scope_3_WATER_QN",
                "Scope_1_CGHG",
                "Biodiversity",
                name="assettypes",
            ),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_program_asset_selection_deleted_at"), "mrv_program_asset_selection", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_program_asset_selection_id"), "mrv_program_asset_selection", ["id"], unique=False)
    op.create_table(
        "mrv_program_custom_reg_inputs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("input_key", sa.String(length=50), nullable=True),
        sa.Column("type_", sa.Enum("regex", "dropdown", name="customreginputstypes"), nullable=False),
        sa.Column(
            "validation_rule",
            sa.Enum("cargill_grain_api", "none", name="customreginputsvalidationrules"),
            nullable=False,
        ),
        sa.Column("config", sa.String(length=255), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("order", "program_id", "deleted_at", name="_order_deleted_at_program_id_uc"),
    )
    op.create_index(
        op.f("ix_mrv_program_custom_reg_inputs_deleted_at"),
        "mrv_program_custom_reg_inputs",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(op.f("ix_mrv_program_custom_reg_inputs_id"), "mrv_program_custom_reg_inputs", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_program_custom_reg_inputs_program_id"),
        "mrv_program_custom_reg_inputs",
        ["program_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_program_custom_reg_inputs_type_"), "mrv_program_custom_reg_inputs", ["type_"], unique=False
    )
    op.create_table(
        "mrv_program_fms_options",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("logo_url", sa.String(length=255), nullable=False),
        sa.Column(
            "fms_type",
            sa.Enum("climate", "field_alytics", "john_deere", "agrian", "agworld", "agriwebb", "smag", name="fmstypes"),
            nullable=False,
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("program_id", "fms_type", name="_program_fms_type"),
        comment="The Farm Management Systems available to a program, e.g. 'john_deere', 'agriwebb'",
    )
    op.create_index(
        op.f("ix_mrv_program_fms_options_deleted_at"), "mrv_program_fms_options", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_program_fms_options_id"), "mrv_program_fms_options", ["id"], unique=False)
    op.create_table(
        "mrv_program_practice_change",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column(
            "practice_change",
            sa.Enum(
                "cover_crops",
                "tillage_reduction",
                "fertilizer_reduction",
                "integrated_grazing",
                "nutrient_management",
                "conservation_practices",
                "reduced_till",
                "no_till",
                "composting",
                "whole_orchard_recycling",
                "crop_rotation",
                "irrigation_management",
                "basic_cover_crops",
                "premium_cover_crops",
                "conventional_till",
                "no_cover_crop",
                "alternating_wet_dry",
                "furrow_irrigation",
                "stubble_retention",
                "stocking_intensity",
                "stocking_duration",
                "soil_amendments",
                "incorporate_legumes",
                "seeding_or_pasture_cropping",
                "landscape_modification",
                "thinning_woody_vegetation",
                "clear_woody_vegetation",
                "rotation_or_intensive_grazing",
                "tcf_irrigation",
                "drip_irrigation",
                name="practicechange",
            ),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="The agricultural practice changes that can be tracked in this program",
    )
    op.create_index(
        op.f("ix_mrv_program_practice_change_deleted_at"), "mrv_program_practice_change", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_program_practice_change_id"), "mrv_program_practice_change", ["id"], unique=False)
    op.create_table(
        "mrv_projects",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("config", sa.JSON(), nullable=True),
        sa.Column("send_marketing_data", sa.Boolean(), nullable=True),
        sa.Column("docusign_id", sa.String(length=128), nullable=True),
        sa.Column("docusign_status", sa.String(length=64), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="When we list Producers  (e.g. farmers) under a given Program on the frontend, these are represented by rows in mrv_projects in the DB. You can think of a Project as a User's participation in a Program. There's a many-to-one relationship between Projects and Users, but for any given Program, a particular user will only have one Project. Projects are linked to Users via table mrv_user_project_permissions.",
    )
    op.create_index(op.f("ix_mrv_projects_deleted_at"), "mrv_projects", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_projects_id"), "mrv_projects", ["id"], unique=False)
    op.create_table(
        "mrv_regions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("level", sa.Enum("STATE", "COUNTY", "HUC8", "CRD", "CUSTOM", name="leveltypes"), nullable=False),
        sa.Column("country", sa.String(length=100), nullable=True),
        sa.Column("program_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(length=100), nullable=False),
        sa.Column("geom", Multipolygon(), nullable=False),
        sa.Column("geom_simplified", Multipolygon(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Not to be confused with the Core table called 'regions', which is different",
    )
    op.create_index(op.f("ix_mrv_regions_id"), "mrv_regions", ["id"], unique=False)
    op.create_table(
        "mrv_reporting_dashboard_sections",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("dashboard_id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["dashboard_id"],
            ["mrv_reporting_dashboards.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboard_sections_deleted_at"),
        "mrv_reporting_dashboard_sections",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboard_sections_id"), "mrv_reporting_dashboard_sections", ["id"], unique=False
    )
    op.create_table(
        "mrv_reporting_dashboards_to_programs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("dashboard_id", sa.Integer(), nullable=False),
        sa.Column("disabled", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["dashboard_id"],
            ["mrv_reporting_dashboards.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("program_id", "dashboard_id", name="_program_id_dashboard_id_uc"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboards_to_programs_deleted_at"),
        "mrv_reporting_dashboards_to_programs",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboards_to_programs_id"), "mrv_reporting_dashboards_to_programs", ["id"], unique=False
    )
    op.create_table(
        "mrv_roles",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("role_type", sa.Enum("SUPER_ADMIN", "PRODUCER", "ADMIN", name="roletypes"), nullable=False),
        sa.Column("restricted", sa.Boolean(), nullable=False, default=False),
        sa.Column("program_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "program_id", name="_roles_name_program_id"),
    )
    op.create_index(op.f("ix_mrv_roles_id"), "mrv_roles", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_roles_program_id"), "mrv_roles", ["program_id"], unique=False)
    op.create_table(
        "mrv_user_program_permissions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("details", sa.JSON(), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "program_id", "deleted_at_unix", name="_program_permissions_user_program"),
    )
    op.create_index(
        op.f("ix_mrv_user_program_permissions_deleted_at"), "mrv_user_program_permissions", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_user_program_permissions_id"), "mrv_user_program_permissions", ["id"], unique=False)
    op.create_table(
        "mrv_verification_dashboard",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("reporting_year", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_verification_dashboard_id"), "mrv_verification_dashboard", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_verification_dashboard_program_id"), "mrv_verification_dashboard", ["program_id"], unique=True
    )
    op.create_table(
        "mrv_boundary_rule_deviation",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("kml_group_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("percent_overlap", sa.Integer(), nullable=False, comment="Percentage deviation from rule check"),
        sa.Column(
            "threshold_crossed",
            sa.Boolean(),
            nullable=False,
            comment="Is percent_overlap > mrv_boundary_rule_config.percent_threshold?",
        ),
        sa.Column(
            "allow_anyway",
            sa.Boolean(),
            nullable=False,
            comment="Set to true, if at the time we do the boundary check, we intend to allow the field to pass (i.e. be created or updated), regardless of outcome. Needed for fields coming from FMI, where we always have to allow fields to be created/updated. If both this flag and threshold_crossed are true, it means field boundary is in error, but we allowed it to be created/updated anyway",
        ),
        sa.Column("rule_config_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column(
            "field_check_error",
            sa.String(length=1024),
            nullable=False,
            comment="If not empty, then the field boundary check API returned an error when it checked this field under this rule. In this case, percent_overlap will be set to 0 and threshold_crossed to false, even though these values were never determined (because of the error)",
        ),
        sa.ForeignKeyConstraint(
            ["rule_config_id"],
            ["mrv_boundary_rule_config.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Add an entry here each time a rule in mrv_boundary_rule_config is checked for a given field",
    )
    op.create_index(
        op.f("ix_mrv_boundary_rule_deviation_allow_anyway"),
        "mrv_boundary_rule_deviation",
        ["allow_anyway"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_boundary_rule_deviation_percent_overlap"),
        "mrv_boundary_rule_deviation",
        ["percent_overlap"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_boundary_rule_deviation_threshold_crossed"),
        "mrv_boundary_rule_deviation",
        ["threshold_crossed"],
        unique=False,
    )
    op.create_table(
        "mrv_confidence_events",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("monitor_event_id", sa.Integer(), nullable=False),
        sa.Column("event_key", sa.String(length=255), nullable=False),
        sa.Column("discard_event_value", sa.Float(), nullable=False),
        sa.Column("confirm_event_value", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(
            ["monitor_event_id"],
            ["mrv_monitor_events.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("monitor_event_id", "event_key", name="_program_id_monitor_api_version_uc"),
        comment="Mapping of event key from monitor event and values of confidence to discard or confirm event",
    )
    op.create_index(op.f("ix_mrv_confidence_events_id"), "mrv_confidence_events", ["id"], unique=False)
    op.create_table(
        "mrv_contract_template",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("template_blob", mysql.MEDIUMBLOB(), nullable=False),
        sa.Column("enrolment_id", sa.Integer(), nullable=True),
        sa.Column("phase_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["enrolment_id"],
            ["mrv_enrolments.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_contract_template_id"), "mrv_contract_template", ["id"], unique=False)
    op.create_table(
        "mrv_dndc_contracted_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("results", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("original_results", sa.JSON(), nullable=True),
        sa.Column("task_id", sa.String(length=36), nullable=True),
        sa.Column("ta_lower_bound", sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_dndc_contracted_results_id"), "mrv_dndc_contracted_results", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_dndc_contracted_results_project_id"), "mrv_dndc_contracted_results", ["project_id"], unique=False
    )
    op.create_table(
        "mrv_dndc_tasks",
        sa.Column("id", sa.String(length=36), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column(
            "status", sa.Enum("in_progress", "interrupted", "finished", name="dndctaskstatuschoices"), nullable=False
        ),
        sa.Column(
            "phase",
            sa.Enum(
                "ENROLMENT", "MONITORING", "QA_QC", "SOIL_SAMPLING", "DASHBOARDS", "VERIFICATION", name="phasetypes"
            ),
            nullable=False,
        ),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_dndc_tasks_created_at"), "mrv_dndc_tasks", ["created_at"], unique=False)
    op.create_index(op.f("ix_mrv_dndc_tasks_id"), "mrv_dndc_tasks", ["id"], unique=False)
    op.create_table(
        "mrv_farms",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent_project_id", sa.Integer(), nullable=True),
        sa.Column("core_farm_group_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("farm_name", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["parent_project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_farms_deleted_at"), "mrv_farms", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_farms_id"), "mrv_farms", ["id"], unique=False)
    op.create_table(
        "mrv_fields",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent_project_id", sa.Integer(), nullable=True),
        sa.Column("fs_field_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("kml_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=True),
        sa.Column("farm_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=False),
        sa.Column("md5", sa.String(length=32), nullable=False),
        sa.Column("area", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(
            ["parent_project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "parent_project_id", "fs_field_id", "deleted_at_unix", name="_project_fs_field_deleted_at_uc"
        ),
    )
    op.create_index(op.f("ix_mrv_fields_deleted_at"), "mrv_fields", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_fields_id"), "mrv_fields", ["id"], unique=False)
    op.create_table(
        "mrv_mobs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent_project_id", sa.Integer(), nullable=True),
        sa.Column("mob_name", sa.String(length=100), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["parent_project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_mobs_id"), "mrv_mobs", ["id"], unique=False)
    op.create_table(
        "mrv_monitor_request_store",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False, comment="Project id for which request is made"),
        sa.Column(
            "year_start",
            sa.Integer(),
            nullable=False,
            comment="Start year for which request is made. Year is inclusive.",
        ),
        sa.Column(
            "year_end", sa.Integer(), nullable=False, comment="End year for which request is made. Year is inclusive."
        ),
        sa.Column(
            "status",
            sa.Enum("PENDING", "SUCCESS", "FAILED", "PARTIALLY_FAILED", "CANCELLED", name="requeststorestatus"),
            nullable=False,
            comment="Status of request. We keep single request in pending/completed/failed/partially_failed status, rest will be in cancelled state",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Store request for monitor api for a project",
    )
    op.create_index(op.f("ix_mrv_monitor_request_store_id"), "mrv_monitor_request_store", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_monitor_request_store_project_id"), "mrv_monitor_request_store", ["project_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_year_end"), "mrv_monitor_request_store", ["year_end"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_year_start"), "mrv_monitor_request_store", ["year_start"], unique=False
    )
    op.create_table(
        "mrv_notifications",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=True),
        sa.Column("project_id", sa.Integer(), nullable=True),
        sa.Column("entity", sa.Enum("Values", "Fields", "Projects", name="notificationentitytypes"), nullable=True),
        sa.Column("entity_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("body", sa.JSON(), nullable=True),
        sa.Column("first_sent_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("last_sent_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("number_sent", sa.Integer(), nullable=False),
        sa.Column("dismissed_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("source", sa.Enum("OPTIS", "DNDC", name="notificationsources"), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_notifications_id"), "mrv_notifications", ["id"], unique=False)
    op.create_table(
        "mrv_nutrient_templates",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "deleted_at", "project_id", "name", name="_nutrient_template_deleted_at_project_id_name_uc"
        ),
    )
    op.create_index(
        op.f("ix_mrv_nutrient_templates_deleted_at"), "mrv_nutrient_templates", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_nutrient_templates_id"), "mrv_nutrient_templates", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_nutrient_templates_project_id"), "mrv_nutrient_templates", ["project_id"], unique=False
    )
    op.create_table(
        "mrv_phase_practice_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column(
            "practice_type",
            sa.Enum("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", name="practicetypes"),
            nullable=False,
        ),
        sa.Column("event_type", sa.Enum("HISTORICAL", "INTENDED", "ACTUAL", name="eventtypes"), nullable=False),
        sa.Column("historical_years", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("phase_id", "practice_type", "event_type", name="phase_id_practice_type_event_type_idx"),
    )
    op.create_index(
        op.f("ix_mrv_phase_practice_config_deleted_at"), "mrv_phase_practice_config", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_phase_practice_config_id"), "mrv_phase_practice_config", ["id"], unique=False)
    op.create_table(
        "mrv_program_boundaries",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("region_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["region_id"],
            ["mrv_regions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("program_id", "region_id", "deleted_at_unix", name="_program_region_deleted_at_uc"),
        comment="The areas to which this program is open, e.g. the US states in which it operates",
    )
    op.create_index(
        op.f("ix_mrv_program_boundaries_deleted_at"), "mrv_program_boundaries", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_program_boundaries_id"), "mrv_program_boundaries", ["id"], unique=False)
    op.create_table(
        "mrv_project_contracts",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project", sa.Integer(), nullable=False),
        sa.Column("user", sa.Integer(), nullable=False),
        sa.Column("docusign_envelope_id", sa.String(length=128), nullable=True),
        sa.Column("docusign_status", sa.String(length=20), nullable=True),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=False),
        sa.Column("deletion_reason", sa.Text(), nullable=True),
        sa.Column(
            "signed_at", mysql.TIMESTAMP(fsp=6), nullable=True, comment="Datetime docusign contract is completed"
        ),
        sa.Column("contract_blob", mysql.LONGBLOB(), nullable=True),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("docusign_envelope_id"),
        sa.UniqueConstraint("user", "project", "deleted_at_unix", name="_project_contracts_user_project"),
    )
    op.create_index(op.f("ix_mrv_project_contracts_deleted_at"), "mrv_project_contracts", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_project_contracts_id"), "mrv_project_contracts", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_project_contracts_signed_at"), "mrv_project_contracts", ["signed_at"], unique=False)
    op.create_table(
        "mrv_project_copy_mapping",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("old_program_id", sa.Integer(), nullable=False),
        sa.Column("old_project_id", sa.Integer(), nullable=False),
        sa.Column("new_project_id", sa.Integer(), nullable=False),
        sa.Column("new_program_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["new_program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["new_project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["old_program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["old_project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "user_id",
            "old_project_id",
            "old_program_id",
            name="_project_copy_mapping_user_id_old_project_id_old_program_id",
        ),
    )
    op.create_index(op.f("ix_mrv_project_copy_mapping_id"), "mrv_project_copy_mapping", ["id"], unique=False)
    op.create_table(
        "mrv_region_to_region",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent_id", sa.Integer(), nullable=False),
        sa.Column("child_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["child_id"],
            ["mrv_regions.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_id"],
            ["mrv_regions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Defines a parent/child relationship between regions, e.g. a given county region would be the child of another state region",
    )
    op.create_table(
        "mrv_reporting_dashboard_rows",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("section_id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["section_id"],
            ["mrv_reporting_dashboard_sections.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_dashboard_rows_deleted_at"), "mrv_reporting_dashboard_rows", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_reporting_dashboard_rows_id"), "mrv_reporting_dashboard_rows", ["id"], unique=False)
    op.create_table(
        "mrv_roles_permissions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column(
            "permission",
            sa.Enum(
                "CREATE_ROLES",
                "CREATE_ROLES_PERMISSIONS",
                "CREATE_ROLES_USERS",
                "GET_PERMISSIONS",
                "GET_USER_PERMISSIONS",
                "GET_PROGRAM",
                "PATCH_PROGRAM",
                "DELETE_PROGRAM",
                "GET_PROGRAM_USER",
                "UPDATE_PROGRAM_USER",
                "GET_PROGRAM_USERS",
                "CREATE_PROGRAM_USERS",
                "DELETE_PROGRAM_USERS",
                "CREATE_PROGRAM_CROP_TYPES",
                "GET_PROGRAM_CROP_TYPES",
                "DELETE_PROGRAM_CROP_TYPES",
                "GET_PROGRAM_PROJECTS",
                "GET_DUMP_PROGRAM_DATA",
                "CREATE_PROGRAM_FIX_MONITORING_DATA",
                "CREATE_PROGRAM_FIX_ENROLMENT_DATA",
                "CREATE_PHASE_PRACTICE_CONFIG",
                "CREATE_PROGRAM_MONITORING_CONFIG",
                "GET_PROGRAM_MONITORING_CONFIG",
                "GET_PROJECT",
                "CREATE_PROJECT",
                "DELETE_PROJECT",
                "GET_PROJECTS",
                "EXPORT_PROJECTS",
                "GET_PROJECT_USERS",
                "CREATE_PROJECT_USERS",
                "DELETE_PROJECT_USERS",
                "GET_PROJECT_FIELD",
                "CREATE_PROJECT_FIELDS",
                "DELETE_PROJECT_FIELDS",
                "GET_PROJECT_CONTRACT",
                "CREATE_PROJECT_CONTRACT",
                "DELETE_PROJECT_CONTRACT",
                "GET_PROJECT_COMPLETION",
                "CREATE_CONTRACT_DELETION_REQUEST",
                "PATCH_PROJECT_CONFIG",
                "CREATE_PROJECT_RECORDING_YEARS",
                "CREATE_PROJECT_FIX_MONITORING_DATA",
                "CREATE_PROJECT_FIX_ENROLMENT_DATA",
                "GET_DOCUSIGN_CALLBACK",
                "GET_DOCUSIGN_CLIENT_ACCESS_DETAILS",
                "GET_DOCUSIGN_CALLBACK_AFTER_SIGNING_CONTRACT",
                "GET_CONTRACT_TEMPLATE_FOR_ENROLMENT",
                "CREATE_CONTRACT_TEMPLATE_FOR_PHASE",
                "DELETE_CONTRACT_TEMPLATE_FOR_ENROLMENT",
                "GET_DOCUSIGN_TOKEN_CHECK",
                "GET_DNDC_RESULT_FOR_PROJECT",
                "RUN_DNDC_FOR_PROJECT_FIELDS",
                "DNDC_CALLBACK_WEBHOOK",
                "GET_NOTIFICATIONS",
                "DISMISS_NOTIFICATIONS",
                "GET_PROGRAMS",
                "CREATE_PROGRAMS",
                "GET_PROGRAM_STATS",
                "UPDATE_PROGRAM_BOUNDARIES",
                "CREATE_BLOB",
                "GET_ELIGIBILITY",
                "GET_ASSETS",
                "ADD_ASSETS",
                "REMOVE_ASSETS",
                "GET_PRACTICE_CHANGES",
                "ADD_PRACTICE_CHANGES",
                "REMOVE_PRACTICE_CHANGES",
                "GET_PROGRAM_PHASES",
                "CREATE_PROGRAM_PHASE",
                "GET_PROGRAM_PHASE",
                "UPDATE_PROGRAM_PHASE",
                "DELETE_PROGRAM_PHASE",
                "GET_PROGRAM_STAGES",
                "CREATE_PROGRAM_STAGES",
                "DELETE_PROGRAM_STAGES",
                "GET_PROGRAM_STAGE",
                "UPDATE_PROGRAM_STAGE",
                "UPDATE_PROGRAM_STAGES",
                "GET_PROGRAM_ATTRIBUTES",
                "CREATE_PROGRAM_ATTRIBUTES",
                "DELETE_PROGRAM_ATTRIBUTES",
                "GET_PROGRAM_ATTRIBUTE",
                "UPDATE_PROGRAM_ATTRIBUTE",
                "UPDATE_PROGRAM_ATTRIBUTES",
                "GET_PROJECT_VALUES_FROM_OPTIS",
                "GET_CONTRACT_LINE_ITEMS",
                "CREATE_CONTRACT_LINE_ITEMS",
                "PATCH_CONTRACT_LINE_ITEMS",
                "DELETE_CONTRACT_LINE_ITEMS",
                "GET_COMMERCIALS",
                "CREATE_COMMERCIALS",
                "PATCH_COMMERCIALS",
                "DELETE_COMMERCIALS",
                "GET_FMS_OPTION_NAMES",
                "CREATE_FMS_OPTIONS",
                "DELETE_FMS_OPTIONS",
                "GET_PROGRAM_CONTRACT_ATTRIBUTES",
                "UPDATE_PROGRAM_CONTRACT_ATTRIBUTES",
                "CREATE_PROJECT_COMMERCIALS",
                "GET_PAYMENT_TYPES",
                "GET_PROJECT_SURVEY",
                "CREATE_PROJECT_SURVEY",
                "GET_PROGRAM_BOUNDARIES",
                "GET_ELIGIBILITY_METHODS",
                "CREATE_STAGE_ELIGIBILITY_CONFIG",
                "GET_PROJECT_FIELD_VALUES",
                "GET_PROJECT_FIELD_VALUES_BY_ID",
                "GET_STRUCTURED_PROJECT_FIELD_VALUES",
                "GET_ALL_STRUCTURED_PROJECT_FIELD_VALUES",
                "GET_PROJECT_VALUES_FOR_FIELDS",
                "UPDATE_PROJECT_FIELD_VALUES",
                "DELETE_PROJECT_FIELD_VALUES",
                "DELETE_SPECIFIC_PROJECT_FIELD_VALUES",
                "IMPORT_PROJECT_FIELD_VALUES",
                "UPDATE_PROJECT_FIELDS",
                "UPDATE_PROJECT_FIELDS_FARM_ID",
                "GET_CUSTOM_INPUTS",
                "UPDATE_CUSTOM_INPUTS",
                "CREATE_CUSTOM_INPUTS",
                "DELETE_CUSTOM_INPUTS",
                "GET_PROJECT_VALUES",
                "UPDATE_PROJECT_VALUES",
                "CREATE_PROJECT_VALUES",
                "DELETE_PROJECT_VALUES",
                "CREATE_VALUES_TEMPLATES",
                "GET_VALUES_TEMPLATES",
                "UPDATE_VALUES_TEMPLATES",
                "DELETE_VALUES_TEMPLATES",
                "GET_OPTIS_LABELS_MAPPING",
                "CREATE_OPTIS_LABELS_MAPPING",
                "GET_CONFLICTS",
                "CREATE_CONFLICTS",
                "GET_CONFLICT_RESOLUTION",
                "CREATE_CONFLICT_RESOLUTION",
                "GET_CONFLICT_RESOLUTION_FILES",
                "UPDATE_CONFLICT_RESOLUTION_FILES",
                "CREATE_CONFLICT_RESOLUTION_FILES",
                "DELETE_CONFLICT_RESOLUTION_FILES",
                "GET_CONFLICTS_CSV",
                "GET_VERIFICATION_DASHBOARDS",
                "CREATE_VERIFICATION_DASHBOARDS",
                "UPDATE_VERIFICATION_DASHBOARDS",
                "DELETE_VERIFICATION_DASHBOARDS",
                "UPDATE_VERIFICATION_DASHBOARD_CONFLICT_TYPE",
                "UPDATE_VERIFICATION_DASHBOARD_STAT_FIELD",
                "UPDATE_VERIFICATION_DASHBOARD_TABLE_FIELD",
                "GET_UNITS_OPTIONS",
                "GET_PROJECT_ENTITIES_VALUES",
                "UPDATE_PROJECT_ENTITIES_VALUES",
                "DELETE_PROJECT_ENTITIES_VALUES",
                "CREATE_PROGRAM_REPORTING_DASHBOARDS_CSV",
                "GET_REPORTING_DASHBOARDS_CHART_TYPES",
                "GET_REPORTING_DASHBOARDS_FULL",
                "CREATE_REPORTING_DASHBOARDS_FULL",
                "UPDATE_REPORTING_DASHBOARDS_FULL",
                "DELETE_REPORTING_DASHBOARDS_FULL",
                "UPDATE_REPORTING_DASHBOARDS_FULL_BY_ID",
                "UPDATE_REPORTING_DASHBOARD_BY_ID",
                "GET_PROGRAM_REPORTING_DASHBOARDS_FULL",
                "GET_PROGRAM_REPORTING_DASHBOARDS_FULL_BY_ID",
                "UPDATE_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM_BY_ID",
                "GET_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM",
                "CREATE_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM",
                "GET_PROGRAM_REPORTING_DASHBOARD_FULL_KEY_BY_KEY",
                "CREATE_REPORTING_DASHBOARDS",
                "DELETE_REPORTING_DASHBOARDS",
                "CREATE_REPORTING_DASHBOARD_SECTIONS",
                "GET_REPORTING_DASHBOARD_SECTION_BY_ID",
                "GET_REPORTING_DASHBOARD_SECTIONS",
                "UPDATE_REPORTING_DASHBOARD_SECTION",
                "CREATE_REPORTING_DASHBOARD_SECTION_ROWS",
                "GET_REPORTING_DASHBOARD_SECTION_ROW_BY_ID",
                "GET_REPORTING_DASHBOARD_SECTION_ROWS",
                "UPDATE_REPORTING_DASHBOARD_SECTION_ROW",
                "CREATE_REPORTING_DASHBOARD_CHART_PRESETS",
                "GET_REPORTING_DASHBOARD_CHART_PRESET_BY_ID",
                "GET_REPORTING_DASHBOARD_CHART_PRESETS",
                "UPDATE_REPORTING_DASHBOARD_CHART_PRESET",
                "GET_REPORTING_DASHBOARD_CHART_INSTANCE_BY_ID",
                "UPDATE_REPORTING_DASHBOARD_CHART_INSTANCE",
                "DELETE_REPORTING_DASHBOARD_CHART_INSTANCES",
                "CREATE_REPORTING_DASHBOARD_CHART_INSTANCES",
                "DELETE_REPORTING_DASHBOARD_CHART_PRESETS",
                "DELETE_REPORTING_DASHBOARD_SECTION_ROWS",
                "DELETE_REPORTING_DASHBOARD_SECTIONS",
                "GET_PROJECT_MOB_BY_ID",
                "UPDATE_PROJECT_MOB_BY_ID",
                "GET_PROJECT_MOBS",
                "CREATE_PROJECT_MOBS",
                "DELETE_PROJECT_MOBS",
                "CREATE_ENROL_PROGRAM_CODES",
                "GET_PROJECT_FARM",
                "GET_PROJECT_FARMS",
                "CREATE_PROJECT_FARMS",
                "UPDATE_PROJECT_FARM",
                "DELETE_PROJECT_FARMS",
                "CREATE_BOUNDARY_RULE_CONFIG",
                "GET_BOUNDARY_RULE_CONFIGS",
                "UPDATE_BOUNDARY_RULE_CONFIG",
                "DELETE_BOUNDARY_RULE_CONFIG",
                "GET_USER_RESTRICTION",
                "CREATE_USER_RESTRICTION",
                "UPDATE_USER_RESTRICTION",
                "DELETE_USER_RESTRICTION",
                name="permission",
            ),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["mrv_roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("role_id", "permission", name="_roles_permissions_role_id_permission"),
    )
    op.create_index(op.f("ix_mrv_roles_permissions_id"), "mrv_roles_permissions", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_roles_permissions_role_id"), "mrv_roles_permissions", ["role_id"], unique=False)
    op.create_table(
        "mrv_roles_users",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("fs_user_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["mrv_roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("fs_user_id", "program_id", name="_roles_user_fs_user_id_program_id"),
    )
    op.create_index(op.f("ix_mrv_roles_users_deleted_at"), "mrv_roles_users", ["deleted_at"], unique=False)
    op.create_index("ix_mrv_roles_users_fs_user_id", "mrv_roles_users", ["fs_user_id"], unique=False)
    op.create_index(op.f("ix_mrv_roles_users_id"), "mrv_roles_users", ["id"], unique=False)
    op.create_table(
        "mrv_stages",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("custom_name", sa.String(length=50), nullable=True),
        sa.Column("fmi_import_start_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("fmi_import_end_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("year_start", sa.Integer(), nullable=True),
        sa.Column("year_end", sa.Integer(), nullable=True),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.Column("description", sa.Unicode(length=200), nullable=True),
        sa.Column("num_rows", mysql.INTEGER(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("icon", sa.String(length=255), nullable=False),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("messages", sa.JSON(), nullable=True),
        sa.Column("config_description", sa.TEXT(), nullable=True),
        sa.Column(
            "type_",
            sa.Enum(
                "FIELD_BOUNDARIES",
                "ASSIGN_PRACTICES",
                "CONFIRM_HISTORY",
                "SUMMER_CROPS",
                "WINTER_CROPS",
                "TILLAGE",
                "NUTRIENT_MGMT",
                "VIEW_OUTCOMES",
                "SURVEY",
                "CONTRACT",
                "ELIGIBILITY",
                "FIELD_INFORMATION",
                "HISTORICAL_CROP_ROTATION",
                "HISTORICAL_TILLAGE",
                "INTENDED_COMMODITY_CROPS",
                "IRRIGATION",
                "RICE_CROP_HISTORY",
                "MOB_HISTORY",
                "MOB_MOVEMENT",
                "FARM_LEVEL_MANAGEMENT",
                "NUTRIENT_MGMT_INTENDED",
                name="stagetypes",
            ),
            nullable=True,
        ),
        sa.Column(
            "eligibility_method",
            sa.Enum(
                "CARGILL_GRAIN_2022",
                "CARGILL_COTTON_2022",
                "ELIGIBILITY_ALWAYS_TRUE",
                "CARGILL_EU_2022_UK",
                "CARGILL_EU_2022_FR",
                "CARGILL_GRAIN_2023",
                "CARGILL_COTTON_2023",
                "CARGILL_EU_2023",
                "CUSTOM",
                name="eligibilitytypes",
            ),
            nullable=True,
        ),
        sa.Column("locked", sa.Boolean(), nullable=False),
        sa.Column("survey_id", sa.String(length=255), nullable=True),
        sa.Column("entity_type", sa.Enum("field", "farm", "mob", name="entitytypechoices"), nullable=False),
        sa.Column(
            "required",
            sa.Boolean(),
            server_default="1",
            nullable=False,
            comment="Do all values need to be filled in order for the ui to unlock and allow passage to the next stage?",
        ),
        sa.Column(
            "interventions_description",
            sa.String(length=255),
            server_default="As you plan out your intended practices, we will check off any interventions that the practices satisfy. Satisfying nutrient interventions is optional.",
            nullable=False,
        ),
        sa.Column("timeline_has_crop_events", sa.Boolean(), server_default="1", nullable=False),
        sa.Column("timeline_has_tillage_events", sa.Boolean(), server_default="1", nullable=False),
        sa.Column("timeline_has_nutrient_events", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("timeline_has_irrigation_events", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("timeline_start_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("timeline_end_date", sa.TIMESTAMP(), nullable=True),
        sa.Column("timeline_enabled", sa.Boolean(), server_default="0", nullable=False),
        sa.Column(
            "timeline_time_scale",
            sa.Enum("daily", "monthly", "biannual", name="timelinetimescales"),
            server_default="biannual",
            nullable=False,
        ),
        sa.Column(
            "optis_prefill",
            sa.Boolean(),
            server_default="0",
            nullable=False,
            comment="Ensures that the stage is pre-filled with data from Optis",
        ),
        sa.Column(
            "optis_year_start",
            sa.Integer(),
            nullable=True,
            comment="Start year of optis call for prefilling data. Year is inclusive.",
        ),
        sa.Column(
            "optis_year_end",
            sa.Integer(),
            nullable=True,
            comment="End year of optis call for prefilling data. Year is inclusive.",
        ),
        sa.Column("allow_enrol_without_assigned_practice", sa.Boolean(), server_default="0", nullable=False),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Every Phase is usually made up of multiple Stages (except for DASHBOARDS phase type). Examples of stages might be 'Nutrient Management',  'Tillage History', 'Field Boundaries' etc. Stages have an order, and each stage might contain a number of Attributes (some stage types, such as 'Field Boundaries', won't have any Attributes). Every Stage has an entity_type, e.g. 'field', 'farm' or 'mob'.",
    )
    op.create_index(op.f("ix_mrv_stages_deleted_at"), "mrv_stages", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_stages_id"), "mrv_stages", ["id"], unique=False)
    op.create_table(
        "mrv_user_project_permissions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user", sa.Integer(), nullable=False),
        sa.Column("project", sa.Integer(), nullable=False),
        sa.Column("details", sa.JSON(), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at_unix", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user", "project", "deleted_at_unix", name="_project_permissions_user_project"),
        comment="Connects a Project (i.e. a Producer) to a User",
    )
    op.create_index(
        op.f("ix_mrv_user_project_permissions_deleted_at"), "mrv_user_project_permissions", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_user_project_permissions_id"), "mrv_user_project_permissions", ["id"], unique=False)
    op.create_table(
        "mrv_verification_dashboard_stat_fields",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("verification_dashboard_id", sa.Integer(), nullable=False),
        sa.Column(
            "stat_field_name",
            sa.Enum(
                "conflicts",
                "producer_with_conflicts",
                "fields_with_conflicts",
                "area_with_conflicts",
                "est_payments_with_conflicts",
                "est_yield_impacted_by_conflicts",
                name="statfields",
            ),
            nullable=False,
        ),
        sa.Column("label", sa.String(length=255), nullable=True),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(["verification_dashboard_id"], ["mrv_verification_dashboard.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "verification_dashboard_id", "stat_field_name", name="_verification_dashboard_id_stat_field_name_uc"
        ),
    )
    op.create_index(
        op.f("ix_mrv_verification_dashboard_stat_fields_id"),
        "mrv_verification_dashboard_stat_fields",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_verification_dashboard_table_fields",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("verification_dashboard_id", sa.Integer(), nullable=False),
        sa.Column(
            "table_field_name",
            sa.Enum(
                "conflict_type",
                "status",
                "producer_name_and_id",
                "field_and_size",
                "producer_data",
                "regrow_data",
                "evidence_and_history",
                "baseline",
                "contract_commitment",
                "est_payments",
                "est_outcomes",
                name="tablefields",
            ),
            nullable=False,
        ),
        sa.Column("is_default", sa.Boolean(), nullable=False),
        sa.Column("label", sa.String(length=255), nullable=True),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(["verification_dashboard_id"], ["mrv_verification_dashboard.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "verification_dashboard_id", "table_field_name", name="_verification_dashboard_id_table_field_name_uc"
        ),
    )
    op.create_index(
        op.f("ix_mrv_verification_dashboard_table_fields_id"),
        "mrv_verification_dashboard_table_fields",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_verification_dashboard_table_fields_verification_dashboard_id"),
        "mrv_verification_dashboard_table_fields",
        ["verification_dashboard_id"],
        unique=False,
    )
    op.create_table(
        "mrv_attributes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("parent_stage_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("custom_name", sa.String(length=50), nullable=True),
        sa.Column(
            "type",
            sa.Enum(
                "summer_crop_type",
                "summer_harvest_date",
                "summer_dry_yield",
                "summer_residue_harvested",
                "summer_planting_date",
                "winter_crop_commitment",
                "winter_crop_type",
                "winter_harvest_date",
                "winter_dry_yield",
                "winter_residue_harvested",
                "winter_planting_date",
                "fall_tillage_practice",
                "fall_tillage_date",
                "fall_tillage_depth",
                "spring_tillage_practice",
                "spring_tillage_date",
                "spring_tillage_depth",
                "planting_season",
                "planting_date",
                "harvest_date",
                "crop_usage",
                "other",
                "string",
                "number",
                "date",
                "bool_",
                "harvest",
                "planting",
                "crop_type",
                "crop_yield",
                "residue_harvested",
                "tillage_period",
                "tillage_practice",
                "tillage_date",
                "tillage_depth",
                "tillage_event",
                "strip_fraction",
                "soil_inversion",
                "spring_tillage",
                "fall_tillage",
                "winter_crop",
                "practice",
                "summer_crop",
                "record_year",
                "winter_crop_termination",
                "application_date",
                "application_product",
                "application_rate",
                "application_rate_unit",
                "application_area",
                "application_product_type",
                "application_depth",
                "application_method",
                "application_rate_unit_vanity",
                "application_product_type_vanity",
                "application_rate_vanity",
                "yield_rate_unit",
                "application_area_vanity",
                "water_amount",
                "water_amount_unit",
                "additive_one",
                "additive_two",
                "additives",
                "farm_number",
                "cover_crop_mix",
                "start_date",
                "end_date",
                "irrigation_method",
                "irrigation_enabled",
                "irrigation_rate_unit",
                "flood_pct",
                "subsurface_drip_depth",
                "subsurface_drip_depth_unit",
                "energy_source",
                "fuel_type",
                "pesticide_farmprint_gaff",
                "change_type",
                "head_count",
                "livestock_class",
                "farm_name",
                "area_sown",
                "residue_burnt",
                "area_tilled_total_pct",
                "area_tilled_for_pasture_renewal_pct",
                "nutrient_management_enabled",
                "landscape_modifications_fuel_usage",
                "landscape_modifications_fuel_type",
                "irrigation_fuel_usage",
                "irrigation_fuel_type",
                "irrigation_electricity_usage",
                "irrigation_electricity_source",
                "irrigation_electricity_grid_name",
                "total_area",
                "total_energy_usage",
                "total_fuel_usage",
                "skip_assign_practice",
                "seeding_rate",
                "seeding_rate_unit",
                "seed_variety",
                name="attributetypes",
            ),
            nullable=True,
        ),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.Column("locked", sa.Boolean(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("options", sa.JSON(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("dependencies", sa.JSON(), nullable=True),
        sa.Column("default_value", sa.String(length=50), nullable=True),
        sa.Column("messages", sa.JSON(), nullable=True),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("visible", sa.Boolean(), nullable=False),
        sa.Column("min_val", sa.String(length=40), nullable=True),
        sa.Column("max_val", sa.String(length=40), nullable=True),
        sa.Column(
            "autofill",
            sa.Boolean(),
            server_default="0",
            nullable=False,
            comment="Auto fills the values in Attribute for the entity",
        ),
        sa.ForeignKeyConstraint(
            ["parent_stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Some stages will expect tabular data, in which case they will contain one Attribute for each type of column of data they expect. Attributes have an order (i.e. which column an attribute will appear in) and a type (e.g. 'crop_type', 'water_amount', 'farm_name'). The actual table data entered by a farmer for a stage will be stored in db table 'mrv_values', so an Attribute tells how what type of data is stored in any given mrv_values row.",
    )
    op.create_index(op.f("ix_mrv_attributes_deleted_at"), "mrv_attributes", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_attributes_id"), "mrv_attributes", ["id"], unique=False)
    op.create_table(
        "mrv_field_eligibilities",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("eligible", sa.Boolean(), nullable=True),
        sa.Column("eligible_practices", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_field_eligibilities_id"), "mrv_field_eligibilities", ["id"], unique=False)
    op.create_table(
        "mrv_fields_baseline",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("baseline_year", mysql.INTEGER(display_width=10, unsigned=True), nullable=True),
        sa.Column("is_returning", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Collection of metadata for fields",
    )
    op.create_index(op.f("ix_mrv_fields_baseline_id"), "mrv_fields_baseline", ["id"], unique=False)
    op.create_table(
        "mrv_fields_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("fs_field_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("kml_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=False),
        sa.Column("group_id", mysql.INTEGER(display_width=10, unsigned=True), nullable=True),
        sa.Column("md5", sa.String(length=32), nullable=False),
        sa.Column("area", sa.Float(), nullable=False, comment="Area in ha."),
        sa.Column("reason", sa.Enum("api", "fmi", name="fieldhistorychangereason"), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="A history of every time a fields related core entity has changed for auditing purposes",
    )
    op.create_index(op.f("ix_mrv_fields_history_id"), "mrv_fields_history", ["id"], unique=False)
    op.create_table(
        "mrv_http_requests",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=True),
        sa.Column("year", sa.SmallInteger(), nullable=True),
        sa.Column(
            "status",
            sa.Enum("new", "waiting_for_callback", "to_retry", "success", "failed", name="statuschoices"),
            nullable=False,
        ),
        sa.Column("retries", sa.Integer(), nullable=True),
        sa.Column("http_method", sa.String(length=10), nullable=False),
        sa.Column("url", sa.Text(), nullable=False),
        sa.Column("body", sa.JSON(), nullable=True),
        sa.Column("response_code", sa.Integer(), nullable=True),
        sa.Column("response_body", sa.JSON(), nullable=True),
        sa.Column("callback_body", sa.JSON(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("trace_id", sa.String(length=36), nullable=True),
        sa.Column("email_sent", sa.Boolean(), nullable=True),
        sa.Column("source", sa.Enum("enrolment", "conflict", "dndc", name="httprequestsource"), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_http_requests_id"), "mrv_http_requests", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_http_requests_trace_id"), "mrv_http_requests", ["trace_id"], unique=False)
    op.create_table(
        "mrv_monitor_request_store_jobs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("request_store_id", sa.Integer(), nullable=False),
        sa.Column("job_id", sa.String(length=255), nullable=True),
        sa.Column("year", sa.Integer(), nullable=False),
        sa.Column(
            "payload",
            sa.JSON(),
            nullable=True,
            comment="Stores payload of request to recreate monitor api calls via cron",
        ),
        sa.Column(
            "status",
            sa.Enum("PENDING", "SUCCESS", "FAILED", "PARTIALLY_FAILED", "CANCELLED", name="requeststorejobstatus"),
            nullable=False,
            comment="Status of request. We keep single request in pending/completed/failed/partially_failed status per project per year, rest will be in cancelled/failed state",
        ),
        sa.Column("retry_count", sa.Integer(), nullable=False, comment="Number of times request is retried"),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["request_store_id"],
            ["mrv_monitor_request_store.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Store job id and status for request store per year",
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_jobs_id"), "mrv_monitor_request_store_jobs", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_jobs_job_id"), "mrv_monitor_request_store_jobs", ["job_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_jobs_request_store_id"),
        "mrv_monitor_request_store_jobs",
        ["request_store_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_jobs_year"), "mrv_monitor_request_store_jobs", ["year"], unique=False
    )
    op.create_table(
        "mrv_nutrient_template_applications",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("nutrient_template_id", sa.Integer(), nullable=False),
        sa.Column("relative_days", sa.Integer(), nullable=True),
        sa.Column(
            "relative_to",
            sa.Enum(
                "BEFORE_PLANTING", "AFTER_PLANTING", "BEFORE_TERMINATION", "AFTER_TERMINATION", name="relativedatetype"
            ),
            nullable=True,
        ),
        sa.Column("application_product", sa.String(length=128), nullable=True),
        sa.Column("application_rate", sa.DECIMAL(precision=8, scale=2), nullable=True),
        sa.Column(
            "application_rate_unit",
            sa.Enum(
                "KG_HA", "MT_HA", "L_HA", "LBS_AC", "GAL_AC", "KG_SQM", "MT_SQM", "L_SQM", name="applicationrateunit"
            ),
            nullable=True,
        ),
        sa.Column(
            "application_method",
            sa.Enum("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", name="applicationmethod"),
            nullable=True,
        ),
        sa.Column("application_depth", sa.Integer(), nullable=True),
        sa.Column("water_amount", sa.DECIMAL(precision=8, scale=2), nullable=True),
        sa.Column("water_amount_unit", sa.Enum("MM", "IN", "GAL", name="wateramountunit"), nullable=True),
        sa.ForeignKeyConstraint(
            ["nutrient_template_id"],
            ["mrv_nutrient_templates.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_nutrient_template_applications_id"), "mrv_nutrient_template_applications", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_nutrient_template_applications_nutrient_template_id"),
        "mrv_nutrient_template_applications",
        ["nutrient_template_id"],
        unique=False,
    )
    op.create_table(
        "mrv_project_completion",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("percentage_complete", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_project_completion_created_at"), "mrv_project_completion", ["created_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_project_completion_id"), "mrv_project_completion", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_project_completion_project_id"), "mrv_project_completion", ["project_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_project_completion_stage_id"), "mrv_project_completion", ["stage_id"], unique=False)
    op.create_table(
        "mrv_project_contract_line_items",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("contract_id", sa.Integer(), nullable=False),
        sa.Column("note", sa.JSON(), nullable=True),
        sa.Column("value", sa.DECIMAL(precision=11, scale=4), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=True),
        sa.Column("field_area", sa.Float(), nullable=True),
        sa.Column("quantity", sa.DECIMAL(precision=11, scale=4), nullable=False),
        sa.Column("units", sa.String(length=255), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["contract_id"],
            ["mrv_project_contracts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_project_contract_line_items_created_at"),
        "mrv_project_contract_line_items",
        ["created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_project_contract_line_items_deleted_at"),
        "mrv_project_contract_line_items",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_project_contract_line_items_id"), "mrv_project_contract_line_items", ["id"], unique=False
    )
    op.create_table(
        "mrv_project_values",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("type_", sa.String(length=15), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=True),
        sa.Column("key", sa.String(length=255), nullable=False),
        sa.Column("value", sa.String(length=255), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_project_values_deleted_at"), "mrv_project_values", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_project_values_id"), "mrv_project_values", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_project_values_project_id"), "mrv_project_values", ["project_id"], unique=False)
    op.create_index(op.f("ix_mrv_project_values_stage_id"), "mrv_project_values", ["stage_id"], unique=False)
    op.create_table(
        "mrv_reporting_chart_instances",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("row_id", sa.Integer(), nullable=False),
        sa.Column("chart_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["chart_id"],
            ["mrv_reporting_chart_presets.id"],
        ),
        sa.ForeignKeyConstraint(
            ["row_id"],
            ["mrv_reporting_dashboard_rows.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_reporting_chart_instances_deleted_at"),
        "mrv_reporting_chart_instances",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(op.f("ix_mrv_reporting_chart_instances_id"), "mrv_reporting_chart_instances", ["id"], unique=False)
    op.create_table(
        "mrv_role_user_restrictions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("role_user_id", sa.Integer(), nullable=True),
        sa.Column("restriction_type", sa.Enum("CUSTOM_INPUT_COOP", name="restrictiontypes"), nullable=False),
        sa.ForeignKeyConstraint(
            ["role_user_id"],
            ["mrv_roles_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_role_user_restrictions_id"), "mrv_role_user_restrictions", ["id"], unique=False)
    op.create_table(
        "mrv_stage_eligibility_config",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("tillage_config", sa.Integer(), nullable=True),
        sa.Column("cover_crop_config", sa.Integer(), nullable=True),
        sa.Column("nm_rate_reduction_config", sa.Integer(), nullable=True),
        sa.Column("nm_split_application_config", sa.Integer(), nullable=True),
        sa.Column("nm_timing_config", sa.Integer(), nullable=True),
        sa.Column("rice_irrigation_config", sa.Integer(), nullable=True),
        sa.Column("grazing_interventions_config", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["cover_crop_config"],
            ["mrv_cover_crop_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["grazing_interventions_config"],
            ["mrv_grazing_interventions_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["nm_rate_reduction_config"],
            ["mrv_nm_rate_reduction_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["nm_split_application_config"],
            ["mrv_nm_split_application_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["nm_timing_config"],
            ["mrv_nm_timing_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["rice_irrigation_config"],
            ["mrv_rice_irrigation_eligibility_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tillage_config"],
            ["mrv_tillage_eligibility_config.id"],
        ),
        sa.PrimaryKeyConstraint("id", "stage_id"),
    )
    op.create_index(
        op.f("ix_mrv_stage_eligibility_config_deleted_at"), "mrv_stage_eligibility_config", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_stage_eligibility_config_id"), "mrv_stage_eligibility_config", ["id"], unique=False)
    op.create_table(
        "mrv_attribute_events_mapping",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("monitor_event_id", sa.Integer(), nullable=False),
        sa.Column("event_key", sa.String(length=255), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["monitor_event_id"],
            ["mrv_monitor_events.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Mapping of event key from monitor event and attribute id to create values for a project in field",
    )
    op.create_index(op.f("ix_mrv_attribute_events_mapping_id"), "mrv_attribute_events_mapping", ["id"], unique=False)
    op.create_table(
        "mrv_conflict_http_requests",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("conflict_result_id", sa.Integer(), nullable=False),
        sa.Column("http_request_id", sa.Integer(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["conflict_result_id"],
            ["mrv_conflict_results.id"],
        ),
        sa.ForeignKeyConstraint(
            ["http_request_id"],
            ["mrv_http_requests.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_conflict_http_requests_id"), "mrv_conflict_http_requests", ["id"], unique=False)
    op.create_table(
        "mrv_contract_attributes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_contract_attributes_deleted_at"), "mrv_contract_attributes", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_contract_attributes_id"), "mrv_contract_attributes", ["id"], unique=False)
    op.create_table(
        "mrv_crop_type_selection",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("crop_type", sa.Integer(), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("crop_type", "attribute_id", name="crop_type_attribute_id_idx"),
    )
    op.create_index(
        op.f("ix_mrv_crop_type_selection_attribute_id"), "mrv_crop_type_selection", ["attribute_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_crop_type_selection_crop_type"), "mrv_crop_type_selection", ["crop_type"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_crop_type_selection_deleted_at"), "mrv_crop_type_selection", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_crop_type_selection_id"), "mrv_crop_type_selection", ["id"], unique=False)
    op.create_table(
        "mrv_dndc_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("year", sa.Integer(), nullable=False),
        sa.Column("results", sa.JSON(), nullable=False),
        sa.Column("request_id", sa.Integer(), nullable=True),
        sa.Column("type_", sa.Enum("baseline", "project", name="dndcresultstype"), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=True),
        sa.Column(
            "phase",
            sa.Enum(
                "ENROLMENT", "MONITORING", "QA_QC", "SOIL_SAMPLING", "DASHBOARDS", "VERIFICATION", name="phasetypes"
            ),
            nullable=False,
        ),
        sa.Column("measure_api_field_request_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_id"],
            ["mrv_http_requests.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_dndc_tasks.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_dndc_results_id"), "mrv_dndc_results", ["id"], unique=False)
    op.create_table(
        "mrv_http_callbacks",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("request_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("year", sa.SmallInteger(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("new", "waiting_for_callback", "to_retry", "success", "failed", name="statuschoices"),
            nullable=False,
        ),
        sa.Column("callback_body", sa.JSON(), nullable=True),
        sa.Column("request_body", sa.JSON(), nullable=True),
        sa.Column("retries", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_id"],
            ["mrv_http_requests.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_http_callbacks_id"), "mrv_http_callbacks", ["id"], unique=False)
    op.create_table(
        "mrv_monitor_response_store",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("request_store_job_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("field_md5", sa.String(length=255), nullable=False),
        sa.Column(
            "response",
            sa.JSON(),
            nullable=True,
            comment="Stores only row crop data from monitor api response per field per year",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_store_job_id"],
            ["mrv_monitor_request_store_jobs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Store response from monitor api for a project",
    )
    op.create_index(
        op.f("ix_mrv_monitor_response_store_field_id"), "mrv_monitor_response_store", ["field_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_monitor_response_store_id"), "mrv_monitor_response_store", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_monitor_response_store_request_store_job_id"),
        "mrv_monitor_response_store",
        ["request_store_job_id"],
        unique=False,
    )
    op.create_table(
        "mrv_optis_labels_attributes_mapping",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "optis_label",
            sa.Enum("curr_crop_id", "cover_crop", "spring_till_class", "fall_till_class", name="optislabels"),
            nullable=False,
        ),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("optis_label", "attribute_id", name="_optis_label_attribute_id_uc"),
    )
    op.create_index(
        op.f("ix_mrv_optis_labels_attributes_mapping_id"), "mrv_optis_labels_attributes_mapping", ["id"], unique=False
    )
    op.create_table(
        "mrv_phase_commercials",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("phase_id", sa.Integer(), nullable=False),
        sa.Column("start_date", sa.DateTime(), nullable=False),
        sa.Column("end_date", sa.DateTime(), nullable=False),
        sa.Column("payment", sa.DECIMAL(precision=11, scale=4), nullable=False),
        sa.Column(
            "payment_for", sa.Enum("FIELD_AREA", "ESTIMATED_GHG", name="commercialspaymentfortype"), nullable=False
        ),
        sa.Column("attribute_id", sa.Integer(), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_phase_commercials_deleted_at"), "mrv_phase_commercials", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_phase_commercials_id"), "mrv_phase_commercials", ["id"], unique=False)
    op.create_table(
        "mrv_practice_stage_attribute_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("phase_practice_config_id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_practice_config_id"],
            ["mrv_phase_practice_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "phase_practice_config_id",
            "stage_id",
            "attribute_id",
            name="phase_practice_config_id_stage_id_attribute_id_idx",
        ),
    )
    op.create_index(
        op.f("ix_mrv_practice_stage_attribute_config_deleted_at"),
        "mrv_practice_stage_attribute_config",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_practice_stage_attribute_config_id"), "mrv_practice_stage_attribute_config", ["id"], unique=False
    )
    op.create_table(
        "mrv_role_user_restrictions_values",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("restriction_id", sa.Integer(), nullable=False),
        sa.Column("value", sa.String(length=255), nullable=False),
        sa.ForeignKeyConstraint(
            ["restriction_id"],
            ["mrv_role_user_restrictions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_role_user_restrictions_values_id"), "mrv_role_user_restrictions_values", ["id"], unique=False
    )
    op.create_table(
        "mrv_values",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("value", sa.VARCHAR(length=50), nullable=True),
        sa.Column("field_id", sa.Integer(), nullable=True),
        sa.Column("row_id", sa.Integer(), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=True),
        sa.Column(
            "progress",
            sa.Enum("enrolment", "monitoring", "verification", "stratification", "qa_qc", name="progresschoices"),
            nullable=False,
        ),
        sa.Column("locked", sa.Boolean(), nullable=False),
        sa.Column("read_only", sa.Boolean(), nullable=False),
        sa.Column("confirmed", sa.Boolean(), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column(
            "source",
            sa.Enum(
                "user",
                "agrian",
                "agworld",
                "agx",
                "climate",
                "efc",
                "john_deere",
                "optis",
                "other_fms",
                "productionwise",
                "terravion",
                "agriwebb",
                "template",
                "smag",
                name="importdatasources",
            ),
            nullable=False,
        ),
        sa.Column("confidence", sa.Float(), nullable=True),
        sa.Column("is_valid", sa.Boolean(), nullable=True),
        sa.Column("category", sa.String(length=255), nullable=True),
        sa.Column("entity_id", sa.Integer(), nullable=True),
        sa.Column("entity_type", sa.Enum("field", "farm", "mob", name="entitytypechoices"), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="The frontend can show a grid of data (i.e. a UI 'table') in the frontend in a given Stage. Data inside each cell in that grid is stored in mrv_values. A row from mrv_values corresponds to a cell in that UI grid. The data itself is either entered manually by a producer, or is fetched via FMS. The DB field 'value' stores the actual value as a string. The type of that value comes from the attribute ('attribute_id'). Given that each farm field gets its own UI grid, 'field_id' tells you which grid the value belongs to, 'row_id' the row in the grid, and the column also comes from the attribute, via mrv_attributes.order.",
    )
    op.create_index(op.f("ix_mrv_values_id"), "mrv_values", ["id"], unique=False)
    op.create_table(
        "mrv_values_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("value", sa.VARCHAR(length=50), nullable=True),
        sa.Column("field_id", sa.Integer(), nullable=True),
        sa.Column("row_id", sa.Integer(), nullable=False),
        sa.Column("attribute_id", sa.Integer(), nullable=True),
        sa.Column(
            "progress",
            sa.Enum("enrolment", "monitoring", "verification", "stratification", "qa_qc", name="progresschoices"),
            nullable=False,
        ),
        sa.Column("locked", sa.Boolean(), nullable=False),
        sa.Column("confirmed", sa.Boolean(), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column(
            "source",
            sa.Enum(
                "user",
                "agrian",
                "agworld",
                "agx",
                "climate",
                "efc",
                "john_deere",
                "optis",
                "other_fms",
                "productionwise",
                "terravion",
                "agriwebb",
                "template",
                "smag",
                name="importdatasources",
            ),
            nullable=False,
        ),
        sa.Column("entity_id", sa.Integer(), nullable=True),
        sa.Column("entity_type", sa.Enum("field", "farm", "mob", name="entitytypechoices"), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_values_history_id"), "mrv_values_history", ["id"], unique=False)
    op.create_table(
        "mrv_verification_dashboard_conflict_types",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("verification_dashboard_id", sa.Integer(), nullable=False),
        sa.Column(
            "conflict_type", sa.Enum("SUMMER_CROP", "WINTER_CROP", "TILLAGE", name="conflicttype"), nullable=False
        ),
        sa.Column("attribute_id", sa.Integer(), nullable=False),
        sa.Column("practice_year", sa.Integer(), nullable=True),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.ForeignKeyConstraint(
            ["attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(["verification_dashboard_id"], ["mrv_verification_dashboard.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "verification_dashboard_id",
            "conflict_type",
            "attribute_id",
            name="_verification_dashboard_id_conflict_type_attribute_id_uc",
        ),
    )
    op.create_index(
        op.f("ix_mrv_verification_dashboard_conflict_types_id"),
        "mrv_verification_dashboard_conflict_types",
        ["id"],
        unique=False,
    )
    op.create_table(
        "mrv_conflicts",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "conflict_type", sa.Enum("SUMMER_CROP", "WINTER_CROP", "TILLAGE", name="conflicttype"), nullable=False
        ),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("producer_name", sa.String(length=255), nullable=True),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("baseline_value", sa.Integer(), nullable=True),
        sa.Column("baseline_attribute_id", sa.Integer(), nullable=False),
        sa.Column("baseline_tillage_period", sa.Enum("fall", "spring", name="tillageperiod"), nullable=True),
        sa.Column("monitoring_value", sa.Integer(), nullable=True),
        sa.Column("monitoring_attribute_id", sa.Integer(), nullable=False),
        sa.Column("monitoring_tillage_period", sa.Enum("fall", "spring", name="tillageperiod"), nullable=True),
        sa.Column("optis_value", sa.String(length=255), nullable=True),
        sa.Column("optis_value_confidence", sa.Float(), nullable=False),
        sa.Column("optis_tillage_period", sa.Enum("fall", "spring", name="tillageperiod"), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["baseline_attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["baseline_value"],
            ["mrv_values.id"],
        ),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["monitoring_attribute_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["monitoring_value"],
            ["mrv_values.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("conflict_type", "project_id", "field_id", name="_conflict_type_project_id_field_id_uc"),
    )
    op.create_index(op.f("ix_mrv_conflicts_field_id"), "mrv_conflicts", ["field_id"], unique=False)
    op.create_index(op.f("ix_mrv_conflicts_id"), "mrv_conflicts", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_conflicts_project_id"), "mrv_conflicts", ["project_id"], unique=False)
    op.create_table(
        "mrv_conflict_resolution_files",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("conflict_id", sa.Integer(), nullable=False),
        sa.Column("file_name", sa.String(length=255), nullable=True),
        sa.Column("gcs_object_path", sa.String(length=255), nullable=True),
        sa.Column("is_uploaded", sa.Boolean(), nullable=False),
        sa.Column(
            "usage",
            sa.Enum("CUSTOMER", "FCI", "NDTI", "NDVI", "RGB", name="conflictresolutionfilesusage"),
            nullable=False,
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["conflict_id"],
            ["mrv_conflicts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_conflict_resolution_files_conflict_id"),
        "mrv_conflict_resolution_files",
        ["conflict_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_conflict_resolution_files_deleted_at"),
        "mrv_conflict_resolution_files",
        ["deleted_at"],
        unique=False,
    )
    op.create_index(op.f("ix_mrv_conflict_resolution_files_id"), "mrv_conflict_resolution_files", ["id"], unique=False)
    op.create_table(
        "mrv_conflict_resolution_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("conflict_id", sa.Integer(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("AGREED_WITH_REGROW", "AGREED_WITH_PRODUCER", "UNRESOLVED", name="conflictstatus"),
            nullable=False,
        ),
        sa.Column("value_id", sa.Integer(), nullable=True),
        sa.Column("old_value", sa.String(length=255), nullable=True),
        sa.Column("new_value", sa.String(length=255), nullable=True),
        sa.Column("created_at", mysql.DATETIME(fsp=6), server_default=sa.text("CURRENT_TIMESTAMP(6)"), nullable=False),
        sa.ForeignKeyConstraint(
            ["conflict_id"],
            ["mrv_conflicts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["value_id"],
            ["mrv_values.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_conflict_resolution_history_conflict_id"),
        "mrv_conflict_resolution_history",
        ["conflict_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_conflict_resolution_history_id"), "mrv_conflict_resolution_history", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_conflict_resolution_history_value_id"),
        "mrv_conflict_resolution_history",
        ["value_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_conflict_resolution_history_value_id"), table_name="mrv_conflict_resolution_history")
    op.drop_index(op.f("ix_mrv_conflict_resolution_history_id"), table_name="mrv_conflict_resolution_history")
    op.drop_index(op.f("ix_mrv_conflict_resolution_history_conflict_id"), table_name="mrv_conflict_resolution_history")
    op.drop_table("mrv_conflict_resolution_history")
    op.drop_index(op.f("ix_mrv_conflict_resolution_files_id"), table_name="mrv_conflict_resolution_files")
    op.drop_index(op.f("ix_mrv_conflict_resolution_files_deleted_at"), table_name="mrv_conflict_resolution_files")
    op.drop_index(op.f("ix_mrv_conflict_resolution_files_conflict_id"), table_name="mrv_conflict_resolution_files")
    op.drop_table("mrv_conflict_resolution_files")
    op.drop_index(op.f("ix_mrv_conflicts_project_id"), table_name="mrv_conflicts")
    op.drop_index(op.f("ix_mrv_conflicts_id"), table_name="mrv_conflicts")
    op.drop_index(op.f("ix_mrv_conflicts_field_id"), table_name="mrv_conflicts")
    op.drop_table("mrv_conflicts")
    op.drop_index(
        op.f("ix_mrv_verification_dashboard_conflict_types_id"), table_name="mrv_verification_dashboard_conflict_types"
    )
    op.drop_table("mrv_verification_dashboard_conflict_types")
    op.drop_index(op.f("ix_mrv_values_history_id"), table_name="mrv_values_history")
    op.drop_table("mrv_values_history")
    op.drop_index(op.f("ix_mrv_values_id"), table_name="mrv_values")
    op.drop_table("mrv_values")
    op.drop_index(op.f("ix_mrv_role_user_restrictions_values_id"), table_name="mrv_role_user_restrictions_values")
    op.drop_table("mrv_role_user_restrictions_values")
    op.drop_index(op.f("ix_mrv_practice_stage_attribute_config_id"), table_name="mrv_practice_stage_attribute_config")
    op.drop_index(
        op.f("ix_mrv_practice_stage_attribute_config_deleted_at"), table_name="mrv_practice_stage_attribute_config"
    )
    op.drop_table("mrv_practice_stage_attribute_config")
    op.drop_index(op.f("ix_mrv_phase_commercials_id"), table_name="mrv_phase_commercials")
    op.drop_index(op.f("ix_mrv_phase_commercials_deleted_at"), table_name="mrv_phase_commercials")
    op.drop_table("mrv_phase_commercials")
    op.drop_index(op.f("ix_mrv_optis_labels_attributes_mapping_id"), table_name="mrv_optis_labels_attributes_mapping")
    op.drop_table("mrv_optis_labels_attributes_mapping")
    op.drop_index(op.f("ix_mrv_monitor_response_store_request_store_job_id"), table_name="mrv_monitor_response_store")
    op.drop_index(op.f("ix_mrv_monitor_response_store_id"), table_name="mrv_monitor_response_store")
    op.drop_index(op.f("ix_mrv_monitor_response_store_field_id"), table_name="mrv_monitor_response_store")
    op.drop_table("mrv_monitor_response_store")
    op.drop_index(op.f("ix_mrv_http_callbacks_id"), table_name="mrv_http_callbacks")
    op.drop_table("mrv_http_callbacks")
    op.drop_index(op.f("ix_mrv_dndc_results_id"), table_name="mrv_dndc_results")
    op.drop_table("mrv_dndc_results")
    op.drop_index(op.f("ix_mrv_crop_type_selection_id"), table_name="mrv_crop_type_selection")
    op.drop_index(op.f("ix_mrv_crop_type_selection_deleted_at"), table_name="mrv_crop_type_selection")
    op.drop_index(op.f("ix_mrv_crop_type_selection_crop_type"), table_name="mrv_crop_type_selection")
    op.drop_index(op.f("ix_mrv_crop_type_selection_attribute_id"), table_name="mrv_crop_type_selection")
    op.drop_table("mrv_crop_type_selection")
    op.drop_index(op.f("ix_mrv_contract_attributes_id"), table_name="mrv_contract_attributes")
    op.drop_index(op.f("ix_mrv_contract_attributes_deleted_at"), table_name="mrv_contract_attributes")
    op.drop_table("mrv_contract_attributes")
    op.drop_index(op.f("ix_mrv_conflict_http_requests_id"), table_name="mrv_conflict_http_requests")
    op.drop_table("mrv_conflict_http_requests")
    op.drop_index(op.f("ix_mrv_attribute_events_mapping_id"), table_name="mrv_attribute_events_mapping")
    op.drop_table("mrv_attribute_events_mapping")
    op.drop_index(op.f("ix_mrv_stage_eligibility_config_id"), table_name="mrv_stage_eligibility_config")
    op.drop_index(op.f("ix_mrv_stage_eligibility_config_deleted_at"), table_name="mrv_stage_eligibility_config")
    op.drop_table("mrv_stage_eligibility_config")
    op.drop_index(op.f("ix_mrv_role_user_restrictions_id"), table_name="mrv_role_user_restrictions")
    op.drop_table("mrv_role_user_restrictions")
    op.drop_index(op.f("ix_mrv_reporting_chart_instances_id"), table_name="mrv_reporting_chart_instances")
    op.drop_index(op.f("ix_mrv_reporting_chart_instances_deleted_at"), table_name="mrv_reporting_chart_instances")
    op.drop_table("mrv_reporting_chart_instances")
    op.drop_index(op.f("ix_mrv_project_values_stage_id"), table_name="mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_values_project_id"), table_name="mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_values_id"), table_name="mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_values_deleted_at"), table_name="mrv_project_values")
    op.drop_table("mrv_project_values")
    op.drop_index(op.f("ix_mrv_project_contract_line_items_id"), table_name="mrv_project_contract_line_items")
    op.drop_index(op.f("ix_mrv_project_contract_line_items_deleted_at"), table_name="mrv_project_contract_line_items")
    op.drop_index(op.f("ix_mrv_project_contract_line_items_created_at"), table_name="mrv_project_contract_line_items")
    op.drop_table("mrv_project_contract_line_items")
    op.drop_index(op.f("ix_mrv_project_completion_stage_id"), table_name="mrv_project_completion")
    op.drop_index(op.f("ix_mrv_project_completion_project_id"), table_name="mrv_project_completion")
    op.drop_index(op.f("ix_mrv_project_completion_id"), table_name="mrv_project_completion")
    op.drop_index(op.f("ix_mrv_project_completion_created_at"), table_name="mrv_project_completion")
    op.drop_table("mrv_project_completion")
    op.drop_index(
        op.f("ix_mrv_nutrient_template_applications_nutrient_template_id"),
        table_name="mrv_nutrient_template_applications",
    )
    op.drop_index(op.f("ix_mrv_nutrient_template_applications_id"), table_name="mrv_nutrient_template_applications")
    op.drop_table("mrv_nutrient_template_applications")
    op.drop_index(op.f("ix_mrv_monitor_request_store_jobs_year"), table_name="mrv_monitor_request_store_jobs")
    op.drop_index(
        op.f("ix_mrv_monitor_request_store_jobs_request_store_id"), table_name="mrv_monitor_request_store_jobs"
    )
    op.drop_index(op.f("ix_mrv_monitor_request_store_jobs_job_id"), table_name="mrv_monitor_request_store_jobs")
    op.drop_index(op.f("ix_mrv_monitor_request_store_jobs_id"), table_name="mrv_monitor_request_store_jobs")
    op.drop_table("mrv_monitor_request_store_jobs")
    op.drop_index(op.f("ix_mrv_http_requests_trace_id"), table_name="mrv_http_requests")
    op.drop_index(op.f("ix_mrv_http_requests_id"), table_name="mrv_http_requests")
    op.drop_table("mrv_http_requests")
    op.drop_index(op.f("ix_mrv_fields_history_id"), table_name="mrv_fields_history")
    op.drop_table("mrv_fields_history")
    op.drop_index(op.f("ix_mrv_fields_baseline_id"), table_name="mrv_fields_baseline")
    op.drop_table("mrv_fields_baseline")
    op.drop_index(op.f("ix_mrv_field_eligibilities_id"), table_name="mrv_field_eligibilities")
    op.drop_table("mrv_field_eligibilities")
    op.drop_index(op.f("ix_mrv_attributes_id"), table_name="mrv_attributes")
    op.drop_index(op.f("ix_mrv_attributes_deleted_at"), table_name="mrv_attributes")
    op.drop_table("mrv_attributes")
    op.drop_index(
        op.f("ix_mrv_verification_dashboard_table_fields_verification_dashboard_id"),
        table_name="mrv_verification_dashboard_table_fields",
    )
    op.drop_index(
        op.f("ix_mrv_verification_dashboard_table_fields_id"), table_name="mrv_verification_dashboard_table_fields"
    )
    op.drop_table("mrv_verification_dashboard_table_fields")
    op.drop_index(
        op.f("ix_mrv_verification_dashboard_stat_fields_id"), table_name="mrv_verification_dashboard_stat_fields"
    )
    op.drop_table("mrv_verification_dashboard_stat_fields")
    op.drop_index(op.f("ix_mrv_user_project_permissions_id"), table_name="mrv_user_project_permissions")
    op.drop_index(op.f("ix_mrv_user_project_permissions_deleted_at"), table_name="mrv_user_project_permissions")
    op.drop_table("mrv_user_project_permissions")
    op.drop_index(op.f("ix_mrv_stages_id"), table_name="mrv_stages")
    op.drop_index(op.f("ix_mrv_stages_deleted_at"), table_name="mrv_stages")
    op.drop_table("mrv_stages")
    op.drop_index(op.f("ix_mrv_roles_users_id"), table_name="mrv_roles_users")
    op.drop_index("ix_mrv_roles_users_fs_user_id", table_name="mrv_roles_users")
    op.drop_index(op.f("ix_mrv_roles_users_deleted_at"), table_name="mrv_roles_users")
    op.drop_table("mrv_roles_users")
    op.drop_index(op.f("ix_mrv_roles_permissions_role_id"), table_name="mrv_roles_permissions")
    op.drop_index(op.f("ix_mrv_roles_permissions_id"), table_name="mrv_roles_permissions")
    op.drop_table("mrv_roles_permissions")
    op.drop_index(op.f("ix_mrv_reporting_dashboard_rows_id"), table_name="mrv_reporting_dashboard_rows")
    op.drop_index(op.f("ix_mrv_reporting_dashboard_rows_deleted_at"), table_name="mrv_reporting_dashboard_rows")
    op.drop_table("mrv_reporting_dashboard_rows")
    op.drop_table("mrv_region_to_region")
    op.drop_index(op.f("ix_mrv_project_copy_mapping_id"), table_name="mrv_project_copy_mapping")
    op.drop_table("mrv_project_copy_mapping")
    op.drop_index(op.f("ix_mrv_project_contracts_signed_at"), table_name="mrv_project_contracts")
    op.drop_index(op.f("ix_mrv_project_contracts_id"), table_name="mrv_project_contracts")
    op.drop_index(op.f("ix_mrv_project_contracts_deleted_at"), table_name="mrv_project_contracts")
    op.drop_table("mrv_project_contracts")
    op.drop_index(op.f("ix_mrv_program_boundaries_id"), table_name="mrv_program_boundaries")
    op.drop_index(op.f("ix_mrv_program_boundaries_deleted_at"), table_name="mrv_program_boundaries")
    op.drop_table("mrv_program_boundaries")
    op.drop_index(op.f("ix_mrv_phase_practice_config_id"), table_name="mrv_phase_practice_config")
    op.drop_index(op.f("ix_mrv_phase_practice_config_deleted_at"), table_name="mrv_phase_practice_config")
    op.drop_table("mrv_phase_practice_config")
    op.drop_index(op.f("ix_mrv_nutrient_templates_project_id"), table_name="mrv_nutrient_templates")
    op.drop_index(op.f("ix_mrv_nutrient_templates_id"), table_name="mrv_nutrient_templates")
    op.drop_index(op.f("ix_mrv_nutrient_templates_deleted_at"), table_name="mrv_nutrient_templates")
    op.drop_table("mrv_nutrient_templates")
    op.drop_index(op.f("ix_mrv_notifications_id"), table_name="mrv_notifications")
    op.drop_table("mrv_notifications")
    op.drop_index(op.f("ix_mrv_monitor_request_store_year_start"), table_name="mrv_monitor_request_store")
    op.drop_index(op.f("ix_mrv_monitor_request_store_year_end"), table_name="mrv_monitor_request_store")
    op.drop_index(op.f("ix_mrv_monitor_request_store_project_id"), table_name="mrv_monitor_request_store")
    op.drop_index(op.f("ix_mrv_monitor_request_store_id"), table_name="mrv_monitor_request_store")
    op.drop_table("mrv_monitor_request_store")
    op.drop_index(op.f("ix_mrv_mobs_id"), table_name="mrv_mobs")
    op.drop_table("mrv_mobs")
    op.drop_index(op.f("ix_mrv_fields_id"), table_name="mrv_fields")
    op.drop_index(op.f("ix_mrv_fields_deleted_at"), table_name="mrv_fields")
    op.drop_table("mrv_fields")
    op.drop_index(op.f("ix_mrv_farms_id"), table_name="mrv_farms")
    op.drop_index(op.f("ix_mrv_farms_deleted_at"), table_name="mrv_farms")
    op.drop_table("mrv_farms")
    op.drop_index(op.f("ix_mrv_dndc_tasks_id"), table_name="mrv_dndc_tasks")
    op.drop_index(op.f("ix_mrv_dndc_tasks_created_at"), table_name="mrv_dndc_tasks")
    op.drop_table("mrv_dndc_tasks")
    op.drop_index(op.f("ix_mrv_dndc_contracted_results_project_id"), table_name="mrv_dndc_contracted_results")
    op.drop_index(op.f("ix_mrv_dndc_contracted_results_id"), table_name="mrv_dndc_contracted_results")
    op.drop_table("mrv_dndc_contracted_results")
    op.drop_index(op.f("ix_mrv_contract_template_id"), table_name="mrv_contract_template")
    op.drop_table("mrv_contract_template")
    op.drop_index(op.f("ix_mrv_confidence_events_id"), table_name="mrv_confidence_events")
    op.drop_table("mrv_confidence_events")
    op.drop_index(op.f("ix_mrv_boundary_rule_deviation_threshold_crossed"), table_name="mrv_boundary_rule_deviation")
    op.drop_index(op.f("ix_mrv_boundary_rule_deviation_percent_overlap"), table_name="mrv_boundary_rule_deviation")
    op.drop_index(op.f("ix_mrv_boundary_rule_deviation_allow_anyway"), table_name="mrv_boundary_rule_deviation")
    op.drop_table("mrv_boundary_rule_deviation")
    op.drop_index(op.f("ix_mrv_verification_dashboard_program_id"), table_name="mrv_verification_dashboard")
    op.drop_index(op.f("ix_mrv_verification_dashboard_id"), table_name="mrv_verification_dashboard")
    op.drop_table("mrv_verification_dashboard")
    op.drop_index(op.f("ix_mrv_user_program_permissions_id"), table_name="mrv_user_program_permissions")
    op.drop_index(op.f("ix_mrv_user_program_permissions_deleted_at"), table_name="mrv_user_program_permissions")
    op.drop_table("mrv_user_program_permissions")
    op.drop_index(op.f("ix_mrv_roles_program_id"), table_name="mrv_roles")
    op.drop_index(op.f("ix_mrv_roles_id"), table_name="mrv_roles")
    op.drop_table("mrv_roles")
    op.drop_index(op.f("ix_mrv_reporting_dashboards_to_programs_id"), table_name="mrv_reporting_dashboards_to_programs")
    op.drop_index(
        op.f("ix_mrv_reporting_dashboards_to_programs_deleted_at"), table_name="mrv_reporting_dashboards_to_programs"
    )
    op.drop_table("mrv_reporting_dashboards_to_programs")
    op.drop_index(op.f("ix_mrv_reporting_dashboard_sections_id"), table_name="mrv_reporting_dashboard_sections")
    op.drop_index(op.f("ix_mrv_reporting_dashboard_sections_deleted_at"), table_name="mrv_reporting_dashboard_sections")
    op.drop_table("mrv_reporting_dashboard_sections")
    op.drop_index(op.f("ix_mrv_regions_id"), table_name="mrv_regions")
    op.drop_table("mrv_regions")
    op.drop_index(op.f("ix_mrv_projects_id"), table_name="mrv_projects")
    op.drop_index(op.f("ix_mrv_projects_deleted_at"), table_name="mrv_projects")
    op.drop_table("mrv_projects")
    op.drop_index(op.f("ix_mrv_program_practice_change_id"), table_name="mrv_program_practice_change")
    op.drop_index(op.f("ix_mrv_program_practice_change_deleted_at"), table_name="mrv_program_practice_change")
    op.drop_table("mrv_program_practice_change")
    op.drop_index(op.f("ix_mrv_program_fms_options_id"), table_name="mrv_program_fms_options")
    op.drop_index(op.f("ix_mrv_program_fms_options_deleted_at"), table_name="mrv_program_fms_options")
    op.drop_table("mrv_program_fms_options")
    op.drop_index(op.f("ix_mrv_program_custom_reg_inputs_type_"), table_name="mrv_program_custom_reg_inputs")
    op.drop_index(op.f("ix_mrv_program_custom_reg_inputs_program_id"), table_name="mrv_program_custom_reg_inputs")
    op.drop_index(op.f("ix_mrv_program_custom_reg_inputs_id"), table_name="mrv_program_custom_reg_inputs")
    op.drop_index(op.f("ix_mrv_program_custom_reg_inputs_deleted_at"), table_name="mrv_program_custom_reg_inputs")
    op.drop_table("mrv_program_custom_reg_inputs")
    op.drop_index(op.f("ix_mrv_program_asset_selection_id"), table_name="mrv_program_asset_selection")
    op.drop_index(op.f("ix_mrv_program_asset_selection_deleted_at"), table_name="mrv_program_asset_selection")
    op.drop_table("mrv_program_asset_selection")
    op.drop_index(op.f("ix_mrv_phases_id"), table_name="mrv_phases")
    op.drop_index(op.f("ix_mrv_phases_deleted_at"), table_name="mrv_phases")
    op.drop_table("mrv_phases")
    op.drop_index(op.f("ix_mrv_monitor_events_id"), table_name="mrv_monitor_events")
    op.drop_table("mrv_monitor_events")
    op.drop_index(op.f("ix_mrv_enrolments_id"), table_name="mrv_enrolments")
    op.drop_index(op.f("ix_mrv_enrolments_deleted_at"), table_name="mrv_enrolments")
    op.drop_table("mrv_enrolments")
    op.drop_index(op.f("ix_mrv_county_eligibilities_id"), table_name="mrv_county_eligibilities")
    op.drop_index(op.f("ix_mrv_county_eligibilities_deleted_at"), table_name="mrv_county_eligibilities")
    op.drop_index(op.f("ix_mrv_county_eligibilities_county_code"), table_name="mrv_county_eligibilities")
    op.drop_table("mrv_county_eligibilities")
    op.drop_index(op.f("ix_mrv_conflict_results_id"), table_name="mrv_conflict_results")
    op.drop_table("mrv_conflict_results")
    op.drop_index(op.f("ix_mrv_boundary_rule_config_enabled"), table_name="mrv_boundary_rule_config")
    op.drop_index(op.f("ix_mrv_boundary_rule_config_deleted_at"), table_name="mrv_boundary_rule_config")
    op.drop_table("mrv_boundary_rule_config")
    op.drop_index(op.f("ix_mrv_tillage_eligibility_config_id"), table_name="mrv_tillage_eligibility_config")
    op.drop_table("mrv_tillage_eligibility_config")
    op.drop_index(op.f("ix_mrv_soils_override_md5"), table_name="mrv_soils_override")
    op.drop_index(op.f("ix_mrv_soils_override_id"), table_name="mrv_soils_override")
    op.drop_table("mrv_soils_override")
    op.drop_index(
        op.f("ix_mrv_rice_irrigation_eligibility_config_id"), table_name="mrv_rice_irrigation_eligibility_config"
    )
    op.drop_table("mrv_rice_irrigation_eligibility_config")
    op.drop_index(op.f("ix_mrv_reporting_dashboards_id"), table_name="mrv_reporting_dashboards")
    op.drop_index(op.f("ix_mrv_reporting_dashboards_deleted_at"), table_name="mrv_reporting_dashboards")
    op.drop_table("mrv_reporting_dashboards")
    op.drop_index(op.f("ix_mrv_reporting_chart_presets_id"), table_name="mrv_reporting_chart_presets")
    op.drop_index(op.f("ix_mrv_reporting_chart_presets_deleted_at"), table_name="mrv_reporting_chart_presets")
    op.drop_table("mrv_reporting_chart_presets")
    op.drop_index(op.f("ix_mrv_programs_id"), table_name="mrv_programs")
    op.drop_index(op.f("ix_mrv_programs_deleted_at"), table_name="mrv_programs")
    op.drop_table("mrv_programs")
    op.drop_index(op.f("ix_mrv_nm_timing_eligibility_config_id"), table_name="mrv_nm_timing_eligibility_config")
    op.drop_table("mrv_nm_timing_eligibility_config")
    op.drop_index(
        op.f("ix_mrv_nm_split_application_eligibility_config_id"),
        table_name="mrv_nm_split_application_eligibility_config",
    )
    op.drop_table("mrv_nm_split_application_eligibility_config")
    op.drop_index(
        op.f("ix_mrv_nm_rate_reduction_eligibility_config_id"), table_name="mrv_nm_rate_reduction_eligibility_config"
    )
    op.drop_table("mrv_nm_rate_reduction_eligibility_config")
    op.drop_index(
        op.f("ix_mrv_grazing_interventions_eligibility_config_id"),
        table_name="mrv_grazing_interventions_eligibility_config",
    )
    op.drop_table("mrv_grazing_interventions_eligibility_config")
    op.drop_index(op.f("ix_mrv_docusign_webhook_id"), table_name="mrv_docusign_webhook")
    op.drop_table("mrv_docusign_webhook")
    op.drop_index(op.f("ix_mrv_docusign_access_id"), table_name="mrv_docusign_access")
    op.drop_table("mrv_docusign_access")
    op.drop_index(op.f("ix_mrv_dndc_lookup_table_year"), table_name="mrv_dndc_lookup_table")
    op.drop_index(op.f("ix_mrv_dndc_lookup_table_id"), table_name="mrv_dndc_lookup_table")
    op.drop_table("mrv_dndc_lookup_table")
    op.drop_index(op.f("ix_mrv_cover_crop_eligibility_config_id"), table_name="mrv_cover_crop_eligibility_config")
    op.drop_table("mrv_cover_crop_eligibility_config")
    op.drop_index(op.f("ix_mrv_countries_name"), table_name="mrv_countries")
    op.drop_index(op.f("ix_mrv_countries_id"), table_name="mrv_countries")
    op.drop_index(op.f("ix_mrv_countries_deleted_at"), table_name="mrv_countries")
    op.drop_table("mrv_countries")
    op.drop_index(op.f("ix_mrv_audits_id"), table_name="mrv_audits")
    op.drop_table("mrv_audits")
    # ### end Alembic commands ###
