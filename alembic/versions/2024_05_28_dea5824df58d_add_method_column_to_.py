"""Add method column to ProgramModelingConfigurations

Revision ID: dea5824df58d
Revises: d84ff653672d
Create Date: 2024-05-28 15:15:47.545761

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "dea5824df58d"
down_revision = "378d874f1a34"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_program_modeling_configuration",
        sa.Column("method", mysql.ENUM("DNDC", "FDCIC", "LUT"), nullable=False, server_default="DNDC"),
    )


def downgrade():
    op.drop_column("mrv_program_modeling_configuration", "method")
