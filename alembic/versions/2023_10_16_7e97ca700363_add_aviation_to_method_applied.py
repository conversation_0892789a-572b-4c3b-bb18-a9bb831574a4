"""add Aviation to method applied

Revision ID: 7e97ca700363
Revises: d137d7923622
Create Date: 2023-10-16 13:06:56.642155

"""

import sqlalchemy as sa
from sqlalchemy import orm

from alembic import op
from phases.enums import AttributeTypes
from phases.model import AttributeOptionsDefaults

# revision identifiers, used by Alembic.
revision = "7e97ca700363"
down_revision = "d137d7923622"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=sa.Enum("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", name="applicationmethod"),
        type_=sa.Enum("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", "AVIATION", name="applicationmethod"),
        existing_nullable=True,
    )

    try:
        bind = op.get_bind()
        session = orm.Session(bind=bind)

        session.query(AttributeOptionsDefaults).filter(
            AttributeOptionsDefaults.type == AttributeTypes.application_method
        ).update({"options": ["Incorporated", "Injected", "Fertigation", "Broadcasted", "Aviation"]})
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    try:
        bind = op.get_bind()
        session = orm.Session(bind=bind)

        session.query(AttributeOptionsDefaults).filter(
            AttributeOptionsDefaults.type == AttributeTypes.application_method
        ).update({"options": ["Incorporated", "Injected", "Fertigation", "Broadcasted"]})
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e

    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=sa.Enum(
            "BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", "AVIATION", name="applicationmethod"
        ),
        type_=sa.Enum("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", name="applicationmethod"),
        existing_nullable=True,
    )
