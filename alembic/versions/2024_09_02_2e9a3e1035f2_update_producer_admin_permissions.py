"""update-producer-admin-permissions

Revision ID: 2e9a3e1035f2
Revises: 3465163191c4
Create Date: 2024-09-02 20:52:26.592496

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "2e9a3e1035f2"
down_revision = "3465163191c4"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.OVERWRITE_SINGLE_PROJECT_FIELD_VALUES.value,
        Permission.OVERWRITE_MULTIPLE_PROJECTS_FIELD_VALUES.value,
    },
}


def upgrade():
    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
