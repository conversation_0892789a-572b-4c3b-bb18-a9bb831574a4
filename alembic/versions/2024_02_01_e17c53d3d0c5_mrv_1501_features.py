"""MRV-1501-features

Revision ID: e17c53d3d0c5
Revises: d1acd2970e09
Create Date: 2024-02-01 09:04:17.440282

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e17c53d3d0c5"
down_revision = "d1acd2970e09"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_features",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("feature_name", sa.String(length=255), nullable=False, comment="Feature Name"),
        sa.Column("description", sa.Text(), nullable=True, comment="Feature Description"),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.Column(
            "strategy", sa.String(length=255), nullable=False, comment="Release Strategy (Boolean, Embargo date, etc.)"
        ),
        sa.Column("strategy_value", sa.String(length=255), nullable=False, comment="Release Strategy Value"),
        sa.PrimaryKeyConstraint("id"),
        comment="A Set of MRV Features and experiments",
    )
    op.create_index(op.f("ix_mrv_features_deleted_at"), "mrv_features", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_mrv_features_id"), "mrv_features", ["id"], unique=False)
    op.create_table(
        "mrv_features_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("feature_id", sa.Integer(), nullable=False),
        sa.Column("action", sa.String(length=255), nullable=False, comment="Action"),
        sa.ForeignKeyConstraint(
            ["feature_id"],
            ["mrv_features.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="History log of MRV Features and experiments",
    )
    op.create_index(op.f("ix_mrv_features_history_id"), "mrv_features_history", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_features_history_id"), table_name="mrv_features_history")
    op.drop_table("mrv_features_history")
    op.drop_index(op.f("ix_mrv_features_id"), table_name="mrv_features")
    op.drop_index(op.f("ix_mrv_features_deleted_at"), table_name="mrv_features")
    op.drop_table("mrv_features")
    # ### end Alembic commands ###
