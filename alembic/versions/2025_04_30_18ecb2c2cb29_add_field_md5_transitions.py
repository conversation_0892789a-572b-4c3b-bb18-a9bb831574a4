"""add field md5 transitions

Revision ID: 18ecb2c2cb29
Revises: bb6011400651
Create Date: 2025-04-30 17:49:07.600492

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "18ecb2c2cb29"
down_revision = "bb6011400651"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_field_md5_transitions",
        sa.<PERSON>umn("field_id", sa.Integer(), nullable=False),
        sa.Column("previous_md5", sa.String(length=32), nullable=False),
        sa.Column("new_md5", sa.String(length=32), nullable=False),
        sa.PrimaryKeyConstraint("field_id"),
    )
    op.create_index(
        op.f("ix_mrv_field_md5_transitions_field_id"), "mrv_field_md5_transitions", ["field_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_field_md5_transitions_field_id"), table_name="mrv_field_md5_transitions")
    op.drop_table("mrv_field_md5_transitions")
