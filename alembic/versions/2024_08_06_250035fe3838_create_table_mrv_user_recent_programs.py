"""create_table-mrv_user_recent_programs

Revision ID: 250035fe3838
Revises: f84bd9680763
Create Date: 2024-08-06 11:15:35.901414

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "250035fe3838"
down_revision = "f84bd9680763"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_user_recent_programs",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.<PERSON>KeyConstraint("id"),
        sa.UniqueConstraint("user_id", "project_id", name="unique_user_id_project_id"),
    )
    op.create_index(op.f("ix_mrv_user_recent_programs_id"), "mrv_user_recent_programs", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_user_recent_programs_program_id"), "mrv_user_recent_programs", ["program_id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_user_recent_programs_project_id"), "mrv_user_recent_programs", ["project_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_user_recent_programs_user_id"), "mrv_user_recent_programs", ["user_id"], unique=False)
    op.create_index(
        "unique_user_id_program_id",
        "mrv_user_recent_programs",
        ["user_id", "program_id", sa.text("(IF(project_id IS NULL, 1, NULL))")],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("unique_user_id_program_id", table_name="mrv_user_recent_programs")
    op.drop_index(op.f("ix_mrv_user_recent_programs_user_id"), table_name="mrv_user_recent_programs")
    op.drop_index(op.f("ix_mrv_user_recent_programs_project_id"), table_name="mrv_user_recent_programs")
    op.drop_index(op.f("ix_mrv_user_recent_programs_program_id"), table_name="mrv_user_recent_programs")
    op.drop_index(op.f("ix_mrv_user_recent_programs_id"), table_name="mrv_user_recent_programs")
    op.drop_table("mrv_user_recent_programs")
    # ### end Alembic commands ###
