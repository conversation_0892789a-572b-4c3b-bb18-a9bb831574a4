"""update validation endpoint permissions

Revision ID: a4174e4cd80c
Revises: bab31a555b71
Create Date: 2024-04-26 16:32:09.210543

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a4174e4cd80c"
down_revision = "bab31a555b71"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.BATCH_ATTRIBUTE_VALIDATION.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
