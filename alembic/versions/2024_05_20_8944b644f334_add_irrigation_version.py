"""add irrigation version

Revision ID: 8944b644f334
Revises: 3f2e743d7837
Create Date: 2024-05-20 15:17:08.827242

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8944b644f334"
down_revision = "3f2e743d7837"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_cover_crop_eligibility_config", sa.Column("default_crop_match", sa.String(length=255), nullable=True)
    )
    op.add_column(
        "mrv_grazing_interventions_eligibility_config",
        sa.Column("default_crop_match", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_rate_reduction_eligibility_config",
        sa.Column("default_crop_match", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_split_application_eligibility_config",
        sa.Column("default_crop_match", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "mrv_nm_timing_eligibility_config", sa.Column("default_crop_match", sa.String(length=255), nullable=True)
    )
    op.add_column(
        "mrv_rice_irrigation_eligibility_config",
        sa.Column(
            "version",
            sa.Enum("IRRIGATION_GENERATOR_V1", "IRRIGATION_GENERATOR_V2", name="irrigationgeneratortype"),
            nullable=False,
        ),
    )
    op.add_column("mrv_rice_irrigation_eligibility_config", sa.Column("description", sa.Text(), nullable=True))
    op.add_column(
        "mrv_rice_irrigation_eligibility_config", sa.Column("default_crop_match", sa.String(length=255), nullable=True)
    )
    op.add_column(
        "mrv_tillage_eligibility_config", sa.Column("default_crop_match", sa.String(length=255), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_tillage_eligibility_config", "default_crop_match")
    op.drop_column("mrv_rice_irrigation_eligibility_config", "default_crop_match")
    op.drop_column("mrv_rice_irrigation_eligibility_config", "description")
    op.drop_column("mrv_rice_irrigation_eligibility_config", "version")
    op.drop_column("mrv_nm_timing_eligibility_config", "default_crop_match")
    op.drop_column("mrv_nm_split_application_eligibility_config", "default_crop_match")
    op.drop_column("mrv_nm_rate_reduction_eligibility_config", "default_crop_match")
    op.drop_column("mrv_grazing_interventions_eligibility_config", "default_crop_match")
    op.drop_column("mrv_cover_crop_eligibility_config", "default_crop_match")
    # ### end Alembic commands ###
