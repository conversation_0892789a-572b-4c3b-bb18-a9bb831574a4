"""Add format, prefix, postfix to Charts

Revision ID: f9ddec0e02d2
Revises: ebc7372d1cd5
Create Date: 2023-11-21 00:42:36.468673

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f9ddec0e02d2"
down_revision = "ebc7372d1cd5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_reporting_chart_presets", sa.Column("format", sa.Enum("NUMBER", "DATE", name="format"), nullable=True)
    )
    op.add_column("mrv_reporting_chart_presets", sa.Column("prefix", sa.JSON(), nullable=True))
    op.add_column("mrv_reporting_chart_presets", sa.Column("postfix", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_chart_presets", "postfix")
    op.drop_column("mrv_reporting_chart_presets", "prefix")
    op.drop_column("mrv_reporting_chart_presets", "format")
    # ### end Alembic commands ###
