"""Add fields for pdr

Revision ID: cb556896b000
Revises: 71bbf94cdfd5
Create Date: 2023-09-29 14:56:49.806777

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "cb556896b000"
down_revision = "71bbf94cdfd5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_dashboards", sa.Column("configurable_attributes", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards", "configurable_attributes")
    # ### end Alembic commands ###
