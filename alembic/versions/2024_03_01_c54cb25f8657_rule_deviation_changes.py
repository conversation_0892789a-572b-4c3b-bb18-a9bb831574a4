"""Rule deviation changes

Revision ID: c54cb25f8657
Revises: 9006ee2648e6
Create Date: 2024-03-01 17:24:25.104033

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "c54cb25f8657"
down_revision = "76fb45ccb62a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_boundary_rule_deviation",
        sa.Column(
            "md5", sa.String(length=32), nullable=True, comment="id of geometry that was checked for boundary errors"
        ),
    )
    op.add_column(
        "mrv_boundary_rule_deviation",
        sa.Column(
            "field_id",
            sa.Integer(),
            nullable=True,
            comment="MRV field id of field that was checked. Usually NULL, except when we call /programs/{program_id}/check_field_boundaries for existing fields",
        ),
    )
    op.alter_column(
        "mrv_boundary_rule_deviation", "kml_group_id", existing_type=mysql.INTEGER(unsigned=True), nullable=True
    )
    op.create_index(op.f("ix_mrv_boundary_rule_deviation_md5"), "mrv_boundary_rule_deviation", ["md5"], unique=False)
    op.create_foreign_key(None, "mrv_boundary_rule_deviation", "mrv_fields", ["field_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_boundary_rule_deviation", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_boundary_rule_deviation_md5"), table_name="mrv_boundary_rule_deviation")
    op.alter_column(
        "mrv_boundary_rule_deviation", "kml_group_id", existing_type=mysql.INTEGER(unsigned=True), nullable=False
    )
    op.drop_column("mrv_boundary_rule_deviation", "field_id")
    op.drop_column("mrv_boundary_rule_deviation", "md5")
    # ### end Alembic commands ###
