"""Rename mrv_reporting_dashboards.configurable_attributes to configured_attributes

Revision ID: a3c64dafa344
Revises: 4a521748ee20
Create Date: 2024-04-10 18:02:27.096934

"""

import sqlalchemy as sa
from sqlalchemy import select
from sqlalchemy.dialects import mysql
from sqlalchemy.orm.session import Session

from alembic import op
from programs.reporting_dashboards.model import ReportingDashboardsToPrograms

# revision identifiers, used by Alembic.
revision = "a3c64dafa344"
down_revision = "4a521748ee20"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_reporting_dashboards", sa.Column("configured_attributes", sa.JSON(none_as_null=True), nullable=True)
    )
    op.drop_column("mrv_reporting_dashboards", "configurable_attributes")

    try:
        session = Session(bind=op.get_bind())
        query = select(ReportingDashboardsToPrograms).where(
            ReportingDashboardsToPrograms.configured_attributes.isnot(None)
        )
        updated = []
        dash_to_progs = session.execute(query).scalars().all()
        new_dicts = {}
        for dash_to_prog in dash_to_progs:
            new_dicts[dash_to_prog.id] = {}
            for key, value in dash_to_prog.configured_attributes.items():
                new_dicts[dash_to_prog.id][key] = {}
                for key_2, value_2 in value.items():
                    if key_2 == "chart_disabled":
                        new_dicts[dash_to_prog.id][key]["disabled"] = value_2
                    elif key_2 == "presets_disabled":
                        new_dicts[dash_to_prog.id][key]["values_disabled"] = value_2

        for dash_to_prog in dash_to_progs:
            dash_to_prog.configured_attributes = new_dicts[dash_to_prog.id]
            updated.append(dash_to_prog)

        session.bulk_save_objects(updated)
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    op.add_column("mrv_reporting_dashboards", sa.Column("configurable_attributes", mysql.JSON(), nullable=True))
    op.drop_column("mrv_reporting_dashboards", "configured_attributes")

    try:
        session = Session(bind=op.get_bind())
        query = select(ReportingDashboardsToPrograms).where(
            ReportingDashboardsToPrograms.configured_attributes.isnot(None)
        )
        updated = []
        dash_to_progs = session.execute(query).scalars().all()
        new_dicts = {}
        for dash_to_prog in dash_to_progs:
            new_dicts[dash_to_prog.id] = {}
            for key, value in dash_to_prog.configured_attributes.items():
                new_dicts[dash_to_prog.id][key] = {}
                for key_2, value_2 in value.items():
                    if key_2 == "disabled":
                        new_dicts[dash_to_prog.id][key]["chart_disabled"] = value_2
                    elif key_2 == "values_disabled":
                        new_dicts[dash_to_prog.id][key]["presets_disabled"] = value_2

        for dash_to_prog in dash_to_progs:
            dash_to_prog.configured_attributes = new_dicts[dash_to_prog.id]
            updated.append(dash_to_prog)

        session.bulk_save_objects(updated)
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e
