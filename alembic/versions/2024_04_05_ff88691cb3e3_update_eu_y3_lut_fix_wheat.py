"""update-eu-y3-lut-fix-wheat

Revision ID: ff88691cb3e3
Revises: 3a72be0eaf90
Create Date: 2024-04-05 12:58:07.269422

"""

from sqlalchemy.orm import Session

from alembic import op
from projects.eligibility.helper import initialise_cargill_eu_lut

# revision identifiers, used by Alembic.
revision = "ff88691cb3e3"
down_revision = "3a72be0eaf90"
branch_labels = None
depends_on = None


def upgrade():
    # Initialising LUT table with 2024 data
    session = Session(bind=op.get_bind())
    initialise_cargill_eu_lut(session=session, year=2024)


def downgrade():
    # no need to going back to previous LUT version
    pass
