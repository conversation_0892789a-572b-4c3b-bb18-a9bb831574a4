"""Added inventory API to SS APIs

Revision ID: 6ed549e6a190
Revises: f580e14397e7
Create Date: 2024-12-12 14:33:52.308152

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "6ed549e6a190"
down_revision = "b694129f2d17"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "ss_api",
        existing_type=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        type_=sa.Enum("measure_api", "explore_api", "biofuels_api", "inventory_api", name="scenariosserviceapi"),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_dndc_tasks",
        "scenarios_service_api",
        existing_type=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        type_=sa.Enum("measure_api", "explore_api", "biofuels_api", "inventory_api", name="scenariosserviceapi"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_tasks",
        "scenarios_service_api",
        existing_type=sa.Enum(
            "measure_api", "explore_api", "biofuels_api", "inventory_api", name="scenariosserviceapi"
        ),
        type_=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        existing_nullable=False,
    )
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "ss_api",
        existing_type=sa.Enum(
            "measure_api", "explore_api", "biofuels_api", "inventory_api", name="scenariosserviceapi"
        ),
        type_=mysql.ENUM("measure_api", "explore_api", "biofuels_api"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
