"""Add Aviation as method to application_method for NM

Revision ID: bf06621645cc
Revises: 0929ee59b871
Create Date: 2023-12-07 16:49:24.796068

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "bf06621645cc"
down_revision = "0929ee59b871"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=mysql.ENUM("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", "AVIATION"),
        type_=sa.Enum(
            "BROADCASTED", "FERTIGATION", "INJECTED", "AVIATION", "INCORPORATED", "SUBSURFACE", name="applicationmethod"
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_nutrient_template_applications",
        "application_method",
        existing_type=sa.Enum(
            "BROADCASTED", "FERTIGATION", "INJECTED", "AVIATION", "INCORPORATED", "SUBSURFACE", name="applicationmethod"
        ),
        type_=mysql.ENUM("BROADCASTED", "FERTIGATION", "INJECTED", "INCORPORATED", "AVIATION"),
        existing_nullable=True,
    )
