"""Add mrv_values_unique_idx

Revision ID: cd129fe15f23
Revises: fa5cbcfb74c9
Create Date: 2023-11-29 02:36:45.627545

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "cd129fe15f23"
down_revision = "fa5cbcfb74c9"
branch_labels = None
depends_on = None


def upgrade():
    try:
        op.create_unique_constraint(
            "mrv_values_unique_idx", "mrv_values", ["field_id", "attribute_id", "row_id", "progress"]
        )
    except Exception:
        msg = "mrv_values_ibfk_1 already exists"  # noqa


def downgrade():
    pass
