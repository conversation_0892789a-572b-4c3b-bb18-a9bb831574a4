"""Add pred soc bd constraint

Revision ID: e997b752eb38
Revises: 40ae21285f3b
Create Date: 2024-11-04 13:50:49.262774

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e997b752eb38"
down_revision = "40ae21285f3b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_check_constraint(
        "ck_mrv_soils_override_pred_soc_ge_0", "mrv_soils_override", sa.Column("pred_soc") >= 0.0
    )
    op.create_check_constraint(
        "ck_mrv_soils_override_pred_soc_le_100", "mrv_soils_override", sa.Column("pred_soc") <= 100.0
    )
    op.create_check_constraint(
        "ck_mrv_soils_override_pred_bd_ge_0_1", "mrv_soils_override", sa.Column("pred_bd") >= 0.1
    )
    op.create_check_constraint(
        "ck_mrv_soils_override_pred_bd_le_2_65", "mrv_soils_override", sa.Column("pred_bd") <= 2.65
    )


def downgrade():
    op.drop_constraint("ck_mrv_soils_override_pred_soc_ge_0", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_pred_soc_le_100", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_pred_bd_ge_0_1", "mrv_soils_override", type_="check")
    op.drop_constraint("ck_mrv_soils_override_pred_bd_le_2_65", "mrv_soils_override", type_="check")
