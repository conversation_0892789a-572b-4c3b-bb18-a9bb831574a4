"""Add stage_type to mrv_raw_field_events

Revision ID: 23df836a0b31
Revises: b771459cbf48
Create Date: 2024-08-19 09:12:51.467084

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "23df836a0b31"
down_revision = "66c1b91231a6"
branch_labels = None
depends_on = None


class StageTypes(enum.StrEnum):
    FIELD_BOUNDARIES = "FIELD_BOUNDARIES"
    ASSIGN_PRACTICES = "ASSIGN_PRACTICES"
    CONFIRM_HISTORY = "CONFIRM_HISTORY"
    SUMMER_CROPS = "SUMMER_CROPS"
    WINTER_CROPS = "WINTER_CROPS"
    TILLAGE = "TILLAGE"
    NUTRIENT_MGMT = "NUTRIENT_MGMT"
    VIEW_OUTCOMES = "VIEW_OUTCOMES"
    SURVEY = "SURVEY"
    CONTRACT = "CONTRACT"
    ELIGIBILITY = "ELIGIBILITY"
    FIELD_INFORMATION = "FIELD_INFORMATION"
    HISTORICAL_CROP_ROTATION = "HISTORICAL_CROP_ROTATION"
    HISTORICAL_TILLAGE = "HISTORICAL_TILLAGE"
    INTENDED_COMMODITY_CROPS = "INTENDED_COMMODITY_CROPS"
    IRRIGATION = "IRRIGATION"
    RICE_CROP_HISTORY = "RICE_CROP_HISTORY"
    MOB_HISTORY = "MOB_HISTORY"
    MOB_MOVEMENT = "MOB_MOVEMENT"
    FARM_LEVEL_MANAGEMENT = "FARM_LEVEL_MANAGEMENT"
    NUTRIENT_MGMT_INTENDED = "NUTRIENT_MGMT_INTENDED"
    CROP_EVENTS = "CROP_EVENTS"
    IRRIGATION_EVENTS = "IRRIGATION_EVENTS"
    NUTRIENT_EVENTS = "NUTRIENT_EVENTS"
    TILLAGE_EVENTS = "TILLAGE_EVENTS"
    CHEMICAL_MANAGEMENT = "CHEMICAL_MANAGEMENT"


def upgrade():
    op.add_column("mrv_raw_field_events", sa.Column("stage_type", sa.Enum(StageTypes), nullable=True))
    op.drop_constraint("mrv_raw_field_events_ibfk_1", "mrv_raw_field_events", type_="foreignkey")
    op.drop_index("_field_id_row_id_event_type_uc", table_name="mrv_raw_field_events")
    op.create_unique_constraint(
        "_field_id_row_id_phase_type_stage_type_uc",
        "mrv_raw_field_events",
        ["field_id", "row_id", "phase_type", "stage_type"],
    )
    op.create_foreign_key("mrv_raw_field_events_ibfk_1", "mrv_raw_field_events", "mrv_fields", ["field_id"], ["id"])


def downgrade():
    op.drop_constraint("mrv_raw_field_events_ibfk_1", "mrv_raw_field_events", type_="foreignkey")
    op.drop_constraint("_field_id_row_id_phase_type_stage_type_uc", "mrv_raw_field_events", type_="unique")
    op.create_index(
        "_field_id_row_id_event_type_uc", "mrv_raw_field_events", ["field_id", "row_id", "event_type"], unique=True
    )
    op.drop_column("mrv_raw_field_events", "stage_type")
    op.create_foreign_key("mrv_raw_field_events_ibfk_1", "mrv_raw_field_events", "mrv_fields", ["field_id"], ["id"])
