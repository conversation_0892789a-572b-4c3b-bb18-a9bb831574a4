"""add more chartType enum

Revision ID: 3b9d0407944d
Revises: 11ed186dd165
Create Date: 2023-08-23 14:46:05.517675

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "3b9d0407944d"
down_revision = "11ed186dd165"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
        ),
        type_=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            name="charttype",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            name="charttype",
        ),
        type_=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
