"""fix typo in unit rate option

Revision ID: 21b6b80975cf
Revises: c5471530a000
Create Date: 2023-10-16 19:08:02.880005

"""

from sqlalchemy import orm

from alembic import op
from phases.enums import AttributeTypes
from phases.model import AttributeOptionsDefaults

# revision identifiers, used by Alembic.
revision = "21b6b80975cf"
down_revision = "c5471530a000"
branch_labels = None
depends_on = None


def upgrade():
    try:
        bind = op.get_bind()
        session = orm.Session(bind=bind)

        session.query(AttributeOptionsDefaults).filter(
            AttributeOptionsDefaults.type == AttributeTypes.irrigation_rate_unit
        ).update({"options": ["mm", "in", "ML/ha", "mm/ha", "oz/ac", "in/ac"]})
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    bind = op.get_bind()
    session = orm.Session(bind=bind)

    session.query(AttributeOptionsDefaults).filter(
        AttributeOptionsDefaults.type == AttributeTypes.irrigation_rate_unit
    ).update({"options": ["mm", "in", "mL/ha", "mm/ha", "oz/ac", "in/ac"]})
    session.commit()
