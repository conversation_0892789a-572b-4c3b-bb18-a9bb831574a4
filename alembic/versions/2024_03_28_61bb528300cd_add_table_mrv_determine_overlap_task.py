"""add table mrv_determine_overlap_task

Revision ID: 61bb528300cd
Revises: 4825205004b6
Create Date: 2024-03-28 13:32:34.333221

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "61bb528300cd"
down_revision = "87fcec7e9194"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_determine_overlap_task",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "finished_at",
            sa.TIMESTAMP(),
            nullable=True,
            comment="Set to time when the task completed. Should be set whether an exception was raised or not",
        ),
        sa.Column("error", sa.Text(), nullable=True, comment="Error string, if one occurred. Typically a stack trace"),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        comment="Created in response to an invocation of an endpoint to determine overlaps between programs",
    )
    op.create_index(op.f("ix_mrv_determine_overlap_task_id"), "mrv_determine_overlap_task", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_determine_overlap_task_id"), table_name="mrv_determine_overlap_task")
    op.drop_table("mrv_determine_overlap_task")
    # ### end Alembic commands ###
