"""Fix misaligned reporting period

Revision ID: fdd0aeeeb57f
Revises: 430cd7577160
Create Date: 2025-01-10 10:30:44.496337

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "fdd0aeeeb57f"
down_revision = "430cd7577160"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_programs mp
        SET
            mp.reporting_period_start_date = TIMESTAMP(CONCAT(mp.crediting_year + 1, '-01-01 00:00:00')),
            mp.reporting_period_end_date = TIMESTAMP(CONCAT(mp.crediting_year + 1, '-12-31 23:59:59'))
        WHERE mp.id in (1133, 1372, 1627);"""
    )


def downgrade():
    pass
