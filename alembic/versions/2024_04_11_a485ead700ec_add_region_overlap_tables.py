"""Add region overlap tables

Revision ID: a485ead700ec
Revises: ce0eb7b6ea5c
Create Date: 2024-04-11 15:19:00.577614

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a485ead700ec"
down_revision = "ce0eb7b6ea5c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_region_overlap_request",
        sa.<PERSON>umn("program_id", sa.Integer(), nullable=False),
        sa.Column("previous_program_id", sa.Integer(), nullable=False),
        sa.Column("core_region_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn(
            "job_id",
            sa.String(length=36),
            nullable=True,
            comment="UUID sent by boundaries service to identify async API call",
        ),
        sa.Column(
            "status",
            sa.Enum("accepted", "running", "successful", "failed", "dismissed", name="jobstatus"),
            nullable=True,
            comment="Job status set by boundaries service",
        ),
        sa.Column(
            "result",
            sa.JSON(),
            nullable=True,
            comment="Contains either the result of the API call, or error, from boundaries service",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["previous_program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_determine_overlap_task.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("job_id"),
        comment="Tracks async requests to boundaries service to determine the overlap between fields in two programs belonging to a particular region",
    )
    op.create_table(
        "mrv_region_overlap_result",
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("previous_program_id", sa.Integer(), nullable=False),
        sa.Column("core_region_id", sa.Integer(), nullable=False),
        sa.Column(
            "program_region_area_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column(
            "previous_program_region_area_ha",
            sa.DECIMAL(precision=20, scale=8),
            nullable=False,
            comment="Area in hectares",
        ),
        sa.Column(
            "area_intersection_ha", sa.DECIMAL(precision=20, scale=8), nullable=False, comment="Area in hectares"
        ),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["previous_program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["mrv_determine_overlap_task.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="When we call boundaries service to determine the overlap between fields belonging to a particular region common to two programs, store the result here",
    )
    op.create_index(
        op.f("ix_mrv_region_overlap_result_created_at"), "mrv_region_overlap_result", ["created_at"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_region_overlap_result_created_at"), table_name="mrv_region_overlap_result")
    op.drop_table("mrv_region_overlap_result")
    op.drop_table("mrv_region_overlap_request")
    # ### end Alembic commands ###
