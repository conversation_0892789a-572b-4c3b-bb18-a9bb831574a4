"""adjust inventory results schema

Revision ID: cab3ef58ff18
Revises: 131614a35bd6
Create Date: 2025-01-14 09:53:40.288294

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "cab3ef58ff18"
down_revision = "131614a35bd6"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_field_level_inventory_outcomes", sa.Column("net_emissions_percentage", sa.Float(), nullable=True)
    )

    op.drop_column("mrv_field_level_inventory_outcomes", "soc")
    op.drop_column("mrv_field_level_inventory_outcomes", "indirect_n2o_emissions")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_emission_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_total_n2o_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "soil_ch4_emissions")
    op.drop_column("mrv_field_level_inventory_outcomes", "project_level_net_emissions")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_ch4_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_indirect_n2o_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "total_n2o_emissions")
    op.drop_column("mrv_field_level_inventory_outcomes", "direct_n2o_emissions")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_direct_n2o_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_soc_project_percentage")
    op.drop_column("mrv_field_level_inventory_outcomes", "field_ghg_emissions")


def downgrade():
    op.add_column(
        "mrv_field_level_inventory_outcomes", sa.Column("field_soc_project_percentage", mysql.FLOAT(), nullable=True)
    )
    op.add_column(
        "mrv_field_level_inventory_outcomes",
        sa.Column("field_direct_n2o_project_percentage", mysql.FLOAT(), nullable=True),
    )
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("direct_n2o_emissions", mysql.FLOAT(), nullable=True))
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("total_n2o_emissions", mysql.FLOAT(), nullable=True))
    op.add_column(
        "mrv_field_level_inventory_outcomes",
        sa.Column("field_indirect_n2o_project_percentage", mysql.FLOAT(), nullable=True),
    )
    op.add_column(
        "mrv_field_level_inventory_outcomes", sa.Column("field_ch4_project_percentage", mysql.FLOAT(), nullable=True)
    )
    op.add_column(
        "mrv_field_level_inventory_outcomes", sa.Column("project_level_net_emissions", mysql.FLOAT(), nullable=True)
    )
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("soil_ch4_emissions", mysql.FLOAT(), nullable=True))
    op.add_column(
        "mrv_field_level_inventory_outcomes",
        sa.Column("field_total_n2o_project_percentage", mysql.FLOAT(), nullable=True),
    )
    op.add_column(
        "mrv_field_level_inventory_outcomes",
        sa.Column("field_emission_project_percentage", mysql.FLOAT(), nullable=True),
    )
    op.add_column(
        "mrv_field_level_inventory_outcomes", sa.Column("indirect_n2o_emissions", mysql.FLOAT(), nullable=True)
    )
    op.add_column("mrv_field_level_inventory_outcomes", sa.Column("soc", mysql.FLOAT(), nullable=True))

    op.drop_column("mrv_field_level_inventory_outcomes", "net_emissions_percentage")
