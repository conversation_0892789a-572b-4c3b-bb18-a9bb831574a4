"""Add visibility column to mrv_reporting_dashboards_to_programs

Revision ID: 08901c25aa2b
Revises: a485ead700ec
Create Date: 2024-04-11 15:45:15.230746

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "08901c25aa2b"
down_revision = "a485ead700ec"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_reporting_dashboards_to_programs",
        sa.Column("visibility", sa.Enum("SUPER_ADMIN", name="visibility"), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards_to_programs", "visibility")
    # ### end Alembic commands ###
