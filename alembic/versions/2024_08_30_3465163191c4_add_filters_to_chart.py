"""add filters to chart

Revision ID: 3465163191c4
Revises: 23df836a0b31
Create Date: 2024-08-30 11:04:15.962455

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3465163191c4"
down_revision = "d370bdbec7d7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_chart_presets", sa.Column("filters", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_chart_presets", "filters")
    # ### end Alembic commands ###
