"""Add program_id to mrv_dndc_tasks and populate it with information from mrv_projects so it can be non-nullable.

Revision ID: a02522ad54dd
Revises: e309a4d98b78
Create Date: 2024-02-09 10:21:09.427797

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a02522ad54dd"
down_revision = "e309a4d98b78"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_dndc_tasks", sa.Column("program_id", sa.Integer, nullable=True))
    op.execute(
        "UPDATE mrv_dndc_tasks JOIN mrv_projects ON mrv_projects.id=mrv_dndc_tasks.project_id "
        "SET mrv_dndc_tasks.program_id = mrv_projects.program_id"
    )
    op.alter_column("mrv_dndc_tasks", "program_id", nullable=False, existing_nullable=True, existing_type=sa.Integer)
    op.alter_column("mrv_dndc_tasks", "project_id", nullable=True, existing_nullable=False, existing_type=sa.Integer)
    op.create_foreign_key(
        "mrv_dndc_tasks_ibfk_2",
        "mrv_dndc_tasks",
        "mrv_programs",
        ["program_id"],
        ["id"],
    )


def downgrade():
    op.drop_column("mrv_dndc_tasks", "program_id")
