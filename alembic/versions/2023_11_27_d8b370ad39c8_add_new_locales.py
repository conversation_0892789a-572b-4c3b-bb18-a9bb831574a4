"""add new locales

Revision ID: d8b370ad39c8
Revises: e4029f0b503f
Create Date: 2023-11-27 17:36:41.524904

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "d8b370ad39c8"
down_revision = "e4029f0b503f"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=mysql.ENUM("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE"),
        type_=sa.Enum(
            "en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HUNG", name="locale"
        ),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=sa.Enum(
            "en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HUNG", name="locale"
        ),
        type_=mysql.ENUM("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE"),
        existing_nullable=False,
    )
