"""add deleted at to role user unique key

Revision ID: 7cd69d75c03b
Revises: 0347520754cc
Create Date: 2023-12-04 14:04:26.555162

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "7cd69d75c03b"
down_revision = "0347520754cc"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("_roles_user_fs_user_id_program_id", table_name="mrv_roles_users")
    op.create_unique_constraint(
        "_roles_user_fs_user_id_program_id_deleted_at", "mrv_roles_users", ["fs_user_id", "program_id", "deleted_at"]
    )


def downgrade():
    op.drop_constraint("_roles_user_fs_user_id_program_id_deleted_at", "mrv_roles_users", type_="unique")
    op.create_index("_roles_user_fs_user_id_program_id", "mrv_roles_users", ["fs_user_id", "program_id"], unique=False)
