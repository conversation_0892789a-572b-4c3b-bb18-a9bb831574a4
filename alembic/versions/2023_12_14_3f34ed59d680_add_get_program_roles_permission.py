"""add get program roles permission

Revision ID: 3f34ed59d680
Revises: 49aaff426a82
Create Date: 2023-12-14 22:40:14.115513

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "3f34ed59d680"
down_revision = "49aaff426a82"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROGRAM_ROLES.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_PROGRAM_ROLES.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_PROGRAM_ROLES.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_PROGRAM_ROLES.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_PROGRAM_ROLES.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
