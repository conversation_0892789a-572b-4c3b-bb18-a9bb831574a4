"""Add new table for scenario service consumers

Revision ID: 6e68b1684f41
Revises: e2a9b471935e
Create Date: 2024-03-15 11:34:30.477266

"""

import sqlalchemy as sa
from sqlalchemy import orm

from alembic import op
from programs.model import Programs

# revision identifiers, used by Alembic.
revision = "6e68b1684f41"
down_revision = "7fbbbd1a1657"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    table = op.create_table(
        "mrv_program_modeling_configuration",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("consumer_id", sa.String(length=36), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_program_modeling_configuration_consumer_id"),
        "mrv_program_modeling_configuration",
        ["consumer_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_program_modeling_configuration_id"), "mrv_program_modeling_configuration", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_program_modeling_configuration_program_id"),
        "mrv_program_modeling_configuration",
        ["program_id"],
        unique=False,
    )

    # Insert existing mappings only if database is not empty
    try:
        session = orm.Session(bind=op.get_bind())
        with session.begin():
            result = session.execute(sa.select(Programs.id).where(Programs.id == 116))
            if result.scalar():
                op.bulk_insert(
                    table,
                    [
                        {"program_id": 116, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 117, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 254, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 255, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 256, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 257, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 258, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 259, "consumer_id": "mrv-cargill-regenconnect-eu"},
                        {"program_id": 21, "consumer_id": "mrv-cargill-regenconnect-us"},
                        {"program_id": 68, "consumer_id": "mrv-cargill-regenconnect-us"},
                        {"program_id": 155, "consumer_id": "mrv-cargill-regenconnect-us"},
                        {"program_id": 285, "consumer_id": "regrow_mrv_test"},
                        {"program_id": 154, "consumer_id": "mrv-kelloggs-ingrained"},
                        {"program_id": 1100, "consumer_id": "mrv-pepsico-ca"},
                        {"program_id": 583, "consumer_id": "mrv-cargill-regenconnect-ca"},
                    ],
                )
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "mrv_program_modeling_configuration_ibfk_1", "mrv_program_modeling_configuration", type_="foreignkey"
    )
    op.drop_index(
        op.f("ix_mrv_program_modeling_configuration_program_id"), table_name="mrv_program_modeling_configuration"
    )
    op.drop_index(op.f("ix_mrv_program_modeling_configuration_id"), table_name="mrv_program_modeling_configuration")
    op.drop_index(
        op.f("ix_mrv_program_modeling_configuration_consumer_id"), table_name="mrv_program_modeling_configuration"
    )
    op.drop_table("mrv_program_modeling_configuration")
    # ### end Alembic commands ###
