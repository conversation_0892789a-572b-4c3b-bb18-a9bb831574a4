"""update conflicts model

Revision ID: a34ad61b53c8
Revises: 7352945240a9
Create Date: 2023-08-22 15:29:32.686893

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a34ad61b53c8"
down_revision = "7352945240a9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_project_conflict_metadata",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("comments", sa.Text(), nullable=True),
        sa.Column(
            "status", sa.Enum("NEEDS_REVIEW", "IN_PROGRESS", "DONE", name="projectconflictstatus"), nullable=False
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_project_conflict_metadata_id"), "mrv_project_conflict_metadata", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_project_conflict_metadata_project_id"),
        "mrv_project_conflict_metadata",
        ["project_id"],
        unique=False,
    )
    op.drop_column("mrv_conflict_resolution_files", "usage")
    op.add_column(
        "mrv_conflicts",
        sa.Column(
            "practice_type",
            sa.Enum("TILLAGE", "COVER_CROP", "IRRIGATION", "COMMODITY_CROP", name="practicetypes"),
            nullable=False,
        ),
    )
    op.add_column("mrv_conflicts", sa.Column("measurement_value", sa.Integer(), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("measurement_data", sa.JSON(), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("monitor_value", sa.String(length=255), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("monitor_data", sa.JSON(), nullable=True))
    op.drop_index("_conflict_type_project_id_field_id_uc", table_name="mrv_conflicts")
    op.create_unique_constraint(
        "_practice_type_project_id_field_id_uc", "mrv_conflicts", ["practice_type", "project_id", "field_id"]
    )
    op.drop_constraint("mrv_conflicts_ibfk_2", "mrv_conflicts", type_="foreignkey")
    op.drop_constraint("mrv_conflicts_ibfk_5", "mrv_conflicts", type_="foreignkey")
    op.drop_constraint("mrv_conflicts_ibfk_1", "mrv_conflicts", type_="foreignkey")
    op.drop_constraint("mrv_conflicts_ibfk_4", "mrv_conflicts", type_="foreignkey")
    op.create_foreign_key(None, "mrv_conflicts", "mrv_values", ["measurement_value"], ["id"])
    op.drop_column("mrv_conflicts", "optis_value_confidence")
    op.drop_column("mrv_conflicts", "producer_name")
    op.drop_column("mrv_conflicts", "monitoring_value")
    op.drop_column("mrv_conflicts", "monitoring_attribute_id")
    op.drop_column("mrv_conflicts", "baseline_value")
    op.drop_column("mrv_conflicts", "conflict_type")
    op.drop_column("mrv_conflicts", "optis_value")
    op.drop_column("mrv_conflicts", "optis_tillage_period")
    op.drop_column("mrv_conflicts", "monitoring_tillage_period")
    op.drop_column("mrv_conflicts", "baseline_attribute_id")
    op.drop_column("mrv_conflicts", "baseline_tillage_period")
    op.create_foreign_key(None, "mrv_phase_commercials", "mrv_phases", ["phase_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_phase_commercials", type_="foreignkey")
    op.add_column("mrv_conflicts", sa.Column("baseline_tillage_period", mysql.ENUM("fall", "spring"), nullable=True))
    op.add_column(
        "mrv_conflicts",
        sa.Column("baseline_attribute_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
    )
    op.add_column("mrv_conflicts", sa.Column("monitoring_tillage_period", mysql.ENUM("fall", "spring"), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("optis_tillage_period", mysql.ENUM("fall", "spring"), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("optis_value", mysql.VARCHAR(length=255), nullable=True))
    op.add_column(
        "mrv_conflicts", sa.Column("conflict_type", mysql.ENUM("SUMMER_CROP", "WINTER_CROP", "TILLAGE"), nullable=False)
    )
    op.add_column(
        "mrv_conflicts",
        sa.Column("baseline_value", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    )
    op.add_column(
        "mrv_conflicts",
        sa.Column("monitoring_attribute_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
    )
    op.add_column(
        "mrv_conflicts",
        sa.Column("monitoring_value", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    )
    op.add_column("mrv_conflicts", sa.Column("producer_name", mysql.VARCHAR(length=255), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("optis_value_confidence", mysql.FLOAT(), nullable=False))
    op.drop_constraint(None, "mrv_conflicts", type_="foreignkey")
    op.create_foreign_key(
        "mrv_conflicts_ibfk_4", "mrv_conflicts", "mrv_attributes", ["monitoring_attribute_id"], ["id"]
    )
    op.create_foreign_key("mrv_conflicts_ibfk_1", "mrv_conflicts", "mrv_attributes", ["baseline_attribute_id"], ["id"])
    op.create_foreign_key("mrv_conflicts_ibfk_5", "mrv_conflicts", "mrv_values", ["monitoring_value"], ["id"])
    op.create_foreign_key("mrv_conflicts_ibfk_2", "mrv_conflicts", "mrv_values", ["baseline_value"], ["id"])
    op.drop_constraint("_practice_type_project_id_field_id_uc", "mrv_conflicts", type_="unique")
    op.create_index(
        "_conflict_type_project_id_field_id_uc",
        "mrv_conflicts",
        ["conflict_type", "project_id", "field_id"],
        unique=False,
    )
    op.drop_column("mrv_conflicts", "monitor_data")
    op.drop_column("mrv_conflicts", "monitor_value")
    op.drop_column("mrv_conflicts", "measurement_data")
    op.drop_column("mrv_conflicts", "measurement_value")
    op.drop_column("mrv_conflicts", "practice_type")
    op.add_column(
        "mrv_conflict_resolution_files",
        sa.Column("usage", mysql.ENUM("CUSTOMER", "FCI", "NDTI", "NDVI", "RGB"), nullable=False),
    )
    op.drop_index(op.f("ix_mrv_project_conflict_metadata_project_id"), table_name="mrv_project_conflict_metadata")
    op.drop_index(op.f("ix_mrv_project_conflict_metadata_id"), table_name="mrv_project_conflict_metadata")
    op.drop_table("mrv_project_conflict_metadata")
    # ### end Alembic commands ###
