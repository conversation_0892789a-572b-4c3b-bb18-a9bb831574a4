"""Add ondelete CASCADE

Revision ID: ea70a312de07
Revises: d767162b095e
Create Date: 2024-06-25 13:57:33.454913

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ea70a312de07"
down_revision = "e5a24170e107"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_field_overlap_result_ibfk_3", "mrv_field_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_field_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_program_overlap_request_ibfk_3", "mrv_program_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_program_overlap_request", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_program_overlap_result_ibfk_3", "mrv_program_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_program_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_project_overlap_request_ibfk_3", "mrv_project_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_project_overlap_request", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_project_overlap_result_ibfk_3", "mrv_project_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_project_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_region_overlap_request_ibfk_3", "mrv_region_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_region_overlap_request", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("mrv_region_overlap_result_ibfk_3", "mrv_region_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        None, "mrv_region_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_region_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_region_overlap_result_ibfk_3",
        "mrv_region_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_region_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        "mrv_region_overlap_request_ibfk_3",
        "mrv_region_overlap_request",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_project_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_project_overlap_result_ibfk_3",
        "mrv_project_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_project_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        "mrv_project_overlap_request_ibfk_3",
        "mrv_project_overlap_request",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_program_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_program_overlap_result_ibfk_3",
        "mrv_program_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_program_overlap_request", type_="foreignkey")
    op.create_foreign_key(
        "mrv_program_overlap_request_ibfk_3",
        "mrv_program_overlap_request",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
    )
    op.drop_constraint(None, "mrv_field_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_field_overlap_result_ibfk_3", "mrv_field_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"]
    )
    # ### end Alembic commands ###
