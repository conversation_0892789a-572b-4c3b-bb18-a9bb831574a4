"""rename field eligibility to field fact

Revision ID: 9f56254861cf
Revises: ced36529ce00
Create Date: 2023-11-03 17:10:24.288336

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "9f56254861cf"
down_revision = "ced36529ce00"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_field_facts",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "phase_id",
            sa.Integer(),
            nullable=True,
            comment="Enrolment vs monitoring phase - see table comment, For baseline phase is set to enrolment",
        ),
        sa.Column("field_id", sa.Integer(), nullable=False, comment="The field these practices apply to"),
        sa.Column(
            "stage_id",
            sa.Integer(),
            nullable=True,
            comment="Always NULL during a monitoring phase, always set during an enrolment phase. This is also null during baseline and contracted stated.",
        ),
        sa.<PERSON>umn("eligible", sa.<PERSON>(), nullable=True),
        sa.<PERSON>umn(
            "facts",
            sa.JSON(),
            nullable=True,
            comment="An array of arrays, e.g. [['no till'], ['reduced till', 'cover crops']]. During the monitoring phase, when we only have the actual practices carried out, there's only one nested array,e.g. [['no till', 'cover crops']]",
        ),
        sa.Column(
            "state",
            sa.Enum("contracted", "eligible", "measured", "baseline", name="fielddatastate"),
            nullable=False,
            comment="The state of the field data",
        ),
        sa.Column(
            "data_type",
            sa.Enum("entity_crop_type", "entity_practices", "entity_yield", name="entitydatatype"),
            nullable=False,
            comment="The type of data we're collecting for a field",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["mrv_phases.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="When we determine which practices are eligible for a field, we store them in this table. During the enrolment phase, we store all the eligible practices. During the monitoring phase, we store the actual practices that were carried out. During both phases, we might calculate the eligible practices for a field many times and store the result each time. The result for any given field might vary over time (e.g. because the rules have changed). Always use the latest results (created_at) for a given field.Update-1: Renamed model from mrv_field_eligibilities to mrv_field_, so that we can includemore than eligible practices into this table. We will also store the crop types, yield, etc.",
    )
    op.create_index(op.f("ix_mrv_field_facts_created_at"), "mrv_field_facts", ["created_at"], unique=False)
    op.create_index(op.f("ix_mrv_field_facts_id"), "mrv_field_facts", ["id"], unique=False)
    op.drop_index("ix_mrv_field_eligibilities_created_at", table_name="mrv_field_eligibilities")
    op.drop_index("ix_mrv_field_eligibilities_id", table_name="mrv_field_eligibilities")
    op.drop_table("mrv_field_eligibilities")
    op.add_column("mrv_conflict_practice_config", sa.Column("fall_tillage_attr_id", sa.Integer(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("spring_tillage_attr_id", sa.Integer(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("winter_crop_attr_id", sa.Integer(), nullable=True))
    op.add_column("mrv_conflict_practice_config", sa.Column("commodity_crop_attr_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "mrv_conflict_practice_config", "mrv_attributes", ["commodity_crop_attr_id"], ["id"])
    op.create_foreign_key(None, "mrv_conflict_practice_config", "mrv_attributes", ["winter_crop_attr_id"], ["id"])
    op.create_foreign_key(None, "mrv_conflict_practice_config", "mrv_attributes", ["spring_tillage_attr_id"], ["id"])
    op.create_foreign_key(None, "mrv_conflict_practice_config", "mrv_attributes", ["fall_tillage_attr_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_constraint(None, "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_constraint(None, "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_constraint(None, "mrv_conflict_practice_config", type_="foreignkey")
    op.drop_column("mrv_conflict_practice_config", "commodity_crop_attr_id")
    op.drop_column("mrv_conflict_practice_config", "winter_crop_attr_id")
    op.drop_column("mrv_conflict_practice_config", "spring_tillage_attr_id")
    op.drop_column("mrv_conflict_practice_config", "fall_tillage_attr_id")
    op.create_table(
        "mrv_field_eligibilities",
        sa.Column("id", mysql.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "field_id",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="The field these practices apply to",
        ),
        sa.Column(
            "stage_id",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Always NULL during a monitoring phase, always set during an enrolment phase",
        ),
        sa.Column("eligible", mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
        sa.Column(
            "eligible_practices",
            mysql.JSON(),
            nullable=True,
            comment="An array of arrays, e.g. [['no till'], ['reduced till', 'cover crops']]. During the monitoring phase, when we only have the actual practices carried out, there's only one nested array,e.g. [['no till', 'cover crops']]",
        ),
        sa.Column("created_at", mysql.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column(
            "phase_id",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Enrolment vs monitoring phase - see table comment",
        ),
        sa.ForeignKeyConstraint(["field_id"], ["mrv_fields.id"], name="mrv_field_eligibilities_ibfk_1"),
        sa.ForeignKeyConstraint(["phase_id"], ["mrv_phases.id"], name="mrv_field_eligibilities_ibfk_3"),
        sa.ForeignKeyConstraint(["stage_id"], ["mrv_stages.id"], name="mrv_field_eligibilities_ibfk_2"),
        sa.PrimaryKeyConstraint("id"),
        comment="When we determine which practices are eligible for a field, we store them in this table. During the enrolment phase, we store all the eligible practices. During the monitoring phase, we store the actual practices that were carried out. During both phases, we might calculate the eligible practices for a field many times and store the result each time. The result for any given field might vary over time (e.g. because the rules have changed). Always use the latest results (created_at) for a given field.",
        mysql_collate="utf8mb4_0900_ai_ci",
        mysql_comment="When we determine which practices are eligible for a field, we store them in this table. During the enrolment phase, we store all the eligible practices. During the monitoring phase, we store the actual practices that were carried out. During both phases, we might calculate the eligible practices for a field many times and store the result each time. The result for any given field might vary over time (e.g. because the rules have changed). Always use the latest results (created_at) for a given field.",
        mysql_default_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_index("ix_mrv_field_eligibilities_id", "mrv_field_eligibilities", ["id"], unique=False)
    op.create_index("ix_mrv_field_eligibilities_created_at", "mrv_field_eligibilities", ["created_at"], unique=False)
    op.drop_index(op.f("ix_mrv_field_facts_id"), table_name="mrv_field_facts")
    op.drop_index(op.f("ix_mrv_field_facts_created_at"), table_name="mrv_field_facts")
    op.drop_table("mrv_field_facts")
    # ### end Alembic commands ###
