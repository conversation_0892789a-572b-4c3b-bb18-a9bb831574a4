"""add mrv_program_modeling_configuration.apply_ghg_per_acre_bounds

Revision ID: 66ab2f5f84cc
Revises: 57883723f966
Create Date: 2025-05-29 14:04:15.743587

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "66ab2f5f84cc"
down_revision = "57883723f966"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_program_modeling_configuration",
        sa.Column("apply_ghg_per_acre_bounds", sa.<PERSON>(), nullable=False, default=True),
    )


def downgrade():
    op.drop_column("mrv_program_modeling_configuration", "apply_ghg_per_acre_bounds")
