"""Change permission name to GET_PROGRAMS_LIST

Revision ID: 6193d0a6e173
Revises: d699cb2f903f
Create Date: 2024-04-26 10:03:50.764658

"""

import enum

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
    restore_removed_permissions,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "6193d0a6e173"
down_revision = "d699cb2f903f"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROGRAMS_LIST.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_PROGRAMS_LIST.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_PROGRAMS_LIST.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_PROGRAMS_LIST.value,
    },
}


class RemovedPermissions(enum.StrEnum):
    GET_PROGRAM_STATS = "Get Program Stats"


removed_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        RemovedPermissions.GET_PROGRAM_STATS.name,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        RemovedPermissions.GET_PROGRAM_STATS.name,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        RemovedPermissions.GET_PROGRAM_STATS.name,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        RemovedPermissions.GET_PROGRAM_STATS.name,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
    restore_removed_permissions(session, regular_roles, removed_permissions_mapping)
