"""add-field-measurement-eligibility-field

Revision ID: 6a21b7104790
Revises: 836bdee91a09
Create Date: 2023-10-25 23:29:10.313377

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6a21b7104790"
down_revision = "836bdee91a09"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_fields", sa.Column("measurement_eligibility", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("mrv_fields", "measurement_eligibility")
