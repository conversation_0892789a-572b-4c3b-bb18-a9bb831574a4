"""add request group

Revision ID: dcc45d2919ee
Revises: 76d4f903ecca
Create Date: 2024-04-12 01:42:13.431257

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "dcc45d2919ee"
down_revision = "76d4f903ecca"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_monitor_request_group",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False, comment="Project id for which request group is made"),
        sa.Column(
            "year_start",
            sa.Integer(),
            nullable=False,
            comment="Start year for which request group is made. Year is inclusive.",
        ),
        sa.Column(
            "year_end",
            sa.Integer(),
            nullable=False,
            comment="End year for which request group is made. Year is inclusive.",
        ),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING",
                "SUCCESS",
                "FAILED",
                "PARTIALLY_FAILED",
                "CANCELLED",
                "WAITING_FOR_CALLBACK",
                name="requeststorestatus",
            ),
            nullable=False,
            comment="Status of request. We keep single request in pending/completed/failed/partially_failed status, rest will be in cancelled state",
        ),
        sa.Column(
            "error_message",
            sa.JSON(),
            nullable=True,
            comment="Stores error message if any of the request/response store fails",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column(
            "completed_at", sa.TIMESTAMP(), nullable=True, comment="Time when request group is finished processing"
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["mrv_projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="Store request group for monitor api for a project",
    )
    op.create_index(op.f("ix_mrv_monitor_request_group_id"), "mrv_monitor_request_group", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_monitor_request_group_project_id"), "mrv_monitor_request_group", ["project_id"], unique=False
    )
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "request_group_id",
            sa.Integer(),
            nullable=True,
            comment="Request group id for which request is made",
            server_default=None,
        ),
    )
    # migrating request store to request group
    op.execute(
        """
    INSERT INTO mrv_monitor_request_group (project_id, year_start, year_end, status, created_at, updated_at)
    SELECT
        mmrs.project_id AS project_id,
        mmrs.year_start AS year_start,
        mmrs.year_end AS year_end,
        mmrs.status AS status,
        mmrs.created_at AS created_at,
        mmrs.updated_at AS updated_at
    FROM mrv_monitor_request_store mmrs
    """
    )
    op.create_index(
        op.f("ix_mrv_monitor_request_store_request_group_id"),
        "mrv_monitor_request_store",
        ["request_group_id"],
        unique=False,
    )
    op.create_foreign_key(None, "mrv_monitor_request_store", "mrv_monitor_request_group", ["request_group_id"], ["id"])
    op.drop_constraint("mrv_monitor_request_store_ibfk_1", "mrv_monitor_request_store", type_="foreignkey")
    op.drop_column("mrv_monitor_request_store", "year_end")
    op.drop_column("mrv_monitor_request_store", "year_start")
    op.drop_column("mrv_monitor_request_store", "project_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "year_start",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Start year for which request is made. Year is inclusive.",
        ),
    )
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "project_id",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Project id for which request is made",
        ),
    )
    op.add_column(
        "mrv_monitor_request_store",
        sa.Column(
            "year_end",
            mysql.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="End year for which request is made. Year is inclusive.",
        ),
    )
    op.drop_constraint(None, "mrv_monitor_request_store", type_="foreignkey")
    op.create_foreign_key(
        "mrv_monitor_request_store_ibfk_1", "mrv_monitor_request_store", "mrv_projects", ["project_id"], ["id"]
    )
    op.drop_index(op.f("ix_mrv_monitor_request_store_request_group_id"), table_name="mrv_monitor_request_store")
    op.create_index(
        "ix_mrv_monitor_request_store_project_id", "mrv_monitor_request_store", ["project_id"], unique=False
    )
    op.drop_column("mrv_monitor_request_store", "request_group_id")
    op.drop_index(op.f("ix_mrv_monitor_request_group_year_start"), table_name="mrv_monitor_request_group")
    op.drop_index(op.f("ix_mrv_monitor_request_group_year_end"), table_name="mrv_monitor_request_group")
    op.drop_index(op.f("ix_mrv_monitor_request_group_project_id"), table_name="mrv_monitor_request_group")
    op.drop_index(op.f("ix_mrv_monitor_request_group_id"), table_name="mrv_monitor_request_group")
    op.drop_table("mrv_monitor_request_group")
    # ### end Alembic commands ###
