"""add enum 'api_internal'

Revision ID: e5a24170e107
Revises: 21d8b71fdde4
Create Date: 2024-06-26 10:36:22.099284

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "e5a24170e107"
down_revision = "21d8b71fdde4"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.UPDATE_FIELD_GEOMETRY.value,
    },
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_fields_history",
        "reason",
        existing_type=mysql.ENUM("api", "fmi"),
        type_=sa.Enum("api", "api_internal", "fmi", name="fieldhistorychangereason"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_fields_history",
        "reason",
        existing_type=sa.Enum("api", "api_internal", "fmi", name="fieldhistorychangereason"),
        type_=mysql.ENUM("api", "fmi"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
