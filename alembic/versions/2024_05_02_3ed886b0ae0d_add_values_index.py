"""Add values index

Revision ID: 3ed886b0ae0d
Revises: eed5fd563f9f
Create Date: 2024-05-02 13:32:11.797254

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "3ed886b0ae0d"
down_revision = "eed5fd563f9f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_mrv_values_updated_at"), "mrv_values", ["updated_at"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_values_updated_at"), table_name="mrv_values")
    # ### end Alembic commands ###
