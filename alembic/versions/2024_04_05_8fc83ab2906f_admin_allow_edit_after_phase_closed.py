"""admin allow edit after phase closed

Revision ID: 8fc83ab2906f
Revises: ff88691cb3e3
Create Date: 2024-04-05 01:41:44.719867

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8fc83ab2906f"
down_revision = "ff88691cb3e3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_project_phase_completion",
        sa.Column(
            "allow_post_close_edit",
            sa.<PERSON>(),
            server_default="0",
            nullable=False,
            comment="If true, the user can edit the phase data even after the phase close date has passed",
        ),
    )


def downgrade():
    op.drop_column("mrv_project_phase_completion", "allow_post_close_edit")
