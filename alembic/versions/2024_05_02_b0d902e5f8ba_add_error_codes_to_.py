"""Add error codes to ScenariosServiceIntegrationErrorCode

Revision ID: b0d902e5f8ba
Revises: d5d9af1a3c08
Create Date: 2024-05-02 18:50:22.282158

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "b0d902e5f8ba"
down_revision = "3ed886b0ae0d"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
        ),
        type_=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            name="scenariosserviceintegrationerrorcode",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_simulation_requests",
        "error_code",
        existing_type=sa.Enum(
            "unsupported_crop",
            "unsupported_product_type",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
            "no_tillage_events_to_reduce_depth",
            "no_boundaries_service_fertilizer_data",
            "no_cultivation_cycles_after_baseline_year",
            "period_between_commodity_crops_too_short",
            name="scenariosserviceintegrationerrorcode",
        ),
        type_=mysql.ENUM(
            "unsupported_crop",
            "unsupported_product_type",
            "scenarios_service_unavailable",
            "scenarios_service_validation_failure",
            "boundaries_service_unavailable",
            "invalid_management_data",
            "overlapping_commodity_crop_periods",
            "missing_baseline_year",
            "generic_scenarios_service_failure",
            "event_outside_phase_period",
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
