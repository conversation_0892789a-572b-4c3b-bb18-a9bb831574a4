"""FSB-14664-additional-fuel-type

Revision ID: 403f573a18a8
Revises: 2a75f578e08c
Create Date: 2023-09-22 14:24:26.883811

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "403f573a18a8"
down_revision = "2a75f578e08c"
branch_labels = None
depends_on = None


def upgrade():

    # update mrv_attribute_options_defaults, find row with type = 'fuel_type' and update the value to include 'Hydrogen'
    sql = """
    update mrv_attribute_options_defaults
    set `options` = json_array('Ethanol', 'Gasoline', 'Diesel', 'Bio-diesel', 'Hydrogen')
    where `type` = 'fuel_type'
    """
    op.execute(sql)


def downgrade():
    # update mrv_attribute_options_defaults, find row with type = 'fuel_type' and update the value to include 'Hydrogen'
    sql = """
    update mrv_attribute_options_defaults
    set `options` = json_array('Ethanol', 'Gasoline', 'Diesel', 'Bio-diesel')
    where `type` = 'fuel_type'
    """
    op.execute(sql)
