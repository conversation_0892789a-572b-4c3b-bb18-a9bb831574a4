"""FSB-14425-addtional-practice-changes

Revision ID: e2571e1829c8
Revises: 403f573a18a8
Create Date: 2023-09-25 09:10:51.399935

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "e2571e1829c8"
down_revision = "403f573a18a8"
branch_labels = None
depends_on = None


def existing_types():
    return [
        "cover_crops",
        "tillage_reduction",
        "fertilizer_reduction",
        "integrated_grazing",
        "nutrient_management",
        "conservation_practices",
        "reduced_till",
        "no_till",
        "composting",
        "whole_orchard_recycling",
        "crop_rotation",
        "irrigation_management",
        "basic_cover_crops",
        "premium_cover_crops",
        "conventional_till",
        "no_cover_crop",
        "alternating_wet_dry",
        "furrow_irrigation",
        "stubble_retention",
        "stocking_intensity",
        "stocking_duration",
        "soil_amendments",
        "incorporate_legumes",
        "seeding_or_pasture_cropping",
        "landscape_modification",
        "thinning_woody_vegetation",
        "clear_woody_vegetation",
        "rotation_or_intensive_grazing",
        "tcf_irrigation",
        "drip_irrigation",
    ]


def new_types():
    return existing_types() + ["rate_reduction", "split_application", "timing"]


def upgrade():

    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=mysql.ENUM(*existing_types()),
        type_=sa.Enum(*new_types(), name="practicechange"),
        existing_nullable=False,
    )

    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=mysql.ENUM(*existing_types()),
        type_=sa.Enum(*new_types(), name="practicechange"),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_county_eligibilities",
        "practice",
        existing_type=mysql.ENUM(*new_types()),
        type_=sa.Enum(*existing_types(), name="practicechange"),
        existing_nullable=False,
    )

    op.alter_column(
        "mrv_program_practice_change",
        "practice_change",
        existing_type=mysql.ENUM(*new_types()),
        type_=sa.Enum(*existing_types(), name="practicechange"),
        existing_nullable=False,
    )
