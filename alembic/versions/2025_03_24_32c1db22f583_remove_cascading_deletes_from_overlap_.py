"""Remove cascading deletes from overlap results

Revision ID: 32c1db22f583
Revises: 8fdac392b66c
Create Date: 2025-03-24 11:00:17.280473

"""

from alembic import op

# from sqlalchemy.orm.session import Session
# from permissions.alembic_helpers import (
#     create_all_regular_roles_with_permissions,
#     delete_roles_permissions_records,
# )
# from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "32c1db22f583"
down_revision = "8fdac392b66c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
# regular_roles = {role.value for role in DefaultRoles}
#
# roles_to_permissions_mapping = {
#     DefaultRoles.SUPER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PRODUCER.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
#     DefaultRoles.RESTRICTED_USER_ADMIN.value: {
#         Permission.NEW_PERMISSION.value,
#     },
# }


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_field_overlap_result_ibfk_3", "mrv_field_overlap_result", type_="foreignkey")
    op.create_foreign_key(None, "mrv_field_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"])
    op.drop_constraint("mrv_program_overlap_result_ibfk_3", "mrv_program_overlap_result", type_="foreignkey")
    op.create_foreign_key(None, "mrv_program_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"])
    op.drop_constraint("mrv_project_overlap_result_ibfk_3", "mrv_project_overlap_result", type_="foreignkey")
    op.create_foreign_key(None, "mrv_project_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"])
    op.drop_constraint("mrv_region_overlap_result_ibfk_3", "mrv_region_overlap_result", type_="foreignkey")
    op.create_foreign_key(None, "mrv_region_overlap_result", "mrv_determine_overlap_task", ["task_id"], ["id"])
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # create_all_regular_roles_with_permissions(
    #     session, regular_roles, roles_to_permissions_mapping
    # )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "mrv_region_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_region_overlap_result_ibfk_3",
        "mrv_region_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(None, "mrv_project_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_project_overlap_result_ibfk_3",
        "mrv_project_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(None, "mrv_program_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_program_overlap_result_ibfk_3",
        "mrv_program_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(None, "mrv_field_overlap_result", type_="foreignkey")
    op.create_foreign_key(
        "mrv_field_overlap_result_ibfk_3",
        "mrv_field_overlap_result",
        "mrv_determine_overlap_task",
        ["task_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###

    # uncomment in case new permissions were added
    # session = Session(bind=op.get_bind())
    # delete_roles_permissions_records(
    #     session, regular_roles, roles_to_permissions_mapping
    # )
