"""Add permission GET_CUBE_DATA

Revision ID: 143f770a595e
Revises: a292672da8af
Create Date: 2024-07-10 09:36:23.374549

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "143f770a595e"
down_revision = "a292672da8af"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.GROUP_ADMIN.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {
        Permission.GET_CUBE_DATA.value,
    },
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {
        Permission.GET_CUBE_DATA.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
