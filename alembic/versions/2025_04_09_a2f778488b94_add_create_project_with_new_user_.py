"""Add create project with new user permission

Revision ID: a2f778488b94
Revises: ad807017257b
Create Date: 2025-04-09 12:26:37.635166

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "a2f778488b94"
down_revision = "ad807017257b"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}
roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS.value: {Permission.CREATE_PROJECT_WITH_NEW_USER.value},
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {Permission.CREATE_PROJECT_WITH_NEW_USER.value},
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
