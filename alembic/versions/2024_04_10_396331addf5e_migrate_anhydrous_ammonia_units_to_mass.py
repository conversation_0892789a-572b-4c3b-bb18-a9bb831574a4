"""Make anhydrous ammonia an is_dry=True product everywhere.

Revision ID: 76d4f903ecca
Revises: 3a72be0eaf90
Create Date: 2024-04-04 15:04:52.408796

"""

import copy

from sqlalchemy import select
from sqlalchemy.orm import Session

from alembic import op
from phases.enums import AttributeTypes
from phases.model import Attribute

# revision identifiers, used by Alembic.
revision = "76d4f903ecca"
down_revision = "98b98428742b"
branch_labels = None
depends_on = None


def get_attributes_by_type(session: Session, attribute_type: AttributeTypes) -> list[int]:
    query = select(Attribute.id).where(Attribute.type == attribute_type)
    res = session.execute(query)
    return res.scalars().all()


def get_attributes(session: Session, ids: list[int]) -> list[Attribute]:
    query = select(Attribute).where(Attribute.id.in_(ids))
    res = session.execute(query)
    return res.scalars().all()


def segment_attribute_ids(attribute_ids: list[int]) -> list[list[int]]:
    segment_size = 50
    return [attribute_ids[i : i + segment_size] for i in range(0, len(attribute_ids), segment_size)]


def upgrade():
    session = Session(bind=op.get_bind())
    try:
        # We want to change all Anhydrous Ammonia products to dry instead of liquid, so we go through all the existing
        # Attribute Options and map the two possible representations for this info to the new value.
        attribute_ids: list[int] = get_attributes_by_type(session, AttributeTypes.application_product)
        for attribute_id_batch in segment_attribute_ids(attribute_ids):
            for attribute in get_attributes(session, attribute_id_batch):
                # First we have to do a pre-check that this attribute needs to be mapped, since SQA forces us to deepcopy
                # the JSON column and doing that on every record would take a long time (I assume).
                if any(
                    [
                        isinstance(option, dict)
                        and option["value"] == "aa"
                        and (
                            ("label" in option and "(liq)" in option["label"])
                            or ("meta" in option and option["meta"] == "liquid")
                        )
                        for option in attribute.options
                    ]
                ):
                    # Do a deep copy because SQA doesn't track changes to JSON columns
                    attribute.options = copy.deepcopy(attribute.options)
                    try:
                        anhydrous_ammonia = next(
                            product for product in attribute.options if "value" in product and product["value"] == "aa"
                        )
                    except StopIteration:
                        continue
                    if "meta" in anhydrous_ammonia and anhydrous_ammonia["meta"] == "liquid":
                        anhydrous_ammonia["meta"] = "dry"
                    if "label" in anhydrous_ammonia and "(liq)" in anhydrous_ammonia["label"]:
                        anhydrous_ammonia["label"] = anhydrous_ammonia["label"].replace("(liq)", "(dry)")
                    session.add(attribute)
            session.commit()
    except Exception as exc:
        if "MockConnection" not in str(exc):
            raise exc


def downgrade():
    session = Session(bind=op.get_bind())
    try:
        attribute_ids: list[int] = get_attributes_by_type(session, AttributeTypes.application_product)
        for attribute_id_batch in segment_attribute_ids(attribute_ids):
            for attribute in get_attributes(session, attribute_id_batch):
                # First we have to do a pre-check that this attribute needs to be mapped, since SQA forces us to deepcopy
                # the JSON column and doing that on every record would take a long time (I assume).
                if any(
                    [
                        isinstance(option, dict)
                        and option["value"] == "aa"
                        and (
                            ("label" in option and "(dry)" in option["label"])
                            or ("meta" in option and option["meta"] == "dry")
                        )
                        for option in attribute.options
                    ]
                ):
                    # Do a deep copy because SQA doesn't track changes to JSON columns
                    attribute.options = copy.deepcopy(attribute.options)
                    try:
                        anhydrous_ammonia = next(
                            product for product in attribute.options if "value" in product and product["value"] == "aa"
                        )
                    except StopIteration:
                        continue
                    if "meta" in anhydrous_ammonia and anhydrous_ammonia["meta"] == "dry":
                        anhydrous_ammonia["meta"] = "liquid"
                    if "label" in anhydrous_ammonia and "(dry)" in anhydrous_ammonia["label"]:
                        anhydrous_ammonia["label"] = anhydrous_ammonia["label"].replace("(dry)", "(liq)")
                    session.add(attribute)
            session.commit()
    except Exception as exc:
        if "MockConnection" not in str(exc):
            raise exc
