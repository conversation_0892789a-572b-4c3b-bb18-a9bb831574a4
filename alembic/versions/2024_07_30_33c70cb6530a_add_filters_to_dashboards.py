"""Add filters to Dashboards and invalid_filters to Charts

Revision ID: 33c70cb6530a
Revises: bc1c8cbd11e8
Create Date: 2024-07-30 14:46:59.291706

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "33c70cb6530a"
down_revision = "bc1c8cbd11e8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_reporting_chart_presets", sa.<PERSON>umn("invalid_filters", sa.JSON(), nullable=True))
    op.add_column("mrv_reporting_dashboards", sa.Column("filters", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards", "filters")
    op.drop_column("mrv_reporting_chart_presets", "invalid_filters")
    # ### end Alembic commands ###
