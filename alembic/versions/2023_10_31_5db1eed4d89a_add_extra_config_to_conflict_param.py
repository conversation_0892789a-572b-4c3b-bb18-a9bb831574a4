"""add extra config to conflict param

Revision ID: 5db1eed4d89a
Revises: 70d6b566ec50
Create Date: 2023-10-31 19:41:04.466695

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5db1eed4d89a"
down_revision = "70d6b566ec50"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_conflict_practice_config",
        sa.Column(
            "extra_config",
            sa.JSON(),
            nullable=True,
            comment="Extra configuration for conflict generation, supports practice parameter green cover override values for now. Ex: {'ppgc_date_range': {'start_date': '2022-01-01', 'end_date': '2022-04-04'}}",
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_conflict_practice_config", "extra_config")
    # ### end Alembic commands ###
