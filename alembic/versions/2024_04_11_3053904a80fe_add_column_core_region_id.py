"""Add column core region id

Revision ID: 3053904a80fe
Revises: 76d4f903ecca
Create Date: 2024-04-11 08:43:04.887980

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "3053904a80fe"
down_revision = "dcc45d2919ee"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_fields",
        sa.Column(
            "core_region_id",
            mysql.INTEGER(display_width=10, unsigned=True),
            nullable=True,
            comment="i.e. kml_files.region_id",
        ),
    )
    op.create_index(op.f("ix_mrv_fields_core_region_id"), "mrv_fields", ["core_region_id"], unique=False)
    op.add_column(
        "mrv_fields_history",
        sa.Column(
            "core_region_id",
            mysql.INTEGER(display_width=10, unsigned=True),
            nullable=True,
            comment="i.e. kml_files.region_id",
        ),
    )
    op.create_index(
        op.f("ix_mrv_fields_history_core_region_id"), "mrv_fields_history", ["core_region_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_fields_history_core_region_id"), table_name="mrv_fields_history")
    op.drop_column("mrv_fields_history", "core_region_id")
    op.drop_index(op.f("ix_mrv_fields_core_region_id"), table_name="mrv_fields")
    op.drop_column("mrv_fields", "core_region_id")
    # ### end Alembic commands ###
