"""Add new event-based stage types

Revision ID: 66c46d8d7e31
Revises: 21713022f8ac
Create Date: 2023-10-17 09:33:47.043570

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "66c46d8d7e31"
down_revision = "21713022f8ac"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_stages",
        "type_",
        existing_type=mysql.ENUM(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "T<PERSON>LA<PERSON>",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATI<PERSON>",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
        ),
        type_=sa.Enum(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
            name="stagetypes",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_stages",
        "type_",
        existing_type=sa.Enum(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
            "CROP_EVENTS",
            "IRRIGATION_EVENTS",
            "NUTRIENT_EVENTS",
            "TILLAGE_EVENTS",
            name="stagetypes",
        ),
        type_=mysql.ENUM(
            "FIELD_BOUNDARIES",
            "ASSIGN_PRACTICES",
            "CONFIRM_HISTORY",
            "SUMMER_CROPS",
            "WINTER_CROPS",
            "TILLAGE",
            "NUTRIENT_MGMT",
            "VIEW_OUTCOMES",
            "SURVEY",
            "CONTRACT",
            "ELIGIBILITY",
            "FIELD_INFORMATION",
            "HISTORICAL_CROP_ROTATION",
            "HISTORICAL_TILLAGE",
            "INTENDED_COMMODITY_CROPS",
            "IRRIGATION",
            "RICE_CROP_HISTORY",
            "MOB_HISTORY",
            "MOB_MOVEMENT",
            "FARM_LEVEL_MANAGEMENT",
            "NUTRIENT_MGMT_INTENDED",
        ),
        existing_nullable=True,
    )
