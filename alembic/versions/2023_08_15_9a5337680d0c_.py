"""Add Attribute application_rate_type

Revision ID: 9a5337680d0c
Revises: a502a736993c
Create Date: 2023-08-15 13:33:23.602229

"""

import enum

import sqlalchemy as sa

from alembic import op

revision = "9a5337680d0c"
down_revision = "a502a736993c"
branch_labels = None
depends_on = None


class OldAttributeTypes(enum.StrEnum):
    summer_crop_type = "summer_crop_type"
    summer_harvest_date = "summer_harvest_date"
    summer_dry_yield = "summer_dry_yield"
    summer_residue_harvested = "summer_residue_harvested"
    summer_planting_date = "summer_planting_date"

    winter_crop_commitment = "winter_crop_commitment"
    winter_crop_type = "winter_crop_type"
    winter_harvest_date = "winter_harvest_date"
    winter_dry_yield = "winter_dry_yield"
    winter_residue_harvested = "winter_residue_harvested"
    winter_planting_date = "winter_planting_date"

    fall_tillage_practice = "fall_tillage_practice"
    fall_tillage_date = "fall_tillage_date"
    fall_tillage_depth = "fall_tillage_depth"

    spring_tillage_practice = "spring_tillage_practice"
    spring_tillage_date = "spring_tillage_date"
    spring_tillage_depth = "spring_tillage_depth"

    # Similar to tillage_period. Could be better to have just `season`,
    # but for the sake of labels and translations, let's stick to having
    # a separate tillage and planting season attributes.
    planting_season = "planting_season"
    planting_date = "planting_date"
    harvest_date = "harvest_date"

    crop_usage = "crop_usage"

    other = "other"
    string = "string"
    number = "number"
    date = "date"
    bool_ = "bool"
    harvest = "harvest"
    planting = "planting"
    crop_type = "crop_type"
    crop_yield = "crop_yield"
    residue_harvested = "residue_harvested"
    tillage_period = "tillage_period"
    tillage_practice = "tillage_practice"
    tillage_date = "tillage_date"
    tillage_depth = "tillage_depth"
    tillage_event = "tillage_event"
    # The proportional area of the field that is strip tilled (0 < value < 1)
    strip_fraction = "strip_fraction"
    soil_inversion = "soil_inversion"

    # Enrolment Attributes
    spring_tillage = "Spring Tillage"
    fall_tillage = "Fall Tillage"
    winter_crop = "Winter Crop"
    practice = "Practice"
    summer_crop = "Summer Crop"
    # Hidden attr to save years for optis fields data
    record_year = "record_year"
    winter_crop_termination = "winter_crop_termination"

    # Application represents products applied at a given rate by mass or volume over an area.
    application_date = "application_date"
    application_product = "application_product"  # product name
    application_rate = "application_rate"  # numerical value
    application_rate_unit = "application_rate_unit"  # compound unit, eg kg1ha-1
    application_area = "application_area"  # ha
    application_product_type = "application_product_type"
    application_depth = "application_depth"
    # application_method should only apply when product_type == organic_amendment
    application_method = "application_method"
    application_rate_unit_vanity = "application_rate_unit_vanity"
    application_product_type_vanity = "application_product_type_vanity"
    application_rate_vanity = "application_rate_vanity"
    yield_rate_unit = "yield_rate_unit"
    application_area_vanity = "application_area_vanity"
    water_amount = "water_amount"
    water_amount_unit = "water_amount_unit"
    additive_one = "additive_one"
    additive_two = "additive_two"
    additives = "additives"

    # farm number can be alphanumeric with dots and dashes.. so it's not really a number
    farm_number = "farm_number"
    cover_crop_mix = "cover_crop_mix"

    # Irrigation Stage
    start_date = "start_date"
    end_date = "end_date"
    irrigation_method = "irrigation_method"
    irrigation_enabled = "irrigation_enabled"
    irrigation_rate_unit = "irrigation_rate_unit"
    flood_pct = "flood_pct"
    subsurface_drip_depth = "subsurface_drip_depth"
    subsurface_drip_depth_unit = "subsurface_drip_depth_unit"

    energy_source = "energy_source"
    fuel_type = "fuel_type"
    pesticide_farmprint_gaff = "pesticide_farmprint_gaff"  # farmprint gaff specific product

    # mob management related attributes
    change_type = "change_type"
    head_count = "head_count"
    livestock_class = "livestock_class"

    # farm management related attributes
    farm_name = "farm_name"
    area_sown = "area_sown"
    residue_burnt = "residue_burnt"
    area_tilled_total_pct = "area_tilled_total_pct"
    area_tilled_for_pasture_renewal_pct = "area_tilled_for_pasture_renewal_pct"
    nutrient_management_enabled = "nutrient_management_enabled"
    landscape_modifications_fuel_usage = "landscape_modifications_fuel_usage"
    landscape_modifications_fuel_type = "landscape_modifications_fuel_type"
    irrigation_fuel_usage = "irrigation_fuel_usage"
    irrigation_fuel_type = "irrigation_fuel_type"
    irrigation_electricity_usage = "irrigation_electricity_usage"
    irrigation_electricity_source = "irrigation_electricity_source"
    irrigation_electricity_grid_name = "irrigation_electricity_grid_name"
    total_area = "total_area"
    total_energy_usage = "total_energy_usage"
    total_fuel_usage = "total_fuel_usage"
    skip_assign_practice = "skip_assign_practice"

    # LDC Specific BS
    seeding_rate = "seeding_rate"
    seeding_rate_unit = "seeding_rate_unit"
    seed_variety = "seed_variety"


class NewAttributeTypes(enum.StrEnum):
    summer_crop_type = "summer_crop_type"
    summer_harvest_date = "summer_harvest_date"
    summer_dry_yield = "summer_dry_yield"
    summer_residue_harvested = "summer_residue_harvested"
    summer_planting_date = "summer_planting_date"

    winter_crop_commitment = "winter_crop_commitment"
    winter_crop_type = "winter_crop_type"
    winter_harvest_date = "winter_harvest_date"
    winter_dry_yield = "winter_dry_yield"
    winter_residue_harvested = "winter_residue_harvested"
    winter_planting_date = "winter_planting_date"

    fall_tillage_practice = "fall_tillage_practice"
    fall_tillage_date = "fall_tillage_date"
    fall_tillage_depth = "fall_tillage_depth"

    spring_tillage_practice = "spring_tillage_practice"
    spring_tillage_date = "spring_tillage_date"
    spring_tillage_depth = "spring_tillage_depth"

    # Similar to tillage_period. Could be better to have just `season`,
    # but for the sake of labels and translations, let's stick to having
    # a separate tillage and planting season attributes.
    planting_season = "planting_season"
    planting_date = "planting_date"
    harvest_date = "harvest_date"

    crop_usage = "crop_usage"

    other = "other"
    string = "string"
    number = "number"
    date = "date"
    bool_ = "bool"
    harvest = "harvest"
    planting = "planting"
    crop_type = "crop_type"
    crop_yield = "crop_yield"
    residue_harvested = "residue_harvested"
    tillage_period = "tillage_period"
    tillage_practice = "tillage_practice"
    tillage_date = "tillage_date"
    tillage_depth = "tillage_depth"
    tillage_event = "tillage_event"
    # The proportional area of the field that is strip tilled (0 < value < 1)
    strip_fraction = "strip_fraction"
    soil_inversion = "soil_inversion"

    # Enrolment Attributes
    spring_tillage = "Spring Tillage"
    fall_tillage = "Fall Tillage"
    winter_crop = "Winter Crop"
    practice = "Practice"
    summer_crop = "Summer Crop"
    # Hidden attr to save years for optis fields data
    record_year = "record_year"
    winter_crop_termination = "winter_crop_termination"

    # Application represents products applied at a given rate by mass or volume over an area.
    application_date = "application_date"
    application_product = "application_product"  # product name
    application_rate = "application_rate"  # numerical value
    application_rate_type = "application_rate_type"  # product rate or one of the nutrient-specific rates
    application_rate_unit = "application_rate_unit"  # compound unit, eg kg1ha-1
    application_area = "application_area"  # ha
    application_product_type = "application_product_type"
    application_depth = "application_depth"
    # application_method should only apply when product_type == organic_amendment
    application_method = "application_method"
    application_rate_unit_vanity = "application_rate_unit_vanity"
    application_product_type_vanity = "application_product_type_vanity"
    application_rate_vanity = "application_rate_vanity"
    yield_rate_unit = "yield_rate_unit"
    application_area_vanity = "application_area_vanity"
    water_amount = "water_amount"
    water_amount_unit = "water_amount_unit"
    additive_one = "additive_one"
    additive_two = "additive_two"
    additives = "additives"

    # farm number can be alphanumeric with dots and dashes.. so it's not really a number
    farm_number = "farm_number"
    cover_crop_mix = "cover_crop_mix"

    # Irrigation Stage
    start_date = "start_date"
    end_date = "end_date"
    irrigation_method = "irrigation_method"
    irrigation_enabled = "irrigation_enabled"
    irrigation_rate_unit = "irrigation_rate_unit"
    flood_pct = "flood_pct"
    subsurface_drip_depth = "subsurface_drip_depth"
    subsurface_drip_depth_unit = "subsurface_drip_depth_unit"

    energy_source = "energy_source"
    fuel_type = "fuel_type"
    pesticide_farmprint_gaff = "pesticide_farmprint_gaff"  # farmprint gaff specific product

    # mob management related attributes
    change_type = "change_type"
    head_count = "head_count"
    livestock_class = "livestock_class"

    # farm management related attributes
    farm_name = "farm_name"
    area_sown = "area_sown"
    residue_burnt = "residue_burnt"
    area_tilled_total_pct = "area_tilled_total_pct"
    area_tilled_for_pasture_renewal_pct = "area_tilled_for_pasture_renewal_pct"
    nutrient_management_enabled = "nutrient_management_enabled"
    landscape_modifications_fuel_usage = "landscape_modifications_fuel_usage"
    landscape_modifications_fuel_type = "landscape_modifications_fuel_type"
    irrigation_fuel_usage = "irrigation_fuel_usage"
    irrigation_fuel_type = "irrigation_fuel_type"
    irrigation_electricity_usage = "irrigation_electricity_usage"
    irrigation_electricity_source = "irrigation_electricity_source"
    irrigation_electricity_grid_name = "irrigation_electricity_grid_name"
    total_area = "total_area"
    total_energy_usage = "total_energy_usage"
    total_fuel_usage = "total_fuel_usage"
    skip_assign_practice = "skip_assign_practice"

    # LDC Specific BS
    seeding_rate = "seeding_rate"
    seeding_rate_unit = "seeding_rate_unit"
    seed_variety = "seed_variety"


def upgrade():
    op.alter_column(
        "mrv_attributes",
        "type",
        existing_type=sa.Enum(OldAttributeTypes),
        type_=sa.Enum(NewAttributeTypes),
    )


def downgrade():
    op.alter_column(
        "mrv_attributes",
        "type",
        existing_type=sa.Enum(NewAttributeTypes),
        type_=sa.Enum(OldAttributeTypes),
    )
