"""Simplify ProjectCompletionSummary table comment

Revision ID: bc1c8cbd11e8
Revises: 1a1505d250a3
Create Date: 2024-08-01 13:26:32.555276

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "bc1c8cbd11e8"
down_revision = "1a1505d250a3"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table_comment(
        "mrv_project_completion_summary",
        existing_comment="\n            ProjectCompletionSummary is a summary of ProjectCompletion.\n            For any project/stage pair it should always be true that\n            1. that the oldest created_at of ProjectCompletion is the created_at of ProjectCompletionSummary\n            2. given a sequence of unchanging percentage_complete leading to the most recent one in ProjectCompletion,\n            the oldest of those is the updated_at of ProjectCompletionSummary; in other words, it records the first time\n            it has its present value and that value hasn't changed to the present\n            ",
    )
    op.create_table_comment(
        "mrv_project_completion_summary", "ProjectCompletionSummary is a summary of ProjectCompletion", schema=None
    )


def downgrade():
    op.drop_table_comment(
        "mrv_project_completion_summary", existing_comment="ProjectCompletionSummary is a summary of ProjectCompletion"
    )
    op.create_table_comment(
        "mrv_project_completion_summary",
        "\n            ProjectCompletionSummary is a summary of ProjectCompletion.\n            For any project/stage pair it should always be true that\n            1. that the oldest created_at of ProjectCompletion is the created_at of ProjectCompletionSummary\n            2. given a sequence of unchanging percentage_complete leading to the most recent one in ProjectCompletion,\n            the oldest of those is the updated_at of ProjectCompletionSummary; in other words, it records the first time\n            it has its present value and that value hasn't changed to the present\n            ",
        schema=None,
    )
