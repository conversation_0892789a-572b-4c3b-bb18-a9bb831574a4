"""update conflict table

Revision ID: d0f6538b6b6f
Revises: 8bdcc9c93292
Create Date: 2024-08-08 00:18:38.623811

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d0f6538b6b6f"
down_revision = "8bdcc9c93292"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflicts", sa.Column("is_conflict", sa.<PERSON>(), server_default="1", nullable=False))
    op.alter_column(
        "mrv_conflicts",
        "previous_commodity_crop",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Integer(),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_conflicts",
        "current_commodity_crop",
        existing_type=mysql.VARCHAR(length=255),
        type_=sa.Integer(),
        existing_nullable=True,
    )
    op.create_foreign_key(
        "conflicts_current_commodity_value_fk", "mrv_conflicts", "mrv_values", ["current_commodity_crop"], ["id"]
    )
    op.create_foreign_key(
        "conflicts_previous_commodity_value_fk", "mrv_conflicts", "mrv_values", ["previous_commodity_crop"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("conflicts_current_commodity_value_fk", "mrv_conflicts", type_="foreignkey")
    op.drop_constraint("conflicts_previous_commodity_value_fk", "mrv_conflicts", type_="foreignkey")
    op.alter_column(
        "mrv_conflicts",
        "current_commodity_crop",
        existing_type=sa.Integer(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_conflicts",
        "previous_commodity_crop",
        existing_type=sa.Integer(),
        type_=mysql.VARCHAR(length=255),
        existing_nullable=True,
    )
    op.drop_column("mrv_conflicts", "is_conflict")
    # ### end Alembic commands ###
