"""add-min-field-size-boundary-rule

Revision ID: 2e3d6ef22b92
Revises: e2b9344e5a3c
Create Date: 2025-01-29 14:31:58.257597

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles

# revision identifiers, used by Alembic.
revision = "2e3d6ef22b92"
down_revision = "e2b9344e5a3c"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {}


def upgrade():
    op.alter_column("mrv_boundary_rule_config", "percent_threshold", existing_type=mysql.INTEGER(), nullable=True)
    op.add_column(
        "mrv_boundary_rule_config",
        sa.Column(
            "field_size",
            sa.Float,
            nullable=True,
            comment="Minimum or Maximum field size in hectares",
        ),
    )
    op.alter_column(
        "mrv_boundary_rule_config",
        "rule",
        existing_type=mysql.ENUM("cropland_or_grassland", "roads_or_rail", "hydrography", "program_fields"),
        type_=sa.Enum(
            "cropland_or_grassland",
            "roads_or_rail",
            "hydrography",
            "min_field_size",
            "max_field_size",
            "program_fields",
            name="fieldboundarycheckrule",
        ),
        existing_nullable=False,
    )
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    op.alter_column(
        "mrv_boundary_rule_config",
        "rule",
        existing_type=sa.Enum(
            "cropland_or_grassland",
            "roads_or_rail",
            "hydrography",
            "min_field_size",
            "max_field_size",
            "program_fields",
            name="fieldboundarycheckrule",
        ),
        type_=mysql.ENUM("cropland_or_grassland", "roads_or_rail", "hydrography", "program_fields"),
        existing_nullable=False,
    )
    op.drop_column("mrv_boundary_rule_config", "field_size")
    op.alter_column("mrv_boundary_rule_config", "percent_threshold", existing_type=mysql.INTEGER(), nullable=False)
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
