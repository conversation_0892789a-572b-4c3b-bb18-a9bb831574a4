"""Add permissions to copy Program

Revision ID: 321d70bc2e7f
Revises: 3b9d0407944d
Create Date: 2023-08-23 17:14:41.482264

"""

import enum

import sqlalchemy as sa
from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
    update_permissions_enum_type,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "321d70bc2e7f"
down_revision = "3b9d0407944d"
branch_labels = None
depends_on = None


regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROGRAM_WITH_CHILDREN.value,
        Permission.CREATE_PROGRAM_WITH_CHILDREN.value,
    },
}


@enum.unique
class PreviousPermission(enum.StrEnum):
    # authorization
    CREATE_ROLES = "Create Roles"
    CREATE_ROLES_PERMISSIONS = "Create Roles Permissions"
    CREATE_ROLES_USERS = "Create Roles Users"
    GET_PERMISSIONS = "Get Permissions"
    GET_USER_PERMISSIONS = "Get User Permissions"

    # program
    GET_PROGRAM = "Get Program"
    PATCH_PROGRAM = "Patch Program"
    DELETE_PROGRAM = "Delete Program"
    GET_PROGRAM_USER = "Get Program User"
    UPDATE_PROGRAM_USER = "Update Program User"
    GET_PROGRAM_USERS = "Get Program Users"
    CREATE_PROGRAM_USERS = "Create Program Users"
    DELETE_PROGRAM_USERS = "Delete Program Users"
    CREATE_PROGRAM_CROP_TYPES = "Create Program Crop Types"
    GET_PROGRAM_CROP_TYPES = "GET Program Crop Types"
    DELETE_PROGRAM_CROP_TYPES = "Delete Program Crop Types"
    GET_PROGRAM_PROJECTS = "Get Program Projects"
    GET_DUMP_PROGRAM_DATA = "Get Dump Program Data"
    CREATE_PROGRAM_FIX_MONITORING_DATA = "Create Program Fix Monitoring Data"
    CREATE_PROGRAM_FIX_ENROLMENT_DATA = "Create Program Fix Enrolment Data"
    CREATE_PHASE_PRACTICE_CONFIG = "Create Phase Practice Config"
    CREATE_PROGRAM_MONITORING_CONFIG = "Create Program Monitoring Config"
    GET_PROGRAM_MONITORING_CONFIG = "Get Program Monitoring Config"
    RUN_PROGRAM_PHASE_COMPLETION = "Run Program Phase Completion"

    # project
    GET_PROJECT = "Get Project"
    CREATE_PROJECT = "Create Project"
    DELETE_PROJECT = "Delete Project"
    GET_PROJECTS = "Get Projects"
    EXPORT_PROJECTS = "Export Projects"
    GET_PROJECT_USERS = "Get Project Users"
    CREATE_PROJECT_USERS = "Create Project Users"
    DELETE_PROJECT_USERS = "Delete Project Users"
    GET_PROJECT_FIELD = "Get Project Field"
    CREATE_PROJECT_FIELDS = "Create Project Fields"
    DELETE_PROJECT_FIELDS = "Delete Project Fields"
    GET_PROJECT_CONTRACT = "Get Project Contract"
    CREATE_PROJECT_CONTRACT = "Create Project Contract"
    DELETE_PROJECT_CONTRACT = "Delete Project Contract"
    GET_PROJECT_COMPLETION = "Get Project Completion"
    CREATE_CONTRACT_DELETION_REQUEST = "Create Contract Deletion Request"
    PATCH_PROJECT_CONFIG = "Patch Project Config"
    CREATE_PROJECT_RECORDING_YEARS = "Create Project Recording Years"
    CREATE_PROJECT_FIX_MONITORING_DATA = "Create Project Fix Monitoring Data"
    CREATE_PROJECT_FIX_ENROLMENT_DATA = "Create Project Fix Enrolment Data"

    # docusign
    GET_DOCUSIGN_CALLBACK = "Get Docusign Callback"
    GET_DOCUSIGN_CLIENT_ACCESS_DETAILS = "Get Docusign Client Access Details"
    GET_DOCUSIGN_CALLBACK_AFTER_SIGNING_CONTRACT = "Get Docusign Callback After Signing Contract"
    GET_CONTRACT_TEMPLATE_FOR_ENROLMENT = "Get Contract Template For Enrolment"
    CREATE_CONTRACT_TEMPLATE_FOR_PHASE = "Create Contract Template For Phase"
    DELETE_CONTRACT_TEMPLATE_FOR_ENROLMENT = "Delete Contract Template For Enrolment"
    GET_DOCUSIGN_TOKEN_CHECK = "Get Docusign Token Check"

    # dndc
    GET_DNDC_RESULT_FOR_PROJECT = "Get DNDC Result For Project"
    RUN_DNDC_FOR_PROJECT_FIELDS = "Run DNDC For Project Fields"
    DNDC_CALLBACK_WEBHOOK = "DNDC Callback Webhook"

    # notifications
    GET_NOTIFICATIONS = "Get Notifications"
    DISMISS_NOTIFICATIONS = "Dismiss Notifications"

    GET_PROGRAMS = "Get Programs"
    CREATE_PROGRAMS = "Create Programs"
    GET_PROGRAM_STATS = "Get Program Stats"

    UPDATE_PROGRAM_BOUNDARIES = "Update Program Boundaries"
    CREATE_BLOB = "Create Blob"
    GET_ELIGIBILITY = "Get Eligibility"

    GET_ASSETS = "Get Assets"
    ADD_ASSETS = "Add Assets"
    REMOVE_ASSETS = "Remove Assets"

    GET_PRACTICE_CHANGES = "Get Practice Changes"
    ADD_PRACTICE_CHANGES = "Add Practice Changes"
    REMOVE_PRACTICE_CHANGES = "Remove Practice Changes"

    GET_PROGRAM_PHASES = "Get Program Phases"
    CREATE_PROGRAM_PHASE = "Create Program Phase"
    GET_PROGRAM_PHASE = "Get Program Phase"
    UPDATE_PROGRAM_PHASE = "Update Program Phase"
    DELETE_PROGRAM_PHASE = "Delete Program Phase"

    GET_PROGRAM_STAGES = "Get Program Stages"
    CREATE_PROGRAM_STAGES = "Create Program Stages"
    DELETE_PROGRAM_STAGES = "Delete Program Stages"
    GET_PROGRAM_STAGE = "Get Program Stage"
    UPDATE_PROGRAM_STAGE = "Update Program Stage"
    UPDATE_PROGRAM_STAGES = "Update Program Stages"

    GET_PROGRAM_ATTRIBUTES = "Get Program Attributes"
    CREATE_PROGRAM_ATTRIBUTES = "Create Program Attributes"
    DELETE_PROGRAM_ATTRIBUTES = "Delete Program Attributes"
    GET_PROGRAM_ATTRIBUTE = "Get Program Attribute"
    UPDATE_PROGRAM_ATTRIBUTE = "Update Program Attribute"
    UPDATE_PROGRAM_ATTRIBUTES = "Update Program Attributes"

    GET_PROJECT_VALUES_FROM_OPTIS = "Get Project Values from Optis"

    GET_CONTRACT_LINE_ITEMS = "Get Contract Line Items"
    CREATE_CONTRACT_LINE_ITEMS = "Create Contract Line Items"
    PATCH_CONTRACT_LINE_ITEMS = "Patch Contract Line Items"
    DELETE_CONTRACT_LINE_ITEMS = "Delete Contract Line Items"

    GET_COMMERCIALS = "Get Commercials"
    CREATE_COMMERCIALS = "Create Commercials"
    PATCH_COMMERCIALS = "Patch Commercials"
    DELETE_COMMERCIALS = "Delete Commercials"

    GET_FMS_OPTION_NAMES = "Get FMS Options Names"
    CREATE_FMS_OPTIONS = "Create FMS Options"
    DELETE_FMS_OPTIONS = "Delete FMS Options"

    GET_PROGRAM_CONTRACT_ATTRIBUTES = "Get Program Contract Attributes"
    UPDATE_PROGRAM_CONTRACT_ATTRIBUTES = "Update Program Contract Attributes"

    CREATE_PROJECT_COMMERCIALS = "Create Project Commercials"

    GET_PAYMENT_TYPES = "Get Payment Types"

    GET_PROJECT_SURVEY = "Get Project Survey"
    CREATE_PROJECT_SURVEY = "Create Project Survey"

    GET_PROGRAM_BOUNDARIES = "Get Program Boundaries"

    GET_ELIGIBILITY_METHODS = "Get Eligibility Methods"

    CREATE_STAGE_ELIGIBILITY_CONFIG = "Create Stage Eligibility Config"

    # project field
    GET_PROJECT_FIELD_VALUES = "Get Project Field Values"
    GET_PROJECT_FIELD_VALUES_BY_ID = "Get Project Field Values by ID"
    GET_STRUCTURED_PROJECT_FIELD_VALUES = "Get Structured Project Field Values"
    GET_ALL_STRUCTURED_PROJECT_FIELD_VALUES = "Get All Structured Project Field Values"
    GET_PROJECT_VALUES_FOR_FIELDS = "Get Project Values for Fields"
    UPDATE_PROJECT_FIELD_VALUES = "Update Project Field Values"
    DELETE_PROJECT_FIELD_VALUES = "Delete Project Field Values"
    DELETE_SPECIFIC_PROJECT_FIELD_VALUES = "Delete Specific Project Field Values"
    IMPORT_PROJECT_FIELD_VALUES = "Import Project Field Values"
    UPDATE_PROJECT_FIELDS = "Update Project Fields"
    # deprecated in favor of UPDATE_PROJECT_FIELDS
    UPDATE_PROJECT_FIELDS_FARM_ID = "Update Project Fields Farm ID"

    # custom inputs
    GET_CUSTOM_INPUTS = "Get Custom Inputs"
    UPDATE_CUSTOM_INPUTS = "Update Custom Inputs"
    CREATE_CUSTOM_INPUTS = "Create Custom Inputs"
    DELETE_CUSTOM_INPUTS = "Delete Custom Inputs"

    # project values
    GET_PROJECT_VALUES = "Get Project Values"
    UPDATE_PROJECT_VALUES = "Update Project Values"
    CREATE_PROJECT_VALUES = "Create Project Values"
    DELETE_PROJECT_VALUES = "Delete Project Values"

    # values templates (e.g. nutrient_management_templates)
    CREATE_VALUES_TEMPLATES = "Create Values Templates"
    GET_VALUES_TEMPLATES = "Get Values Templates"
    UPDATE_VALUES_TEMPLATES = "Update Values Templates"
    DELETE_VALUES_TEMPLATES = "Delete Values Templates"

    # optis labels mapping
    GET_OPTIS_LABELS_MAPPING = "Get OPTIS Labels Mapping"
    CREATE_OPTIS_LABELS_MAPPING = "Create OPTIS Labels Mapping"

    # conflicts
    GET_CONFLICTS = "Get Conflicts"
    CREATE_CONFLICTS = "Create Conflicts"
    GET_CONFLICT_RESOLUTION = "Get Conflict Resolution"
    CREATE_CONFLICT_RESOLUTION = "Create Conflict Resolution"
    GET_CONFLICT_RESOLUTION_FILES = "Get Conflict Resolution Files"
    UPDATE_CONFLICT_RESOLUTION_FILES = "Update Conflict Resolution Files"
    CREATE_CONFLICT_RESOLUTION_FILES = "Create Conflict Resolution Files"
    DELETE_CONFLICT_RESOLUTION_FILES = "Delete Conflict Resolution Files"
    GET_CONFLICTS_CSV = "Get Conflicts CSV"

    # Verification Dashboard
    GET_VERIFICATION_DASHBOARDS = "Get Verification Dashboards"
    CREATE_VERIFICATION_DASHBOARDS = "Create Verification Dashboards"
    UPDATE_VERIFICATION_DASHBOARDS = "Update Verification Dashboards"
    DELETE_VERIFICATION_DASHBOARDS = "Delete Verification Dashboards"
    UPDATE_VERIFICATION_DASHBOARD_CONFLICT_TYPE = "Update Verification Dashboard Conflict Type"
    UPDATE_VERIFICATION_DASHBOARD_STAT_FIELD = "Update Verification Dashboard Stat Field"
    UPDATE_VERIFICATION_DASHBOARD_TABLE_FIELD = "Update Verification Dashboard Table Field"

    # units options
    GET_UNITS_OPTIONS = "Get Units Options"

    # entities values
    GET_PROJECT_ENTITIES_VALUES = "Get Project Entities Values"
    UPDATE_PROJECT_ENTITIES_VALUES = "Update Project Entities Values"
    DELETE_PROJECT_ENTITIES_VALUES = "Delete Project Entities Values"

    # reporting dashboards
    CREATE_PROGRAM_REPORTING_DASHBOARDS_CSV = "Create Program Reporting Dashboards CSV"
    GET_REPORTING_DASHBOARDS_CHART_TYPES = "Get Reporting Dashboards Chart Types"
    GET_REPORTING_DASHBOARDS_FULL = "Get Reporting Dashboards Full"
    CREATE_REPORTING_DASHBOARDS_FULL = "Create Reporting Dashboards Full"
    UPDATE_REPORTING_DASHBOARDS_FULL = "Update Reporting Dashboards Full"
    DELETE_REPORTING_DASHBOARDS_FULL = "Delete Reporting Dashboards Full"
    UPDATE_REPORTING_DASHBOARDS_FULL_BY_ID = "Update Reporting Dashboards Full by ID"
    UPDATE_REPORTING_DASHBOARD_BY_ID = "Update Reporting Dashboard by ID"
    GET_PROGRAM_REPORTING_DASHBOARDS_FULL = "Get Program Reporting Dashboards Full"
    GET_PROGRAM_REPORTING_DASHBOARDS_FULL_BY_ID = "Get Program Reporting Dashboards Full by ID"
    UPDATE_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM_BY_ID = "Update Program Reporting Dashboard to Program by ID"
    GET_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM = "Get Program Reporting Dashboard to Program"
    CREATE_PROGRAM_REPORTING_DASHBOARD_TO_PROGRAM = "Create Program Reporting Dashboard to Program"
    GET_PROGRAM_REPORTING_DASHBOARD_FULL_KEY_BY_KEY = "Get Program Reporting Dashboard Full Key by Key"
    VIEW_REPORTING_DASHBOARDS = "View Reporting Dashboards"
    CREATE_REPORTING_DASHBOARDS = "Create Reporting Dashboards"
    DELETE_REPORTING_DASHBOARDS = "Delete Reporting Dashboards"
    CREATE_REPORTING_DASHBOARD_SECTIONS = "Create Reporting Dashboard Sections"
    GET_REPORTING_DASHBOARD_SECTION_BY_ID = "Get Reporting Dashboard Section by ID"
    GET_REPORTING_DASHBOARD_SECTIONS = "Get Reporting Dashboard Sections"
    UPDATE_REPORTING_DASHBOARD_SECTION = "Update Reporting Dashboard Section"
    CREATE_REPORTING_DASHBOARD_SECTION_ROWS = "Create Reporting Dashboard Section Rows"
    GET_REPORTING_DASHBOARD_SECTION_ROW_BY_ID = "Get Reporting Dashboard Section Row by ID"
    GET_REPORTING_DASHBOARD_SECTION_ROWS = "Get Reporting Dashboard Section Rows"
    UPDATE_REPORTING_DASHBOARD_SECTION_ROW = "Update Reporting Dashboard Section Row"
    CREATE_REPORTING_DASHBOARD_CHART_PRESETS = "Create Reporting Dashboard Chart Presets"
    GET_REPORTING_DASHBOARD_CHART_PRESET_BY_ID = "Get Reporting Dashboard Chart Preset by ID"
    GET_REPORTING_DASHBOARD_CHART_PRESETS = "Get Reporting Dashboard Chart Presets"
    UPDATE_REPORTING_DASHBOARD_CHART_PRESET = "Update Reporting Dashboard Chart Preset"
    GET_REPORTING_DASHBOARD_CHART_INSTANCE_BY_ID = "Get Reporting Dashboard Chart Instance by ID"
    UPDATE_REPORTING_DASHBOARD_CHART_INSTANCE = "Update Reporting Dashboard Chart Instance"
    DELETE_REPORTING_DASHBOARD_CHART_INSTANCES = "Delete Reporting Dashboard Chart Instances"
    CREATE_REPORTING_DASHBOARD_CHART_INSTANCES = "Create Reporting Dashboard Chart Instances"
    DELETE_REPORTING_DASHBOARD_CHART_PRESETS = "Delete Reporting Dashboard Chart Presets"
    DELETE_REPORTING_DASHBOARD_SECTION_ROWS = "Delete Reporting Dashboard Section Rows"
    DELETE_REPORTING_DASHBOARD_SECTIONS = "Delete Reporting Dashboard Sections"

    # mobs
    GET_PROJECT_MOB_BY_ID = "Get Mob by ID"
    UPDATE_PROJECT_MOB_BY_ID = "Update Mob by ID"
    GET_PROJECT_MOBS = "Get Project Mobs"
    CREATE_PROJECT_MOBS = "Create Project Mobs"
    DELETE_PROJECT_MOBS = "Delete Project Mobs"

    # program codes
    CREATE_ENROL_PROGRAM_CODES = "Create Enrol Program Codes"

    # farm
    GET_PROJECT_FARM = "Get Project Farm"
    GET_PROJECT_FARMS = "Get Project Farms"
    CREATE_PROJECT_FARMS = "Create Project Farms"
    UPDATE_PROJECT_FARM = "Update Project Farm"
    DELETE_PROJECT_FARMS = "Delete Project Farms"

    # Boundary Rule Configs
    CREATE_BOUNDARY_RULE_CONFIG = "Create boundary rule config"
    GET_BOUNDARY_RULE_CONFIGS = "Get boundary rule configs"
    UPDATE_BOUNDARY_RULE_CONFIG = "Update boundary rule config"
    DELETE_BOUNDARY_RULE_CONFIG = "Delete boundary rule config"

    GET_USER_RESTRICTION = "Get User Restriction"
    CREATE_USER_RESTRICTION = "Create User Restriction"
    UPDATE_USER_RESTRICTION = "Update User Restriction"
    DELETE_USER_RESTRICTION = "Delete User Restriction"

    # Core user management
    DELETE_USER = "Delete User"
    CREATE_SUPER_ADMIN = "Create Super Admin"

    # Structured Events
    CREATE_PROJECT_FIELD_EVENTS = "Create Project Field Events"
    GET_PROJECT_FIELD_EVENTS = "Get Project Field Events"
    UPDATE_PROJECT_FIELD_EVENTS = "Update Project Field Events"
    DELETE_PROJECT_FIELD_EVENTS = "Delete Project Field Events"


def upgrade():
    session = Session(bind=op.get_bind())
    update_permissions_enum_type(op, existing_type=sa.Enum(PreviousPermission), new_type=sa.Enum(Permission))
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
    update_permissions_enum_type(op, existing_type=sa.Enum(Permission), new_type=sa.Enum(PreviousPermission))
