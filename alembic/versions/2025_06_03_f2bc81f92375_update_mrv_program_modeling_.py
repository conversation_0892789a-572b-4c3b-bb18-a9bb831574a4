"""update mrv_program_modeling_configuration.apply_ghg_per_acre_bounds default

Revision ID: f2bc81f92375
Revises: 43b7c12281ab
Create Date: 2025-06-03 19:54:18.292026

"""

import sqlalchemy as sa

from alembic import op

revision = "f2bc81f92375"
down_revision = "d73ebe79a4f8"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_program_modeling_configuration",
        "apply_ghg_per_acre_bounds",
        existing_type=sa.Boolean(),
        nullable=False,
        server_default="1",
    )
    op.execute(
        "UPDATE mrv_program_modeling_configuration SET apply_ghg_per_acre_bounds = true WHERE program_id != 1649;"
    )


def downgrade():
    # column alteration is idempotent, so no downgrade needed
    pass
