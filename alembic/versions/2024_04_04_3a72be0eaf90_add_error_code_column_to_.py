"""Add error_code column to DndcSimulationRequests table.

Revision ID: 3a72be0eaf90
Revises: 6e68b1684f41
Create Date: 2024-04-04 12:21:53.940852

"""

import enum

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3a72be0eaf90"
down_revision = "04414ba82d76"
branch_labels = None
depends_on = None


class ScenariosServiceIntegrationErrorCode(enum.StrEnum):
    unsupported_crop = enum.auto()
    unsupported_product_type = enum.auto()
    scenarios_service_unavailable = enum.auto()
    boundaries_service_unavailable = enum.auto()
    invalid_management_data = enum.auto()
    overlapping_commodity_crop_periods = enum.auto()
    missing_baseline_year = enum.auto()
    generic_scenarios_service_failure = enum.auto()


def upgrade():
    op.add_column(
        "mrv_dndc_simulation_requests",
        sa.Column("error_code", sa.Enum(ScenariosServiceIntegrationErrorCode), nullable=True),
    )


def downgrade():
    op.drop_column("mrv_dndc_simulation_requests", "error_code")
