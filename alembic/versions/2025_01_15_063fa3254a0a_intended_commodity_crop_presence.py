"""intended-commodity-crop-presence

Revision ID: 063fa3254a0a
Revises: a77595ef9af9
Create Date: 2025-01-15 16:02:01.357605

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "063fa3254a0a"
down_revision = "a77595ef9af9"
branch_labels = None
depends_on = None


def upgrade():
    # update column to enum nullable
    op.alter_column(
        "mrv_cover_crop_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_grazing_interventions_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_rate_reduction_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_split_application_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_timing_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_rice_irrigation_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )
    op.alter_column(
        "mrv_tillage_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        type_=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        nullable=True,
    )

    # handle empty values
    op.execute(
        "UPDATE mrv_cover_crop_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute(
        "UPDATE mrv_grazing_interventions_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute("UPDATE mrv_nm_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''")
    op.execute(
        "UPDATE mrv_nm_rate_reduction_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute(
        "UPDATE mrv_nm_split_application_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute(
        "UPDATE mrv_nm_timing_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute(
        "UPDATE mrv_rice_irrigation_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )
    op.execute(
        "UPDATE mrv_tillage_eligibility_config SET commodity_crops_check = NULL where commodity_crops_check = ''"
    )


def downgrade():
    # update column to boolean nullable
    op.alter_column(
        "mrv_tillage_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_rice_irrigation_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_timing_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_split_application_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_rate_reduction_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_nm_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_grazing_interventions_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )
    op.alter_column(
        "mrv_cover_crop_eligibility_config",
        "commodity_crops_check",
        existing_type=sa.Enum("FILTER_BASELINE", "PRESENT_IN_HISTORY", name="commoditycropcheckchoice"),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=True,
    )

    # handle NULL values
    op.execute(
        "UPDATE mrv_cover_crop_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute(
        "UPDATE mrv_grazing_interventions_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute("UPDATE mrv_nm_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL")
    op.execute(
        "UPDATE mrv_nm_rate_reduction_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute(
        "UPDATE mrv_nm_split_application_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute(
        "UPDATE mrv_nm_timing_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute(
        "UPDATE mrv_rice_irrigation_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )
    op.execute(
        "UPDATE mrv_tillage_eligibility_config SET commodity_crops_check = 0 where commodity_crops_check is NULL"
    )

    # update column to boolean non-nullable
    op.alter_column(
        "mrv_tillage_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_rice_irrigation_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_nm_timing_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_nm_split_application_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_nm_rate_reduction_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_nm_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_grazing_interventions_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
    op.alter_column(
        "mrv_cover_crop_eligibility_config",
        "commodity_crops_check",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=sa.text("'0'"),
        type_=mysql.TINYINT(display_width=1),
        nullable=False,
    )
