"""add notes to history

Revision ID: 8bdcc9c93292
Revises: 33c70cb6530a
Create Date: 2024-08-05 11:06:12.140527

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8bdcc9c93292"
down_revision = "33c70cb6530a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflict_resolution_history", sa.Column("old_notes", sa.Text(), nullable=True))
    op.add_column("mrv_conflict_resolution_history", sa.Column("new_notes", sa.Text(), nullable=True))
    op.create_index(op.f("ix_mrv_conflicts_practice_type"), "mrv_conflicts", ["practice_type"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_conflicts_practice_type"), table_name="mrv_conflicts")
    op.drop_column("mrv_conflict_resolution_history", "new_notes")
    op.drop_column("mrv_conflict_resolution_history", "old_notes")
    # ### end Alembic commands ###
