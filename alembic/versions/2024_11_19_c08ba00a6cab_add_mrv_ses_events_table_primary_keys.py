"""add mrv_ses_events table primary keys

Revision ID: c08ba00a6cab
Revises: bd40197309e6
Create Date: 2024-11-19 09:55:54.291758

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c08ba00a6cab"
down_revision = "bd40197309e6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_ses_events",
        sa.Column("ses_event_id", sa.String(length=36), nullable=False),
        sa.Column("mrv_phase_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("revision", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["mrv_phase_id"],
            ["mrv_phases.id"],
        ),
        sa.PrimaryKeyConstraint("ses_event_id", "mrv_phase_id"),
        sa.UniqueConstraint("ses_event_id", "mrv_phase_id", name="_ses_id_phase_id_uc"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("mrv_ses_events")
    # ### end Alembic commands ###
