"""add stage-field completion table

Revision ID: 95b0561c14ee
Revises: 53ac3a6a701d
Create Date: 2025-03-07 14:18:20.472032

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "95b0561c14ee"
down_revision = "53ac3a6a701d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_field_stage_completion",
        sa.<PERSON>n("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("stage_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("field_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn(
            "field_completion_status",
            sa.<PERSON>um("complete", "incomplete", "undetermined", name="narrowestcompletionstatus"),
            nullable=False,
        ),
        sa.<PERSON>KeyConstraint("id"),
        sa.UniqueConstraint("stage_id", "field_id", name="_field_stage_completion_unique_field_stage"),
    )
    op.create_index(
        op.f("ix_mrv_field_stage_completion_field_id"), "mrv_field_stage_completion", ["field_id"], unique=False
    )
    op.create_index(op.f("ix_mrv_field_stage_completion_id"), "mrv_field_stage_completion", ["id"], unique=False)
    op.create_index(
        op.f("ix_mrv_field_stage_completion_stage_id"), "mrv_field_stage_completion", ["stage_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_mrv_field_stage_completion_stage_id"), table_name="mrv_field_stage_completion")
    op.drop_index(op.f("ix_mrv_field_stage_completion_id"), table_name="mrv_field_stage_completion")
    op.drop_index(op.f("ix_mrv_field_stage_completion_field_id"), table_name="mrv_field_stage_completion")
    op.drop_table("mrv_field_stage_completion")
