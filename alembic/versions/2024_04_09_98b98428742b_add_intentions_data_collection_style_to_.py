"""Add intentions_data_collection_style to program_modeling_configurations so we know whether to use intended practices or intended events.

Revision ID: 98b98428742b
Revises: 87fcec7e9194
Create Date: 2024-04-09 16:22:18.141579

"""

import enum

import sqlalchemy as sa

from alembic import op


class IntentionsDataCollectionStyle(enum.StrEnum):
    PRACTICES = enum.auto()
    EVENTS = enum.auto()


# revision identifiers, used by Alembic.
revision = "98b98428742b"
down_revision = "8ae3ad2c655b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_program_modeling_configuration",
        sa.Column("intentions_data_collection_style", sa.Enum(IntentionsDataCollectionStyle), nullable=True),
    )
    try:
        op.execute("UPDATE mrv_program_modeling_configuration SET intentions_data_collection_style='EVENTS'")
        # Regenconnect US is currently the only program we want to send practices for
        op.execute(
            "UPDATE mrv_program_modeling_configuration SET intentions_data_collection_style='PRACTICES' WHERE program_id in (21, 68, 155)"
        )
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e
    op.alter_column(
        "mrv_program_modeling_configuration",
        "intentions_data_collection_style",
        nullable=False,
        existing_nullable=True,
        existing_type=sa.Enum(IntentionsDataCollectionStyle),
    )


def downgrade():
    op.drop_column("mrv_program_modeling_configuration", "intentions_data_collection_style")
