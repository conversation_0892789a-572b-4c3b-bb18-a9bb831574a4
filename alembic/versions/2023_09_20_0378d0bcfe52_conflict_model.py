"""conflict model

Revision ID: 0378d0bcfe52
Revises: 7f0b3b35d359
Create Date: 2023-09-21 17:24:06.509342

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "0378d0bcfe52"
down_revision = "7f0b3b35d359"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_conflict_practice_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("stage_id", sa.Integer(), nullable=False),
        sa.Column("years", sa.JSO<PERSON>(), nullable=False),
        sa.Column(
            "practice_type",
            sa.Enum(
                "TILLAGE",
                "COVER_CROP",
                "IRRIGATION",
                "COMMODITY_CROP",
                name="practicetypes",
            ),
            nullable=False,
        ),
        sa.Column("comparison_attr_id", sa.Integer(), nullable=False),
        sa.Column("measurement_attr_id", sa.Integer(), nullable=False),
        sa.Column("start_date_attr_id", sa.Integer(), nullable=False),
        sa.Column("end_date_attr_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["comparison_attr_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["end_date_attr_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["measurement_attr_id"],
            ["mrv_attributes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["stage_id"],
            ["mrv_stages.id"],
        ),
        sa.ForeignKeyConstraint(
            ["start_date_attr_id"],
            ["mrv_attributes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_conflict_practice_config_id"),
        "mrv_conflict_practice_config",
        ["id"],
        unique=False,
    )
    op.drop_index("ix_mrv_conflict_http_requests_id", table_name="mrv_conflict_http_requests")
    op.drop_table("mrv_conflict_http_requests")
    op.add_column(
        "mrv_conflicts",
        sa.Column(
            "status",
            sa.Enum("NEEDS_REVIEW", "IN_PROGRESS", "DONE", name="projectconflictstatus"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_conflicts", "status")
    op.create_table(
        "mrv_conflict_http_requests",
        sa.Column("id", mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column(
            "conflict_result_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "http_request_id",
            mysql.INTEGER(display_width=11),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "is_deleted",
            mysql.TINYINT(display_width=1),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["conflict_result_id"],
            ["mrv_conflict_results.id"],
            name="mrv_conflict_http_requests_ibfk_1",
        ),
        sa.ForeignKeyConstraint(
            ["http_request_id"],
            ["mrv_http_requests.id"],
            name="mrv_conflict_http_requests_ibfk_2",
        ),
        sa.PrimaryKeyConstraint("id"),
        mysql_default_charset="latin1",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_mrv_conflict_http_requests_id",
        "mrv_conflict_http_requests",
        ["id"],
        unique=False,
    )
    op.drop_index(
        op.f("ix_mrv_conflict_practice_config_id"),
        table_name="mrv_conflict_practice_config",
    )
    op.drop_table("mrv_conflict_practice_config")
    # ### end Alembic commands ###
