"""custom-eligibility-preserved-baseline

Revision ID: b5e59d689443
Revises: 063fa3254a0a
Create Date: 2025-01-16 18:33:40.516542

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b5e59d689443"
down_revision = "063fa3254a0a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_stage_eligibility_config",
        sa.Column("preserved_baseline", sa.<PERSON>(), server_default="0", nullable=False),
    )


def downgrade():
    op.drop_column("mrv_stage_eligibility_config", "preserved_baseline")
