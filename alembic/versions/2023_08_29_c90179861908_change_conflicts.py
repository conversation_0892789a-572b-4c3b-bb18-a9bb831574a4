"""Change conflicts

Revision ID: c90179861908
Revises: 3b9d0407944d
Create Date: 2023-08-29 09:03:38.798019

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "c90179861908"
down_revision = "3b9d0407944d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("mrv_conflict_resolution_history_ibfk_2", "mrv_conflict_resolution_history", type_="foreignkey")
    op.drop_index("ix_mrv_conflict_resolution_history_value_id", table_name="mrv_conflict_resolution_history")
    op.drop_column("mrv_conflict_resolution_history", "value_id")
    op.add_column("mrv_conflicts", sa.Column("measurement_value_id", sa.Integer(), nullable=True))
    op.drop_constraint("mrv_conflicts_ibfk_7", "mrv_conflicts", type_="foreignkey")
    op.create_foreign_key(None, "mrv_conflicts", "mrv_values", ["measurement_value_id"], ["id"])
    op.drop_column("mrv_conflicts", "measurement_value")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_conflicts",
        sa.Column("measurement_value", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "mrv_conflicts", type_="foreignkey")
    op.create_foreign_key("mrv_conflicts_ibfk_7", "mrv_conflicts", "mrv_values", ["measurement_value"], ["id"])
    op.drop_column("mrv_conflicts", "measurement_value_id")
    op.add_column(
        "mrv_conflict_resolution_history",
        sa.Column("value_id", mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "mrv_conflict_resolution_history_ibfk_2", "mrv_conflict_resolution_history", "mrv_values", ["value_id"], ["id"]
    )
    op.create_index(
        "ix_mrv_conflict_resolution_history_value_id", "mrv_conflict_resolution_history", ["value_id"], unique=False
    )
    # ### end Alembic commands ###
