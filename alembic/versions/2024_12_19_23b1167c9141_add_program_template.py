"""Add program_template

Revision ID: 23b1167c9141
Revises: 6ed549e6a190
Create Date: 2024-12-19 14:19:46.208068

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "23b1167c9141"
down_revision = "6ed549e6a190"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_programs",
        sa.Column(
            "program_template",
            sa.Enum("legacy", "event_based", name="programtemplate"),
            server_default="legacy",
            nullable=False,
            comment="template used when program was created",
        ),
    )


def downgrade():
    op.drop_column("mrv_programs", "program_template")
