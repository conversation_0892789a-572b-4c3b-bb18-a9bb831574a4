"""add-permission-to-get-and-patch-project-user

Revision ID: f8baf10b061a
Revises: 684ad086e89a
Create Date: 2024-06-12 16:28:32.478056

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission

# revision identifiers, used by Alembic.
revision = "f8baf10b061a"
down_revision = "684ad086e89a"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PRODUCER.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.PROGRAM_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
    },
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {
        Permission.GET_PROJECT_USER.value,
        Permission.PATCH_PROJECT_USER.value,
    },
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
