"""remove-previous-commodity-crop-from-conflicts

Revision ID: 73c291fd7e70
Revises: 18260dccad86
Create Date: 2024-10-08 16:56:05.828628

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.engine.reflection import Inspector

from alembic import op

# revision identifiers, used by Alembic.
revision = "73c291fd7e70"
down_revision = "18260dccad86"
branch_labels = None
depends_on = None


def column_exists(table_name, column_name, conn):
    try:
        inspector = Inspector.from_engine(conn)
        columns = [col["name"] for col in inspector.get_columns(table_name)]
        return column_name in columns
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def upgrade():
    conn = op.get_bind()
    if column_exists("mrv_conflicts", "previous_commodity_crop", conn):
        op.drop_constraint("conflicts_previous_commodity_value_fk", "mrv_conflicts", type_="foreignkey")
        op.drop_column("mrv_conflicts", "previous_commodity_crop")


def downgrade():
    conn = op.get_bind()
    if not column_exists("mrv_conflicts", "previous_commodity_crop", conn):
        op.add_column(
            "mrv_conflicts", sa.Column("previous_commodity_crop", mysql.INTEGER(), autoincrement=False, nullable=True)
        )
        op.create_foreign_key(
            "conflicts_previous_commodity_value_fk", "mrv_conflicts", "mrv_values", ["previous_commodity_crop"], ["id"]
        )
