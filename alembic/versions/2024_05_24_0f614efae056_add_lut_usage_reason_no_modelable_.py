"""add-lut-usage-reason-no-modelable-practice

Revision ID: 0f614efae056
Revises: a94f7bb921ca
Create Date: 2024-05-24 13:48:55.604713

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "0f614efae056"
down_revision = "a94f7bb921ca"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=mysql.ENUM(
            "explore_result_missing",
            "ghg_too_low",
            "ghg_too_high",
            "mrv_couldnt_build_ss_input",
            "ss_error",
            "generic_error",
        ),
        type_=sa.Enum(
            "explore_result_missing",
            "ghg_too_low",
            "ghg_too_high",
            "mrv_couldnt_build_ss_input",
            "ss_error",
            "generic_error",
            "no_modelable_practice",
            name="lutusagereasons",
        ),
        existing_nullable=True,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_dndc_results",
        "lut_usage_reason",
        existing_type=sa.Enum(
            "explore_result_missing",
            "ghg_too_low",
            "ghg_too_high",
            "mrv_couldnt_build_ss_input",
            "ss_error",
            "generic_error",
            "no_modelable_practice",
            name="lutusagereasons",
        ),
        type_=mysql.ENUM(
            "explore_result_missing",
            "ghg_too_low",
            "ghg_too_high",
            "mrv_couldnt_build_ss_input",
            "ss_error",
            "generic_error",
        ),
        existing_nullable=True,
    )
