"""create inventory outcomes

Revision ID: 3d2b1238c444
Revises: b4e2a5534c9a
Create Date: 2025-01-08 11:25:37.889513

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3d2b1238c444"
down_revision = "b4e2a5534c9a"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_crop_level_inventory_outcomes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("crop_type", sa.String(length=32), nullable=False),
        sa.Column("total_yield", sa.Float(), nullable=True),
        sa.Column("number_of_fields", sa.Integer(), nullable=True),
        sa.Column("mapped_acres", sa.Float(), nullable=True),
        sa.Column("total_emissions", sa.Float(), nullable=True),
        sa.Column("total_emissions_per_bushel", sa.Float(), nullable=True),
        sa.Column("indirect_n2o_emissions", sa.Float(), nullable=True),
        sa.Column("direct_n2o_emissions", sa.Float(), nullable=True),
        sa.Column("soil_ch4_emissions", sa.Float(), nullable=True),
        sa.Column("soc", sa.Float(), nullable=True),
        sa.Column("soc_per_bushel", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_crop_level_inventory_outcomes_id"), "mrv_crop_level_inventory_outcomes", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_crop_level_inventory_outcomes_program_id"),
        "mrv_crop_level_inventory_outcomes",
        ["program_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_crop_level_inventory_outcomes_updated_at"),
        "mrv_crop_level_inventory_outcomes",
        ["updated_at"],
        unique=False,
    )

    op.create_table(
        "mrv_field_level_inventory_outcomes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("crop_name", sa.String(length=32), nullable=True),
        sa.Column("mapped_acres", sa.Float(), nullable=True),
        sa.Column("total_yield", sa.Float(), nullable=True),
        sa.Column("project_level_net_emissions", sa.Float(), nullable=True),
        sa.Column("field_ghg_emissions", sa.Float(), nullable=True),
        sa.Column("field_emission_project_percentage", sa.Float(), nullable=True),
        sa.Column("total_n2o_emissions", sa.Float(), nullable=True),
        sa.Column("field_total_n2o_project_percentage", sa.Float(), nullable=True),
        sa.Column("indirect_n2o_emissions", sa.Float(), nullable=True),
        sa.Column("field_indirect_n2o_project_percentage", sa.Float(), nullable=True),
        sa.Column("direct_n2o_emissions", sa.Float(), nullable=True),
        sa.Column("field_direct_n2o_project_percentage", sa.Float(), nullable=True),
        sa.Column("soil_ch4_emissions", sa.Float(), nullable=True),
        sa.Column("field_ch4_project_percentage", sa.Float(), nullable=True),
        sa.Column("soc", sa.Float(), nullable=True),
        sa.Column("field_soc_project_percentage", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("task_id", sa.String(length=36), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_field_level_inventory_outcomes_id"), "mrv_field_level_inventory_outcomes", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_mrv_field_level_inventory_outcomes_program_id"),
        "mrv_field_level_inventory_outcomes",
        ["program_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_mrv_field_level_inventory_outcomes_updated_at"),
        "mrv_field_level_inventory_outcomes",
        ["updated_at"],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f("ix_mrv_field_level_inventory_outcomes_updated_at"), table_name="mrv_field_level_inventory_outcomes"
    )
    op.drop_index(
        op.f("ix_mrv_field_level_inventory_outcomes_program_id"), table_name="mrv_field_level_inventory_outcomes"
    )
    op.drop_index(op.f("ix_mrv_field_level_inventory_outcomes_id"), table_name="mrv_field_level_inventory_outcomes")
    op.drop_table("mrv_field_level_inventory_outcomes")

    op.drop_index(
        op.f("ix_mrv_crop_level_inventory_outcomes_updated_at"), table_name="mrv_crop_level_inventory_outcomes"
    )
    op.drop_index(
        op.f("ix_mrv_crop_level_inventory_outcomes_program_id"), table_name="mrv_crop_level_inventory_outcomes"
    )
    op.drop_index(op.f("ix_mrv_crop_level_inventory_outcomes_id"), table_name="mrv_crop_level_inventory_outcomes")
    op.drop_table("mrv_crop_level_inventory_outcomes")
