"""add export run

Revision ID: cf5c239d76cd
Revises: 3d210e1d817f
Create Date: 2024-07-04 02:39:48.438155

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "cf5c239d76cd"
down_revision = "3d210e1d817f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_program_export_runs",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("program_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn(
            "export_run_status",
            sa.<PERSON>um("IN_PROGRESS", "COMPLETED", "FAILED", name="exportrunstatus"),
            nullable=False,
            comment="The status of the export run. Only one export per program is allowed to be in progress at a time.",
        ),
        sa.Column("query_params", sa.JSO<PERSON>(), nullable=False),
        sa.Column("user", sa.Integer(), nullable=False),
        sa.Column("failure_reason", sa.Text(), nullable=True, comment="The reason for the failure of the export run."),
        sa.Column("gcs_object", sa.Text(), nullable=True, comment="The GCS object of the exported file."),
        sa.Column("created_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_at", sa.TIMESTAMP(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["mrv_programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="This table stores the export runs for the program.Each run is a snapshot of the program data at the time of export.Only one export is allowed to run at a time for a program.",
    )
    op.create_index(op.f("ix_mrv_program_export_runs_id"), "mrv_program_export_runs", ["id"], unique=False)
    op.add_column("mrv_notifications", sa.Column("user", sa.Integer(), nullable=True))
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=mysql.ENUM("OPTIS", "DNDC", "MRV"),
        type_=sa.Enum("OPTIS", "DNDC", "MRV", "MRV_EXPORT", name="notificationsources"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_notifications",
        "source",
        existing_type=sa.Enum("OPTIS", "DNDC", "MRV", "MRV_EXPORT", name="notificationsources"),
        type_=mysql.ENUM("OPTIS", "DNDC", "MRV"),
        existing_nullable=False,
    )
    op.drop_column("mrv_notifications", "user")
    op.drop_index(op.f("ix_mrv_program_export_runs_id"), table_name="mrv_program_export_runs")
    op.drop_table("mrv_program_export_runs")
    # ### end Alembic commands ###
