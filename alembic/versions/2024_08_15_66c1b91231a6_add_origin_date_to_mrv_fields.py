"""add origin_date to mrv_fields

Revision ID: 66c1b91231a6
Revises: b771459cbf48
Create Date: 2024-08-15 10:32:22.255029

"""

import sqlalchemy as sa

from alembic import op
from fields.model import Fields

# revision identifiers, used by Alembic.
revision = "66c1b91231a6"
down_revision = "b771459cbf48"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mrv_fields",
        sa.Column("origin_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
    )
    op.create_index(op.f("ix_mrv_fields_origin_date"), "mrv_fields", ["origin_date"], unique=False)
    op.execute(sa.update(Fields).values(updated_at=Fields.updated_at, origin_date=Fields.created_at))


def downgrade():
    op.drop_index(op.f("ix_mrv_fields_origin_date"), table_name="mrv_fields")
    op.drop_column("mrv_fields", "origin_date")
