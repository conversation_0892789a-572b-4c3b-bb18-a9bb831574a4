"""Add mrv_explore_results table.

Revision ID: 7fbbbd1a1657
Revises: c54cb25f8657
Create Date: 2024-03-25 17:25:38.970205

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7fbbbd1a1657"
down_revision = "6d4ff8c76b18"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mrv_explore_results",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("simulation_request_id", sa.Integer(), nullable=False),
        sa.Column("total_emissions_reductions_estimate", sa.DECIMAL(precision=28, scale=14), nullable=True),
        sa.Column("is_error", sa.<PERSON>(), nullable=False),
        sa.Column("error_message", sa.String(length=200), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["mrv_fields.id"],
        ),
        sa.ForeignKeyConstraint(
            ["simulation_request_id"],
            ["mrv_dndc_simulation_requests.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mrv_explore_results_id"), "mrv_explore_results", ["id"], unique=False)
    op.create_index(op.f("ix_mrv_explore_results_field_id"), "mrv_explore_results", ["field_id"], unique=False)


def downgrade():
    op.drop_constraint("mrv_explore_results_ibfk_1", "mrv_explore_results", type_="foreignkey")
    op.drop_index(op.f("ix_mrv_explore_results_field_id"), table_name="mrv_explore_results")
    op.drop_index(op.f("ix_mrv_explore_results_id"), table_name="mrv_explore_results")
    op.drop_table("mrv_explore_results")
