"""Add authoritative list of attribute option defaults

Revision ID: e6d30728da26
Revises: a502a736993c
Create Date: 2023-08-15 14:39:33.008318

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e6d30728da26"
down_revision = "a502a736993c"
branch_labels = None
depends_on = None

bulk_insert = {
    "practice": [],
    "spring_tillage_practice": ["conventional till", "reduced till", "no till"],
    "summer_crop_type": [1, 2, 3, 36, 5, 37, 4, 6, 41, 42, 43, 12, 13, 23, 25, 61],
    "fall_tillage_practice": ["conventional till", "reduced till", "no till"],
    "winter_crop_commitment": [
        224,
        1,
        0,
        39,
        44,
        205,
        52,
        53,
        246,
        247,
        24,
        58,
        27,
        28,
    ],
    "winter_planting_date": [],
    "tillage_practice": ["conventional till", "reduced till", "no till"],
    "tillage_period": ["Fall", "Spring"],
    "tillage_date": [],
    "summer_harvest_date": [],
    "summer_dry_yield": [],
    "summer_residue_harvested": [
        "95%",
        "85%",
        "25%",
        "75%",
        "All residue harvested",
        "50%",
        "No residue harvested",
    ],
    "irrigation_method": [
        "Drip",
        "AWD",
        "No irrigation",
        "Pivot",
        "Flood",
        "Sprinkler",
        "Furrow",
        "Sub-surface drip",
        "Subsurface drip",
    ],
    "tillage_depth": [],
    "summer_planting_date": [],
    "winter_crop_type": [],
    "fall_tillage_date": [],
    "fall_tillage_depth": [],
    "record_year": [],
    "crop_type": [],
    "residue_harvested": [
        "95%",
        "85%",
        "25%",
        "75%",
        "All residue harvested",
        "50%",
        "No residue harvested",
    ],
    "planting_date": [],
    "application_product": [],
    "application_product_type": [],
    "application_date": [],
    "application_rate": [],
    "application_rate_unit": [
        "Pt/ac",
        "lb1ac-1",
        "mt1sqm-1",
        "Gal N/ac",
        "kg1ha-1",
        "l1sqm-1",
        "l1ha-1",
        "flo1ac-1",
        "kg1sqm-1",
        "nlb1ac-1",
        "Gal/ac",
        "gal1ac-1",
        "mt1ha-1",
        "Qt/ac",
        "nkg1sqm-1",
        "nkg1ha-1",
        "Lbs/ac",
        "Lbs N/ac",
        "pt1ac-1",
        "qt1ac-1",
        "Fl. oz/ac",
    ],
    "application_area": [],
    "application_depth": ["6-8 in", "1-3 in", "0 in", "3-6 in", "8+ in"],
    "application_method": ["Incorporated", "Injected", "Fertigation", "Broadcasted"],
    "application_rate_vanity": [],
    "application_rate_unit_vanity": [],
    "application_area_vanity": [],
    "yield_rate_unit": ["T/ac", "kg/ha", "bu/ac", "lb/ac", "t/ha"],
    "farm_number": [],
    "winter_crop_termination": [
        "Winterkill",
        "Grain Harvest",
        "Grazed",
        "Herbicide",
        "Tillage",
        "Mechanical",
        "Forage Harvest",
    ],
    "winter_harvest_date": [],
    "winter_dry_yield": [],
    "planting_season": ["summer", "winter"],
    "cover_crop_mix": ["Basic cover crop", "Commodity", "Premium cover crop"],
    "harvest_date": [],
    "winter_residue_harvested": [],
    "start_date": [],
    "end_date": [],
    "crop_yield": [],
    "soil_inversion": [],
    "irrigation_enabled": [],
    "irrigation_rate_unit": ["mm", "in", "mL/ha", "mm/ha", "oz/ac", "in/ac"],
    "fuel_type": ["Ethanol", "Gasoline", "Diesel", "Bio-diesel"],
    "energy_source": ["Main Grid", "Other Renewable", "Diesel", "Solar"],
    "spring_tillage_date": [],
    "spring_tillage_depth": [],
    "pesticide_farmprint_gaff": [
        "Insecticide",
        "Glyphosate",
        "Fungicide",
        "Pesticide",
        "Herbicide",
    ],
    "date": [],
    "change_type": ["Add", "Remove"],
    "head_count": [],
    "landscape_modifications_fuel_usage": [],
    "landscape_modifications_fuel_type": [
        "Ethanol",
        "Gasoline",
        "Diesel",
        "Bio-diesel",
    ],
    "irrigation_fuel_usage": [],
    "irrigation_fuel_type": ["Ethanol", "Gasoline", "Diesel", "Bio-diesel"],
    "irrigation_electricity_usage": [],
    "irrigation_electricity_source": [
        "Off-grid",
        "Alternative",
        "Main Grid",
        "Solar",
        "Alternative Renewable",
    ],
    "irrigation_electricity_grid_name": [],
    "area_sown": ["Not sown", "25%", "100%", "75%", "50%"],
    "residue_burnt": [],
    "area_tilled_for_pasture_renewal_pct": [],
    "nutrient_management_enabled": [],
    "area_tilled_total_pct": [],
    "farm_name": [],
    "total_area": [],
    "total_energy_usage": [],
    "total_fuel_usage": [],
    "tillage_event": [],
    "water_amount": [],
    "water_amount_unit": ["cm", "mm", "in", "gal"],
    "flood_pct": [],
    "additive_one": [
        "None",
        "agrotain",
        "anvol",
        "nutrisphere",
        "n_serve",
        "agrotain_plus",
    ],
    "additive_two": [
        "None",
        "agrotain",
        "anvol",
        "nutrisphere",
        "n_serve",
        "agrotain_plus",
    ],
    "crop_usage": [
        "Forage Harvest and Grazing",
        "Cover",
        "Forage Harvest",
        "Grazing",
        "Commodity",
    ],
    "strip_fraction": [],
    "livestock_class": [],
    "additives": [
        "agrotain",
        "anvol",
        "nutrisphere",
        "n_serve",
        "No additives",
        "agrotain_plus",
    ],
    "subsurface_drip_depth": [],
    "subsurface_drip_depth_unit": ["cm", "in"],
    "seeding_rate": [],
    "seeding_rate_unit": [
        "Kg per hectare",
        "Lbs per acre",
        "Seeds per hectare",
        "Seeds per acre",
    ],
    "seed_variety": [],
}


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_attribute_options_defaults",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "type",
            sa.Enum(
                "summer_crop_type",
                "summer_harvest_date",
                "summer_dry_yield",
                "summer_residue_harvested",
                "summer_planting_date",
                "winter_crop_commitment",
                "winter_crop_type",
                "winter_harvest_date",
                "winter_dry_yield",
                "winter_residue_harvested",
                "winter_planting_date",
                "fall_tillage_practice",
                "fall_tillage_date",
                "fall_tillage_depth",
                "spring_tillage_practice",
                "spring_tillage_date",
                "spring_tillage_depth",
                "planting_season",
                "planting_date",
                "harvest_date",
                "crop_usage",
                "other",
                "string",
                "number",
                "date",
                "bool_",
                "harvest",
                "planting",
                "crop_type",
                "crop_yield",
                "residue_harvested",
                "tillage_period",
                "tillage_practice",
                "tillage_date",
                "tillage_depth",
                "tillage_event",
                "strip_fraction",
                "soil_inversion",
                "spring_tillage",
                "fall_tillage",
                "winter_crop",
                "practice",
                "summer_crop",
                "record_year",
                "winter_crop_termination",
                "application_date",
                "application_product",
                "application_rate",
                "application_rate_unit",
                "application_area",
                "application_product_type",
                "application_depth",
                "application_method",
                "application_rate_unit_vanity",
                "application_product_type_vanity",
                "application_rate_vanity",
                "yield_rate_unit",
                "application_area_vanity",
                "water_amount",
                "water_amount_unit",
                "additive_one",
                "additive_two",
                "additives",
                "farm_number",
                "cover_crop_mix",
                "start_date",
                "end_date",
                "irrigation_method",
                "irrigation_enabled",
                "irrigation_rate_unit",
                "flood_pct",
                "subsurface_drip_depth",
                "subsurface_drip_depth_unit",
                "energy_source",
                "fuel_type",
                "pesticide_farmprint_gaff",
                "change_type",
                "head_count",
                "livestock_class",
                "farm_name",
                "area_sown",
                "residue_burnt",
                "area_tilled_total_pct",
                "area_tilled_for_pasture_renewal_pct",
                "nutrient_management_enabled",
                "landscape_modifications_fuel_usage",
                "landscape_modifications_fuel_type",
                "irrigation_fuel_usage",
                "irrigation_fuel_type",
                "irrigation_electricity_usage",
                "irrigation_electricity_source",
                "irrigation_electricity_grid_name",
                "total_area",
                "total_energy_usage",
                "total_fuel_usage",
                "skip_assign_practice",
                "seeding_rate",
                "seeding_rate_unit",
                "seed_variety",
                name="attributetypes",
            ),
            nullable=True,
        ),
        sa.Column("options", sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        comment="The `options` in mrv_attributes are per-program, and the existance in that list determines if enabled in that program. This table serves as an authoritative source for _all_ of those options, not just the ones that are enabled",
    )
    op.create_index(
        op.f("ix_mrv_attribute_options_defaults_id"), "mrv_attribute_options_defaults", ["id"], unique=False
    )
    # ### end Alembic commands ###

    # Insert data
    try:
        table = sa.Table(
            "mrv_attribute_options_defaults",
            sa.MetaData(),
            autoload_with=op.get_bind().engine,
        )

        data_to_insert = [{"type": type_in, "options": options} for type_in, options in bulk_insert.items()]
        op.bulk_insert(table, data_to_insert)
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mrv_attribute_options_defaults_id"), table_name="mrv_attribute_options_defaults")
    op.drop_table("mrv_attribute_options_defaults")
    # ### end Alembic commands ###
