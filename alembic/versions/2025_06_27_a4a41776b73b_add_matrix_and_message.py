"""add-matrix-and-message-to-eligibility-config

Revision ID: a4a41776b73b
Revises: cb27ff2f97c0
Create Date: 2025-06-27 19:16:40.588922

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a4a41776b73b"
down_revision = "cb27ff2f97c0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_stage_eligibility_config",
        sa.Column(
            "eligibility_matrix",
            sa.JSON(),
            nullable=True,
            comment="JSON representation of the EligibilityMatrix. If not set, follows our existing Must improve practice logic",
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_stage_eligibility_config", "eligibility_matrix")
    # ### end Alembic commands ###
