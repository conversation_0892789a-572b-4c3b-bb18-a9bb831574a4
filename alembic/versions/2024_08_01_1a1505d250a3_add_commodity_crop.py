"""add commodity crop

Revision ID: 1a1505d250a3
Revises: ddcbceed1e9c
Create Date: 2024-08-01 01:39:35.034421

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "1a1505d250a3"
down_revision = "ddcbceed1e9c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_conflicts", sa.Column("previous_commodity_crop", sa.String(length=255), nullable=True))
    op.add_column("mrv_conflicts", sa.Column("current_commodity_crop", sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_conflicts", "current_commodity_crop")
    op.drop_column("mrv_conflicts", "previous_commodity_crop")
    # ### end Alembic commands ###
