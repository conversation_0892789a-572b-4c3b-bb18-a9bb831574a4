"""Add reporting period to mrv programs

Revision ID: 7cd119863df7
Revises: c08ba00a6cab
Create Date: 2024-11-20 09:33:57.377937

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7cd119863df7"
down_revision = "c08ba00a6cab"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_programs", sa.Column("reporting_period_start_date", sa.TIMESTAMP(), nullable=True))
    op.add_column("mrv_programs", sa.Column("reporting_period_end_date", sa.TIMESTAMP(), nullable=True))
    op.execute(
        """UPDATE mrv_programs mp
        SET
            mp.reporting_period_start_date = STR_TO_DATE(CONCAT(mp.crediting_year + 1, '-01-01 00:00:00'), '%Y-%m-%d %H:%i:%s'),
            mp.reporting_period_end_date = STR_TO_DATE(CONCAT(mp.crediting_year + 1, '-12-31 23:59:59'), '%Y-%m-%d %H:%i:%s');"""
    )
    op.alter_column(
        table_name="mrv_programs",
        column_name="reporting_period_start_date",
        nullable=False,
        existing_type=sa.TIMESTAMP(),
    )
    op.alter_column(
        table_name="mrv_programs",
        column_name="reporting_period_end_date",
        nullable=False,
        existing_type=sa.TIMESTAMP(),
    )


def downgrade():
    op.drop_column("mrv_programs", "reporting_period_end_date")
    op.drop_column("mrv_programs", "reporting_period_start_date")
