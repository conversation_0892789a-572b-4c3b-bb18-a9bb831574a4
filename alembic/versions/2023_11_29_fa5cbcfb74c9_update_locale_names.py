"""update locale names

Revision ID: fa5cbcfb74c9
Revises: d8b370ad39c8
Create Date: 2023-11-29 15:49:06.172156

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "fa5cbcfb74c9"
down_revision = "d8b370ad39c8"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=mysql.ENUM(
            "en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HUNG"
        ),
        type_=sa.Enum(
            "en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HU", name="locale"
        ),
        existing_nullable=False,
    )


def downgrade():
    op.alter_column(
        "mrv_programs",
        "locale",
        existing_type=sa.Enum(
            "en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HU", name="locale"
        ),
        type_=mysql.ENUM("en_US", "en_GB", "fr", "pl_PL", "pt_BR", "ro_RO", "ru_RU", "de_DE", "vi_VN", "hu_HUNG"),
        existing_nullable=False,
    )
