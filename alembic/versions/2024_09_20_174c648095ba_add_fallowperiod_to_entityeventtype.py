"""Add FallowPeriod to EntityEventType

Revision ID: 174c648095ba
Revises: 09d576952cbd
Create Date: 2024-09-20 11:13:06.694315

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "174c648095ba"
down_revision = "09d576952cbd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_raw_field_events",
        "event_type",
        existing_type=mysql.ENUM(
            "APPLICATION_EVENT", "CROPPING_EVENT", "GRAZING_EVENT", "IRRIGATION_EVENT", "TILLAGE_EVENT"
        ),
        type_=sa.Enum(
            "APPLICATION_EVENT",
            "CROPPING_EVENT",
            "FALLOW_PERIOD",
            "GRAZING_EVENT",
            "IRRIGATION_EVENT",
            "TILLAGE_EVENT",
            name="entityeventtype",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_raw_field_events",
        "event_type",
        existing_type=sa.Enum(
            "APPLICATION_EVENT",
            "CROPPING_EVENT",
            "FALLOW_PERIOD",
            "GRAZING_EVENT",
            "IRRIGATION_EVENT",
            "TILLAGE_EVENT",
            name="entityeventtype",
        ),
        type_=mysql.ENUM("APPLICATION_EVENT", "CROPPING_EVENT", "GRAZING_EVENT", "IRRIGATION_EVENT", "TILLAGE_EVENT"),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
