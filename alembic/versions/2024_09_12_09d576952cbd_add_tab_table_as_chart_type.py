"""add-tab-table-as-chart-type

Revision ID: 09d576952cbd
Revises: 7e13f032e40e
Create Date: 2024-09-12 11:38:07.469251

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "09d576952cbd"
down_revision = "7e13f032e40e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
        ),
        type_=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "TAB_TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
            name="charttype",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_chart_presets",
        "chart_type",
        existing_type=sa.Enum(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "TAB_TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
            name="charttype",
        ),
        type_=mysql.ENUM(
            "DOUGHNUT",
            "VERTICAL_BAR",
            "HORIZONTAL_BAR",
            "MAP",
            "PIE",
            "STAT",
            "TABLE",
            "LINE",
            "MAP_WITH_BUBBLES",
            "TIMESTAMP_LINE",
            "VERTICAL_BAR_STACKED",
            "HORIZONTAL_BAR_STACKED",
            "VERTICAL_BAR_GROUPED",
            "HORIZONTAL_BAR_GROUPED",
            "PROGRESS",
            "SCATTER_PLOT",
            "GREEN_COVER_SCATTER_PLOT",
            "STRING",
        ),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
