"""Add visibility to mrv_reporting_dashboards

Revision ID: 4efa57026eb8
Revises: a3c64dafa344
Create Date: 2024-04-21 16:09:17.775698

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "4efa57026eb8"
down_revision = "a3c64dafa344"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "mrv_reporting_dashboards", sa.Column("visibility", sa.Enum("SUPER_ADMIN", name="visibility"), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_reporting_dashboards", "visibility")
    # ### end Alembic commands ###
