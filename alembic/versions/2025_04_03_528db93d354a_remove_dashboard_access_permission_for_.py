"""remove-dashboard-access-permission-for-producer-admin-ro

Revision ID: 528db93d354a
Revises: 729aaf9766e9
Create Date: 2025-04-03 11:31:12.665255

"""

from sqlalchemy.orm.session import Session

from alembic import op
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles

# revision identifiers, used by Alembic.
revision = "528db93d354a"
down_revision = "729aaf9766e9"
branch_labels = None
depends_on = None

regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS.value: {},
}


def upgrade():
    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)


def downgrade():
    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
