"""Replace MeasurementEligibility unique constraint with conditional unique constraint.

Revision ID: 7e13f032e40e
Revises: 2e9a3e1035f2
Create Date: 2024-09-05 14:44:54.533660

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7e13f032e40e"
down_revision = "65b012bb5f94"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("TRUNCATE mrv_measurement_eligibility;")
    op.drop_constraint("mrv_measurement_eligibility_ibfk_1", "mrv_measurement_eligibility", type_="foreignkey")
    op.drop_index("field_id_deleted_at_uc", table_name="mrv_measurement_eligibility")
    op.create_index(
        "unique_undeleted_field_eligibility",
        "mrv_measurement_eligibility",
        ["field_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )
    op.create_foreign_key(
        "mrv_measurement_eligibility_ibfk_1", "mrv_measurement_eligibility", "mrv_fields", ["field_id"], ["id"]
    )


def downgrade():
    op.execute("TRUNCATE mrv_measurement_eligibility;")
    op.drop_constraint("mrv_measurement_eligibility_ibfk_1", "mrv_measurement_eligibility", type_="foreignkey")
    op.drop_index("unique_undeleted_field_eligibility", table_name="mrv_measurement_eligibility")
    op.create_index(
        "field_id_deleted_at_uc",
        "mrv_measurement_eligibility",
        ["field_id", "deleted_at"],
        unique=True,
    )
    op.create_foreign_key(
        "mrv_measurement_eligibility_ibfk_1", "mrv_measurement_eligibility", "mrv_fields", ["field_id"], ["id"]
    )
