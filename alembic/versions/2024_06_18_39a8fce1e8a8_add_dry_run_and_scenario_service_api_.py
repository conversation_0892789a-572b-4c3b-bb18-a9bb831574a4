"""add dry_run and scenario_service_api columns to mrv_dndc_tasks table

Revision ID: 39a8fce1e8a8
Revises: 96321a4550d8
Create Date: 2024-06-18 09:21:42.246778

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "39a8fce1e8a8"
down_revision = "96321a4550d8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mrv_dndc_tasks", sa.Column("is_dry_run", sa.<PERSON>(), nullable=False, server_default="0"))
    op.add_column(
        "mrv_dndc_tasks",
        sa.Column(
            "scenarios_service_api",
            sa.Enum("measure_api", "explore_api", "biofuels_api", name="scenariosserviceapi"),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mrv_dndc_tasks", "scenarios_service_api")
    op.drop_column("mrv_dndc_tasks", "is_dry_run")
    # ### end Alembic commands ###
