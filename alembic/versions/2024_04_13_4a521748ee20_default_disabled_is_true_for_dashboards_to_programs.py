"""mrv_reporting_dashboards_to_programs.disabled is True by default (server-side)

Revision ID: 4a521748ee20
Revises: 08901c25aa2b
Create Date: 2024-04-12 17:41:02.029707

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4a521748ee20"
down_revision = "08901c25aa2b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_dashboards_to_programs",
        "disabled",
        existing_type=mysql.TINYINT(display_width=1),
        server_default="1",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_reporting_dashboards_to_programs",
        "disabled",
        existing_type=mysql.TINYINT(display_width=1),
        server_default=None,
        existing_nullable=False,
    )
    # ### end Alembic commands ###
