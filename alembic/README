# DB migrations

We use alembic!

## How to point to a specific DB

Currently 3307 is the default port for the local DB.
We use 3306 for everything else.
Update the `sqlalchemy.url` in `alembic.ini` to point to the DB you want to target.
Please make sure you don't commit the changed file!!
```ini
; alembic.ini
sqlalchemy.url = mysql+aiomysql://username:<EMAIL>:3307/database-name
```

## How to consolidate migrations

1. Delete all the migration files
2. Reset your local db to empty
3. `make alembic_create_automigration MESSAGE="Consolidate migrations"`
4. Add `import helper` to top of generated migration
5. `local=True alembic upgrade head`
6. `make test`
7. If it passes, then commit the changes.
