from fastapi import Request

from domain_event_bus.domain_event_handlers import (
    bulk_field_completion_event_handler,
    bulk_project_completion_event_handler,
    DomainEventHandler,
    entity_value_last_updated_at_event_handler,
    field_completion_event_handler,
    field_lineage_event_handler,
    field_status_change_handler,
    measurement_eligibility_event_handler,
    reconcile_event_associations_handler,
)
from domain_event_bus.domain_events import DomainEvent, DomainEventType

EVENT_TYPE_TO_HANDLERS = {
    DomainEventType.bulk_field_events_changed: [bulk_field_completion_event_handler],
    DomainEventType.bulk_project_events_changed: [bulk_project_completion_event_handler],
    DomainEventType.field_boundary_updated: [
        field_lineage_event_handler,
        measurement_eligibility_event_handler,
        reconcile_event_associations_handler,
    ],
    DomainEventType.field_events_changed: [entity_value_last_updated_at_event_handler, field_completion_event_handler],
    DomainEventType.field_values_changed: [entity_value_last_updated_at_event_handler],
    DomainEventType.fields_created: [field_lineage_event_handler, field_status_change_handler],
    DomainEventType.fields_deleted: [measurement_eligibility_event_handler, field_status_change_handler],
    DomainEventType.phase_completed: [measurement_eligibility_event_handler],
    DomainEventType.project_contract_updated: [measurement_eligibility_event_handler],
}


class DomainEventBus:
    def __init__(self, event_type_to_handlers: dict[DomainEventType, list[DomainEventHandler]]) -> None:
        self.__event_type_to_handlers = event_type_to_handlers

    async def publish(self, event: DomainEvent, request: Request) -> None:
        event_type = event.get_event_type()
        event_handlers = self.__event_type_to_handlers.get(event_type, [])
        for handler in event_handlers:
            await handler.handle_event(
                request=request,
                event=event,
            )

    def handlers_were_called(self, event_type: DomainEventType) -> bool:
        """
        This is to support checking that handlers were called
        """
        event_handlers = self.__event_type_to_handlers.get(event_type, [])
        return all(eh.called for eh in event_handlers)


event_bus = DomainEventBus(EVENT_TYPE_TO_HANDLERS)
