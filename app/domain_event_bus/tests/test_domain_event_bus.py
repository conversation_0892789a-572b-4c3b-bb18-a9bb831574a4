from datetime import datetime

from fastapi import Request

from domain_event_bus.domain_event_bus import DomainEventBus, EVENT_TYPE_TO_HANDLERS
from domain_event_bus.domain_event_handlers import DomainEventHandler
from domain_event_bus.domain_events import DomainEvent, FieldBoundaryUpdatedEvent
from domain_event_bus.enums import DomainEventType


class HandlerOne(DomainEventHandler):

    def __init__(self):
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.field_boundary_updated,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True


class HandlerTwo(DomainEventHandler):

    def __init__(self):
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.project_contract_updated,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True


async def test_domain_event_bus(mdl, app_request):
    event_handler_1 = HandlerOne()
    event_handler_2 = HandlerTwo()
    event_type_to_handlers = {
        DomainEventType.field_boundary_updated: [event_handler_1],
        DomainEventType.project_contract_updated: [event_handler_2],
    }
    event_bus = DomainEventBus(event_type_to_handlers=event_type_to_handlers)

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event = FieldBoundaryUpdatedEvent(
        program_id=program.id,
        project_id=project.id,
        field_id_to_old_md5={field.id: "old_md5"},
        update_time=datetime.now(),
    )
    await event_bus.publish(event=event, request=app_request)

    # confirm correct subscribers notified
    assert event_handler_1.called
    assert not event_handler_2.called


def test_event_type_to_handlers():
    for event_type, event_handlers in EVENT_TYPE_TO_HANDLERS.items():
        for handler in event_handlers:
            supported_event_types = handler.get_supported_event_types()
            assert event_type in supported_event_types
