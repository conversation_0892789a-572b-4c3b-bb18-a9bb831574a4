from unittest.mock import ANY

from sqlalchemy import select

from domain_event_bus.domain_event_handlers import (
    EntityValueLastUpdatedAtEventHandler,
    FieldCompletionEventHandler,
    FieldLineageEventHandler,
    MeasurementEligibilityEventHandler,
)
from domain_event_bus.domain_events import (
    FieldEventsChanged,
    FieldsCreated<PERSON>vent,
    FieldsDeleted,
    FieldValuesChanged,
)
from fields.model import Fields
from helper.helper import run_query
from phases.enums import PhaseTypes
from projects.model import Projects


async def test_measurement_eligibility_handler(mdl, mocker, app_request):
    program_id = (await mdl.Programs()).id
    project_id = (await mdl.Projects(program_id=program_id)).id
    mock_calc_m_elig = mocker.patch(
        "domain_event_bus.domain_event_handlers.measurement_elig_tasks.measurement_eligibility_task"
    )
    await MeasurementEligibilityEventHandler().handle_event(
        request=app_request, event=FieldsDeleted(program_id=program_id, project_id=project_id, field_ids=[])
    )
    mock_calc_m_elig.delay.assert_called_once_with(
        fs_impersonator_user_id=ANY, fs_user_id=ANY, program_id=program_id, project_ids=[project_id]
    )


async def test_field_completion_event_hander(mdl, mocker, app_request):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.DASHBOARDS, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=1)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None)
    stage_completion_identifier_lookup = {}

    mock_update_stage_completions_for_field = mocker.patch(
        "ui.projects.completion.domain_handler_processor.completion_tasks.run_field_completion_change_handler"
    )

    await FieldCompletionEventHandler().handle_event(
        request=app_request,
        event=FieldEventsChanged(
            program_id=program.id,
            phase_id=phase.id,
            project_id=project.id,
            field_id=field.id,
            stage_completion_identifier_lookup=stage_completion_identifier_lookup,
        ),
    )
    mock_update_stage_completions_for_field.delay.assert_called_once_with(
        fs_impersonator_user_id=ANY,
        fs_user_id=ANY,
        phase_id=phase.id,
        field_id=field.id,
        stage_completion_identifier_lookup=stage_completion_identifier_lookup,
    )


async def test_entity_value_last_updated_at_event_handler(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id, value_last_updated_at=None)
    field = await mdl.Fields(parent_project_id=project.id, value_last_updated_at=None)

    await EntityValueLastUpdatedAtEventHandler().handle_event(
        request=app_request,
        event=FieldValuesChanged(
            program_id=program.id,
            project_id=project.id,
            field_id=field.id,
        ),
    )

    async with db_session_maker() as s:
        query = select(Fields).where(Fields.id == field.id)
        res = (await run_query(query=query, s=s)).scalars().first()
        assert res.value_last_updated_at is not None

        query = select(Projects).where(Projects.id == project.id)
        res = (await run_query(query=query, s=s)).scalars().first()
        assert res.value_last_updated_at is not None


async def test_field_lineage_event_handler(mdl, mocker, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    mock_set_field_baselines = mocker.patch(
        "domain_event_bus.domain_event_handlers.field_lineage_tasks.set_field_baselines_task"
    )
    await FieldLineageEventHandler().handle_event(
        request=app_request,
        event=FieldsCreatedEvent(program_id=program.id, project_id=project.id, field_ids=[field.id]),
    )
    mock_set_field_baselines.delay.assert_called_once_with(
        program_id=program.id,
        project_id=project.id,
        field_ids=[field.id],
        fs_impersonator_user_id=ANY,
        fs_user_id=ANY,
    )
