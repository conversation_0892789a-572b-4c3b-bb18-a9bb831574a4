import enum


class DomainEventType(enum.StrEnum):
    bulk_field_events_changed = "bulk_field_events_changed"
    bulk_project_events_changed = "bulk_project_events_changed"
    field_boundary_updated = "field_boundary_updated"
    field_events_changed = "field_events_changed"
    field_values_changed = "field_values_changed"
    fields_created = "fields_created"
    fields_deleted = "fields_deleted"
    phase_completed = "phase_completed"
    project_contract_updated = "project_contract_updated"
