# this file is for tasks where we want to do a sequence of offline
# calls in response to domain events that don't obviously belong in
# any other single area

import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from fields.methods import back_fill_field_status


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_field_status_change_handling(
    self: DBTask,
    *,
    project_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    """
    The method employed by this task assumes all of the given project ids are in the same program
    """
    from ui.projects.completion.handle_project_stage_updates import (
        attempt_updating_all_relevant_stage_completions_for_project,
    )

    await back_fill_field_status(request, project_ids=[project_id])
    await attempt_updating_all_relevant_stage_completions_for_project(request, project_id)
