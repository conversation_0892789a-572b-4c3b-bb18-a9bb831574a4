from annotations import DictStrAny


def field_outcomes_1(program_id: int, project_id: int, field_id: int) -> DictStrAny:
    return {
        "id": 1,
        "program_id": program_id,
        "project_id": project_id,
        "field_id": field_id,
        "start_date": "2022-09-14",
        "end_date": "2023-07-15",
        "crop_name": "wheat_winter",
        "crop_yield": 51.7,
        "credit_share": 4.038594960849750,
        "reversible_credit_share": 3.7536177532008600,
        "reversible_dsoc_baseline": -14.298400313835700,
        "reversible_dsoc_practice_change": -24.***************,
        "reversible_dsoc_preliminary_credit": 10.103101945084500,
        "reversible_dsoc_mean": 13.214878311001300,
        "reversible_dsoc_standard_deviation": 24.949461895945300,
        "non_reversible_credit_share": 0.2849772076488960,
        "non_reversible_mean": 0.18231482451372000,
        "non_reversible_standard_deviation": 7.544940664443770,
        "non_reversible_direct_n2o_baseline": 0.6661726122780750,
        "non_reversible_direct_n2o_practice_change": 0.9551998594176010,
        "non_reversible_direct_n2o_preliminary_credit": -0.28902724713952600,
        "non_reversible_direct_n2o_mean": 0.23878210607253600,
        "non_reversible_direct_n2o_standard_deviation": 7.544940664443770,
        "non_reversible_indirect_n2o_baseline": 0.3401170732606750,
        "non_reversible_indirect_n2o_practice_change": 0.2578473349196170,
        "non_reversible_indirect_n2o_credit": 0.08226973834105780,
        "non_reversible_soil_ch4_baseline": 0.0,
        "non_reversible_soil_ch4_practice_change": 0.0,
        "non_reversible_soil_ch4_credit": 0.0,
        "non_reversible_fossil_fuel_co2_baseline": 0.0,
        "non_reversible_fossil_fuel_co2_practice_change": 0.13873701989987400,
        "non_reversible_fossil_fuel_co2_credit": -0.13873701989987400,
        "created_at": "2024-02-01T20:38:33.275Z",
    }


def field_outcomes_2(program_id: int, project_id: int, field_id: int) -> DictStrAny:
    return {
        "id": 2,
        "program_id": program_id,
        "project_id": project_id,
        "field_id": field_id,
        "start_date": "2022-10-04",
        "end_date": "2023-09-18",
        "crop_name": "corn",
        "crop_yield": None,
        "credit_share": 24.320608279361600,
        "reversible_credit_share": 21.910837879765700,
        "reversible_dsoc_baseline": -56.610795668799300,
        "reversible_dsoc_practice_change": -73.0050594660637,
        "reversible_dsoc_preliminary_credit": 16.394263797264400,
        "reversible_dsoc_mean": 22.*************,
        "reversible_dsoc_standard_deviation": 86.35685323007950,
        "non_reversible_credit_share": 2.****************,
        "non_reversible_mean": 6.8707199316329200,
        "non_reversible_standard_deviation": 26.164622312749000,
        "non_reversible_direct_n2o_baseline": 21.25080780050620,
        "non_reversible_direct_n2o_practice_change": 16.05407562217290,
        "non_reversible_direct_n2o_preliminary_credit": 5.1967321783333200,
        "non_reversible_direct_n2o_mean": 5.616191255915930,
        "non_reversible_direct_n2o_standard_deviation": 26.164622312749000,
        "non_reversible_indirect_n2o_baseline": 6.380175596973600,
        "non_reversible_indirect_n2o_practice_change": 4.638140448552890,
        "non_reversible_indirect_n2o_credit": 1.7420351484207200,
        "non_reversible_soil_ch4_baseline": 0.0,
        "non_reversible_soil_ch4_practice_change": 0.0,
        "non_reversible_soil_ch4_credit": 0.0,
        "non_reversible_fossil_fuel_co2_baseline": None,  # 0.0,
        "non_reversible_fossil_fuel_co2_practice_change": None,  # 0.***************,
        "non_reversible_fossil_fuel_co2_credit": None,  # -0.***************,
        "created_at": "2024-02-01T20:38:33.275Z",
    }


def crop_outcomes_1(program_id: int) -> DictStrAny:
    return {
        "id": 1,
        "program_id": program_id,
        "crop_type": "corn",
        "start_date": "2022-04-10",
        "end_date": "2023-12-15",
        "total_yield": 46258778.43765190,
        "number_of_fields": 4181,
        "number_of_acres": 232229.27,
        "total_reversible_credit": 0.0019458562141450200,
        "total_non_reversible_credit": 0.00021130641866857400,
        "total_credit": 0.0021571626328136000,
        "reversible_credit_mean": 91437.67100271880,
        "reversible_credit_standard_deviation": 8736.239329434660,
        "total_reversible_emissions_reductions": 90012.93148166290,
        "non_reversible_credit_mean": 10036.154765148000,
        "non_reversible_credit_standard_deviation": 2330.3105594879500,
        "total_non_reversible_emissions_reductions": 9774.776803643290,
        "created_at": "2024-02-01T21:15:39.185Z",
    }


def crop_outcomes_2(program_id: int) -> DictStrAny:
    return {
        "id": 2,
        "program_id": program_id,
        "crop_type": "rice",
        "start_date": "2022-04-10",
        "end_date": "2023-10-01",
        "total_yield": 179479.557512342,
        "number_of_fields": 23,
        "number_of_acres": 914.932385430822,
        "total_reversible_credit": 0.00042502875636462600,
        "total_non_reversible_credit": 0.0003483908066160410,
        "total_credit": 0.0007734195629806670,
        "reversible_credit_mean": 90.12241098978410,
        "reversible_credit_standard_deviation": 202.8673512562290,
        "total_reversible_emissions_reductions": 62.52902781281510,
        "non_reversible_credit_mean": 101.2384771711630,
        "non_reversible_credit_standard_deviation": 199.34811043411100,
        "total_non_reversible_emissions_reductions": 76.28397312234410,
        "created_at": "2024-02-01T21:15:39.185Z",
    }


# this should be pretty realistic
def crop_outcomes_3(program_id: int) -> DictStrAny:
    return {
        "id": 3,
        "program_id": program_id,
        "crop_type": "alfalfa",
        "start_date": "2022-04-10",
        "end_date": "2023-10-01",
        "total_yield": 103864,
        "number_of_fields": 18,
        "number_of_acres": 500.141,
        "total_reversible_credit": 0.00176016,
        "total_non_reversible_credit": 0.000215249,
        "total_credit": 0.00197541,
        "reversible_credit_mean": 206.357,
        "reversible_credit_standard_deviation": 188.919,
        "total_reversible_emissions_reductions": 182.818,
        "non_reversible_credit_mean": 30.2103,
        "non_reversible_credit_standard_deviation": 57.3916,
        "total_non_reversible_emissions_reductions": 22.3566,
        "created_at": "2024-02-01T21:15:39.185Z",
    }


def program_outcomes_1(program_id: int) -> DictStrAny:
    return {
        "id": 1,
        "program_id": program_id,
        "start_date": "2022-04-10",
        "end_date": "2023-12-15",
        "number_of_fields": 4181,
        "number_of_acres": 232229.27,
        "total_reversible_credit": 0.0019458562141450200,
        "total_non_reversible_credit": 0.00021130641866857400,
        "total_credit": 0.0021571626328136000,
        "reversible_credit_mean": 91437.67100271880,
        "reversible_credit_standard_deviation": 8736.239329434660,
        "total_reversible_emissions_reductions": 90012.93148166290,
        "non_reversible_credit_mean": 10036.154765148000,
        "non_reversible_credit_standard_deviation": 2330.3105594879500,
        "total_non_reversible_emissions_reductions": 9774.776803643290,
        "created_at": "2024-02-01T21:15:39.185Z",
    }
