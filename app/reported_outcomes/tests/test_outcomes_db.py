from datetime import datetime

from programs.enums import Protocols
from reported_outcomes.data import get_reporting_excluded_fields_for_program
from reported_outcomes.db import (
    _set_program_outcome_approval,
    get_field_outcomes_for_program,
    query_outcome_approvals_for_program,
    revoke_program_outcome_approval,
)
from reported_outcomes.enums import OutcomeApprovalStatusType
from reported_outcomes.tests.support_functions import field_outcomes_2


async def test_set_program_outcome_approval(app_request, mdl):
    # so, we need to be able to update test runs to approved runs,
    # but no existing run can be a test run
    program = await mdl.Programs()
    task = await mdl.DndcTasks(
        program_id=program.id,
    )

    is_test_run = True
    await _set_program_outcome_approval(app_request, program.id, task.id, is_test_run)
    approvals = await query_outcome_approvals_for_program(app_request, program.id)
    assert len(approvals) == 1
    assert approvals[0].outcome_approval_status == OutcomeApprovalStatusType.TEST

    is_test_run = False
    await _set_program_outcome_approval(app_request, program.id, task.id, is_test_run)
    approvals = await query_outcome_approvals_for_program(app_request, program.id)
    assert len(approvals) == 1
    assert approvals[0].outcome_approval_status == OutcomeApprovalStatusType.APPROVED

    # tests are not allowed to overwrite approval
    is_test_run = True
    await _set_program_outcome_approval(app_request, program.id, task.id, is_test_run)
    approvals = await query_outcome_approvals_for_program(app_request, program.id)
    assert len(approvals) == 1
    assert approvals[0].outcome_approval_status == OutcomeApprovalStatusType.APPROVED


async def test_get_field_outcomes_for_program(app_request, mdl):
    # this is testing that program-specific exclusions are working
    program_id = 155
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3, id=155)
    project = await mdl.Projects(program_id=program.id)
    task = await mdl.DndcTasks(
        program_id=program.id,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    exclusions = get_reporting_excluded_fields_for_program(program_id)
    max_excluded_id = max(exclusions)
    not_excluded_id = max_excluded_id + 1
    for field_id in [max_excluded_id, not_excluded_id]:
        await mdl.Fields(parent_project_id=project.id, deleted_at=None, id=field_id)
        test_field_outcomes = field_outcomes_2(program_id, project.id, field_id)
        test_field_outcomes["created_at"] = datetime.now()
        test_field_outcomes["task_id"] = task.id
        # the id doesn't have to be the field_id;
        # # this is just to prevent the tests spuriously creating a primary key failure
        test_field_outcomes["id"] = field_id
        await mdl.FieldLevelOutcomes(**test_field_outcomes)

    field_outcomes = await get_field_outcomes_for_program(app_request, program_id)
    assert len(field_outcomes) == 1
    assert field_outcomes[0].id == not_excluded_id


async def test_revoke_program_outcome_approval(app_request, mdl):
    program = await mdl.Programs()
    approved_task = await mdl.DndcTasks(
        program_id=program.id,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=test_task.id, outcome_approval_status=OutcomeApprovalStatusType.TEST
    )
    await revoke_program_outcome_approval(app_request, program.id)

    approvals = await query_outcome_approvals_for_program(app_request, program.id)
    assert len(approvals) == 2
    for approval in approvals:
        assert approval.outcome_approval_status == OutcomeApprovalStatusType.REVOKED
