from dataclasses import dataclass
from datetime import date

# So, these dataclasses aren't actually hooked up to anything at the moment.
# Instead of doing two translations for one set of outputs, we need more customers.
# Having said that, there are ideas here in how we want outcomes to be structured
# in other situations going forward


@dataclass
class InterventionImprovementMagnitudes:
    """
    Magnitude of change expected under baseline scenario
    versus under measured practice change
    """

    baseline: float
    practice_change: float


@dataclass
class InterventionImprovementMagnitudesWithPreliminaryCredit(InterventionImprovementMagnitudes):
    """
    Raw difference between magnitude under practice change minus baseline
    """

    credit: float


@dataclass
class UncertainInterventionImprovementMagnitudesWithPreliminaryCredit(InterventionImprovementMagnitudes):
    """
    Amount of uncertainty for intervention, with credit also minus the mean uncertainty
    """

    uncertainty_mean: float
    uncertainty_standard_deviation: float
    uncertainty_corrected_credit: float


@dataclass
class ReversibleFieldOutcome:
    """
    Reversible outcome, i.e. improvement (or decline) in soil organic carbon
    """

    credit_share: float
    change_in_soil_organic_carbon: UncertainInterventionImprovementMagnitudesWithPreliminaryCredit


@dataclass
class NonReversibleFieldOutcome:
    """
    Non-reversible outcome, i.e. changes in per-season emissions
    """

    credit_share: float
    mean: float
    standard_deviation: float
    direct_n2o: UncertainInterventionImprovementMagnitudesWithPreliminaryCredit
    indirect_n2o: InterventionImprovementMagnitudesWithPreliminaryCredit
    soil_ch4: InterventionImprovementMagnitudesWithPreliminaryCredit
    fossil_fuel_co2: InterventionImprovementMagnitudesWithPreliminaryCredit


@dataclass
class FieldOutcome:
    """
    The total project outcome of a field
    """

    project_id: int
    field_id: int
    start_date: date
    end_date: date
    crop_name: str
    crop_yield: float
    crop_yield_unit: str
    credit_share: float
    reversible_outcome: ReversibleFieldOutcome
    non_reversible_outcome: NonReversibleFieldOutcome


@dataclass
class CropOutcome:
    """
    The total project outcome of a crop
    """

    crop_type: str
    start_date: date
    end_date: date
    total_yield: float
    number_of_fields: int
    area_in_acres: float
    total_reversible_credit: float
    total_non_reversible_credit: float
    total_credit: float
    reversible_credit_mean: float
    reversible_credit_standard_deviation: float
    total_reversible_emissions_reductions: float
    non_reversible_credit_mean: float
    non_reversible_credit_standard_deviation: float
    total_non_reversible_emissions_reductions: float


@dataclass
class ReportedOutcomes:
    """
    All of the reported outcomes reported for a program
    """

    program_id: int
    field_outcomes: dict[int, FieldOutcome]
    crop_outcomes: dict[str, CropOutcome]
