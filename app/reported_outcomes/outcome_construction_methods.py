import csv
import io
import math
from datetime import date
from typing import Any, Optional
from urllib.parse import quote

from fastapi import Request

from core.model import Users
from defaults.defaults import defaults_retriever
from entity_events.event_creators.helpers import harvest_yield_rate_unit_value_to_enums
from entity_events.events.yield_rate import YieldRate
from entity_events.units import AreaUnit, VolumeUnit
from fields.db import get_field_level_reporting_metadata
from helper.helper import CustomStreamingResponse
from logger import get_logger
from programs.db import get_coops_for_projects, get_program
from programs.export.methods import create_io_csv_file, zip_in_memory_files
from programs.model import Programs
from projects.db import get_users_for_projects
from reported_outcomes import db, read_me_definitions
from reported_outcomes.constants import ZIP_CONTENT_TYPE
from reported_outcomes.data import get_supplimental_yields_for_fields
from reported_outcomes.model import (
    CropLevelInventoryOutcomes,
    CropLevelOutcomes,
    FieldLevelInventoryOutcomes,
    FieldLevelOutcomes,
    ProgramLevelOutcomes,
)
from scenarios_service.enums import MeasureProtocolResponseTypes, ScenariosServiceApi
from scenarios_service.generalized_integration.helpers import (
    lookup_measure_protocol_response_type,
)

logger = get_logger(__name__)


async def construct_combined_zip_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    zipped_file = await build_raw_combined_zip_for_program(request, program_id)
    output_file_name = f"combined_outcomes_for_{program_id}"
    response = CustomStreamingResponse(zipped_file, media_type=ZIP_CONTENT_TYPE)
    response.headers["Content-Disposition"] = "attachment; filename*=utf-8''{0}.zip".format(quote(output_file_name))
    return response


# this function is not used but is being staged in for follow-up work in MRV-6038
def construct_combined_zip_for_program_from_data(program_id: int, zipped_file: bytes) -> CustomStreamingResponse:
    output_file_name = f"combined_outcomes_for_{program_id}"
    response = CustomStreamingResponse(zipped_file, media_type=ZIP_CONTENT_TYPE)
    response.headers["Content-Disposition"] = "attachment; filename*=utf-8''{0}.zip".format(quote(output_file_name))
    return response


async def build_raw_combined_zip_for_program(request: Request, program_id: int) -> bytes:
    """
    This method is both used inline for get responses and for constructing the stored
    combined result
    """
    ss_api = await db.get_scenario_service_api_of_selected_results(request, program_id)
    if ss_api is None or ss_api == ScenariosServiceApi.measure_api:
        return await _build_raw_combined_zip_for_measure_program(request, program_id)
    elif ss_api == ScenariosServiceApi.inventory_api:
        return await _build_raw_combined_zip_for_inventory_program(request, program_id)
    else:
        raise NotImplementedError(f"Output construction is not available for results from {ss_api.value} API")


async def _build_raw_combined_zip_for_measure_program(request: Request, program_id: int) -> bytes:
    logger.info(f"Building field outcomes for program {program_id}")
    field_outcomes = await _build_csv_content_for_field_outcomes(request, program_id)
    logger.info(f"Building crop outcomes for program {program_id}")
    crop_outcomes = await _build_csv_content_for_crop_outcomes(request, program_id, field_outcomes)
    logger.info(f"Building program outcomes for program {program_id}")
    program_outcomes = await _build_csv_content_for_program_outcomes(request, program_id)
    logger.info(f"Building other contents for program {program_id}")
    does_program_have_co_ops = _check_field_outcome_content_for_co_ops(field_outcomes)
    measure_protocol_response_type = await _get_measure_protocol_response_type(request, program_id)
    read_me_contents = read_me_definitions.get_field_outcomes_read_me(
        does_program_have_co_ops, measure_protocol_response_type
    )
    logger.info(f"Creating field outcome CSV file for program {program_id}")
    file_name_with_data: list[tuple[str, bytes]] = [
        (
            f"field_outcomes_for_program_{program_id}.csv",
            await create_io_csv_file(field_outcomes),
        ),
    ]
    if crop_outcomes:
        logger.info("Creating crop outcome CSV file for program {program_id}")
        file_name_with_data.append(
            (
                f"crop_outcomes_for_program_{program_id}.csv",
                await create_io_csv_file(crop_outcomes),
            )
        )
        read_me_contents = read_me_contents + read_me_definitions.get_crop_outcomes_read_me()
    if program_outcomes:
        logger.info(f"Creating program outcome CSV file for program {program_id}")
        file_name_with_data.append(
            (
                f"program_outcomes_for_program_{program_id}.csv",
                await create_io_csv_file(program_outcomes),
            )
        )
        read_me_contents = read_me_contents + read_me_definitions.get_program_outcomes_read_me()

    file_name_with_data.append(
        (
            f"readme_for_program_{program_id}.csv",
            await create_io_csv_file(read_me_contents),
        )
    )
    logger.info(f"Zipping files for program {program_id}")
    return await zip_in_memory_files(file_name_with_file_data=file_name_with_data)


async def _build_raw_combined_zip_for_inventory_program(request: Request, program_id: int) -> bytes:
    field_inventory_outcomes = await _build_csv_content_for_field_inventory_outcomes(request, program_id)
    crop_inventory_outcomes = await _build_csv_content_for_crop_inventory_outcomes(
        request, program_id, field_inventory_outcomes
    )
    read_me_contents = (
        read_me_definitions.get_field_inventory_outcomes_read_me()
        + read_me_definitions.get_crop_inventory_outcomes_read_me()
    )

    file_name_with_data: list[tuple[str, bytes]] = [
        (
            f"field_outcomes_for_program_{program_id}.csv",
            await create_io_csv_file(field_inventory_outcomes),
        ),
        (
            f"crop_outcomes_for_program_{program_id}.csv",
            await create_io_csv_file(crop_inventory_outcomes),
        ),
        (
            f"readme_for_program_{program_id}.csv",
            await create_io_csv_file(read_me_contents),
        ),
    ]
    return await zip_in_memory_files(file_name_with_file_data=file_name_with_data)


async def construct_csv_of_field_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    field_outcome_content = await _build_csv_content_for_field_outcomes(request, program_id)
    does_program_have_co_ops = _check_field_outcome_content_for_co_ops(field_outcome_content)
    measure_protocol_response_type = await _get_measure_protocol_response_type(request, program_id)
    return _build_csv_result(
        _get_field_outcome_headers(does_program_have_co_ops, measure_protocol_response_type),
        field_outcome_content,
        f"field_outcomes_for_program_{program_id}.csv",
    )


def _check_field_outcome_content_for_co_ops(field_outcome_content: list[dict[str, Any]]) -> bool:
    if field_outcome_content:
        return "Co-op" in field_outcome_content[0]
    else:
        return False


async def _get_measure_protocol_response_type(request: Request, program_id: int) -> MeasureProtocolResponseTypes:
    program: Programs = await get_program(request=request, program_id=program_id)
    return lookup_measure_protocol_response_type(program.protocol)


async def _build_csv_content_for_field_outcomes(request: Request, program_id: int) -> list[dict[str, Any]]:
    field_outcomes = await db.get_field_outcomes_for_program(request, program_id)
    content_dictionaries = await _add_project_level_info_to_field_outcomes(
        request, [_map_field_outcome(field_outcome) for field_outcome in field_outcomes]
    )
    measure_protocol_response_type = await _get_measure_protocol_response_type(request, program_id)
    content_dictionaries = await _add_field_level_info_to_field_outcomes(
        request, program_id, content_dictionaries, "Credited Crop"
    )
    content_dictionaries = _remove_coops_if_all_none(content_dictionaries)
    content_dictionaries = _resequence_field_outcomes(content_dictionaries, measure_protocol_response_type)
    return [_clean_values(cd) for cd in content_dictionaries]


async def _build_csv_content_for_field_inventory_outcomes(request: Request, program_id: int) -> list[dict[str, Any]]:
    field_outcomes = await db.get_field_inventory_outcomes_for_program(request, program_id)
    content_dictionaries = await _add_project_level_info_to_field_outcomes(
        request, [_map_field_inventory_outcome(field_outcome) for field_outcome in field_outcomes]
    )
    content_dictionaries = await _add_field_level_info_to_field_outcomes(
        request, program_id, content_dictionaries, "Crop Type"
    )
    content_dictionaries = _resequence_field_inventory_outcomes(content_dictionaries)
    return [_clean_values(cd) for cd in content_dictionaries]


def _resequence_field_outcomes(
    content_dictionaries: list[dict[str, Any]], measure_protocol_response_type: MeasureProtocolResponseTypes
) -> list[dict[str, Any]]:
    has_co_ops = _check_field_outcome_content_for_co_ops(content_dictionaries)
    headers = _get_field_outcome_headers(has_co_ops, measure_protocol_response_type)
    return [{h: cd[h] for h in headers} for cd in content_dictionaries]


def _resequence_field_inventory_outcomes(content_dictionaries: list[dict[str, Any]]) -> list[dict[str, Any]]:
    headers = _get_field_inventory_outcome_headers()
    return [{h: cd[h] for h in headers} for cd in content_dictionaries]


def _remove_coops_if_all_none(content_dictionaries: list[dict[str, Any]]) -> list[dict[str, Any]]:
    all_are_none = all([cd["Co-op"] is None for cd in content_dictionaries])
    if all_are_none:
        for cd in content_dictionaries:
            del cd["Co-op"]
    return content_dictionaries


async def _add_project_level_info_to_field_outcomes(
    request: Request, field_outcomes: list[dict[str, Any]]
) -> list[dict[str, Any]]:
    project_ids = list({fo["Project ID"] for fo in field_outcomes})
    user_per_project = await get_users_for_projects(request, project_ids)
    content_dictionaries = [_add_user_info_to_field_outcome(fo, user_per_project) for fo in field_outcomes]
    coop_per_project = await get_coops_for_projects(request, project_ids)
    return [_add_coop_info_to_fields_outcome(cd, coop_per_project) for cd in content_dictionaries]


async def _add_field_level_info_to_field_outcomes(
    request: Request, program_id: int, field_outcomes: list[dict[str, Any]], crop_property_name: str
) -> list[dict[str, Any]]:
    field_ids = list({fo["Field ID"] for fo in field_outcomes})
    reporting_metadata_by_field = await get_field_level_reporting_metadata(request, field_ids)
    reporting_metadata_by_field = await _supplement_yields_in_reporting_metadata(
        request, program_id, reporting_metadata_by_field
    )
    return [
        await _add_field_info_to_field_outcome(fo, reporting_metadata_by_field, crop_property_name)
        for fo in field_outcomes
    ]


# this is a hack for 155 until we can figure out how to get all the yields in the regular reporting
async def _supplement_yields_in_reporting_metadata(
    request: Request, program_id: int, reporting_metadata_by_field: dict[int, dict[str, Any]]
) -> dict[int, dict[str, Any]]:
    supplimentary_yields_by_field = get_supplimental_yields_for_fields(program_id)
    for field_id, supplimentary_yields in supplimentary_yields_by_field.items():
        supplimentary_yield = supplimentary_yields[0]
        if field_id not in reporting_metadata_by_field:
            # we have supplimentary yield but for a field
            # removed from the program
            continue
        reporting_metadata = reporting_metadata_by_field[field_id]
        if reporting_metadata["Credited Crop"] in [None, "", "no cover"]:
            reporting_metadata["Credited Crop"] = supplimentary_yield["Credited Crop"]
        reporting_metadata["Yield Value"] = supplimentary_yield["Yield Value"]
        reporting_metadata["Yield Unit"] = supplimentary_yield["Yield Unit"]
        reporting_metadata_by_field[field_id] = reporting_metadata
    return reporting_metadata_by_field


def _get_field_outcome_headers(
    does_program_have_co_ops: bool, measure_protocol_response_type: MeasureProtocolResponseTypes
) -> list[str]:
    lead_group = [
        "Field ID",
        "Email",
        "Project ID",
        "Farm Name",
        "Field Name",
        "First Name",
        "Last Name",
    ]
    if does_program_have_co_ops:
        co_op_group = ["Co-op"]
    else:
        co_op_group = []
    middle_group = [
        "Region",
        "Country",
        "Mapped Area (acres)",
        "MD5",
        "Enrollment Year",
    ]
    # we figured out these columns weren't coming through for the first Vietnam rice program (582)
    # so we removed them for Scope 1 outcomes (at least for now)
    if measure_protocol_response_type == MeasureProtocolResponseTypes.scope_3:
        scope_specific_group = [
            "Credited Crop",
            "Yield (bushels/acre*)",
            "Baseline Practice",
            "Completed Practice Change",
        ]
    elif measure_protocol_response_type == MeasureProtocolResponseTypes.scope_1:
        scope_specific_group = []
    else:
        raise NotImplementedError(f"Did not handle measure protocol response type: {measure_protocol_response_type}")
    trailing_group = [
        "Eligible",
        "Reporting Period Start Date",
        "Reporting Period End Date",
        "Credit Share (tCO2e)",
        "Reversible Credit Share (tCO2e)",
        "Reversible dSOC Practice Change (tCO2e)",
        "Reversible dSOC Baseline (tCO2e)",
        "Reversible dSOC Preliminary Credit (tCO2e)",
        "Reversible dSOC Mean (tCO2e)",
        "Reversible dSOC Standard Deviation (tCO2e)",
        "Non Reversible Credit Share (tCO2e)",
        "Non Reversible Emission Reductions Mean (tCO2e)",
        "Non Reversible Emission Reductions Standard Deviation (tCO2e)",
        "Non Reversible Direct N2O Practice Change (tCO2e)",
        "Non Reversible Direct N2O Baseline (tCO2e)",
        "Non Reversible Direct N2O Preliminary Credit (tCO2e)",
        "Non Reversible Direct N2O Mean (tCO2e)",
        "Non Reversible Direct N2O Standard Deviation (tCO2e)",
        "Non Reversible Indirect N2O Practice Change (tCO2e)",
        "Non Reversible Indirect N2O Baseline (tCO2e)",
        "Non Reversible Indirect N2O Credit (tCO2e)",
        "Non Reversible Soil CH4 Practice Change (tCO2e)",
        "Non Reversible Soil CH4 Baseline (tCO2e)",
        "Non Reversible Soil CH4 Credit (tCO2e)",
    ]
    return lead_group + co_op_group + middle_group + scope_specific_group + trailing_group


def _get_field_inventory_outcome_headers() -> list[str]:
    return [_generate_header_entry(row) for row in read_me_definitions.get_field_inventory_outcomes_read_me()]


def _map_field_outcome(field_outcome: FieldLevelOutcomes) -> dict[str, Any]:
    return {
        "Field ID": field_outcome.field_id,
        "Project ID": field_outcome.project_id,
        "Reporting Period Start Date": _format_date_as_string(field_outcome.start_date),
        "Reporting Period End Date": _format_date_as_string(field_outcome.end_date),
        "Credited Crop": field_outcome.crop_name,
        "Crop Yield Ave (bu/ac)": field_outcome.crop_yield,
        "Credit Share (tCO2e)": field_outcome.credit_share,
        "Reversible Credit Share (tCO2e)": field_outcome.reversible_credit_share,
        "Reversible dSOC Practice Change (tCO2e)": field_outcome.reversible_dsoc_practice_change,
        "Reversible dSOC Baseline (tCO2e)": field_outcome.reversible_dsoc_baseline,
        "Reversible dSOC Preliminary Credit (tCO2e)": field_outcome.reversible_dsoc_preliminary_credit,
        "Reversible dSOC Mean (tCO2e)": field_outcome.reversible_dsoc_mean,
        "Reversible dSOC Standard Deviation (tCO2e)": field_outcome.reversible_dsoc_standard_deviation,
        "Non Reversible Credit Share (tCO2e)": field_outcome.non_reversible_credit_share,
        "Non Reversible Emission Reductions Mean (tCO2e)": field_outcome.non_reversible_mean,
        "Non Reversible Emission Reductions Standard Deviation (tCO2e)": field_outcome.non_reversible_standard_deviation,
        "Non Reversible Direct N2O Practice Change (tCO2e)": field_outcome.non_reversible_direct_n2o_practice_change,
        "Non Reversible Direct N2O Baseline (tCO2e)": field_outcome.non_reversible_direct_n2o_baseline,
        "Non Reversible Direct N2O Preliminary Credit (tCO2e)": field_outcome.non_reversible_direct_n2o_preliminary_credit,
        "Non Reversible Direct N2O Mean (tCO2e)": field_outcome.non_reversible_direct_n2o_mean,
        "Non Reversible Direct N2O Standard Deviation (tCO2e)": field_outcome.non_reversible_direct_n2o_standard_deviation,
        "Non Reversible Indirect N2O Practice Change (tCO2e)": field_outcome.non_reversible_indirect_n2o_practice_change,
        "Non Reversible Indirect N2O Baseline (tCO2e)": field_outcome.non_reversible_indirect_n2o_baseline,
        "Non Reversible Indirect N2O Credit (tCO2e)": field_outcome.non_reversible_indirect_n2o_credit,
        "Non Reversible Soil CH4 Practice Change (tCO2e)": field_outcome.non_reversible_soil_ch4_practice_change,
        "Non Reversible Soil CH4 Baseline (tCO2e)": field_outcome.non_reversible_soil_ch4_baseline,
        "Non Reversible Soil CH4 Credit (tCO2e)": field_outcome.non_reversible_soil_ch4_credit,
        # We aren't currently reporting on fossil fuel practice changes
        # "Non Reversible Fossil Fuel CO2 Practice Change": field_outcome.non_reversible_fossil_fuel_co2_practice_change,
        # "Non Reversible Fossil Fuel CO2 Baseline": field_outcome.non_reversible_fossil_fuel_co2_baseline,
        # "Non Reversible Fossil Fuel CO2 Credit": field_outcome.non_reversible_fossil_fuel_co2_credit,
    }


def _map_field_inventory_outcome(field_outcome: FieldLevelInventoryOutcomes) -> dict[str, Any]:
    if field_outcome.net_emissions_factor is not None:
        net_emissions_factor = _inventory_result_rounding(field_outcome.net_emissions_factor)
    else:
        net_emissions_factor = None

    if field_outcome.ghg_emissions_factor is not None:
        ghg_emissions_factor = _inventory_result_rounding(field_outcome.ghg_emissions_factor)
    else:
        ghg_emissions_factor = None
    if field_outcome.soc_emissions_factor is not None:
        soc_emissions_factor = _inventory_result_rounding(field_outcome.soc_emissions_factor)
    else:
        soc_emissions_factor = None

    return {
        "Field ID": field_outcome.field_id,
        "Project ID": field_outcome.project_id,
        "Crop Type": field_outcome.crop_name,
        "Mapped Area (acres)": _inventory_result_rounding(field_outcome.mapped_acres),
        "Crop Yield Ave (bu/ac)": _inventory_result_rounding(field_outcome.total_yield / field_outcome.mapped_acres),
        "Yield (bushels*)": _inventory_result_rounding(field_outcome.total_yield),
        "Net Emissions Factor Percentage (%)": _inventory_result_rounding(field_outcome.net_emissions_percentage),
        "Net Emissions Factor (mtCO2e/bushels*)": net_emissions_factor,
        "GHG Emissions Factor (mtCO2e/bushels*)": ghg_emissions_factor,
        "SOC Emissions Factor (mtCO2e/bushels*)": soc_emissions_factor,
    }


def _add_user_info_to_field_outcome(
    field_outcome: dict[str, Any], user_per_project: dict[int, Users]
) -> dict[str, Any]:
    user = user_per_project[field_outcome["Project ID"]]
    field_outcome.update({"Email": user.email, "First Name": user.name, "Last Name": user.surname})
    return field_outcome


def _add_coop_info_to_fields_outcome(
    field_outcome: dict[str, Any], coops_per_project: dict[int, str]
) -> dict[str, Any]:
    project_id = field_outcome["Project ID"]
    field_outcome["Co-op"] = coops_per_project.get(project_id, None)
    return field_outcome


async def _add_field_info_to_field_outcome(
    base_field_outcome: dict[str, Any], reporting_metadata_by_field: dict[int, dict[str, Any]], crop_property_name: str
) -> dict[str, Any]:
    metadata_for_field = reporting_metadata_by_field[base_field_outcome["Field ID"]]
    keys_to_add = [
        "Field Name",
        "Farm Name",
        "Region",
        "Country",
        "MD5",
        "Baseline Practice",
        "Completed Practice Change",
        "Eligible",
        "Enrollment Year",
    ]
    base_field_outcome.update({k: metadata_for_field[k] for k in keys_to_add})

    if "Mapped Area (acres)" not in base_field_outcome:
        base_field_outcome["Mapped Area (acres)"] = metadata_for_field["Mapped Area"]

    if not base_field_outcome[crop_property_name] and metadata_for_field["Credited Crop"]:
        regrow_crop = await defaults_retriever.translate_core_crop_to_regrow_name(metadata_for_field["Credited Crop"])
        base_field_outcome[crop_property_name] = regrow_crop

    yield_rate = base_field_outcome["Crop Yield Ave (bu/ac)"]
    if not yield_rate:
        magnitude = metadata_for_field["Yield Value"]
        unit = metadata_for_field["Yield Unit"]
        crop = base_field_outcome[crop_property_name]
        yield_rate = await _calculate_yield_rate(magnitude, unit, crop)

    base_field_outcome["Yield (bushels/acre*)"] = yield_rate
    del base_field_outcome["Crop Yield Ave (bu/ac)"]
    return base_field_outcome


async def _calculate_yield_rate(
    magnitude: Optional[float], unit: Optional[str], crop: Optional[str]
) -> Optional[float]:
    if unit == "bu/ac":
        return magnitude
    if not (magnitude and unit and crop):
        return None
    numerator_unit, denominator_unit = harvest_yield_rate_unit_value_to_enums(unit)
    if numerator_unit is not None:
        try:
            return await YieldRate(
                value=magnitude, numerator_unit=numerator_unit, denominator_unit=denominator_unit
            ).convert_value_to_unit(
                to_numerator_unit=VolumeUnit.BUSHEL, to_denominator_unit=AreaUnit.ACRE, crop_type=crop
            )
        except NotImplementedError:
            logger.error(f"Unknown crop for yield rate in outcomes reporting: {crop}")
            return None
    return None


def _inventory_result_rounding(magnitude: float) -> float:
    """
    If the number has an absolute value greater than or equal to one,
    the number is rounded to the tenths place
    (i.e. 11.29 becomes 11.3, -1.29 becomes -1.3).
    If the absolute magnitude is smaller than one, preserve three digits (-0.008946 becomes -0.00895).
    """
    if magnitude == 0.0:
        return 0.0
    elif abs(magnitude) >= 1.0:
        return round(magnitude, 1)
    else:
        significant_digits = 3
        return round(magnitude, significant_digits - int(math.floor(math.log10(abs(magnitude)))) - 1)


def _format_date_as_string(date_to_format: date) -> str:
    return date_to_format.strftime("%Y-%m-%d")


def _clean_values(value_dict: dict[str, Any]) -> dict[str, Any]:
    return {k: _clean_value(v) for k, v in value_dict.items()}


def _clean_value(value_to_clean: Any) -> Any:
    # yes, the string 'null' has come through as data
    if value_to_clean is None or value_to_clean in ["", "null"]:
        return "NA"
    if value_to_clean == "#N/A":
        return "NA"
    return value_to_clean


async def construct_csv_of_crop_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    field_outcomes = await _build_csv_content_for_field_outcomes(request, program_id)
    filename = f"crop_outcomes_for_program_{program_id}.csv"
    return _build_csv_result(
        _get_crop_outcome_headers(),
        await _build_csv_content_for_crop_outcomes(request, program_id, field_outcomes),
        filename,
    )


async def _build_csv_content_for_crop_outcomes(
    request: Request, program_id: int, field_outcomes: list[dict[str, Any]]
) -> list[dict[str, Any]]:
    crop_outcomes = await db.get_crop_outcomes_for_program(request, program_id)
    crop_level_field_totals = _build_crop_level_field_totals(field_outcomes)
    content_dictionaries = [_map_crop_outcome(crop_outcome, crop_level_field_totals) for crop_outcome in crop_outcomes]
    content_dictionaries = _resequence_outcomes_by_headers(content_dictionaries, _get_crop_outcome_headers())
    return [_clean_values(cd) for cd in content_dictionaries]


async def _build_csv_content_for_crop_inventory_outcomes(
    request: Request, program_id: int, field_outcomes: list[dict[str, Any]]
) -> list[dict[str, Any]]:
    crop_outcomes = await db.get_crop_inventory_outcomes_for_program(request, program_id)
    content_dictionaries = [_map_crop_inventory_outcome(crop_outcome) for crop_outcome in crop_outcomes]
    content_dictionaries = _resequence_outcomes_by_headers(content_dictionaries, _get_crop_inventory_outcome_headers())
    return [_clean_values(cd) for cd in content_dictionaries]


def _build_crop_level_field_totals(field_outcomes: list[dict[str, Any]]) -> dict[str, dict[str, Any]]:
    crop_level_field_totals: dict[str, dict[str, Any]] = {}
    for fo in field_outcomes:
        if "Credited Crop" not in fo:
            continue
        current_crop = fo["Credited Crop"]
        totals = crop_level_field_totals.get(current_crop, {})
        totals["Number Of Fields"] = totals.get("Number Of Fields", 0) + 1
        mapped_area = float(fo["Mapped Area (acres)"])
        totals["Mapped Area (acres)"] = totals.get("Mapped Area (acres)", 0) + mapped_area
        if fo["Yield (bushels/acre*)"] != "NA":
            yield_value = float(fo["Yield (bushels/acre*)"])
            totals["Total Yield (bushels*)"] = totals.get("Total Yield (bushels*)", 0) + (
                fo["Mapped Area (acres)"] * yield_value
            )
        crop_level_field_totals[current_crop] = totals
    return crop_level_field_totals


def _resequence_outcomes_by_headers(
    content_dictionaries: list[dict[str, Any]], headers: list[str]
) -> list[dict[str, Any]]:
    return [{h: cd[h] for h in headers} for cd in content_dictionaries]


def _generate_header_entry(readme_row: dict[str, str]) -> str:
    parameter = readme_row["Parameter"].replace("*", "")
    unit = readme_row["Unit"]
    if unit:
        return parameter + " (" + unit + ")"
    return parameter


def _get_crop_outcome_headers() -> list[str]:
    return [_generate_header_entry(row) for row in read_me_definitions.get_crop_outcomes_read_me()]


def _get_crop_inventory_outcome_headers() -> list[str]:
    return [_generate_header_entry(row) for row in read_me_definitions.get_crop_inventory_outcomes_read_me()]


def _map_crop_outcome(
    crop_outcome: CropLevelOutcomes, crop_level_field_totals: dict[str, dict[str, Any]]
) -> dict[str, str]:
    field_totals = crop_level_field_totals[crop_outcome.crop_type]
    number_of_fields = field_totals.get("Number Of Fields", crop_outcome.number_of_fields)
    mapped_area = field_totals.get("Mapped Area (acres)", crop_outcome.number_of_acres)
    total_yield = field_totals.get("Total Yield (bushels*)", crop_outcome.total_yield)
    average_yield = total_yield / mapped_area

    if crop_outcome.total_reversible_emissions_reductions and crop_outcome.total_non_reversible_emissions_reductions:
        total_credit = (
            crop_outcome.total_reversible_emissions_reductions + crop_outcome.total_non_reversible_emissions_reductions
        )
        total_credits_per_yield = total_credit / total_yield
    elif crop_outcome.total_yield and crop_outcome.total_credit:
        total_credit = crop_outcome.total_credit * total_yield
        total_credits_per_yield = crop_outcome.total_credit
    else:
        total_credit = None
        total_credits_per_yield = crop_outcome.total_credit

    return {
        "Crop": crop_outcome.crop_type,
        "Number Of Fields": number_of_fields,
        "Reporting Period Start Date (earliest)": _format_date_as_string(crop_outcome.start_date),
        "Reporting Period End Date (latest)": _format_date_as_string(crop_outcome.end_date),
        "Total Yield (bushels*)": total_yield,
        "Average Yield (bushels/acre*)": average_yield,
        "Mapped Area (acres)": mapped_area,
        "Total Reversible Credit (tCO2e)": crop_outcome.total_reversible_credit,
        "Total Non Reversible Credit (tCO2e)": crop_outcome.total_non_reversible_credit,
        "Total Credits (tCO2e)": total_credit,
        "Total Credits Per Yield (tCO2e/bushel)": total_credits_per_yield,
        "Reversible Credit Mean (tCO2e)": crop_outcome.reversible_credit_mean,
        "Reversible Credit Standard Deviation (tCO2e)": crop_outcome.reversible_credit_standard_deviation,
        "Total Reversible Emissions Reductions (tCO2e)": crop_outcome.total_reversible_emissions_reductions,
        "Non Reversible Credit Mean (tCO2e)": crop_outcome.non_reversible_credit_mean,
        "Non Reversible Credit Standard Deviation (tCO2e)": crop_outcome.non_reversible_credit_standard_deviation,
        "Total Non Reversible Emissions Reductions (tCO2e)": crop_outcome.total_non_reversible_emissions_reductions,
    }


def _map_crop_inventory_outcome(crop_outcome: CropLevelInventoryOutcomes) -> dict[str, Any]:
    average_yield = crop_outcome.total_yield / crop_outcome.mapped_acres
    indirect_n2o_rounded = _inventory_result_rounding(crop_outcome.indirect_n2o_emissions / crop_outcome.total_yield)
    direct_n2o_rounded = _inventory_result_rounding(crop_outcome.direct_n2o_emissions / crop_outcome.total_yield)
    ch4_emissions_rounded = _inventory_result_rounding(crop_outcome.soil_ch4_emissions / crop_outcome.total_yield)
    soc_rounded = _inventory_result_rounding(crop_outcome.soc / crop_outcome.total_yield)
    rounded_total_per_yield = _inventory_result_rounding(
        indirect_n2o_rounded + direct_n2o_rounded + ch4_emissions_rounded + soc_rounded
    )
    return {
        "Crop": crop_outcome.crop_type,
        "Number Of Fields": crop_outcome.number_of_fields,
        "Mapped Area (acres)": _inventory_result_rounding(crop_outcome.mapped_acres),
        "Total Yield (bushels*)": _inventory_result_rounding(crop_outcome.total_yield),
        "Average Yield (bushels/acre*)": _inventory_result_rounding(average_yield),
        "Net Emissions (mtCO2e/Yield Unit)": rounded_total_per_yield,
        "Indirect N2O Emissions (mtCO2e/Yield Unit)": indirect_n2o_rounded,
        "Direct N2O Emissions (mtCO2e/Yield Unit)": direct_n2o_rounded,
        "CH4 Emissions (mtCO2e/Yield Unit)": ch4_emissions_rounded,
        "Land Management Removals (mtCO2e/Yield Unit)": soc_rounded,
    }


async def construct_csv_of_program_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    filename = f"program_outcomes_for_program_{program_id}.csv"
    return _build_csv_result(
        _get_program_outcome_headers(), await _build_csv_content_for_program_outcomes(request, program_id), filename
    )


async def _build_csv_content_for_program_outcomes(request: Request, program_id: int) -> list[dict[str, Any]]:
    program_outcomes = await db.get_program_outcomes_for_program(request, program_id)
    content_dictionaries = [_map_program_outcome(program_outcome) for program_outcome in program_outcomes]
    content_dictionaries = _resequence_outcomes_by_headers(content_dictionaries, _get_program_outcome_headers())
    return [_clean_values(cd) for cd in content_dictionaries]


def _map_program_outcome(program_outcome: ProgramLevelOutcomes) -> dict[str, str]:
    return {
        "Number Of Fields": program_outcome.number_of_fields,
        "Mapped Area (acres)": program_outcome.number_of_acres,
        "Reporting Period Start Date (earliest)": _format_date_as_string(program_outcome.start_date),
        "Reporting Period End Date (latest)": _format_date_as_string(program_outcome.end_date),
        "Total Reversible Credit (tCO2e)": program_outcome.total_reversible_credit,
        "Total Non Reversible Credit (tCO2e)": program_outcome.total_non_reversible_credit,
        "Total Credits (tCO2e)": program_outcome.total_credit,
        "Reversible Credit Mean (tCO2e)": program_outcome.reversible_credit_mean,
        "Reversible Credit Standard Deviation (tCO2e)": program_outcome.reversible_credit_standard_deviation,
        "Total Reversible Emissions Reductions (tCO2e)": program_outcome.total_reversible_emissions_reductions,
        "Non Reversible Credit Mean (tCO2e)": program_outcome.non_reversible_credit_mean,
        "Non Reversible Credit Standard Deviation (tCO2e)": program_outcome.non_reversible_credit_standard_deviation,
        "Total Non Reversible Emissions Reductions (tCO2e)": program_outcome.total_non_reversible_emissions_reductions,
    }


def _get_program_outcome_headers() -> list[str]:
    return [_generate_header_entry(row) for row in read_me_definitions.get_program_outcomes_read_me()]


def _build_csv_stream(header_fields: list[str], content_maps: list[dict[str, str]], filename: str) -> io.StringIO:
    stream = io.StringIO()
    csv_writer = csv.DictWriter(
        stream,
        fieldnames=header_fields,
        quoting=csv.QUOTE_ALL,
    )
    csv_writer.writeheader()
    csv_writer.writerows(content_maps)
    return stream


def _build_csv_result(
    header_fields: list[str], content_maps: list[dict[str, str]], filename: str
) -> CustomStreamingResponse:
    stream = _build_csv_stream(header_fields, content_maps, filename)
    response = CustomStreamingResponse(stream.getvalue(), media_type="text/csv")

    response.headers["Content-Disposition"] = f"attachment; filename={filename}"
    return response
