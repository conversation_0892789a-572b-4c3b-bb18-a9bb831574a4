from fastapi import APIRouter, Depends, Request, status

import reported_outcomes.tasks as reported_outcome_tasks
from helper.helper import CustomStreamingResponse
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from reported_outcomes import outcome_construction_methods, paths
from reported_outcomes.db import query_outcome_approvals_for_program
from reported_outcomes.schema import OutcomeApprovalStatus

router = APIRouter(prefix=paths.base)
tags = ["reported_outcomes"]

logger = get_logger(__name__)


@router.get(
    paths.outcome_approvals,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_FIELD_OUTCOMES]))],
)
async def get_outcome_approvals_for_program(request: Request, program_id: int) -> list[OutcomeApprovalStatus]:
    return await query_outcome_approvals_for_program(request, program_id)


@router.get(
    paths.combined_outcomes_zip,
    response_class=CustomStreamingResponse,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_FIELD_OUTCOMES]))],
)
async def get_zip_of_combined_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    return await outcome_construction_methods.construct_combined_zip_for_program(request, program_id)


@router.get(
    paths.build_combined_outcomes_zip,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_FIELD_OUTCOMES]))],
)
async def start_build_zip_of_combined_outcomes_for_program(request: Request, program_id: int) -> None:
    logger.info(f"starting build of combined outcomes for program {program_id}")
    reported_outcome_tasks.run_build_of_combined_zip_for_program.delay(
        program_id=program_id,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@router.get(
    paths.field_outcomes_csv,
    response_class=CustomStreamingResponse,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_FIELD_OUTCOMES]))],
)
async def get_csv_of_field_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    return await outcome_construction_methods.construct_csv_of_field_outcomes_for_program(request, program_id)


@router.get(
    paths.crop_outcomes_csv,
    response_class=CustomStreamingResponse,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_CROP_OUTCOMES]))],
)
async def get_csv_of_crop_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    return await outcome_construction_methods.construct_csv_of_crop_outcomes_for_program(request, program_id)


@router.get(
    paths.program_outcomes_csv,
    response_class=CustomStreamingResponse,
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_CROP_OUTCOMES]))],
)
async def get_csv_of_program_outcomes_for_program(request: Request, program_id: int) -> CustomStreamingResponse:
    return await outcome_construction_methods.construct_csv_of_program_outcomes_for_program(request, program_id)
