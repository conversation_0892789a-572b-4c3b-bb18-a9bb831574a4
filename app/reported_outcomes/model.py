from sqlalchemy import Column, Date, DateTime, Enum, Float, Integer, String
from sqlalchemy.sql import func
from sqlalchemy.sql.schema import ForeignKey

from db.mysql import Base
from reported_outcomes.enums import OutcomeApprovalStatusType


class FieldLevelOutcomes(Base):
    __tablename__ = "mrv_field_level_outcomes"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # program_id ideally shouldn't be nullable but it was added after the initial definition
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=True, index=True)
    project_id = Column(Integer, ForeignKey("mrv_projects.id"), nullable=False)
    field_id = Column(Integer, ForeignKey("mrv_fields.id"), nullable=False)
    start_date = Column(Date)
    end_date = Column(Date)
    crop_name = Column(String(32))
    crop_yield = Column(Float)
    credit_share = Column(Float)
    reversible_credit_share = Column(Float)
    reversible_dsoc_baseline = Column(Float)
    reversible_dsoc_practice_change = Column(Float)
    reversible_dsoc_preliminary_credit = Column(Float)
    reversible_dsoc_mean = Column(Float)
    reversible_dsoc_standard_deviation = Column(Float)
    non_reversible_credit_share = Column(Float)
    non_reversible_mean = Column(Float)
    non_reversible_standard_deviation = Column(Float)
    non_reversible_direct_n2o_baseline = Column(Float)
    non_reversible_direct_n2o_practice_change = Column(Float)
    non_reversible_direct_n2o_preliminary_credit = Column(Float)
    non_reversible_direct_n2o_mean = Column(Float)
    non_reversible_direct_n2o_standard_deviation = Column(Float)
    non_reversible_indirect_n2o_baseline = Column(Float)
    non_reversible_indirect_n2o_practice_change = Column(Float)
    non_reversible_indirect_n2o_credit = Column(Float)
    non_reversible_soil_ch4_baseline = Column(Float)
    non_reversible_soil_ch4_practice_change = Column(Float)
    non_reversible_soil_ch4_credit = Column(Float)
    non_reversible_fossil_fuel_co2_baseline = Column(Float)
    non_reversible_fossil_fuel_co2_practice_change = Column(Float)
    non_reversible_fossil_fuel_co2_credit = Column(Float)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    # task_id ideally shouldn't be nullable but it was added after the initial definition
    task_id = Column(String(36), ForeignKey("mrv_dndc_tasks.id"), nullable=True)
    updated_at = Column(
        DateTime, server_default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, index=True
    )


class CropLevelOutcomes(Base):
    __tablename__ = "mrv_crop_level_outcomes"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # program_id ideally shouldn't be nullable but it was added after the initial definition
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=True, index=True)
    crop_type = Column(String(32), nullable=False)
    start_date = Column(Date)
    end_date = Column(Date)
    total_yield = Column(Float)
    number_of_fields = Column(Integer)
    number_of_acres = Column(Float)
    total_reversible_credit = Column(Float)
    total_non_reversible_credit = Column(Float)
    total_credit = Column(Float)
    reversible_credit_mean = Column(Float)
    reversible_credit_standard_deviation = Column(Float)
    total_reversible_emissions_reductions = Column(Float)
    non_reversible_credit_mean = Column(Float)
    non_reversible_credit_standard_deviation = Column(Float)
    total_non_reversible_emissions_reductions = Column(Float)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    # task_id ideally shouldn't be nullable but it was added after the initial definition
    task_id = Column(String(36), ForeignKey("mrv_dndc_tasks.id"), nullable=True)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        index=True,
    )


class ProgramLevelOutcomes(Base):
    __tablename__ = "mrv_program_level_outcomes"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=False, index=True)
    start_date = Column(Date)
    end_date = Column(Date)
    number_of_fields = Column(Integer)
    number_of_acres = Column(Float)
    total_reversible_credit = Column(Float)
    total_non_reversible_credit = Column(Float)
    total_credit = Column(Float)
    reversible_credit_mean = Column(Float)
    reversible_credit_standard_deviation = Column(Float)
    total_reversible_emissions_reductions = Column(Float)
    non_reversible_credit_mean = Column(Float)
    non_reversible_credit_standard_deviation = Column(Float)
    total_non_reversible_emissions_reductions = Column(Float)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    # this should have a foreign key to mrv_dndc_tasks.id like the others, but that failed during
    # deployment, so we're on the honor system
    task_id = Column(String(36), nullable=True)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        index=True,
    )


class OutcomeApprovalStatuses(Base):
    __tablename__ = "mrv_outcome_approval_statuses"
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), primary_key=True, nullable=False, index=True)
    # ideally, this would be a  ForeignKey("mrv_dndc_tasks.id"), but that conflicts with it being a primary key
    # unless we also go back and nail down the collation for everything, which probably isn't happening
    task_id = Column(String(36), primary_key=True, nullable=False, index=True)
    outcome_approval_status = Column(Enum(OutcomeApprovalStatusType), nullable=False)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(
        DateTime, server_default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, index=True
    )


class FieldLevelInventoryOutcomes(Base):
    __tablename__ = "mrv_field_level_inventory_outcomes"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # avoiding foreign keys to avoid an incident in creating them
    # see commment for CropLevelInventoryOutcomes below
    program_id = Column(Integer, nullable=False, index=True)
    project_id = Column(Integer, nullable=False)
    field_id = Column(Integer, nullable=False)
    crop_name = Column(String(32))
    mapped_acres = Column(Float)
    total_yield = Column(Float)
    net_emissions_percentage = Column(Float)
    net_emissions_factor = Column(Float)
    ghg_emissions_factor = Column(Float)
    soc_emissions_factor = Column(Float)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    task_id = Column(String(36), nullable=False)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        index=True,
    )


class CropLevelInventoryOutcomes(Base):
    __tablename__ = "mrv_crop_level_inventory_outcomes"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # in an ideal world, this would be ForeignKey("mrv_programs.id")
    # but that MySQL foreign key creation is too dangerous
    # see https://regrow.atlassian.net/wiki/spaces/ENG/pages/3183312918/2024-05-02T04+33+20Z+MRV+production+database+lock+error
    # and https://regrow.atlassian.net/wiki/spaces/ENG/pages/3522560001/2024-11-05T20+18+58Z+MRV+production+database+lock+error
    program_id = Column(Integer, nullable=False, index=True)
    crop_type = Column(String(32), nullable=False)
    total_yield = Column(Float)
    number_of_fields = Column(Integer)
    mapped_acres = Column(Float)
    # total, not per yield, unless noted otherwise
    total_emissions = Column(Float)
    total_emissions_per_bushel = Column(Float)
    indirect_n2o_emissions = Column(Float)
    direct_n2o_emissions = Column(Float)
    soil_ch4_emissions = Column(Float)
    soc = Column(Float)
    soc_per_bushel = Column(Float)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    task_id = Column(String(36), nullable=False)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        index=True,
    )


# for unit testing only, not actual tables at this stage of development
class FieldLevelBiofuelsOutcomes(Base):
    __tablename__ = "mrv_field_level_biofuels_outcomes"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    program_id = Column(Integer, nullable=False, index=True)
    project_id = Column(Integer, nullable=False)
    field_id = Column(Integer, nullable=False)

    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    task_id = Column(String(36), nullable=False)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        index=True,
    )


# for unit testing only, not actual tables at this stage of development
class ProgramLevelBiofuelsOutcomes(Base):
    __tablename__ = "mrv_program_level_biofuels_outcomes"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=False, index=True)
