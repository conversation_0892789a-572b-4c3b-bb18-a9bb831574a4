from typing import Optional, Union

from fastapi import Request
from sqlalchemy import delete, update
from sqlalchemy.dialects.mysql import insert

from helper.helper import run_query
from logger import get_logger
from reported_outcomes.data import get_reporting_excluded_fields_for_program
from reported_outcomes.enums import OutcomeApprovalStatusType
from reported_outcomes.model import (
    CropLevelInventoryOutcomes,
    CropLevelOutcomes,
    FieldLevelInventoryOutcomes,
    FieldLevelOutcomes,
    OutcomeApprovalStatuses,
    ProgramLevelOutcomes,
)
from reported_outcomes.schema import (
    CropLevelInventoryOutcomesResponse,
    CropLevelOutcomesResponse,
    FieldLevelInventoryOutcomesResponse,
    FieldLevelOutcomesResponse,
    OutcomeApprovalStatus,
    ProgramLevelOutcomesResponse,
    ReportedBiofuelsProgramOutcomes,
    ReportedInventoryProgramOutcomes,
    ReportedProgramOutcomes,
)
from root_crud import create, get
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.db import get_dndc_task_by_id

logger = get_logger(__name__)


async def store_program_outcomes(request: Request, program_outcomes: ReportedProgramOutcomes) -> None:
    if program_outcomes.task_id is None:
        raise AssertionError("program outcomes should have a set task_id")

    logger.debug(f"Revoking program {program_outcomes.program_id} approval")
    await revoke_program_outcome_approval(request, program_outcomes.program_id)

    logger.debug(f"Removing previous outcomes for task {program_outcomes.task_id}")
    for mod in [CropLevelOutcomes, FieldLevelOutcomes, ProgramLevelOutcomes]:
        await _remove_model_outcomes_for_task(request, program_outcomes.task_id, mod)

    logger.debug(f"Creating outcomes for task {program_outcomes.task_id}")
    await create.create(
        request=request,
        instances=program_outcomes.crop_outcomes.values(),
        orm_type=CropLevelOutcomes,
        type=CropLevelOutcomesResponse,
    )
    await create.create(
        request=request,
        instances=program_outcomes.field_outcomes.values(),
        orm_type=FieldLevelOutcomes,
        type=FieldLevelOutcomesResponse,
    )
    if program_outcomes.program_outcomes:
        await create.create(
            request=request,
            instances=[program_outcomes.program_outcomes],
            orm_type=ProgramLevelOutcomes,
            type=ProgramLevelOutcomesResponse,
        )
    logger.debug(f"Setting approval of task {program_outcomes.task_id} for program {program_outcomes.program_id}")
    await _set_program_outcome_approval(
        request, program_outcomes.program_id, program_outcomes.task_id, program_outcomes.is_test_run
    )


async def store_inventory_program_outcomes(
    request: Request, program_outcomes: ReportedInventoryProgramOutcomes
) -> None:
    if program_outcomes.task_id is None:
        raise AssertionError("program outcomes should have a set task_id")

    await revoke_program_outcome_approval(request, program_outcomes.program_id)
    for mod in [CropLevelInventoryOutcomes, FieldLevelInventoryOutcomes]:
        await _remove_model_outcomes_for_task(request, program_outcomes.task_id, mod)
    await create.create(
        request=request,
        instances=program_outcomes.crop_inventory_outcomes.values(),
        orm_type=CropLevelInventoryOutcomes,
        type=CropLevelInventoryOutcomesResponse,
    )
    await create.create(
        request=request,
        instances=program_outcomes.field_inventory_outcomes.values(),
        orm_type=FieldLevelInventoryOutcomes,
        type=FieldLevelInventoryOutcomesResponse,
    )
    await _set_program_outcome_approval(
        request, program_outcomes.program_id, program_outcomes.task_id, program_outcomes.is_test_run
    )


async def store_biofuels_program_outcomes(request: Request, program_outcomes: ReportedBiofuelsProgramOutcomes) -> None:
    await revoke_program_outcome_approval(request, program_outcomes.program_id)
    if program_outcomes.task_id is None:
        raise AssertionError("program outcomes should have a set task_id")
    # TO-DO: remove biofuels-y stuff
    # for mod in []:
    #    await _remove_model_outcomes_for_task(request, program_outcomes.task_id, mod)
    # TO-DO:  add stuff like this for biofuels instead of inventory
    # await create.create(
    #    request=request,
    #    instances=program_outcomes.crop_inventory_outcomes.values(),
    #    orm_type=CropLevelInventoryOutcomes,
    #    type=CropLevelInventoryOutcomesResponse,
    # )
    await _set_program_outcome_approval(
        request, program_outcomes.program_id, program_outcomes.task_id, program_outcomes.is_test_run
    )


async def get_crop_outcomes_for_program(request: Request, program_id: int) -> list[CropLevelOutcomes]:
    approved_task = await _get_approved_or_test_task_id_for_program(request, program_id)
    if approved_task is None:
        return []
    return await get.generic_get(
        request=request,
        orm_type=CropLevelOutcomes,
        filters=[
            get.Filter(id_field=CropLevelOutcomes.program_id, ids=[program_id]),
            get.Filter(id_field=CropLevelOutcomes.task_id, ids=[approved_task]),
        ],
        order_by_cols=[CropLevelOutcomes.crop_type],
        empty_return=True,
    )


async def get_crop_inventory_outcomes_for_program(
    request: Request, program_id: int
) -> list[CropLevelInventoryOutcomes]:
    approved_task = await _get_approved_or_test_task_id_for_program(request, program_id)
    if approved_task is None:
        return []
    return await get.generic_get(
        request=request,
        orm_type=CropLevelInventoryOutcomes,
        filters=[
            get.Filter(id_field=CropLevelInventoryOutcomes.program_id, ids=[program_id]),
            get.Filter(id_field=CropLevelInventoryOutcomes.task_id, ids=[approved_task]),
        ],
        order_by_cols=[CropLevelInventoryOutcomes.crop_type],
        empty_return=True,
    )


async def get_program_outcomes_for_program(request: Request, program_id: int) -> list[ProgramLevelOutcomes]:
    approved_task = await _get_approved_or_test_task_id_for_program(request, program_id)
    if approved_task is None:
        return []
    return await get.generic_get(
        request=request,
        orm_type=ProgramLevelOutcomes,
        filters=[
            get.Filter(id_field=ProgramLevelOutcomes.program_id, ids=[program_id]),
            get.Filter(id_field=ProgramLevelOutcomes.task_id, ids=[approved_task]),
        ],
        order_by_cols=[ProgramLevelOutcomes.start_date],
        empty_return=True,
    )


async def get_field_outcomes_for_program(request: Request, program_id: int) -> list[FieldLevelOutcomes]:
    approved_task = await _get_approved_or_test_task_id_for_program(request, program_id)
    if approved_task is None:
        return []
    flos = await get.generic_get(
        request=request,
        orm_type=FieldLevelOutcomes,
        filters=[
            get.Filter(id_field=FieldLevelOutcomes.program_id, ids=[program_id]),
            get.Filter(id_field=FieldLevelOutcomes.task_id, ids=[approved_task]),
        ],
        order_by_cols=[FieldLevelOutcomes.project_id, FieldLevelOutcomes.field_id],
        empty_return=True,
    )
    excluded_fields = get_reporting_excluded_fields_for_program(program_id)
    return [flo for flo in flos if flo.field_id not in excluded_fields]


async def get_field_inventory_outcomes_for_program(
    request: Request, program_id: int
) -> list[FieldLevelInventoryOutcomes]:
    approved_task = await _get_approved_or_test_task_id_for_program(request, program_id)
    if approved_task is None:
        return []
    flos = await get.generic_get(
        request=request,
        orm_type=FieldLevelInventoryOutcomes,
        filters=[
            get.Filter(id_field=FieldLevelInventoryOutcomes.program_id, ids=[program_id]),
            get.Filter(id_field=FieldLevelInventoryOutcomes.task_id, ids=[approved_task]),
        ],
        order_by_cols=[FieldLevelInventoryOutcomes.project_id, FieldLevelInventoryOutcomes.field_id],
        empty_return=True,
    )
    excluded_fields = get_reporting_excluded_fields_for_program(program_id)
    return [flo for flo in flos if flo.field_id not in excluded_fields]


async def revoke_program_outcome_approval(request: Request, program_id: int) -> None:
    async with request.state.sql_session.begin() as s:
        revoke_update = (
            update(OutcomeApprovalStatuses)
            .where(OutcomeApprovalStatuses.program_id == program_id)
            .values(outcome_approval_status=OutcomeApprovalStatusType.REVOKED)
        )
        await run_query(query=revoke_update, s=s)


async def _remove_model_outcomes_for_task(
    request: Request,
    task_id: str,
    model: Union[CropLevelOutcomesResponse, FieldLevelOutcomesResponse, ProgramLevelOutcomesResponse],
) -> None:
    delete_stmt = delete(model).where(model.task_id == task_id)
    async with request.state.sql_session.begin() as s:
        await s.execute(delete_stmt)


async def _set_program_outcome_approval(request: Request, program_id: int, task_id: str, is_test_run: bool) -> None:
    if is_test_run:
        approval_status_type = OutcomeApprovalStatusType.TEST
    else:
        approval_status_type = OutcomeApprovalStatusType.APPROVED

    base_insert = insert(OutcomeApprovalStatuses).values(
        program_id=program_id, task_id=task_id, outcome_approval_status=approval_status_type
    )
    if is_test_run:
        insert_stmt = base_insert.prefix_with("IGNORE")
    else:
        insert_stmt = base_insert.on_duplicate_key_update(
            program_id=program_id, task_id=task_id, outcome_approval_status=approval_status_type
        )

    async with request.state.sql_session.begin() as s:
        await s.execute(insert_stmt)


async def query_outcome_approvals_for_program(request: Request, program_id: int) -> list[OutcomeApprovalStatus]:
    statuses = await get.generic_get(
        request=request,
        orm_type=OutcomeApprovalStatuses,
        filters=[get.Filter(id_field=OutcomeApprovalStatuses.program_id, ids=[program_id])],
        empty_return=True,
    )
    return [
        OutcomeApprovalStatus(
            program_id=stat.program_id, task_id=stat.task_id, outcome_approval_status=stat.outcome_approval_status
        )
        for stat in statuses
    ]


async def get_scenario_service_api_of_selected_results(request: Request, program_id: int) -> ScenariosServiceApi:
    task_id = await _get_approved_or_test_task_id_for_program(request, program_id)
    if task_id is None:
        raise ValueError(
            f"Program {program_id} does not have an associated approved task and therefore cannot have selected results"
        )
    task = await get_dndc_task_by_id(request, task_id)
    return task.scenarios_service_api


async def _get_approved_or_test_task_id_for_program(request: Request, program_id: int) -> Optional[str]:
    result = await get.generic_get(
        request=request,
        orm_type=OutcomeApprovalStatuses,
        filters=[
            get.Filter(id_field=OutcomeApprovalStatuses.program_id, ids=[program_id]),
            get.Filter(
                id_field=OutcomeApprovalStatuses.outcome_approval_status, ids=[OutcomeApprovalStatusType.APPROVED]
            ),
        ],
        empty_return=True,
    )
    if len(result) > 0:
        return result[0].task_id

    result = await get.generic_get(
        request=request,
        orm_type=OutcomeApprovalStatuses,
        filters=[
            get.Filter(id_field=OutcomeApprovalStatuses.program_id, ids=[program_id]),
            get.Filter(id_field=OutcomeApprovalStatuses.outcome_approval_status, ids=[OutcomeApprovalStatusType.TEST]),
        ],
        empty_return=True,
    )
    if len(result) > 0:
        return result[0].task_id
    return None
