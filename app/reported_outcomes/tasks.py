import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from logger import get_logger
from reported_outcomes.outcome_construction_methods import (
    build_raw_combined_zip_for_program,
)
from reported_outcomes.storage import upload_combined_outcomes

logger = get_logger(__name__)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_build_of_combined_zip_for_program(
    self: DBTask,
    *,
    program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    logger.info(f"getting combined zip outcomes for {program_id}")
    zip_contents = await build_raw_combined_zip_for_program(request, program_id)
    logger.info(f"uploading combined zip outcomes for {program_id}")
    await upload_combined_outcomes(request, program_id, zip_contents)
