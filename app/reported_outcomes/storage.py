from typing import Any

from fastapi import Request

from config import get_settings
from gcloud_client.storage.download import get_object
from gcloud_client.storage.upload import upload_object
from logger import get_logger
from reported_outcomes.constants import OUTCOMES_DIR, ZIP_CONTENT_TYPE

settings = get_settings()
logger = get_logger(__name__)


# interface


async def upload_combined_outcomes(request: Request, program_id: int, file_data: bytes) -> None:
    object_name = _get_object_name(program_id)
    bucket_name = settings.GCLOUD_STORAGE_PRIVATE_BUCKET
    logger.info(f"uploading combined outcomes of {program_id} to {object_name} in bucket {bucket_name}")
    await upload_object(
        request=request,
        bucket_name=bucket_name,
        data=file_data,
        object_name=object_name,
        content_type=ZIP_CONTENT_TYPE,
    )


async def download_combined_outcomes(request: Request, program_id: int) -> Any | None:
    object_name = _get_object_name(program_id)
    outcomes = None
    try:
        outcomes = get_object(request, settings.GCLOUD_STORAGE_PRIVATE_BUCKET, object_name)
    except Exception:
        logger.info(f"failed to find combined outcomes for program {program_id}")
        outcomes = None
    return outcomes


# internals


def _get_object_name(program_id: int) -> str:
    return f"{OUTCOMES_DIR}/combined_outcomes_for_{program_id}.zip"
