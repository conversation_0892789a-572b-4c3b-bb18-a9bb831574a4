from config import get_settings
from slack_integration.enums import MRVSlackChannel

settings = get_settings()

MRV_SLACK_CHANNEL_TO_WEBHOOK_URL = {
    MRVSlackChannel.MEASUREMENT_ELIGIBILITY: settings.SLACK_WEBHOOK_URL_MEASUREMENT_ELIGIBILITY,
    MRVSlackChannel.MRV_CRON_ALERTS: settings.SLACK_WEBHOOK_URL_CRON_ALERTS,
    MRVSlackChannel.MRV_DATA_EXPORT_ALERTS: settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
    MRVSlackChannel.MRV_EXPLORE_ALERTS: settings.SLACK_WEBHOOK_URL_EXPLORE_ALERTS,
    MRVSlackChannel.MRV_MEASURE_ALERTS: settings.SLACK_WEBHOOK_URL_MEASURE_ALERTS,
    MRVSlackChannel.MRV_MONITOR_ALERTS: settings.SLACK_WEBHOOK_URL_MONITOR_ALERTS,
}
