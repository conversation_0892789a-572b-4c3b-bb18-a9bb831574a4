{% if fields|length >= 80 %}
{{ '{:.<20}'.format("Farm") }} {{ '{:.^20}'.format("Field") }} {{ '{:.^12}'.format("Area") }} {{ '{:.^40}'.format("Practice") }}
{{ '{:.<20}'.format("") }} {{ '{:.<20}'.format("") }} {{ '{:.^12}'.format(user_units) }} {{ '{:.<40}'.format("") }}
{% for row in fields[80:120] %}
{{ '{: <25}'.format(row.note["farm_name"])[:20] }} {{ '{: <20}'.format(row.note.get('field_name'))[:20] }} {{ '{:>12.2f}'.format(row.field_area) }} {{ '{: <20}'.format(row.note.get('ASSIGN_PRACTICES','')) }}{% endfor %}

{% if fields|length < 120 %}
fields 80 - {{ fields|length }} fields
{% else %}
fields 80 - 120 of {{ fields|length }} fields
{% endif %}

{% endif %}
