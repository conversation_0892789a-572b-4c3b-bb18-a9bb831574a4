from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from values.methods import get_previous_and_current_commodity_crop_by_year


async def test_get_previous_and_current_commodity_crop_by_year(app_request, mdl):
    program = await mdl.Programs(crediting_year=2022)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.SUMMER_CROPS, enabled=True)
    crop_type_attribute = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.summer_crop_type, enabled=True
    )
    record_year_attribute = await mdl.Attribute(parent_stage_id=stage.id, type=AttributeTypes.record_year, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.Values(
        attribute_id=record_year_attribute.id,
        field_id=field.id,
        value="2023",
        row_id=0,
        progress="monitoring",
    )
    await mdl.Values(
        attribute_id=record_year_attribute.id,
        field_id=field.id,
        value="2022",
        row_id=1,
        progress="monitoring",
    )
    await mdl.Values(
        attribute_id=record_year_attribute.id,
        field_id=field.id,
        value="2021",
        row_id=2,
        progress="monitoring",
    )
    value1 = await mdl.Values(
        attribute_id=crop_type_attribute.id,
        field_id=field.id,
        value="corn",
        row_id=0,
        progress="monitoring",
    )
    value2 = await mdl.Values(
        attribute_id=crop_type_attribute.id,
        field_id=field.id,
        value="soybean",
        row_id=1,
        progress="monitoring",
    )
    await mdl.Values(
        attribute_id=crop_type_attribute.id,
        field_id=field.id,
        value="rice",
        row_id=2,
        progress="monitoring",
    )

    result = await get_previous_and_current_commodity_crop_by_year(
        request=app_request,
        field_id=field.id,
        program_year=2022,
    )

    assert result == {
        "previous_commodity_crop": value2.id,
        "current_commodity_crop": value1.id,
    }
