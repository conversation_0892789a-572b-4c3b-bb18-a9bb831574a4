from dataclasses import dataclass
from typing import Any, Dict, List

import pytest

from phases.enums import AttributeTypes, StageTypes
from phases.model import Attribute, StageWithPhase
from values.constants import REDUCED_TILL
from values.crud import update_row_ids
from values.enums import ImportDataSources, ProgressChoices
from values.model import Values
from values.schema import ValuesRequestAttributeFieldWithReadOnly


@dataclass
class Case:
    new_row_to_values: Dict[int, List[ValuesRequestAttributeFieldWithReadOnly]]
    expected_res: List[ValuesRequestAttributeFieldWithReadOnly]


def _generate_value_model(value: Any, attribute_id: int, row_id: int, field_id: int) -> Values:
    return Values(
        value=value,
        attribute_id=attribute_id,
        row_id=row_id,
        locked=True,
        confirmed=True,
        progress=ProgressChoices.monitoring,
        field_id=field_id,
        source=ImportDataSources.other_fms,
    )


def _generate_value(
    value: Any, attribute_id: int, row_id: int, field_id: int
) -> ValuesRequestAttributeFieldWithReadOnly:
    return ValuesRequestAttributeFieldWithReadOnly(
        value=value,
        attribute_id=attribute_id,
        row_id=row_id,
        locked=True,
        confirmed=True,
        progress=ProgressChoices.monitoring,
        field_id=field_id,
        source=ImportDataSources.other_fms,
    )


STAGES = {
    1: StageWithPhase(
        id=1,
        type_=StageTypes.SUMMER_CROPS,
    ),
    2: StageWithPhase(
        id=2,
        type_=StageTypes.WINTER_CROPS,
    ),
    3: StageWithPhase(
        id=3,
        type_=StageTypes.TILLAGE,
    ),
    4: StageWithPhase(id=4, type_=StageTypes.NUTRIENT_MGMT),
}
ATTRIBUTES = {
    # summer crop attributes
    10: Attribute(id=10, type=AttributeTypes.record_year, parent_stage_id=1),
    11: Attribute(id=11, type=AttributeTypes.crop_type, parent_stage_id=1),
    12: Attribute(id=12, type=AttributeTypes.planting_date, parent_stage_id=1),
    13: Attribute(id=13, type=AttributeTypes.harvest_date, parent_stage_id=1),
    14: Attribute(id=14, type=AttributeTypes.residue_harvested, parent_stage_id=1),
    15: Attribute(id=15, type=AttributeTypes.irrigation_method, parent_stage_id=1),
    # winter crop attributes
    20: Attribute(id=20, type=AttributeTypes.record_year, parent_stage_id=2),
    21: Attribute(id=21, type=AttributeTypes.crop_type, parent_stage_id=2),
    22: Attribute(id=22, type=AttributeTypes.planting_date, parent_stage_id=2),
    23: Attribute(id=23, type=AttributeTypes.harvest_date, parent_stage_id=2),
    # tillage attributes
    30: Attribute(id=30, type=AttributeTypes.record_year, parent_stage_id=3),
    31: Attribute(id=31, type=AttributeTypes.tillage_period, parent_stage_id=3),
    32: Attribute(id=32, type=AttributeTypes.tillage_practice, parent_stage_id=3),
    33: Attribute(id=33, type=AttributeTypes.tillage_date, parent_stage_id=3),
    34: Attribute(id=34, type=AttributeTypes.tillage_depth, parent_stage_id=3),
    # application attributes
    40: Attribute(id=40, type=AttributeTypes.record_year, parent_stage_id=4),
    41: Attribute(id=41, type=AttributeTypes.application_product, parent_stage_id=4),
    42: Attribute(id=42, type=AttributeTypes.application_date, parent_stage_id=4),
}
EXISTING_ROW_TO_VALUES = {
    # field 1, stage 1, row 100
    (1, 1, 100): [
        _generate_value_model(value="2024", attribute_id=10, row_id=100, field_id=1),
        _generate_value_model(value="corn", attribute_id=11, row_id=100, field_id=1),
        _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=1),
    ],
    # field 1, stage 3, row 200
    (1, 3, 200): [
        _generate_value_model(value="2024", attribute_id=30, row_id=200, field_id=1),
        _generate_value_model(value="Fall", attribute_id=31, row_id=200, field_id=1),
        _generate_value_model(value=REDUCED_TILL, attribute_id=32, row_id=200, field_id=1),
        _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=200, field_id=1),
        _generate_value_model(value=1, attribute_id=34, row_id=200, field_id=1),
    ],
    # field 1, stage 4, row 300
    (1, 4, 300): [
        _generate_value_model(value="2024", attribute_id=40, row_id=300, field_id=1),
        _generate_value_model(value="fertilizer", attribute_id=41, row_id=300, field_id=1),
        _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=300, field_id=1),
    ],
    # field 2, stage 1, row 100
    (2, 1, 100): [
        _generate_value_model(value="2024", attribute_id=10, row_id=100, field_id=2),
        _generate_value_model(value="corn", attribute_id=11, row_id=100, field_id=2),
        _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=2),
    ],
}


@pytest.mark.asyncio
async def test_new_values():
    cases = [
        # crop with different crop type
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=101, field_id=1),
            ],
        ),
        # crop with different year
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2025", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2025-01-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2025", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2025-01-01T00:00:00.000Z", attribute_id=12, row_id=101, field_id=1),
            ],
        ),
        # tillage with different date
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2024-01-02T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=123, attribute_id=34, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=201, field_id=1),
                _generate_value(value="2024-01-02T00:00:00.000Z", attribute_id=33, row_id=201, field_id=1),
                _generate_value(value=123, attribute_id=34, row_id=201, field_id=1),
            ],
        ),
        # application with different date
        Case(
            new_row_to_values={
                (1, 4, 0): [
                    _generate_value(value="2024", attribute_id=40, row_id=0, field_id=1),
                    _generate_value(value="fertilizer", attribute_id=41, row_id=0, field_id=1),
                    _generate_value(value="2024-01-02T00:00:00.000Z", attribute_id=42, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=40, row_id=301, field_id=1),
                _generate_value(value="fertilizer", attribute_id=41, row_id=301, field_id=1),
                _generate_value(value="2024-01-02T00:00:00.000Z", attribute_id=42, row_id=301, field_id=1),
            ],
        ),
        # application with different product
        Case(
            new_row_to_values={
                (1, 4, 0): [
                    _generate_value(value="2024", attribute_id=40, row_id=0, field_id=1),
                    _generate_value(value="water", attribute_id=41, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=40, row_id=301, field_id=1),
                _generate_value(value="water", attribute_id=41, row_id=301, field_id=1),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=301, field_id=1),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_existing_values():
    cases = [
        # crop
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=1),
            ],
        ),
        # tillage
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=123, attribute_id=34, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=200, field_id=1),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=200, field_id=1),
                _generate_value(value=123, attribute_id=34, row_id=200, field_id=1),
            ],
        ),
        # application
        Case(
            new_row_to_values={
                (1, 4, 0): [
                    _generate_value(value="2024", attribute_id=40, row_id=0, field_id=1),
                    _generate_value(value="fertilizer", attribute_id=41, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=40, row_id=300, field_id=1),
                _generate_value(value="fertilizer", attribute_id=41, row_id=300, field_id=1),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=300, field_id=1),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_different_field():
    cases = [
        # crop
        Case(
            new_row_to_values={
                (3, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=3),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=3),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=3),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=0, field_id=3),
                _generate_value(value="corn", attribute_id=11, row_id=0, field_id=3),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=3),
            ],
        ),
        # tillage
        Case(
            new_row_to_values={
                (3, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=3),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=3),
                    _generate_value(value=123, attribute_id=34, row_id=0, field_id=3),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=0, field_id=3),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=3),
                _generate_value(value=123, attribute_id=34, row_id=0, field_id=3),
            ],
        ),
        # application
        Case(
            new_row_to_values={
                (3, 4, 0): [
                    _generate_value(value="2024", attribute_id=40, row_id=0, field_id=3),
                    _generate_value(value="fertilizer", attribute_id=41, row_id=0, field_id=3),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=0, field_id=3),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=40, row_id=0, field_id=3),
                _generate_value(value="fertilizer", attribute_id=41, row_id=0, field_id=3),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=42, row_id=0, field_id=3),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_different_stage():
    cases = [
        Case(
            new_row_to_values={
                (1, 2, 0): [
                    _generate_value(value="2024", attribute_id=20, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=21, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=22, row_id=0, field_id=1),
                ]
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=20, row_id=0, field_id=1),
                _generate_value(value="corn", attribute_id=21, row_id=0, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=22, row_id=0, field_id=1),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_multiple_fields_new_values():
    cases = [
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ],
                (2, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=2),
                    _generate_value(value="barley", attribute_id=11, row_id=0, field_id=2),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=2),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=101, field_id=1),
                _generate_value(value="2024", attribute_id=10, row_id=101, field_id=2),
                _generate_value(value="barley", attribute_id=11, row_id=101, field_id=2),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=101, field_id=2),
            ],
        )
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_multiple_fields_existing_values():
    cases = [
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ],
                (2, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=2),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=2),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=2),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=1),
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=2),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=2),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=2),
            ],
        )
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


@pytest.mark.asyncio
async def test_multiple_fields_new_and_existing_values():
    cases = [
        Case(
            new_row_to_values={
                # new values
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=0, field_id=1),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=1),
                ],
                # existing values
                (2, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=2),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=2),
                    _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=0, field_id=2),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=101, field_id=1),
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=2),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=2),
                _generate_value(value="2024-02-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=2),
            ],
        )
    ]
    for case in cases:
        res = await update_row_ids(EXISTING_ROW_TO_VALUES, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


async def test_crop_rows():
    existing_row_to_values_tillage = {
        # complete row
        (1, 1, 100): [
            _generate_value_model(value="2024", attribute_id=10, row_id=100, field_id=1),
            _generate_value_model(value="corn", attribute_id=11, row_id=100, field_id=1),
            _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=12, row_id=100, field_id=1),
        ],
        # starter row
        (1, 1, 101): [
            _generate_value_model(value="2000", attribute_id=10, row_id=101, field_id=1),
        ],
        # starter row
        (1, 1, 102): [
            _generate_value_model(value="2001", attribute_id=10, row_id=102, field_id=1),
            _generate_value_model(value="No residue harvested", attribute_id=14, row_id=102, field_id=1),
            _generate_value_model(value="No irrigation", attribute_id=15, row_id=102, field_id=1),
        ],
        # non-starter row with planting date
        (1, 1, 103): [
            _generate_value_model(value="2002", attribute_id=10, row_id=103, field_id=1),
            _generate_value_model(value="2002-01-01T00:00:00.000Z", attribute_id=12, row_id=103, field_id=1),
        ],
    }
    cases = [
        # exact match + year match + same year match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=10, field_id=1),
                ],
                (1, 1, 1): [
                    _generate_value(value="2000", attribute_id=10, row_id=1, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=1, field_id=1),
                ],
                (1, 1, 2): [
                    _generate_value(value="2000", attribute_id=10, row_id=2, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=2, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=1),
                _generate_value(value="2000", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2000", attribute_id=10, row_id=106, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=106, field_id=1),
            ],
        ),
        # year match + same year match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2000", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                ],
                (1, 1, 1): [
                    _generate_value(value="2000", attribute_id=10, row_id=1, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2000", attribute_id=10, row_id=105, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=105, field_id=1),
            ],
        ),
        # year match + different year match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2000", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                ],
                (1, 1, 1): [
                    _generate_value(value="2001", attribute_id=10, row_id=1, field_id=1),
                    _generate_value(value="barley", attribute_id=11, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="2001", attribute_id=10, row_id=102, field_id=1),
                _generate_value(value="barley", attribute_id=11, row_id=102, field_id=1),
            ],
        ),
        # exact match + no match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2024", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                ],
                (1, 1, 1): [
                    _generate_value(value="1999", attribute_id=10, row_id=1, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=10, row_id=100, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=100, field_id=1),
                _generate_value(value="1999", attribute_id=10, row_id=105, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=105, field_id=1),
            ],
        ),
        # year match + no match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2000", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                ],
                (1, 1, 1): [
                    _generate_value(value="1999", attribute_id=10, row_id=1, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=10, row_id=101, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=101, field_id=1),
                _generate_value(value="1999", attribute_id=10, row_id=105, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=105, field_id=1),
            ],
        ),
        # no match
        Case(
            new_row_to_values={
                (1, 1, 0): [
                    _generate_value(value="2002", attribute_id=10, row_id=0, field_id=1),
                    _generate_value(value="corn", attribute_id=11, row_id=0, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2002", attribute_id=10, row_id=104, field_id=1),
                _generate_value(value="corn", attribute_id=11, row_id=104, field_id=1),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(existing_row_to_values_tillage, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res


async def test_tillage_rows():
    existing_row_to_values_tillage = {
        # complete row
        (1, 3, 200): [
            _generate_value_model(value="2024", attribute_id=30, row_id=200, field_id=1),
            _generate_value_model(value="Fall", attribute_id=31, row_id=200, field_id=1),
            _generate_value_model(value=REDUCED_TILL, attribute_id=32, row_id=200, field_id=1),
            _generate_value_model(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=200, field_id=1),
            _generate_value_model(value=1, attribute_id=34, row_id=200, field_id=1),
        ],
        # starter row
        (1, 3, 201): [
            _generate_value_model(value="2000", attribute_id=30, row_id=201, field_id=1),
        ],
        # starter row
        (1, 3, 202): [
            _generate_value_model(value="2001", attribute_id=30, row_id=202, field_id=1),
            _generate_value_model(value="Fall", attribute_id=31, row_id=202, field_id=1),
            _generate_value_model(value=REDUCED_TILL, attribute_id=32, row_id=202, field_id=1),
        ],
        # non-starter row
        (1, 3, 203): [
            _generate_value_model(value="2002", attribute_id=30, row_id=203, field_id=1),
            _generate_value_model(value="2002-01-01T00:00:00.000Z", attribute_id=33, row_id=203, field_id=1),
        ],
        # non-starter row
        (1, 3, 204): [
            _generate_value_model(value="2003", attribute_id=30, row_id=204, field_id=1),
            _generate_value_model(value=1, attribute_id=34, row_id=204, field_id=1),
        ],
    }
    cases = [
        # exact match + year match + same year match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
                (1, 3, 1): [
                    _generate_value(value="2000", attribute_id=30, row_id=1, field_id=1),
                    _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=1, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=1, field_id=1),
                ],
                (1, 3, 2): [
                    _generate_value(value="2000", attribute_id=30, row_id=2, field_id=1),
                    _generate_value(value="2000-01-02T00:00:00.000Z", attribute_id=33, row_id=2, field_id=1),
                    _generate_value(value=2, attribute_id=34, row_id=2, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=200, field_id=1),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=200, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=200, field_id=1),
                _generate_value(value="2000", attribute_id=30, row_id=201, field_id=1),
                _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=201, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=201, field_id=1),
                _generate_value(value="2000", attribute_id=30, row_id=207, field_id=1),
                _generate_value(value="2000-01-02T00:00:00.000Z", attribute_id=33, row_id=207, field_id=1),
                _generate_value(value=2, attribute_id=34, row_id=207, field_id=1),
            ],
        ),
        # year match + same year match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2000", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
                (1, 3, 1): [
                    _generate_value(value="2000", attribute_id=30, row_id=1, field_id=1),
                    _generate_value(value="2000-01-02T00:00:00.000Z", attribute_id=33, row_id=1, field_id=1),
                    _generate_value(value=2, attribute_id=34, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=30, row_id=201, field_id=1),
                _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=201, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=201, field_id=1),
                _generate_value(value="2000", attribute_id=30, row_id=206, field_id=1),
                _generate_value(value="2000-01-02T00:00:00.000Z", attribute_id=33, row_id=206, field_id=1),
                _generate_value(value=2, attribute_id=34, row_id=206, field_id=1),
            ],
        ),
        # year match + different year match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2000", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
                (1, 3, 1): [
                    _generate_value(value="2001", attribute_id=30, row_id=1, field_id=1),
                    _generate_value(value="2001-01-01T00:00:00.000Z", attribute_id=33, row_id=1, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=30, row_id=201, field_id=1),
                _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=201, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=201, field_id=1),
                _generate_value(value="2001", attribute_id=30, row_id=202, field_id=1),
                _generate_value(value="2001-01-01T00:00:00.000Z", attribute_id=33, row_id=202, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=202, field_id=1),
            ],
        ),
        # exact match + no year match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
                (1, 3, 1): [
                    _generate_value(value="1999", attribute_id=30, row_id=1, field_id=1),
                    _generate_value(value="1999-01-01T00:00:00.000Z", attribute_id=33, row_id=1, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=200, field_id=1),
                _generate_value(value="2024-01-01T00:00:00.000Z", attribute_id=33, row_id=200, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=200, field_id=1),
                _generate_value(value="1999", attribute_id=30, row_id=206, field_id=1),
                _generate_value(value="1999-01-01T00:00:00.000Z", attribute_id=33, row_id=206, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=206, field_id=1),
            ],
        ),
        # year match + no year match match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2000", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
                (1, 3, 1): [
                    _generate_value(value="1999", attribute_id=30, row_id=1, field_id=1),
                    _generate_value(value="1999-01-01T00:00:00.000Z", attribute_id=33, row_id=1, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=1, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2000", attribute_id=30, row_id=201, field_id=1),
                _generate_value(value="2000-01-01T00:00:00.000Z", attribute_id=33, row_id=201, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=201, field_id=1),
                _generate_value(value="1999", attribute_id=30, row_id=206, field_id=1),
                _generate_value(value="1999-01-01T00:00:00.000Z", attribute_id=33, row_id=206, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=206, field_id=1),
            ],
        ),
        # no match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2002", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2002-01-02T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2002", attribute_id=30, row_id=205, field_id=1),
                _generate_value(value="2002-01-02T00:00:00.000Z", attribute_id=33, row_id=205, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=205, field_id=1),
            ],
        ),
        # no match
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2003", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2003-01-01T00:00:00.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2003", attribute_id=30, row_id=205, field_id=1),
                _generate_value(value="2003-01-01T00:00:00.000Z", attribute_id=33, row_id=205, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=205, field_id=1),
            ],
        ),
        # exact match different time
        Case(
            new_row_to_values={
                (1, 3, 0): [
                    _generate_value(value="2024", attribute_id=30, row_id=0, field_id=1),
                    _generate_value(value="2024-01-01T00:00:01.000Z", attribute_id=33, row_id=0, field_id=1),
                    _generate_value(value=1, attribute_id=34, row_id=0, field_id=1),
                ],
            },
            expected_res=[
                _generate_value(value="2024", attribute_id=30, row_id=200, field_id=1),
                _generate_value(value="2024-01-01T00:00:01.000Z", attribute_id=33, row_id=200, field_id=1),
                _generate_value(value=1, attribute_id=34, row_id=200, field_id=1),
            ],
        ),
    ]
    for case in cases:
        res = await update_row_ids(existing_row_to_values_tillage, case.new_row_to_values, STAGES, ATTRIBUTES)
        assert res == case.expected_res
