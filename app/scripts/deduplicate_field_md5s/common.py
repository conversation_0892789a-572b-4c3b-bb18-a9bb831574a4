from dataclasses import dataclass

from pymysql.cursors import DictCursor

# we actually do want print statements, so add some indirection to pass checking
PRINTFUNC = print


@dataclass
class KmlGroup:
    id: int
    name: str
    md5: str
    kml_id: int


# locate all duplicate md5s - EXCLUDING the one we want to keep
def gather_duplicate_kml_groups(keep_field_md5: str, cursor: DictCursor) -> list[KmlGroup]:
    query = f"""
        SELECT kg.id, kg.name, kf.md5, kg.kml_id
        FROM kml_files kf
            JOIN kml_groups kg ON kf.id = kg.kml_id
        WHERE kf.geometry = (SELECT geometry FROM kml_files WHERE md5 = '{keep_field_md5}')
            AND kf.md5 != '{keep_field_md5}';
    """  # nosec
    cursor.execute(query)
    rows = cursor.fetchall()
    duplicate_kml_groups = [
        KmlGroup(id=row["id"], name=row["name"], md5=row["md5"], kml_id=row["kml_id"]) for row in rows
    ]
    PRINTFUNC(f"found {len(duplicate_kml_groups)} duplicate kml_groups matching md5 {keep_field_md5}")
    return duplicate_kml_groups
