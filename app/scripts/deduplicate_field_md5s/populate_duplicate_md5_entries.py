import itertools
import os

ENV = os.environ.get("ENV", "dev")

DB_HOST = f"flurosense-db.us.{ENV}.internal"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
DB_NAME = f"flurosense-{ENV}"

import pymysql
from pymysql.connections import Connection
from pymysql.cursors import DictCursor

from scripts.deduplicate_field_md5s.common import gather_duplicate_kml_groups, KmlGroup

# we actually do want print statements, so add some indirection to pass checking
PRINTFUNC = print


def build_mrv_field_transitions(
    md5s_to_replace: list[str], keep_field_md5: str, cursor: DictCursor
) -> list[tuple[int, str, str]]:
    formatted_md5s = ",".join([f"{md5}" for md5 in md5s_to_replace])
    query = f"""
        SELECT id, md5
        FROM mrv_fields
        WHERE md5 in ({formatted_md5s})
    """  # nosec
    cursor.execute(query)
    rows = cursor.fetchall()
    return [(row["id"], row["md5"], keep_field_md5) for row in rows]


def store_mrv_field_transitions(transitions: list[tuple[int, str, str]], cursor: DictCursor) -> None:
    for t in transitions:
        stmt = f"INSERT INTO mrv_field_md5_transitions(field_id, previous_md5, next_md5) VALUES ({t[0]}, '{t[1]}', '{t[2]}')"  # nosec
        cursor.execute(stmt)
    connection.commit()


def populate_entries_for_retained_md5(keep_field_md5: str, cursor: DictCursor, connection: Connection) -> None:
    duplicate_groups = gather_duplicate_kml_groups(keep_field_md5, cursor)
    duplicate_md5s = [dup.md5 for dup in duplicate_groups]
    transitions = build_mrv_field_transitions(duplicate_md5s, keep_field_md5, cursor)
    store_mrv_field_transitions(transitions, cursor)


if __name__ == "__main__":
    if DB_USER == "":
        raise AssertionError("You must set DB_USER and associated variables before running script")

    # if we find we're using this script often (heaven help us),
    # then we can make an environment variable for this, but for now just hardcode it
    retained_md5s = ["465b51d89e4664d5a0898e354fd0d9f2"]
    if not retained_md5s:
        raise AssertionError("You must select which MD5s you will be retaining in this script")

    connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor(pymysql.cursors.DictCursor)  # Ensure results are returned as dictionaries

    for md5 in retained_md5s:
        populate_entries_for_retained_md5(md5, cursor, connection)
