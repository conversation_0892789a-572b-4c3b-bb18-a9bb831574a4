import os
from dataclasses import dataclass
from typing import Any

import pymysql
from pymysql.connections import Connection
from pymysql.cursors import DictCursor

from scripts.deduplicate_field_md5s.common import gather_duplicate_kml_groups, KmlGroup

# we actually do want print statements, so add some indirection to pass checking
PRINTFUNC = print


ENV = os.environ.get("ENV", "dev")

DB_HOST = f"flurosense-db.us.{ENV}.internal"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
DB_NAME = f"flurosense-{ENV}"


# fetch the kml_groups.id and the kml_files.id for the md5 we want to keep - should only be ONE
def build_kept_kml_file_id(keep_field_md5: str, cursor: DictCursor) -> int:
    query = f"SELECT id FROM kml_files WHERE md5 = '{keep_field_md5}' LIMIT 1;"  # nosec
    cursor.execute(query)
    row = cursor.fetchone()
    if row is None:
        raise AssertionError("unexpected missing result")
    keep_kml_files_id = row["id"]
    PRINTFUNC(f"keeping kml_files.id {keep_kml_files_id} for md5 {keep_field_md5}")
    return keep_kml_files_id


def select_kept_md5s(cursor: DictCursor) -> list[str]:
    query = "SELECT DISTINCT new_md5 FROM mrv_field_md5_transitions;"
    cursor.execute(query)
    rows = cursor.fetchall()
    return [r["new_md5"] for r in rows]


def get_field_md5_transitions(new_md5: str, cursor: DictCursor) -> list[dict[str, Any]]:
    query = (
        f"SELECT field_id, previous_md5, next_md5 FROM mrv_field_md5_transitions where next_md5='{new_md5}';"  # nosec
    )
    cursor.execute(query)
    rows = cursor.fetchall()
    return list(rows)


def update_kml_group(kg: KmlGroup, keep_kml_files_id: int, cursor: DictCursor) -> None:
    query = f"UPDATE kml_groups SET kml_id = {keep_kml_files_id} WHERE id = {kg.id};"  # nosec
    PRINTFUNC(query)
    cursor.execute(query)


def transition_kml_to_new_md5(new_md5: str, cursor: DictCursor, connection: Connection) -> None:
    keep_kml_files_id = build_kept_kml_file_id(new_md5, cursor)
    duplicate_groups = gather_duplicate_kml_groups(new_md5, cursor)
    for dg in duplicate_groups:
        update_kml_group(dg, keep_kml_files_id, cursor)
    connection.commit()


def transition_mrv_fields_to_new_md5(new_md5: str, cursor: DictCursor, connection: Connection) -> None:
    field_transitions = get_field_md5_transitions(new_md5, cursor)
    for ft in field_transitions:
        query = f"UPDATE mrv_fields SET md5='{ft['next_md5']}' WHERE md5='{ft['previous_md5']}' and id={ft['field_id']}"  # nosec
        cursor.execute(query)
    connection.commit()


def transition_to_new_md5(new_md5: str, cursor: DictCursor, connection: Connection) -> None:
    transition_kml_to_new_md5(new_md5, cursor, connection)
    transition_mrv_fields_to_new_md5(new_md5, cursor, connection)


if __name__ == "__main__":
    if DB_USER == "":
        raise AssertionError("You must set DB_USER and associated variables before running script")

    connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor(pymysql.cursors.DictCursor)  # Ensure results are returned as dictionaries
    new_md5s = select_kept_md5s(cursor)
    for new_md5 in new_md5s:
        transition_to_new_md5(new_md5, cursor, connection)
