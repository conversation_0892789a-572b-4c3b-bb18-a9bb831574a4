import argparse
import json
import os
import time
from dataclasses import dataclass
from typing import Any, Generator, List

import MySQLdb
import requests
from MySQLdb.cursors import <PERSON>ursor
from requests import Response

ENV = os.environ.get("ENV", "dev")

DB_HOST = f"flurosense-db.us.{ENV}.internal"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
DB_NAME = f"flurosense-{ENV}"

if ENV == "prod":
    BOUNDARIES_SERVICE_HOST = "boundaries-service.int.prod.regrow.cloud"
else:
    BOUNDARIES_SERVICE_HOST = "boundaries-service-qa.int.dev.regrow.cloud"

# the time query param is a hack to prevent the query from being cached
SEARCH_BOUNDARY_BASE_URL = f"http://{BOUNDARIES_SERVICE_HOST}/search?time={int(time.time())}"  # NOSONAR
CREATE_BOUNDARY_BASE_URL = f"http://{BOUNDARIES_SERVICE_HOST}/collections/fields_flurosense/items"  # NOSONAR

# we actually do want print statements, so add some indirection to pass checking
PRINTFUNC = print


@dataclass
class KMLFile:
    kml_id: int
    geojson: dict
    md5: str


@dataclass
class KMLFileUpdate:
    kml_id: int
    md5: str
    area: float


def get_md5s_by_project_ids(project_ids: List[int], cursor: Cursor) -> List[str]:
    placeholders = ",".join(["%s"] * len(project_ids))
    query = f"""SELECT kf.md5
    FROM kml_files kf
    JOIN kml_groups kg ON kf.id = kg.kml_id
    JOIN mrv_fields mf ON kg.id = mf.fs_field_id
    WHERE mf.parent_project_id IN ({placeholders})"""  # nosec

    cursor.execute(query, tuple(project_ids))
    res = cursor.fetchall()

    md5s = [single_res[0] for single_res in res]
    return md5s


def get_md5s_by_program_id(program_id: int, cursor: Cursor) -> List[str]:
    query = """SELECT mf.md5
    FROM mrv_fields mf
    JOIN mrv_projects prj ON mf.parent_project_id = prj.id
    JOIN mrv_programs prg ON prj.program_id = prg.id
    WHERE mf.deleted_at IS NULL
        AND prg.id = %s"""

    cursor.execute(query, (program_id,))
    res = cursor.fetchall()

    md5s = [single_res[0] for single_res in res]
    return md5s


def get_missing_boundary_ids(md5s: List[str]) -> List[str]:
    existing_md5s = []
    for chunk in _lst_to_chunks(md5s, 5):
        url = SEARCH_BOUNDARY_BASE_URL
        for md5 in chunk:
            url = url + "&ids=" + md5
        res = requests.get(url, timeout=120)
        if res.status_code > 299:
            PRINTFUNC(f"get_missing_md5s got status code {res.status_code}: {res.json()} for MD5s: {chunk}")
            continue
        features = res.json()["features"]
        for feature in features:
            existing_md5s.append(feature["id"])
    missing_md5s = set(md5s) - set(existing_md5s)
    return list(missing_md5s)


def get_missing_kml_files(missing_md5s: List[str], cursor: Cursor) -> List[KMLFile]:
    placeholders = ",".join(["%s"] * len(missing_md5s))
    query = f"""SELECT id, ST_AsGeoJSON(geometry), md5 AS geojson
    FROM kml_files
    WHERE md5 IN ({placeholders})"""  # nosec

    cursor.execute(query, tuple(missing_md5s))
    res = cursor.fetchall()

    kml_files = [
        KMLFile(kml_id=single_res[0], geojson=json.loads(single_res[1]), md5=single_res[2]) for single_res in res
    ]
    return kml_files


def get_geometry_from_prod(md5: str) -> dict | None:
    url = f"http://boundaries-service.int.prod.regrow.cloud/search?time={int(time.time())}&ids={md5}"  # NOSONAR
    res = requests.get(url, timeout=120)
    if res.status_code > 299:
        PRINTFUNC(f"get_geometry_from_prod got status code {res.status_code}: {res.text} for MD5: {md5}")
        return None
    features = res.json().get("features", [])
    if not features:
        return None
    return features[0]["geometry"]


def post_geometry(kml_file: KMLFile) -> Response:
    body = {
        "type": "FeatureCollection",
        "features": [{"type": "Feature", "geometry": kml_file.geojson, "properties": None}],
    }
    return requests.post(CREATE_BOUNDARY_BASE_URL, json=body, timeout=120)


def create_boundary(kml_file: KMLFile) -> dict | None:
    """
    Adds a single boundary to the boundaries service.

    If we get an invalid geometry error in dev, then try to fetch the geometry from prod,
    and add that geometry instead. This can happen if the geometry in dev was adjusted
    slightly before being uploaded to prod.
    """
    res = post_geometry(kml_file)
    if res.status_code == 422 and "Invalid geometry" in res.text and ENV == "dev":
        # check if there is a valid geometry for this MD5 in prod
        if geom := get_geometry_from_prod(kml_file.md5):
            kml_file.geojson = geom
            res = post_geometry(kml_file)
        else:
            PRINTFUNC(f"valid geometry for MD5 {kml_file.md5} not found in prod")
    if res.status_code > 299:
        PRINTFUNC(f"create_boundary got status code {res.status_code}: {res.json()} for {kml_file.md5=}")
        return None
    return res.json()["features"][0]


def create_boundaries(kml_files: List[KMLFile]) -> List[KMLFileUpdate]:
    """
    Adds boundaries to the boundaries service.

    We create boundaries one at a time, because we are not able to see which geometry
    produced an error (and potentially fetch the corrected geometry from prod) if we
    create boundaries in chunks.
    """
    PRINTFUNC(f"Creating {len(kml_files)} boundaries")
    kml_file_updates = []
    for kml_file in kml_files:
        if feature := create_boundary(kml_file):
            kml_file_update = KMLFileUpdate(
                kml_id=kml_file.kml_id,
                md5=feature["id"],
                area=feature["properties"]["area_m2"] / 10000,
            )
            kml_file_updates.append(kml_file_update)
    return kml_file_updates


def update_kml_files(kml_file_updates: List[KMLFileUpdate], cursor: Cursor) -> None:
    PRINTFUNC(f"Updating {len(kml_file_updates)} kml_files")
    case_placeholders = "\n".join(["WHEN kf.id = %s THEN %s"] * len(kml_file_updates))
    kml_id_placeholders = ",".join(["%s"] * len(kml_file_updates))

    name_tuples = [(update.kml_id, f"{update.md5}.kml") for update in kml_file_updates]
    md5_tuples = [(update.kml_id, update.md5) for update in kml_file_updates]
    area_tuples = [(update.kml_id, update.area) for update in kml_file_updates]
    kml_ids = [update.kml_id for update in kml_file_updates]

    query = f"""UPDATE kml_files kf
    JOIN kml_groups kg ON kf.id = kg.kml_id
    LEFT JOIN mrv_fields mf ON kg.id = mf.fs_field_id
    SET kf.realName = CASE
        {case_placeholders}
        ELSE kf.realName
    END,
    kf.md5 = CASE
        {case_placeholders}
        ELSE kf.md5
    END,
    kf.area = CASE
        {case_placeholders}
        ELSE kf.area
    END,
    mf.md5 = CASE
        {case_placeholders}
        ELSE mf.md5
    END,
    mf.area = CASE
        {case_placeholders}
        ELSE mf.area
    END
    WHERE kf.id in ({kml_id_placeholders})"""  # nosec

    cursor.execute(
        query,
        tuple([item for tup in name_tuples + md5_tuples + area_tuples + md5_tuples + area_tuples for item in tup])
        + tuple(kml_ids),
    )


def _lst_to_chunks(lst: List[Any], chunk_size: int) -> Generator[List[Any], None, None]:
    for i in range(0, len(lst), chunk_size):
        yield lst[i : i + chunk_size]


def parse_comma_separated_strings(value: str) -> List[str]:
    """
    Parse comma-separated string values.
    """
    if not value:
        return []
    return [s.strip().strip("'\"") for s in value.split(",") if s.strip()]


def parse_comma_separated_ints(value: str) -> List[int]:
    """
    Parse comma-separated integer values.
    """
    if not value:
        return []
    return [int(s.strip()) for s in value.split(",") if s.strip()]


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Add missing field boundaries to the boundaries service and update database records."
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run to show how many boundaries would be updated.",
        default=False,
    )
    parser.add_argument(
        "--program-id",
        type=int,
        help="Single MRV program ID to process. Cannot be used with --project-ids or --boundary-ids.",
    )
    parser.add_argument(
        "--project-ids",
        type=str,
        help="Comma-separated list of project IDs (e.g., '1,2,3'). Cannot be used with --program-id or --boundary-ids.",
    )
    parser.add_argument(
        "--boundary-ids",
        type=str,
        help="Comma-separated list of boundary IDs/MD5s (e.g., 'abc123,def456'). Cannot be used with --program-id or --project-ids.",
    )
    args = parser.parse_args()

    if DB_USER == "":
        raise AssertionError("You must set DB_USER and associated variables before running script")

    # Get input parameters from command line arguments
    project_ids = parse_comma_separated_ints(args.project_ids) if args.project_ids else []
    md5s = parse_comma_separated_strings(args.boundary_ids) if args.boundary_ids else []
    program_id = args.program_id

    # Validate that exactly one input method is provided
    input_methods = [bool(project_ids), bool(md5s), bool(program_id)]
    if sum(input_methods) == 0:
        raise AssertionError("You must provide exactly one of: --program-id, --project-ids, or --boundary-ids")
    if sum(input_methods) > 1:
        raise AssertionError(
            "You cannot provide more than one input method. Choose either --program-id, --project-ids, or --boundary-ids"
        )

    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor()

    # Get MD5s based on the input method
    if program_id:
        md5s = get_md5s_by_program_id(program_id, cursor)
    elif not md5s:  # project_ids case
        md5s = get_md5s_by_project_ids(project_ids, cursor)

    if args.dry_run:
        PRINTFUNC("\n=== DRY RUN MODE ===")
        PRINTFUNC("No actual changes will be made to the database or boundaries service.")

    missing_boundary_ids = get_missing_boundary_ids(md5s)
    if missing_boundary_ids:
        missing_kml_files = get_missing_kml_files(missing_boundary_ids, cursor)

        if args.dry_run:
            PRINTFUNC(f"\nDRY RUN RESULTS:")
            PRINTFUNC(f"  - Found {len(missing_boundary_ids)} missing boundary ids (MD5s)")
            PRINTFUNC(f"  - Would create {len(missing_kml_files)} boundaries in the boundaries service")
            PRINTFUNC(f"  - Would update {len(missing_kml_files)} MD5 hashes in the kml_files table")
            PRINTFUNC(f"\nFirst 10 MD5s that would be updated:")
            for kml_file in missing_kml_files[:10]:
                PRINTFUNC(f"  - {kml_file.md5}")
        else:
            kml_file_updates = create_boundaries(missing_kml_files)
            update_kml_files(kml_file_updates, cursor)
            PRINTFUNC(f"Successfully updated {len(kml_file_updates)} MD5 hashes")
    else:
        if args.dry_run:
            PRINTFUNC(f"\nDRY RUN RESULTS:")
            PRINTFUNC(f"  - No missing MD5 hashes found")
            PRINTFUNC(f"  - No changes would be made")
        else:
            PRINTFUNC("No missing MD5 hashes found. No updates needed.")

    if not args.dry_run:
        connection.commit()
    cursor.close()
    connection.close()
