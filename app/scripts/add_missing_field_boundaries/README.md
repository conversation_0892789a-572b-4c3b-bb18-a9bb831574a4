# Add Missing Field Boundaries Script

This script:

1. **Identifies** KML files with MD5s missing from the boundaries service
2. **Creates** boundaries in the boundaries service for missing KML files
3. **Updates** KML files and associated MRV fields with new MD5s and areas

In `--dry-run` mode, it shows what would be processed without making any actual changes.

## Prerequisites

Set the required database environment variables:
```bash
export DB_USER="your_db_user"
export DB_PASSWORD="your_db_password"
export ENV="dev"  # or "prod"
```
Note: To allow this script to authenticate with flurosense-prod using PAM / gcloud login token credentials, you may need to set environment variable `LIBMYSQL_ENABLE_CLEARTEXT_PLUGIN=1`.

## Usage

The script accepts exactly one of three input methods:

### 1. Single Program ID
```bash
python add_missing_field_boundaries.py --program-id 123
```

### 2. Multiple Project IDs
```bash
python add_missing_field_boundaries.py --project-ids "1,2,3,4"
```

### 3. Specific Boundary IDs (MD5s)
```bash
python add_missing_field_boundaries.py --boundary-ids "abc123,def456,ghi789"
```

## Dry Run Mode

Add `--dry-run` to any command to see what would be updated without making changes:

```bash
# Dry run examples
python add_missing_field_boundaries.py --program-id 123 --dry-run
python add_missing_field_boundaries.py --project-ids "1,2,3" --dry-run
python add_missing_field_boundaries.py --boundary-ids "abc123,def456" --dry-run
```

## Help

```bash
python add_missing_field_boundaries.py --help
```
