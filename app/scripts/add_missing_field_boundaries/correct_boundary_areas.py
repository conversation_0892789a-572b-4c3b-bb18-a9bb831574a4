# this script:
# 1. identifies the KML files for which the area is different than the area in the boundaries service based on the MD5
# 2. updates the area for the KML files, and associated MRV fields, using the area in the boundaries service

import os
import time
from dataclasses import dataclass
from typing import Any, Dict, Generator, List

import MySQLdb
import requests
from MySQLdb.cursors import Cursor

ENV = os.environ.get("ENV", "dev")

DB_HOST = f"flurosense-db.us.{ENV}.internal"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
DB_NAME = f"flurosense-{ENV}"

# the time query param is a hack to prevent the query from being cached
SEARCH_BOUNDARY_BASE_URL = f"http://boundaries-service.int.{ENV}.regrow.cloud/search?time={int(time.time())}"  # NOSONAR


@dataclass
class KMLFile:
    md5: str
    area: float


def get_kml_files(project_ids: List[int], cursor: Cursor) -> List[KMLFile]:
    placeholders = ",".join(["%s"] * len(project_ids))
    query = f"""SELECT kf.md5, kf.area
    FROM kml_files kf
    JOIN kml_groups kg ON kf.id = kg.kml_id
    JOIN mrv_fields mf ON kg.id = mf.fs_field_id
    WHERE kg.deleted_at IS NULL
        AND mf.parent_project_id IN ({placeholders})"""  # nosec

    cursor.execute(query, tuple(project_ids))
    res = cursor.fetchall()

    kml_files = [KMLFile(md5=single_res[0], area=float(single_res[1])) for single_res in res]
    return kml_files


def get_md5_to_area(kml_files: List[KMLFile]) -> Dict[str, float]:
    md5_to_area = {}
    for chunk in _lst_to_chunks(kml_files, 50):
        url = SEARCH_BOUNDARY_BASE_URL
        for kml_file in chunk:
            url = url + "&ids=" + kml_file.md5
        res = requests.get(url, timeout=120)
        if res.status_code > 299:
            # print(f"got status code {res.status_code}: {res.json()} for MD5s: {chunk}")
            continue
        features = res.json()["features"]
        for feature in features:
            md5 = feature["id"]
            area = feature["properties"]["area_m2"] / 10000
            md5_to_area[md5] = area
        time.sleep(1)
    return md5_to_area


def get_md5_to_area_updates(kml_files: List[KMLFile], md5_to_area: Dict[str, float]) -> Dict[str, float]:
    md5_to_area_updates = {}
    for kml_file in kml_files:
        correct_area = md5_to_area.get(kml_file.md5)
        if not correct_area:
            continue
        if abs(kml_file.area - correct_area) > 1:
            md5_to_area_updates[kml_file.md5] = correct_area
    return md5_to_area_updates


def update_kml_files(md5_to_area_updates: Dict[str, float], cursor: Cursor) -> None:
    case_placeholders = "\n".join(["WHEN kf.md5 = %s THEN %s"] * len(md5_to_area_updates))
    md5_placeholders = ",".join(["%s"] * len(md5_to_area_updates))

    area_tuples = [(md5, area) for md5, area in md5_to_area_updates.items()]
    md5s = list(md5_to_area_updates.keys())

    query = f"""UPDATE kml_files kf
        JOIN kml_groups kg ON kf.id = kg.kml_id
        LEFT JOIN mrv_fields mf ON kg.id = mf.fs_field_id
        SET kf.area = CASE
            {case_placeholders}
            ELSE kf.area
        END,
        mf.area = CASE
            {case_placeholders}
            ELSE mf.area
        END
        WHERE kf.md5 in ({md5_placeholders})"""  # nosec

    cursor.execute(query, tuple([item for tup in area_tuples + area_tuples for item in tup]) + tuple(md5s))


def _lst_to_chunks(lst: List[Any], chunk_size: int) -> Generator[List[Any], None, None]:
    for i in range(0, len(lst), chunk_size):
        yield lst[i : i + chunk_size]


if __name__ == "__main__":
    if DB_USER == "":
        raise AssertionError("You must set DB_USER and associated variables before running script")

    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor()

    project_ids: List[int] = []
    kml_files = get_kml_files(project_ids, cursor)
    md5_to_area = get_md5_to_area(kml_files)
    md5_to_area_updates = get_md5_to_area_updates(kml_files, md5_to_area)
    update_kml_files(md5_to_area_updates, cursor)

    # connection.commit()
    # cursor.close()
    # connection.close()
