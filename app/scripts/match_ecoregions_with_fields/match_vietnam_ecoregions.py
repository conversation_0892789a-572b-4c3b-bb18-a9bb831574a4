"""<PERSON><PERSON>t to populate stratum data to mrv fields
Details:
The script contains two main processes:
    1. It creates csv file (FIELDS_CSV_FILE) with fields data which should be populated. Data contains:
        producer_id,field_id,md5,lat,lon,production_system,crop_type.
        This data is needed for "boundaries-service" API requests(used as filters).
    2. Makes requests to "boundaries-service" to get ecoregion's data related to field. And write
        this data to Values (mrv_values).

How use script:
    1. In the beginning of module there is "SETTINGS" section. Update settings if needed (at least db credentials).
    2. In the end of module there is "match_vietnam_ecoregions_with_fields" function which run
        both processes: a)create csv file with fields data; b)makes requests to "boundaries-service" and
        write data to DB. It's recommended to run those processes separately to avoid mistakes (just
        comment one of the function calling).

How to run script for specific fields which were not populated.
    1. After first script running new "error" file (FIELDS_ERROR_CSV_FILE) is created.
        Call `print_fields_ids_from_error_file()` to get ids of failed fields from "error" file.
    2. Write failed fields ids to `FIELDS_IDS_TO_UPDATE` in SETTINGS section.
    3. Run full script again.
"""

import csv
import datetime
import os
import time
from collections import defaultdict

import MySQLdb
import requests
from dateutil.relativedelta import relativedelta

# SETTINGS START
PROGRAM_ID = 1208
YEARS_RANGE_TO_FILL_DATA = 6

# work together with YEARS_RANGE_TO_FILL_DATA, to filter out the seasons which for the earliest year (2019 for the case of program 1208)
# 1 means to keep the last season for the earliest year, this ensures we have at least 1 season for the earliest year
NUM_SEASONS_TO_KEEP_FOR_EARLIEST_YEAR = 1

# For program 1208 remove all events which end after 2024-08-01, since the competition period starts at 2024-08-01
MAX_EVENT_END_DATE = datetime.datetime(2024, 8, 1)

AMOUNT_OF_ATTRIBUTES_TO_UPDATE = 30

BOUNDARIES_SERVICE_URL = (
    "http://boundaries-service.int.dev.regrow.cloud/datasets/baselines_interventions_snv/0.1.0/data"
)

# Dev
DB_HOST = "flurosense-db.us.dev.internal"
DB_USER = ""
DB_PASSWORD = ""
DB_NAME = "flurosense-dev"
FIELDS_CSV_FILE = f"{DB_NAME}_fields_data_v1.0.csv"
FIELDS_ERROR_CSV_FILE = f"{DB_NAME}_fields_errors_v1.0.csv"

# Prod
# DB_HOST = "flurosense-db.us.prod.internal"
# DB_USER = ""
# DB_PASSWORD = ""
# DB_NAME = "flurosense-prod"
# FIELDS_CSV_FILE = f".../mrv-service/app/scripts/match_ecoregions_with_fields/prod_{DB_NAME}_fields_data_v1.0.csv"
# FIELDS_ERROR_CSV_FILE = f".../mrv-service/app/scripts/match_ecoregions_with_fields/prod_{DB_NAME}_fields_errors_v1.0.csv"

ATTRIBUTE_TYPES_TO_UPDATE = "'record_year', 'application_product', 'application_date', 'application_rate', 'application_rate_unit', 'application_method', 'nutrient_management_enabled', 'additives', 'application_rate_type', 'record_year', 'crop_type', 'planting_date', 'winter_crop_termination', 'harvest_date', 'residue_harvested', 'residue_burnt', 'crop_usage', 'planting_rate_unit', 'planting_method', 'planting_rate', 'record_year', 'tillage_event', 'tillage_date', 'tillage_depth', 'soil_inversion', 'record_year', 'irrigation_enabled', 'irrigation_method', 'end_date', 'start_date'"

# If there are few rows in Stages datacollection specify suffixes here:
STAGES_SUFFIXES = {
    "HISTORICAL_CROP_ROTATION": [""],
    "HISTORICAL_TILLAGE": [""],
    "NUTRIENT_MGMT": ["_1", "_2", "_3"],
    "IRRIGATION": ["_1", "_2"],
}

BOUNDARIES_SERVICE_ATTRIBUTES_PREFIX = "baselines_interventions_snv."

# If boundaries-service has different name for attribute:
ATTRIBUTE_TYPE_TO_BOUNDARIES_SERVICE_ATTRIBUTE = {
    "crop_type": "crop_name",
    "start_date": "irrigation_start_date",
    "end_date": "irrigation_end_date",
}

DEFAULT_ATTRIBUTES_VALUES = {
    "crop_type": "rice",
    "application_rate_unit": "kg1ha-1",
    "planting_rate_unit": "kg/ha",
}

CHANGE_VALUES_TO_VALUES = {
    "50 percent harvested": "50% Harvested",
    "Grain harvest": "Grain Harvest",
    "Urea (46, 0, 0)": "urea",
    "Manure_compost (2, 0, 1)": "manure_compost",
}

# If FIELDS_IDS_TO_UPDATE is empty, all fields will be updated
FIELDS_IDS_TO_UPDATE: list = []

REMOVE_EMPTY_LINES_FOR_ATTRIBUTES = [
    "nutrient_management_enabled",
]

ADJUST_RECORD_YEAR_BY_STAGE_ATTRIBUTE = {
    "HISTORICAL_TILLAGE": "tillage_date",
    "NUTRIENT_MGMT": "application_date",
    "IRRIGATION": "end_date",
}

REMOVE_RESIDUE_DATA_FOR_YEAR = None

# SETTINGS END


def create_db_cursor():  # type: ignore[no-untyped-def]
    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor()
    # print("Connected to DB")
    return connection, cursor


def create_csv_file_with_filters_for_boundary_service(cursor) -> None:  # type: ignore[no-untyped-def]
    if os.path.isfile(FIELDS_CSV_FILE):
        # print(f"Fields CSV data file is already exists: {FIELDS_CSV_FILE}")
        return
    # Get Fields ids and md5 by program id
    global FIELDS_IDS_TO_UPDATE
    if not FIELDS_IDS_TO_UPDATE:  # get all fields
        sql_cmd = f"""
        SELECT mrv_fields.parent_project_id, mrv_fields.id, mrv_fields.md5
        FROM mrv_fields
        JOIN mrv_projects
            ON mrv_fields.parent_project_id=mrv_projects.id AND mrv_projects.program_id={PROGRAM_ID} AND mrv_projects.status!='deleted'
        WHERE mrv_fields.deleted_at IS NULL
        """  # nosec
    else:  # get fields specified in FIELDS_IDS_TO_UPDATE
        fields_ids_to_update = [str(i) for i in FIELDS_IDS_TO_UPDATE]
        fields_ids_str = ",".join(fields_ids_to_update)
        sql_cmd = f"""
        SELECT mrv_fields.parent_project_id, mrv_fields.id, mrv_fields.md5
        FROM mrv_fields
        JOIN mrv_projects
            ON mrv_fields.parent_project_id=mrv_projects.id AND mrv_projects.program_id={PROGRAM_ID} AND mrv_projects.status!='deleted'
        WHERE mrv_fields.deleted_at IS NULL AND mrv_fields.id IN ({fields_ids_str})
        """  # nosec

    cursor.execute(sql_cmd)
    project_id_field_id_md5 = cursor.fetchall()
    # print("Got Fields ids and md5")

    # CSV file to store Field data
    # print(f"Started created Fields CSV data file: {FIELDS_CSV_FILE}")
    with open(FIELDS_CSV_FILE, "w", newline="") as file:
        writer = csv.writer(file)
        writer.writerow(["producer_id", "field_id", "md5", "lat", "lon", "production_system", "crop_type"])

        # Iterating Fields to get centroid, filter data for boundaries-service
        for producer_id, field_id, field_md5 in project_id_field_id_md5:
            # Get Filed centroid by md5
            sql_cmd = f"""
            SELECT @centroid:=ST_Centroid(ST_GeomFromText(ST_AsText(geometry))), ST_X(@centroid) as lat, ST_Y(@centroid) as lon
            FROM kml_files
            WHERE md5="{field_md5}"
            """  # nosec
            cursor.execute(sql_cmd)
            centroid_lat_lon = cursor.fetchone()
            lat = centroid_lat_lon[1]
            lon = centroid_lat_lon[2]

            # Get field properties for boundaries-service filter
            sql_cmd = f"""
            SELECT DISTINCT mrv_attributes.type, mrv_values.value
            FROM mrv_values
            JOIN mrv_attributes
                ON mrv_attributes.id=mrv_values.attribute_id AND mrv_attributes.type in ("crop_type", "production_system")
            WHERE mrv_values.field_id={field_id}
            ORDER BY mrv_attributes.type
            """  # nosec
            cursor.execute(sql_cmd)
            attribute_type_and_value = cursor.fetchall()

            attribute_type_and_value = dict(attribute_type_and_value)
            crop_type = attribute_type_and_value.get("crop_type")
            production_system = attribute_type_and_value.get("production_system")

            writer.writerow([producer_id, field_id, field_md5, lat, lon, production_system, crop_type])

    # print(f"Finished created Fields CSV data file: {FIELDS_CSV_FILE}")


def get_fields_data_from_boundary_service_and_write_it_to_db(connection, cursor) -> None:  # type: ignore[no-untyped-def]
    # Get attribute_id, attribute_type, stage_type
    sql_cmd = f"""
        SELECT mrv_attributes.id as attribute_id, mrv_attributes.type as attribute_type, mrv_stages.type_ as stage_type
        FROM mrv_attributes
            JOIN mrv_stages
            ON mrv_stages.id=mrv_attributes.parent_stage_id AND mrv_stages.type_ IN (
                'HISTORICAL_CROP_ROTATION', 'HISTORICAL_TILLAGE', 'NUTRIENT_MGMT', 'IRRIGATION'
            )
            JOIN mrv_phases
            ON mrv_phases.id=mrv_stages.phase_id AND mrv_phases.program_id={PROGRAM_ID} AND mrv_phases.type_='ENROLMENT'
        # WHERE mrv_attributes.deleted_at IS NULL AND mrv_attributes.enabled=1 # Used for only enabled attributes
        WHERE mrv_attributes.deleted_at IS NULL AND mrv_attributes.type in ({ATTRIBUTE_TYPES_TO_UPDATE})
        ORDER BY stage_type
    """  # nosec
    cursor.execute(sql_cmd)
    attribute_id_and_type_and_stage_type = cursor.fetchall()

    # Get crediting_year from reporting_period_start_date
    sql_cmd = f"SELECT reporting_period_start_date FROM mrv_programs WHERE id={PROGRAM_ID}"  # nosec
    cursor.execute(sql_cmd)
    reporting_period_start_date = cursor.fetchone()[0]
    crediting_year = reporting_period_start_date.year - 1

    assert len(attribute_id_and_type_and_stage_type) == AMOUNT_OF_ATTRIBUTES_TO_UPDATE

    # Open CSV file with Fields data
    # print("Open Fields CSV data file")
    with open(FIELDS_CSV_FILE, mode="r") as csv_file_to_read:
        csv_reader = csv.DictReader(csv_file_to_read)

        # Create CSV file for error logs
        with open(FIELDS_ERROR_CSV_FILE, "w", newline="") as csv_file_to_write_errors:
            csv_errors_writer = csv.writer(csv_file_to_write_errors)
            csv_errors_writer.writerow(["producer_id", "field_id", "error", "lat", "lon", "production_system"])

            # Iterate Fields
            # print("Start upload data to fields:")
            field_counter = 0
            new_time = time.time()
            missed = []
            for field in csv_reader:
                try:
                    producer_id = field["producer_id"]
                    field_id = field["field_id"]
                    field_lat = field["lat"]
                    field_lon = field["lon"]
                    field_production_system = field["production_system"]

                    # Decided to replace empty production_system by "Intensive": https://regrow.atlassian.net/browse/MRV-2874
                    # if not field_production_system:
                    #     # Write error log
                    #     csv_errors_writer.writerow([producer_id, field_id, "No production_system", field_lat, field_lon, field_production_system])
                    #     continue

                    boundary_filter = f"INTERSECTS(geometry, POINT({field_lon} {field_lat}))"

                    field_production_system = field_production_system.lower()

                    # Decided to replace empty production_system by "Intensive": https://regrow.atlassian.net/browse/MRV-2874
                    if not field_production_system:
                        production_system = "Intensive"
                    elif "intensive" in field_production_system:
                        production_system = "Intensive"
                    elif "organic" in field_production_system:
                        production_system = "Organic"
                    elif "aquaculture" in field_production_system:
                        production_system = "Aquaculture"

                    # Get field data from boundaries-service
                    request_data = {
                        "filter": boundary_filter,
                        "data-filter": f"production_system = '{production_system}'",
                    }
                    r = requests.post(url=BOUNDARIES_SERVICE_URL, json=request_data)  # nosec
                    ecoregion_data = r.json().get("data")

                    if not ecoregion_data:
                        csv_errors_writer.writerow(
                            [
                                producer_id,
                                field_id,
                                "Field is not in ecoregion",
                                field_lat,
                                field_lon,
                                field_production_system,
                            ]
                        )
                        continue

                    # Remove BOUNDARIES_SERVICE_ATTRIBUTES_PREFIX from boundaries service response
                    for i in ecoregion_data:
                        for key in list(i.keys()):
                            i[key.replace(BOUNDARIES_SERVICE_ATTRIBUTES_PREFIX, "")] = i.pop(key)

                    # Update date values to real valid values and create list of data to fill Values in DB
                    data_list = []
                    for initial_ecoregion_data in ecoregion_data:
                        # planting and harvest date are in format "m/d"
                        planting_date = initial_ecoregion_data["planting_date"]
                        harvest_date = initial_ecoregion_data["harvest_date"]

                        year = crediting_year
                        harvest_date_datetime = datetime.datetime.strptime(f"{harvest_date}/{year}", "%m/%d/%Y")
                        planting_date_datetime = datetime.datetime.strptime(f"{planting_date}/{year}", "%m/%d/%Y")
                        if planting_date_datetime > harvest_date_datetime:
                            year -= 1
                            planting_date_datetime = datetime.datetime.strptime(f"{planting_date}/{year}", "%m/%d/%Y")

                        # Create list of data to fill Values in DB
                        for years in range(YEARS_RANGE_TO_FILL_DATA):
                            data = initial_ecoregion_data.copy()
                            data["planting_date"] = planting_date_datetime - relativedelta(years=years)
                            data["harvest_date"] = harvest_date_datetime - relativedelta(years=years)
                            data["record_year"] = data["harvest_date"].year

                            def _calculate_date_by_condition(planting_date, harvest_date, condition):  # type: ignore[no-untyped-def]
                                """condition example: "60 d after planting" or "14 d before harvest" """
                                splited_condition = condition.split(" ")
                                days = int(splited_condition[0])
                                before_after = splited_condition[2]
                                planting_harvest = splited_condition[3]

                                if planting_harvest == "planting":
                                    date = planting_date
                                elif planting_harvest == "harvest":
                                    date = harvest_date

                                if before_after == "before":
                                    return date - relativedelta(days=days)
                                elif before_after == "after":
                                    return date + relativedelta(days=days)

                            data["tillage_date"] = _calculate_date_by_condition(
                                data["planting_date"], data["harvest_date"], data["tillage_date"]
                            )

                            for date_key in ["irrigation_start_date", "irrigation_end_date", "application_date"]:
                                for counter in range(1, 5):
                                    date_key_with_counter = f"{date_key}_{counter}"
                                    date_condition = data.get(date_key_with_counter)
                                    if not date_condition:
                                        continue
                                    data[date_key_with_counter] = _calculate_date_by_condition(
                                        data["planting_date"], data["harvest_date"], date_condition
                                    )

                            data_list.append(data)

                    # Sort data_list by "harvest_date"
                    data_list = sorted(data_list, key=lambda d: d["harvest_date"], reverse=True)

                    # Filter out the old data, only keep one season for the earliest year
                    if NUM_SEASONS_TO_KEEP_FOR_EARLIEST_YEAR is not None:
                        while (
                            len(data_list) > NUM_SEASONS_TO_KEEP_FOR_EARLIEST_YEAR
                            and data_list[-1]["record_year"]
                            == data_list[-(NUM_SEASONS_TO_KEEP_FOR_EARLIEST_YEAR + 1)]["record_year"]
                        ):
                            data_list.pop()

                    stage_type_to_attribute_id_and_type: dict = {}
                    for attribute_id, attribute_type, stage_type in attribute_id_and_type_and_stage_type:
                        stage_t = stage_type_to_attribute_id_and_type.get(stage_type)
                        if not stage_t:
                            stage_type_to_attribute_id_and_type[stage_type] = [[attribute_id, attribute_type]]
                        else:
                            stage_t.append([attribute_id, attribute_type])

                    all_values_data = []
                    row_id_by_stage_type: dict = {}
                    for data in data_list:
                        for stage_type, attributes_ids_and_types in stage_type_to_attribute_id_and_type.items():
                            row_id = row_id_by_stage_type.get(stage_type, 0)
                            for suffix in STAGES_SUFFIXES[stage_type]:
                                for attribute_id, attribute_type in attributes_ids_and_types:
                                    if attribute_type == "record_year":
                                        value = data.get(attribute_type)
                                    elif attribute_type in DEFAULT_ATTRIBUTES_VALUES.keys():
                                        value = DEFAULT_ATTRIBUTES_VALUES.get(attribute_type)
                                    else:
                                        if attribute_type in ATTRIBUTE_TYPE_TO_BOUNDARIES_SERVICE_ATTRIBUTE.keys():
                                            value = data.get(
                                                ATTRIBUTE_TYPE_TO_BOUNDARIES_SERVICE_ATTRIBUTE[attribute_type] + suffix
                                            )
                                        else:
                                            value = data.get(attribute_type + suffix)
                                        if value in CHANGE_VALUES_TO_VALUES.keys():
                                            value = CHANGE_VALUES_TO_VALUES.get(value)
                                        # Change True/False to 1/0
                                        if isinstance(value, bool):
                                            value = int(value)

                                    value_data = {
                                        "row_id": row_id,
                                        "attribute_id": attribute_id,
                                        "attribute_type": attribute_type,
                                        "stage_type": stage_type,
                                        "value": value,
                                    }
                                    all_values_data.append(value_data)

                                row_id += 1
                                row_id_by_stage_type[stage_type] = row_id

                    # Remove lines which enabled=False
                    value_obj_criterias_to_remove = []
                    for attr in REMOVE_EMPTY_LINES_FOR_ATTRIBUTES:
                        for value_obj in all_values_data:
                            attribute_type = value_obj["attribute_type"]
                            value = value_obj["value"]
                            stage_type = value_obj["stage_type"]
                            row_id = value_obj["row_id"]
                            if (attribute_type == attr) and (not value):
                                value_obj_criterias_to_remove.append(
                                    {
                                        "stage_type": stage_type,
                                        "row_id": row_id,
                                    }
                                )
                    for criterias_to_remove in value_obj_criterias_to_remove:
                        # Remove line (all values with different attributes in row) if
                        # "stage_type" and "row_id" are in "value_obj_criterias_to_remove"
                        all_values_data = [
                            v
                            for v in all_values_data
                            if not (
                                (v["stage_type"] == criterias_to_remove["stage_type"])
                                and (v["row_id"] == criterias_to_remove["row_id"])
                            )
                        ]

                    # Update record_year attribute
                    record_year_values = []
                    for stage, attr in ADJUST_RECORD_YEAR_BY_STAGE_ATTRIBUTE.items():
                        for value_obj in all_values_data:
                            attribute_type = value_obj["attribute_type"]
                            value = value_obj["value"]
                            stage_type = value_obj["stage_type"]
                            row_id = value_obj["row_id"]

                            if (stage_type == stage) and (attribute_type == attr):
                                record_year_values.append(
                                    {
                                        "stage_type": stage_type,
                                        "row_id": row_id,
                                        "record_year_value": value.year,
                                    }
                                )
                    for record_year_value in record_year_values:
                        stage = record_year_value["stage_type"]
                        row = record_year_value["row_id"]

                        for value_obj in all_values_data:
                            attribute_type = value_obj["attribute_type"]
                            stage_type = value_obj["stage_type"]
                            row_id = value_obj["row_id"]
                            if (attribute_type == "record_year") and (stage_type == stage) and (row_id == row):
                                value_obj["value"] = record_year_value["record_year_value"]

                    # Remove "residue_burnt" and "residue_harvested" for year (REMOVE_RESIDUE_DATA_FOR_YEAR)
                    row_id_for_residue_data_to_remove = None
                    for value_obj in all_values_data:
                        attribute_type = value_obj["attribute_type"]
                        stage_type = value_obj["stage_type"]
                        row_id = value_obj["row_id"]
                        value = value_obj["value"]
                        if (
                            (stage_type == "HISTORICAL_CROP_ROTATION")
                            and (attribute_type == "record_year")
                            and (value == REMOVE_RESIDUE_DATA_FOR_YEAR)
                        ):
                            row_id_for_residue_data_to_remove = row_id
                            break
                    all_values_data = [
                        v
                        for v in all_values_data
                        if not (
                            (v["stage_type"] == "HISTORICAL_CROP_ROTATION")
                            and (v["row_id"] == row_id_for_residue_data_to_remove)
                            and (v["attribute_type"] in ["residue_burnt", "residue_harvested"])
                        )
                    ]

                    # Remove events past MAX_EVENT_END_DATE
                    if MAX_EVENT_END_DATE:
                        new_all_values_data = []

                        stage_values = defaultdict(list)
                        for value in all_values_data:
                            stage_values[value["stage_type"]].append(value)

                        for stage_type, values in stage_values.items():
                            rows: dict = defaultdict(list)
                            for value in values:
                                rows[value["row_id"]].append(value)

                            rows = {
                                row_id: row
                                for row_id, row in rows.items()
                                if next(
                                    v
                                    for v in row
                                    if v.get("attribute_type", None)
                                    in ["harvest_date", "application_date", "tillage_date", "end_date"]
                                )["value"]
                                < MAX_EVENT_END_DATE
                            }

                            # re-assign row_ids
                            map_to_new_row_id = {num: i for i, num in enumerate(sorted(rows.keys()))}
                            new_all_values_data.extend(
                                [
                                    {
                                        **value,
                                        "row_id": map_to_new_row_id[value["row_id"]],
                                    }
                                    for row in rows.values()
                                    for value in row
                                ]
                            )

                            all_values_data = new_all_values_data

                    # Insert values to DB
                    sql_rows = []
                    for value_obj in all_values_data:
                        attribute_id = value_obj["attribute_id"]
                        row_id = value_obj["row_id"]
                        value = value_obj["value"]
                        if isinstance(value, datetime.datetime):
                            value = value.strftime("%Y-%m-%dT00:00:00.000Z")
                        else:
                            value = str(value)

                        sql_row = f"('{value}', {field_id}, {row_id}, {attribute_id}, 'enrolment', false, true, true, 'csv', 'field')"
                        sql_rows.append(sql_row)

                    sql_cmd = """
                        INSERT INTO mrv_values(value, field_id, row_id, attribute_id, progress,
                                            locked, read_only, confirmed, source, entity_type)
                        VALUES
                    """
                    sql_cmd += ",\n".join(sql_rows)

                    try:
                        attributes_ids = [str(row["attribute_id"]) for row in all_values_data]
                        attributes_ids_to_delete = ",".join(attributes_ids)
                        cursor.execute(
                            f"DELETE FROM mrv_values WHERE attribute_id IN ({attributes_ids_to_delete}) AND field_id={field_id}"  # nosec
                        )
                        cursor.execute(sql_cmd)
                        connection.commit()
                        # print(f"Updated Field ID: {field_id}")
                    except Exception as e:
                        str_e = str(e).replace(",", "")
                        csv_errors_writer.writerow(
                            [producer_id, field_id, f"DB error: {str_e}", field_lat, field_lon, field_production_system]
                        )
                        raise e

                    if (field_counter > 0) and (field_counter % 100) == 0:
                        # print("Time spent for 100 fields: {}".format(time.time() - new_time))
                        new_time = time.time()
                    field_counter += 1
                except Exception:
                    missed.append(field_id)
                    # print('excepted ', field_id)
                    continue
            # print('missed', missed)


def print_fields_ids_from_error_file() -> None:
    """Is used to get fields ids which failed to populate first time.
    Those ids can be written to FIELDS_IDS_TO_UPDATE to run script once again.
    """
    with open(FIELDS_ERROR_CSV_FILE, mode="r") as csv_file_to_read:
        csv_reader = csv.DictReader(csv_file_to_read)

        fields_ids = []
        for field in csv_reader:
            field_id = field["field_id"]
            fields_ids.append(field_id)

        str_ids = ", ".join(fields_ids)
        # print(str_ids)


def match_vietnam_ecoregions_with_fields() -> None:
    """Main function which:
    1. creates csv file with fields geo data
    2. makes requests to "boundaries-service" to get ecoregions data and
        write this data to mrv_values
    """

    # print("START")
    connection, cursor = create_db_cursor()
    try:
        create_csv_file_with_filters_for_boundary_service(cursor)
        get_fields_data_from_boundary_service_and_write_it_to_db(connection, cursor)
    finally:
        cursor.close()
    # print("DONE")


if __name__ == "__main__":
    match_vietnam_ecoregions_with_fields()
