import csv

import MySQLdb
import requests

# SETTINGS START

# Dev
DB_HOST = "flurosense-db.us.dev.internal"
DB_USER = ""
DB_PASWORD = ""
DB_NAME = "flurosense-dev6"
FIELDS_CSV_FILE = f".../mrv-service/app/scripts/match_ecoregions_with_fields/{DB_NAME}_fields_data_v1.3.csv"
FIELDS_ERROR_CSV_FILE = (
    f".../mrv-service/app/scripts/match_ecoregions_with_fields/{DB_NAME}_adjust_historic_data_errors_v1.3.csv"
)

# Prod
# DB_HOST = "flurosense-db.us.prod.internal"
# DB_USER = ""
# DB_PASWORD = ""
# DB_NAME = "flurosense-prod"
# FIELDS_CSV_FILE = f".../mrv-service/app/scripts/match_ecoregions_with_fields/prod_{DB_NAME}_fields_data_v1.0.csv"
# FIELDS_ERROR_CSV_FILE = (
#     f".../mrv-service/app/scripts/match_ecoregions_with_fields/prod_{DB_NAME}_adjust_historic_data_errors_v1.0.csv"
# )

BOUNDARIES_SERVICE_URL = (
    "http://boundaries-service.int.dev.regrow.cloud/datasets/baselines_interventions_snv/0.1.0/data"
)

# To get producers list execute sql command like this:
# select mpv.project_id from mrv_project_values mpv
# JOIN mrv_program_custom_reg_inputs mpcri on mpcri.id = mpv.`key`
# where mpcri.program_id = 582
# and value like 'Kitoku%';

PRODUCERS_ID_LIST = ()

PLANTING_RATE_DEFAULTS = {
    "fw": "161",
    "hf": "160",
}

# SETTINGS END


def create_db_cursor():  # type: ignore[no-untyped-def]
    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASWORD, db=DB_NAME)
    cursor = connection.cursor()
    # print("Connected to DB")
    return connection, cursor


def adjust_historic_planting_rate() -> None:
    # print("START")
    connection, cursor = create_db_cursor()
    try:
        sql_cmd = """
        SELECT mrv_attributes.id
        FROM mrv_attributes
            JOIN mrv_stages
            ON mrv_stages.id=mrv_attributes.parent_stage_id AND mrv_stages.type_='HISTORICAL_CROP_ROTATION'
            JOIN mrv_phases
            ON mrv_phases.id=mrv_stages.phase_id AND mrv_phases.program_id=582 AND mrv_phases.type_='ENROLMENT'
        WHERE mrv_attributes.deleted_at IS NULL AND mrv_attributes.type='planting_rate'
        """
        cursor.execute(sql_cmd)
        planting_rate_attribute_id = cursor.fetchone()[0]

        with open(FIELDS_CSV_FILE, mode="r") as csv_file_to_read:
            csv_reader = csv.DictReader(csv_file_to_read)

            with open(FIELDS_ERROR_CSV_FILE, "w", newline="") as csv_file_to_write_errors:
                csv_errors_writer = csv.writer(csv_file_to_write_errors)
                csv_errors_writer.writerow(["producer_id", "field_id", "error"])

                all_producers = set()
                for field in csv_reader:
                    producer_id = int(field["producer_id"])
                    all_producers.add(producer_id)
                    field_id = field["field_id"]
                    field_lat = field["lat"]
                    field_lon = field["lon"]
                    field_production_system = field["production_system"]

                    if producer_id not in PRODUCERS_ID_LIST:
                        continue
                    if not field_production_system:
                        csv_errors_writer.writerow(
                            [
                                str(producer_id),
                                field_id,
                                "No production_system",
                                field_lat,
                                field_lon,
                                field_production_system,
                            ]
                        )
                        continue

                    boundary_filter = f"INTERSECTS(geometry, POINT({field_lon} {field_lat}))"

                    field_production_system = field_production_system.lower()
                    if "intensive" in field_production_system:
                        production_system = "Intensive"
                    elif "organic" in field_production_system:
                        production_system = "Organic"
                    elif "aquaculture" in field_production_system:
                        production_system = "Aquaculture"

                    # Get field data from boundaries-service
                    request_data = {
                        "filter": boundary_filter,
                        "data-filter": f"production_system = '{production_system}'",
                    }
                    r = requests.post(url=BOUNDARIES_SERVICE_URL, json=request_data)  # nosec
                    ecoregion_data = r.json().get("data")

                    if not ecoregion_data:
                        csv_errors_writer.writerow([str(producer_id), field_id, "Field is not in ecoregion"])
                        continue

                    ecoregion_id = ecoregion_data[0]["feature.ecoregion_id"]
                    planting_rate_value = PLANTING_RATE_DEFAULTS[ecoregion_id]
                    sql_cmd = f"""
                        UPDATE mrv_values
                        SET value='{planting_rate_value}'
                        WHERE field_id={field_id} AND attribute_id={planting_rate_attribute_id}
                    """  # nosec
                    cursor.execute(sql_cmd)
                    connection.commit()

                    # print(f"Field: {field_id}. Producer: {producer_id}. Ecoregion id: {ecoregion_id}.")

                producer: int
                for producer in PRODUCERS_ID_LIST:
                    if producer not in all_producers:
                        csv_errors_writer.writerow([str(producer), "", "Producer doesn't have fields"])
    finally:
        cursor.close()
    # print("DONE")


if __name__ == "__main__":
    adjust_historic_planting_rate()
