import MySQLdb

# SETTINGS START

# Dev
DB_HOST = "flurosense-db.us.dev.internal"
DB_USER = ""
DB_PASWORD = ""
DB_NAME = "flurosense-dev6"

# Prod
# DB_HOST = "flurosense-db.us.prod.internal"
# DB_USER = ""
# DB_PASWORD = ""
# DB_NAME = "flurosense-prod"

# To get PRODUCTION_SYSTEM_ATTRIBUTE_ID execute SQL command like this (change "mrv_phases.program_id"):
# SELECT mrv_attributes.id as attribute_id, mrv_attributes.type as attribute_type, mrv_stages.type_ as stage_type
# FROM mrv_attributes
#     JOIN mrv_stages
#     ON mrv_stages.id=mrv_attributes.parent_stage_id
#     JOIN mrv_phases
#     ON mrv_phases.id=mrv_stages.phase_id AND mrv_phases.program_id=582 AND mrv_phases.type_='ENROLMENT'
# WHERE mrv_attributes.deleted_at IS NULL AND mrv_attributes.type='production_system'
# ORDER BY stage_type
PRODUCTION_SYSTEM_ATTRIBUTE_ID = 66627

# Fields which don't have production_system:
FIELDS_IDS_TO_UPDATE: list = []

# SETTINGS END


def create_db_cursor():  # type: ignore[no-untyped-def]
    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASWORD, db=DB_NAME)
    cursor = connection.cursor()
    # print("Connected to DB")
    return connection, cursor


def add_production_system_to_fields() -> None:
    # print("START")
    connection, cursor = create_db_cursor()
    try:
        sql_rows = []
        for field_id in set(FIELDS_IDS_TO_UPDATE):
            sql_row = f"('Intensive rice production', {field_id}, 0, {PRODUCTION_SYSTEM_ATTRIBUTE_ID}, 'enrolment', 0, 0, 1, 'csv', 'field')"
            sql_rows.append(sql_row)

        # Ignore(pass) existing values. Values can be ignore becase there is no Null values in DB:
        # SELECT *
        # FROM mrv_values
        # WHERE row_id=0 AND value is Null AND attribute_id=66627
        sql_cmd = """
            INSERT IGNORE INTO mrv_values(value, field_id, row_id, attribute_id, progress,
                                   locked, read_only, confirmed, source, entity_type)
            VALUES
        """
        sql_cmd += ",\n".join(sql_rows)

        cursor.execute(sql_cmd)
        connection.commit()
    finally:
        cursor.close()
    # print("DONE")


if __name__ == "__main__":
    add_production_system_to_fields()
