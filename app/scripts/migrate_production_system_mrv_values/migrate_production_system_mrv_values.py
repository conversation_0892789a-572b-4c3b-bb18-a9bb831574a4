import os
from collections import defaultdict

import MySQLdb
from MySQLdb.cursors import Cursor

ENV = os.environ.get("ENV", "dev")

DB_HOST = f"flurosense-db.us.{ENV}.internal"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
DB_NAME = f"flurosense-{ENV}"

SOURCE_PROGRAM_ID = 0
DEST_PROGRAM_ID = 0


def get_attribute_id(attribute_type: str, stage_type: str, phase_type: str, program_id: int, cursor: Cursor) -> int:
    query = f"""SELECT ma.id
    FROM mrv_attributes ma
    JOIN mrv_stages ms on ma.parent_stage_id = ms.id
    JOIN mrv_phases mp on ms.phase_id = mp.id
    WHERE mp.program_id = {program_id}
        AND mp.type_ = '{phase_type}'
        AND mp.deleted_at IS NULL
        AND ms.type_ = '{stage_type}'
        AND ms.deleted_at IS NULL
        AND ma.type = '{attribute_type}'
        AND ma.enabled = 1
        AND ma.deleted_at IS NULL"""  # nosec

    cursor.execute(query)
    res = cursor.fetchall()
    return int(res[0][0])


def get_md5_to_production_system(program_id: int, field_enrolled: bool, cursor: Cursor) -> dict[str, str]:
    query = f"""SELECT mf.md5, mv.value
    FROM mrv_values mv
    JOIN mrv_attributes ma on mv.attribute_id = ma.id
    JOIN mrv_fields mf on mv.field_id = mf.id
    JOIN mrv_projects mp on mf.parent_project_id = mp.id
    WHERE mp.program_id = {program_id}
        AND ma.type = 'production_system'"""  # nosec
    if field_enrolled:
        query = query + "AND mf.status = 'enrolled'"

    cursor.execute(query)
    res = cursor.fetchall()
    return {single_res[0]: single_res[1] for single_res in res}


def get_md5_to_field_ids(program_id: int, cursor: Cursor) -> dict[str, list[int]]:
    query = f"""SELECT mf.md5, mf.id
    FROM mrv_fields mf
    JOIN mrv_projects mp on mf.parent_project_id = mp.id
    WHERE mp.program_id = {program_id}"""  # nosec

    cursor.execute(query)
    res = cursor.fetchall()

    md5_to_field_ids = defaultdict(list)
    for single_res in res:
        md5_to_field_ids[single_res[0]].append(single_res[1])
    return md5_to_field_ids


def get_field_id_to_production_system(
    md5_to_production_system: dict[str, str], md5_to_field_ids: dict[str, list[int]]
) -> dict[int, str]:
    field_id_to_production_system = {}
    for md5, field_ids in md5_to_field_ids.items():
        production_system = md5_to_production_system.get(md5)
        if not production_system:
            continue
        for field_id in field_ids:
            field_id_to_production_system[field_id] = production_system
    return field_id_to_production_system


def insert_production_system_mrv_values(
    record_year_attribute_id: int,
    production_system_attribute_id: int,
    field_id_to_production_system: dict[int, str],
    cursor: Cursor,
) -> None:
    insert_record_year = [
        f"(2025, {field_id}, 0, 0, 1, 'enrolment', {record_year_attribute_id}, 'user', 0, 1, 'field')"
        for field_id in field_id_to_production_system.keys()
    ]
    insert_production_system = [
        f"('{production_system}', {field_id}, 0, 0, 1, 'enrolment', {production_system_attribute_id}, 'user', 0, 1, 'field')"
        for field_id, production_system in field_id_to_production_system.items()
    ]
    values_to_insert_str = ",".join(insert_record_year + insert_production_system)
    query = f"""INSERT INTO mrv_values (value, field_id, row_id, locked, confirmed, progress, attribute_id, source, read_only, is_valid, entity_type) VALUES {values_to_insert_str}"""  # nosec

    cursor.execute(query)


if __name__ == "__main__":
    connection = MySQLdb.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME)
    cursor = connection.cursor()

    assert SOURCE_PROGRAM_ID and DEST_PROGRAM_ID != 0

    record_year_attribute_id = get_attribute_id(
        attribute_type="record_year",
        stage_type="RICE_CROP_HISTORY",
        phase_type="ENROLMENT",
        program_id=DEST_PROGRAM_ID,
        cursor=cursor,
    )
    production_system_attribute_id = get_attribute_id(
        attribute_type="production_system",
        stage_type="RICE_CROP_HISTORY",
        phase_type="ENROLMENT",
        program_id=DEST_PROGRAM_ID,
        cursor=cursor,
    )
    md5_to_production_system = get_md5_to_production_system(
        program_id=SOURCE_PROGRAM_ID, field_enrolled=True, cursor=cursor
    )
    md5_to_dest_field_ids = get_md5_to_field_ids(program_id=DEST_PROGRAM_ID, cursor=cursor)
    dest_field_id_to_production_system = get_field_id_to_production_system(
        md5_to_production_system=md5_to_production_system, md5_to_field_ids=md5_to_dest_field_ids
    )
    insert_production_system_mrv_values(
        record_year_attribute_id=record_year_attribute_id,
        production_system_attribute_id=production_system_attribute_id,
        field_id_to_production_system=dest_field_id_to_production_system,
        cursor=cursor,
    )

    dest_md5_to_production_system = get_md5_to_production_system(
        program_id=DEST_PROGRAM_ID, field_enrolled=False, cursor=cursor
    )
    assert md5_to_production_system == dest_md5_to_production_system

    # connection.commit()
    # cursor.close()
    # connection.close()
