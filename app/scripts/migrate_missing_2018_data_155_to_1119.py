import csv
import logging
import os
import sys

import pymysql

logging.basicConfig(level=logging.INFO, format="%(message)s")  # Make sure INFO-level logs are shown

RECORD_YEAR_TILLAGE = 2018
RECORD_YEAR_COVER = 2019
ROW_ID = 40
TILLAGE_EVENT = 1
FALL_TILLAGE_DATE = "2018-10-05"
PLANTING_DATE_COVER_CROPS = "2018-10-06"
HARVEST_DATE_COVER_CROPS = "2019-04-20"

DB_HOST = "flurosense-db.us.dev.internal"
DB_NAME = "flurosense-dev"
DB_USER = os.environ.get("DB_USER", "")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")


def get_fall_tillage_migration_sql(row: dict) -> tuple[str, list]:
    field_id = row["field_id_1119"]
    tillage_practice = row["value"].lower()

    if tillage_practice == "reduced till":
        tillage_depth = 10
    elif tillage_practice == "conventional till":
        tillage_depth = 20
    else:
        logging.info("No tillage practice found, skipping")
        return "", []

    query = """
    insert into mrv_values
      (value, field_id, attribute_id, row_id, locked, confirmed, read_only)
    values
      (%s, %s, %s, %s, 1, 1, 1),  -- record year
      (%s, %s, %s, %s, 1, 1, 1),  -- tillage event
      (%s, %s, %s, %s, 1, 1, 1),  -- tillage practice
      (%s, %s, %s, %s, 1, 1, 1),  -- tillage date
      (%s, %s, %s, %s, 1, 1, 1)   -- tillage depth
    """

    params = [
        RECORD_YEAR_TILLAGE,
        field_id,
        66803,
        ROW_ID,
        TILLAGE_EVENT,
        field_id,
        66852,
        ROW_ID,
        tillage_practice,
        field_id,
        66855,
        ROW_ID,
        FALL_TILLAGE_DATE,
        field_id,
        66853,
        ROW_ID,
        tillage_depth,
        field_id,
        66854,
        ROW_ID,
    ]

    return query, params


def get_winter_crop_migration_sql(row: dict) -> tuple[str, list]:
    crop_type_155 = row["value"]
    field_id = row["field_id_1119"]

    if crop_type_155 == "full_cover":
        crop_type_1119 = "basic_cover crop"
        crop_usage = "Cover"
    elif crop_type_155 == "wheat_winter":
        crop_type_1119 = "wheat_winter"
        crop_usage = "Commodity"
    else:
        logging.info("No cover cropping, skipping")
        return "", []

    query = """
    insert into mrv_values
      (value, field_id, attribute_id, row_id, locked, confirmed, read_only)
    values
      (%s, %s, %s, %s, 1, 1, 1),
      (%s, %s, %s, %s, 1, 1, 1),
      (%s, %s, %s, %s, 1, 1, 1),
      (%s, %s, %s, %s, 1, 1, 1),
      (%s, %s, %s, %s, 1, 1, 1)
    """

    params = [
        RECORD_YEAR_COVER,
        field_id,
        66799,
        ROW_ID,
        crop_type_1119,
        field_id,
        66844,
        ROW_ID,
        crop_usage,
        field_id,
        66847,
        ROW_ID,
        PLANTING_DATE_COVER_CROPS,
        field_id,
        66845,
        ROW_ID,
        HARVEST_DATE_COVER_CROPS,
        field_id,
        66846,
        ROW_ID,
    ]

    return query, params


def process_csv_and_execute(file_path: str, cursor: pymysql.cursors.Cursor) -> None:
    with open(file_path, newline="") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            logging.info("--------------------------------------------------------")
            logging.info(f"Processing project: {row['project_id']}, field: {row['field_id']}")
            logging.info(f"Attribute: {row['attribute_name']}")
            logging.info(f"Value: {row['value']}")

            query = ""
            params: list[str] = []
            if row["attribute_name"] == "Fall tillage":
                query, params = get_fall_tillage_migration_sql(row)
            elif row["attribute_name"] == "Winter Crop Type":
                query, params = get_winter_crop_migration_sql(row)

            if query:
                try:
                    logging.info(f"Executing SQL for field: {row['field_id']}")
                    logging.info(cursor.mogrify(query, params))
                    cursor.execute(query, params)
                    logging.info(f"✅ Executed: {row['attribute_name']} for field {row['field_id_1119']}")
                except Exception as e:
                    logging.info(f"❌ Failed to execute SQL for row: {row}")
                    logging.info(e)


def main() -> None:
    if len(sys.argv) != 2:
        logging.info("Usage: python migrate_missing_2018_data_155_to_1119.py <path_to_csv>")
        sys.exit(1)

    file_path = sys.argv[1]

    if not all([DB_HOST, DB_NAME, DB_USER, DB_PASSWORD]):
        logging.info("❌ Please set DB_HOST, DB_NAME, DB_USER, and DB_PASSWORD environment variables.")
        sys.exit(1)

    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME)
        cursor = connection.cursor()
        process_csv_and_execute(file_path, cursor)
        connection.commit()
        cursor.close()
        connection.close()
        logging.info("🎉 Done.")
    except Exception as e:
        logging.info("❌ Database connection failed:")
        logging.info(e)
        sys.exit(1)


# Usage python migrate_missing_2018_data_155_to_1119.py your_file.csv
if __name__ == "__main__":
    main()
