from fastapi import status

from phases.enums import PracticeRuleConditions, PracticeRuleTypes
from phases.stages import paths
from programs.enums import PracticeChange, PracticeChangeGroup


async def test_get_stage_practice_change_rules_api(mdl, async_client):
    stage = await mdl.Stage()
    await mdl.PracticeChangeRules(
        stage_id=stage.id,
        rule={
            "type": PracticeRuleTypes.GROUP_PRACTICE_SELECTION,
            "condition": PracticeRuleConditions.MAX,
            "target": PracticeChangeGroup.tillage,
            "value": 2,
        },
    )
    await mdl.PracticeChangeRules(
        stage_id=stage.id,
        rule={
            "type": PracticeRuleTypes.PRACTICE_DEPENDENCY,
            "condition": PracticeRuleConditions.ANY,
            "target": PracticeChange.nutrient_management,
            "value": [PracticeChange.reduced_till, PracticeChange.conventional_till],
        },
    )

    response = await async_client.get(f"{paths.base}/{stage.id}/practice_change_rules")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["stage_id"] == stage.id
    assert "rules" in response_data

    rules = response_data["rules"]
    assert len(rules) >= 2  # At least the 2 default group rules

    # Check that our custom rules are included
    rule1_found = any(
        rule.get("type") == PracticeRuleTypes.GROUP_PRACTICE_SELECTION
        and rule.get("target") == PracticeChangeGroup.tillage
        and rule.get("value") == 2
        for rule in rules
    )
    rule2_found = any(
        rule.get("type") == PracticeRuleTypes.PRACTICE_DEPENDENCY
        and rule.get("target") == PracticeChange.nutrient_management
        for rule in rules
    )

    assert rule1_found
    assert rule2_found


async def test_get_stage_practice_change_rules_api_without_eligibility_matrix(mdl, async_client):
    stage = await mdl.Stage()

    response = await async_client.get(f"{paths.base}/{stage.id}/practice_change_rules")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["stage_id"] == stage.id

    rules = response_data["rules"]

    # Should include default group rules
    group_selection_rules = [rule for rule in rules if rule.get("type") == PracticeRuleTypes.GROUP_PRACTICE_SELECTION]
    assert len(group_selection_rules) == 2

    # Should NOT include EligibilityMatrixRule
    eligibility_matrix_rules = [rule for rule in rules if rule.get("type") == PracticeRuleTypes.ELIGIBILITY_MATRIX]
    assert len(eligibility_matrix_rules) == 0


async def test_get_stage_practice_change_rules_api_with_eligibility_matrix(mdl, async_client):
    stage = await mdl.Stage()
    await mdl.StageEligibilityConfig(
        stage_id=stage.id,
        name="Test Config",
        eligibility_matrix={
            "practice_change_groups": ["Tillage practices", "Crop Practices"],
            "matrix": {
                "Conventional Till,No Cover Crop": [
                    ["Reduced Till", "Cover Crops"],
                    ["No Till", "Cover Crops"],
                ]
            },
        },
    )

    response = await async_client.get(f"{paths.base}/{stage.id}/practice_change_rules")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["stage_id"] == stage.id

    rules = response_data["rules"]

    # Should include default group rules
    group_selection_rules = [rule for rule in rules if rule.get("type") == PracticeRuleTypes.GROUP_PRACTICE_SELECTION]
    assert len(group_selection_rules) == 2

    # Should include EligibilityMatrixRule
    eligibility_matrix_rules = [rule for rule in rules if rule.get("type") == PracticeRuleTypes.ELIGIBILITY_MATRIX]
    assert len(eligibility_matrix_rules) == 1

    eligibility_rule = eligibility_matrix_rules[0]
    assert eligibility_rule["value"] is not None


async def test_get_stage_practice_change_rules_api_nonexistent_stage(async_client):
    nonexistent_stage_id = 99999

    response = await async_client.get(f"{paths.base}/{nonexistent_stage_id}/practice_change_rules")

    # The endpoint should handle nonexistent stages gracefully
    # Based on the implementation, it should return empty rules for nonexistent stages
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["stage_id"] == nonexistent_stage_id
    assert "rules" in response_data
