import httpx
import pytest
from fastapi import HTTPException

from phases.enums import PhaseTypes
from phases.router import (
    create_phase_crop_types,
    delete_phase_crop_types,
    get_phase_crop_types,
)
from phases.tests.test_async_phases import create_program, get_phase_id
from programs.enums import ProgramTemplate


async def test_get_phase_crop_types(async_client: httpx.AsyncClient, app_request: httpx.Request):
    program_id = await create_program(
        async_client,
        create_default_phases=True,
        precreate_monitoring=False,
        program_template=ProgramTemplate.event_based,
    )
    phase_id = await get_phase_id(async_client, program_id, phase_type=PhaseTypes.ENROLMENT)
    crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    assert len(crop_types_for_phase) > 0
    fallow_crop_type = next(
        (crop_type for crop_type in crop_types_for_phase if crop_type["regrow_name"] == "fallow"), None
    )
    assert fallow_crop_type is not None
    assert crop_types_for_phase[0]["regrow_name"] == "alfalfa"


async def test_get_phase_crop_types_phase_without_enabled_crops(
    async_client: httpx.AsyncClient, app_request: httpx.Request
):
    program_id = await create_program(
        async_client,
        create_default_phases=True,
        precreate_monitoring=False,
        program_template=ProgramTemplate.event_based,
    )
    phase_id = await get_phase_id(async_client, program_id, phase_type=PhaseTypes.DASHBOARDS)
    crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    assert len(crop_types_for_phase) == 0


async def test_create_phase_crop_types(async_client: httpx.AsyncClient, app_request: httpx.Request):
    program_id = await create_program(
        async_client,
        create_default_phases=True,
        precreate_monitoring=False,
        program_template=ProgramTemplate.event_based,
    )
    phase_id = await get_phase_id(async_client, program_id, phase_type=PhaseTypes.ENROLMENT)
    ids_to_create = [1]
    # delete and then recreate the crop type
    await delete_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id, crop_ids=ids_to_create)

    crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    created_crops = await create_phase_crop_types(
        request=app_request, program_id=program_id, phase_id=phase_id, crop_ids=ids_to_create
    )
    assert len(created_crops) == 1

    new_crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    assert len(new_crop_types_for_phase) == len(crop_types_for_phase) + 1


async def test_create_phase_crop_types_existing(async_client: httpx.AsyncClient, app_request: httpx.Request):
    program_id = await create_program(
        async_client,
        create_default_phases=True,
        precreate_monitoring=False,
        program_template=ProgramTemplate.event_based,
    )
    phase_id = await get_phase_id(async_client, program_id, phase_type=PhaseTypes.ENROLMENT)
    ids_to_create = [1]

    with pytest.raises(HTTPException) as exc_info:
        await create_phase_crop_types(
            request=app_request, program_id=program_id, phase_id=phase_id, crop_ids=ids_to_create
        )

    assert exc_info.value.status_code == 400
    assert "already enabled" in str(exc_info.value.detail)


async def test_delete_phase_crop_types(async_client: httpx.AsyncClient, app_request: httpx.Request):
    program_id = await create_program(
        async_client,
        create_default_phases=True,
        precreate_monitoring=False,
        program_template=ProgramTemplate.event_based,
    )
    phase_id = await get_phase_id(async_client, program_id, phase_type=PhaseTypes.ENROLMENT)
    crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    ids_to_delete = [crop_types_for_phase[0]["regrow_id"], crop_types_for_phase[1]["regrow_id"]]
    deleted_ids = await delete_phase_crop_types(
        request=app_request, program_id=program_id, phase_id=phase_id, crop_ids=ids_to_delete
    )
    assert len(deleted_ids) == 2

    new_crop_types_for_phase = await get_phase_crop_types(request=app_request, program_id=program_id, phase_id=phase_id)
    for deleted_id in deleted_ids:
        assert deleted_id not in [crop_type["regrow_id"] for crop_type in new_crop_types_for_phase]
