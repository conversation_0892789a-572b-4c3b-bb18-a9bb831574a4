from contextlib import nullcontext as does_not_raise
from datetime import datetime

import pytest
from fastapi import HTTPException

from phases.enums import PracticeRuleConditions, PracticeRuleTypes, StageTypes
from phases.schema import (
    EligibilityMatrix,
    EligibilityMatrixRule,
    GroupSelectionRule,
    PracticeDependencyRule,
    StageRequestNoParentBase,
    StageResponse,
)
from programs.enums import PracticeChange, PracticeChangeGroup


@pytest.mark.parametrize(
    "stage_type,required,exception",
    [
        (StageTypes.ASSIGN_PRACTICES, True, does_not_raise()),
        (StageTypes.ASSIGN_PRACTICES, False, pytest.raises(HTTPException)),
        (StageTypes.NUTRIENT_MGMT_INTENDED, True, does_not_raise()),
        (StageTypes.NUTRIENT_MGMT_INTENDED, False, does_not_raise()),
    ],
)
def test_stage_validator(stage_type: StageTypes, required: bool, exception):
    with exception:
        StageRequestNoParentBase(
            name="foo",
            fmi_import_end_date=datetime(2020, 1, 1),
            fmi_import_start_date=datetime(2019, 1, 1),
            order=1,
            description="foo",
            type_=stage_type,
            required=required,
            locked=True,
        )


@pytest.mark.parametrize(
    "stage_type,has_required_entry_section,has_interventions_section",
    [
        (StageTypes.ASSIGN_PRACTICES, False, False),
        (StageTypes.IRRIGATION, True, False),
        (StageTypes.NUTRIENT_MGMT_INTENDED, True, True),
        (StageTypes.NUTRIENT_EVENTS, True, False),
        (StageTypes.IRRIGATION_EVENTS, True, False),
    ],
)
def test_stage_flags(
    stage_type: StageTypes,
    has_required_entry_section: bool,
    has_interventions_section: bool,
):
    stage = StageResponse(
        name="foo",
        fmi_import_end_date=datetime(2020, 1, 1),
        fmi_import_start_date=datetime(2019, 1, 1),
        order=1,
        description="foo",
        type_=stage_type,
        required=True,
        locked=True,
    )
    assert stage.has_required_entry_section == has_required_entry_section
    assert stage.has_interventions_section == has_interventions_section


def test_stage_practice_dependency_rule():
    rule = PracticeDependencyRule(
        target=PracticeChange.nutrient_management,
        condition=PracticeRuleConditions.ANY,
        value=[PracticeChange.reduced_till, PracticeChange.conventional_till],
    )
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.nutrient_management, PracticeChange.reduced_till]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.nutrient_management]) is False

    rule = PracticeDependencyRule(
        target=PracticeChange.nutrient_management,
        condition=PracticeRuleConditions.ALL,
        value=[PracticeChange.reduced_till, PracticeChange.cover_crops],
    )
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.nutrient_management, PracticeChange.reduced_till]) is False
    assert (
        rule.is_valid(
            assigned_practices=[
                PracticeChange.nutrient_management,
                PracticeChange.reduced_till,
                PracticeChange.cover_crops,
            ]
        )
        is True
    )

    rule = PracticeDependencyRule(
        target=PracticeChange.nutrient_management,
        condition=PracticeRuleConditions.NONE,
        value=[PracticeChange.reduced_till, PracticeChange.cover_crops],
    )
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.nutrient_management]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.nutrient_management, PracticeChange.reduced_till]) is False
    assert (
        rule.is_valid(
            assigned_practices=[
                PracticeChange.nutrient_management,
                PracticeChange.conventional_till,
            ]
        )
        is True
    )


def test_stage_practice_group_selection_rule():
    rule = GroupSelectionRule(target=PracticeChangeGroup.tillage, condition=PracticeRuleConditions.MAX, value=1)
    assert rule.is_valid(assigned_practices=[PracticeChange.cover_crops]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till, PracticeChange.cover_crops]) is True
    assert rule.is_valid(assigned_practices=[PracticeChange.reduced_till, PracticeChange.conventional_till]) is False


def test_eligibility_matrix_rule_creation():
    """Test EligibilityMatrixRule creation and validation."""
    # Create a simple eligibility matrix
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage, PracticeChangeGroup.crop},
        matrix={
            "Conventional Till,No Cover Crop": [
                [PracticeChange.reduced_till, PracticeChange.cover_crops],
                [PracticeChange.no_till, PracticeChange.cover_crops],
            ]
        },
    )

    # Test rule creation
    rule = EligibilityMatrixRule(value=eligibility_matrix)
    assert rule.type == PracticeRuleTypes.ELIGIBILITY_MATRIX
    assert rule.value == eligibility_matrix
    assert rule.target == {PracticeChangeGroup.tillage, PracticeChangeGroup.crop}


def test_eligibility_matrix_rule_validation():
    """Test EligibilityMatrixRule type validation."""
    eligibility_matrix = EligibilityMatrix(practice_change_groups={PracticeChangeGroup.tillage}, matrix={})

    # Test valid type
    rule = EligibilityMatrixRule(value=eligibility_matrix)
    assert rule.type == PracticeRuleTypes.ELIGIBILITY_MATRIX

    # Test invalid type (should raise validation error)
    with pytest.raises(ValueError, match="Invalid type"):
        EligibilityMatrixRule(
            type=PracticeRuleTypes.PRACTICE_DEPENDENCY,
            value=eligibility_matrix,
        )


def test_eligibility_matrix_rule_is_valid_signature():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.no_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = [PracticeChange.no_till]
    baseline_practices = [PracticeChange.conventional_till]

    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    result = rule.is_valid(set(assigned_practices), baseline_practices)
    assert result is True


def test_eligibility_matrix_rule_is_valid_with_string_practices():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
                [PracticeChange.no_till],
            ],
            "Reduced Till": [
                [PracticeChange.no_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices_str = ["Reduced Till"]
    baseline_practices_str = ["Conventional Till"]

    result = rule.is_valid(assigned_practices_str, baseline_practices_str)
    assert result is True

    assigned_practices_invalid = ["Invalid Practice"]
    with pytest.raises(ValueError, match="'Invalid Practice' is not a valid PracticeChange"):
        rule.is_valid(assigned_practices_invalid, baseline_practices_str)

    assigned_practices_set = {"No Till"}
    result = rule.is_valid(assigned_practices_set, baseline_practices_str)
    assert result is True


def test_eligibility_matrix_rule_is_valid_complex_matrix():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage, PracticeChangeGroup.crop},
        matrix={
            "Conventional Till,No Cover Crop": [
                [PracticeChange.reduced_till, PracticeChange.no_cover_crop],
                [PracticeChange.no_till, PracticeChange.cover_crops],
                [PracticeChange.reduced_till, PracticeChange.cover_crops],
            ],
            "Cover Crops,Reduced Till": [
                [PracticeChange.no_till, PracticeChange.premium_cover_crops],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Reduced Till", "Cover Crops"]
    baseline_practices = ["Conventional Till", "No Cover Crop"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    assigned_practices = ["No Till", "Cover Crops"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    assigned_practices = ["No Till", "Cover Crops", "Rate reduction"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    assigned_practices = ["Conventional Till", "Premium Cover Crops"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False


def test_eligibility_matrix_rule_is_valid_no_eligible_practices():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["No Till"]
    baseline_practices = ["Reduced Till"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False


def test_eligibility_matrix_rule_is_valid_invalid_practice_strings():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Non-existent Practice"]
    baseline_practices = ["Conventional Till"]
    with pytest.raises(ValueError, match="'Non-existent Practice' is not a valid PracticeChange"):
        rule.is_valid(assigned_practices, baseline_practices)

    assigned_practices = ["Reduced Till", "Invalid Practice"]
    with pytest.raises(ValueError, match="'Invalid Practice' is not a valid PracticeChange"):
        rule.is_valid(assigned_practices, baseline_practices)


def test_eligibility_matrix_rule_is_valid_empty_inputs():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = []
    baseline_practices = ["Conventional Till"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False

    assigned_practices = ["Reduced Till"]
    baseline_practices = []
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False


def test_eligibility_matrix_rule_is_valid_invalid_baseline_practices():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Reduced Till"]
    baseline_practices = ["Invalid Baseline Practice"]
    with pytest.raises(ValueError, match="'Invalid Baseline Practice' is not a valid PracticeChange"):
        rule.is_valid(assigned_practices, baseline_practices)


def test_eligibility_matrix_rule_is_valid_crop_only_matrix():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.crop},
        matrix={
            "No Cover Crop": [
                [PracticeChange.cover_crops],
                [PracticeChange.premium_cover_crops],
            ],
            "Cover Crops": [
                [PracticeChange.premium_cover_crops],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Cover Crops"]
    baseline_practices = ["No Cover Crop"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    assigned_practices = ["Premium Cover Crops"]
    baseline_practices = ["Cover Crops"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True


def test_eligibility_matrix_rule_is_valid_irrigation_only_matrix():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.irrigation},
        matrix={
            "Traditional cascade flooding": [
                [PracticeChange.alternating_wet_dry],
                [PracticeChange.drip_irrigation],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Alternating wetting & drying"]
    baseline_practices = ["Traditional cascade flooding"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True

    assigned_practices = ["Drip irrigation"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True


def test_eligibility_matrix_rule_is_valid_empty_matrix():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={},
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Reduced Till"]
    baseline_practices = ["Conventional Till"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False


def test_eligibility_matrix_rule_is_valid_with_none_baseline_practices():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Reduced Till"]
    baseline_practices = ["Conventional Till", None]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True


def test_eligibility_matrix_rule_is_valid_practice_group_mismatch():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage},
        matrix={
            "Conventional Till": [
                [PracticeChange.reduced_till],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Rate reduction"]
    baseline_practices = ["Conventional Till"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is False


def test_eligibility_matrix_rule_is_valid_mixed_practice_groups():
    eligibility_matrix = EligibilityMatrix(
        practice_change_groups={PracticeChangeGroup.tillage, PracticeChangeGroup.crop},
        matrix={
            "Conventional Till,No Cover Crop": [
                [PracticeChange.reduced_till, PracticeChange.cover_crops],
            ],
        },
    )
    rule = EligibilityMatrixRule(value=eligibility_matrix)

    assigned_practices = ["Reduced Till", "Cover Crops", "Rate reduction"]
    baseline_practices = ["Conventional Till", "No Cover Crop"]
    result = rule.is_valid(assigned_practices, baseline_practices)
    assert result is True
