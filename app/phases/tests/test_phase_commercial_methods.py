from datetime import datetime

from sqlalchemy import select

from helper.helper import run_query
from phases.enums import CommercialsPaymentForType, RegionTier
from phases.methods import (
    create_or_update_commercial_regions_for_phase,
    create_phase_commercials,
    delete_phase_commercials,
    get_phase_commercials,
    get_phase_commercials_for_phase,
    update_phase_commercial,
)
from phases.model import (
    CommercialPractices,
    CommercialRegions,
    CommercialRegionTiers,
    PhaseCommercials,
)
from phases.schema import (
    CommercialRegionsRequest,
    PhaseCommercialsRequest,
    PhaseCommercialsResponse,
    PhaseCommercialsUpdateInDB,
)
from programs.enums import PayoutStructure, PracticeChange


async def test_get_phase_commercial(mdl, app_request):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    region_1 = await mdl.Regions()
    region_2 = await mdl.Regions()
    region_3 = await mdl.Regions()
    region_4 = await mdl.Regions()
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_1.id)
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_2.id)
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_3.id)
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_4.id)
    commercial_1 = await mdl.PhaseCommercials(
        phase_id=phase.id,
        payment=10,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    commercial_2 = await mdl.PhaseCommercials(
        phase_id=phase.id,
        payment=20,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    await mdl.CommercialPractices(commercial_id=commercial_1.id, practice_change=PracticeChange.cover_crops)
    await mdl.CommercialPractices(commercial_id=commercial_2.id, practice_change=PracticeChange.no_till)
    await mdl.CommercialRegionTiers(commercial_id=commercial_1.id, region_tier=RegionTier.NON_TARGETED)
    await mdl.CommercialRegionTiers(commercial_id=commercial_2.id, region_tier=RegionTier.TARGETED)

    # pay per practice
    commercials = await get_phase_commercials(
        request=app_request,
        commercial_ids=[commercial_1.id, commercial_2.id],
        payout_structure=PayoutStructure.pay_per_practice,
    )
    expected_commercials = [
        PhaseCommercialsResponse(
            id=commercial_1.id,
            phase_id=phase.id,
            start_date=commercial_1.start_date,
            end_date=commercial_1.end_date,
            payment=commercial_1.payment,
            payment_for=commercial_1.payment_for,
            practice_changes=[PracticeChange.cover_crops],
            region_tier=RegionTier.NON_TARGETED,
        ),
        PhaseCommercialsResponse(
            id=commercial_2.id,
            phase_id=phase.id,
            start_date=commercial_2.start_date,
            end_date=commercial_2.end_date,
            payment=commercial_2.payment,
            payment_for=commercial_2.payment_for,
            practice_changes=[PracticeChange.no_till],
            region_tier=RegionTier.TARGETED,
        ),
    ]
    assert sorted(commercials, key=lambda x: x.id) == expected_commercials

    # not pay per practice
    commercials = await get_phase_commercials(
        request=app_request,
        commercial_ids=[commercial_1.id, commercial_2.id],
        payout_structure=PayoutStructure.pay_per_area_enrolled,
    )
    expected_commercials = [
        PhaseCommercialsResponse(
            id=commercial_1.id,
            phase_id=phase.id,
            start_date=commercial_1.start_date,
            end_date=commercial_1.end_date,
            payment=commercial_1.payment,
            payment_for=commercial_1.payment_for,
        ),
        PhaseCommercialsResponse(
            id=commercial_2.id,
            phase_id=phase.id,
            start_date=commercial_2.start_date,
            end_date=commercial_2.end_date,
            payment=commercial_2.payment,
            payment_for=commercial_2.payment_for,
        ),
    ]
    assert sorted(commercials, key=lambda x: x.id) == expected_commercials


async def test_get_phase_commercials_for_phase(mdl, app_request):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    other_phase = await mdl.Phases(program_id=program.id)
    region_1 = await mdl.Regions()
    region_2 = await mdl.Regions()
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_1.id)
    await mdl.CommercialRegions(phase_id=other_phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_2.id)
    commercial_1 = await mdl.PhaseCommercials(
        phase_id=phase.id,
        payment=10,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    commercial_2 = await mdl.PhaseCommercials(
        phase_id=other_phase.id,
        payment=10,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    await mdl.CommercialPractices(commercial_id=commercial_1.id, practice_change=PracticeChange.cover_crops)
    await mdl.CommercialPractices(commercial_id=commercial_2.id, practice_change=PracticeChange.no_till)
    await mdl.CommercialRegionTiers(commercial_id=commercial_1.id, region_tier=RegionTier.NON_TARGETED)
    await mdl.CommercialRegionTiers(commercial_id=commercial_2.id, region_tier=RegionTier.NON_TARGETED)

    commercials = await get_phase_commercials_for_phase(
        request=app_request, phase_id=phase.id, payout_structure=PayoutStructure.pay_per_practice
    )
    expected_commercials = [
        PhaseCommercialsResponse(
            id=commercial_1.id,
            phase_id=phase.id,
            start_date=commercial_1.start_date,
            end_date=commercial_1.end_date,
            payment=commercial_1.payment,
            payment_for=commercial_1.payment_for,
            practice_changes=[PracticeChange.cover_crops],
            region_tier=RegionTier.NON_TARGETED,
        ),
    ]
    assert commercials == expected_commercials


async def test_create_phase_commercials_pay_per_practice(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    region_1 = await mdl.Regions()
    region_2 = await mdl.Regions()
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_1.id)
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_2.id)
    commercial_requests = [
        PhaseCommercialsRequest(
            phase_id=phase.id,
            start_date=datetime(2025, 1, 1),
            end_date=datetime(2025, 12, 31),
            payment=10,
            payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
            practice_changes=[PracticeChange.cover_crops],
            region_tier=RegionTier.NON_TARGETED,
        ),
        PhaseCommercialsRequest(
            phase_id=phase.id,
            start_date=datetime(2025, 1, 1),
            end_date=datetime(2025, 12, 31),
            payment=20,
            payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
            practice_changes=[PracticeChange.no_till],
            region_tier=RegionTier.TARGETED,
        ),
    ]
    commercials = await create_phase_commercials(request=app_request, commercial_requests=commercial_requests)
    assert len(commercials) == 2

    async with db_session_maker() as s:
        query = select(PhaseCommercials).where(PhaseCommercials.phase_id == phase.id).order_by(PhaseCommercials.payment)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        commercial_1_id = res[0].id
        commercial_2_id = res[1].id
        assert (
            res[0].to_dict()
            == PhaseCommercials(
                id=res[0].id,
                phase_id=phase.id,
                start_date=datetime(2025, 1, 1),
                end_date=datetime(2025, 12, 31),
                payment=10,
                payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == PhaseCommercials(
                id=res[1].id,
                phase_id=phase.id,
                start_date=datetime(2025, 1, 1),
                end_date=datetime(2025, 12, 31),
                payment=20,
                payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(CommercialPractices)
            .where(CommercialPractices.commercial_id.in_([commercial_1_id, commercial_2_id]))
            .order_by(CommercialPractices.commercial_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == CommercialPractices(
                id=res[0].id, commercial_id=commercial_1_id, practice_change=PracticeChange.cover_crops
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == CommercialPractices(
                id=res[1].id, commercial_id=commercial_2_id, practice_change=PracticeChange.no_till
            ).to_dict()
        )

        query = (
            select(CommercialRegionTiers)
            .where(CommercialRegionTiers.commercial_id.in_([commercial_1_id, commercial_2_id]))
            .order_by(CommercialRegionTiers.commercial_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == CommercialRegionTiers(
                id=res[0].id, commercial_id=commercial_1_id, region_tier=RegionTier.NON_TARGETED
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == CommercialRegionTiers(
                id=res[1].id, commercial_id=commercial_2_id, region_tier=RegionTier.TARGETED
            ).to_dict()
        )


async def test_update_phase_commercial(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    commercial = await mdl.PhaseCommercials(
        phase_id=phase.id, payment=10, payment_for=CommercialsPaymentForType.FIELD_AREA
    )

    await update_phase_commercial(
        request=app_request,
        commercial_id=commercial.id,
        commercial_update=PhaseCommercialsUpdateInDB(payment=20),
        payout_structure=PayoutStructure.pay_per_area_enrolled,
    )

    async with db_session_maker() as s:
        query = select(PhaseCommercials).where(PhaseCommercials.phase_id == phase.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == PhaseCommercials(
                id=commercial.id,
                phase_id=commercial.phase_id,
                start_date=commercial.start_date,
                end_date=commercial.end_date,
                payment=20,
                payment_for=commercial.payment_for,
                deleted_at=commercial.deleted_at,
            ).to_dict()
        )


async def test_delete_phase_commercials_pay_per_practice(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    region = await mdl.Regions()
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region.id)
    commercial_1 = await mdl.PhaseCommercials(
        phase_id=phase.id,
        payment=10,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    commercial_2 = await mdl.PhaseCommercials(
        phase_id=phase.id,
        payment=20,
        payment_for=CommercialsPaymentForType.FIELD_PRACTICE,
    )
    await mdl.CommercialPractices(commercial_id=commercial_1.id, practice_change=PracticeChange.cover_crops)
    commercial_practice_2 = await mdl.CommercialPractices(
        commercial_id=commercial_2.id, practice_change=PracticeChange.no_till
    )
    await mdl.CommercialRegionTiers(commercial_id=commercial_1.id, region_tier=RegionTier.NON_TARGETED)
    commercial_region_tier_2 = await mdl.CommercialRegionTiers(
        commercial_id=commercial_2.id, region_tier=RegionTier.NON_TARGETED
    )

    await delete_phase_commercials(
        request=app_request, commercial_ids=[commercial_1.id], payout_structure=PayoutStructure.pay_per_practice
    )

    async with db_session_maker() as s:
        query = (
            select(PhaseCommercials)
            .where(PhaseCommercials.id.in_([commercial_1.id, commercial_2.id]))
            .order_by(PhaseCommercials.id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert res[0].deleted_at is not None
        assert res[1].deleted_at is None

        query = (
            select(CommercialPractices)
            .where(CommercialPractices.commercial_id.in_([commercial_1.id, commercial_2.id]))
            .where(CommercialPractices.deleted_at.is_(None))
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert res[0].to_dict() == commercial_practice_2.to_dict()

        query = (
            select(CommercialRegionTiers)
            .where(CommercialRegionTiers.commercial_id.in_([commercial_1.id, commercial_2.id]))
            .where(CommercialRegionTiers.deleted_at.is_(None))
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert res[0].to_dict() == commercial_region_tier_2.to_dict()


async def test_create_or_update_commercial_regions_for_phase(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    region_1 = await mdl.Regions()
    region_2 = await mdl.Regions()
    region_3 = await mdl.Regions()
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_1.id)
    await mdl.CommercialRegions(phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_2.id)

    await create_or_update_commercial_regions_for_phase(
        request=app_request,
        phase_id=phase.id,
        commercial_region_requests=[
            CommercialRegionsRequest(region_tier=RegionTier.TARGETED, region_id=region_1.id),
            CommercialRegionsRequest(region_tier=RegionTier.TARGETED, region_id=region_2.id),
            CommercialRegionsRequest(region_tier=RegionTier.NON_TARGETED, region_id=region_3.id),
        ],
    )

    async with db_session_maker() as s:
        query = (
            select(CommercialRegions)
            .where(CommercialRegions.phase_id == phase.id)
            .where(CommercialRegions.deleted_at.is_(None))
            .order_by(CommercialRegions.region_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == CommercialRegions(
                id=res[0].id, phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_1.id
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == CommercialRegions(
                id=res[1].id, phase_id=phase.id, region_tier=RegionTier.TARGETED, region_id=region_2.id
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == CommercialRegions(
                id=res[2].id, phase_id=phase.id, region_tier=RegionTier.NON_TARGETED, region_id=region_3.id
            ).to_dict()
        )
