from phases.schema import EligibilityMatrix, EligibilityMatrixRule
from programs.enums import PracticeChange, PracticeChangeGroup


class TestEligibilityMatrix:
    def test_get_eligible_practices_for_baseline_tillage_only(self):
        """Test get_eligible_practices_for_baseline with tillage practices only."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.tillage},
            matrix={
                "Conventional Till": [
                    [PracticeChange.reduced_till],
                    [PracticeChange.no_till],
                ],
                "Reduced Till": [
                    [PracticeChange.no_till],
                ],
            },
        )

        # Test conventional till baseline
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Conventional Till"],
        )
        expected = [
            [PracticeChange.reduced_till],
            [PracticeChange.no_till],
        ]
        assert result == expected

        # Test reduced till baseline
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Reduced Till"],
        )
        expected = [[PracticeChange.no_till]]
        assert result == expected

    def test_get_eligible_practices_for_baseline_crop_only(self):
        """Test get_eligible_practices_for_baseline with crop practices only."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.crop},
            matrix={
                "No Cover Crop": [
                    [PracticeChange.cover_crops],
                    [PracticeChange.premium_cover_crops],
                ],
                "Cover Crops": [
                    [PracticeChange.premium_cover_crops],
                ],
            },
        )

        # Test no cover crop baseline
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["No Cover Crop"],
        )
        expected = [
            [PracticeChange.cover_crops],
            [PracticeChange.premium_cover_crops],
        ]
        assert result == expected

    def test_get_eligible_practices_for_baseline_tillage_and_crop(self):
        """Test get_eligible_practices_for_baseline with both tillage and crop practices."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
            },
            matrix={
                "Conventional Till,No Cover Crop": [
                    [PracticeChange.reduced_till, PracticeChange.no_cover_crop],
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                ],
                "Cover Crops,Reduced Till": [
                    [PracticeChange.no_till, PracticeChange.premium_cover_crops],
                ],
            },
        )

        # Test conventional till + no cover crop baseline
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Conventional Till", "No Cover Crop"],
        )
        expected = [
            [PracticeChange.reduced_till, PracticeChange.no_cover_crop],
            [PracticeChange.no_till, PracticeChange.cover_crops],
        ]
        assert result == expected

        # Test reduced till + cover crops baseline
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Reduced Till", "Cover Crops"],
        )
        expected = [[PracticeChange.no_till, PracticeChange.premium_cover_crops]]
        assert result == expected

    def test_get_eligible_practices_for_baseline_all_practice_groups(self):
        """Test get_eligible_practices_for_baseline with tillage, crop, and irrigation practices."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
                PracticeChangeGroup.irrigation,
            },
            matrix={
                "Conventional Till,No Cover Crop,Traditional cascade flooding": [
                    [
                        PracticeChange.reduced_till,
                        PracticeChange.cover_crops,
                        PracticeChange.alternating_wet_dry,
                    ],
                    [
                        PracticeChange.no_till,
                        PracticeChange.premium_cover_crops,
                        PracticeChange.alternating_wet_dry,
                    ],
                ],
            },
        )

        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=[
                "Conventional Till",
                "No Cover Crop",
                "Traditional cascade flooding",
            ],
        )
        expected = [
            [
                PracticeChange.reduced_till,
                PracticeChange.cover_crops,
                PracticeChange.alternating_wet_dry,
            ],
            [
                PracticeChange.no_till,
                PracticeChange.premium_cover_crops,
                PracticeChange.alternating_wet_dry,
            ],
        ]
        assert result == expected

    def test_get_eligible_practices_for_baseline_no_matching_key(self):
        """Test get_eligible_practices_for_baseline when baseline doesn't match any matrix key."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
            },
            matrix={
                "Conventional Till,No Cover Crop": [
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                ],
            },
        )

        # Test baseline that doesn't exist in matrix
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["No Till", "Premium Cover Crops"],
        )
        assert result == []

    def test_get_eligible_practices_for_baseline_with_none_values(self):
        """Test get_eligible_practices_for_baseline with None baseline practices."""
        # Test that None values are properly handled in baseline key building
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.tillage},
            matrix={
                "Conventional Till": [
                    [PracticeChange.reduced_till],
                    [PracticeChange.no_till],
                ],
            },
        )

        # Test with valid tillage practice
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Conventional Till"],
        )
        expected = [
            [PracticeChange.reduced_till],
            [PracticeChange.no_till],
        ]
        assert result == expected

        # Test with empty baseline practices list
        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=[],
        )
        # Should return empty list since empty string key is not in the matrix
        assert result == []

    def test_get_eligible_practices_for_baseline_empty_matrix(self):
        """Test get_eligible_practices_for_baseline with empty matrix."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.tillage},
            matrix={},
        )

        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Conventional Till"],
        )
        assert result == []

    def test_get_eligible_practices_for_baseline_irrigation_only(self):
        """Test get_eligible_practices_for_baseline with irrigation practices only."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.irrigation},
            matrix={
                "Traditional cascade flooding": [
                    [PracticeChange.alternating_wet_dry],
                    [PracticeChange.drip_irrigation],
                ],
            },
        )

        result = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Traditional cascade flooding"],
        )
        expected = [
            [PracticeChange.alternating_wet_dry],
            [PracticeChange.drip_irrigation],
        ]
        assert result == expected


class TestEligibilityMatrixOrdering:
    """Test that matrix key ordering is consistent across all lookup functions."""

    def test_get_eligible_practices_ordering_consistency(self):
        """Test that get_eligible_practices_for_baseline handles different input orders consistently."""
        # Create matrix with a key that has multiple practices
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
            },
            matrix={
                "Conventional Till,No Cover Crop": [
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                ],
            },
        )

        # Test different input orders should give same result
        result1 = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["Conventional Till", "No Cover Crop"]
        )
        result2 = eligibility_matrix.get_eligible_practices_for_baseline(
            baseline_practices=["No Cover Crop", "Conventional Till"]  # Different order
        )

        expected = [
            [PracticeChange.reduced_till, PracticeChange.cover_crops],
            [PracticeChange.no_till, PracticeChange.cover_crops],
        ]

        assert result1 == expected
        assert result2 == expected
        assert result1 == result2  # Both should be identical

    def test_eligibility_matrix_rule_ordering_consistency(self):
        """Test that EligibilityMatrixRule.is_valid handles different input orders consistently."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
            },
            matrix={
                "Conventional Till,No Cover Crop": [
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                ],
            },
        )
        rule = EligibilityMatrixRule(value=eligibility_matrix)

        # Test different baseline practice orders
        assigned_practices = ["Reduced Till", "Cover Crops"]

        result1 = rule.is_valid(
            assigned_practices=assigned_practices,
            baseline_practices=["Conventional Till", "No Cover Crop"],
        )
        result2 = rule.is_valid(
            assigned_practices=assigned_practices,
            baseline_practices=[
                "No Cover Crop",
                "Conventional Till",
            ],  # Different order
        )

        assert result1 is True
        assert result2 is True
        assert result1 == result2  # Both should be identical

    def test_json_parsing_ordering_consistency(self):
        """Test that JSON parsing normalizes keys consistently regardless of input order."""
        from phases.schema import StageEligibilityConfigRequest

        # Test JSON with different key orders
        json_data1 = {
            "practice_change_groups": ["Tillage practices", "Crop Practices"],
            "matrix": {
                "Conventional Till,No Cover Crop": [
                    ["Reduced Till", "Cover Crops"],
                ]
            },
        }

        json_data2 = {
            "practice_change_groups": ["Tillage practices", "Crop Practices"],
            "matrix": {
                "No Cover Crop,Conventional Till": [  # Different order in key
                    ["Reduced Till", "Cover Crops"],
                ]
            },
        }

        # Parse both JSON structures
        config1 = StageEligibilityConfigRequest(stage_id=1, name="Test Config 1", eligibility_matrix=json_data1)
        config2 = StageEligibilityConfigRequest(stage_id=1, name="Test Config 2", eligibility_matrix=json_data2)

        # Both should result in the same normalized matrix
        matrix1 = config1.eligibility_matrix
        matrix2 = config2.eligibility_matrix

        assert matrix1 is not None
        assert matrix2 is not None

        # Both matrices should have the same normalized key
        assert "Conventional Till,No Cover Crop" in matrix1.matrix
        assert "Conventional Till,No Cover Crop" in matrix2.matrix

        # Both should give same results for lookups
        result1 = matrix1.get_eligible_practices_for_baseline(baseline_practices=["Conventional Till", "No Cover Crop"])
        result2 = matrix2.get_eligible_practices_for_baseline(baseline_practices=["No Cover Crop", "Conventional Till"])

        assert result1 == result2

    def test_three_practice_ordering_consistency(self):
        """Test ordering consistency with three practices (more complex case)."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={
                PracticeChangeGroup.tillage,
                PracticeChangeGroup.crop,
                PracticeChangeGroup.irrigation,
            },
            matrix={
                "Conventional Till,No Cover Crop,Traditional cascade flooding": [
                    [
                        PracticeChange.reduced_till,
                        PracticeChange.cover_crops,
                        PracticeChange.alternating_wet_dry,
                    ],
                ],
            },
        )

        # Test all possible orderings of the same three practices
        orderings = [
            ["Conventional Till", "No Cover Crop", "Traditional cascade flooding"],
            ["Conventional Till", "Traditional cascade flooding", "No Cover Crop"],
            ["No Cover Crop", "Conventional Till", "Traditional cascade flooding"],
            ["No Cover Crop", "Traditional cascade flooding", "Conventional Till"],
            ["Traditional cascade flooding", "Conventional Till", "No Cover Crop"],
            ["Traditional cascade flooding", "No Cover Crop", "Conventional Till"],
        ]

        expected = [
            [
                PracticeChange.reduced_till,
                PracticeChange.cover_crops,
                PracticeChange.alternating_wet_dry,
            ],
        ]

        results = []
        for ordering in orderings:
            result = eligibility_matrix.get_eligible_practices_for_baseline(baseline_practices=ordering)
            results.append(result)
            assert result == expected, f"Failed for ordering: {ordering}"

        # All results should be identical
        for i in range(1, len(results)):
            assert results[0] == results[i], f"Results differ between orderings {orderings[0]} and {orderings[i]}"

    def test_normalize_key_function_directly(self):
        """Test the _normalize_key function directly."""
        # Test that the normalization function works correctly
        practices1 = ["Conventional Till", "No Cover Crop"]
        practices2 = ["No Cover Crop", "Conventional Till"]
        practices3 = [
            "Traditional cascade flooding",
            "Conventional Till",
            "No Cover Crop",
        ]
        practices4 = [
            "No Cover Crop",
            "Traditional cascade flooding",
            "Conventional Till",
        ]

        key1 = EligibilityMatrix.normalize_key(practices1)
        key2 = EligibilityMatrix.normalize_key(practices2)
        key3 = EligibilityMatrix.normalize_key(practices3)
        key4 = EligibilityMatrix.normalize_key(practices4)

        # Different orders should produce same normalized key
        assert key1 == key2
        assert key1 == "Conventional Till,No Cover Crop"

        assert key3 == key4
        assert key3 == "Conventional Till,No Cover Crop,Traditional cascade flooding"
