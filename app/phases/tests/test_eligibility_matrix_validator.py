import pytest
from pydantic import ValidationError

from phases.schema import EligibilityMatrix, StageEligibilityConfigRequest
from programs.enums import PracticeChange, PracticeChangeGroup


class TestParseEligibilityMatrix:
    """Test the parse_eligibility_matrix validator in StageEligibilityConfigRequest."""

    def test_parse_eligibility_matrix_valid_json(self):
        """Test parsing valid JSON into EligibilityMatrix."""
        valid_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {
                "practice_change_groups": ["Tillage practices", "Crop Practices"],
                "matrix": {
                    "Conventional Till,No Cover Crop": [
                        ["Reduced Till", "No Cover Crop"],
                        ["No Till", "Basic Cover Crops"],
                    ],
                    "No Till,Premium Cover Crops": [["No Till", "Basic Cover Crops"]],
                },
            },
        }

        request = StageEligibilityConfigRequest(**valid_json)

        assert request.eligibility_matrix is not None
        assert len(request.eligibility_matrix.practice_change_groups) == 2
        assert PracticeChangeGroup.tillage in request.eligibility_matrix.practice_change_groups
        assert PracticeChangeGroup.crop in request.eligibility_matrix.practice_change_groups

        # Check matrix structure
        assert len(request.eligibility_matrix.matrix) == 2

        # Check one of the matrix entries
        key_found = False
        for key, practices in request.eligibility_matrix.matrix.items():
            if PracticeChange.conventional_till in key and PracticeChange.no_cover_crop in key:
                key_found = True
                assert len(practices) == 2
                assert practices[0] == [
                    PracticeChange.no_cover_crop,
                    PracticeChange.reduced_till,
                ]
                assert practices[1] == [
                    PracticeChange.basic_cover_crops,
                    PracticeChange.no_till,
                ]
                break
        assert key_found, "Expected matrix key not found"

    def test_parse_eligibility_matrix_none_value(self):
        """Test that None value is handled correctly."""
        request_data = {"stage_id": 1, "name": "Test Stage", "preserved_baseline": False, "eligibility_matrix": None}

        request = StageEligibilityConfigRequest(**request_data)
        assert request.eligibility_matrix is None

    def test_parse_eligibility_matrix_already_parsed(self):
        """Test that already parsed EligibilityMatrix is handled correctly."""
        eligibility_matrix = EligibilityMatrix(
            practice_change_groups={PracticeChangeGroup.tillage},
            matrix={"Conventional Till": [[PracticeChange.reduced_till]]},
        )

        request_data = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": eligibility_matrix,
        }

        request = StageEligibilityConfigRequest(**request_data)
        # Check that the content is preserved correctly
        assert request.eligibility_matrix.practice_change_groups == eligibility_matrix.practice_change_groups
        assert request.eligibility_matrix.matrix == eligibility_matrix.matrix

    @pytest.mark.parametrize(
        "invalid_input,expected_error",
        [
            ("not_a_dict", "eligibility_matrix must be a dict or EligibilityMatrix"),
            (123, "eligibility_matrix must be a dict or EligibilityMatrix"),
            ([], "eligibility_matrix must be a dict or EligibilityMatrix"),
        ],
    )
    def test_parse_eligibility_matrix_invalid_type(self, invalid_input, expected_error):
        """Test validation error for invalid input types."""
        request_data = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": invalid_input,
        }

        with pytest.raises(ValidationError) as exc_info:
            StageEligibilityConfigRequest(**request_data)

        assert expected_error in str(exc_info.value)

    def test_parse_eligibility_matrix_invalid_practice_change_group(self):
        """Test validation error for invalid practice change group."""
        invalid_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {"practice_change_groups": ["Invalid Group Name"], "matrix": {}},
        }

        with pytest.raises(ValidationError) as exc_info:
            StageEligibilityConfigRequest(**invalid_json)

        assert "Invalid Group Name' is not a valid PracticeChangeGroup (type=value_error)" in str(exc_info.value)

    def test_parse_eligibility_matrix_invalid_practice_change_in_key(self):
        """Test validation error for invalid practice change in matrix key."""
        invalid_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {
                "practice_change_groups": ["Tillage practices"],
                "matrix": {"Invalid Practice,No Cover Crop": [["Reduced Till", "No Cover Crop"]]},
            },
        }

        with pytest.raises(ValidationError) as exc_info:
            StageEligibilityConfigRequest(**invalid_json)

        assert "Invalid Practice' is not a valid PracticeChange (type=value_error)" in str(exc_info.value)

    def test_parse_eligibility_matrix_invalid_practice_change_in_practices(self):
        """Test validation error for invalid practice change in practices."""
        invalid_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {
                "practice_change_groups": ["Tillage practices"],
                "matrix": {"Conventional Till,No Cover Crop": [["Invalid Practice", "No Cover Crop"]]},
            },
        }

        with pytest.raises(ValidationError) as exc_info:
            StageEligibilityConfigRequest(**invalid_json)

        assert "Invalid Practice' is not a valid PracticeChange (type=value_error)" in str(exc_info.value)

    def test_parse_eligibility_matrix_empty_groups_and_matrix(self):
        """Test parsing with empty practice_change_groups and matrix."""
        empty_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {"practice_change_groups": [], "matrix": {}},
        }

        request = StageEligibilityConfigRequest(**empty_json)

        assert request.eligibility_matrix is not None
        assert len(request.eligibility_matrix.practice_change_groups) == 0
        assert len(request.eligibility_matrix.matrix) == 0

    def test_parse_eligibility_matrix_complex_matrix(self):
        """Test parsing a more complex matrix with multiple practices."""
        complex_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {
                "practice_change_groups": ["Tillage practices", "Crop Practices", "Nutrient practices"],
                "matrix": {
                    "Conventional Till,No Cover Crop": [
                        ["Reduced Till", "No Cover Crop"],
                        ["Reduced Till", "Basic Cover Crops"],
                        ["No Till", "Premium Cover Crops"],
                    ],
                    "No Till,Basic Cover Crops,Fertilizer reduction": [
                        ["No Till", "Premium Cover Crops", "Nutrient Management"]
                    ],
                },
            },
        }

        request = StageEligibilityConfigRequest(**complex_json)

        assert request.eligibility_matrix is not None
        assert len(request.eligibility_matrix.practice_change_groups) == 3
        assert PracticeChangeGroup.tillage in request.eligibility_matrix.practice_change_groups
        assert PracticeChangeGroup.crop in request.eligibility_matrix.practice_change_groups
        assert PracticeChangeGroup.nutrient in request.eligibility_matrix.practice_change_groups

        # Check complex matrix key
        complex_key_found = False
        for key, practices in request.eligibility_matrix.matrix.items():
            if (
                PracticeChange.no_till in key
                and PracticeChange.basic_cover_crops in key
                and PracticeChange.fertilizer_reduction in key
            ):
                complex_key_found = True
                assert len(practices) == 1
                expected_practices = [
                    PracticeChange.no_till,
                    PracticeChange.nutrient_management,
                    PracticeChange.premium_cover_crops,
                ]
                assert practices[0] == expected_practices
                break
        assert complex_key_found, "Expected complex matrix key not found"

    def test_parse_eligibility_matrix_missing_fields(self):
        """Test parsing with missing practice_change_groups or matrix fields."""
        # Missing practice_change_groups
        missing_groups_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {"matrix": {"Conventional Till,No Cover Crop": [["Reduced Till", "No Cover Crop"]]}},
        }

        request = StageEligibilityConfigRequest(**missing_groups_json)
        assert len(request.eligibility_matrix.practice_change_groups) == 0

        # Missing matrix
        missing_matrix_json = {
            "stage_id": 1,
            "name": "Test Stage",
            "preserved_baseline": False,
            "eligibility_matrix": {"practice_change_groups": ["Tillage practices"]},
        }

        request = StageEligibilityConfigRequest(**missing_matrix_json)
        assert len(request.eligibility_matrix.matrix) == 0
