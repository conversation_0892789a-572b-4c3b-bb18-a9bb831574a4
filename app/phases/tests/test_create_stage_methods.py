from datetime import datetime

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, status

from defaults.attribute_options import (
    EVENT_BASED_WATER_AMOUNT_UNIT_DEFAULT_OPTIONS,
    NUTRIENT_EVENTS_STAGE_APPLICATION_METHOD_DEFAULT_OPTIONS,
)
from defaults.defaults import defaults_retriever
from logger import get_logger
from phases.create_stage_methods import (
    create_nutrient_events_stage,
    retrofit_ebdc_application_depth_unit_attributes,
)
from phases.db import get_enabled_attrs_by_stage_ids, get_phase_type_by_phase_id
from phases.enums import (
    attribute_type_to_label,
    AttributeTypes,
    PhaseTypes,
    StageIcons,
    StageTypes,
)
from phases.methods import create_attributes, create_stages
from phases.model import Attribute
from phases.schema import AttributeRequest, StageRequest
from programs.enums import ProgramTemplate

logger = get_logger(__name__)


def validate_nutrient_depth_unit_attribute(application_attributes: list[Attribute]):
    found_depth_unit = False
    for aa in application_attributes:
        logger.info(f"aa.name: {aa.name}")
        if aa.name == "Depth Unit":
            found_depth_unit = True
            assert aa.order == 9
        elif aa.name == "Water Amount":
            assert aa.order == 10
        elif aa.name == "Water Amount Unit":
            assert aa.order == 11
        elif aa.name == "Additives":
            assert aa.order == 12
    assert found_depth_unit


async def create_nutrient_events_stage_minus_application_depth_unit(
    request: Request, phase_id: int, custom_order: int = 3
) -> int:
    stage_request = StageRequest(
        phase_id=phase_id,
        name="Nutrients",
        description="",
        type_=StageTypes.NUTRIENT_EVENTS,
        icon=StageIcons.NUTRIENT_EVENTS.value,
        fmi_import_start_date=datetime(datetime.now().year - 5, 1, 1),
        fmi_import_end_date=datetime(datetime.now().year + 10, 1, 1),
        order=custom_order,
        locked=True,
        num_rows=1,
        required=True,
        enabled=True,
    )
    create_stage_response = await create_stages(
        request=request,
        instances=[stage_request],
    )
    stage = create_stage_response[0]
    if stage is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Parent stage not found"},
        )
    parent_stage_id = stage.id
    phase_type = await get_phase_type_by_phase_id(request, phase_id)
    default_application_product_options = await defaults_retriever.application_product_options_with_regrow_name(
        phase_type=phase_type
    )
    default_additive_options = await defaults_retriever.additive_options_with_regrow_name(phase_type=phase_type)
    additive_options = default_additive_options

    attributes = [
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.nutrient_management_enabled],
            type=AttributeTypes.nutrient_management_enabled,
            order=0,
            label="Nutrients applied?",
            locked=True,
            description="",
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_date],
            type=AttributeTypes.application_date,
            order=1,
            label="Application date",
            description="",
            locked=True,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_product],
            type=AttributeTypes.application_product,
            order=2,
            label="Product",
            options=default_application_product_options,
            locked=True,
            description="",
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_rate],
            type=AttributeTypes.application_rate,
            order=4,
            label="Rate",
            description="",
            locked=True,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_rate_type],
            type=AttributeTypes.application_rate_type,
            order=5,
            label="Rate type",
            description="",
            options=await defaults_retriever.get_options_for_attribute_type(
                request=request, attribute_type=AttributeTypes.application_rate_type
            ),
            locked=True,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_rate_unit],
            type=AttributeTypes.application_rate_unit,
            order=6,
            label="Rate unit",
            description="",
            locked=True,
            options=await defaults_retriever.get_options_for_attribute_type(
                request=request, attribute_type=AttributeTypes.application_rate_unit
            ),
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_method],
            type=AttributeTypes.application_method,
            order=7,
            label="Application method",
            description="",
            options=NUTRIENT_EVENTS_STAGE_APPLICATION_METHOD_DEFAULT_OPTIONS,
            locked=True,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.application_depth],
            type=AttributeTypes.application_depth,
            order=8,
            label="Application depth",
            description="",
            locked=True,
            min_val=0,
            max_val=50.8,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.water_amount],
            type=AttributeTypes.water_amount,
            order=9,
            label="Water amount",
            description="",
            locked=True,
            min_val=0,
            max_val=999_999.99,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.water_amount_unit],
            type=AttributeTypes.water_amount_unit,
            order=10,
            label="Water amount unit",
            description="",
            options=EVENT_BASED_WATER_AMOUNT_UNIT_DEFAULT_OPTIONS,
            locked=True,
            enabled=True,
        ),
        AttributeRequest(
            parent_stage_id=parent_stage_id,
            name=attribute_type_to_label[AttributeTypes.additives],
            type=AttributeTypes.additives,
            order=11,
            label="Additive(s)",
            description="",
            options=additive_options,
            locked=True,
            enabled=True,
        ),
    ]
    await create_attributes(
        request=request,
        instances=attributes,
    )
    return parent_stage_id


async def test_create_nutrient_events_stage(mdl, app_request):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage_id = await create_nutrient_events_stage(app_request, e_phase.id)
    attributes = await get_enabled_attrs_by_stage_ids(app_request, [stage_id])
    validate_nutrient_depth_unit_attribute(attributes)


async def test_retrofit_ebdc_application_depth_unit_attributes(mdl, app_request):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage_id = await create_nutrient_events_stage_minus_application_depth_unit(app_request, e_phase.id)
    await retrofit_ebdc_application_depth_unit_attributes(app_request)
    attributes = await get_enabled_attrs_by_stage_ids(app_request, [stage_id])
    validate_nutrient_depth_unit_attribute(attributes)
