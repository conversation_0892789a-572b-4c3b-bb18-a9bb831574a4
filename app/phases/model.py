import sqlalchemy as sa
from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON>, <PERSON>te<PERSON>, JSO<PERSON>, String, Text, Unicode
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.sql.schema import <PERSON><PERSON>ey, UniqueConstraint
from sqlalchemy.sql.sqltypes import Boolean, TIMESTAMP

from config import get_settings
from core import model as core_model
from db.mysql import Base

# This is required for the association proxy to work in stages. Will remove this once
# the association proxy is removed for enrolment.
from enrolments.model import Enrolments  # noqa: F401
from helper.mixin import IdMixin, IdTimestampMixin, TimestampMixin
from phases import enums
from projects.practice.cover_crop_generator.enums import CoverCropGeneratorType
from projects.practice.enums import PracticeTypes
from projects.practice.irrigation_generator.enums import IrrigationGeneratorType
from projects.practice.tillage_generator.enums import TillageGeneratorType
from values.enums import EntityTypeChoices

settings = get_settings()


class PhasesWithoutRelationships(Base):
    __tablename__ = "mrv_phases"
    __table_args__ = {
        "comment": (
            "An MRV program can have one or more phases. Each phase has a type, e.g. "
            "enrollment, monitoring, verification. It also has duration defined by a start "
            "and end date. We also have the 'DASHBOARDS' phase type, which isn't a true "
            "phase (presumably we're using it to make the UI easier to implement?). A Phase "
            "is made up of one or more Stages"
        )
    }

    id = Column(Integer, nullable=False, primary_key=True, index=True)
    start_date = Column(TIMESTAMP)
    end_date = Column(TIMESTAMP)
    program_id = Column(ForeignKey("mrv_programs.id"), nullable=False)
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    modified_at = Column(TIMESTAMP, nullable=True, default=None)
    enabled = sa.Column(sa.Boolean, nullable=False, default=False)
    deleted_at = Column(TIMESTAMP, nullable=True, default=None, index=True)
    show_contract = sa.Column(sa.Boolean, nullable=False, default=False)
    contract_type = sa.Column(sa.Enum(enums.ContractType), nullable=True)
    no_contract_message = sa.Column(sa.Text(), nullable=True)
    name = sa.Column(sa.String(50), nullable=True)
    type_ = sa.Column(sa.Enum(enums.PhaseTypes), nullable=False)
    deleted_at_unix = sa.Column(sa.Integer, nullable=True, default=None)
    params = sa.Column(sa.JSON, nullable=False, default="{}")


class Phases(PhasesWithoutRelationships):
    stages = relationship(
        "Stage",
        primaryjoin="and_(Stage.phase_id == Phases.id,Stage.deleted_at.is_(None))",
        lazy="selectin",
        viewonly=True,
    )
    stages_ = relationship(
        "Stage", primaryjoin="and_(Stage.phase_id == Phases.id, Stage.deleted_at.is_(None))", lazy="select"
    )
    contract_template = relationship(
        "ContractTemplate",
        backref="mrv_phases",
        lazy="selectin",
        viewonly=True,
        uselist=False,
        primaryjoin="and_(ContractTemplate.phase_id == Phases.id,ContractTemplate.deleted_at.is_(None))",
    )
    phase_commercials = relationship(
        "PhaseCommercials",
        primaryjoin="and_(PhaseCommercials.phase_id == Phases.id, PhaseCommercials.deleted_at.is_(None))",
        lazy="select",
    )
    phase_practice_configs = relationship(
        "PhasePracticeConfig",
        primaryjoin="and_(PhasePracticeConfig.phase_id == Phases.id, PhasePracticeConfig.deleted_at.is_(None))",
        lazy="select",
    )

    @property
    def contract(self) -> bool:
        return bool(self.contract_template)


class Stage(Base):
    __tablename__ = "mrv_stages"
    __table_args__ = {
        "comment": (
            "Every Phase is usually made up of multiple Stages (except for DASHBOARDS phase type). "
            "Examples of stages might be 'Nutrient Management',  'Tillage History', 'Field Boundaries' etc. "
            "Stages have an order, and each stage might contain a number of Attributes (some stage "
            "types, such as 'Field Boundaries', won't have any Attributes). Every Stage has an "
            "entity_type, e.g. 'field', 'farm' or 'mob'."
        )
    }
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    phase_id = Column(Integer, ForeignKey("mrv_phases.id"), nullable=True)
    name = Column(String(50), nullable=False)
    custom_name = Column(String(50), nullable=True)
    fmi_import_start_date = Column(TIMESTAMP)
    fmi_import_end_date = Column(TIMESTAMP)
    year_start = Column(sa.Integer, default=2022)
    year_end = Column(sa.Integer, default=2022)
    order = Column(
        Integer, nullable=False
    )  # need help on how to implement this at the db level. can we do this with the ID instead?
    description = Column(Unicode(200), nullable=True)
    num_rows = Column(INTEGER, default=1, nullable=False)
    attributes = relationship(
        "Attribute",
        primaryjoin="and_(Attribute.parent_stage_id == Stage.id, Attribute.deleted_at.is_(None))",
        lazy="subquery",
        viewonly=True,
        back_populates="_stage",
    )
    attributes_ = relationship(
        "Attribute",
        primaryjoin="and_(Attribute.parent_stage_id == Stage.id, Attribute.deleted_at.is_(None))",
        lazy="select",
        back_populates="_stage",
    )
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)
    icon = Column(sa.String(255), nullable=False, default="/static/stages/survey.svg")
    enabled = Column(Boolean, nullable=False, default=True)
    messages = Column(JSON, nullable=True)
    config_description = Column(sa.TEXT)
    type_ = Column(sa.Enum(enums.StageTypes))
    eligibility_method = Column(sa.Enum(enums.EligibilityTypes))
    locked = Column(sa.Boolean, nullable=False)
    survey_id = Column(sa.String(255), nullable=True)
    entity_type = Column(sa.Enum(EntityTypeChoices), nullable=False, default=EntityTypeChoices.field)
    required = Column(
        sa.Boolean,
        nullable=False,
        default=True,
        server_default="1",
        comment="Do all values need to be filled in order for the ui to unlock and allow passage to the next stage?",
    )
    interventions_description = Column(
        sa.String(255),
        nullable=False,
        default=enums.DEFAULT_INTERVENTION_DESCRIPTION,
        server_default=enums.DEFAULT_INTERVENTION_DESCRIPTION,
    )
    timeline_has_crop_events = Column(sa.Boolean, default=True, server_default="1", nullable=False)
    timeline_has_tillage_events = Column(sa.Boolean, default=True, server_default="1", nullable=False)
    timeline_has_nutrient_events = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    timeline_has_irrigation_events = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    timeline_start_date = Column(TIMESTAMP, nullable=True)
    timeline_end_date = Column(TIMESTAMP, nullable=True)
    timeline_enabled = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    timeline_time_scale = Column(
        Enum(enums.TimelineTimeScales),
        nullable=False,
        default=enums.TimelineTimeScales.biannual,
        server_default=enums.TimelineTimeScales.biannual,
    )
    optis_prefill = Column(
        sa.Boolean,
        default=False,
        server_default="0",
        nullable=False,
        comment="Ensures that the stage is pre-filled with data from Optis",
    )
    optis_year_start = Column(
        sa.Integer,
        nullable=True,
        comment="Start year of optis call for prefilling data. Year is inclusive.",
    )
    optis_year_end = Column(
        sa.Integer,
        nullable=True,
        comment="End year of optis call for prefilling data. Year is inclusive.",
    )
    allow_enrol_without_assigned_practice = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    csv_import_enabled = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    enabled_metrics = Column(JSON, nullable=True)
    monitor_prefill_enabled = Column(sa.Boolean, default=False, server_default="0", nullable=False)
    default_no_practice_observation = Column(sa.Boolean, default=False, server_default="0", nullable=False)


class StageWithPhase(Stage):
    """Stage model with attached Phase data. Is used when Phase object is needed."""

    phase = relationship(
        "Phases",
        primaryjoin="and_(Phases.id == Stage.phase_id, Phases.deleted_at.is_(None))",
        lazy="subquery",
        uselist=False,
        overlaps="stages_",
    )

    @property
    def phase_type(self) -> str | None:
        if self.phase_id:
            phase_type_ = self.phase.type_
            return phase_type_.upper() if phase_type_ else None
        else:
            return None


class Attribute(Base):
    __tablename__ = "mrv_attributes"
    __table_args__ = {
        "comment": (
            "Some stages will expect tabular data, in which case they will contain one Attribute "
            "for each type of column of data they expect. Attributes have an order (i.e. which "
            "column an attribute will appear in) and a type (e.g. 'crop_type', 'water_amount', "
            "'farm_name'). The actual table data entered by a farmer for a stage will be stored "
            "in db table 'mrv_values', so an Attribute tells how what type of data is stored "
            "in any given mrv_values row."
        )
    }

    id = Column(Integer, nullable=False, primary_key=True, index=True)
    parent_stage_id = Column(Integer, ForeignKey("mrv_stages.id"))
    name = Column(String(50), nullable=False)
    custom_name = Column(String(50), nullable=True)
    type = Column(Enum(enums.AttributeTypes), nullable=False)
    order = Column(Integer, nullable=False)
    locked = Column(Boolean, default=True)
    description = Column(Text)
    options = Column(JSON, nullable=False, default="[]")
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)
    dependencies = Column(JSON, nullable=True, default="[]")
    default_value = Column(String(50), nullable=True)

    _stage = relationship(
        "Stage",
        primaryjoin="and_(Stage.id == Attribute.parent_stage_id, Stage.deleted_at.is_(None))",
        lazy="subquery",
        uselist=False,
        back_populates="attributes",
    )
    start_date = association_proxy("_stage", "fmi_import_start_date")
    end_date = association_proxy("_stage", "fmi_import_end_date")

    phase_id = association_proxy("_stage", "phase_id")

    messages = Column(JSON, nullable=True)
    enabled = Column(Boolean, nullable=False, default=False)
    visible = Column(Boolean, nullable=False, default=True)
    min_val = Column(String(40), nullable=True)
    max_val = Column(String(40), nullable=True)
    autofill = Column(
        sa.Boolean,
        nullable=False,
        default=False,
        server_default="0",
        comment="Auto fills the values in Attribute for the entity",
    )
    crop_type_selections = relationship(
        "CropTypeSelection",
        primaryjoin="and_(CropTypeSelection.attribute_id == Attribute.id, CropTypeSelection.deleted_at.is_(None))",
        lazy="select",
    )


class AttributeOptionsDefaults(Base):
    __tablename__ = "mrv_attribute_options_defaults"
    __table_args__ = {
        "comment": (
            "The `options` in mrv_attributes are per-program, and the existance in that list determines if enabled in that program. This table serves as an authoritative source for _all_ of those options, not just the ones that are enabled"
        )
    }
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    type = Column(Enum(enums.AttributeTypes), nullable=False)
    options = Column(JSON, nullable=False, default="[]")


class PhaseCommercials(Base):
    __tablename__ = "mrv_phase_commercials"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    phase_id = Column(Integer, ForeignKey("mrv_phases.id"), nullable=False)
    start_date = Column(sa.DateTime, nullable=False)
    end_date = Column(sa.DateTime, nullable=False)
    # payment is $. It's good practice to use scale=4 for storing currencies.
    payment = Column(sa.DECIMAL(precision=11, scale=4, asdecimal=True), nullable=False)
    payment_for = Column(Enum(enums.CommercialsPaymentForType), nullable=False)
    deleted_at = sa.Column(sa.TIMESTAMP, nullable=True, index=True)


class CommercialPractices(Base):
    __tablename__ = "mrv_commercial_practices"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # the ID of the associated PhaseCommercial
    commercial_id = Column(Integer, nullable=False)
    practice_change = Column(String(255), nullable=False)
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)


class CommercialRegions(Base):
    __tablename__ = "mrv_commercial_regions"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # the ID of the associated Phase
    phase_id = Column(Integer, nullable=False)
    # the tier of the associated CoreRegion
    region_tier = Column(sa.Enum(enums.RegionTier), nullable=False)
    # the ID of the associated CoreRegion
    region_id = Column(Integer, nullable=False)
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)


class CommercialRegionTiers(Base):
    __tablename__ = "mrv_commercial_region_tiers"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # the ID of the associated PhaseCommercial
    commercial_id = Column(Integer, nullable=False)
    # the RegionTier the PhaseCommercial is associated to
    region_tier = Column(sa.Enum(enums.RegionTier), nullable=False)
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)


class CropTypeSelection(Base):
    __tablename__ = "mrv_crop_type_selection"
    __table_args__ = (
        UniqueConstraint(
            "crop_type",
            "attribute_id",
            name="crop_type_attribute_id_idx",
        ),
    )

    id = Column(Integer, nullable=False, primary_key=True, index=True)
    crop_type = (
        Column(
            INTEGER(10, unsigned=True),
            ForeignKey(core_model.Crops.id),
            nullable=False,
            index=True,
        )
        if settings.env != "local"
        else Column(Integer, nullable=False, index=True)
    )
    attribute_id = Column(Integer, ForeignKey("mrv_attributes.id"), nullable=True, index=True)
    phase_id = Column(Integer, ForeignKey("mrv_phases.id"), nullable=True, index=True)
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)


class CommonEligibilityConfig:
    local_common_practice_source = Column(sa.Enum(enums.LocalCommonPracticeSources), nullable=True)
    practice_comparison_years = Column(sa.Enum(enums.PracticeComparisonYearsChoice), nullable=False)
    historical_years_to_check = Column(sa.Integer, nullable=False, default=0)
    commodity_crops_check = Column(sa.Enum(enums.CommodityCropCheckChoice), nullable=True)
    default_crop_match = Column(sa.String(length=255), nullable=True)
    enabled = Column(sa.Boolean, nullable=False, default=True, server_default="1")
    eligible_intended_commodity_crops = Column(sa.String(length=255), nullable=True)


class StageEligibilityConfig(TimestampMixin, Base):
    __tablename__ = "mrv_stage_eligibility_config"
    id = Column(Integer, nullable=False, primary_key=True, index=True, autoincrement=True)
    stage_id = Column(
        Integer,
        ForeignKey("mrv_stages.id"),
        nullable=False,
        primary_key=True,
    )
    name = Column(String(255), nullable=False)
    tillage_config = Column(Integer, ForeignKey("mrv_tillage_eligibility_config.id"), nullable=True)
    cover_crop_config = Column(Integer, ForeignKey("mrv_cover_crop_eligibility_config.id"), nullable=True)
    nm_rate_reduction_config = Column(
        Integer,
        ForeignKey("mrv_nm_rate_reduction_eligibility_config.id"),
        nullable=True,
    )
    nm_split_application_config = Column(
        Integer,
        ForeignKey("mrv_nm_split_application_eligibility_config.id"),
        nullable=True,
    )
    nm_timing_config = Column(Integer, ForeignKey("mrv_nm_timing_eligibility_config.id"), nullable=True)
    rice_irrigation_config = Column(Integer, ForeignKey("mrv_rice_irrigation_eligibility_config.id"), nullable=True)
    grazing_interventions_config = Column(
        Integer,
        ForeignKey("mrv_grazing_interventions_eligibility_config.id"),
        nullable=True,
    )
    nm_config = Column(Integer, ForeignKey("mrv_nm_eligibility_config.id"), nullable=True)
    preserved_baseline = Column(sa.Boolean, nullable=False, default=False, server_default="0")
    """
        Example Matrix
        {
            "practice_change_groups": [
                "Tillage practices",
                "Crop Practices"
            ],
            "validation_message": "A cropping and tillage practice must be selected for each field. Please choose a practice in both categories or remove the field from enrollment.",
            "matrix": {
                "Conventional Till,No Cover Crop": [
                    ["Reduced Till", "No Cover Crop"],
                    ["Reduced Till", "Basic Cover Crops"],
                    ["Reduced Till", "Premium Cover Crops"],
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "Conventional Till,Basic Cover Crops": [
                    ["Reduced Till", "No Cover Crop"],
                    ["Reduced Till", "Basic Cover Crops"],
                    ["Reduced Till", "Premium Cover Crops"],
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "Conventional Till,Premium Cover Crops": [
                    ["Reduced Till", "No Cover Crop"],
                    ["Reduced Till", "Basic Cover Crops"],
                    ["Reduced Till", "Premium Cover Crops"],
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "Reduced Till,No Cover Crop": [
                    ["Reduced Till", "Basic Cover Crops"],
                    ["Reduced Till", "Premium Cover Crops"],
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "Reduced Till,Basic Cover Crops": [
                    ["Reduced Till", "Premium Cover Crops"],
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "Reduced Till,Premium Cover Crops": [
                    ["No Till", "No Cover Crop"],
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "No Till,No Cover Crop": [
                    ["No Till", "Basic Cover Crops"],
                    ["No Till", "Premium Cover Crops"]
                ],
                "No Till,Basic Cover Crops": [
                    ["No Till", "Premium Cover Crops"]
                ]
            }
        }
    """
    eligibility_matrix = Column(
        sa.JSON,
        nullable=True,
        comment="JSON representation of the EligibilityMatrix. If not set, follows our existing Must improve practice logic",
    )


class TillageEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_tillage_eligibility_config"
    version = Column(sa.Enum(TillageGeneratorType), nullable=False, default=TillageGeneratorType.TILLAGE_GENERATOR_V2)
    description = Column(sa.Text, nullable=True)


class CoverCropEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_cover_crop_eligibility_config"
    version = Column(
        sa.Enum(CoverCropGeneratorType), nullable=False, default=CoverCropGeneratorType.COVER_CROP_GENERATOR_V2
    )
    description = Column(sa.Text, nullable=True)


class NmRateReductionEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_nm_rate_reduction_eligibility_config"


class NmSplitApplicationEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_nm_split_application_eligibility_config"


class NmTimingEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_nm_timing_eligibility_config"


class RiceIrrigationEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_rice_irrigation_eligibility_config"
    version = Column(
        sa.Enum(IrrigationGeneratorType), nullable=False, default=IrrigationGeneratorType.IRRIGATION_GENERATOR_V2
    )
    description = Column(sa.Text, nullable=True)


class GrazingInterventionsEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_grazing_interventions_eligibility_config"


class NMEligibilityConfig(CommonEligibilityConfig, IdMixin, Base):
    __tablename__ = "mrv_nm_eligibility_config"


class PhasePracticeConfig(IdTimestampMixin, Base):
    __tablename__ = "mrv_phase_practice_config"
    __table_args__ = (
        UniqueConstraint(
            "phase_id",
            "practice_type",
            "event_type",
            name="phase_id_practice_type_event_type_idx",
        ),
    )
    phase_id = Column(Integer, ForeignKey("mrv_phases.id"), nullable=False)
    practice_type = Column(sa.Enum(PracticeTypes), nullable=False)
    event_type = Column(sa.Enum(enums.EventTypes), nullable=False)
    historical_years = Column(sa.Integer, nullable=False, default=1)
    config = Column(sa.JSON, nullable=True, comment="Configuration for the practice, such as cover crop mapping rules")


class PracticeStageAttributeConfig(IdTimestampMixin, Base):
    __tablename__ = "mrv_practice_stage_attribute_config"
    __table_args__ = (
        UniqueConstraint(
            "phase_practice_config_id",
            "stage_id",
            "attribute_id",
            name="phase_practice_config_id_stage_id_attribute_id_idx",
        ),
    )
    phase_practice_config_id = Column(Integer, ForeignKey("mrv_phase_practice_config.id"), nullable=False)
    stage_id = Column(Integer, ForeignKey("mrv_stages.id"), nullable=False)
    attribute_id = Column(Integer, ForeignKey("mrv_attributes.id"), nullable=False)
    enabled = Column(Boolean, nullable=False, default=True)


class PracticeChangeRules(IdTimestampMixin, Base):
    __tablename__ = "mrv_practice_change_rules"
    stage_id = Column(Integer, ForeignKey("mrv_stages.id"), nullable=False)
    # rule_type = Column(sa.Enum(PracticeRuleTypes), nullable=False)
    rule = Column(sa.JSON, nullable=False)
