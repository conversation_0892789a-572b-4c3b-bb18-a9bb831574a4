from pydantic import BaseModel

from permissions.enums import Permission, RestrictionTypes, RoleTypes


class RequestRole(BaseModel):
    name: str
    program_id: int | None
    role_type: RoleTypes
    restricted: bool = False
    group_management: bool = False

    class Config:
        orm_mode = True


class ResponseRole(RequestRole):
    id: int


class RoleAttributes(BaseModel):
    permissions: set[Permission]
    role_type: RoleTypes
    restricted: bool = False


# should remove ResponseRoleWithRoleName and just use ResponseRole
# once after we switched to use id instead of name in both FE and BE
class ResponseRoleWithRoleName(ResponseRole):
    role: str | None
    role_name: str


class RequestRolePermission(BaseModel):
    role_id: int
    permission: Permission

    class Config:
        orm_mode = True


class ResponseRolePermission(RequestRolePermission):
    id: int


class RequestRoleUser(BaseModel):
    fs_user_id: int
    role_id: int

    class Config:
        orm_mode = True


class ResponseRoleUser(RequestRoleUser):
    id: int


class Role(BaseModel):
    id: int
    name: str
    program_id: int | None
    restricted: bool
    role_type: RoleTypes
    group_management: bool


class RoleUserWithRole(BaseModel):
    id: int
    fs_user_id: int
    role_id: int
    program_id: int | None
    restriction_ids: list[int]
    role: Role


class AdminRoleUser(RoleUserWithRole):
    program_permission_id: int | None = None
    unrestricted_group_access: bool = False
    group_ids: list[int] = []


class RoleUserRestrictionResponse(BaseModel):
    id: int
    user_id: int | None
    role_user_id: int | None
    restriction_source_id: int | None
    type: RestrictionTypes
    values: list[str]


class RoleUserRestrictionRequest(BaseModel):
    user_id: int | None
    restriction_source_id: int | None
    type: RestrictionTypes
    values: list[str]


class RoleUserRestrictionUpdateRequest(BaseModel):
    user_id: int | None
    restriction_source_id: int | None
    type: RestrictionTypes | None
    values: list[str] | None
