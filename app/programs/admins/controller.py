import enum

from fastapi import Request

from base_controller import BaseController
from core.methods import get_core_user_detail
from core.schema import UserDetailResponse
from helper.helper import UserInfoRetriever
from permissions.enums import DefaultRoles
from programs import db, schema as program_schema


class ProgramAdminsController(BaseController):
    def __init__(self, request: Request | None = None):
        super().__init__(request=request)

    @staticmethod
    def tags() -> list[str]:
        return ["program_admins"]

    @staticmethod
    def prefix() -> str | None:
        return "/programs/{program_id}/admins"

    async def search_program_admins(
        self, program_id: int, query_string: str
    ) -> list[program_schema.ProgramPermissionExtra]:
        """
        List all the admins for a program, filtered by search query
        """
        rows = await db.get_all_admins(request=self.request, program_ids=[program_id], search_string=query_string)
        result = [
            program_schema.ProgramPermissionExtra(
                id=row.program_permission_id,
                user_id=row.fs_user_id,
                program_id=row.program_id,
                role_user_id=row.id,
                role_id=row.role_id,
                role_type=row.role.role_type,
                role=(DefaultRoles(row.role.name).name if row.role.name in DefaultRoles.values() else None),
                role_name=row.role.name,
                restriction_ids=row.restriction_ids,
                unrestricted_group_access=row.unrestricted_group_access,
                group_ids=row.group_ids,
            )
            for row in rows
        ]
        user_info_retriever = UserInfoRetriever(result)
        await user_info_retriever.load_user_info()

        # fill in user details
        for permission in result:
            permission.user_info = user_info_retriever.get_user_info(permission.user_id)
        return result

    async def do_read(self, request: Request, program_id: int, user_id: int) -> UserDetailResponse | None:
        is_admin = await db.is_program_admin(request=request, program_id=program_id, user_id=user_id)
        if not is_admin:
            return None
        return await get_core_user_detail(user_id=user_id)

    class Routes(enum.StrEnum):
        root = ""
        get = "/{user_id}"
        post = "/{user_id}"
        search = "/search"
        read = "/{user_id}"
        patch = "/{user_id}"
        delete = "/{user_id}"
        create = "/create"
