from fastapi import APIRouter, Depends, HTTPException, Request, status

from core.methods import patch_core_user_detail
from core.schema import UserDetailResponse
from db.fastapi_dependencies import transacted
from helper.async_tools import Tasks
from permissions.enums import Permission
from permissions.methods import get_accessible_admin_users, get_original_user_id
from permissions.resolver import Permissions
from programs import db, schema as program_schema
from programs.admins.controller import ProgramAdminsController as TheController
from programs.admins.schema import PatchAdminDetailRequest
from programs.router import update_user_program_role
from programs.schema import RoleUpdateRequest
from user_groups.admin.router import update_groups_for_admin
from user_groups.admin.utils import check_group_admin_editing_allowed
from user_groups.schema import UpdateGroupsAdminsRequest

router = APIRouter(prefix=TheController.prefix(), tags=TheController.tags())


def raise_404(
    msg: str = "user not found",
    user_id: int | None = None,
) -> None:
    if user_id is not None:
        msg += f" for user_id: {user_id}"

    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": msg})


@router.get(
    TheController.Routes.search,  # /programs/{program_id}/admins/search
    response_model=list[program_schema.ProgramPermissionExtra],
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_GROUPS_FOR_ADMIN, Permission.GET_PROGRAM_USERS]))],
)
async def do_search(
    request: Request, program_id: int, query_string: str | None = None
) -> list[program_schema.ProgramPermissionExtra]:
    controller = TheController(request=request)
    query_string = query_string.strip() if query_string else ""
    user_id = get_original_user_id(request=request)

    tasks = Tasks()
    tasks.add(controller.search_program_admins(program_id=program_id, query_string=query_string))
    tasks.add(get_accessible_admin_users(request=request, program_id=program_id, user_id=user_id))
    search_result, accessible_admin_users = await tasks.complete_all()

    # only return groups that the user has access to, and unrestricted access admins
    return [
        admin_user
        for admin_user in search_result
        if admin_user.user_id in accessible_admin_users or admin_user.unrestricted_group_access
    ]


@router.get(
    TheController.Routes.get,  # /programs/{program_id}/admins/{user_id}
    response_model=UserDetailResponse,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.PATCH_PROGRAM, Permission.GET_PROGRAM_USERS]))],
)
async def do_get(request: Request, program_id: int, user_id: int) -> UserDetailResponse:
    controller = TheController(request=request)
    result = await controller.do_read(request=request, program_id=program_id, user_id=user_id)
    if result is None:
        raise_404(user_id=user_id)
    return result


@router.patch(
    TheController.Routes.patch,  # /programs/{program_id}/admins/{user_id}
    response_model=None,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(transacted), Depends(Permissions([Permission.UPDATE_PROGRAM_USER]))],
)
async def do_patch(request: Request, program_id: int, user_id: int, payload: PatchAdminDetailRequest) -> None:
    is_admin = await db.is_program_admin(request=request, program_id=program_id, user_id=user_id)
    if not is_admin:
        raise_404(user_id=user_id)
    await check_group_admin_editing_allowed(request=request, program_id=program_id, target_user_ids=[user_id])

    # update groups if groups are provided
    if payload.group_info_exist():
        await update_groups_for_admin(
            request=request,
            program_id=program_id,
            user_id=user_id,
            payload=UpdateGroupsAdminsRequest(groups=payload.groups, unrestricted_access=payload.unrestricted_access),
        )

    # update role if role_id is provided
    if payload.role_info_exist():
        await update_user_program_role(
            request=request,
            program_id=program_id,
            user_id=user_id,
            role_update=RoleUpdateRequest(role_id=payload.role_id),
        )

    await patch_core_user_detail(user_id=user_id, payload=payload)
