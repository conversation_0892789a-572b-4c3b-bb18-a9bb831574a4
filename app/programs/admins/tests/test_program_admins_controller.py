from unittest.mock import patch

from core.schema import User
from permissions.enums import DefaultRoles, RoleTypes
from programs.admins.controller import ProgramAdminsController
from programs.schema import ProgramPermissionExtra


async def test_search_program_admins(mdl, app_request, faker):
    user_id = faker.random_number(3)
    controller = ProgramAdminsController(request=app_request)

    program = await mdl.Programs(group_management=True)
    permission = await mdl.ProgramPermissions(program_id=program.id, user_id=user_id)
    role = await mdl.Roles(name=DefaultRoles.GROUP_ADMIN, role_type=RoleTypes.ADMIN, group_management=True)
    role_user = await mdl.RolesUsers(role_id=role.id, fs_user_id=user_id, program_id=program.id)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")
    await mdl.UserGroupsAdmins(group_id=group1.id, program_id=program.id, user_id=user_id)
    await mdl.UserGroupsAdmins(group_id=group2.id, program_id=program.id, user_id=user_id)
    await mdl.UserGroupsAdmins(group_id=group3.id, program_id=program.id, user_id=user_id)

    mock_user = User(_user_id=user_id, first_name="Test", last_name="User", email="<EMAIL>")
    with patch("core.methods.get_user_info_batch", return_value=[mock_user]):
        result = await controller.search_program_admins(program_id=program.id, query_string=None)

    assert len(result) == 1
    assert (
        result[0].to_dict()
        == ProgramPermissionExtra(
            id=permission.id,
            user_id=user_id,
            program_id=program.id,
            details=None,
            deleted_at=None,
            user_info=mock_user,
            role_id=role.id,
            role_user_id=role_user.id,
            role_type=RoleTypes.ADMIN,
            role=DefaultRoles.GROUP_ADMIN.name,
            role_name=DefaultRoles.GROUP_ADMIN,
            restriction_ids=[],
            unrestricted_group_access=False,
            group_ids=sorted([group1.id, group2.id, group3.id]),
        ).to_dict()
    )
