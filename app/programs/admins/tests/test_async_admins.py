from config import get_settings
from permissions.enums import DefaultRoles, RoleTypes
from programs.admins.schema import PatchAdminDetailRequest

settings = get_settings()


async def test_update_admin_detail(async_client, mdl, faker, mocker, db_session_maker, grant_all_permissions_factory):
    await grant_all_permissions_factory()
    program = await mdl.Programs(group_management=True)
    role1 = await mdl.Roles(
        name=DefaultRoles.PRODUCER_ADMIN_READ_ONLY_ACCESS, role_type=RoleTypes.ADMIN, group_management=True
    )
    role2 = await mdl.Roles(
        name=DefaultRoles.PRODUCER_ADMIN_EDIT_ACCESS, role_type=RoleTypes.ADMIN, group_management=True
    )
    user_id = faker.random_number(3)
    await mdl.ProgramPermissions(program_id=program.id, user_id=user_id)
    await mdl.RolesUsers(role_id=role1.id, fs_user_id=user_id, program_id=program.id)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3", display_name="group3")
    new_name = faker.name()

    mock_core_method = mocker.patch("programs.admins.router.patch_core_user_detail", return_value=None)
    url = f"/programs/{program.id}/admins/{user_id}"
    response = await async_client.patch(
        url,
        json={"first_name": new_name},
    )
    assert response.status_code == 200
    mock_core_method.assert_called_with(
        user_id=user_id,
        payload=PatchAdminDetailRequest(first_name=new_name),
    )

    # update role succeeded
    response = await async_client.patch(
        url,
        json={"role_id": role2.id},
    )
    assert response.status_code == 200
    response = await async_client.get(f"/users/{user_id}")
    assert list(response.json()["program_roles"].values())[0]["role_info"]["id"] == role2.id

    # update groups succeeded
    response = await async_client.patch(
        url,
        json={"groups": [group1.id, group2.id], "unrestricted_access": False},
    )
    assert response.status_code == 200
    response = await async_client.get(f"/programs/{program.id}/admin_groups/{user_id}")
    assert {group["id"] for group in response.json()["groups"]} == {group1.id, group2.id}
    assert response.json()["unrestricted_access"] is False

    response = await async_client.patch(
        url,
        json={"groups": [], "unrestricted_access": True},
    )
    assert response.status_code == 200
    response = await async_client.get(f"/programs/{program.id}/admin_groups/{user_id}")
    assert response.json()["unrestricted_access"] is True
    assert {group["id"] for group in response.json()["groups"]} == {group1.id, group2.id, group3.id}
