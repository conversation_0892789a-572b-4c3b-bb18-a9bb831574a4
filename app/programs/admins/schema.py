from core.schema import PatchUserDetailRequest


class PatchAdminDetailRequest(PatchUserDetailRequest):
    """
    Request model for updating admin details. Including user details, role and groups.
    """

    role_id: int | None = None
    unrestricted_access: bool | None = None
    groups: list[int] | None = None

    def group_info_exist(self) -> bool:
        return self.groups is not None and self.unrestricted_access is not None

    def role_info_exist(self) -> bool:
        return self.role_id is not None
