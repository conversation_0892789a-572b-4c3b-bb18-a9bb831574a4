import asyncio

from fastapi import APIRouter, Depends, Request

from config import get_settings
from notifications.enums import NotificationCodes, NotificationSources
from notifications.methods import get_notifications, update_notification_body
from notifications.schema import NotificationBody, ResponseNotification
from permissions.enums import Permission
from permissions.resolver import Permissions
from programs.export import paths
from programs.export.db import (
    get_all_in_process_export_run_after_t_minutes,
    update_export_run,
)
from programs.export.schema import ProgramExportRunResponse
from slack_integration.integration import post_message

settings = get_settings()

router = APIRouter()


@router.get(paths.update_stuck_export, dependencies=[Depends(Permissions([Permission.CREATE_SUPER_ADMIN]))])
async def update_stuck_export(request: Request) -> None:
    """
    This is expected to be called every 5 minute, this is not a heavy api call.
    """
    stuck_exports: list[ProgramExportRunResponse]
    notifications: list[ResponseNotification]

    stuck_exports, notifications = await asyncio.gather(
        *[
            get_all_in_process_export_run_after_t_minutes(request=request, time_in_minutes=5),
            get_notifications(
                session=request.state.sql_session,
                settings=settings,
                use_number_sent_limit=False,
                source=NotificationSources.MRV_EXPORT,
            ),
        ]
    )

    program_id_user_id_notification_map: dict[int, dict[int, list[ResponseNotification]]] = {}
    for notification in notifications:
        program_id_user_id_notification_map.setdefault(notification.program_id, {}).setdefault(
            notification.user, []
        ).append(notification)

    export_stuck_ids = [export.id for export in stuck_exports]
    for export in stuck_exports:
        program_id = export.program_id
        user_id = export.user
        await asyncio.gather(
            *[
                update_export_run(
                    request=request,
                    export_run_id=export.id,
                    failure_reason="Export run is stuck for more than 5 minutes",
                ),
                update_notification_body(
                    session=request.state.sql_session,
                    notification_ids=[i.id for i in notifications],
                    body=NotificationBody(
                        url=f"{settings.MRV_INTERNAL_URL}{paths.base}/{program_id}{paths.get_export_file}",
                        method="GET",
                        message="",
                        code=NotificationCodes.ERROR,
                    ),
                    program_id=program_id,
                    user_id=user_id,
                ),
            ]
        )

    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_CRON_ALERTS,
        message=f"Export runs - {export_stuck_ids} stuck for more than 5 minutes",
        dict_={
            "error": "Export runs are stuck for more than 5 minutes, marked failed by cron",
            "environment": settings.env,
        },
    )
