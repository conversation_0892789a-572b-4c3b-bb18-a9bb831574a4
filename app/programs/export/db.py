from typing import Any

from fastapi import Request
from sqlalchemy import and_, case, func, or_, Row, Subquery
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from sqlalchemy.sql import text

from core import model as core_model
from fields.model import Fields
from helper.helper import run_query
from helper.i18n import Locale
from phases.enums import StageTypes
from phases.model import Attribute, Phases, Stage
from programs.export.enums import ExportRunStatus
from programs.export.model import ProgramExportRuns
from programs.export.schema import (
    CommonExportSchema,
    FieldExportOutput,
    ProducerExportOutput,
    ProgramExportRunCreate,
    ProgramExportRunResponse,
    ProgramExportRunUpdate,
)
from programs.model import BoundaryRuleDeviation, ProgramFieldOverlap, Programs
from projects.model import ProjectPermissions, Projects
from root_crud import create, get, update
from user_groups.model import UserGroupProjects, UserGroups
from values.model import Values


def get_project_groups_subquery(program_id: int) -> Subquery:
    return (
        select(
            UserGroupProjects.project_id.label("project_id"),
            func.group_concat(UserGroups.id.op("SEPARATOR")(", ")).label("group_ids"),
            func.group_concat(UserGroups.name.op("SEPARATOR")(", ")).label("group_names"),
        )
        .join(UserGroups, UserGroupProjects.group_id == UserGroups.id)
        .where(UserGroupProjects.deleted_at.is_(None))
        .where(UserGroups.deleted_at.is_(None))
        .where(UserGroups.program_id == program_id)
        .group_by(UserGroupProjects.project_id)
    ).subquery()


async def get_project_and_field_data_for_program(
    request: Request,
    program_id: int,
    include_deleted_projects: bool,
    include_deleted_fields: bool,
    include_reporting_disabled: bool,
) -> list[CommonExportSchema]:
    """
    Returns project, field and user information from MRV and core tables.
    """
    project_groups_subquery = get_project_groups_subquery(program_id=program_id)
    query = (
        select(
            core_model.Users.email.label("user_email"),
            core_model.Users.name.label("name"),
            core_model.Users.surname.label("surname"),
            Projects.id.label("project_id"),
            project_groups_subquery.c.group_ids.label("group_ids"),
            project_groups_subquery.c.group_names.label("group_names"),
            Fields.id.label("mrv_field_id"),
            Fields.md5.label("field_md5"),
            Fields.fs_field_id.label("fs_field_id"),
            Fields.area.label("field_area_ha"),
            Fields.status.label("field_status"),
            core_model.KMLGroups.name.label("field_name"),
            core_model.Groups.name.label("farm_name"),
            core_model.CoreRegions.name.label("state_name"),
            Fields.deleted_at.label("field_deleted_at"),
            Projects.deleted_at.label("project_deleted_at"),
            Projects.reporting_enabled.label("reporting_enabled"),
        )
        .select_from(Projects)
        .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
        .join(core_model.Users, ProjectPermissions.user == core_model.Users.id)
        .outerjoin(project_groups_subquery, Projects.id == project_groups_subquery.c.project_id)
        .join(Fields, Fields.parent_project_id == Projects.id)
        .join(core_model.KMLGroups, core_model.KMLGroups.id == Fields.fs_field_id)
        .join(core_model.KMLFiles, core_model.KMLFiles.id == core_model.KMLGroups.kml_id)
        .join(core_model.Groups, core_model.Groups.id == Fields.farm_id)
        .join(core_model.CoreRegions, core_model.CoreRegions.id == core_model.KMLFiles.region_id)
        .where(Projects.program_id == program_id)
    )
    if not include_deleted_projects:
        query = query.where(
            Projects.deleted_at.is_(None),
            ProjectPermissions.deleted_at.is_(None),
            core_model.Users.deleted_at.is_(None),
        )
    if not include_deleted_fields:
        query = query.where(Fields.deleted_at.is_(None))
    if not include_reporting_disabled:
        query = query.where(Projects.reporting_enabled.is_(True))

    async with request.state.sql_session() as s:
        records = await run_query(query=query, s=s)
        records = records.all()
    return records


async def get_program_data_per_stage(
    request: Request,
    program_id: int,
    include_deleted_projects: bool,
    include_deleted_fields: bool,
    include_reporting_disabled: bool,
    stage_type_to_exclude: list[StageTypes] | None = None,
    project_ids: list[int] | None = None,
) -> list:
    """
    Returns all the field values for a program in a flat structure.
    """
    query = (
        select(
            Values.field_id.label("mrv_field_id"),
            Stage.id.label("stage_id"),
            Stage.type_.label("stage_type"),
            Stage.order.label("stage_order"),
            Stage.name.label("stage_name"),
            Stage.custom_name.label("stage_custom_name"),
            Phases.id.label("phase_id"),
            Phases.type_.label("phase_type"),
            Attribute.id.label("attribute_id"),
            Attribute.type.label("attribute_type"),
            Attribute.name.label("attribute_name"),
            Attribute.custom_name.label("attribute_custom_name"),
            Attribute.order.label("attribute_order"),
            Values.value.label("value"),
            Values.row_id.label("row_id"),
            Fields.deleted_at.label("field_deleted_at"),
            Projects.deleted_at.label("project_deleted_at"),
            Projects.reporting_enabled.label("reporting_enabled"),
        )
        .select_from(Projects)
        .join(Phases, Phases.program_id == Projects.program_id)
        .join(Stage, Phases.id == Stage.phase_id)
        .join(Attribute, Stage.id == Attribute.parent_stage_id)
        .join(Fields, Fields.parent_project_id == Projects.id)
        .join(Values, and_(Values.attribute_id == Attribute.id, Values.field_id == Fields.id))
        .where(Projects.program_id == program_id)
        .where(Phases.deleted_at.is_(None))
        .where(Phases.enabled.is_(True))
        .where(Stage.deleted_at.is_(None))
        .where(Stage.enabled.is_(True))
        .where(Attribute.deleted_at.is_(None))
        .where(Attribute.enabled.is_(True))
    )
    if project_ids:
        query = query.where(Projects.id.in_(project_ids))
    if stage_type_to_exclude:
        query = query.where(Stage.type_.not_in(stage_type_to_exclude))
    if not include_deleted_projects:
        query = query.where(
            Projects.deleted_at.is_(None),
        )
    if not include_deleted_fields:
        query = query.where(
            Fields.deleted_at.is_(None),
        )
    if not include_reporting_disabled:
        query = query.where(Projects.reporting_enabled.is_(True))

    async with request.state.sql_session() as s:
        records = await run_query(query=query, s=s)
        records = records.all()
    return records


async def get_field_boundaries(
    request: Request,
    program_id: int,
    include_deleted_projects: bool,
    include_deleted_fields: bool,
    include_reporting_disabled: bool,
) -> list[FieldExportOutput]:
    """
    Returns field details for a program, with geometry and centroid.
    """
    project_groups_subquery = get_project_groups_subquery(program_id=program_id)
    query = (
        select(
            core_model.Users.email.label("user_email"),
            Projects.id.label("project_id"),
            project_groups_subquery.c.group_ids.label("group_ids"),
            project_groups_subquery.c.group_names.label("group_names"),
            core_model.Groups.name.label("farm_name"),
            core_model.KMLGroups.name.label("field_name"),
            Fields.id.label("mrv_field_id"),
            Fields.md5.label("field_md5"),
            Fields.fs_field_id.label("fs_field_id"),
            Fields.area.label("field_area_ha"),
            Fields.status.label("field_status"),
            func.ST_AsGeoJSON(func.ST_SwapXY(core_model.KMLFiles.geometry)).label("geojson"),
            func.ST_AsText(func.ST_SwapXY(core_model.KMLFiles.geometry)).label("geometry_wkt"),
            case(
                (
                    or_(
                        core_model.KMLFiles.geometry.is_(None), func.not_(func.st_isvalid(core_model.KMLFiles.geometry))
                    ),
                    0.0,
                ),
                (
                    func.ST_GeometryType(core_model.KMLFiles.geometry) == "POLYGON",
                    func.ST_Y(
                        func.ST_Transform(func.ST_Centroid(func.ST_Transform(core_model.KMLFiles.geometry, 6933)), 4326)
                    ),
                ),
                (
                    func.ST_GeometryType(core_model.KMLFiles.geometry) == "MULTIPOLYGON",
                    func.ST_Y(
                        func.ST_Transform(func.ST_Centroid(func.ST_Transform(core_model.KMLFiles.geometry, 6933)), 4326)
                    ),
                ),
                else_=None,
            ).label("centroid_lon"),
            case(
                (
                    or_(
                        core_model.KMLFiles.geometry.is_(None), func.not_(func.st_isvalid(core_model.KMLFiles.geometry))
                    ),
                    0.0,
                ),
                (
                    func.ST_GeometryType(core_model.KMLFiles.geometry) == "POLYGON",
                    func.ST_X(
                        func.ST_Transform(func.ST_Centroid(func.ST_Transform(core_model.KMLFiles.geometry, 6933)), 4326)
                    ),
                ),
                (
                    func.ST_GeometryType(core_model.KMLFiles.geometry) == "MULTIPOLYGON",
                    func.ST_X(
                        func.ST_Transform(func.ST_Centroid(func.ST_Transform(core_model.KMLFiles.geometry, 6933)), 4326)
                    ),
                ),
                else_=None,
            ).label("centroid_lat"),
            Fields.deleted_at.label("field_deleted_at"),
            Projects.deleted_at.label("project_deleted_at"),
            Projects.reporting_enabled.label("reporting_enabled"),
        )
        .select_from(Projects)
        .join(Fields, Fields.parent_project_id == Projects.id)
        .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
        .join(core_model.Users, ProjectPermissions.user == core_model.Users.id)
        .outerjoin(project_groups_subquery, Projects.id == project_groups_subquery.c.project_id)
        .join(core_model.KMLGroups, core_model.KMLGroups.id == Fields.fs_field_id)
        .join(core_model.KMLFiles, core_model.KMLFiles.id == core_model.KMLGroups.kml_id)
        .join(core_model.Groups, core_model.Groups.id == Fields.farm_id)
        .join(core_model.CoreRegions, core_model.CoreRegions.id == core_model.KMLFiles.region_id)
        .where(Projects.program_id == program_id)
    )
    if not include_deleted_projects:
        query = query.where(
            Projects.deleted_at.is_(None),
            ProjectPermissions.deleted_at.is_(None),
            core_model.Users.deleted_at.is_(None),
        )
    if not include_deleted_fields:
        query = query.where(Fields.deleted_at.is_(None))
    if not include_reporting_disabled:
        query = query.where(Projects.reporting_enabled.is_(True))

    async with request.state.sql_session() as s:
        records = await run_query(query=query, s=s)
        records = records.all()
    return records


async def get_producer_data_per_program(
    request: Request,
    program_id: int,
    include_deleted_projects: bool,
    include_reporting_disabled: bool,
) -> list[ProducerExportOutput]:
    """
    Returns producer details for a program, with phone number and country.
    """
    project_groups_subquery = get_project_groups_subquery(program_id=program_id)
    query = (
        select(
            core_model.Users.id.label("user_id"),
            Projects.id.label("project_id"),
            project_groups_subquery.c.group_ids.label("group_ids"),
            project_groups_subquery.c.group_names.label("group_names"),
            core_model.Users.name.label("user_first_name"),
            core_model.Users.surname.label("user_last_name"),
            core_model.Users.email.label("user_email"),
            core_model.Users.settings["company"]["country"].label("user_country"),
            core_model.Users.settings["company"]["state"].label("user_region"),
            case(
                (
                    and_(core_model.Users.phone.isnot(None), core_model.Users.phone != ""),
                    core_model.Users.phone,
                ),
                else_=core_model.Users.settings["phone"].as_string(),
            ).label("user_phone_number"),
            Projects.deleted_at.label("project_deleted_at"),
            Projects.reporting_enabled.label("reporting_enabled"),
        )
        .select_from(Projects)
        .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
        .join(core_model.Users, ProjectPermissions.user == core_model.Users.id)
        .outerjoin(project_groups_subquery, Projects.id == project_groups_subquery.c.project_id)
        .where(Projects.program_id == program_id)
    )
    if not include_deleted_projects:
        query = query.where(
            Projects.deleted_at.is_(None),
            ProjectPermissions.deleted_at.is_(None),
            core_model.Users.deleted_at.is_(None),
        )
    if not include_reporting_disabled:
        query = query.where(Projects.reporting_enabled.is_(True))

    async with request.state.sql_session() as s:
        records = await run_query(query=query, s=s)
        records = records.all()
    return records


async def get_program_field_overlaps(
    request: Request, program_id: int, include_deleted_projects: bool, include_reporting_disabled: bool
) -> list[Row]:
    """
    If we perform any field boundary checks in the program, and if at any time we had a rule config to detect
    program field overlaps, this function will return all the overlaps detected in the program.

    See the comments in the function for what some of the columns mean.
    """
    overlapped_with_project = aliased(Projects)
    source_project = aliased(Projects)
    overlapped_with_permission = aliased(ProjectPermissions)
    source_permission = aliased(ProjectPermissions)
    query = (
        select(
            ProgramFieldOverlap.id.label("overlap_id"),
            ProgramFieldOverlap.created_at.label("overlap_detected_at_utc"),
            # If `overlap_after_delete` is "true" it means there's a bug in our code. When this value is "true", it
            # means that the overlap was detected *after* the field/project that was overlapped with, was deleted. This
            # should never happen. It means the producer saw an overlap error (if threshold was crossed) when there
            # shouldn't have been one.
            case(
                (
                    or_(
                        ProgramFieldOverlap.created_at > Fields.deleted_at,
                        ProgramFieldOverlap.created_at > overlapped_with_permission.deleted_at,
                        ProgramFieldOverlap.created_at > overlapped_with_project.deleted_at,
                    ),
                    "true",
                ),
                else_="false",
            ).label("overlap_after_delete"),
            # `percentage_overlap` and `area_intersection_ha` apply to this particular overlapped with field only. If
            # the source field is overlaps multiple fields, there will be multiple rows for the same source field,
            # each with different `percentage_overlap` and `area_intersection_ha` values.
            ProgramFieldOverlap.percentage_overlap,
            ProgramFieldOverlap.area_intersection_ha,
            # Columns with "attempted_" prefix refer to the field/project that the attempt to add/edit field came from,
            # i.e. they refer to the field that the producer was trying to add/edit.
            core_model.KMLGroups.name.label("attempted_adding_field_name"),
            core_model.KMLGroups.id.label("attempted_adding_field_core_field_id"),
            source_project.id.label("attempted_from_project_id"),
            source_project.status.label("attempted_from_project_status"),
            # Columns with "overlapped_with_" refer to the field/project that the field overlapped with. They are the
            # fields, which at the time of the overlap check, where already registered (and non-deleted) in the program.
            overlapped_with_project.id.label("overlapped_with_project_id"),
            overlapped_with_project.status.label("overlapped_with_project_status"),
            Fields.id.label("overlapped_with_field_id"),
            # Note that since the overlap was detected, the field that was overlapped with may have been deleted.
            Fields.status.label("overlapped_with_field_status"),
        )
        .join(Fields, ProgramFieldOverlap.field_id == Fields.id)
        .join(overlapped_with_project, Fields.parent_project_id == overlapped_with_project.id)
        .join(BoundaryRuleDeviation, ProgramFieldOverlap.deviation_id == BoundaryRuleDeviation.id)
        .join(core_model.KMLFiles, BoundaryRuleDeviation.md5 == core_model.KMLFiles.md5)
        .join(core_model.KMLGroups, core_model.KMLFiles.id == core_model.KMLGroups.kml_id)
        .join(source_permission, core_model.KMLGroups.created_by == source_permission.user)
        .join(source_project, source_permission.project == source_project.id)
        .join(overlapped_with_permission, overlapped_with_project.id == overlapped_with_permission.project)
        .where(source_project.program_id == program_id, overlapped_with_project.program_id == program_id)
    )
    # The following apply to the source project (the project where the field add/edit attempt was made from), not the
    # project being overlapped with
    if not include_deleted_projects:
        query = query.where(
            source_project.deleted_at.is_(None),
            source_permission.deleted_at.is_(None),
        )
    if not include_reporting_disabled:
        query = query.where(source_project.reporting_enabled.is_(True))
    # Order by descending date order of overlaps detected. Then order by ID to make order completely deterministic.
    query = query.order_by(ProgramFieldOverlap.created_at.desc(), ProgramFieldOverlap.id.desc())
    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).all()


async def get_user_or_program_locale(request: Request, program_id: int, user_id: int) -> Locale:
    """
    Returns the locale for a user,
    If user locale is not present returns program locale.
    """
    query = select(
        core_model.Users.settings,
    ).where(core_model.Users.id == user_id)

    async with request.state.sql_session() as s:
        records = await run_query(query=query, s=s)
        user_settings = records.scalar_one_or_none()
    locale = None
    if user_settings is not None:
        locale = user_settings.get("langLocale")
        if locale is None:
            locale = user_settings.get("locale")
    if locale is None:
        query = select(
            Programs.locale,
        ).where(Programs.id == program_id)
        async with request.state.sql_session() as s:
            records = await run_query(query=query, s=s)
            program_locale = records.scalar_one_or_none()
        if program_locale is None:
            locale = "en-US"
        locale = program_locale
    try:
        return Locale(locale)
    except ValueError:
        return Locale.en_US


async def get_or_create_export_run(
    request: Request,
    program_id: int,
    query_params: dict[str, Any],
) -> tuple[ProgramExportRunResponse, bool]:
    """
    Returns the export run if it exists, else creates a new export run.
    """
    current_export_run = await get.generic_get(
        request=request,
        orm_type=ProgramExportRuns,
        type_=ProgramExportRunResponse,
        filters=[
            get.Filter(id_field=ProgramExportRuns.program_id, ids=[program_id]),
            get.Filter(id_field=ProgramExportRuns.user, ids=[request.state.fs_user_id]),
            get.Filter(id_field=ProgramExportRuns.export_run_status, ids=[ExportRunStatus.IN_PROGRESS]),
        ],
        empty_return=True,
    )
    is_new_request = False
    if not current_export_run:
        is_new_request = True
        current_export_run = await create.create(
            request=request,
            instances=[
                ProgramExportRunCreate(
                    program_id=program_id,
                    query_params=query_params,
                    user=request.state.fs_user_id,
                )
            ],
            orm_type=ProgramExportRuns,
            type=ProgramExportRunResponse,
            translate=True,
        )
    return current_export_run[0], is_new_request


async def get_latest_export_run(
    request: Request,
    program_id: int,
) -> ProgramExportRunResponse:
    """
    Returns the latest export run for a program.
    """
    return (
        await get.generic_get(
            request=request,
            orm_type=ProgramExportRuns,
            type_=ProgramExportRunResponse,
            filters=[
                get.Filter(id_field=ProgramExportRuns.program_id, ids=[program_id]),
                get.Filter(id_field=ProgramExportRuns.user, ids=[request.state.fs_user_id]),
            ],
            order_by_desc=True,
            order_by_cols=[ProgramExportRuns.created_at],
            empty_return=True,
        )
    )[0]


async def update_export_run(
    request: Request,
    export_run_id: int,
    failure_reason: str | None = None,
    gcs_object: str | None = None,
) -> ProgramExportRunResponse:
    """
    Updates the export run with the status and failure reason in case of failure.
    """

    return await update.partial_update(
        request=request,
        id=export_run_id,
        item=ProgramExportRunUpdate(
            export_run_status=ExportRunStatus.FAILED if failure_reason else ExportRunStatus.COMPLETED,
            failure_reason=failure_reason,
            gcs_object=gcs_object,
        ),
        orm_type=ProgramExportRuns,
    )


async def get_all_in_process_export_run_after_t_minutes(
    request: Request,
    time_in_minutes: int,
) -> list[ProgramExportRunResponse]:
    """
    Returns all the export runs that are in progress and have been running for more than t minutes.
    """
    query = select(ProgramExportRuns).where(
        and_(
            ProgramExportRuns.export_run_status == ExportRunStatus.IN_PROGRESS,
            ProgramExportRuns.created_at <= text(f"NOW() - INTERVAL {time_in_minutes} MINUTE"),
        )
    )

    async with request.state.sql_session() as session:
        return (await run_query(query=query, s=session)).scalars().all()
