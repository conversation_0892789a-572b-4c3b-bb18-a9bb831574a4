import time
from collections import defaultdict
from contextlib import contextmanager
from datetime import datetime, timezone
from typing import Iterator, List, Tuple, Type

from fastapi import Request
from google.cloud import bigquery
from google.protobuf.json_format import Parse
from regrow.ses.context.v1.context_pb2 import EventContext
from regrow.ses.event.v1.event_pb2 import Event
from ses_client.event import event_type, EventWithContext, StructuredEvent
from ses_client.sequence import cropping_sequence

from annotations import DictStrAny
from config import get_settings
from cultivation_cycles.methods import get_cultivation_cycles
from cultivation_cycles.schema import CultivationCycleId
from defaults.attribute_options import (
    ApplicationMethod,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent, SESEventWithContext
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from logger import get_logger
from phases.db import get_stages_from_program_and_phase_types
from phases.enums import PhaseTypes, StageTypes
from programs.model import Programs
from ses_integration.constants import (
    SES_EVENT_TO_ENTITY_EVENT,
)
from ses_integration.no_practice_observations import (
    get_no_practice_observations_from_ses_structured_event_context,
)
from ui.projects.field_events.methods import (
    _filter_m_phase_cultivation_cycles,
    get_event_query_range_for_reporting_period,
)
from ui.projects.field_events.schema import (
    NoPracticeObservations,
)

settings = get_settings()
logger = get_logger(__name__)
schema = settings.BIGQUERY_PROJECT
env = settings.env if settings.env == "prod" else "dev"
client = bigquery.Client(project=schema)

ApplicationMethodMap: dict[str, str] = {
    ApplicationMethod.BROADCASTED: ApplicationMethod.BROADCASTED,
    ApplicationMethod.FERTIGATION: "Fertigation subsurface drip",
    ApplicationMethod.FERTIGATION_FURROW: ApplicationMethod.FERTIGATION_FURROW,
    ApplicationMethod.FERTIGATION_SPRINKLER: ApplicationMethod.FERTIGATION_SPRINKLER,
    ApplicationMethod.FERTIGATION_DRIP: ApplicationMethod.FERTIGATION_DRIP,
    ApplicationMethod.INJECTED: ApplicationMethod.INJECTED,
    ApplicationMethod.AVIATION: ApplicationMethod.AVIATION,
    ApplicationMethod.SUBSURFACE: ApplicationMethod.SUBSURFACE,
    ApplicationMethod.INCORPORATED: ApplicationMethod.INCORPORATED,
}


@contextmanager
def timer(event_name: str = "Timer") -> Iterator[None]:
    start_time = time.perf_counter()
    try:
        yield
    finally:
        end_time = time.perf_counter()
        elapsed_time = end_time - start_time
        logger.debug(f"Elapsed time for {event_name}: {elapsed_time:.4f} seconds")


async def _get_enabled_phase_type_to_stage_types(
    request: Request, program: Programs
) -> dict[PhaseTypes, list[StageTypes]]:
    enabled_phase_types = [PhaseTypes.ENROLMENT]
    if not program.is_single_phase_data_collection:
        enabled_phase_types.append(PhaseTypes.MONITORING)
    enabled_phase_type_to_stage_types = {}
    for phase_type in enabled_phase_types:
        enabled_stages = await get_stages_from_program_and_phase_types(
            request=request, program_id=program.id, phase_types=[phase_type]
        )
        enabled_phase_type_to_stage_types[phase_type] = [stage.type_ for stage in enabled_stages]
    return enabled_phase_type_to_stage_types


def _query_bigquery_for_program_events(program_id: int, is_single_phase_data_collection: bool) -> list[dict]:
    """
    Export all program data for events from BigQuery.
    """
    query = """
        WITH associations as (
            SELECT
                event_association.project_id as project_id,
                event_association.field_id as field_id,
                event_association.ses_event_id as event_id,
                phase_associations.revision as revision,
                phases.type_ as phase_type
            FROM
                `flurosense_db_sync.flurosense-dev_mrv_field_event_associations` as event_association
                JOIN `flurosense_db_sync.flurosense-dev_mrv_phase_event_associations` as phase_associations ON event_association.id = phase_associations.field_event_association_id
                JOIN `flurosense_db_sync.flurosense-dev_mrv_phases` as phases ON phases.id = phase_associations.phase_id
            WHERE phase_associations.deleted_at IS NULL
                AND event_association.deleted_at IS NULL
                AND event_association.program_id = @program_id
                AND phases.type_ = @phase_type
        ),
        latest_contexts AS (
          SELECT *
          FROM (
            SELECT *,
                   ROW_NUMBER() OVER (PARTITION BY event_id, event_revision ORDER BY created DESC) AS rn
            FROM `ses_db_sync.public_context`
          ) ranked
          WHERE rn = 1
        )
        SELECT
                associations.project_id as project_id,
                associations.field_id as field_id,
                associations.event_id as event_id,
                associations.phase_type as phase_type,
                events.data as event_data,
                contexts.data as context_data
            FROM
                associations
                JOIN `ses_db_sync.public_event` as events ON associations.event_id = events.id and associations.revision = events.revision
                JOIN latest_contexts AS contexts ON associations.event_id = contexts.event_id AND associations.revision = contexts.event_revision
                ORDER BY project_id, field_id
    """

    if env == "prod":
        # Names are set in BigQuery and should be renamed to be generic in the future
        query = query.replace("flurosense-dev", "flurosense-prod")

    phase_type = PhaseTypes.ENROLMENT.value if is_single_phase_data_collection else PhaseTypes.MONITORING.value
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("program_id", "INT64", program_id),
            bigquery.ScalarQueryParameter("phase_type", "STRING", phase_type),
        ]
    )
    with timer("QUERY"):
        rows = client.query_and_wait(query, job_config=job_config).to_dataframe()  # API request
    return rows.to_dict(orient="records")


def _parse_ses_events_to_entity_events(
    field_id: int,
    cropping_events: (
        List[Tuple[SESEventWithContext | None, SESEventWithContext | None, SESEventWithContext | None]] | None
    ),
    field_practice_events: List[SESEventWithContext] | None,
) -> Tuple[List[EntityEvent], dict[str, NoPracticeObservations], dict[CroppingIDs, Tuple[datetime, datetime]]]:
    """
    Convert SES events to entity events.
    """
    entity_events = []
    no_practice_observations: dict[str, NoPracticeObservations] = dict[str, NoPracticeObservations]()
    cropping_dates: dict[CroppingIDs, Tuple[datetime, datetime]] = {}
    if cropping_events is not None:
        for field_crop_events in cropping_events:
            try:
                entity_event = CroppingEvent.from_ses_events(events=field_crop_events, entity_id=field_id)
                cropping_dates[str(entity_event.id)] = (
                    entity_event.get_interval_start_or_occurred_at(),
                    entity_event.get_interval_end_or_occurred_at(),
                )
                for evt in field_crop_events:
                    if evt and evt.context:
                        npo = get_no_practice_observations_from_ses_structured_event_context(evt.context)
                        if npo:
                            # If there are no practice observations, we map them to the cropping event
                            # This is useful for harvesting events that have no practice observations
                            no_practice_observations[str(entity_event.id)] = npo

            except Exception as e:
                logger.error(f"Error parsing cropping events: {e}")
                continue
            entity_events.append(entity_event)

    if field_practice_events is not None:
        for ses_event in field_practice_events:
            ses_type = event_type(ses_event.event)
            entity_event_type: Type[EntityEvent] = SES_EVENT_TO_ENTITY_EVENT[ses_type]
            npo = get_no_practice_observations_from_ses_structured_event_context(ses_event.context)
            if ses_type is not None:
                entity_event = entity_event_type.from_ses_events(events=[ses_event], entity_id=field_id)
                entity_events.append(entity_event)
                if npo is not None and isinstance(entity_event, FallowPeriod):
                    # If there are no practice observations, we map them to the cropping event
                    # This is useful for harvesting events that have no practice observations
                    no_practice_observations[str(entity_event.id)] = npo
                    cropping_dates[str(entity_event.id)] = (
                        entity_event.get_interval_start_or_occurred_at(),
                        entity_event.get_interval_end_or_occurred_at(),
                    )
            else:
                logger.error(f"Unsupported SES event type: {ses_type}")
    return (entity_events, no_practice_observations, cropping_dates)


def _filter_events(
    all_events: List[SESEventWithContext],
    field_id: int,
) -> Tuple[List[EntityEvent], dict[str, NoPracticeObservations], dict[CroppingIDs, Tuple[datetime, datetime]]]:
    """
    Filter events to get cropping and field practice events.
    """
    event_with_context_by_id = {ev.event.id: ev for ev in all_events}
    cropping_components = [
        ev.event
        for ev in all_events
        if event_type(ev.event)
        in {
            StructuredEvent.TYPE_SOWING_ACTIVITY,
            StructuredEvent.TYPE_PLANTING_ACTIVITY,
            StructuredEvent.TYPE_HARVEST_ACTIVITY,
            StructuredEvent.TYPE_TERMINATION_ACTIVITY,
        }
    ]
    cropping_events: list[tuple[Event | None, Event | None, Event | None]] = cropping_sequence(cropping_components)
    cropping_events_with_context = [
        (
            event_with_context_by_id.get(composite_crop_event[0].id) if composite_crop_event[0] else None,
            event_with_context_by_id.get(composite_crop_event[1].id) if composite_crop_event[1] else None,
            event_with_context_by_id.get(composite_crop_event[2].id) if composite_crop_event[2] else None,
        )
        for composite_crop_event in cropping_events
    ]
    noncropping_events = [ev for ev in all_events if ev.event.id not in {cr.id for cr in cropping_components}]
    return _parse_ses_events_to_entity_events(
        field_id=field_id,
        cropping_events=cropping_events_with_context,
        field_practice_events=[EventWithContext(event=ev.event, context=ev.context) for ev in noncropping_events],
    )


def _convert_rows_to_event(
    rows: list[dict],
) -> Tuple[dict[str, List[EntityEvent]], dict[str, NoPracticeObservations]]:
    """
    Convert dict to events.
    """
    logger.debug("converting events")
    events: dict[str, list] = defaultdict(list)
    res: dict[str, List[EntityEvent]] = {}
    no_practice_observations: dict[str, NoPracticeObservations] = dict[str, NoPracticeObservations]()

    with timer("PARSE EVENTS"):
        for row in rows:
            field_id = row["field_id"]
            if field_id not in events:
                events[field_id] = []
            event_id = row["event_id"]
            event = Parse(row["event_data"], Event(), ignore_unknown_fields=True)
            event.id = event_id
            context = Parse(row["context_data"], EventContext(), ignore_unknown_fields=True)
            ses_event = SESEventWithContext(event=event, context=context)
            events[field_id].append(ses_event)
    with timer("FILTER CROPPING EVENTS"):
        for field_id, ses_events in events.items():
            processed_events, np, _ = _filter_events(ses_events, int(field_id))
            if np:
                no_practice_observations.update(np)

            res[field_id] = processed_events
    return res, no_practice_observations


def _event_to_csv(
    ret: dict[str, dict[StageTypes, list[DictStrAny]]],
    ses_events: list[EntityEvent],
    field_id: str,
    application_product_regrow_name_to_product_map: dict[str, dict],
    cultivation_cycle_id: CultivationCycleId | None,
    no_practice_observations: dict[str, NoPracticeObservations],
    phase_name: str,
    enabled_stage_types: list[StageTypes],
) -> dict[str, dict[StageTypes, list[DictStrAny]]]:
    """
    Convert event to CSV format.
    """
    crop_year: str | None = None
    no_practice_observation: NoPracticeObservations | None = None

    if cultivation_cycle_id is not None:

        cropping_event_id: CroppingIDs = cultivation_cycle_id.crop_event_id
        if cropping_event_id is not None:
            # Get no practice observation for the harvesting event
            no_practice_observation = no_practice_observations.get(str(cropping_event_id))

        if cultivation_cycle_id.crop_type is not None:
            cycle_crop = cultivation_cycle_id.crop_type.replace("_", " ").capitalize()
            cycle_start = cultivation_cycle_id.start_date.strftime("%b %Y")
            cycle_end = cultivation_cycle_id.end_date.strftime("%b %Y")
            if cycle_crop == "Fallow":
                cycle_crop = "No Commodity"
            crop_year = f"{cycle_crop} {cycle_start} - {cycle_end}"

        else:
            crop_year = f"Empty Cycle {cultivation_cycle_id.harvest_year}"

    for ses_event in ses_events:
        res = DictStrAny()
        res["MRV Field ID"] = field_id
        res["Crop Year"] = crop_year

        stage_type = None
        if isinstance(ses_event, TillageEvent):
            stage_type = StageTypes.TILLAGE_EVENTS
            if stage_type not in enabled_stage_types:
                continue
            if stage_type not in ret[phase_name]:
                ret[phase_name][stage_type] = []

            till_ui_dict = ses_event.to_ui_dict()

            res["Tilled"] = 1
            res["Tillage Practice"] = till_ui_dict.get("tillage_practice")
            res["Tillage Date"] = till_ui_dict.get("tillage_date")
            res["Tillage Depth"] = till_ui_dict.get("tillage_depth")
            res["Tillage Depth Units"] = till_ui_dict.get("tillage_depth_unit")
            res["Soil inversion"] = till_ui_dict.get("soil_inversion")

        elif isinstance(ses_event, ApplicationEvent):
            stage_type = StageTypes.NUTRIENT_EVENTS
            if stage_type not in enabled_stage_types:
                continue
            if stage_type not in ret[phase_name]:
                ret[phase_name][stage_type] = []

            app_ui_dict = ses_event.to_ui_dict()

            product_name = app_ui_dict.get("application_product")
            product = application_product_regrow_name_to_product_map.get(product_name, {})

            res["Application Date"] = app_ui_dict.get("application_date")
            res["Applied"] = 1
            res["Product"] = product.get("name", 0)
            res["N ratio"] = product.get("n_ratio", 0)
            res["P ratio"] = product.get("p_ratio", 0)
            res["K ratio"] = product.get("k_ratio", 0)
            res["Rate Type"] = app_ui_dict.get("application_rate_type")
            res["Rate"] = app_ui_dict.get("application_rate")
            res["Rate Unit"] = app_ui_dict.get("application_rate_unit")

            method = app_ui_dict.get("application_method")
            res["Application Method"] = ApplicationMethodMap.get(method, method)

            res["Application Depth"] = app_ui_dict.get("application_depth")
            res["Application Depth Unit"] = app_ui_dict.get("application_depth_unit")
            res["Water Amount"] = app_ui_dict.get("water_amount")
            res["Water Amount Unit"] = app_ui_dict.get("water_amount_unit")
            res["Additives"] = app_ui_dict.get("additives")

        elif isinstance(ses_event, IrrigationEvent):
            stage_type = StageTypes.IRRIGATION_EVENTS
            if stage_type not in enabled_stage_types:
                continue
            if stage_type not in ret[phase_name]:
                ret[phase_name][stage_type] = []

            irr_ui_dict = ses_event.to_ui_dict()

            res["Irrigated"] = 1
            res["Start Date"] = irr_ui_dict.get("start_date")
            res["End Date"] = irr_ui_dict.get("end_date")
            res["Irrigation Method"] = irr_ui_dict.get("irrigation_method")
            res["Subsurface Drip Depth"] = irr_ui_dict.get("subsurface_depth")
            res["Subsurface Drip Depth Unit"] = irr_ui_dict.get("subsurface_drip_depth_unit")
            res["Flood %"] = irr_ui_dict.get("flood_percentage")

        elif isinstance(ses_event, CroppingEvent):
            stage_type = StageTypes.CROP_EVENTS
            if stage_type not in enabled_stage_types:
                continue
            if stage_type not in ret[phase_name]:
                ret[phase_name][stage_type] = []

            crop_ui_dict = ses_event.to_ui_dict()

            res["Crop Type"] = crop_ui_dict.get("crop_type")
            res["Crop Usage"] = crop_ui_dict.get("crop_usage")
            res["Planting Date"] = crop_ui_dict.get("planting_date")
            res["Harvest Date"] = crop_ui_dict.get("harvest_date")
            res["Termination method"] = crop_ui_dict.get("termination_method")
            res["Crop Yield"] = crop_ui_dict.get("crop_yield")
            res["Yield Unit"] = crop_ui_dict.get("yield_rate_unit")
            res["Residue Harvested"] = crop_ui_dict.get("residue_harvested")

        elif isinstance(ses_event, FallowPeriod):
            stage_type = StageTypes.CROP_EVENTS
            if stage_type not in enabled_stage_types:
                continue
            if stage_type not in ret[phase_name]:
                ret[phase_name][stage_type] = []

            fallow_ui_dict = ses_event.to_ui_dict()

            res["Crop Type"] = "Fallow"
            res["Crop Usage"] = None
            res["Planting Date"] = fallow_ui_dict.get("start_date")
            res["Harvest Date"] = fallow_ui_dict.get("end_date")
            res["Termination method"] = None
            res["Crop Yield"] = None
            res["Yield Unit"] = None
            res["Residue Harvested"] = None
        else:
            logger.warning(f"Unknown event type: {ses_event.entity_type}")
            continue
        ret[phase_name][stage_type].append(res)

    if no_practice_observation:
        if no_practice_observation.tillage_event:
            npo_stage = StageTypes.TILLAGE_EVENTS
            if npo_stage in enabled_stage_types:
                npo_res = DictStrAny()
                npo_res["MRV Field ID"] = field_id
                npo_res["Crop Year"] = crop_year

                npo_res["Tilled"] = 0
                npo_res["Tillage Practice"] = None
                npo_res["Tillage Date"] = None
                npo_res["Tillage Depth"] = None
                npo_res["Tillage Depth Units"] = None
                npo_res["Soil inversion"] = None

                if npo_stage not in ret[phase_name]:
                    ret[phase_name][npo_stage] = []
                ret[phase_name][npo_stage].append(npo_res)

        if no_practice_observation.application_event:
            npo_stage = StageTypes.NUTRIENT_EVENTS
            if npo_stage in enabled_stage_types:
                npo_res = DictStrAny()
                npo_res["MRV Field ID"] = field_id
                npo_res["Crop Year"] = crop_year

                npo_res["Application Date"] = None
                npo_res["Applied"] = 0
                npo_res["Product"] = None
                npo_res["N ratio"] = None
                npo_res["P ratio"] = None
                npo_res["K ratio"] = None
                npo_res["Rate Type"] = None
                npo_res["Rate"] = None
                npo_res["Rate Unit"] = None
                npo_res["Application Method"] = None
                npo_res["Application Depth"] = None
                npo_res["Application Depth Unit"] = None
                npo_res["Water Amount"] = None
                npo_res["Water Amount Unit"] = None
                npo_res["Additives"] = None

                if npo_stage not in ret[phase_name]:
                    ret[phase_name][npo_stage] = []
                ret[phase_name][npo_stage].append(npo_res)

        if no_practice_observation.irrigation_event:
            npo_stage = StageTypes.IRRIGATION_EVENTS
            if npo_stage in enabled_stage_types:
                npo_res = DictStrAny()
                npo_res["MRV Field ID"] = field_id
                npo_res["Crop Year"] = crop_year

                npo_res["Irrigated"] = 0
                npo_res["Start Date"] = None
                npo_res["End Date"] = None
                npo_res["Irrigation Method"] = None
                npo_res["Subsurface Drip Depth"] = None
                npo_res["Subsurface Drip Depth Unit"] = None
                npo_res["Flood %"] = None

                if npo_stage not in ret[phase_name]:
                    ret[phase_name][npo_stage] = []
                ret[phase_name][npo_stage].append(npo_res)

    return ret


def _filter_events_by_phase_date_range(
    events: list[EntityEvent], program: Programs, phase_type: PhaseTypes
) -> list[EntityEvent]:
    (range_start, range_end) = get_event_query_range_for_reporting_period(program=program, phase_type=phase_type)
    range_start = range_start.replace(tzinfo=timezone.utc)
    range_end = range_end.replace(tzinfo=timezone.utc)
    included_events = []
    for ev in events:
        if ev.get_interval_start_or_occurred_at():
            if ev.get_interval_start_or_occurred_at().replace(tzinfo=timezone.utc) < range_start:
                # If the event starts or occurs before the range, skip it
                continue
        if ev.get_interval_end_or_occurred_at():
            if ev.get_interval_end_or_occurred_at().replace(tzinfo=timezone.utc) > range_end:
                # If the event ends or occurs after the range, skip it
                continue
        included_events.append(ev)
    return included_events


async def get_program_events(
    request: Request,
    program: Programs,
    application_product_regrow_name_to_product_map: dict[str, dict],
) -> dict[str, dict[str, list[DictStrAny]]]:
    """
    Get program events for the given program.
    This function queries the BigQuery database for events associated with the program,
    processes the events, and returns them in a structured format.
    """

    # dict[phase_name[stage, data]]
    ret: dict[str, dict[StageTypes, list[DictStrAny]]] = {}

    rows = _query_bigquery_for_program_events(
        program_id=program.id, is_single_phase_data_collection=program.is_single_phase_data_collection
    )
    (events_by_field, no_practice_observations) = _convert_rows_to_event(rows)
    enabled_phase_type_to_stage_types = await _get_enabled_phase_type_to_stage_types(request=request, program=program)
    with timer("GENERATE CSV"):
        for field, events in events_by_field.items():
            for phase_type in enabled_phase_type_to_stage_types.keys():
                enabled_stage_types = enabled_phase_type_to_stage_types[phase_type]
                phase_name = "ENROLLMENT" if phase_type == PhaseTypes.ENROLMENT else "MONITORING"
                if phase_name not in ret:
                    ret[phase_name] = dict[StageTypes, list[DictStrAny]]()
                phase_events = _filter_events_by_phase_date_range(events, program, phase_type)
                cycles, unbinned_events = get_cultivation_cycles(events=phase_events)
                cycles = _filter_m_phase_cultivation_cycles(
                    phase_type=phase_type,
                    program=program,
                    cultivation_cycles=cycles,
                )
                for cycle in cycles:
                    ret = _event_to_csv(
                        ret=ret,
                        ses_events=cycle.events,
                        field_id=field,
                        application_product_regrow_name_to_product_map=application_product_regrow_name_to_product_map,
                        cultivation_cycle_id=cycle.id,
                        no_practice_observations=no_practice_observations,
                        phase_name=phase_name,
                        enabled_stage_types=enabled_stage_types,
                    )
                ret = _event_to_csv(
                    ret=ret,
                    ses_events=unbinned_events,
                    field_id=field,
                    application_product_regrow_name_to_product_map=application_product_regrow_name_to_product_map,
                    cultivation_cycle_id=None,
                    no_practice_observations=no_practice_observations,
                    phase_name=phase_name,
                    enabled_stage_types=enabled_stage_types,
                )

    logger.debug("processed events")
    return ret
