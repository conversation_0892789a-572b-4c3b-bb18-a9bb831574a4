from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field
from pydantic_geojson import MultiPolygonModel

from fields.enums import FieldStatus
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs.export.enums import ExportRunStatus


class CommonExportSchema(BaseModel):
    user_email: str | None = None
    name: str | None = None
    surname: str | None = None
    project_id: int | None = None
    group_ids: str | None = None
    group_names: str | None = None
    mrv_field_id: int | None = None
    field_md5: str | None = None
    fs_field_id: int | None = None
    field_area: str | None = None
    field_status: str | None = None
    field_name: str | None = None
    farm_name: str | None = None
    state_name: str | None = None
    field_deleted_at: datetime | None = None
    project_deleted_at: datetime | None = None
    reporting_enabled: bool | None = None

    class Config:
        orm_mode = True


class RowDataWithAttributeValue(BaseModel):
    attribute_id: int
    attribute_type: AttributeTypes
    attribute_name: str
    attribute_order: int
    value: Any
    row_id: int

    class Config:
        orm_mode = True


class FieldStageRowData(BaseModel):
    mrv_field_id: int
    stage_id: int
    stage_type: StageTypes
    stage_name: str
    stage_order: int
    phase_id: int
    phase_type: PhaseTypes
    field_deleted_at: datetime | None = None
    project_deleted_at: datetime | None = None
    reporting_enabled: bool
    row_data: list[RowDataWithAttributeValue] = Field(default_factory=list)

    class Config:
        orm_mode = True


class FieldStageRowDataFlat(BaseModel):
    mrv_field_id: int
    stage_id: int
    stage_type: StageTypes
    stage_order: int
    stage_name: str
    stage_custom_name: str | None = None
    phase_id: int
    phase_type: PhaseTypes
    attribute_id: int
    attribute_type: AttributeTypes
    attribute_name: str
    attribute_order: int
    attribute_custom_name: str | None = None
    value: Any
    row_id: int
    field_deleted_at: datetime | None = None
    project_deleted_at: datetime | None = None
    reporting_enabled: bool

    class Config:
        orm_mode = True


class FieldExportOutput(BaseModel):
    user_email: str | None = None
    project_id: int
    group_ids: str | None = None
    group_names: str | None = None
    mrv_field_id: int
    field_md5: str
    fs_field_id: int
    field_area: str
    field_status: FieldStatus | None
    geojson: MultiPolygonModel
    geometry_wkt: str
    centroid_lon: float
    centroid_lat: float
    field_deleted_at: datetime | None = None
    project_deleted_at: datetime | None = None
    reporting_enabled: bool | None = None


class ProducerExportOutput(BaseModel):
    user_id: int
    project_id: int
    group_ids: str | None = None
    group_names: str | None = None
    user_first_name: str | None = None
    user_last_name: str | None = None
    user_email: str | None = None
    user_country: str | None = None
    user_region: str | None = None
    user_phone_number: str | None = None
    project_deleted_at: datetime | None = None
    reporting_enabled: bool | None = None


class ExportFileResponse(BaseModel):
    file_name: str
    download_url: str
    file_size: int

    class Config:
        orm_mode = True


class ProgramExportRunCreate(BaseModel):
    program_id: int
    query_params: dict[str, Any]
    user: int


class ProgramExportRunUpdate(BaseModel):
    export_run_status: ExportRunStatus
    failure_reason: str | None = None
    gcs_object: str | None = None


class ProgramExportRunResponse(BaseModel):
    id: int
    program_id: int
    export_run_status: str
    query_params: dict[str, Any]
    user: int
    gcs_object: str | None = None
    failure_reason: str | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
