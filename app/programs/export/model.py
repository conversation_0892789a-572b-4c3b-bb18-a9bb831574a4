import sqlalchemy as sa
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.sql.schema import Foreign<PERSON>ey

from config import get_settings
from core import model as core_model
from db.mysql import Base
from helper.mixin import IdCreateUpdateTimestampMixin
from programs.export.enums import ExportRunStatus

settings = get_settings()


class ProgramExportRuns(IdCreateUpdateTimestampMixin, Base):
    __tablename__ = "mrv_program_export_runs"
    __table_args__ = {
        "comment": (
            "This table stores the export runs for the program."
            "Each run is a snapshot of the program data at the time of export."
            "Only one export is allowed to run at a time for a program."
        )
    }

    program_id = sa.Column(ForeignKey("mrv_programs.id"), nullable=False)
    export_run_status = sa.Column(
        sa.Enum(ExportRunStatus),
        nullable=False,
        default=ExportRunStatus.IN_PROGRESS,
        comment="The status of the export run. Only one export per program is allowed to be in progress at a time.",
    )
    query_params = sa.Column(sa.JSON, nullable=False, default="{}")
    user = (
        sa.Column(
            INTEGER(10, unsigned=True),
            ForeignKey(core_model.Users.id),
            nullable=False,
            comment="The user who initiated the export run.",
        )
        if settings.env != "local"
        else sa.Column(sa.Integer, nullable=False)
    )
    failure_reason = sa.Column(sa.Text, nullable=True, comment="The reason for the failure of the export run.")
    gcs_object = sa.Column(sa.Text, nullable=True, comment="The GCS object of the exported file.")
