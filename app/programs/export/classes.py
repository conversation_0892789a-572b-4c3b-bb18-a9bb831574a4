from annotations import DictStrAny
from programs.enums import UnitsTypes
from programs.export.constants import (
    ACRES_FRAGMENT,
    HECTARES_FRAGMENT,
    TOTAL_COMPLETION,
    TOTAL_COMPLETION_PREFIX,
)
from programs.model import Programs


class RemapChartQuery:
    """
    Remaps parts of the cube query (measures, dimensions, order, filters) to:
        - replace area unit (acres/hectares) with matching program units
        - replace columns names containing "total_completion" postfix with program specific postfix for program 68 and
          155

    To use:
    ```python

    chart_query = RemapChartQuery(program_orm).remap(cube_query)
    ```
    """

    def __init__(self, program: Programs):
        if program.units == UnitsTypes.US_IMPERIAL:
            self.find_unit, self.replace_unit = HECTARES_FRAGMENT, ACRES_FRAGMENT
        else:
            self.find_unit, self.replace_unit = ACRES_FRAGMENT, HECTARES_FRAGMENT
        self.replace_completion = TOTAL_COMPLETION_PREFIX
        if program.id in (68, 155):
            self.replace_completion += f"_{program.id}"

    def remap(self, chart_query: DictStrAny) -> DictStrAny:
        """
        Remaps the chart query to replace area unit and total completion columns. This function mutates chart_query, as
        well as returning it.

        e.g. the query might look like this:

            {
              "query": {
                "dimensions": ["MrvReportingProjects.totalAreaAc"],
                "order": {
                  "MrvReportingProjects.total_completion_programId": "asc"
                },
                "measures": ["MrvReportingProjects.total_completion_programId"],
                "filters": [
                  {
                    "member": "MrvReportingProjects.totalAreaAc",
                    "operator": "equals",
                    "values": ["signed"]
                  }
                ]
              }
            }

        and if this program has metric and 68, it might remap it as:

            {
              "query": {
                "dimensions": ["MrvReportingProjects.totalAreaHa"],
                "order": {
                  "MrvReportingProjects.total_completion_68": "asc"
                },
                "measures": ["MrvReportingProjects.total_completion_68"],
                "filters": [
                  {
                    "member": "MrvReportingProjects.totalAreaHa",
                    "operator": "equals",
                    "values": ["signed"]
                  }
                ]
              }
            }
        """
        self._remap_chart_columns(chart_query, "dimensions")
        self._remap_chart_columns(chart_query, "measures")
        self._remap_chart_order(chart_query)
        self._remap_chart_filters(chart_query)
        return chart_query

    def _replace(self, column: str) -> str:
        return column.replace(self.find_unit, self.replace_unit).replace(TOTAL_COMPLETION, self.replace_completion)

    def _remap_chart_columns(self, chart_query: DictStrAny, column_type: str) -> None:
        if columns := chart_query.get(column_type):
            chart_query[column_type] = [self._replace(column) for column in columns]

    def _remap_chart_order(self, chart_query: DictStrAny) -> None:
        if order := chart_query.get("order"):
            if isinstance(order, list):
                chart_query["order"] = [[self._replace(column), direction] for column, direction in order]
            elif isinstance(order, dict):
                chart_query["order"] = {self._replace(column): direction for column, direction in order.items()}
            else:
                raise ValueError(f"Invalid order type: {type(order)} - needs to be either list or dict")

    def _remap_chart_filters(self, chart_query: DictStrAny) -> None:
        if filters := chart_query.get("filters"):
            for filter_dict in filters:
                if member := filter_dict.get("member"):
                    filter_dict["member"] = self._replace(member)
