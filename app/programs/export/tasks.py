from __future__ import annotations

from datetime import datetime, UTC
from typing import TYPE_CHECKING

import elasticapm
from celery import chain, group
from fastapi import Header

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from logger import get_logger
from notifications.enums import NotificationCodes, NotificationSources
from notifications.methods import get_notifications, update_notification_body
from notifications.schema import NotificationBody
from programs.db import get_program
from programs.enums import ProgramTemplate
from programs.export import paths
from programs.export.constants import EXPORT_DIR
from programs.export.db import (
    get_latest_export_run,
    get_or_create_export_run,
    get_user_or_program_locale,
    update_export_run,
)
from programs.export.enums import ExportOutputFileType
from programs.export.helper import create_notification_for_export
from programs.export.methods import (
    export_event_based_program_data,
    export_field_overlap_data,
    export_producer_data,
    export_program_data,
    export_reporting_data,
    export_zipped_data,
)
from slack_integration.integration import post_message

logger = get_logger(__name__)

if TYPE_CHECKING:
    from fastapi import Request

settings = get_settings()


async def _update_notification_for_export(
    request: Request, program_id: int, fs_user_id: int, code: NotificationCodes
) -> None:
    notifications = await get_notifications(
        session=request.state.sql_session,
        settings=settings,
        program_id=program_id,
        user_id=fs_user_id,
        source=NotificationSources.MRV_EXPORT,
        use_number_sent_limit=False,
    )
    await update_notification_body(
        session=request.state.sql_session,
        notification_ids=[i.id for i in notifications],
        body=NotificationBody(
            url=f"{settings.MRV_INTERNAL_URL}{paths.base}/{program_id}{paths.get_export_file}",
            method="GET",
            message="",
            code=code,
        ),
        program_id=program_id,
        user_id=fs_user_id,
    )


async def _handle_succeeded_export(request: Request, export_name: str, program_id: int, fs_user_id: int) -> None:
    export_run = await get_latest_export_run(
        request=request,
        program_id=program_id,
    )
    await update_export_run(
        request=request,
        export_run_id=export_run.id,
        gcs_object=f"{EXPORT_DIR}/{export_name}.zip",
    )
    await _update_notification_for_export(
        request=request, program_id=program_id, fs_user_id=fs_user_id, code=NotificationCodes.ALL_DONE
    )


async def _handle_failed_export(request: Request, exception: Exception, program_id: int, fs_user_id: int) -> None:
    export_run = await get_latest_export_run(
        request=request,
        program_id=program_id,
    )
    await update_export_run(
        request=request,
        export_run_id=export_run.id,
        failure_reason=str(exception),
    )
    await _update_notification_for_export(
        request=request, program_id=program_id, fs_user_id=fs_user_id, code=NotificationCodes.ERROR
    )
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
        message=f"Export all program data failed for program_id: {program_id} and user_id: {fs_user_id}",
        dict_={
            "error": str(exception),
            "environment": settings.env,
        },
    )
    raise exception


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_reporting_data_task(
    self: DBTask,
    *,
    export_name: str,
    program_id: int,
    locale: str,
    x_access_token: str,
    output_file_type: ExportOutputFileType,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await export_reporting_data(
            request=request,
            export_name=export_name,
            program_id=program_id,
            locale=locale,
            x_access_token=x_access_token,
            output_file_type=output_file_type,
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_program_data_task(
    self: DBTask,
    *,
    export_name: str,
    program_id: int,
    locale: str,
    output_file_type: ExportOutputFileType,
    include_deleted_fields: bool,
    include_deleted_projects: bool,
    include_reporting_disabled: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await export_program_data(
            request=request,
            export_name=export_name,
            program_id=program_id,
            locale=locale,
            output_file_type=output_file_type,
            include_deleted_fields=include_deleted_fields,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_event_based_program_data_task(
    self: DBTask,
    *,
    export_name: str,
    program_id: int,
    output_file_type: ExportOutputFileType,
    include_deleted_fields: bool,
    include_deleted_projects: bool,
    include_reporting_disabled: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await export_event_based_program_data(
            request=request,
            export_name=export_name,
            program_id=program_id,
            output_file_type=output_file_type,
            include_deleted_fields=include_deleted_fields,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_producer_data_task(
    self: DBTask,
    *,
    export_name: str,
    program_id: int,
    output_file_type: ExportOutputFileType,
    include_deleted_projects: bool,
    include_reporting_disabled: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await export_producer_data(
            request=request,
            export_name=export_name,
            program_id=program_id,
            output_file_type=output_file_type,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_field_overlap_data_task(
    self: DBTask,
    *,
    export_name: str,
    program_id: int,
    output_file_type: ExportOutputFileType,
    include_deleted_projects: bool,
    include_reporting_disabled: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await export_field_overlap_data(
            request=request,
            export_name=export_name,
            program_id=program_id,
            output_file_type=output_file_type,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_zipped_data_task(
    self: DBTask,
    previous_task_outputs: list,
    *,
    export_name: str,
    program_id: int,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        export_zipped_data(export_name=export_name)
        await _handle_succeeded_export(
            request=request, export_name=export_name, program_id=program_id, fs_user_id=fs_user_id
        )
    except Exception as e:
        await _handle_failed_export(request=request, exception=e, program_id=program_id, fs_user_id=fs_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def export_all_program_data_v2_task(
    self: DBTask,
    *,
    program_id: int,
    include_deleted_fields: bool = False,
    include_deleted_projects: bool = False,
    include_reporting_disabled: bool = False,
    output_file_type: ExportOutputFileType = ExportOutputFileType.CSV,
    x_access_token: str = Header(),
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    _, is_new_request = await get_or_create_export_run(
        request=request,
        program_id=program_id,
        query_params={
            "include_deleted_fields": include_deleted_fields,
            "include_deleted_projects": include_deleted_projects,
            "include_reporting_disabled": include_reporting_disabled,
        },
    )
    if not is_new_request:
        return

    await create_notification_for_export(request=request, program_id=program_id)

    program = await get_program(request=request, program_id=program_id)
    program_name = program.name.replace("/", "-")
    export_time = datetime.now(tz=UTC).strftime("%Y-%m-%dT%H:%M:%S")
    export_name = f"{program_id}_{program_name}_{export_time}"
    locale = await get_user_or_program_locale(
        request=request,
        program_id=program_id,
        user_id=fs_user_id,
    )
    export_data_tasks = [
        export_reporting_data_task.s(
            export_name=export_name,
            program_id=program_id,
            locale=locale,
            x_access_token=x_access_token,
            output_file_type=output_file_type,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
        export_program_data_task.s(
            export_name=export_name,
            program_id=program_id,
            locale=locale,
            output_file_type=output_file_type,
            include_deleted_fields=include_deleted_fields,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
        export_producer_data_task.s(
            export_name=export_name,
            program_id=program_id,
            output_file_type=output_file_type,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
        export_field_overlap_data_task.s(
            export_name=export_name,
            program_id=program_id,
            output_file_type=output_file_type,
            include_deleted_projects=include_deleted_projects,
            include_reporting_disabled=include_reporting_disabled,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ]
    if program.program_template == ProgramTemplate.event_based:
        export_data_tasks.append(
            export_event_based_program_data_task.s(
                export_name=export_name,
                program_id=program_id,
                output_file_type=output_file_type,
                include_deleted_fields=include_deleted_fields,
                include_deleted_projects=include_deleted_projects,
                include_reporting_disabled=include_reporting_disabled,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            )
        )
    chain(
        group(export_data_tasks),
        export_zipped_data_task.s(
            export_name=export_name,
            program_id=program_id,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ).delay()
