from __future__ import annotations

from fastapi import Request

from config import get_settings
from notifications.enums import (
    NotificationCodes,
    NotificationEntityTypes,
    NotificationSources,
)
from notifications.schema import Notification, NotificationBody
from notifications.tasks import create_notification
from programs.export import paths

settings = get_settings()


def sort_comma_separated_string(comma_separated_str: str | None, filter_empty_string: bool) -> str:
    """
    Sorts a comma-separated string and returns it as a string.

    Args:
        comma_separated_str (str): A string containing comma-separated values.

    Returns:
        str: A string containing comma-separated values sorted in ascending order.
    """
    if comma_separated_str is not None:
        if filter_empty_string:
            return ",".join([s for s in sorted(comma_separated_str.split(",")) if s != ""])
        return ",".join(sorted(comma_separated_str.split(",")))
    return ""


async def create_notification_for_export(
    request: Request,
    program_id: int,
) -> None:
    """
    Creates a notification specifically for exporting all program data in pending status.
    """
    notification = Notification(
        entity_id=program_id,
        entity=NotificationEntityTypes.Programs,
        user=request.state.fs_user_id,
        body=NotificationBody(
            url=f"{settings.MRV_INTERNAL_URL}{paths.base}{paths.get_export_file}",
            method="GET",
            message="",
            code=NotificationCodes.PROCESSING,
        ),
        source=NotificationSources.MRV_EXPORT,
        program_id=program_id,
    )
    create_notification.delay(
        notification=notification,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_id=request.state.fs_impersonator_user_id,
    )


def convert_table_config_column_to_cube_column(column_name: str) -> str:
    """
    input -> MrvReportingProjects.eligible_area or MrvReportingProjects.eligible_areaAc
    output -> Mrv Reporting Projects Eligible Area or Mrv Reporting Projects Eligible Area Ac
    """
    # replace underscores and dot with spaces
    column_name = column_name.replace("_", " ")
    column_name = column_name.replace(".", " ")
    for char in column_name:
        # update column name to have spaces before each capital letter
        if char.isupper():
            column_name = column_name.replace(char, " " + char)
    # strip new column of leading and trailing spaces
    column_name = column_name.strip()

    # capitalize the first letter of each word after splitting
    return " ".join([word.capitalize() for word in column_name.split()])
