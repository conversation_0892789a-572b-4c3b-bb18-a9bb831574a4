from programs.export.helper import (
    convert_table_config_column_to_cube_column,
    sort_comma_separated_string,
)


def test_sort_comma_separated_string():
    assert (
        sort_comma_separated_string(comma_separated_str="No till,Cover crops", filter_empty_string=True)
        == "Cover crops,No till"
    )
    assert (
        sort_comma_separated_string(comma_separated_str="Cover crops,No till", filter_empty_string=True)
        == "Cover crops,No till"
    )
    assert sort_comma_separated_string(comma_separated_str=",Cover crops", filter_empty_string=True) == "Cover crops"
    assert sort_comma_separated_string(comma_separated_str=",Cover crops", filter_empty_string=False) == ",Cover crops"


def test_convert_table_config_column_to_cube_column():
    assert (
        convert_table_config_column_to_cube_column(column_name="MrvReportingProjects.reenrolled_AreaAc")
        == "Mrv Reporting Projects Reenrolled Area Ac"
    )
    assert (
        convert_table_config_column_to_cube_column(column_name="MrvReportingSummary.contract_signed_at")
        == "Mrv Reporting Summary Contract Signed At"
    )
