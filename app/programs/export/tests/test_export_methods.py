import asyncio
import re
import zipfile
from datetime import datetime
from io import Bytes<PERSON>
from unittest.mock import patch

import pandas as pd
import pytest

from defaults.attribute_options import CropUsage
from fields.enums import FieldStatus
from helper.i18n import Locale
from phases.enums import AttributeTypes, DependencyType, PhaseTypes, StageTypes
from programs.enums import ProgramTemplate, UnitsTypes
from programs.export.constants import ENROLLMENT
from programs.export.methods import (
    create_io_excel_file_with_sheets,
    get_event_based_program_data,
    get_field_overlap_data,
    get_producer_data,
    get_program_data,
    get_program_template_parameters,
    get_reporting_data_for_program,
    get_table_columns_with_prefix_postfix_data_from_table_config,
    substitute_prefix_postfix,
    substitute_template_params,
    substitute_template_params_in_dashboard_chart,
    update_stage_row_data_from_flat_data,
    use_value_after_checking_dependency,
    zip_in_memory_files,
)
from programs.model import Programs
from programs.reporting_dashboards.common.schema import DashboardChartWithDashboard<PERSON>ey
from programs.utils import update_chart_query
from root_crud import update


async def test_zip_in_memory_files():
    file1 = b"File 1 content"
    file2 = b"File 2 content"
    file3 = b"File 3 content"

    result = await zip_in_memory_files(
        file_name_with_file_data=[("file1.txt", file1), ("file2.txt", file2), ("file3.txt", file3)]
    )

    assert isinstance(result, bytes) is True

    in_memory = BytesIO(result)

    with zipfile.ZipFile(in_memory, "r") as zip_file:
        assert set(zip_file.namelist()) == {"file1.txt", "file2.txt", "file3.txt"}

        assert zip_file.read("file1.txt") == b"File 1 content"
        assert zip_file.read("file2.txt") == b"File 2 content"
        assert zip_file.read("file3.txt") == b"File 3 content"


async def test_create_io_excel_file_with_sheets():
    test_data = {
        "Sheet1": [{"Column1": "Data1", "Column2": 10}, {"Column1": "Data2", "Column2": 20}],
        "Sheet2": [{"Column1": "Data3", "Column2": 30}, {"Column1": "Data4", "Column2": 40}],
        "A very long sheet name that exceeds thirty-one characters": [{"Column1": "Data5", "Column2": 50}],
    }

    result = await create_io_excel_file_with_sheets(data_with_title=test_data)

    assert isinstance(result, bytes) is True

    in_memory = BytesIO(result)
    with pd.ExcelFile(in_memory, engine="calamine") as xlfile:
        sheet_names = xlfile.sheet_names
        assert set(sheet_names) == {"Sheet1", "Sheet2", "A very long sheet name that exc"}

        df_sheet1 = pd.read_excel(xlfile, sheet_name="Sheet1")
        pd.testing.assert_frame_equal(df_sheet1, pd.DataFrame(test_data["Sheet1"]))

        df_sheet2 = pd.read_excel(xlfile, sheet_name="Sheet2")
        pd.testing.assert_frame_equal(df_sheet2, pd.DataFrame(test_data["Sheet2"]))
        df_long_name_sheet = pd.read_excel(xlfile, sheet_name="A very long sheet name that exc")
        pd.testing.assert_frame_equal(
            df_long_name_sheet, pd.DataFrame(test_data["A very long sheet name that exceeds thirty-one characters"])
        )


async def test_update_chart_query_with_existing_filters():
    chart_query = {"filters": [{"member": "Existing.filter", "operator": "equals", "values": ["existing_value"]}]}
    program_id = 123000

    expected_result = {
        "filters": [
            {"member": "Existing.filter", "operator": "equals", "values": ["existing_value"]},
            {"member": "MrvPrograms.program_id", "operator": "equals", "values": ["123000"]},
            {"member": "Users.not_deleted", "operator": "equals", "values": ["1"]},
        ]
    }

    result = await update_chart_query(chart_query=chart_query, program_id=program_id)

    assert result == expected_result


async def test_update_chart_query_without_existing_filters():
    chart_query = {}
    program_id = 123

    expected_result = {
        "filters": [
            {"member": "MrvPrograms.program_id", "operator": "equals", "values": ["123"]},
            {"member": "Users.not_deleted", "operator": "equals", "values": ["1"]},
        ]
    }

    result = await update_chart_query(chart_query=chart_query, program_id=program_id)

    assert result == expected_result


async def test_get_reporting_data_for_program(mdl, app_request, httpx_mock, cubejs_responses):
    httpx_mock.add_response(
        url=re.compile(r"http://cubejs\.int\.dev\.regrow\.cloud/.*"),  # NOSONAR
        method="GET",
        json=cubejs_responses["correct"],
        status_code=200,
    )
    # excel_output = {
    #     "File name ¥ (2035)": {0: "2035 Cockrill 16 ¥", 1: "2035 Gibson Rd2 ¥"},
    #     "Region name": {0: "Tennessee", 1: "Ohio"},
    #     "Full name": {0: "Van Blackketter", 1: "Rom Hastings"},
    #     "PracticeChanges.value": {0: "Cover Crops", 1: "Cover Crops"},
    #     "MrvValues.value": {0: "corn", 1: "corn"},
    #     "Total area hectares": {0: 4.654222675, 1: 28.521106205},
    # }
    program = await mdl.Programs(name="test_Program", currency_char="¥", crediting_year=2035, units=UnitsTypes.METRIC)
    dashboard = await mdl.ReportingDashboards(key="ENROLLMENT")
    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    row = await mdl.ReportingDashboardRows(section_id=section.id)
    await mdl.ReportingDashboardCharts(
        row_id=row.id,
        title="Enrollment progress summary",
        query={"foo": "bar"},
        title_map={
            "Kml Files Name": "File name {{PROGRAM_CURRENCY_SYMBOL}} ({{PROGRAM_CREDITING_YEAR}})",
            "Regions Name": "Region name",
            "Users Fullname": "Full name",
            "Practice Changes Value": "Practice change value",
            "Mrv Values Value": "Mrv value",
            "Kml Files Total Area Ac": "Total area {{PROGRAM_AREA_MEASUREMENT}}",
        },
        table_config={
            "column_map": {
                "KmlFiles.name": {
                    "prefix": {"key": "{{PROGRAM_CREDITING_YEAR}}", "default_message": "{{PROGRAM_CREDITING_YEAR}}"},
                    "postfix": {"key": "{{PROGRAM_CURRENCY_SYMBOL}}", "default_message": "{{PROGRAM_CURRENCY_SYMBOL}}"},
                }
            }
        },
    )
    await mdl.ReportingDashboardsToPrograms(program_id=program.id, dashboard=dashboard, disabled=False)

    result = await get_reporting_data_for_program(
        request=app_request, program_id=program.id, x_access_token="foo", locale=Locale.en_US
    )
    assert result == {
        "ENROLLMENT": {
            "Enrollment progress summary": [
                {
                    "File name ¥ (2035)": "2035 Cockrill 16 ¥",
                    "Region name": "Tennessee",
                    "Full name": "Van Blackketter",
                    "PracticeChanges.value": "Cover Crops",
                    "MrvValues.value": "corn",
                    "Total area hectares": 4.654222675,
                },
                {
                    "File name ¥ (2035)": "2035 Gibson Rd2 ¥",
                    "Region name": "Ohio",
                    "Full name": "Rom Hastings",
                    "PracticeChanges.value": "Cover Crops",
                    "MrvValues.value": "corn",
                    "Total area hectares": 28.521106205,
                },
            ]
        }
    }


def test_update_stage_row_data_from_flat_data():
    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        "value1",
        10,
    )

    # test new entry
    data_map = {}
    key_idx_map = {
        "mrv_field_id": 0,
        "stage_id": 1,
        "stage_type": 2,
        "stage_name": 3,
        "stage_custom_name": 4,
        "stage_order": 5,
        "phase_id": 6,
        "phase_type": 7,
        "field_deleted_at": 8,
        "project_deleted_at": 9,
        "reporting_enabled": 10,
        "attribute_id": 11,
        "attribute_type": 12,
        "attribute_name": 13,
        "attribute_custom_name": 14,
        "attribute_order": 15,
        "value": 16,
        "row_id": 17,
    }
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=False,
        key_idx_map=key_idx_map,
    )
    assert "key1" in data_map
    assert data_map["key1"]["mrv_field_id"] == 1
    assert data_map["key1"]["row_data"][0]["value"] == "value1"

    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        "value2",
        2,
    )
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=True,
        key_idx_map=key_idx_map,
    )
    assert data_map["key1"]["row_data"][0]["value"] == "value1,value2"

    # test append row data
    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        "value3",
        3,
    )
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=False,
        key_idx_map=key_idx_map,
    )
    assert len(data_map["key1"]["row_data"]) == 2
    assert data_map["key1"]["row_data"][0]["value"] == "value1,value2"
    assert data_map["key1"]["row_data"][0]["row_id"] == 10
    assert data_map["key1"]["row_data"][1]["value"] == "value3"
    assert data_map["key1"]["row_data"][1]["row_id"] == 3

    # test None concatenate row data
    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        None,
        4,
    )
    data_map["key1"]["row_data"][0]["value"] = ""
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=True,
        key_idx_map=key_idx_map,
    )
    assert data_map["key1"]["row_data"][0]["value"] == ""

    # test None concatenage row data with string value.
    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        None,
        1,
    )
    data_map = {}
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=True,
        key_idx_map=key_idx_map,
    )
    assert data_map["key1"]["row_data"][0]["value"] == ""
    flat_data = (
        1,
        1,
        StageTypes.ASSIGN_PRACTICES,
        "name1",
        "custom_name1",
        1,
        1,
        PhaseTypes.ENROLMENT,
        None,
        None,
        True,
        1,
        AttributeTypes.practice,
        "name1",
        "custom_name1",
        1,
        "value5",
        5,
    )
    update_stage_row_data_from_flat_data(
        stage_row_data_flat=flat_data,
        field_stage_row_data_map=data_map,
        key="key1",
        concatenate_value=True,
        key_idx_map=key_idx_map,
    )
    assert data_map["key1"]["row_data"][0]["value"] == "value5"


async def test_get_program_data(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_2.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100.0)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
        status=FieldStatus.enrolled,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(
        name="assign practice", type_=StageTypes.ASSIGN_PRACTICES, phase_id=phase.id, enabled=True, order=2
    )
    attribute = await mdl.Attribute(
        name="test_attribute", order=1, parent_stage_id=stage.id, enabled=True, type=AttributeTypes.practice
    )
    value1 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value", row_id=0)
    value2 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value2", row_id=1)

    stage2 = await mdl.Stage(
        name="crop type", type_=StageTypes.HISTORICAL_CROP_ROTATION, phase_id=phase.id, enabled=True, order=1
    )
    attribute2 = await mdl.Attribute(
        name="record year", order=0, parent_stage_id=stage2.id, enabled=True, type=AttributeTypes.record_year
    )
    attribute3 = await mdl.Attribute(
        name="crop type", order=1, parent_stage_id=stage2.id, enabled=True, type=AttributeTypes.crop_type
    )
    stage3 = await mdl.Stage(
        name="nutrient management", type_=StageTypes.NUTRIENT_MGMT, phase_id=phase.id, enabled=True, order=3
    )
    attribute4 = await mdl.Attribute(
        name="Product",
        order=1,
        parent_stage_id=stage3.id,
        enabled=True,
        type=AttributeTypes.application_product,
    )
    attribute5 = await mdl.Attribute(
        name="Method",
        order=2,
        parent_stage_id=stage3.id,
        enabled=True,
        type=AttributeTypes.application_method,
    )
    value3 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2023, row_id=0)
    value4 = await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="rice", row_id=0)
    value5 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2022, row_id=1)
    value6 = await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="corn", row_id=1)
    await mdl.Values(field_id=field.id, attribute_id=attribute4.id, value="pots", row_id=0)
    value8 = await mdl.Values(field_id=field.id, attribute_id=attribute5.id, value="Broadcasted", row_id=0)
    await mdl.Values(field_id=field.id, attribute_id=attribute4.id, value="ammnit", row_id=1)
    value10 = await mdl.Values(field_id=field.id, attribute_id=attribute5.id, value="Fertigation", row_id=1)

    # testing happy flow only for now.

    with patch(
        "defaults.defaults.defaults_retriever.get_default_application_product_list",
        return_value=[
            {"name": "Potassium Sulfate", "type": "pots", "n_ratio": 0, "p_ratio": 0, "k_ratio": 44},
            {"name": "ammonium nitrate", "type": "ammnit", "n_ratio": 34, "p_ratio": 0, "k_ratio": 0},
        ],
    ):
        result = await get_program_data(
            request=app_request,
            program_id=program.id,
            locale=Locale.en_US,
            include_deleted_fields=False,
            include_deleted_projects=False,
            include_reporting_disabled=False,
        )

    assert result == {
        ENROLLMENT: {
            "Field Boundaries": [
                (
                    user.email,
                    project.id,
                    f"{user_group_1.id}, {user_group_2.id}",
                    "Group 1, Group 2",
                    group.name,
                    kml_group.name,
                    field.id,
                    field.md5,
                    field.fs_field_id,
                    100.0,
                    field.status,
                    '{"type": "MultiPolygon", "coordinates": [[[[37.020098201368114, -109.072265625], [37.020098201368114, -102.0849609375], [41.04621681452063, -102.0849609375], [41.04621681452063, -109.072265625], [37.020098201368114, -109.072265625]]]]}',
                    "MULTIPOLYGON(((-109.072265625 37.020098201368114,-102.0849609375 37.020098201368114,-102.0849609375 41.04621681452063,-109.072265625 41.04621681452063,-109.072265625 37.020098201368114)))",
                    -105.57861328124999,
                    39.00495903656406,
                    None,
                    None,
                    True,
                )
            ],
            stage2.name: [
                {
                    "user_email": user.email,
                    "project_id": project.id,
                    "group_ids": f"{user_group_1.id}, {user_group_2.id}",
                    "group_names": "Group 1, Group 2",
                    "mrv_field_id": field.id,
                    "field_md5": field.md5,
                    "fs_field_id": field.fs_field_id,
                    "field_area_ha": 100.0,
                    "field_status": field.status,
                    "field_name": kml_group.name,
                    "farm_name": group.name,
                    "state_name": region.name,
                    "field_deleted_at": None,
                    "project_deleted_at": None,
                    "reporting_enabled": True,
                    attribute2.name: f"{value3.value}",
                    attribute3.name: value4.value,
                },
                {
                    "user_email": user.email,
                    "project_id": project.id,
                    "group_ids": f"{user_group_1.id}, {user_group_2.id}",
                    "group_names": "Group 1, Group 2",
                    "mrv_field_id": field.id,
                    "field_md5": field.md5,
                    "fs_field_id": field.fs_field_id,
                    "field_area_ha": 100.0,
                    "field_status": field.status,
                    "field_name": kml_group.name,
                    "farm_name": group.name,
                    "state_name": region.name,
                    "field_deleted_at": None,
                    "project_deleted_at": None,
                    "reporting_enabled": True,
                    attribute2.name: f"{value5.value}",
                    attribute3.name: value6.value,
                },
            ],
            stage.name: [
                {
                    "user_email": user.email,
                    "project_id": project.id,
                    "group_ids": f"{user_group_1.id}, {user_group_2.id}",
                    "group_names": "Group 1, Group 2",
                    "mrv_field_id": field.id,
                    "field_md5": field.md5,
                    "fs_field_id": field.fs_field_id,
                    "field_area_ha": 100.0,
                    "field_status": field.status,
                    "field_name": kml_group.name,
                    "farm_name": group.name,
                    "state_name": region.name,
                    "field_deleted_at": None,
                    "project_deleted_at": None,
                    "reporting_enabled": True,
                    attribute.name: f"{value1.value},{value2.value}",
                }
            ],
            stage3.name: [
                {
                    "user_email": user.email,
                    "project_id": project.id,
                    "group_ids": f"{user_group_1.id}, {user_group_2.id}",
                    "group_names": "Group 1, Group 2",
                    "mrv_field_id": field.id,
                    "field_md5": field.md5,
                    "fs_field_id": field.fs_field_id,
                    "field_area_ha": 100.0,
                    "field_status": field.status,
                    "field_name": kml_group.name,
                    "farm_name": group.name,
                    "state_name": region.name,
                    "field_deleted_at": None,
                    "project_deleted_at": None,
                    "reporting_enabled": True,
                    attribute5.name: value8.value,
                    attribute4.name: "Potassium Sulfate",
                    "N ratio": 0,
                    "P ratio": 0,
                    "K ratio": 44,
                },
                {
                    "user_email": user.email,
                    "project_id": project.id,
                    "group_ids": f"{user_group_1.id}, {user_group_2.id}",
                    "group_names": "Group 1, Group 2",
                    "mrv_field_id": field.id,
                    "field_md5": field.md5,
                    "fs_field_id": field.fs_field_id,
                    "field_area_ha": 100.0,
                    "field_status": field.status,
                    "field_name": kml_group.name,
                    "farm_name": group.name,
                    "state_name": region.name,
                    "field_deleted_at": None,
                    "project_deleted_at": None,
                    "reporting_enabled": True,
                    attribute5.name: value10.value,
                    attribute4.name: "ammonium nitrate",
                    "N ratio": 34,
                    "P ratio": 0,
                    "K ratio": 0,
                },
            ],
        }
    }


@patch("programs.export.methods.get_program_events")
async def test_get_event_based_program_data(mock_get_program_events, mdl, app_request):
    program = await mdl.Programs(name="test_program", program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100.0)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
        status=FieldStatus.enrolled,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(name="CROP_EVENTS", type_=StageTypes.CROP_EVENTS, phase_id=phase.id, enabled=True)

    crop_event_data = {
        "MRV Field ID": field.id,
        "Crop Year": "Corn Jun 2024 - Jun 2025",
        "Crop Type": "corn",
        "Crop Usage": CropUsage.COMMODITY,
        "Planting Date": datetime(2025, 1, 1).date(),
        "Harvest Date": datetime(2025, 6, 1).date(),
        "Termination method": None,
        "Crop Yield": 1,
        "Yield Unit": "bu/ac",
        "Residue Harvested": None,
    }
    mock_get_program_events.return_value = {"ENROLLMENT": {stage.type_: [crop_event_data]}}

    res = await get_event_based_program_data(
        request=app_request,
        program_id=program.id,
        include_deleted_fields=False,
        include_deleted_projects=False,
        include_reporting_disabled=False,
    )
    expected_res = {
        "ENROLLMENT": {
            stage.type_: [
                {
                    "User Email": user.email,
                    "First Name": user.name,
                    "Last Name": user.surname,
                    "Producer ID": project.id,
                    "Group IDs": None,
                    "Group Names": None,
                    "MRV Field ID": field.id,
                    "Field MD5": field.md5,
                    "Field Area (ha)": field.area,
                    "Field Name": kml_group.name,
                    "Farm Name": group.name,
                    "State Name": region.name,
                    "Field Status": field.status,
                }
                | crop_event_data
            ]
        }
    }
    assert res == expected_res


async def test_get_producer_data(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project1 = await mdl.Projects(program_id=program.id)
    user1 = await mdl.Users(
        email="<EMAIL>",
        phone="1234567890",
        settings={"company": {"state": "test1", "country": "country_test1"}, "phone": "123"},
    )
    await mdl.ProjectPermissions(
        user=user1.id,
        project=project1.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_2.id)

    project2 = await mdl.Projects(program_id=program.id)
    user2 = await mdl.Users(
        email="<EMAIL>",
        phone="*********",
        settings={"company": {"state": "test2", "country": "country_test2"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user2.id,
        project=project2.id,
    )
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_2.id)

    project3 = await mdl.Projects(program_id=program.id)
    user3 = await mdl.Users(
        email="<EMAIL>",
        phone=None,
        settings={"company": {"state": "test3", "country": "country_test3"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user3.id,
        project=project3.id,
    )
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_2.id)

    project4 = await mdl.Projects(program_id=program.id)
    user4 = await mdl.Users(
        email="<EMAIL>",
        phone="",
        settings={"company": {"state": "test4", "country": "country_test4"}, "phone": "77777777777"},
    )
    await mdl.ProjectPermissions(
        user=user4.id,
        project=project4.id,
    )
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_2.id)

    result = await get_producer_data(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_reporting_disabled=False,
    )
    assert result == {
        "Producer Data": [
            (
                user1.id,
                project1.id,
                f"{user_group_1.id}, {user_group_2.id}",
                "Group 1, Group 2",
                user1.name,
                user1.surname,
                user1.email,
                user1.settings["company"]["country"],
                user1.settings["company"]["state"],
                user1.phone,
                None,
                True,
            ),
            (
                user2.id,
                project2.id,
                f"{user_group_1.id}, {user_group_2.id}",
                "Group 1, Group 2",
                user2.name,
                user2.surname,
                user2.email,
                user2.settings["company"]["country"],
                user2.settings["company"]["state"],
                user2.settings["phone"],
                None,
                True,
            ),
            (
                user3.id,
                project3.id,
                f"{user_group_1.id}, {user_group_2.id}",
                "Group 1, Group 2",
                user3.name,
                user3.surname,
                user3.email,
                user3.settings["company"]["country"],
                user3.settings["company"]["state"],
                user3.settings["phone"],
                None,
                True,
            ),
            (
                user4.id,
                project4.id,
                f"{user_group_1.id}, {user_group_2.id}",
                "Group 1, Group 2",
                user4.name,
                user4.surname,
                user4.email,
                user4.settings["company"]["country"],
                user4.settings["company"]["state"],
                user4.settings["phone"],
                None,
                True,
            ),
        ]
    }


async def test_get_field_overlap_data(app_request):
    data = [{"field_id": 1, "field_name": "test_field"}]
    # I'm mocking get_program_field_overlaps because it's tested properly in test_get_program_field_overlaps
    with patch("programs.export.methods.get_program_field_overlaps", return_value=data):
        result = await get_field_overlap_data(app_request, 1, True, True)
    assert result == {"Program Field Overlaps": data}


@pytest.mark.parametrize(
    "row_data,dependent_attribute, expected_out",
    (
        (
            {
                1: "2021",
                2: "corn",
                3: "cover",
                4: "10",
            },
            2,
            False,
        ),
        (
            {
                1: "2020",
                2: "corn",
                3: "cover",
                4: "10",
            },
            3,
            True,
        ),
        (
            {
                1: "2022",
                2: "corn",
                3: "cover",
                4: "10",
            },
            4,
            True,  # Whenever the dependency start having `or` and `and` conditions, it will be True or False based on the conditions.
        ),
        (
            {
                1: "2022",
                2: "corn",
                3: "commodity",
                4: "15",
            },
            4,
            True,
        ),
    ),
)
async def test_use_value_after_checking_dependency(
    row_data: dict[int, str], dependent_attribute: int, expected_out: bool
):
    dependency_map = {
        2: {1: {DependencyType.ENABLE: [], DependencyType.DISABLE: ["2021"]}},
        3: {2: {DependencyType.ENABLE: ["corn"], DependencyType.DISABLE: []}},
        4: {
            1: {DependencyType.ENABLE: [], DependencyType.DISABLE: ["2021"]},
            2: {DependencyType.ENABLE: ["corn"], DependencyType.DISABLE: []},
            3: {DependencyType.ENABLE: [], DependencyType.DISABLE: ["cover"]},
        },
    }
    assert (
        await use_value_after_checking_dependency(
            row_data_attribute_id_value_map=row_data,
            dependency_map=dependency_map,
            dependent_attribute=dependent_attribute,
        )
        is expected_out
    )


async def test_get_program_data_with_dependency(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_2.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
        status=FieldStatus.enrolled,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(
        name="assign practice", type_=StageTypes.ASSIGN_PRACTICES, phase_id=phase.id, enabled=True, order=2
    )
    attribute = await mdl.Attribute(
        name="test_attribute", order=1, parent_stage_id=stage.id, enabled=True, type=AttributeTypes.practice
    )
    value1 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value", row_id=0)
    value2 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value2", row_id=1)

    stage2 = await mdl.Stage(
        name="crop type", type_=StageTypes.HISTORICAL_CROP_ROTATION, phase_id=phase.id, enabled=True, order=1
    )
    attribute2 = await mdl.Attribute(
        name="record year", order=0, parent_stage_id=stage2.id, enabled=True, type=AttributeTypes.record_year
    )
    attribute3 = await mdl.Attribute(
        name="crop type",
        order=1,
        parent_stage_id=stage2.id,
        enabled=True,
        type=AttributeTypes.crop_type,
        dependencies=[{"id": attribute2.id, "value": "2023", "operator": "is_not"}],
    )
    value3 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2023, row_id=0)
    # will be excluded by dependency
    await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="rice", row_id=0)
    value5 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2022, row_id=1)
    value6 = await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="corn", row_id=1)

    # testing happy flow only for now.

    program_data = await get_program_data(
        request=app_request,
        program_id=program.id,
        locale=Locale.en_US,
        include_deleted_fields=False,
        include_deleted_projects=False,
        include_reporting_disabled=False,
    )

    create_excel_tasks = []
    all_file_names = []
    for phase_type, data_with_title in program_data.items():
        create_excel_tasks.append(create_io_excel_file_with_sheets(data_with_title=data_with_title))
        all_file_names.append(f"{program.id}_{phase_type}_Program_data.xlsx")

    file_output = await asyncio.gather(*create_excel_tasks)
    result: list[tuple[str, bytes]] = []
    for idx, data in enumerate(file_output):
        result.append((all_file_names[idx], data))

    assert len(result) == 1
    assert result[0][0] == f"{program.id}_{ENROLLMENT}_Program_data.xlsx"
    result_body = result[0][1]
    assert isinstance(result_body, bytes) is True

    excel_output1 = {
        "user_email": {0: user.email},
        "project_id": {0: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2"},
        "farm_name": {0: group.name},
        "field_name": {0: kml_group.name},
        "mrv_field_id": {0: field.id},
        "field_md5": {0: field.md5},
        "fs_field_id": {0: field.fs_field_id},
        "field_area_ha": {0: float(field.area)},
        "field_status": {0: field.status},
        "geojson": {
            0: '{"type": "MultiPolygon", "coordinates": [[[[37.020098201368114, -109.072265625], [37.020098201368114, -102.0849609375], [41.04621681452063, -102.0849609375], [41.04621681452063, -109.072265625], [37.020098201368114, -109.072265625]]]]}'
        },
        "geometry_wkt": {
            0: "MULTIPOLYGON(((-109.072265625 37.020098201368114,-102.0849609375 37.020098201368114,-102.0849609375 41.04621681452063,-109.072265625 41.04621681452063,-109.072265625 37.020098201368114)))"
        },
        "centroid_lon": {0: -105.57861328124999},
        "centroid_lat": {0: 39.00495903656406},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True},
    }
    excel_output2 = {
        "user_email": {0: user.email, 1: user.email},
        "project_id": {0: project.id, 1: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}", 1: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2", 1: "Group 1, Group 2"},
        "mrv_field_id": {0: field.id, 1: field.id},
        "field_md5": {0: field.md5, 1: field.md5},
        "fs_field_id": {0: field.fs_field_id, 1: field.fs_field_id},
        "field_area_ha": {0: float(field.area), 1: float(field.area)},
        "field_status": {0: field.status, 1: field.status},
        "field_name": {0: kml_group.name, 1: kml_group.name},
        "farm_name": {0: group.name, 1: group.name},
        "state_name": {0: region.name, 1: region.name},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True, 1: True},
        "record year": {0: value3.value, 1: value5.value},
        "crop type": {0: None, 1: value6.value},
    }
    excel_output3 = {
        "user_email": {0: user.email},
        "project_id": {0: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2"},
        "mrv_field_id": {0: field.id},
        "field_md5": {0: field.md5},
        "fs_field_id": {0: field.fs_field_id},
        "field_area_ha": {0: float(field.area)},
        "field_status": {0: field.status},
        "field_name": {0: kml_group.name},
        "farm_name": {0: group.name},
        "state_name": {0: region.name},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True},
        "test_attribute": {0: f"{value1.value},{value2.value}"},
    }

    with pd.ExcelFile(BytesIO(result_body), engine="calamine") as xlfile:
        sheet_names = xlfile.sheet_names
        assert sorted(sheet_names) == sorted(["Field Boundaries", "crop type", "assign practice"])

        df_sheet1 = pd.read_excel(xlfile, sheet_name="Field Boundaries")
        pd.testing.assert_frame_equal(df_sheet1, pd.DataFrame(excel_output1))

        df_sheet2 = pd.read_excel(xlfile, sheet_name="crop type")
        pd.testing.assert_frame_equal(df_sheet2, pd.DataFrame(excel_output2))

        df_sheet3 = pd.read_excel(xlfile, sheet_name="assign practice")
        pd.testing.assert_frame_equal(df_sheet3, pd.DataFrame(excel_output3))


async def test_get_program_template_parameters(mdl, app_request):
    prog1 = await mdl.Programs(currency_char="£", crediting_year=2026, units=UnitsTypes.US_IMPERIAL)
    params, prog = await get_program_template_parameters(app_request, prog1.id)
    assert params == {
        "PROGRAM_CURRENCY_SYMBOL": "£",
        "PROGRAM_CREDITING_YEAR": "2026",
        "PROGRAM_PREVIOUS_YEAR": "2025",
        "PROGRAM_AREA_MEASUREMENT": "acres",
        "PROGRAM_AREA_MEASUREMENT_SYMBOL": "ac",
    }
    assert prog.id == prog1.id
    prog2 = await mdl.Programs(currency_char=None, crediting_year=2030, units=UnitsTypes.METRIC)
    # Setting currency_char = None in constructor makes it use default of "$", so we need to save it again to get
    # an actual None value.
    prog2.currency_char = None
    await update.update(request=app_request, instances=[prog2], type=Programs)
    # Check NULL value for currency_char doesn't break anything
    params, prog = await get_program_template_parameters(app_request, prog2.id)
    assert params == {
        "PROGRAM_CURRENCY_SYMBOL": None,
        "PROGRAM_CREDITING_YEAR": "2030",
        "PROGRAM_PREVIOUS_YEAR": "2029",
        "PROGRAM_AREA_MEASUREMENT": "hectares",
        "PROGRAM_AREA_MEASUREMENT_SYMBOL": "ha",
    }
    assert prog.id == prog2.id


def test_substitute_template_params():
    # Check that a template variable can be repeated, i.e. {{name}}
    template = "I'm {{name}} and I'm {{age}} years old. Did I mention that I'm {{name}}?"
    params = {"name": "John", "age": "30", "height": "170cm"}
    assert substitute_template_params(template, params) == "I'm John and I'm 30 years old. Did I mention that I'm John?"

    # Check that single curly braces don't get substituted
    template = "{name} is {age}"
    assert substitute_template_params(template, params) == "{name} is {age}"

    # Check that missing variables or NULL values aren't replaced
    template = "All {{thing}} are {{color}}"
    params = {"object": "apples", "color": None}
    assert substitute_template_params(template, params) == "All {{thing}} are {{color}}"


def test_substitute_template_params_in_dashboard_chart():
    params = {
        "first_name": "Charlotte",
        "last_name": "Brontë",
        "low_age": "20",
        "high_age": "30",
    }
    chart_data = DashboardChartWithDashboardKey(
        title="{{first_name}} {{last_name}} Profile",
        title_map={
            "age_range": "{{low_age}}-{{high_age}}",
            "height": "{{height}}cm",
        },
        chart_query={"foo": "{{last_name}}"},
        dashboard_key="{{first_name}}",
    )
    substitute_template_params_in_dashboard_chart(chart_data, params)
    assert chart_data == DashboardChartWithDashboardKey(
        title="Charlotte Brontë Profile",
        title_map={
            "age_range": "20-30",
            # "height" was never defined in params
            "height": "{{height}}cm",
        },
        # The following aren't subsituted by this function
        chart_query={"foo": "{{last_name}}"},
        dashboard_key="{{first_name}}",
    )


def test_substitute_prefix_postfix():
    assert substitute_prefix_postfix(
        prefix={
            "key": "{{title}}",
            "default_message": "{{surname}}",
        },
        postfix={"key": "{{last_name}}", "default_message": ""},
        template_params={"title": "Ms", "surname": "Miss", "last_name": "smith"},
    ) == (
        {
            "key": "Ms",
            "default_message": "Miss",
        },
        {"key": "smith", "default_message": ""},
    )


def test_get_table_columns_with_prefix_postfix_data_from_table_config():
    table_config = {
        "hidden_columns": None,
        "column_map": {
            "MrvReportingSummary.tons": {
                "format": "number",
                "prefix": None,
                "postfix": {"key": "tCO2e", "default_message": "tCO2e"},
            },
            "MrvReportingSummary.dollars": {
                "format": "number",
                "prefix": {"key": "{{PROGRAM_CURRENCY_SYMBOL}}", "default_message": "{{PROGRAM_CURRENCY_SYMBOL}}"},
                "postfix": None,
            },
            "MrvReportingSummary.fm_last_sync": {"format": "date", "prefix": None, "postfix": None},
            "MrvReportingProjects.total_AreaAc": {
                "format": "number",
                "prefix": None,
                "postfix": {
                    "key": "{{PROGRAM_AREA_MEASUREMENT_SYMBOL}}",
                    "default_message": "{{PROGRAM_AREA_MEASUREMENT_SYMBOL}}",
                },
            },
        },
    }
    template_params = {
        "PROGRAM_CURRENCY_SYMBOL": "$",
        "PROGRAM_AREA_MEASUREMENT_SYMBOL": "ha",
    }

    assert get_table_columns_with_prefix_postfix_data_from_table_config(table_config, template_params) == {
        "MrvReportingSummary.tons": {"prefix": {}, "postfix": {"key": "tCO2e", "default_message": "tCO2e"}},
        "MrvReportingSummary.dollars": {"prefix": {"key": "$", "default_message": "$"}, "postfix": {}},
        "MrvReportingSummary.fm_last_sync": {"prefix": {}, "postfix": {}},
        "MrvReportingProjects.total_AreaAc": {"prefix": {}, "postfix": {"key": "ha", "default_message": "ha"}},
    }
