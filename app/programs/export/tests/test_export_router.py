import re
import zipfile
from io import BytesIO

import pandas as pd
from httpx import AsyncClient

import programs.export.router
from fields.enums import FieldStatus
from gcloud_client.storage.mocks import mocked_create_presigned_url
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs import paths as program_paths
from programs.export import paths
from programs.export.constants import ENROLLMENT


async def test_get_reporting_data_api(mdl, httpx_mock, cubejs_responses, async_client: AsyncClient):
    httpx_mock.add_response(
        url=re.compile(r"http://cubejs\.int\.dev\.regrow\.cloud/.*"),  # NOSONAR
        method="GET",
        json=cubejs_responses["correct"],
        status_code=200,
    )
    excel_output = {
        "File name": {0: "Cockrill 16", 1: "Gibson Rd2"},
        "Region name": {0: "Tennessee", 1: "Ohio"},
        "Full name": {0: "Van Blackketter", 1: "Rom Hastings"},
        "PracticeChanges.value": {0: "Cover Crops", 1: "Cover Crops"},
        "MrvValues.value": {0: "corn", 1: "corn"},
        "Total area": {0: 4.654222675, 1: 28.521106205},
    }
    program = await mdl.Programs(name="test_Program")
    dashboard = await mdl.ReportingDashboards(key="ENROLLMENT")
    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    row = await mdl.ReportingDashboardRows(section_id=section.id)
    await mdl.ReportingDashboardCharts(
        row_id=row.id,
        title="Enrollment progress summary",
        query={"foo": "bar"},
        title_map={
            "Kml Files Name": "File name",
            "Regions Name": "Region name",
            "Users Fullname": "Full name",
            "Practice Changes Value": "Practice change value",
            "Mrv Values Value": "Mrv value",
            "Kml Files Total Area Ac": "Total area",
        },
    )
    await mdl.ReportingDashboardsToPrograms(program_id=program.id, dashboard=dashboard, disabled=False)
    response = await async_client.get(
        f"{program_paths.base}/{program.id}{paths.base}{paths.reporting_data_export}", headers={"X-Access-Token": "foo"}
    )
    result = response.content
    assert isinstance(result, bytes) is True
    assert (
        response.headers["Content-Disposition"]
        == f"attachment; filename*=utf-8''{program.id}_test_Program_reporting_data.zip"
    )

    in_memory = BytesIO(result)

    with zipfile.ZipFile(in_memory, "r") as zip_file:
        file_name_to_check = f"{program.id}_{dashboard.key}_Report_data.xlsx"
        assert zip_file.namelist() == [file_name_to_check]

        with pd.ExcelFile(BytesIO(zip_file.read(file_name_to_check)), engine="calamine") as xlfile:
            sheet_names = xlfile.sheet_names
            assert sheet_names == ["Enrollment progress summary"]
            df_sheet1 = pd.read_excel(xlfile, sheet_name="Enrollment progress summary")
            pd.testing.assert_frame_equal(df_sheet1, pd.DataFrame(excel_output))

    # test 404
    response = await async_client.get(
        f"{program_paths.base}/-1{paths.base}{paths.reporting_data_export}", headers={"X-Access-Token": "foo"}
    )
    assert response.status_code == 404
    assert response.json()["detail"] == {"message": "Program - -1 not found"}


async def test_get_program_data_api(mdl, async_client: AsyncClient):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_2.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
        status=FieldStatus.enrolled,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(
        name="assign practice", type_=StageTypes.ASSIGN_PRACTICES, phase_id=phase.id, enabled=True, order=2
    )
    attribute = await mdl.Attribute(
        name="test_attribute", order=1, parent_stage_id=stage.id, enabled=True, type=AttributeTypes.practice
    )
    value1 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value", row_id=0)
    value2 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value2", row_id=1)

    stage2 = await mdl.Stage(
        name="crop type", type_=StageTypes.HISTORICAL_CROP_ROTATION, phase_id=phase.id, enabled=True, order=1
    )
    attribute2 = await mdl.Attribute(
        name="record year", order=0, parent_stage_id=stage2.id, enabled=True, type=AttributeTypes.record_year
    )
    attribute3 = await mdl.Attribute(
        name="crop type", order=1, parent_stage_id=stage2.id, enabled=True, type=AttributeTypes.crop_type
    )
    value3 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2023, row_id=0)
    value4 = await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="rice", row_id=0)
    value5 = await mdl.Values(field_id=field.id, attribute_id=attribute2.id, value=2022, row_id=1)
    value6 = await mdl.Values(field_id=field.id, attribute_id=attribute3.id, value="corn", row_id=1)

    # testing happy flow only for now.
    response = await async_client.get(
        f"{program_paths.base}/{program.id}{paths.base}{paths.program_data_export}", headers={"X-Access-Token": "foo"}
    )
    result = response.content

    assert isinstance(result, bytes) is True
    assert (
        response.headers["Content-Disposition"]
        == f"attachment; filename*=utf-8''{program.id}_test_program_program_data.zip"
    )

    excel_output1 = {
        "user_email": {0: user.email},
        "project_id": {0: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2"},
        "farm_name": {0: group.name},
        "field_name": {0: kml_group.name},
        "mrv_field_id": {0: field.id},
        "field_md5": {0: field.md5},
        "fs_field_id": {0: field.fs_field_id},
        "field_area_ha": {0: float(field.area)},
        "field_status": {0: field.status},
        "geojson": {
            0: '{"type": "MultiPolygon", "coordinates": [[[[37.020098201368114, -109.072265625], [37.020098201368114, -102.0849609375], [41.04621681452063, -102.0849609375], [41.04621681452063, -109.072265625], [37.020098201368114, -109.072265625]]]]}'
        },
        "geometry_wkt": {
            0: "MULTIPOLYGON(((-109.072265625 37.020098201368114,-102.0849609375 37.020098201368114,-102.0849609375 41.04621681452063,-109.072265625 41.04621681452063,-109.072265625 37.020098201368114)))"
        },
        "centroid_lon": {0: -105.57861328124999},
        "centroid_lat": {0: 39.00495903656406},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True},
    }
    excel_output2 = {
        "user_email": {0: user.email, 1: user.email},
        "project_id": {0: project.id, 1: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}", 1: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2", 1: "Group 1, Group 2"},
        "mrv_field_id": {0: field.id, 1: field.id},
        "field_md5": {0: field.md5, 1: field.md5},
        "fs_field_id": {0: field.fs_field_id, 1: field.fs_field_id},
        "field_area_ha": {0: float(field.area), 1: float(field.area)},
        "field_status": {0: field.status, 1: field.status},
        "field_name": {0: kml_group.name, 1: kml_group.name},
        "farm_name": {0: group.name, 1: group.name},
        "state_name": {0: region.name, 1: region.name},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True, 1: True},
        "record year": {0: value3.value, 1: value5.value},
        "crop type": {0: value4.value, 1: value6.value},
    }

    excel_output3 = {
        "user_email": {0: user.email},
        "project_id": {0: project.id},
        "group_ids": {0: f"{user_group_1.id}, {user_group_2.id}"},
        "group_names": {0: "Group 1, Group 2"},
        "mrv_field_id": {0: field.id},
        "field_md5": {0: field.md5},
        "fs_field_id": {0: field.fs_field_id},
        "field_area_ha": {0: float(field.area)},
        "field_status": {0: field.status},
        "field_name": {0: kml_group.name},
        "farm_name": {0: group.name},
        "state_name": {0: region.name},
        "field_deleted_at": {},
        "project_deleted_at": {},
        "reporting_enabled": {0: True},
        "test_attribute": {0: f"{value1.value},{value2.value}"},
    }

    in_memory = BytesIO(result)
    with zipfile.ZipFile(in_memory, "r") as zip_file:
        file_name_to_check = f"{program.id}_{ENROLLMENT}_Program_data.xlsx"
        assert zip_file.namelist() == [file_name_to_check]

        with pd.ExcelFile(BytesIO(zip_file.read(file_name_to_check)), engine="calamine") as xlfile:
            sheet_names = xlfile.sheet_names
            assert sorted(sheet_names) == sorted(["Field Boundaries", "crop type", "assign practice"])

            df_sheet1 = pd.read_excel(xlfile, sheet_name="Field Boundaries")
            pd.testing.assert_frame_equal(df_sheet1, pd.DataFrame(excel_output1))

            df_sheet2 = pd.read_excel(xlfile, sheet_name="crop type")
            pd.testing.assert_frame_equal(df_sheet2, pd.DataFrame(excel_output2))

            df_sheet3 = pd.read_excel(xlfile, sheet_name="assign practice")
            pd.testing.assert_frame_equal(df_sheet3, pd.DataFrame(excel_output3))

    # test 404
    response = await async_client.get(
        f"{program_paths.base}/-1{paths.base}{paths.program_data_export}", headers={"X-Access-Token": "foo"}
    )
    assert response.status_code == 404
    assert response.json()["detail"] == {"message": "Program - -1 not found"}


async def test_get_producer_data_api(mdl, app_request, async_client: AsyncClient):
    program = await mdl.Programs(name="test_program")
    project1 = await mdl.Projects(program_id=program.id)
    user1 = await mdl.Users(
        email="<EMAIL>",
        phone="1234567890",
        settings={"company": {"state": "test1", "country": "country_test1"}, "phone": "123"},
    )
    await mdl.ProjectPermissions(
        user=user1.id,
        project=project1.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_2.id)

    project2 = await mdl.Projects(program_id=program.id)
    user2 = await mdl.Users(
        email="<EMAIL>",
        phone="*********",
        settings={"company": {"state": "test2", "country": "country_test2"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user2.id,
        project=project2.id,
    )
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_2.id)

    project3 = await mdl.Projects(program_id=program.id)
    user3 = await mdl.Users(
        email="<EMAIL>",
        phone=None,
        settings={"company": {"state": "test3", "country": "country_test3"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user3.id,
        project=project3.id,
    )
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_2.id)

    project4 = await mdl.Projects(program_id=program.id)
    user4 = await mdl.Users(
        email="<EMAIL>",
        phone="",
        settings={"company": {"state": "test4", "country": "country_test4"}, "phone": "77777777777"},
    )
    await mdl.ProjectPermissions(
        user=user4.id,
        project=project4.id,
    )
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_2.id)

    response = await async_client.get(
        f"{program_paths.base}/{program.id}{paths.base}{paths.producer_data_export}", headers={"X-Access-Token": "foo"}
    )
    result = response.content

    assert isinstance(result, bytes) is True
    assert (
        response.headers["Content-Disposition"]
        == f"attachment; filename*=utf-8''{program.id}_test_program_producer_data.zip"
    )

    in_memory = BytesIO(result)
    excel_output = {
        "user_id": {0: user1.id, 1: user2.id, 2: user3.id, 3: user4.id},
        "project_id": {0: project1.id, 1: project2.id, 2: project3.id, 3: project4.id},
        "group_ids": {
            0: f"{user_group_1.id}, {user_group_2.id}",
            1: f"{user_group_1.id}, {user_group_2.id}",
            2: f"{user_group_1.id}, {user_group_2.id}",
            3: f"{user_group_1.id}, {user_group_2.id}",
        },
        "group_names": {0: "Group 1, Group 2", 1: "Group 1, Group 2", 2: "Group 1, Group 2", 3: "Group 1, Group 2"},
        "user_first_name": {0: user1.name, 1: user2.name, 2: user3.name, 3: user4.name},
        "user_last_name": {0: user1.surname, 1: user2.surname, 2: user3.surname, 3: user4.surname},
        "user_email": {0: user1.email, 1: user2.email, 2: user3.email, 3: user4.email},
        "user_country": {
            0: user1.settings["company"]["country"],
            1: user2.settings["company"]["country"],
            2: user3.settings["company"]["country"],
            3: user4.settings["company"]["country"],
        },
        "user_region": {
            0: user1.settings["company"]["state"],
            1: user2.settings["company"]["state"],
            2: user3.settings["company"]["state"],
            3: user4.settings["company"]["state"],
        },
        "user_phone_number": {0: 1234567890, 1: *********, 2: *********, 3: 77777777777},
        "project_deleted_at": {},
        "reporting_enabled": {0: True, 1: True, 2: True, 3: True},
    }

    with zipfile.ZipFile(in_memory, "r") as zip_file:
        file_name_to_check = f"{program.id}_Producer_data.xlsx"
        assert zip_file.namelist() == [file_name_to_check]

        with pd.ExcelFile(BytesIO(zip_file.read(file_name_to_check)), engine="calamine") as xlfile:
            sheet_names = xlfile.sheet_names
            assert sheet_names == ["Producer Data"]

            df_sheet1 = pd.read_excel(xlfile, sheet_name="Producer Data")
            pd.testing.assert_frame_equal(df_sheet1, pd.DataFrame(excel_output), check_dtype=False)

    # test 404
    response = await async_client.get(
        f"{program_paths.base}/-1{paths.base}{paths.producer_data_export}", headers={"X-Access-Token": "foo"}
    )
    assert response.status_code == 404
    assert response.json()["detail"] == {"message": "Program - -1 not found"}


async def test_all_data_export_api_404(async_client: AsyncClient):
    # test 404 for all export
    response = await async_client.get(
        f"{program_paths.base}/-1{paths.base}{paths.all_data_export}", headers={"X-Access-Token": "foo"}
    )
    assert response.status_code == 404
    assert response.json()["detail"] == {"message": "Program - -1 not found"}


async def test_get_export_file(monkeypatch, async_client: AsyncClient, mdl):
    program = await mdl.Programs(name="test_program")
    await mdl.ProgramExportRuns(
        program_id=program.id,
        export_run_status="completed",
        gcs_object="test_gcs_object",
        user=13,
        query_params={},
    )
    monkeypatch.setattr(
        programs.export.router,
        "create_presigned_url",
        mocked_create_presigned_url,
    )
    response = await async_client.get(f"{program_paths.base}/{program.id}{paths.base}{paths.get_export_file}")
    assert response.status_code == 200
    assert response.json() == {
        "file_name": "test_gcs_object",
        "download_url": "https://storage.googleapis.com/flurosense-australia-southeast1-mrv-service-dev/program_images%2Flogo_image_c0d62ce420344592bb0395f7d18bab82.jpeg?bla=bla",
        "file_size": 128,
    }
