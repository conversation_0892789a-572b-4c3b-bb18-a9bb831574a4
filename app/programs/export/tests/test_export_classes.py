from programs.enums import UnitsTypes
from programs.export.classes import RemapChartQuery
from programs.export.constants import (
    ACRES_FRAGMENT,
    HECTARES_FRAGMENT,
    TOTAL_COMPLETION_PREFIX,
)


async def test_remap_chart_query_init(mdl):
    prog1 = await mdl.Programs(id=68, units=UnitsTypes.US_IMPERIAL)
    remap = RemapChartQuery(prog1)
    assert remap.find_unit == HECTARES_FRAGMENT
    assert remap.replace_unit == ACRES_FRAGMENT
    assert remap.replace_completion == f"{TOTAL_COMPLETION_PREFIX}_68"

    prog2 = await mdl.Programs(id=155, units=UnitsTypes.METRIC)
    remap = RemapChartQuery(prog2)
    assert remap.find_unit == ACRES_FRAGMENT
    assert remap.replace_unit == HECTARES_FRAGMENT
    assert remap.replace_completion == f"{TOTAL_COMPLETION_PREFIX}_155"

    prog3 = await mdl.Programs(id=1, units=UnitsTypes.US_IMPERIAL)
    remap = RemapChartQuery(prog3)
    assert remap.find_unit == HECTARES_FRAGMENT
    assert remap.replace_unit == ACRES_FRAGMENT
    assert remap.replace_completion == TOTAL_COMPLETION_PREFIX


async def test_remap_chart_query_replace(mdl):
    prog = await mdl.Programs(id=68, units=UnitsTypes.US_IMPERIAL)
    remap = RemapChartQuery(prog)
    # Because prog is Imperial, acres unit should stay the same
    assert remap._replace("MrvReportingProjects.totalAreaAc") == "MrvReportingProjects.totalAreaAc"
    # but hectares should be replaced with acres
    assert remap._replace("MrvReportingProjects.totalAreaHa") == "MrvReportingProjects.totalAreaAc"
    assert (
        remap._replace("MrvReportingProjects.total_completion_programId") == "MrvReportingProjects.total_completion_68"
    )
    # and other columns should stay the same
    assert remap._replace("MrvReportingSummary.email") == "MrvReportingSummary.email"


async def test_remap_chart_columns(mdl):
    prog = await mdl.Programs(id=155, units=UnitsTypes.METRIC)
    remap = RemapChartQuery(prog)
    measures = [
        "MrvReportingSummary.dimension_totalAreaAc",
        "MrvReportingProjects.summer_completion",
        "MrvReportingProjects.eligible_area",
    ]
    dimensions = [
        "MrvReportingSummary.full_name",
        "MrvReportingProjects.ineligible_area",
        "MrvReportingProjects.totalAreaHa",
        "MrvReportingProjects.total_completion_programId",
    ]
    query = {
        "measures": measures,
        "dimensions": dimensions,
    }
    remap._remap_chart_columns(query, "measures")
    assert query["measures"] == [
        "MrvReportingSummary.dimension_totalAreaHa",
        "MrvReportingProjects.summer_completion",
        "MrvReportingProjects.eligible_area",
    ]
    remap._remap_chart_columns(query, "dimensions")
    assert query["dimensions"] == [
        "MrvReportingSummary.full_name",
        "MrvReportingProjects.ineligible_area",
        "MrvReportingProjects.totalAreaHa",
        "MrvReportingProjects.total_completion_155",
    ]


async def test_remap_chart_order(mdl):
    prog = await mdl.Programs(id=1119, units=UnitsTypes.METRIC)
    remap = RemapChartQuery(prog)
    order_as_dict = {
        "MrvReportingProjects.total_completion_programId": "asc",
        "MrvReportingProjects.winter_completion": "desc",
        "MrvReportingProjects.eligible_area": "desc",
        "MrvReportingProjects.totalAreaAc": "asc",
    }
    query = {"order": order_as_dict}
    remap._remap_chart_order(query)
    assert query["order"] == {
        "MrvReportingProjects.total_completion": "asc",
        "MrvReportingProjects.winter_completion": "desc",
        "MrvReportingProjects.eligible_area": "desc",
        "MrvReportingProjects.totalAreaHa": "asc",
    }
    prog = await mdl.Programs(id=68, units=UnitsTypes.US_IMPERIAL)
    remap = RemapChartQuery(prog)
    order_as_list = [
        ["MrvReportingProjects.winter_completion", "asc"],
        ["MrvReportingProjects.total_completion_programId", "desc"],
        ["MrvReportingProjects.eligible_area", "desc"],
        ["MrvReportingProjects.totalAreaAc", "asc"],
    ]
    query = {"order": order_as_list}
    remap._remap_chart_order(query)
    assert query["order"] == [
        ["MrvReportingProjects.winter_completion", "asc"],
        ["MrvReportingProjects.total_completion_68", "desc"],
        ["MrvReportingProjects.eligible_area", "desc"],
        ["MrvReportingProjects.totalAreaAc", "asc"],
    ]


async def test_remap_chart_filters(mdl):
    prog = await mdl.Programs(id=155, units=UnitsTypes.METRIC)
    remap = RemapChartQuery(prog)
    filters = [
        {
            "member": "MrvReportingProjects.totalAreaAc",
            "operator": "equals",
            "values": [100],
        },
        {
            "member": "MrvReportingProjects.totalAreaHa",
            "operator": "equals",
            "values": [200],
        },
        {
            "member": "MrvReportingProjects.total_completion_programId",
            "operator": "equals",
            "values": [10],
        },
        {
            "member": "MrvReportingSummary.email",
            "operator": "equals",
            "values": ["<EMAIL>"],
        },
    ]
    query = {"filters": filters}
    remap._remap_chart_filters(query)
    assert query["filters"] == [
        {
            "member": "MrvReportingProjects.totalAreaHa",
            "operator": "equals",
            "values": [100],
        },
        {
            "member": "MrvReportingProjects.totalAreaHa",
            "operator": "equals",
            "values": [200],
        },
        {
            "member": "MrvReportingProjects.total_completion_155",
            "operator": "equals",
            "values": [10],
        },
        {
            "member": "MrvReportingSummary.email",
            "operator": "equals",
            "values": ["<EMAIL>"],
        },
    ]


async def test_remap_chart_query_remap(mdl):
    prog = await mdl.Programs(id=1, units=UnitsTypes.METRIC)
    query = {
        "dimensions": [
            "MrvReportingSummary.projectId",
            "MrvReportingSummary.dimension_totalAreaAc",
            "MrvReportingProjects.eligible_area",
        ],
        "measures": [
            "MrvReportingProjects.count_fields",
            "MrvReportingProjects.total_completion_programId",
        ],
        "filters": [
            {"member": "MrvReportingSummary.dimension_totalAreaAc", "operator": "equals", "values": ["signed"]},
            {"member": "MrvReportingProjects.contractStatus", "operator": "equals", "values": ["signed"]},
        ],
        "order": {
            "MrvReportingSummary.first_name": "asc",
            "MrvReportingSummary.dimension_totalAreaAc": "desc",
        },
    }
    remap = RemapChartQuery(prog)
    remap.remap(query)
    assert query == {
        "dimensions": [
            "MrvReportingSummary.projectId",
            "MrvReportingSummary.dimension_totalAreaHa",
            "MrvReportingProjects.eligible_area",
        ],
        "measures": [
            "MrvReportingProjects.count_fields",
            "MrvReportingProjects.total_completion",
        ],
        "filters": [
            {"member": "MrvReportingSummary.dimension_totalAreaHa", "operator": "equals", "values": ["signed"]},
            {"member": "MrvReportingProjects.contractStatus", "operator": "equals", "values": ["signed"]},
        ],
        "order": {
            "MrvReportingSummary.first_name": "asc",
            "MrvReportingSummary.dimension_totalAreaHa": "desc",
        },
    }
