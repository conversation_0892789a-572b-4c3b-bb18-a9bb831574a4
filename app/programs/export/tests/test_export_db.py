from datetime import datetime, timedelta, UTC
from decimal import Decimal

from fields.enums import FieldStatus
from helper.i18n import Locale
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs.export.db import (
    get_field_boundaries,
    get_latest_export_run,
    get_or_create_export_run,
    get_producer_data_per_program,
    get_program_data_per_stage,
    get_program_field_overlaps,
    get_project_and_field_data_for_program,
    get_user_or_program_locale,
    update_export_run,
)
from programs.export.enums import ExportRunStatus


async def test_get_project_and_field_data_for_program(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project.id, group_id=user_group_2.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id, md5="test_md5", fs_field_id=kml_group.id, area=100, farm_id=group.id
    )

    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert out[0].user_email == user.email
    assert out[0].project_id == project.id
    assert out[0].group_ids == f"{user_group_1.id}, {user_group_2.id}"
    assert out[0].group_names == "Group 1, Group 2"
    assert out[0].mrv_field_id == field.id
    assert out[0].field_md5 == field.md5
    assert out[0].fs_field_id == kml_group.id
    assert out[0].field_area_ha == field.area
    assert out[0].field_status == field.status
    assert out[0].field_name == kml_group.name
    assert out[0].farm_name == group.name
    assert out[0].state_name == region.name
    assert out[0].field_deleted_at is None
    assert out[0].project_deleted_at is None
    assert out[0].reporting_enabled is True

    group = await mdl.Groups(name="test_farm_deleted")
    region = await mdl.CoreRegions(name="test_region_deleted")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name_deleted", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5_deleted",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
        deleted_at=datetime.now(),
    )

    # deleted field not returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert len(out) == 1

    # deleted field returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=True,
        include_reporting_disabled=False,
    )
    assert len(out) == 2

    # deleted project not returned
    project = await mdl.Projects(program_id=program.id, deleted_at=datetime.now())
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    group = await mdl.Groups(name="test_farm_deleted_project")
    region = await mdl.CoreRegions(name="test_regio_deleted_project")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name_deleted_project", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5_deleted_project",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
    )

    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert len(out) == 1

    # deleted project returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=True,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert len(out) == 2

    # both project and field returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=True,
        include_deleted_fields=True,
        include_reporting_disabled=False,
    )
    assert len(out) == 3

    # reporting disabled project not returned
    project = await mdl.Projects(program_id=program.id, reporting_enabled=False)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    group = await mdl.Groups(name="test_farm_reporting_disabled")
    region = await mdl.CoreRegions(name="test_region_reporting_disabled")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name_reporting_disabled", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id,
        md5="test_md5_reporting_disabled",
        fs_field_id=kml_group.id,
        area=100,
        farm_id=group.id,
    )

    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert len(out) == 1

    # reporting disabled project returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=True,
    )
    assert len(out) == 2

    # both reporting disabled project and deleted field returned

    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=True,
        include_reporting_disabled=True,
    )
    assert len(out) == 3

    # all reporting disabled project, deleted project and deleted field returned
    out = await get_project_and_field_data_for_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=True,
        include_deleted_fields=True,
        include_reporting_disabled=True,
    )
    assert len(out) == 4


async def test_get_program_data_per_stage(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)
    stage = await mdl.Stage(
        name="test_stage", type_=StageTypes.ASSIGN_PRACTICES, phase_id=phase.id, enabled=True, order=1
    )
    attribute = await mdl.Attribute(
        name="test_attribute", order=1, parent_stage_id=stage.id, enabled=True, type=AttributeTypes.practice
    )
    value1 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value", row_id=0)
    value2 = await mdl.Values(field_id=field.id, attribute_id=attribute.id, value="test_value", row_id=1)

    out = await get_program_data_per_stage(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
        stage_type_to_exclude=None,
    )
    assert len(out) == 2
    for data in out:
        assert data.mrv_field_id == field.id
        assert data.stage_id == stage.id
        assert data.stage_type == stage.type_
        assert data.stage_order == stage.order
        assert data.stage_name == stage.name
        assert data.phase_id == phase.id
        assert data.phase_type == phase.type_
        assert data.attribute_id == attribute.id
        assert data.attribute_type == attribute.type
        assert data.attribute_name == attribute.name
        assert data.attribute_order == attribute.order
        assert data.value == value1.value
        assert data.row_id in (value1.row_id, value2.row_id)
        assert data.field_deleted_at is None
        assert data.project_deleted_at is None
        assert data.reporting_enabled is True


async def test_get_field_boundaries(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group = await mdl.UserGroups(program_id=program.id)
    await mdl.UserGroupProjects(group_id=user_group.id, project_id=project.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id, md5="test_md5", fs_field_id=kml_group.id, area=100, farm_id=group.id
    )

    out = await get_field_boundaries(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert out[0].user_email == user.email
    assert out[0].project_id == project.id
    assert out[0].group_ids == str(user_group.id)
    assert out[0].group_names == user_group.name
    assert out[0].farm_name == group.name
    assert out[0].field_name == kml_group.name
    assert out[0].mrv_field_id == field.id
    assert out[0].field_md5 == field.md5
    assert out[0].fs_field_id == kml_group.id
    assert out[0].field_area_ha == field.area
    assert (
        out[0].geojson
        == '{"type": "MultiPolygon", "coordinates": [[[[37.020098201368114, -109.072265625], [37.020098201368114, -102.0849609375], [41.04621681452063, -102.0849609375], [41.04621681452063, -109.072265625], [37.020098201368114, -109.072265625]]]]}'
    )
    assert (
        out[0].geometry_wkt
        == "MULTIPOLYGON(((-109.072265625 37.020098201368114,-102.0849609375 37.020098201368114,-102.0849609375 41.04621681452063,-109.072265625 41.04621681452063,-109.072265625 37.020098201368114)))"
    )
    assert out[0].centroid_lat == 39.00495903656406
    assert out[0].centroid_lon == -105.57861328124999
    assert out[0].field_deleted_at is None
    assert out[0].project_deleted_at is None
    assert out[0].reporting_enabled is True


async def test_get_field_boundaries_invalid_geometry(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    user = await mdl.Users(email="<EMAIL>")
    await mdl.ProjectPermissions(
        user=user.id,
        project=project.id,
    )
    user_group = await mdl.UserGroups(program_id=program.id)
    await mdl.UserGroupProjects(group_id=user_group.id, project_id=project.id)
    group = await mdl.Groups(name="test_farm")
    region = await mdl.CoreRegions(name="test_region")
    kml_file = await mdl.KMLFiles(
        region_id=region.id,
        geometry={
            "type": "MultiPolygon",
            "coordinates": [
                [
                    [
                        [43.836077, 0.31761],
                        [43.836152, 0.317724],
                        [43.836375, 0.317751],
                        [43.837356, 0.317831],
                        [43.837521, 0.317372],
                        [43.837614, 0.317006],
                        [43.837579, 0.316971],
                        [43.837333, 0.316966],
                        [43.836928, 0.316986],
                        [43.836687, 0.316936],
                        [43.836513, 0.316775],
                        [43.836521, 0.316295],
                        [43.836209, 0.316114],
                        [43.835967, 0.315876],
                        [43.83585, 0.315889],
                        [43.835866, 0.316053],
                        [43.83585, 0.315889],
                        [43.835899, 0.316362],
                        [43.835963, 0.316871],
                        [43.836017, 0.317163],
                        [43.836077, 0.31761],
                    ]
                ]
            ],
        },
    )

    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    field = await mdl.Fields(
        parent_project_id=project.id, md5="test_md5", fs_field_id=kml_group.id, area=100, farm_id=group.id
    )

    out = await get_field_boundaries(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_deleted_fields=False,
        include_reporting_disabled=False,
    )
    assert out[0].user_email == user.email
    assert out[0].project_id == project.id
    assert out[0].group_ids == str(user_group.id)
    assert out[0].group_names == user_group.name
    assert out[0].farm_name == group.name
    assert out[0].field_name == kml_group.name
    assert out[0].mrv_field_id == field.id
    assert out[0].field_md5 == field.md5
    assert out[0].fs_field_id == kml_group.id
    assert out[0].field_area_ha == field.area
    assert (
        out[0].geojson == '{"type": "MultiPolygon", "coordinates": [[[[0.31761, 43.836077], [0.317724, '
        "43.836152], [0.317751, 43.836375], [0.317831, 43.837356], [0.317372, "
        "43.837521], [0.317006, 43.837614], [0.316971, 43.837579], [0.316966, "
        "43.837333], [0.316986, 43.836928], [0.316936, 43.836687], [0.316775, "
        "43.836513], [0.316295, 43.836521], [0.316114, 43.836209], [0.315876, "
        "43.835967], [0.315889, 43.83585], [0.316053, 43.835866], [0.315889, "
        "43.83585], [0.316362, 43.835899], [0.316871, 43.835963], [0.317163, "
        "43.836017], [0.31761, 43.836077]]]]}"
    )
    assert (
        out[0].geometry_wkt == "MULTIPOLYGON(((43.836077 0.31761,43.836152 0.317724,"
        "43.836375 0.317751,43.837356 0.317831,43.837521 0.317372,43.837614 0.317006,"
        "43.837579 0.316971,43.837333 0.316966,43.836928 0.316986,43.836687 0.316936,"
        "43.836513 0.316775,43.836521 0.316295,43.836209 0.316114,43.835967 0.315876,"
        "43.83585 0.315889,43.835866 0.316053,43.83585 0.315889,43.835899 0.316362,"
        "43.835963 0.316871,43.836017 0.317163,43.836077 0.31761)))"
    )
    assert out[0].centroid_lon == 0
    assert out[0].centroid_lat == 0
    assert out[0].field_deleted_at is None
    assert out[0].project_deleted_at is None
    assert out[0].reporting_enabled is True


async def test_get_producer_data_per_program(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    project1 = await mdl.Projects(program_id=program.id)
    user1 = await mdl.Users(
        email="<EMAIL>",
        phone="1234567890",
        settings={"company": {"state": "test1", "country": "country_test1"}, "phone": "123"},
    )
    await mdl.ProjectPermissions(
        user=user1.id,
        project=project1.id,
    )
    user_group_1 = await mdl.UserGroups(name="Group 1", program_id=program.id)
    user_group_2 = await mdl.UserGroups(name="Group 2", program_id=program.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=user_group_2.id)

    project2 = await mdl.Projects(program_id=program.id)
    user2 = await mdl.Users(
        email="<EMAIL>",
        phone="*********",
        settings={"company": {"state": "test2", "country": "country_test2"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user2.id,
        project=project2.id,
    )
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project2.id, group_id=user_group_2.id)

    project3 = await mdl.Projects(program_id=program.id)
    user3 = await mdl.Users(
        email="<EMAIL>",
        phone=None,
        settings={"company": {"state": "test3", "country": "country_test3"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user3.id,
        project=project3.id,
    )
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project3.id, group_id=user_group_2.id)

    project4 = await mdl.Projects(program_id=program.id)
    user4 = await mdl.Users(
        email="<EMAIL>",
        phone="",
        settings={"company": {"state": "test4", "country": "country_test4"}, "phone": "77777777777"},
    )
    await mdl.ProjectPermissions(
        user=user4.id,
        project=project4.id,
    )
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_1.id)
    await mdl.UserGroupProjects(project_id=project4.id, group_id=user_group_2.id)

    out = await get_producer_data_per_program(
        request=app_request,
        program_id=program.id,
        include_deleted_projects=False,
        include_reporting_disabled=False,
    )

    assert len(out) == 4
    for data in out:
        assert data.group_ids == f"{user_group_1.id}, {user_group_2.id}"
        assert data.group_names == "Group 1, Group 2"
        assert data.user_email in (user1.email, user2.email, user3.email, user4.email)
        assert data.user_phone_number in (user1.phone, user2.phone, user3.settings["phone"], user4.settings["phone"])
        assert data.user_region in (
            user1.settings["company"]["state"],
            user2.settings["company"]["state"],
            user3.settings["company"]["state"],
            user4.settings["company"]["state"],
        )
        assert data.user_country in (
            user1.settings["company"]["country"],
            user2.settings["company"]["country"],
            user3.settings["company"]["country"],
            user4.settings["company"]["country"],
        )
        assert data.project_id in (project1.id, project2.id, project3.id, project4.id)


async def test_get_program_field_overlaps(mdl, app_request, faker):
    program = await mdl.Programs()
    rule_config = await mdl.BoundaryRuleConfig(program_id=program.id)
    dst_project = await mdl.Projects(program_id=program.id)
    dst_field = await mdl.Fields(parent_project_id=dst_project.id, status=FieldStatus.registered)
    dst_user = await mdl.Users()
    await mdl.ProjectPermissions(project=dst_project.id, user=dst_user.id)

    md5_1 = faker.md5()
    name_1 = faker.name()
    overlap_at_1 = faker.date_time()
    area_1 = faker.pyfloat(min_value=0, max_value=10_000, right_digits=2)
    src_user_1 = await mdl.Users()
    src_project_1 = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=src_user_1.id, project=src_project_1.id)
    src_kml_file_1 = await mdl.KMLFiles(md5=md5_1)
    src_kml_group_1 = await mdl.KMLGroups(kml_id=src_kml_file_1.id, name=name_1, created_by=src_user_1.id)
    percent_overlap_1 = faker.pyfloat(min_value=0, max_value=100, right_digits=2)
    deviation_1 = await mdl.BoundaryRuleDeviation(
        md5=md5_1, rule_config_id=rule_config.id, percent_overlap=percent_overlap_1, created_at=overlap_at_1
    )
    overlap_1 = await mdl.ProgramFieldOverlap(
        created_at=overlap_at_1,
        deviation_id=deviation_1.id,
        field_id=dst_field.id,
        percentage_overlap=percent_overlap_1,
        area_intersection_ha=area_1,
    )

    md5_2 = faker.md5()
    name_2 = faker.name()
    overlap_at_2 = overlap_at_1 - timedelta(days=1)
    area_2 = faker.pyfloat(min_value=0, max_value=10_000, right_digits=2)
    src_user_1 = await mdl.Users()
    src_project_2 = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=src_user_1.id, project=src_project_2.id)
    src_kml_file_2 = await mdl.KMLFiles(md5=md5_2)
    src_kml_group_2 = await mdl.KMLGroups(kml_id=src_kml_file_2.id, name=name_2, created_by=src_user_1.id)
    percent_overlap_2 = faker.pyfloat(min_value=0, max_value=100, right_digits=2)
    deviation_2 = await mdl.BoundaryRuleDeviation(
        md5=md5_2, rule_config_id=rule_config.id, percent_overlap=percent_overlap_2, created_at=overlap_at_2
    )
    overlap_2 = await mdl.ProgramFieldOverlap(
        created_at=overlap_at_2,
        deviation_id=deviation_2.id,
        field_id=dst_field.id,
        percentage_overlap=percent_overlap_2,
        area_intersection_ha=area_2,
    )

    result = await get_program_field_overlaps(app_request, program.id, True, True)
    expected = [
        {
            "overlap_id": overlap_1.id,
            "overlap_detected_at_utc": overlap_at_1,
            "overlap_after_delete": "false",
            "percentage_overlap": Decimal(str(overlap_1.percentage_overlap)),
            "area_intersection_ha": Decimal(str(overlap_1.area_intersection_ha)),
            "attempted_adding_field_name": name_1,
            "attempted_adding_field_core_field_id": src_kml_group_1.id,
            "attempted_from_project_id": src_project_1.id,
            "attempted_from_project_status": src_project_1.status,
            "overlapped_with_project_id": dst_project.id,
            "overlapped_with_project_status": dst_project.status,
            "overlapped_with_field_id": dst_field.id,
            "overlapped_with_field_status": dst_field.status,
        },
        {
            "overlap_id": overlap_2.id,
            "overlap_detected_at_utc": overlap_at_2,
            "overlap_after_delete": "false",
            "percentage_overlap": Decimal(str(overlap_2.percentage_overlap)),
            "area_intersection_ha": Decimal(str(overlap_2.area_intersection_ha)),
            "attempted_adding_field_name": name_2,
            "attempted_adding_field_core_field_id": src_kml_group_2.id,
            "attempted_from_project_id": src_project_2.id,
            "attempted_from_project_status": src_project_2.status,
            "overlapped_with_project_id": dst_project.id,
            "overlapped_with_project_status": dst_project.status,
            "overlapped_with_field_id": dst_field.id,
            "overlapped_with_field_status": dst_field.status,
        },
    ]
    assert [r._mapping for r in result] == expected


async def test_get_user_or_program_locale(mdl, app_request):
    program = await mdl.Programs(name="test_program", locale="en-US")
    project1 = await mdl.Projects(program_id=program.id)
    user1 = await mdl.Users(
        email="<EMAIL>",
        phone="1234567890",
        settings={"company": {"state": "test1", "country": "country_test1"}, "phone": "123", "locale": "hu-HU"},
    )

    await mdl.ProjectPermissions(
        user=user1.id,
        project=project1.id,
    )

    # using user locale
    out = await get_user_or_program_locale(
        request=app_request,
        program_id=program.id,
        user_id=user1.id,
    )
    assert out == Locale.hu_HU

    project2 = await mdl.Projects(program_id=program.id)
    user2 = await mdl.Users(
        email="<EMAIL>",
        phone="*********",
        settings={"company": {"state": "test2", "country": "country_test2"}, "phone": "*********", "locale": "random"},
    )
    await mdl.ProjectPermissions(
        user=user2.id,
        project=project2.id,
    )

    # using random locale defaults to en_US
    out = await get_user_or_program_locale(
        request=app_request,
        program_id=program.id,
        user_id=user2.id,
    )
    assert out == Locale.en_US

    project3 = await mdl.Projects(program_id=program.id)
    user3 = await mdl.Users(
        email="<EMAIL>",
        phone=None,
        settings={"company": {"state": "test3", "country": "country_test3"}, "phone": "*********"},
    )
    await mdl.ProjectPermissions(
        user=user3.id,
        project=project3.id,
    )

    # using program locale
    out = await get_user_or_program_locale(
        request=app_request,
        program_id=program.id,
        user_id=user3.id,
    )
    assert out == Locale.en_US

    project4 = await mdl.Projects(program_id=program.id)
    user4 = await mdl.Users(
        email="<EMAIL>",
        phone=None,
        settings={
            "company": {"state": "test3", "country": "country_test3"},
            "phone": "*********",
            "locale": "hu-HU",
            "langLocale": "en-US",
        },
    )
    await mdl.ProjectPermissions(
        user=user4.id,
        project=project4.id,
    )

    # using lang locale
    out = await get_user_or_program_locale(
        request=app_request,
        program_id=program.id,
        user_id=user4.id,
    )
    assert out == Locale.en_US


async def test_get_or_create_export_run(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.COMPLETED,
        gcs_object="test_gcs_object",
        query_params={},
    )
    await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.FAILED,
        failure_reason="test failure",
        query_params={},
    )
    export_run_in_progress = await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.IN_PROGRESS,
        query_params={},
    )

    export_out, is_export_created = await get_or_create_export_run(
        request=app_request,
        program_id=program.id,
        query_params={},
    )
    assert is_export_created is False
    assert export_out.id == export_run_in_progress.id
    assert export_out.program_id == program.id
    assert export_out.user == 1
    assert export_out.export_run_status == ExportRunStatus.IN_PROGRESS

    program2 = await mdl.Programs(name="test_program2")
    await mdl.ProgramExportRuns(
        program_id=program2.id,
        user=1,
        export_run_status=ExportRunStatus.COMPLETED,
        gcs_object="test_gcs_object2",
        query_params={},
    )
    export_out, is_export_created = await get_or_create_export_run(
        request=app_request,
        program_id=program2.id,
        query_params={},
    )
    assert is_export_created is True
    assert export_out.program_id == program2.id
    assert export_out.user == 1
    assert export_out.export_run_status == ExportRunStatus.IN_PROGRESS


async def test_get_latest_export_run(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.COMPLETED,
        gcs_object="test_gcs_object",
        query_params={},
        created_at=datetime.now(tz=UTC) - timedelta(hours=1),
    )
    export_run_failed = await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.FAILED,
        failure_reason="test failure",
        query_params={},
        created_at=datetime.now(tz=UTC),
    )

    out = await get_latest_export_run(
        request=app_request,
        program_id=program.id,
    )
    assert out.id == export_run_failed.id


async def test_update_export_run(mdl, app_request):
    program = await mdl.Programs(name="test_program")
    export_in_progress = await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.IN_PROGRESS,
        query_params={},
        created_at=datetime.now(tz=UTC) - timedelta(hours=1),
    )

    export_out = await update_export_run(
        request=app_request,
        export_run_id=export_in_progress.id,
        gcs_object="test_gcs_object",
    )

    assert export_out.id == export_in_progress.id
    assert export_out.program_id == program.id
    assert export_out.user == 1
    assert export_out.export_run_status == ExportRunStatus.COMPLETED
    assert export_out.gcs_object == "test_gcs_object"

    export_in_progress = await mdl.ProgramExportRuns(
        program_id=program.id,
        user=1,
        export_run_status=ExportRunStatus.IN_PROGRESS,
        query_params={},
        created_at=datetime.now(tz=UTC),
    )

    export_out = await update_export_run(
        request=app_request,
        export_run_id=export_in_progress.id,
        failure_reason="failed",
    )

    assert export_out.id == export_in_progress.id
    assert export_out.program_id == program.id
    assert export_out.user == 1
    assert export_out.export_run_status == ExportRunStatus.FAILED
    assert export_out.failure_reason == "failed"
    assert export_out.gcs_object is None
