from datetime import datetime, timedelta, UTC

import sqlalchemy as sa
from httpx import AsyncClient

from notifications.enums import NotificationCodes, NotificationSources
from notifications.model import Notifications
from programs.export import paths
from programs.export.enums import ExportRunStatus
from programs.export.model import ProgramExportRuns


async def test_update_stuck_export_api(async_client: AsyncClient, mdl, db_session_maker):
    program = await mdl.Programs()
    user = await mdl.Users(email="<EMAIL>")
    export_run = await mdl.ProgramExportRuns(
        program_id=program.id,
        user=user.id,
        export_run_status=ExportRunStatus.IN_PROGRESS,
        created_at=datetime.now(tz=UTC) - timedelta(minutes=6),
    )
    notification = await mdl.Notifications(
        program_id=program.id,
        user=user.id,
        source=NotificationSources.MRV_EXPORT,
    )

    response = await async_client.get(f"/cron{paths.update_stuck_export}")
    assert response.status_code == 200

    async with db_session_maker() as s:
        result = await s.execute(sa.select(ProgramExportRuns).where(ProgramExportRuns.id == export_run.id))
        updated_export_run = result.scalars().all()
        result = await s.execute(sa.select(Notifications).where(Notifications.id == notification.id))
        updated_notification = result.scalars().all()
    assert updated_export_run[0].export_run_status == ExportRunStatus.FAILED
    assert updated_export_run[0].failure_reason == "Export run is stuck for more than 5 minutes"
    assert updated_notification[0].body["code"] == NotificationCodes.ERROR
