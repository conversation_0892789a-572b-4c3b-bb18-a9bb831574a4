import pytest

from annotations import DictStrAny
from programs.reporting_dashboards.dashboards.enums import (
    CubeBinaryFilters,
    CubeUnaryFilters,
    FilterKey,
)


@pytest.fixture(scope="package")
def dash_filters():
    return [
        {
            "type": None,
            "key": FilterKey.REGIONS,
            "label": "q9c4a9Yg",
            "member": "pNROmxqe",
            "query": {},
            "operator": CubeBinaryFilters.GT,
            "additional_applied_queries": None,
        },
        {
            "type": None,
            "key": FilterKey.GROUPS,
            "label": "blMyTDJ9",
            "member": "2HIsEa2h",
            "query": {},
            "operator": CubeBinaryFilters.EQUALS,
            "additional_applied_queries": None,
        },
        {
            "type": None,
            "key": FilterKey.PRACTICE_TYPE,
            "label": "VHcKvV3e",
            "member": "pNROmxq1",
            "query": {},
            "operator": CubeUnaryFilters.SET,
            "additional_applied_queries": None,
        },
        {
            "type": None,
            "key": FilterKey.PRODUCER,
            "label": "blMyTDJ8",
            "member": "2HIsEa2j",
            "query": {},
            "operator": CubeUnaryFilters.NOT_SET,
            "additional_applied_queries": None,
        },
    ]


@pytest.fixture(scope="package")
def chart_filters() -> list[DictStrAny]:
    return [
        {
            "type": None,
            "key": FilterKey.GROUPS,
            "label": "clMyTDJ9",
            "member": "3HIsEa2h",
            "query": {},
            "operator": CubeBinaryFilters.EQUALS,
            "additional_applied_queries": None,
        },
        {
            "type": None,
            "key": FilterKey.PRACTICE_TYPE,
            "label": "BHcKvV3e",
            "member": "qNROmxq1",
            "query": {},
            "operator": CubeUnaryFilters.SET,
            "additional_applied_queries": None,
        },
    ]
