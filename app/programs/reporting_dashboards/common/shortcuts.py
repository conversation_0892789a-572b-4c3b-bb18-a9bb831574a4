from __future__ import annotations

import json
from copy import deepcopy
from typing import TYPE_CHECKING

import elasticapm
from fastapi import H<PERSON><PERSON><PERSON><PERSON><PERSON>, Request, status

from helper.async_tools import Tasks
from helper.external_api import get_from_cubejs
from logger import get_logger
from programs.reporting_dashboards.dash_to_prog.validators import (
    get_data_from_cubejs_result,
    get_results_from_cubejs_response,
)
from programs.reporting_dashboards.dashboards.schema import (
    ReportingDashboardsFullResponse,
)
from programs.utils import update_chart_query
from root_crud.lazy_relations import load_lazy_relations

if TYPE_CHECKING:
    from programs.reporting_dashboards.model import ReportingDashboards

logger = get_logger(__name__)


@elasticapm.async_capture_span()
async def populate_filters_from_cubejs(
    request: Request, x_access_token: str, dashboards: list[ReportingDashboards], program_id: int | None = None
) -> None:
    dash_filter_to_task_map = {}
    # Keep them separate to avoid reaching the count of 15 async tasks.
    chart_filter_to_task_map = {}
    async with Tasks() as chart_io_tasks:
        async with Tasks() as io_tasks:
            for dashboard in dashboards:
                dashboard = await load_lazy_relations(request, dashboard, ReportingDashboardsFullResponse)
                dash_filters = dashboard.filters
                if not dash_filters:
                    dash_filters = []

                for i, filter_ in enumerate(dash_filters):
                    if filter_["query"] is None:  # skip filters without query
                        continue
                    query = deepcopy(filter_["query"])
                    if program_id:
                        query = await update_chart_query(query, program_id)
                    task = io_tasks.add(
                        get_from_cubejs(
                            headers={"authorization": f"Bearer {x_access_token}"},
                            params={"query": json.dumps(query), "queryType": "multi"},
                        )
                    )
                    dash_filter_to_task_map[(dashboard.id, i)] = task
                # add filters from charts
                for section in dashboard.sections:
                    for row in section.rows:
                        for chart in row.charts:
                            if not chart.filters:
                                continue

                            for i, filter_ in enumerate(chart.filters):
                                if filter_["query"] is None:  # skip filters without query
                                    continue
                                query = deepcopy(filter_["query"])
                                if program_id:
                                    query = await update_chart_query(query, program_id)
                                task = chart_io_tasks.add(
                                    get_from_cubejs(
                                        headers={"authorization": f"Bearer {x_access_token}"},
                                        params={"query": json.dumps(query), "queryType": "multi"},
                                    )
                                )
                                chart_filter_to_task_map[(chart.id, i)] = task
            await io_tasks.complete_all()

        await chart_io_tasks.complete_all()

    for dashboard in dashboards:
        dash_filters = dashboard.filters
        if not dash_filters:
            dash_filters = []

        for i, filter_ in enumerate(dash_filters):
            if filter_["query"] is None:  # skip filters without query
                continue
            response = await dash_filter_to_task_map[(dashboard.id, i)]
            results = get_results_from_cubejs_response(response, allow_empty_response=True)

            if len(results) > 1:
                logger.error(msg := "Multiple results returned from CubeJS")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail={"message": msg})

            filter_["values"] = get_data_from_cubejs_result(results[0], allow_empty_response=True)

        for section in dashboard.sections:
            for row in section.rows:
                for chart in row.charts:
                    if not chart.filters:
                        continue

                    for i, filter_ in enumerate(chart.filters):
                        if filter_["query"] is None:  # skip filters without query
                            continue
                        response = await chart_filter_to_task_map[(chart.id, i)]
                        results = get_results_from_cubejs_response(response, allow_empty_response=True)

                        if len(results) > 1:
                            logger.error(msg := "Multiple results returned from CubeJS for charts")
                            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail={"message": msg})

                        filter_["values"] = get_data_from_cubejs_result(results[0], allow_empty_response=True)
