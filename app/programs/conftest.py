from __future__ import annotations

from typing import TYPE_CHECKING

import pytest

if TYPE_CHECKING:
    from annotations import DictStrAny


@pytest.fixture(scope="package")
def cubejs_responses() -> dict[str, DictStrAny]:
    return {
        "empty": {},
        "empty_results": {"results": []},
        "empty_data": {"results": [{"data": [], "total": 0}]},
        "correct": {
            "results": [
                {
                    "data": [
                        {
                            "KmlFiles.name": "Cockrill 16",
                            "Regions.name": "Tennessee",
                            "Users.fullname": "Van Blackketter",
                            "PracticeChanges.value": "Cover Crops",
                            "MrvValues.value": "corn",
                            "KmlFiles.totalAreaAc": 4.654222675,
                        },
                        {
                            "KmlFiles.name": "Gibson Rd2",
                            "Regions.name": "Ohio",
                            "Users.fullname": "Rom Hastings",
                            "PracticeChanges.value": "Cover Crops",
                            "MrvValues.value": "corn",
                            "KmlFiles.totalAreaAc": 28.521106205,
                        },
                    ],
                    "annotation": {
                        "dimensions": {
                            "KmlFiles.name": {"title": "Kml Files Name"},
                            "Regions.name": {"title": "Regions Name"},
                            "Users.fullname": {"title": "Users Fullname"},
                            "PracticeChanges.value": {"title": "PracticeChanges Value"},
                            "MrvValues.value": {"title": "MrvValues Value"},
                            "KmlFiles.totalAreaAc": {"title": "Kml Files Total Area Ac"},
                        }
                    },
                    "total": 2,
                }
            ]
        },
        "wrong_keys": {"results": [{"data": [{"foo": " Cockrill 16"}, {"bar": "Gibson Rd2"}], "total": 2}]},
        "wrong_keys_2": {
            "results": [
                {"data": [{"foo": " Cockrill 16"}], "total": 1},
                {"data": [{"bar": " Cockrill 16"}], "total": 1},
            ]
        },
    }
