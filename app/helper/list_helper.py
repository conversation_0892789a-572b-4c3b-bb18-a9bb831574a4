from collections import defaultdict
from typing import Callable, TypeVar

T = TypeVar("T")
Key = TypeVar("Key")


def partition(condition: Callable[[T], bool], items: list[T]) -> tuple[list[T], list[T]]:
    """
    Partition a list into two lists based on a condition.

    Args:
        condition (Callable[[T], bool]): A function that takes an item from the list and returns True or False.
        items (list[T]): The list to partition.

    Returns:
        tuple[list[T], list[T]]: A tuple containing two lists: the first list contains items that satisfy the condition,
                                  and the second list contains items that do not.
    """
    true_list = []
    false_list = []
    for item in items:
        if condition(item):
            true_list.append(item)
        else:
            false_list.append(item)
    return true_list, false_list


def group_by(to_group: list[T], key: Callable[[T], Key]) -> dict[Key, list[T]]:
    """
    This is the canonical group_by operation.

    Accepts:
        to_group: the list whose elements we group
        key: callable function that produces keys from each elements
    Returns:
        A dict where the keys are the results of calling the 'key' function and values are a list of elements that
        resulted in a given key.
    """
    to_return = defaultdict(list)
    for elem in to_group:
        to_return[key(elem)].append(elem)
    return {k: v for k, v in to_return.items()}


def flatten_list(to_flatten: list[list[T]]) -> list[T]:
    """
    Flatten a list by one level
    """
    return [elem for sublist in to_flatten for elem in sublist]
