from helper.list_helper import partition


def test_partition_with_ints():
    input_list = [1, 2, 3, 4, 5, 6]
    output = partition(lambda x: x % 2 == 0, input_list)
    assert output == ([2, 4, 6], [1, 3, 5])


def test_partition_with_strings():
    input_list = ["apple", "bat", "cat", "elephant"]
    output = partition(lambda x: len(x) > 3, input_list)
    assert output == (["apple", "elephant"], ["bat", "cat"])


def test_partition_with_empty_list():
    input_list = []
    output = partition(lambda x: x > 0, input_list)
    assert output == ([], [])


def test_partition_with_all_true():
    input_list = [1, 2, 3]
    output = partition(lambda x: True, input_list)
    assert output == ([1, 2, 3], [])


def test_partition_with_all_false():
    input_list = [1, 2, 3]
    output = partition(lambda x: False, input_list)
    assert output == ([], [1, 2, 3])
