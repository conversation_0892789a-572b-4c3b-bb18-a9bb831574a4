from json import JSONDecodeError
from unittest.mock import Mock

from httpx import Response

from helper.external_api import is_response_continue_wait


def test_is_response_continue_wait():
    response = Mock(spec=Response)

    assert is_response_continue_wait(response) is False

    # Should return False if response does not contain valid JSON
    response.json.side_effect = JSONDecodeError("", "", 0)
    assert is_response_continue_wait(response) is False

    # Should return False if response does not contain error
    response.json.reset_mock(side_effect=True)
    response.json.return_value = {}
    assert is_response_continue_wait(response) is False

    # Should return False if response does not contain the correct error
    response.json.return_value = {"error": "some error"}
    assert is_response_continue_wait(response) is False

    # Should return True if response has the correct error
    response.json.return_value = {"error": "Continue wait"}
    assert is_response_continue_wait(response) is True
