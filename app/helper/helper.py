from __future__ import annotations

import inspect
import json
from typing import Any, cast, Literal, overload, TYPE_CHECKING
from urllib.parse import (
    parse_qs as parse_query_string,
    urlencode as encode_query_string,
)

import elasticapm
from fastapi import HTTPException, Request, status
from fastapi.responses import Response
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.dialects import mysql
from sqlalchemy.exc import MultipleResultsFound, NoResultFound
from starlette.types import ASGIApp, Receive, Scope, Send

from logger import get_logger

if TYPE_CHECKING:
    from pydantic.typing import AbstractSetIntStr, DictStrAny, MappingIntStrAny
    from sqlalchemy.engine import Result, Row
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.orm import Query
    from sqlalchemy.sql.compiler import SQLCompiler

    from db.mysql import Base
    from programs.schema import ProgramPermission

logger = get_logger(__name__)


def query_2_str(query: Query) -> SQLCompiler:
    return query.compile(dialect=mysql.dialect())


@overload
async def run_query(
    s: AsyncSession, query: Query, paginated: Literal[True], **kwargs: dict[str, Any]
) -> Page[Base]: ...


@overload
async def run_query(
    s: AsyncSession,
    query: Query,
    paginated: Literal[False] = False,
    **kwargs: dict[str, Any],
) -> Result: ...


@elasticapm.async_capture_span()
async def run_query(
    s: AsyncSession, query: Query, paginated: bool = False, **kwargs: dict[str, Any]
) -> Page[Base] | Result:

    if paginated:
        return cast(Page, await paginate(s, query, **kwargs))

    return await s.execute(query, kwargs)


class PropertyBaseModel(BaseModel):
    """
    Workaround for serializing properties with pydantic until
    https://github.com/samuelcolvin/pydantic/issues/935
    is solved
    """

    @classmethod
    def get_properties(cls) -> list[str]:
        return [
            prop
            for prop in dir(cls)
            if isinstance(getattr(cls, prop), property) and prop not in ("__values__", "fields")
        ]

    def to_dict(self) -> DictStrAny:
        return self.dict()

    def dict(
        self,
        *,
        include: AbstractSetIntStr | MappingIntStrAny | None = None,
        exclude: AbstractSetIntStr | MappingIntStrAny | None = None,
        by_alias: bool = False,
        skip_defaults: bool | None = None,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
    ) -> DictStrAny:
        attribs = super().dict(
            include=include,
            exclude=exclude,
            by_alias=by_alias,
            skip_defaults=skip_defaults,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none,
        )
        props = self.get_properties()
        # Include and exclude properties
        if include:
            props = [prop for prop in props if prop in include]
        if exclude:
            props = [prop for prop in props if prop not in exclude]

        # Update the attribute dict with the properties
        if props:
            attribs.update({prop: getattr(self, prop) for prop in props})

        return attribs


class QueryStringFlatteningMiddleware:
    def __init__(self, app: ASGIApp) -> None:
        self.app = app

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        query_string = scope.get("query_string", None)
        if scope["type"] == "http" and query_string:
            parsed: dict[str, list[str]] = parse_query_string(query_string.decode("utf-8"))
            flattened = {}
            for name, values in parsed.items():
                all_values = []
                for value in values:
                    all_values.extend(value.split(","))

                flattened[name] = all_values

            # doseq: Turn lists into repeated parameters, which is better for FastAPI
            scope["query_string"] = encode_query_string(flattened, doseq=True).encode("utf-8")

            await self.app(scope, receive, send)
        else:
            await self.app(scope, receive, send)


def optional(*fields: Any) -> Any:
    """Decorator function used to modify a pydantic model's fields to all be optional.
    Alternatively, you can  also pass the field names that should be made optional as arguments
    to the decorator.
    Taken from https://github.com/samuelcolvin/pydantic/issues/1223#issuecomment-775363074
    """

    def dec(_cls: Any) -> Any:
        for field in fields:
            _cls.__fields__[field].required = False
        return _cls

    if fields and inspect.isclass(fields[0]) and issubclass(fields[0], BaseModel):
        cls = fields[0]
        fields = cls.__fields__
        return dec(cls)

    return dec


def check_update_valid(obj: BaseModel) -> None:
    logger.debug(obj)
    logger.debug(obj.__dict__)
    logger.debug([v for v in obj.__dict__.values() if v is not None])
    if len([v for v in obj.__dict__.values() if v is not None]) == 0:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="You must pass some change. Also, check you arent passing a list",
        )


class UserInfoRetriever:
    def __init__(self, permissions_list: list[ProgramPermission], allow_missing_user: bool = True):
        ids_list = set()
        self.user_info_data: dict = {}
        self.allow_missing_user = allow_missing_user

        for permission in permissions_list:
            try:
                ids_list.add(permission.user_id)
            except AttributeError:
                ids_list.add(permission.user)

        self.ids_list = ids_list

    async def load_user_info(self) -> None:
        from core.methods import get_user_info_batch  # TODO: make module-level import

        logger.debug("starting request to core")
        users_data = await get_user_info_batch(list(self.ids_list), self.allow_missing_user)
        logger.debug("finished request to core")
        self.user_info_data = {user._user_id: user for user in users_data if user}

    def get_user_info(self, user_id: int) -> Any:
        """
        Return None if user is missing
        """
        return self.user_info_data.get(user_id)


class UserSyncInfoRetriever:
    def __init__(self, user_ids: Any):
        self.user_ids: Any = user_ids
        self.user_sync_info_data: Any = {}

    async def load_user_sync_info(self) -> None:
        from core.methods import get_user_latest_syncs  # TODO: make module-level import

        logger.debug("starting request to core")
        data = await get_user_latest_syncs(self.user_ids)
        self.user_sync_info_data = data
        logger.debug("finished request to core")

    def get_user_sync_info(self) -> list[dict[int, str]] | None:
        return self.user_sync_info_data


class TextFormatter:
    @staticmethod
    def indent_block(src: str = "", indent_text: str = "   ") -> str:
        result = ""
        for line in str(src).split("\n"):
            result += f"{indent_text}{line}\n"

        return result

    @staticmethod
    # take list of ints and return string of comma separated ints
    def list_to_string(src: list[Any] | None = None, delim: str = ",") -> str:
        if src is None:
            return ""
        src_as_string = [str(i) for i in src]
        return delim.join(src_as_string)


class JSONHelper:
    @staticmethod
    def write(value: Any) -> str | None:
        import json

        try:
            return json.dumps(value, indent=4, default=str)
        except Exception as e:
            logger.error(e)
            return ""

    @staticmethod
    def read(value: str | None) -> Any:
        if value is None:
            return None

        try:
            return json.loads(value)
        except Exception as e:
            logger.error(e)
            return None


class NumberHelper:
    @staticmethod
    def to_float(value: Any, default_value: Any = None) -> float | None:
        if value is None:
            return default_value

        try:
            return float(str(value))
        except ValueError:
            return default_value

    @staticmethod
    def to_int(value: Any, default_value: Any = None) -> int | None:
        if value is None:
            return default_value

        try:
            return int(str(value))
        except ValueError:
            return default_value


class QueryHelper:
    @staticmethod
    async def to_list(request: Request, query: str, parameters: dict[str, Any] | None = None) -> list[dict] | None:
        if parameters is None:
            parameters = {}

        result = []
        try:
            async with request.state.sql_session() as s:
                rows = await run_query(s=s, query=text(query), **parameters)
                for row in rows.mappings():
                    result.append(dict(row))
        except Exception as e:
            logger.error(e)
            return None

        return result

    @staticmethod
    async def to_list_single(
        request: Request,
        query: str,
        column_name: str,
        default_value: Any = None,
        parameters: dict[str, Any] | None = None,
    ) -> list[Any] | None:
        """
        Run query, take the named column and return a list of it's values
        """
        if parameters is None:
            parameters = {}

        rows = await QueryHelper.to_list(request=request, query=query, parameters=parameters)
        if rows is None:
            return None

        result = []
        for row in rows:
            if column_name not in row:
                result.append(default_value)  # this is so we will have the same length list as the original query
                continue

            result.append(row[column_name])

        return result

    @staticmethod
    async def to_single_value(
        request: Request,
        query: Query,
        column_name: str,
        default_value: Any = None,
        parameters: dict[str, Any] | None = None,
    ) -> Any | None:
        if parameters is None:
            parameters = {}

        result = await QueryHelper.to_list_single(
            request=request,
            query=query,
            column_name=column_name,
            default_value=default_value,
            parameters=parameters,
        )
        if result is None:
            return None

        if len(result) == 0:
            return None

        return result[0]

    @staticmethod
    async def to_key_value(
        request: Request,
        query: str,
        key_column: str,
        value_column: str,
        parameters: dict[str, Any] | None = None,
    ) -> dict[str, Any] | None:
        if parameters is None:
            parameters = {}

        rows = await QueryHelper.to_list(request=request, query=query, parameters=parameters)
        if rows is None:
            return None

        result = {}
        for row in rows:
            if key_column not in row:
                continue

            if value_column not in row:
                continue

            result[str(row[key_column])] = row[value_column]

        return result


class ResultHelper:
    @staticmethod
    def get_one(res: Result) -> Row | None:
        try:
            res = res.one()
            return res[0]
        except NoResultFound:
            logger.debug("No result found")
        except MultipleResultsFound:
            logger.debug("Multiple results found")
        return None


# TODO: this shouldn't be here.
def assert_dict_contains_subset(actual: dict, expected_subset: dict) -> None:
    """
    Assert that the key-value pairs in `actual` is a superset of `expected_subset`

    Raises: AssertionError if the assertion fails
    """
    # See https://stackoverflow.com/a/59777678
    assert actual | expected_subset == actual


# Note we have a commented out alternative version of this class below. This is because of
# https://regrow.atlassian.net/browse/MRV-4383
class CustomStreamingResponse(Response):
    """
    This is really just the Response class. It only exists because of https://regrow.atlassian.net/browse/MRV-4383.
    The workaround is to not use StreamingResponse, but to use plain Response. However, we want an easy way to
    switch back to using StreamingResponse if the bug is fixed, hence we're using this class to make it easier to
    switch back if we need to
    """


# See https://regrow.atlassian.net/browse/MRV-4383 for why this code is commented out.
#
# class CustomStreamingResponse(StreamingResponse):
#     """
#     This is really just the StreamingResponse class. It only exists because of
#     https://regrow.atlassian.net/browse/MRV-4383. The workaround is to not use StreamingResponse, but to use plain
#     Response. However, we want an easy way to switch back to using StreamingResponse if the bug is fixed, hence we're
#     using this class to make it easier to switch back if we need to.
#     """
#     def __init__(self, content: bytes | str, *args: Any, **kwargs: Any) -> None:
#         # Seems kinda pointless to create an iterator of a list with one item - not sure why we ever did this. But
#         # I'm preserving this behaviour.
#         super().__init__(iter([content]), *args, **kwargs)
