import enum

from fastapi import Request

from base_controller import BaseController
from projects.db import get_project_by_id
from projects.user_groups import db, schema
from user_groups.db import get_user_groups_by_ids
from user_groups.schema import UserGroup


class ProjectGroupsController(BaseController):
    def __init__(self, request: Request | None = None):
        super().__init__(request=request)

    @staticmethod
    def tags() -> list[str]:
        return ["project_user_groups"]

    @staticmethod
    def prefix() -> str | None:
        return "/projects/{project_id}/user_groups"

    async def do_read(self, project_id: int) -> schema.ProjectUserGroupsResponse | None:
        """
        List user groups associated with a project
        """
        project = await get_project_by_id(request=self.request, project_id=project_id)
        if not project:
            self.add_error(f"project not exist: {project_id}")
            return None

        group_ids = await db.get_project_group_ids(request=self.request, project_id=project_id)
        groups = await get_user_groups_by_ids(request=self.request, group_ids=group_ids)
        return schema.ProjectUserGroupsResponse(
            project_id=project_id, groups=[UserGroup.from_orm(group) for group in groups]
        )

    async def do_update(self, project_id: int, group_ids: list[int]) -> schema.ProjectUserGroupsResponse | None:
        """
        Associate a list of user groups with a project
        """
        project = await get_project_by_id(request=self.request, project_id=project_id)
        if not project:
            self.add_error(f"project not exist: {project_id}")
            return None

        groups = await get_user_groups_by_ids(request=self.request, group_ids=group_ids)
        await db.update_project_groups(
            request=self.request, project_id=project_id, group_ids=[group.id for group in groups]
        )
        return schema.ProjectUserGroupsResponse(
            project_id=project_id, groups=[UserGroup.from_orm(group) for group in groups]
        )

    class Routes(enum.StrEnum):
        list_project_groups = ""
        update_project_groups = ""
