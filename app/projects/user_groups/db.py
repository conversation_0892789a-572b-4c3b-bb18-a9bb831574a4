from fastapi import Request
from sqlalchemy.future import select

from config import get_settings
from helper.helper import run_query
from logger import get_logger
from root_crud import create, delete, get
from user_groups import model

settings = get_settings()
logger = get_logger(__name__)


async def get_project_group_ids(request: Request, project_id: int) -> list[int]:
    async with request.state.sql_session() as s:
        query = (
            select(model.UserGroupProjects.group_id)
            .join(model.UserGroups, model.UserGroups.id == model.UserGroupProjects.group_id)
            .where(model.UserGroupProjects.project_id == project_id)
            .where(model.UserGroups.deleted_at.is_(None))
            .where(model.UserGroupProjects.deleted_at.is_(None))
        )
    return (await run_query(query=query, s=s)).scalars().all()


async def update_project_groups(request: Request, project_id: int, group_ids: list[int]) -> list[int]:
    await delete.soft(
        request=request,
        orm_type=model.UserGroupProjects,
        id_field=model.UserGroupProjects.project_id,
        ids=[project_id],
        filters=[get.Filter(id_field=model.UserGroupProjects.deleted_at, ids=[None])],
    )
    await create.create(
        request=request,
        instances=[model.UserGroupProjects(project_id=project_id, group_id=group_id) for group_id in set(group_ids)],
        orm_type=model.UserGroupProjects,
        translate=False,
    )
    return group_ids
