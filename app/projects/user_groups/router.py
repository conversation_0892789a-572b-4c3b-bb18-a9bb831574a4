from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Request, status

from permissions.enums import Permission
from permissions.resolver import Permissions
from projects.user_groups import schema
from projects.user_groups.controller import (  # your controller
    ProjectGroupsController as TheController,
)

router = APIRouter(prefix=TheController.prefix(), tags=TheController.tags())


@router.get(
    TheController.Routes.list_project_groups,  # /projects/{project_id}/user_groups
    response_model=schema.ProjectUserGroupsResponse,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_GROUPS]))],
)
async def get_project_groups(request: Request, project_id: int) -> schema.ProjectUserGroupsResponse:
    controller = TheController(request=request)
    result = await controller.do_read(project_id=project_id)
    handle_error(result, status.HTTP_400_BAD_REQUEST, controller.error_string)
    return result


@router.post(
    TheController.Routes.update_project_groups,  # /projects/{project_id}/user_groups
    response_model=schema.ProjectUserGroupsResponse,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.UPDATE_PROJECT_GROUPS]))],
)
async def update_project_groups(
    request: Request,
    project_id: int,
    payload: schema.UpdateProjectUserGroupsRequest,
) -> schema.ProjectUserGroupsResponse:
    controller = TheController(request=request)
    result = await controller.do_update(
        project_id=project_id,
        group_ids=payload.group_ids,
    )
    handle_error(result, status.HTTP_400_BAD_REQUEST, controller.error_string)
    return result


def handle_error(
    result: Any | None = None, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR, msg: str = ""
) -> None:
    if result is not None:
        return None

    raise HTTPException(status_code=status_code, detail={"message": str(msg)})
