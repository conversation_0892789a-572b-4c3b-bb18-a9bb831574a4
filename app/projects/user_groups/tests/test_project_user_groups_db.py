import asyncio
import random

import pytest

from projects.user_groups.db import get_project_group_ids, update_project_groups


@pytest.mark.skip(reason="This test is flaky, need to investigate deadlock issue")
async def test_update_project_groups(app_request, mdl):
    program = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program.id)
    project2 = await mdl.Projects(program_id=program.id)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")

    # create associations
    tasks = [
        update_project_groups(app_request, project1.id, [group1.id, group2.id]),
        update_project_groups(app_request, project2.id, [group2.id, group3.id]),
    ]
    await asyncio.gather(*tasks)
    assert set(await get_project_group_ids(app_request, project1.id)) == {group1.id, group2.id}
    assert set(await get_project_group_ids(app_request, project2.id)) == {group2.id, group3.id}

    # update associations
    tasks = [
        update_project_groups(app_request, project1.id, [group2.id]),
        update_project_groups(app_request, project2.id, [group1.id, group3.id]),
    ]
    await asyncio.gather(*tasks)
    assert set(await get_project_group_ids(app_request, project1.id)) == {group2.id}
    assert set(await get_project_group_ids(app_request, project2.id)) == {group1.id, group3.id}

    # delete associations
    tasks = [
        update_project_groups(app_request, project1.id, []),
        update_project_groups(app_request, project2.id, []),
    ]
    await asyncio.gather(*tasks)
    assert set(await get_project_group_ids(app_request, project1.id)) == set()
    assert set(await get_project_group_ids(app_request, project2.id)) == set()

    # concurrent updates with 20 projects
    groups = [await mdl.UserGroups(program_id=program.id, name=f"group {str(i)}") for i in range(20)]
    projects = [await mdl.Projects(program_id=program.id) for _ in range(20)]
    for _ in range(10):
        project_groups = {project.id: {group.id for group in random.choices(groups, k=10)} for project in projects}
        tasks = [
            update_project_groups(app_request, project_id, list(group_ids))
            for project_id, group_ids in project_groups.items()
        ]
        await asyncio.gather(*tasks)
        for project_id, group_ids in project_groups.items():
            assert set(await get_project_group_ids(app_request, project_id)) == group_ids
