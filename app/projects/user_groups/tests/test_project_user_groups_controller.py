from projects.user_groups.controller import ProjectGroupsController


async def test_do_read(app_request, mdl):
    program = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program.id)
    project2 = await mdl.Projects(program_id=program.id)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")

    await mdl.UserGroupProjects(project_id=project1.id, group_id=group1.id)
    await mdl.UserGroupProjects(project_id=project1.id, group_id=group2.id)
    await mdl.UserGroupProjects(project_id=project2.id, group_id=group3.id)

    controller = ProjectGroupsController(request=app_request)
    result = await controller.do_read(project_id=project1.id)
    assert result is not None
    assert result.project_id == project1.id
    assert {group.id for group in result.groups} == {group1.id, group2.id}

    result = await controller.do_read(project_id=project2.id)
    assert result is not None
    assert result.project_id == project2.id
    assert {group.id for group in result.groups} == {group3.id}

    result = await controller.do_read(project_id=0)
    assert result is None
    assert controller.error_string != ""


async def test_do_update(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")

    controller = ProjectGroupsController(request=app_request)
    result = await controller.do_update(project_id=project.id, group_ids=[group1.id, group2.id])
    assert result is not None
    assert result.project_id == project.id
    assert {group.id for group in result.groups} == {group1.id, group2.id}

    result = await controller.do_update(project_id=project.id, group_ids=[group3.id])
    assert result is not None
    assert result.project_id == project.id
    assert {group.id for group in result.groups} == {group3.id}

    result = await controller.do_update(project_id=0, group_ids=[group1.id])
    assert result is None
    assert controller.error_string != ""
