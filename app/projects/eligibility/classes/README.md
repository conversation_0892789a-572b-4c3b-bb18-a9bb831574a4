# Eligibility System

The eligibility system determines which sustainable agricultural practices a field is eligible to implement based on historical data, field characteristics, and program requirements. This document explains the architecture and flow of the eligibility system, focusing on the standard eligibility runner implementation.

## Overview

The eligibility system evaluates field data to determine which practices (tillage, cover crop, irrigation, nutrient management) can be implemented on each field. The system considers:

- Historical practices from cultivation cycles
- Field characteristics and location
- Program-specific requirements
- Practice dependencies and rules

The output is a set of eligible practices for each field, which guides farmers in selecting sustainable agricultural practices that qualify for program benefits.

## Key Components

### EligibilityRunner

The central class that orchestrates the eligibility determination process. It:
- Collects necessary data (entity IDs, eligibility configuration, interventions, cultivation cycles)
- Manages baseline year determination
- Creates and runs practice-specific eligibility checkers
- Collects results and prepares the final eligibility output

### Practice Eligibility Classes

Specialized classes for each practice type:
- **TillageEligibility**: Evaluates tillage practices
- **CoverCropEligibility**: Evaluates cover crop practices
- **IrrigationEligibility**: Evaluates irrigation practices
- **NMEligibility**: Evaluates nutrient management practices

Each class implements the `run_eligibility()` method to determine eligible practices based on historical data and program rules.

### Practice Generators

Classes that analyze cultivation cycles to determine historical practices:
- **TillageGenerator**: Analyzes tillage events
- **CoverCropGenerator**: Analyzes cover crop events
- **IrrigationGenerator**: Analyzes irrigation events

Each generator has multiple versions (V1, V2, V3) to support different program requirements and calculation methodologies.

### ComputePractices

Combines the allowed practices from different practice types to generate valid practice combinations that a field is eligible for.

### Cultivation Cycles

Time periods delineated as commodity crop harvest-to-harvest at the field level. These replace the concept of "record years" with a more flexible approach for grouping field events. While programs with finished E phase before migrating to EBDC is still using the legacy record year defined cultivation cycle to make sure existing eligibility result do not change.

## Eligibility Flow

### Entry Points

The system has multiple entry points based on eligibility type:
- **Program-specific eligibility**: Custom logic for specific programs (Cargill Grain, Cotton, EU, etc.)
- **Always eligible**: Bypasses checks and returns all fields as eligible
- **Custom eligibility**: Standard logic implemented by the `custom_eligibility_runner`

### Flow Diagram for Eligibility Runner

```mermaid
flowchart TD
    %% Top-level flow
    Client["Client Request<br>run_eligibility\(\)"] --> EligibilityType["Determine Eligibility Type"]
    EligibilityType --> ProgramSpecific["Program-Specific Eligibility<br>\(Cargill Grain, Cotton, EU, etc.\)"]
    EligibilityType --> AlwaysEligible["Always Eligible<br>\(Bypass checks\)"]
    EligibilityType --> CustomEligibility["Custom Eligibility<br>\(Standard logic\)"]

    %% Custom Eligibility Runner flow
    subgraph CustomEligibilityFlow["custom_eligibility_runner Flow"]
        A["custom_eligibility_runner<br>project_id, stage_id, phase_id, entity_type, default_baseline_year"] --> B["Create EligibilityRunner instance"]
        B --> C["EligibilityRunner.run\(\)"]

        C --> D["Parallel Initialization"]
        D --> D1["_get_entity_ids\(\)<br>self.entity_ids"]
        D --> D2["_set_eligibility_config\(\)<br>self.eligibility_config"]
        D --> D3["_set_interventions\(\)<br>self.interventions"]
        D --> D4["_set_cultivation_cycles\(\)<br>self.cultivation_cycles"]

        D1 & D2 & D3 & D4 --> E["_set_baseline_years\(\)<br>Sets baseline years for each field"]

        E --> F["Create Practice Eligibility Runner Instances"]
        F --> F1["TillageEligibility"]
        F --> F2["CoverCropEligibility"]
        F --> F3["IrrigationEligibility"]
        F --> F4["NMEligibility"]

        F1 & F2 & F3 & F4 --> G["Run Eligibility Checks in Parallel<br>run_eligibility\(eligibility_config, cultivation_cycles\)"]

        G --> H["Practice Generator Processing"]
        H --> H1["1.Get appropriate generator version"]
        H1 --> H2["2.Generate practices from events"]
        H2 --> H3["3.Determine historical practices"]
        H3 --> H4["4.Determine allowed practices"]

        H4 --> I["Collect Eligibility Results<br>self.allowed_practices\[PracticeType\]"]

        I --> J["ComputePractices<br>Combines allowed practices from<br>different practice types"]

        J --> K["Return Eligibility Results<br>dict\[int, FieldEligibility\]"]
    end

    %% Connect to main flow
    CustomEligibility --> A

    %% Post-processing for parameterized eligibility
    K --> PostProcess["Post-Processing"]
    PostProcess --> FlattenPractices["Flatten Eligible Practices<br>\(For Parameterized Eligibility\)"]
    PostProcess --> RemoveImpossible["Remove Impossible Practices<br>\(Based on dependency rules\)"]
    PostProcess --> ValidatePractices["Validate Practices<br>\(Check against program allowed practices\)"]

    FlattenPractices & RemoveImpossible & ValidatePractices --> FinalResult["Final Eligibility Results"]
```

## Detailed Process Flow

1. **Client Request**: The process begins with a call to `run_eligibility()` with project_id and stage_id.

2. **Eligibility Type Determination**: The system determines which eligibility method to use based on the stage configuration.

3. **Custom Eligibility Runner**:
   - Creates an EligibilityRunner instance with project_id, stage_id, phase_id, entity_type, and default_baseline_year
   - Calls EligibilityRunner.run() to start the eligibility process

4. **Parallel Initialization**:
   - `_get_entity_ids()`: Retrieves the entity IDs (usually field IDs) related to the project
   - `_set_eligibility_config()`: Gets the StageEligibilityConfig for the specified stage
   - `_set_interventions()`: Gets the program PracticeChanges enabled on the phase's program
   - `_set_cultivation_cycles()`: Retrieves cultivation cycles associated with the project

5. **Baseline Year Setting**:
   - Sets the baseline year for each field, using either the default baseline year or preserved baseline years if enabled

6. **Practice Eligibility Runners**:
   - Creates instances of TillageEligibility, CoverCropEligibility, IrrigationEligibility, and NMEligibility based on enabled configurations
   - Each runner executes its `run_eligibility()` method with the appropriate eligibility configuration and cultivation cycles

7. **Practice Generator Processing**:
   - Selects the appropriate practice generator version
   - Generates practices from events in cultivation cycles
   - Determines historical practices based on past events
   - Determines allowed practices based on historical practices and program rules

8. **Result Collection**:
   - Stores results in the EligibilityRunner's `allowed_practices` dictionary, organized by practice type

9. **Practice Computation**:
   - ComputePractices combines allowed practices from different practice types to generate valid practice combinations

10. **Post-Processing** (for parameterized eligibility):
    - Flattens eligible practices
    - Removes impossible practices based on dependency rules
    - Validates practices against program-allowed practices

11. **Final Result**:
    - Returns a dictionary mapping entity IDs to FieldEligibility objects

## Usage

The eligibility system is typically invoked during:
1. **Project enrollment**: To determine which practices a field is eligible for
2. **Practice assignment**: To validate that assigned practices are eligible
3. **Eligibility recalculation**: When field data is updated

Example:
```python
# Determine eligibility for a project
eligibility_results = await determine_project_eligibilities_at_enrolment(
    request=request,
    project_id=project_id,
    stage_id=stage_id
)

# Access eligibility for a specific field
field_eligibility = eligibility_results[field_id]
eligible_practices = field_eligibility.eligible_practices
is_eligible = field_eligibility.eligible
```

## Configuration

Eligibility is configured through:
1. **StageEligibilityConfig**: Configures which practice types are enabled and their versions
2. **CommonEligibilityConfig**: Configures specific rules for each practice type
3. **PhasePracticeConfig**: Configures which attributes to look at for practice determination

These configurations are typically set up during program creation and can be customized for different programs and phases.

## Extending the System

To add a new practice type:
1. Create a new practice generator class extending `PracticeGenerator`
2. Create a new eligibility class extending `BaseEligibility`
3. Update EligibilityRunner to include the new practice type
4. Update ComputePractices to handle the new practice type combinations
