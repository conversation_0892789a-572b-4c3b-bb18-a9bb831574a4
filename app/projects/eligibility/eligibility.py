from __future__ import annotations

from copy import deepcopy
from typing import TYPE_CHECKING

import elasticapm
from fastapi import HTTPException, Request, status

from logger import get_logger
from phases.enums import (
    CARGILL_EU_2022_ELIGIBILITY_TYPES,
    EligibilityTypes,
    PracticeRuleConditions,
    PracticeRuleTypes,
)
from phases.methods import get_eligibility_type
from phases.schema import PracticeDependencyRule
from programs.db import get_program_by_project_id
from programs.enums import PracticeChange
from projects.db import (
    get_field_ids_for_project_id,
    persist_field_baselines,
    persist_field_eligibilities,
)
from projects.eligibility.cargill_cotton_2022 import run_cargill_cotton_2022
from projects.eligibility.cargill_cotton_2023 import run_cargill_cotton_2023
from projects.eligibility.cargill_cotton_2024 import run_cargill_cotton_2024
from projects.eligibility.cargill_eu_2022 import run_cargill_eu_2022
from projects.eligibility.cargill_eu_2023 import run_cargill_eu_2023
from projects.eligibility.cargill_eu_2024 import run_cargill_eu_2024
from projects.eligibility.cargill_grain_2022 import run_cargill_grain_2022
from projects.eligibility.cargill_grain_2023 import run_cargill_grain_2023
from projects.eligibility.cargill_grain_2024 import run_cargill_grain_2024
from projects.eligibility.cargill_grain_2025 import run_cargill_grain_2025
from projects.eligibility.classes.compute_practices import ComputePractices
from projects.eligibility.helper import (
    create_true_eligibility_response,
    get_disallowed_eligible_practices,
)
from projects.schema import FieldEligiblity

if TYPE_CHECKING:
    from phases.schema import StageWithEligibilityPhaseEntityType
    from values.enums import EntityTypeChoices

logger = get_logger(__name__)

# Programs that do not generate the NO_COVER_CROP practice in E Phase eligibility, so should not generate them here.
PROGRAMS_EXCLUDING_NO_COVER_CROP = {68, 155}


async def run_eligibility(request: Request, stage_id: int, project_id: int) -> dict[int, FieldEligiblity]:
    """
    Eligibility overview: https://regrow.atlassian.net/wiki/spaces/ENG/pages/2774269985/Eligibility
    """
    eligibility_details: StageWithEligibilityPhaseEntityType | None = await get_eligibility_type(
        request=request, stage_id=stage_id
    )
    if eligibility_details is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Eligibility method not selected for this stage"},
        )
    eligibility_type = eligibility_details.eligibility_method
    phase_id = eligibility_details.phase_id
    entity_type = eligibility_details.entity_type
    # baseline_year is the final year of the baseline period
    baseline_year = eligibility_details.baseline_year

    if eligibility_type == EligibilityTypes.CARGILL_GRAIN_2022:
        from projects.methods import get_enrolment_start_date

        start_date = await get_enrolment_start_date(request, project_id)
        eligibilities = await run_cargill_grain_2022(
            request=request, project_id=project_id, enrolment_start_date=start_date
        )
    elif eligibility_type == EligibilityTypes.CARGILL_COTTON_2022:
        eligibilities = await run_cargill_cotton_2022(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE:
        eligibilities = await always_eligible(request=request, project_id=project_id)
    # TODO consolidate EU Eligibility Types if we don't need the split in the logic
    elif eligibility_type in CARGILL_EU_2022_ELIGIBILITY_TYPES:
        eligibilities = await run_cargill_eu_2022(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_EU_2023:
        eligibilities = await run_cargill_eu_2023(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_EU_2024:
        eligibilities = await run_cargill_eu_2024(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_GRAIN_2023:
        eligibilities = await run_cargill_grain_2023(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_GRAIN_2024:
        eligibilities = await run_cargill_grain_2024(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_GRAIN_2025:
        eligibilities = await run_cargill_grain_2025(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_COTTON_2023:
        eligibilities = await run_cargill_cotton_2023(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CARGILL_COTTON_2024:
        eligibilities = await run_cargill_cotton_2024(request=request, project_id=project_id)
    elif eligibility_type == EligibilityTypes.CUSTOM or eligibility_type == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
        eligibilities = await custom_eligibility_runner(
            request=request,
            project_id=project_id,
            stage_id=stage_id,
            phase_id=phase_id,
            entity_type=entity_type,
            default_baseline_year=baseline_year,
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": (f"Eligibility method {eligibility_type} has invalid eligibililty")},
        )

    program = await get_program_by_project_id(request=request, project_id=project_id)

    if eligibility_type == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
        from programs.methods import get_practice_changes_by_program_id

        # Flatten the eligible practices, also append the always eligible practices
        all_program_practices = await get_practice_changes_by_program_id(request=request, program_id=program.id)
        always_eligible_practices = [
            practice.practice_change for practice in all_program_practices if practice.always_eligible
        ]
        eligibilities = flatten_eligible_practices(
            eligibilities=eligibilities, always_eligible_practices=always_eligible_practices
        )
        eligibilities = await remove_impossible_practices(
            request=request, eligibilities=eligibilities, stage_id=stage_id
        )
    disallowed_eligible_practices = await get_disallowed_eligible_practices(
        request=request, program_id=program.id, field_eligibilities=eligibilities
    )
    if disallowed_eligible_practices:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": (f"Eligible practices {disallowed_eligible_practices} not allowed for program {program.id}")
            },
        )

    return eligibilities


def flatten_eligible_practices(
    eligibilities: dict[int, FieldEligiblity], always_eligible_practices: list[PracticeChange] | None = None
) -> dict[int, FieldEligiblity]:
    """
    Flatten the eligible practices for each field into a single list of practice changes.
    Also, add the additional practices with the eligible practices if provided.
    This is only for the PARAMETERISED_ELIGIBILITY type.
    """
    flattened_eligibilities: dict[int, FieldEligiblity] = deepcopy(eligibilities)

    for field_eligibility in flattened_eligibilities.values():
        # if the field is hard ineligible, failed Param 3 or 6
        # then the field is ineligible even with always eligible practices offered
        if field_eligibility.hard_ineligible:
            continue

        original_eligibility = field_eligibility.eligible

        # merge eligible practices with always eligible practices
        eligible_practices_set = {
            practice for combination in field_eligibility.eligible_practices for practice in combination
        }
        eligible_practices_set.update(always_eligible_practices or [])

        # convert the result back to nested lists (not necessary but keeps the structure consistent)
        field_eligibility.eligible_practices = [[practice] for practice in eligible_practices_set]
        # reset eligibility to True if there are eligible practices (since we've added always eligible practices)
        field_eligibility.eligible = bool(field_eligibility.eligible_practices)

        # clear message if field was previously ineligible but is now eligible due to always eligible practices
        if not original_eligibility and field_eligibility.eligible:
            field_eligibility.message = ""

    return flattened_eligibilities


async def remove_impossible_practices(
    request: Request, eligibilities: dict[int, FieldEligiblity], stage_id: int
) -> dict[int, FieldEligiblity]:
    """
    Remove practices that are not possible due to dependency rules.
    This is only for the PARAMETERISED_ELIGIBILITY type.
    """
    from projects.classes.practice_change_rule_validator import (
        PracticeChangeRuleValidator,
    )

    # Get practice change rules for the stage
    validator = PracticeChangeRuleValidator(request=request, stage_id=stage_id)
    practice_change_rules = await validator.get_practice_change_rules()
    # Only PRACTICE_DEPENDENCY rules with ALL or ANY conditions could produce impossible practices
    dependency_rules = [
        rule
        for rule in practice_change_rules
        if rule.type == PracticeRuleTypes.PRACTICE_DEPENDENCY
        and isinstance(rule, PracticeDependencyRule)
        and rule.condition in [PracticeRuleConditions.ALL, PracticeRuleConditions.ANY]
    ]
    if not dependency_rules:
        return eligibilities

    # For each field, filter practices based on dependency rules
    for field_eligibility in eligibilities.values():
        original_eligibility = field_eligibility.eligible
        field_practices_set = {
            practice for combination in field_eligibility.eligible_practices for practice in combination
        }

        # Check each rules against current practices
        while True:
            impossible_practices = {
                rule.target
                for rule in dependency_rules
                if rule.target in field_practices_set and not rule.is_valid(field_practices_set)
            }
            if not impossible_practices:
                # all rules passed
                break
            field_practices_set -= impossible_practices

        # Update eligible practices with remaining valid ones
        field_eligibility.eligible_practices = [[practice] for practice in field_practices_set]
        field_eligibility.eligible = len(field_eligibility.eligible_practices) > 0

        # update message if field was previously eligible but is now ineligible due to dependency rules
        if original_eligibility and not field_eligibility.eligible:
            field_eligibility.message = "No practices are eligible for this field, due to dependency rules"

    return eligibilities


# immediately return eligible: True for all fields in a project, to bypass any checks
# we need to generate all the combinations of practice changes so need to supply fields
async def always_eligible(request: Request, project_id: int) -> dict[int, FieldEligiblity]:
    fields = await get_field_ids_for_project_id(
        request=request,
        project_id=project_id,
    )
    return await create_true_eligibility_response(request, project_id, fields)


async def custom_eligibility_runner(
    request: Request,
    project_id: int,
    stage_id: int,
    phase_id: int,
    entity_type: EntityTypeChoices,
    default_baseline_year: int,
) -> dict[int, FieldEligiblity]:
    """
    This is the standard eligibility logic and is not specific to a program.
    """
    from projects.eligibility.classes.eligibility_runner import EligibilityRunner

    runner = EligibilityRunner(
        request=request,
        project_id=project_id,
        assign_practice_stage_id=stage_id,
        phase_id=phase_id,
        entity_type=entity_type,
        default_baseline_year=default_baseline_year,
    )
    await runner.run()

    return ComputePractices(
        eligibility_config=runner.eligibility_config,
        eligible_entity_practices=runner.allowed_practices,
    ).generate_allowed_practices()


@elasticapm.async_capture_span()
async def determine_project_eligibilities_at_enrolment(
    request: Request, project_id: int, stage_id: int
) -> dict[int, FieldEligiblity]:
    """
    Determine eligible practices for all fields in a project at enrolment time (though this can still be called after
    enrolment has closed and monitering has commenced). Store the results in mrv_field_eligibilities

    Args:
        request: Fast API request object
        project_id: get eligibilities for all fields in this project
        stage_id: determines eligibility method

    Returns:
        A dict that maps field IDs to `FieldEligiblity`
    """

    response_map: dict[int, FieldEligiblity] = await run_eligibility(
        request=request, project_id=project_id, stage_id=stage_id
    )
    # save selected practices
    await persist_field_eligibilities(request, stage_id, response_map)
    # save baseline practices
    await persist_field_baselines(request=request, stage_id=stage_id, field_eligibility_responses=response_map)
    return response_map
