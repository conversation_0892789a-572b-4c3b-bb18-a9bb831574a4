import csv
from collections import defaultdict
from collections.abc import Iterable
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, <PERSON><PERSON>

import sqlalchemy
from fastapi import Request
from sqlalchemy import delete
from sqlalchemy.future import select
from sqlalchemy.orm import Session

from config import get_settings
from cultivation_cycles.schema import (
    CultivationCycle,
    CultivationCycleId,
)
from defaults.attribute_options import IrrigationMethods
from defaults.defaults import defaults_retriever
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.events.entity_event import EntityEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.methods import get_entity_events_for_project_phase
from helper.datetime_helper import parse_datetime_as_utc
from helper.helper import run_query
from helper.i18n import Locale, translate_message
from logger import get_logger
from phases.enums import AttributeTypes, EligibilityTypes, PhaseTypes, StageTypes
from phases.model import Phases, Stage
from programs.enums import PracticeChange
from programs.model import Programs
from projects.eligibility.constants import DATE_FORMAT
from projects.eligibility.enums import (
    <PERSON><PERSON>,
    Cover<PERSON>ropType,
    IrrigationIntensity,
    TillageIntensity,
)
from projects.eligibility.model import LUT
from projects.model import Projects
from projects.schema import FieldEligiblity, HistoricPracticeResult
from values.enums import EntityTypeChoices

settings = get_settings()
logger = get_logger(__name__)


def create_empty_eligibility_response(fields: dict[int, Any]) -> dict[int, FieldEligiblity]:
    return {field_id: FieldEligiblity() for field_id in fields.keys()}


async def create_true_eligibility_response(
    request: Request, project_id: int, fields: list[int]
) -> dict[int, FieldEligiblity]:
    sql = """
        select ppc.practice_change
        from mrv_program_practice_change ppc
        join mrv_projects p on p.program_id = ppc.program_id
        where p.id = :project_id
        and ppc.deleted_at is null
    """
    async with request.state.sql_session() as s:
        try:
            query = sqlalchemy.text(sql)
            res = await run_query(
                query=query,
                s=s,
                project_id=project_id,
            )
            records = list(res)
        except Exception as e:
            logger.error(f"failed to get practice changes for project id {project_id}, {e}")
            raise e

    result = [p[0] for p in records]
    practices = [[PracticeChange[name].value] for name in result]
    return {field_id: FieldEligiblity(eligible=True, eligible_practices=practices) for field_id in fields}


def get_tillage_intensity(tillage: str | None) -> TillageIntensity:
    if tillage is None:
        return TillageIntensity.UNKNOWN
    tillage = tillage.lower()
    if tillage == "no till":
        return TillageIntensity.NO_TILL
    if tillage == "reduced till":
        return TillageIntensity.REDUCED_TILL
    if tillage == "conventional till":
        return TillageIntensity.CONVENTIONAL_TILL
    return TillageIntensity.UNKNOWN


def get_tillage_practice_from_tillage_intensity(tillage_intensity: TillageIntensity | None) -> PracticeChange | None:
    if tillage_intensity is None:
        return None
    if tillage_intensity == TillageIntensity.NO_TILL:
        return PracticeChange.no_till
    if tillage_intensity == TillageIntensity.REDUCED_TILL:
        return PracticeChange.reduced_till
    if tillage_intensity == TillageIntensity.CONVENTIONAL_TILL:
        return PracticeChange.conventional_till
    return None


def get_irrigation_practices_from_irrigation_events(
    irrigation_events: list[EntityEvent],
) -> list[PracticeChange]:
    """
    This function assumes that all irrigation_events in input were applied to a single crop, which might not be true if
    there are multiple crops in a year (due to a cover crop or double commodity crop).

    Returns: list of practice changes, including None if an irrigation method isn't considered.
    """
    results = []
    # IrrigationMethods.alternating_wet_dry is deprecated but is included here so that we can accept data from the
    # Kellogg's Ingrained 2023 program.
    flood_events = [
        event
        for event in irrigation_events
        if event.method in {IrrigationMethods.flood, IrrigationMethods.alternating_wet_dry}
    ]

    if flood_events and flood_events_are_awd(flood_events):
        results.append(PracticeChange.alternating_wet_dry)
    else:
        irrigation_method_to_practice = {
            IrrigationMethods.flood: PracticeChange.tcf_irrigation,
            IrrigationMethods.alternating_wet_dry: PracticeChange.tcf_irrigation,
            IrrigationMethods.drip: PracticeChange.drip_irrigation,
            IrrigationMethods.furrow: PracticeChange.furrow_irrigation,
        }
        results.extend(
            [
                (irrigation_method_to_practice[event.method] if event.method in irrigation_method_to_practice else None)
                for event in irrigation_events
            ]
        )
    return results


def get_irrigation_practices_from_irrigation_events_without_converting_flood_to_awd(
    irrigation_events: list[EntityEvent],
) -> list[PracticeChange]:
    """
    Returns: list of practice changes, including None if an irrigation method isn't considered.
    """
    results = []
    irrigation_method_to_practice = {
        IrrigationMethods.flood: PracticeChange.tcf_irrigation,
        IrrigationMethods.alternating_wet_dry: PracticeChange.tcf_irrigation,
        IrrigationMethods.drip: PracticeChange.drip_irrigation,
        IrrigationMethods.furrow: PracticeChange.furrow_irrigation,
    }
    results.extend(
        [
            (irrigation_method_to_practice[event.method] if event.method in irrigation_method_to_practice else None)
            for event in irrigation_events
        ]
    )
    return results


def flood_events_are_awd(flood_events: Iterable[EntityEvent]) -> bool:
    return len(get_drydown_periods(flood_events)) > 0


def get_drydown_periods(flood_events: Iterable[EntityEvent]) -> list[tuple[datetime, datetime]]:
    previous_end_date = None
    valid_drydown_periods: list[tuple[datetime, datetime]] = []
    flood_events_with_dates = filter(
        lambda e: e.get_interval_start_or_occurred_at() is not None and e.get_interval_end_or_occurred_at() is not None,
        flood_events,
    )
    for flood_event in sorted(flood_events_with_dates, key=lambda event: event.get_interval_start_or_occurred_at()):
        # If the irrigation events overlap (bad data), we don't consider it a valid drydown, but don't throw
        if (
            previous_end_date is not None
            and flood_event.get_interval_start_or_occurred_at() - timedelta(days=5) >= previous_end_date
        ):
            valid_drydown_periods.append((previous_end_date, flood_event.get_interval_start_or_occurred_at()))
        previous_end_date = flood_event.get_interval_end_or_occurred_at()
    return valid_drydown_periods


def get_irrigation_intensity(irrigation_practice: PracticeChange | None) -> int:
    if irrigation_practice is None:
        return IrrigationIntensity.UNKNOWN
    if irrigation_practice == PracticeChange.tcf_irrigation:
        return IrrigationIntensity.TCF
    if irrigation_practice == PracticeChange.alternating_wet_dry:
        return IrrigationIntensity.AWD
    if irrigation_practice == PracticeChange.furrow_irrigation:
        return IrrigationIntensity.FURROW
    if irrigation_practice == PracticeChange.drip_irrigation:
        return IrrigationIntensity.DRIP
    return IrrigationIntensity.UNKNOWN


def get_irrigation_practice_from_irrigation_intensity(
    irrigation_intensity: IrrigationIntensity | None,
) -> PracticeChange | None:
    if irrigation_intensity is None:
        return None
    if irrigation_intensity == IrrigationIntensity.TCF:
        return PracticeChange.tcf_irrigation
    if irrigation_intensity == IrrigationIntensity.AWD:
        return PracticeChange.alternating_wet_dry
    if irrigation_intensity == IrrigationIntensity.FURROW:
        return PracticeChange.furrow_irrigation
    if irrigation_intensity == IrrigationIntensity.DRIP:
        return PracticeChange.drip_irrigation
    return None


def get_cover_crop_intensity(cover_crop: str | None, wheat_winter_as_no_cover: bool) -> CoverCropType:
    if cover_crop is None:
        return CoverCropType.UNKNOWN
    cover_crop = cover_crop.lower()
    # "no cover" is a legacy name for "fallow" in the UI
    # see flurosense-ui/src/containers/mrv/enrollment/base/crop-practice.tsx
    no_cover = ["fallow", "no cover"]

    # wheat_winter is considered as fallow for eligibility so group them together
    # but for cargill Y4 we want to consider wheat_winter as a regular crop
    # https://regrow.atlassian.net/browse/MRV-2180
    if wheat_winter_as_no_cover:
        no_cover.append("wheat_winter")

    if cover_crop in no_cover or cover_crop.startswith("no cover"):
        return CoverCropType.NO_COVER_CROP
    # "full cover" the legacy name for "full_season_cover_crop", but both can exist
    if "full" in cover_crop:
        return CoverCropType.FULL_COVER_CROP
    if "basic" in cover_crop:
        return CoverCropType.BASIC_COVER_CROP
    if "premium" in cover_crop:
        return CoverCropType.PREMIUM_COVER_CROP
    # cover crop is some other value that we haven't accounted for
    if cover_crop != "":
        return CoverCropType.FULL_COVER_CROP
    # cover crop value is null
    return CoverCropType.UNKNOWN


def get_cover_crop_practice_from_cover_crop_type(cover_crop_type: CoverCropType | None) -> PracticeChange | None:
    if cover_crop_type is None:
        return None
    if cover_crop_type == CoverCropType.NO_COVER_CROP:
        return PracticeChange.no_cover_crop
    if cover_crop_type == CoverCropType.FULL_COVER_CROP:
        return PracticeChange.cover_crops
    if cover_crop_type == CoverCropType.BASIC_COVER_CROP:
        return PracticeChange.basic_cover_crops
    if cover_crop_type == CoverCropType.PREMIUM_COVER_CROP:
        return PracticeChange.premium_cover_crops
    return None


async def get_eligibility_method_by_project_phase(
    request: Request, project_id: int, phase: PhaseTypes
) -> EligibilityTypes | None:
    query = (
        select(Stage.eligibility_method)
        .join(Phases, Phases.id == Stage.phase_id)
        .join(Programs, Programs.id == Phases.program_id)
        .join(Projects, Projects.program_id == Programs.id)
        .where(Projects.id == project_id)
        .where(Phases.type_ == phase)
        .where(Stage.type_ == StageTypes.ASSIGN_PRACTICES)
    )
    try:
        async with request.state.sql_session() as s:
            res = (await run_query(query=query, s=s, project_id=project_id, phase=phase)).one()
        return res[0]
    except Exception:
        return None


def translate_practices(values: dict[str, str], locale: Locale) -> dict[StageTypes, str]:
    lookup = {}
    for value in PracticeChange:
        lookup[value] = translate_message(value, locale)

    if StageTypes.ASSIGN_PRACTICES.value in values:
        practices = values[StageTypes.ASSIGN_PRACTICES.value]
        practice_values = practices.split(", ")

        try:
            translated_values = [lookup[pv] for pv in practice_values]
            values[StageTypes.ASSIGN_PRACTICES.value] = ", ".join(translated_values)
        except Exception:
            return values

    return values


# use this function to parse the csv and insert into the mrv_lut table
def initialise_cargill_eu_lut(session: Session, year: int) -> None:
    try:
        records: list[LUT] = []
        with open(
            settings.PROJECT_PATH / "projects" / "eligibility" / f"cargill_eu_{year}_lut.csv".format(year=year)
        ) as csv_file:
            reader = csv.DictReader(csv_file)
            for row in reader:
                records.append(LUT(year=year, **row))
        session.execute(delete(LUT).where(LUT.year == year))
        session.bulk_save_objects(records)
        session.commit()
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def get_field_values(values: list[HistoricPracticeResult]) -> dict[StageTypes, dict[int, dict[AttributeTypes, str]]]:
    rows: dict[StageTypes, dict[int, dict[AttributeTypes, str]]] = {}
    for value in values:
        if value.stage not in rows:
            rows[value.stage] = {}
        if value.row_id not in rows[value.stage]:
            rows[value.stage][value.row_id] = {}
        rows[value.stage][value.row_id][value.attribute_type] = value.value
    return rows


def get_tillage_practice_from_tillage_event(
    event: TillageEvent, fallback_values: list[HistoricPracticeResult]
) -> str | None:
    """
    Get the tillage practice from the event or fallback values
    Currently TillageEvent does not contain tillage practice, so we are using fallback values
    The decision of adding tillage practice to TillageEvent or not will be made later on
    """
    if hasattr(event, "tillage_practice") and event.tillage_practice is not None:
        # get the tillage intensity from the event (not been added to the TillageEvent yet)
        return event.tillage_practice
    else:
        # get the tillage intensity fallback from values
        rows: dict[int, list[HistoricPracticeResult]] = {}
        for value in fallback_values:
            if value.stage != StageTypes.HISTORICAL_TILLAGE:
                continue
            if value.row_id not in rows:
                rows[value.row_id] = []
            rows[value.row_id].append(value)

        # find matching row containing tillage date
        matching_row = None
        for row in rows.values():
            for row_value in row:
                if (
                    row_value.attribute_type == AttributeTypes.tillage_date
                    and row_value.value is not None
                    and parse_datetime_as_utc(row_value.value) == event.occurred_at
                ):
                    matching_row = row
                    break

        if matching_row is None:
            logger.info("No matching tillage date found in fallback values for event", event)
            return None

        for value in matching_row:
            if value.attribute_type == AttributeTypes.tillage_practice:
                logger.debug("using fallback tillage practice: %s for TillageEvent: %s", value.value, event)
                return value.value

        logger.info("No matching tillage practice found in fallback values for event", event)
        return None


def map_date_to_string(date: str, interval_map: dict[Tuple[str, str], str]) -> str | None:
    date_format = "%Y-%m-%d"
    """
    Map date string with %Y-%m-%d format to string based on the inclusive intervals provided
    example usage:
        map_date_to_string(
            date=planting_date,
            interval_map={
                ("2023-07-01", "2023-12-31"): Season.WINTER,
                ("2024-01-01", "2024-06-01"): Season.SUMMER,
            },
        )
    :param date: string in format Y-m-d or Y-m-d H:M:S:f
    :param interval_map: dictionary with keys as tuples of start and end dates and values as strings
    :return: mapped string if date falls in one of the interval or None if no interval matches
    """
    # need to account for both Y-m-d and Y-m-d H:M:S:f cases
    parsed_date = datetime.strptime(date.split("T")[0], date_format)
    result = None
    for (start, end), value in interval_map.items():
        start_date = datetime.strptime(start, date_format)
        end_date = datetime.strptime(end, date_format)
        if start_date <= parsed_date <= end_date:
            result = value
            break
    return result


def get_barley_type_by_planting_date(planting_date: str) -> str | None:
    """
    Get the barley type based on the planting date, this is used for EU Y2 and Y3 programs
    Since winter barley and spring barley have different GHG values in the LUT
    :param planting_date:
    :return: Barley.WINTER_BARLEY or Barley.SPRING_BARLEY or None
    """
    arbitrary_year = 2000  # comparing only month and day
    date_with_arbitrary_year = (
        datetime.strptime(planting_date.split("T")[0], DATE_FORMAT).replace(year=arbitrary_year).strftime(DATE_FORMAT)
    )
    return map_date_to_string(
        date=date_with_arbitrary_year,
        interval_map={
            (
                f"{arbitrary_year}-01-01",
                f"{arbitrary_year}-06-30",
            ): Barley.SPRING_BARLEY,
            (
                f"{arbitrary_year}-07-01",
                f"{arbitrary_year}-12-31",
            ): Barley.WINTER_BARLEY,
        },
    )


def get_cultivation_cycle_year(event: EntityEvent) -> int | None:
    """
    Returns the cultivation cycle year for a given event e.g.
    Tillage event happened 15th Mar 2024 count towards a Spring Tillage of the crop year 2023.
    Tillage event happened 15th Aug 2024 will be count towards a Fall Tillage of the crop year 2024
    This applies to US Y4 and EU Y3 programs so far.
    Check requirement if you want to use it for other programs
    """
    event_time = event.get_interval_start_or_occurred_at()
    if event_time is None:
        return None

    if event_time.strftime("%m-%d") < "06-01":
        return event_time.year - 1
    return event_time.year


async def get_disallowed_eligible_practices(
    request: Request, program_id: int, field_eligibilities: dict[int, FieldEligiblity]
) -> set:
    from programs.methods import get_practice_change_set_by_program_id

    allowed_practice_set = await get_practice_change_set_by_program_id(request=request, program_id=program_id)
    eligible_practice_set = set()
    for field_eligibility in field_eligibilities.values():
        for eligible_practice_combination in field_eligibility.eligible_practices:
            eligible_practice_set.update(set(eligible_practice_combination))
    return eligible_practice_set - allowed_practice_set


def derive_cultivation_cycles_for_legacy(events: list[EntityEvent]) -> list[CultivationCycle]:
    """
    Generate cultivation cycles based on record year for legacy programs
    This is intended to be used for legacy programs that are not migrated to EBDC
    e.g. program 190, 1189, 1190
    """
    # Group events by entity_id and year
    events_by_entity_and_year: dict[int, dict[int, list[EntityEvent]]] = defaultdict(lambda: defaultdict(list))
    for event in events:
        year = (event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()).year
        events_by_entity_and_year[event.entity_id][year].append(event)

    # Create cultivation cycles from record year
    all_cultivation_cycles = []
    for entity_events in events_by_entity_and_year.values():
        for year_events in entity_events.values():
            all_dates = [
                e.get_interval_start_or_occurred_at() for e in year_events if e.get_interval_start_or_occurred_at()
            ] + [e.get_interval_end_or_occurred_at() for e in year_events if e.get_interval_end_or_occurred_at()]
            start_date = min(all_dates)
            end_date = max(all_dates)
            all_cultivation_cycles.append(
                CultivationCycle(
                    id=CultivationCycleId(crop_event_id=None, crop_type=None, start_date=start_date, end_date=end_date),
                    start=start_date,
                    end=end_date,
                    events=year_events,
                )
            )
    return all_cultivation_cycles


async def get_legacy_entity_events_for_enrollment(request: Request, project_id: int) -> list[CultivationCycle]:
    # Get events for the current phase
    events = await get_entity_events_for_project_phase(
        request=request,
        project_id=project_id,
        phase_type=PhaseTypes.ENROLMENT,
        entity_type=EntityTypeChoices.field,
        event_creation_specification=EventCreationSpecification(error_handling=EventCreatorErrorHandling.SKIP_EVENT),
    )

    # build cultivation cycles and return
    return derive_cultivation_cycles_for_legacy(events=events)


async def translate_core_crop_to_regrow_name(core_crop_name: str | None) -> str | None:
    """
    Translate the name of a Core crop, which is what MRV stores in MRV Values for the crop_type attribute, into a
    RegrowName of the corresponding DefaultService crop, or None if the crop is not found in Defaults Service.
    """
    if core_crop_name is None:
        return None
    try:
        return await defaults_retriever.translate_core_crop_to_regrow_name(core_crop_name)
    except NotImplementedError:
        logger.warning(f"Core crop {core_crop_name} not found in Defaults Service crop_translation.")
        return None
