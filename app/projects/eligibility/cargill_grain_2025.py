from fastapi import Request

from cultivation_cycles.methods import get_cultivation_cycles
from defaults.attribute_options import CropUsage
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.tillage_event import TillageEvent
from fields.baselines.methods import get_or_create_fields_baseline_years
from fields.db import get_fields_by_project_id
from logger import get_logger
from programs.db import get_program_by_project_id
from programs.enums import PracticeChange
from projects.eligibility.enums import CoverCropType, TillageIntensity
from projects.eligibility.helper import (
    get_cover_crop_intensity,
    get_tillage_intensity,
    get_tillage_practice_from_tillage_event,
)
from projects.eligibility.schema import PracticeChangeWithBaseline
from projects.schema import FieldEligiblity
from ses_integration.fetch_entity_events import fetch_events_for_field_phase

logger = get_logger(__name__)

REQUIRED_HISTORICAL_CROPS = [
    "corn",
    "soybeans",  # core crop name
    "soybean",  # regrow crop name
    "sunflowers",  # core crop name
    "sunflower",  # regrow crop name
    "wheat_spring",
    "sorghum",
    "flax",
    "wheat_winter",
    "cotton",
]

ELIGIBILITY_MATRIX: dict[tuple[int, int], list[list[PracticeChange]]] = {
    (TillageIntensity.NO_TILL, CoverCropType.NO_COVER_CROP): [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
    ],
    (TillageIntensity.NO_TILL, CoverCropType.BASIC_COVER_CROP): [],
    (TillageIntensity.REDUCED_TILL, CoverCropType.NO_COVER_CROP): [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.no_till],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
    ],
    (TillageIntensity.REDUCED_TILL, CoverCropType.BASIC_COVER_CROP): [
        [PracticeChange.no_till, PracticeChange.cover_crops],
    ],
    (TillageIntensity.CONVENTIONAL_TILL, CoverCropType.NO_COVER_CROP): [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.no_till],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
        [PracticeChange.reduced_till],
        [PracticeChange.cover_crops],
    ],
    (TillageIntensity.CONVENTIONAL_TILL, CoverCropType.BASIC_COVER_CROP): [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
    ],
}


def run_crop_type_eligibility(events: list[EntityEvent]) -> bool:
    for event in events:
        if not isinstance(event, CroppingEvent):
            continue

        if event.crop_usage == CropUsage.COMMODITY and event.crop_type in REQUIRED_HISTORICAL_CROPS:
            return True
    return False


# https://regrow.atlassian.net/browse/MRV-5416 - CargillRegenConnect US Y5 2025 - Eligibility logic is the same as year 3 and year 4 (program 155, 1119).
# based on logic presented in https://regrow.atlassian.net/wiki/spaces/PROD/pages/2695856196/Eligibility+on+practices+change+-+CargillRegenConnect+US+Y3+2023
# with the difference being we switched to cultivation cycle
def run_fields_eligibility(baseline_year_events: list[EntityEvent]) -> PracticeChangeWithBaseline:
    most_intense_tillage = TillageIntensity.UNKNOWN
    cc = CoverCropType.UNKNOWN

    logger.debug("baseline year events: %s", baseline_year_events)
    for event in baseline_year_events:
        if isinstance(event, TillageEvent):
            # no need for fallback as we have fully switched to cultivation cycle
            tillage_practice = get_tillage_practice_from_tillage_event(event=event, fallback_values=[])
            mapped_tillage: int = get_tillage_intensity(tillage=tillage_practice)
            most_intense_tillage = max(most_intense_tillage, mapped_tillage)
        elif isinstance(event, CroppingEvent) and event.crop_usage == CropUsage.COVER:
            cc = get_cover_crop_intensity(cover_crop=event.crop_type, wheat_winter_as_no_cover=False)

    logger.debug("till: %s, cc: %s", most_intense_tillage, cc)

    # If no tillage event found, consider it as NO_TILL
    if most_intense_tillage == TillageIntensity.UNKNOWN:
        most_intense_tillage = TillageIntensity.NO_TILL

    # If not cover crop is found, consider it as NO_COVER_CROP
    if cc == CoverCropType.UNKNOWN:
        cc = CoverCropType.NO_COVER_CROP

    # Y4 has switched to using Basic cover crop as a generic cover crop type,
    # however SE still returns Full cover if a particular crop type is selected
    # https://regrow.atlassian.net/wiki/spaces/PROD/pages/3088351412/Mar+24+Using+Full+cover+to+Basic+cover+as+generic+cover+crop+crop+type+in+MRV+programs
    if cc == CoverCropType.FULL_COVER_CROP:
        cc = CoverCropType.BASIC_COVER_CROP

    return PracticeChangeWithBaseline(
        allowed_practice_changes=ELIGIBILITY_MATRIX.get((most_intense_tillage, cc), []),
        baseline_cover_crop=cc,
        baseline_tillage=most_intense_tillage,
    )


async def run_cargill_grain_2025(request: Request, project_id: int) -> dict[int, FieldEligiblity]:
    fields = await get_fields_by_project_id(request=request, project_id=project_id)
    fields_eligibility: dict[int, FieldEligiblity] = {field.id: FieldEligiblity() for field in fields}
    fields_baseline_years: dict[int, int] = await get_or_create_fields_baseline_years(
        request=request, field_ids=[field.id for field in fields]
    )

    program = await get_program_by_project_id(request=request, project_id=project_id)

    for field in fields:
        try:
            events = await fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=True,
                is_single_phase_program=program.is_single_phase_data_collection,
            )
            events = [event for event in events if not isinstance(event, FallowPeriod)]

        except Exception as e:
            logger.warning("Error getting Structured Events for field %s: %s", field.id, e)
            fields_eligibility[field.id].message = (
                "The field is not eligible because there are issues with the management history data. "
                "Please return to the previous screens and recheck that all information is entered correctly. "
                "Please refer to the timeline and the tables to check that all data entered makes agronomic sense."
            )
            fields_eligibility[field.id].title = "Structured Events"
            continue

        if not (run_crop_type_eligibility(events=events)):
            fields_eligibility[field.id].message = (
                "This field is not eligible as none of the required historic crop types were grown."
            )
            fields_eligibility[field.id].crop_types = REQUIRED_HISTORICAL_CROPS
            fields_eligibility[field.id].title = "Historic Crops"
            continue

        logger.debug("field_id: %s", field.id)

        # calculate baseline
        baseline_year = fields_baseline_years[field.id]
        cultivation_cycles, _ = get_cultivation_cycles(events=events)
        baseline_cultivation_cycle = next(
            (cycle for cycle in cultivation_cycles if cycle.end.year == baseline_year), None
        )
        baseline_year_events: list[EntityEvent] = []
        if baseline_cultivation_cycle:
            baseline_year_events = [event for event in baseline_cultivation_cycle.events if not event.is_intended]

        logger.debug("field_id: %s with baseline year: %s", field.id, baseline_year)

        allowed_practices_with_baseline = run_fields_eligibility(baseline_year_events=baseline_year_events)
        allowed_practices = allowed_practices_with_baseline.allowed_practice_changes
        fields_eligibility[field.id].baseline_cover_crop = allowed_practices_with_baseline.baseline_cover_crop
        fields_eligibility[field.id].baseline_tillage = allowed_practices_with_baseline.baseline_tillage
        if allowed_practices:
            fields_eligibility[field.id].eligible_practices = allowed_practices
        else:
            fields_eligibility[field.id].message = "This field is not eligible for any practice changes."
            fields_eligibility[field.id].title = "Practice Changes"

    return fields_eligibility
