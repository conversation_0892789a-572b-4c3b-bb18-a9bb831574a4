from datetime import datetime, timezone
from unittest.mock import patch

import pytest

from defaults.attribute_options import CropUsage
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from entity_events.units import LengthUnit
from programs.enums import PracticeChange
from projects.eligibility.cargill_grain_2025 import (
    run_cargill_grain_2025,
    run_crop_type_eligibility,
    run_fields_eligibility,
)
from projects.eligibility.enums import CoverCropType, TillageIntensity
from projects.eligibility.schema import PracticeChangeWithBaseline
from values.enums import EntityTypeChoices


def test_run_crop_type_eligibility(faker):
    field_id = faker.unique.random_number(3)
    for crop_type in [
        "corn",
        "soybeans",
        "soybean",
        "sunflowers",
        "sunflower",
        "wheat_spring",
        "sorghum",
        "flax",
        "wheat_winter",
        "cotton",
    ]:
        events = [
            CroppingEvent(
                entity_id=field_id,
                entity_type=EntityTypeChoices.field,
                interval=Interval(
                    start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    end=datetime(2023, 10, 1, tzinfo=timezone.utc),
                ),
                crop_type=crop_type,
                crop_usage=CropUsage.COMMODITY,
            )
        ]
        assert run_crop_type_eligibility(events=events) is True

    events = [
        CroppingEvent(
            entity_id=field_id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                end=datetime(2023, 10, 1, tzinfo=timezone.utc),
            ),
            crop_type="barley",
            crop_usage=CropUsage.COMMODITY,
        )
    ]
    assert run_crop_type_eligibility(events=events) is False

    events = [
        CroppingEvent(
            entity_id=field_id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                end=datetime(2023, 10, 1, tzinfo=timezone.utc),
            ),
            crop_type="corn",
            crop_usage=CropUsage.COVER,
        )
    ]
    assert run_crop_type_eligibility(events=events) is False

    events = [
        TillageEvent(
            entity_id=field_id,
            entity_type=EntityTypeChoices.field,
            occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
        )
    ]
    assert run_crop_type_eligibility(events=events) is False

    assert run_crop_type_eligibility(events=[]) is False


@pytest.mark.parametrize(
    "baseline_year_events,expected",
    [
        # no till, no cover crop
        (
            [
                TillageEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    tillage_practice="no till",
                    soil_inversion=False,
                ),
            ],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                ],
                baseline_cover_crop=CoverCropType.NO_COVER_CROP,
                baseline_tillage=TillageIntensity.NO_TILL,
            ),
        ),
        # reduced till, no cover crop
        (
            [
                TillageEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    tillage_practice="reduced till",
                    soil_inversion=False,
                    depth={"value": 10, "unit": LengthUnit.CENTIMETRE},
                ),
            ],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                    [PracticeChange.no_till],
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                ],
                baseline_cover_crop=CoverCropType.NO_COVER_CROP,
                baseline_tillage=TillageIntensity.REDUCED_TILL,
            ),
        ),
        # conventional till, no cover crop
        (
            [
                TillageEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    tillage_practice="conventional till",
                    soil_inversion=True,
                ),
            ],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                    [PracticeChange.no_till],
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                    [PracticeChange.reduced_till],
                    [PracticeChange.cover_crops],
                ],
                baseline_cover_crop=CoverCropType.NO_COVER_CROP,
                baseline_tillage=TillageIntensity.CONVENTIONAL_TILL,
            ),
        ),
        # no till, cover crop
        (
            [
                TillageEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    tillage_practice="no till",
                    soil_inversion=False,
                ),
                CroppingEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2023, 9, 1, tzinfo=timezone.utc),
                        end=datetime(2024, 3, 1, tzinfo=timezone.utc),
                    ),
                    crop_type="rye",
                    crop_usage=CropUsage.COVER,
                ),
            ],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[],
                baseline_cover_crop=CoverCropType.BASIC_COVER_CROP,
                baseline_tillage=TillageIntensity.NO_TILL,
            ),
        ),
        # reduced till, cover crop
        (
            [
                TillageEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
                    tillage_practice="reduced till",
                    soil_inversion=False,
                ),
                CroppingEvent(
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2023, 9, 1, tzinfo=timezone.utc),
                        end=datetime(2024, 3, 1, tzinfo=timezone.utc),
                    ),
                    crop_type="rye",
                    crop_usage=CropUsage.COVER,
                ),
            ],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                ],
                baseline_cover_crop=CoverCropType.BASIC_COVER_CROP,
                baseline_tillage=TillageIntensity.REDUCED_TILL,
            ),
        ),
        # empty events
        (
            [],
            PracticeChangeWithBaseline(
                allowed_practice_changes=[
                    [PracticeChange.no_till, PracticeChange.cover_crops],
                    [PracticeChange.reduced_till, PracticeChange.cover_crops],
                ],
                baseline_cover_crop=CoverCropType.NO_COVER_CROP,
                baseline_tillage=TillageIntensity.NO_TILL,
            ),
        ),
    ],
)
def test_run_fields_eligibility(baseline_year_events, expected):
    result = run_fields_eligibility(baseline_year_events=baseline_year_events)
    assert result == expected


@patch("projects.eligibility.cargill_grain_2025.fetch_events_for_field_phase")
async def test_run_cargill_grain_2025(mock_fetch_events, mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_3 = await mdl.Fields(parent_project_id=project.id)

    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2023, is_returning=True)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2023, is_returning=True)
    await mdl.FieldsBaseline(field_id=field_3.id, baseline_year=2023, is_returning=True)

    field_1_events = [
        CroppingEvent(
            entity_id=field_1.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                end=datetime(2023, 10, 1, tzinfo=timezone.utc),
            ),
            crop_type="corn",
            crop_usage=CropUsage.COMMODITY,
        ),
        TillageEvent(
            entity_id=field_1.id,
            entity_type=EntityTypeChoices.field,
            occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
            tillage_practice="no till",
            soil_inversion=False,
        ),
    ]

    field_2_events = [
        CroppingEvent(
            entity_id=field_2.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                end=datetime(2023, 10, 1, tzinfo=timezone.utc),
            ),
            crop_type="barley",
            crop_usage=CropUsage.COMMODITY,
        ),
    ]

    field_3_events = [
        CroppingEvent(
            entity_id=field_3.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 5, 1, tzinfo=timezone.utc),
                end=datetime(2023, 10, 1, tzinfo=timezone.utc),
            ),
            crop_type="corn",
            crop_usage=CropUsage.COMMODITY,
        ),
        TillageEvent(
            entity_id=field_3.id,
            entity_type=EntityTypeChoices.field,
            occurred_at=datetime(2023, 5, 1, tzinfo=timezone.utc),
            tillage_practice="no till",
            soil_inversion=False,
        ),
        CroppingEvent(
            entity_id=field_3.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2023, 9, 1, tzinfo=timezone.utc),
                end=datetime(2024, 3, 1, tzinfo=timezone.utc),
            ),
            crop_type="rye",
            crop_usage=CropUsage.COVER,
        ),
    ]

    mock_fetch_events.side_effect = lambda request, field_id, enrollment_phase_only, is_single_phase_program: {
        field_1.id: field_1_events,
        field_2.id: field_2_events,
        field_3.id: field_3_events,
    }[field_id]

    result = await run_cargill_grain_2025(request=app_request, project_id=project.id)
    assert result[field_1.id].baseline_tillage == TillageIntensity.NO_TILL
    assert result[field_1.id].baseline_cover_crop == CoverCropType.NO_COVER_CROP
    assert result[field_1.id].eligible_practices == [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
    ]

    assert (
        result[field_2.id].message
        == "This field is not eligible as none of the required historic crop types were grown."
    )
    assert result[field_2.id].title == "Historic Crops"

    assert field_3.id in result
    assert result[field_3.id].eligible_practices == [
        [PracticeChange.no_till, PracticeChange.cover_crops],
        [PracticeChange.reduced_till, PracticeChange.cover_crops],
    ]
    assert result[field_3.id].baseline_tillage == TillageIntensity.NO_TILL
    assert result[field_3.id].baseline_cover_crop == CoverCropType.NO_COVER_CROP


@patch("projects.eligibility.cargill_grain_2025.fetch_events_for_field_phase")
async def test_run_cargill_grain_2025_with_exception(mock_fetch_events, mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2023, is_returning=True)

    mock_fetch_events.side_effect = Exception("Test exception")
    result = await run_cargill_grain_2025(request=app_request, project_id=project.id)
    assert result[field.id].message == (
        "The field is not eligible because there are issues with the management history data. "
        "Please return to the previous screens and recheck that all information is entered correctly. "
        "Please refer to the timeline and the tables to check that all data entered makes agronomic sense."
    )
    assert result[field.id].title == "Structured Events"
