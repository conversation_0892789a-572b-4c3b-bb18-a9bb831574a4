from helper import external_api_mocked_responses
from phases.enums import CommodityCropCheckChoice, PracticeComparisonYearsChoice
from phases.schema import CommonEligibilityConfig
from projects.eligibility.classes.common_eligibility import CommonEligibility
from projects.eligibility.tests.setup_events_data import setup_events_data
from projects.practice.schema import Entity<PERSON>ractice
from projects.practice.tillage_generator.enums import TillageGeneratorType
from values.enums import EntityTypeChoices


async def test_common_eligibility_intended_commodity_crop_dependency(mdl, app_request, mocker) -> None:
    mocker.patch(
        "projects.eligibility.classes.base_eligibility.core_field_counties_get_api",
        new=external_api_mocked_responses.mocked_core_field_counties_get_api,
    )
    program_data = await setup_events_data(mdl)

    historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
        ]
        for field_id in program_data.field_ids
    }

    commodity_historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    commodity_intended_practices = {
        field_id: [
            EntityPractice(year=2025, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    def new_common_eligibility() -> CommonEligibility:
        common_eligibility_obj = CommonEligibility(
            request=app_request,
            baseline_years={field_id: 2024 for field_id in program_data.field_ids},
            historical_practices=historical_practices,
        )
        common_eligibility_obj.set_historical_and_intended_commodity_crop(
            historical_commodity_crop=commodity_historical_practices,
            intended_commodity_crop=commodity_intended_practices,
        )
        return common_eligibility_obj

    # Matching one specific configured crop
    common_eligibility = new_common_eligibility()
    await common_eligibility.set_matched_commodity_crop_years(default_crop_match="soybeans")
    for field_id in program_data.field_ids:
        assert common_eligibility.matched_commodity_crop_years[field_id] == {2022, 2023}
        filtered_practices = common_eligibility.filter_historical_practices(
            entity_id=field_id, historical_years_to_check=4, filter_with_commodity_crops_check=True
        )
        assert {practice.year for practice in filtered_practices} == {2022, 2023}

    # Matching intended commodity crop
    common_eligibility = new_common_eligibility()
    await common_eligibility.set_matched_commodity_crop_years(default_crop_match=None)
    for field_id in program_data.field_ids:
        assert common_eligibility.matched_commodity_crop_years[field_id] == {2021, 2024}
        filtered_practices = common_eligibility.filter_historical_practices(
            entity_id=field_id, historical_years_to_check=4, filter_with_commodity_crops_check=True
        )
        assert {practice.year for practice in filtered_practices} == {2021, 2024}

    # Any
    common_eligibility = new_common_eligibility()
    for field_id in program_data.field_ids:
        filtered_practices = common_eligibility.filter_historical_practices(
            entity_id=field_id, historical_years_to_check=4, filter_with_commodity_crops_check=False
        )
        assert {practice.year for practice in filtered_practices} == {2021, 2022, 2023, 2024}


async def test_common_eligibility_eligible_intended_commodity_crops(mdl, app_request, mocker) -> None:
    mocker.patch(
        "projects.eligibility.classes.base_eligibility.core_field_counties_get_api",
        new=external_api_mocked_responses.mocked_core_field_counties_get_api,
    )
    program_data = await setup_events_data(mdl)

    historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
        ]
        for field_id in program_data.field_ids
    }

    commodity_historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    common_eligibility_config = CommonEligibilityConfig(
        local_common_practice_source=None,
        practice_comparison_years=PracticeComparisonYearsChoice.ANY,
        historical_years_to_check=4,
        enabled=True,
        version=TillageGeneratorType.TILLAGE_GENERATOR_V1,
        commodity_crops_check=CommodityCropCheckChoice.FILTER_BASELINE,
        eligible_intended_commodity_crops="corn",
    )

    commodity_intended_practices = {
        field_id: [
            EntityPractice(year=2025, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    def new_common_eligibility() -> CommonEligibility:
        common_eligibility_obj = CommonEligibility(
            request=app_request,
            baseline_years={field_id: 2024 for field_id in program_data.field_ids},
            historical_practices=historical_practices,
        )
        common_eligibility_obj.set_historical_and_intended_commodity_crop(
            historical_commodity_crop=commodity_historical_practices,
            intended_commodity_crop=commodity_intended_practices,
        )
        return common_eligibility_obj

    # Matching one specific configured crop
    common_eligibility = new_common_eligibility()
    for field_id in program_data.field_ids:
        practice_with_errors = await common_eligibility.compare_historical_practices(
            entity_id=field_id,
            eligibility_config=common_eligibility_config,
            enabled_practices_having_practice_type=[],
            compare_allowed_better_than_historical=None,
        )
        assert "ineligible intended commodity crop" not in practice_with_errors.error_message

    # Not matching one specific configured crop
    common_eligibility_config.eligible_intended_commodity_crops = "soybeans"
    common_eligibility = new_common_eligibility()
    for field_id in program_data.field_ids:
        practice_with_errors = await common_eligibility.compare_historical_practices(
            entity_id=field_id,
            eligibility_config=common_eligibility_config,
            enabled_practices_having_practice_type=[],
            compare_allowed_better_than_historical=None,
        )
        assert "ineligible intended commodity crop" in practice_with_errors.error_message


async def test_common_eligibility_intended_commodity_crops_present(mdl, app_request, mocker) -> None:
    mocker.patch(
        "projects.eligibility.classes.base_eligibility.core_field_counties_get_api",
        new=external_api_mocked_responses.mocked_core_field_counties_get_api,
    )
    program_data = await setup_events_data(mdl)

    historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="no_till"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="reduced_till"),
        ]
        for field_id in program_data.field_ids
    }

    commodity_historical_practices = {
        field_id: [
            EntityPractice(year=2021, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
            EntityPractice(year=2022, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2023, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="soybeans"),
            EntityPractice(year=2024, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    common_eligibility_config = CommonEligibilityConfig(
        local_common_practice_source=None,
        practice_comparison_years=PracticeComparisonYearsChoice.ANY,
        historical_years_to_check=4,
        enabled=True,
        version=TillageGeneratorType.TILLAGE_GENERATOR_V1,
        commodity_crops_check=CommodityCropCheckChoice.PRESENT_IN_HISTORY,
    )

    commodity_intended_practices = {
        field_id: [
            EntityPractice(year=2025, entity_id=field_id, entity_type=EntityTypeChoices.field, practice="corn"),
        ]
        for field_id in program_data.field_ids
    }

    def new_common_eligibility() -> CommonEligibility:
        common_eligibility_obj = CommonEligibility(
            request=app_request,
            baseline_years={field_id: 2024 for field_id in program_data.field_ids},
            historical_practices=historical_practices,
        )
        common_eligibility_obj.set_historical_and_intended_commodity_crop(
            historical_commodity_crop=commodity_historical_practices,
            intended_commodity_crop=commodity_intended_practices,
        )
        return common_eligibility_obj

    # Matching one specific configured crop
    common_eligibility = new_common_eligibility()
    for field_id in program_data.field_ids:
        practice_with_errors = await common_eligibility.compare_historical_practices(
            entity_id=field_id,
            eligibility_config=common_eligibility_config,
            enabled_practices_having_practice_type=[],
            compare_allowed_better_than_historical=None,
        )
        assert "intended commodity crop is not present" not in practice_with_errors.error_message

    # Not matching one specific configured crop
    common_eligibility = new_common_eligibility()

    # set unmatched intended commodity crop
    for practices in commodity_intended_practices.values():
        for practice in practices:
            practice.practice = "canola"

    for field_id in program_data.field_ids:
        practice_with_errors = await common_eligibility.compare_historical_practices(
            entity_id=field_id,
            eligibility_config=common_eligibility_config,
            enabled_practices_having_practice_type=[],
            compare_allowed_better_than_historical=None,
        )
        assert "intended commodity crop is not present" in practice_with_errors.error_message
