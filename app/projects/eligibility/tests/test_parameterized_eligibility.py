from phases.enums import PracticeRuleConditions, PracticeRuleTypes
from programs.enums import PracticeChange
from projects.eligibility.eligibility import (
    flatten_eligible_practices,
    remove_impossible_practices,
)
from projects.schema import FieldEligiblity


def test_flatten_eligible_practices() -> None:

    # No always eligible practices
    eligibilities = {
        1: FieldEligiblity(eligible=True, eligible_practices=[[PracticeChange.nutrient_management]]),
        2: FieldEligiblity(eligible=False, eligible_practices=[]),
    }
    result = flatten_eligible_practices(eligibilities, [])

    assert result[1].eligible is True
    assert result[1].eligible_practices == [[PracticeChange.nutrient_management]]
    assert result[2].eligible is False
    assert result[2].eligible_practices == []

    # With always eligible practices
    eligibilities = {
        1: FieldEligiblity(eligible=True, eligible_practices=[[PracticeChange.nutrient_management]]),
        2: FieldEligiblity(eligible=False, eligible_practices=[]),
    }
    result = flatten_eligible_practices(eligibilities, [PracticeChange.reduced_till])
    assert result[1].eligible is True
    assert {practice for combination in result[1].eligible_practices for practice in combination} == {
        PracticeChange.nutrient_management,
        PracticeChange.reduced_till,
    }
    assert result[2].eligible is True
    assert result[2].eligible_practices == [[PracticeChange.reduced_till]]

    # hard ineligible fields remains ineligible even if there are always eligible practices
    eligibilities = {
        1: FieldEligiblity(eligible=False, eligible_practices=[], hard_ineligible=True),
    }
    result = flatten_eligible_practices(eligibilities, [PracticeChange.reduced_till])
    assert result[1].eligible is False
    assert result[1].eligible_practices == []


async def test_remove_impossible_practices(app_request, mdl) -> None:
    stage_id = (await mdl.Stage()).id
    await mdl.PracticeChangeRules(
        stage_id=stage_id,
        rule={
            "type": PracticeRuleTypes.PRACTICE_DEPENDENCY,
            "condition": PracticeRuleConditions.ALL,
            "target": PracticeChange.nutrient_management,
            "value": [PracticeChange.reduced_till, PracticeChange.cover_crops],
        },
    )
    await mdl.PracticeChangeRules(
        stage_id=stage_id,
        rule={
            "type": PracticeRuleTypes.PRACTICE_DEPENDENCY,
            "condition": PracticeRuleConditions.ANY,
            "target": PracticeChange.split_application,
            "value": [PracticeChange.reduced_till, PracticeChange.cover_crops],
        },
    )
    await mdl.PracticeChangeRules(
        stage_id=stage_id,
        rule={
            "type": PracticeRuleTypes.PRACTICE_DEPENDENCY,
            "condition": PracticeRuleConditions.ALL,
            "target": PracticeChange.residue_removal,
            "value": [PracticeChange.nutrient_management],
        },
    )
    # Test cases for the new rules
    eligibilities = {
        1: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.reduced_till],
                [PracticeChange.nutrient_management],
            ],  # NM is impossible
        ),
        2: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.nutrient_management],
                [PracticeChange.split_application],
            ],  # Both impossible
        ),
        3: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.nutrient_management],
                [PracticeChange.split_application],
                [PracticeChange.reduced_till, PracticeChange.cover_crops],
            ],  # All possible
        ),
        4: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.residue_removal],
                [PracticeChange.nutrient_management],
            ],  # NM impossible cascading cause residue_removal impossible as well
        ),
    }

    result = await remove_impossible_practices(app_request, eligibilities, stage_id)

    assert result[1].eligible is True
    assert result[1].eligible_practices == [[PracticeChange.reduced_till]]
    assert result[2].eligible is False
    assert result[2].eligible_practices == []
    assert result[3].eligible is True
    assert {practice for combination in result[3].eligible_practices for practice in combination} == {
        PracticeChange.nutrient_management,
        PracticeChange.split_application,
        PracticeChange.reduced_till,
        PracticeChange.cover_crops,
    }
    assert result[4].eligible is False
    assert result[4].eligible_practices == []


async def test_remove_impossible_practices_with_eligibility_matrix_rule(app_request, mdl) -> None:
    """Test remove_impossible_practices function with EligibilityMatrixRule present."""
    stage_id = (await mdl.Stage()).id

    # Create stage eligibility config with matrix to trigger EligibilityMatrixRule creation
    await mdl.StageEligibilityConfig(
        stage_id=stage_id,
        name="Test Matrix Config",
        eligibility_matrix={
            "practice_change_groups": ["Tillage practices", "Crop Practices"],
            "matrix": {
                "Conventional Till,No Cover Crop": [
                    ["Reduced Till", "Cover Crops"],
                    ["No Till", "Cover Crops"],
                ]
            },
        },
    )

    # Create a practice dependency rule: nutrient_management requires BOTH reduced_till AND cover_crops
    await mdl.PracticeChangeRules(
        stage_id=stage_id,
        rule={
            "type": PracticeRuleTypes.PRACTICE_DEPENDENCY,
            "condition": PracticeRuleConditions.ALL,
            "target": PracticeChange.nutrient_management,
            "value": [PracticeChange.reduced_till, PracticeChange.cover_crops],
        },
    )

    # Test eligibilities
    eligibilities = {
        1: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.nutrient_management],  # Should be impossible - missing cover_crops
                [PracticeChange.reduced_till],
            ],
        ),
        2: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.nutrient_management],  # Should be valid - has both required practices
                [PracticeChange.reduced_till],
                [PracticeChange.cover_crops],
            ],
        ),
    }

    result = await remove_impossible_practices(app_request, eligibilities, stage_id)

    # Field 1: nutrient_management should be removed due to dependency rule violation
    # (missing cover_crops requirement)
    assert result[1].eligible is True
    field_1_practices = {practice for combination in result[1].eligible_practices for practice in combination}
    # nutrient_management should be removed, only reduced_till should remain
    assert field_1_practices == {PracticeChange.reduced_till}

    # Field 2: all practices should remain valid since it has both required practices
    assert result[2].eligible is True
    field_2_practices = {practice for combination in result[2].eligible_practices for practice in combination}
    assert field_2_practices == {
        PracticeChange.nutrient_management,
        PracticeChange.reduced_till,
        PracticeChange.cover_crops,
    }


async def test_remove_impossible_practices_ignores_eligibility_matrix_rule(app_request, mdl) -> None:
    """Test that remove_impossible_practices ignores EligibilityMatrixRule and only processes PRACTICE_DEPENDENCY rules."""
    stage_id = (await mdl.Stage()).id

    # Create stage eligibility config with matrix (this will add EligibilityMatrixRule to the rules list)
    await mdl.StageEligibilityConfig(
        stage_id=stage_id,
        name="Test Matrix Config",
        eligibility_matrix={
            "practice_change_groups": ["Tillage practices"],
            "matrix": {"Conventional Till": [["Reduced Till"], ["No Till"]]},
        },
    )

    # Don't create any PRACTICE_DEPENDENCY rules - only EligibilityMatrixRule should be present

    eligibilities = {
        1: FieldEligiblity(
            eligible=True,
            eligible_practices=[
                [PracticeChange.nutrient_management],
                [PracticeChange.reduced_till],
                [PracticeChange.cover_crops],
            ],
        ),
    }

    result = await remove_impossible_practices(app_request, eligibilities, stage_id)

    # Since there are no PRACTICE_DEPENDENCY rules, all practices should remain unchanged
    # The EligibilityMatrixRule should be ignored by remove_impossible_practices
    assert result[1].eligible is True
    assert {practice for combination in result[1].eligible_practices for practice in combination} == {
        PracticeChange.nutrient_management,
        PracticeChange.reduced_till,
        PracticeChange.cover_crops,
    }
