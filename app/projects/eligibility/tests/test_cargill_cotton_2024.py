from datetime import datetime
from unittest.mock import patch

from defaults.attribute_options import CropUsage
from phases.enums import AttributeTypes, StageTypes
from projects.eligibility.cargill_cotton_2024 import run_cargill_cotton_2024
from projects.schema import Historic<PERSON>ractice<PERSON>ield, HistoricPracticeResult
from values.enums import TillageValues


@patch("projects.eligibility.cargill_cotton_2024.get_project_historic_practices")
async def test_run_cargill_cotton_2024(mock_get_project_historic_practices, mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
    )
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_3 = await mdl.Fields(parent_project_id=project.id)

    # fmt: off
    mock_get_project_historic_practices.return_value = {
        field_1.id: HistoricPracticeField(
            created_at=datetime(2024, 1, 1, 0, 0, 0),
            values_=[
                # cotton crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="cotton", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # cover crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2023", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="cover", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_usage, value=CropUsage.COVER, row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.planting_date, value="2023-12-01", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.harvest_date, value="2023-12-31", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # reduced tillage
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_event, value=1, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_date, value="2024-01-01", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_practice, value=TillageValues.reduced_till, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
            ]
        ),
        field_2.id: HistoricPracticeField(
            created_at=datetime(2024, 1, 1, 0, 0, 0),
            values_=[
                # no cotton crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="corn", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # cover crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2023", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="cover", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_usage, value=CropUsage.COVER, row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.planting_date, value="2023-12-01", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.harvest_date, value="2023-12-31", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # reduced tillage
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_event, value=1, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_date, value="2024-01-01", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_practice, value=TillageValues.reduced_till, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
            ]
        ),
        field_3.id: HistoricPracticeField(
            created_at=datetime(2024, 1, 1, 0, 0, 0),
            values_=[
                # cotton crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="cotton", row_id=0, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # cover crop
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2023", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_type, value="cover", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.crop_usage, value=CropUsage.COVER, row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.planting_date, value="2023-12-01", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                HistoricPracticeResult(attribute_type=AttributeTypes.harvest_date, value="2023-12-31", row_id=1, stage=StageTypes.HISTORICAL_CROP_ROTATION),
                # conventional tillage
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_event, value=1, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_date, value="2024-01-01", row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_practice, value=TillageValues.conventional_till, row_id=0, stage=StageTypes.HISTORICAL_TILLAGE),
                # conventional tillage
                HistoricPracticeResult(attribute_type=AttributeTypes.record_year, value="2024", row_id=1, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_event, value=1, row_id=1, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_date, value="2024-02-01", row_id=1, stage=StageTypes.HISTORICAL_TILLAGE),
                HistoricPracticeResult(attribute_type=AttributeTypes.tillage_practice, value=TillageValues.conventional_till, row_id=1, stage=StageTypes.HISTORICAL_TILLAGE),
            ]
        )
    }
    # fmt: on

    fields_eligibility = await run_cargill_cotton_2024(request=app_request, project_id=project.id)
    assert len(fields_eligibility) == 3
    assert fields_eligibility[field_1.id].eligible is True
    assert fields_eligibility[field_2.id].eligible is False
    assert fields_eligibility[field_2.id].message == "summer_crop_type_valid: False"
    assert fields_eligibility[field_3.id].eligible is False
    assert fields_eligibility[field_3.id].message == "tillage_selection_valid: False,"
