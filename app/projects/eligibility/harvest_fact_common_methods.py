from typing import Callable

from fastapi import Request

from defaults.attribute_options import CropUsage
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.methods import get_entity_events_for_field
from fields.db import get_fields_by_project_id
from fields.enums import EntityDataType, FieldDataState
from fields.model import FieldFacts
from fields.schema import Field
from logger import get_logger
from phases.dataclasses import DatetimeRange
from phases.db import get_phase_from_project_id
from phases.enums import PhaseTypes, StageTypes
from programs.db import (
    get_program_from_field_id,
    get_program_reporting_period_by_field_id,
)
from programs.enums import ProgramTemplate
from projects.eligibility.methods import create_multiple_field_facts
from ses_integration.fetch_entity_events import fetch_events_for_field_phase

logger = get_logger(__name__)


async def compute_harvest_field_facts(
    request: Request, project_id: int, data_type: EntityDataType, field_compute_function: Callable
) -> dict[int, list[str]]:
    """
    For all of the fields in this project, use the field_compute_function to extract
    facts for the field in the reporting period of the project and store them as FieldFacts
    """
    fields = await get_fields_by_project_id(request=request, project_id=project_id)
    if not fields:
        return {}
    field = fields[0]
    reporting_period = await get_program_reporting_period_by_field_id(request, field.id)
    monitoring_phase = await get_phase_from_project_id(request, project_id, PhaseTypes.MONITORING)
    result_by_field_id: dict[int, list[str]] = {}
    field_fact_instances: list[FieldFacts] = []
    for field in fields:
        field_result = await field_compute_function(request, field, reporting_period)
        if len(field_result) < 1:
            continue
        field_fact_instances.append(
            FieldFacts(
                field_id=field.id,
                phase_id=monitoring_phase.id,
                facts=[field_result],
                state=FieldDataState.measured,
                data_type=data_type,
            )
        )
        result_by_field_id[field.id] = field_result
    await create_multiple_field_facts(
        request=request,
        instances=field_fact_instances,
    )
    return result_by_field_id


def _event_in_bounds(ev: EntityEvent, reporting_period: DatetimeRange) -> bool:
    """
    Make sure the end date of the entity event is in the reporting period
    """
    ev_end = ev.get_interval_end_or_occurred_at()
    if ev_end is None:
        return False
    ev_end_date = ev_end.date()
    return ev_end_date >= reporting_period.start.date() and ev_end_date <= reporting_period.end.date()


async def get_commodity_cropping_events_for_project_field(
    request: Request, field: Field, reporting_period: DatetimeRange
) -> list[CroppingEvent]:
    """
    Get the cropping events for the project field in order by harvest date,
    for any purpose, but here to allow the extraction of harvest facts
    """
    # it would be better to push constraining and sorting the date range and types to the database
    # instead of undertaking them manually
    program = await get_program_from_field_id(request=request, field_id=field.id)
    try:
        if program.program_template == ProgramTemplate.event_based:
            field_events = await fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=False,
                is_single_phase_program=program.is_single_phase_data_collection,
            )
            field_events = [ev for ev in field_events if isinstance(ev, CroppingEvent)]
        else:
            field_events = (
                await get_entity_events_for_field(
                    request,
                    field,
                    phase_type=PhaseTypes.MONITORING,
                    stage_types=[
                        StageTypes.SUMMER_CROPS,
                        StageTypes.WINTER_CROPS,
                        StageTypes.INTENDED_COMMODITY_CROPS,
                        StageTypes.CROP_EVENTS,
                        StageTypes.HISTORICAL_CROP_ROTATION,
                    ],
                )
            )[PhaseTypes.MONITORING]
    except ValueError as ve:
        logger.error(f"Unable to fetch entity events for field {field.id} due to ValueError {ve}")
        return []
    events_within_bounds = sorted(
        [fe for fe in field_events if _event_in_bounds(fe, reporting_period)],
        key=lambda e: e.get_interval_end_or_occurred_at(),
    )
    for ev in events_within_bounds:
        if not isinstance(ev, CroppingEvent):
            raise AssertionError(f"Unexpected event: {ev}")
    return [ev for ev in events_within_bounds if ev.crop_usage in [CropUsage.COMMODITY]]
