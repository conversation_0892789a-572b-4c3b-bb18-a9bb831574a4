from __future__ import annotations

from collections import defaultdict
from datetime import datetime

import elasticapm
from shapely.geometry import shape
from shapely.validation import explain_validity
from starlette.requests import Request

from cultivation_cycles.methods import (
    get_cultivation_cycles,
    get_reporting_period_cultivation_cycles,
)
from cultivation_cycles.schema import CultivationCycle
from defaults.attribute_options import <PERSON>ropUsage, TillagePractice
from defaults.defaults_translator import (
    are_crops_biofuels_api_enabled,
    are_crops_measure_api_enabled,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.methods import get_entity_events_for_field
from fields import methods as fields_methods
from fields.model import Fields
from fields.schema import Field
from helper.async_tools import Tasks
from logger import get_logger
from phases import db as phases_db
from phases.db import (
    get_phase_from_project_id,
    get_stage_by_program_id_and_stage_type,
)
from phases.enums import EligibilityTypes, PhaseTypes, StageTypes
from phases.model import Phases, Stage
from phases.schema import PracticeChangeRule
from programs.db import get_program_by_id
from programs.enums import AccountingMethod, PracticeChange, ProgramTemplate
from programs.model import Programs
from projects import db as projects_db, methods as project_methods
from projects.classes.practice_change_rule_validator import PracticeChangeRuleValidator
from projects.db import get_project_by_id
from projects.eligibility.constants import (
    COVER_CROP_INTENSITY,
    PRACTICE_FROM_COVER_CROP_USAGE,
    PRACTICE_FROM_TILLAGE,
    TILLAGE_INTENSITY,
)
from projects.eligibility.db import get_eligible_enrolment_practices_for_field
from projects.eligibility.eligibility import (
    determine_project_eligibilities_at_enrolment,
)
from projects.eligibility.measurement import db as measurement_eligibility_db
from projects.eligibility.measurement.constants import (
    CARGILL_US_24_25_COMMODITY_AS_COVER_CROP_TYPES,
    CARGILL_US_24_25_PROGRAM_IDS,
    PREMIUM_COVER_CROPS,
)
from projects.eligibility.measurement.schema import (
    EligibilityDecisionOption,
    MeasurementEligibilityBuilder,
    MonitoredPracticeEligibilityResult,
)
from root_crud import get, update
from scenarios_service.generalized_integration.fetch_events_for_simulations import (
    translate_and_maybe_substitute_core_crop_name,
)
from ses_integration.fetch_entity_events import fetch_events_for_field_phase

logger = get_logger(__name__)


async def calculate_field_measurement_eligibility(
    request: Request, program_id: int, project_id: int
) -> list[MeasurementEligibilityBuilder]:
    """
    Determine and update field Measurement Eligibility for all fields in project. The result is stored in
    mrv_measurement_eligibility as well as mrv_fields.measurement_eligibility for convenience.

    Previous records in mrv_measurement_eligibility for the project's fields are soft-deleted.
    """
    e_phase = await get_phase_from_project_id(request=request, project_id=project_id, type=PhaseTypes.ENROLMENT)
    m_phase = await get_phase_from_project_id(request=request, project_id=project_id, type=PhaseTypes.MONITORING)
    if m_phase is not None and m_phase.start_date > datetime.now():
        return []

    program = await get_program_by_id(request=request, program_id=program_id)

    project = await get_project_by_id(request=request, project_id=project_id)
    if not project.reporting_enabled:
        logger.info(
            f"Reporting is not enabled for project {project_id}, skipping measurement eligibility calculation",
            project_id,
        )
        return []

    filters = [get.Filter(id_field=Fields.parent_project_id, ids=[project_id])]
    fields = await get.generic_get(request=request, orm_type=Fields, filters=filters, empty_return=True)
    field_ids = [f.id for f in fields]
    if len(fields) == 0:
        return []

    elig_builders: list[MeasurementEligibilityBuilder] = [
        MeasurementEligibilityBuilder(project_id=project_id, field_id=field_id) for field_id in field_ids
    ]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}

    await check_e_phase_completion(request=request, project_id=project_id, e_phase=e_phase, elig_builders=elig_builders)
    await check_contract_signed(
        request=request, program_id=program.id, project_id=project_id, elig_builders=elig_builders
    )
    await check_m_phase_completion(request=request, project_id=project_id, m_phase=m_phase, elig_builders=elig_builders)
    await check_monitored_practice_eligibility(
        request=request,
        program=program,
        project_id=project_id,
        fields=fields,
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )
    await check_monitored_commodity_crop_eligibility(
        request=request,
        program=program,
        fields=fields,
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )
    await check_field_boundary_eligibility(
        fields=fields, field_ids=field_ids, elig_builders_by_field=elig_builders_by_field
    )
    await check_credited_crop_type_eligibility()
    await check_modelable_crop_type_eligibility(
        request=request,
        program=program,
        fields=fields,
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    for elig_builder in elig_builders:
        # When we've upgraded to Pydantic 2, use @computed_field for this.
        elig_builder.compute_measurement_eligible()

    await measurement_eligibility_db.save_measurement_eligibilities(request, elig_builders)
    for field in fields:
        field.measurement_eligibility = elig_builders_by_field[field.id].measurement_eligible
    await update.update(request=request, instances=fields, type=Fields)

    # Return elig_builders for ease of testing. Builder should not be used as a general purpose "view" by consumers.
    return elig_builders


async def check_contract_signed(
    request: Request, program_id: int, project_id: int, elig_builders: list[MeasurementEligibilityBuilder]
) -> None:
    # First check if there's a Contract stage at all, disabling check if not
    stages = await phases_db.get_stages_from_program_and_phase_types(
        request, program_id, [PhaseTypes.ENROLMENT], [StageTypes.CONTRACT]
    )
    if not stages or all(stg.enabled is False for stg in stages):
        for elig_builder in elig_builders:
            elig_builder.contract_signed = EligibilityDecisionOption.DISABLED
        return

    # Check that the contract is signed
    contract = await projects_db.get_project_contract(request=request, project_id=project_id)
    contract_signed = contract is not None and project_methods.has_completed_contract(contract)
    for elig_builder in elig_builders:
        elig_builder.contract_signed = (
            EligibilityDecisionOption.ELIGIBLE if contract_signed else EligibilityDecisionOption.INELIGIBLE
        )


async def check_e_phase_completion(
    request: Request, project_id: int, e_phase: Phases, elig_builders: list[MeasurementEligibilityBuilder]
) -> None:
    # This call will only save the new Phase Completion value if it's completed
    phase_completion = await project_methods.calculate_project_phase_completion(request, project_id, e_phase.id)
    for elig_builder in elig_builders:
        elig_builder.set_enrollment_data_entry_complete(phase_completion.is_completed)


async def check_m_phase_completion(
    request: Request, project_id: int, m_phase: Phases, elig_builders: list[MeasurementEligibilityBuilder]
) -> None:
    """This check does not save an M Phase completion record because an M Phase completion implies that data entry
    is complete AND that the Finish stage has been accepted. Only the complete_project_phase endpoint should save
    an M Phase Completion record.
    """
    if not m_phase or not m_phase.enabled:
        for elig_builder in elig_builders:
            elig_builder.handle_measurement_phase_disabled()
        return

    # Has the Finish stage been completed
    already_completed = (
        await project_methods.get_project_phase_completion(request, project_id, m_phase.id)
    ).is_completed
    # Don't save the completion result since only Finish stage acceptance should complete M Phase
    phase_completion = await project_methods.calculate_project_phase_completion(
        request, project_id, m_phase.id, save_result=False
    )
    for elig_builder in elig_builders:
        # Incomplete means data is missing (is_completed=False) or the Finish stage hasn't been completed
        # (already_complete=False)
        elig_builder.set_measurement_data_entry_complete(already_completed and phase_completion.is_completed)


async def check_monitored_practice_eligibility(
    request: Request,
    program: Programs,
    project_id: int,
    fields: list[Field],
    elig_builders: list[MeasurementEligibilityBuilder],
    elig_builders_by_field: dict[int, MeasurementEligibilityBuilder],
) -> None:
    """
    Calculate and populate Monitored Practice Eligibility on a list of MeasurementEligibilityBuilder.
    """
    # Short-circuit if the accounting method is not intervention or inventory.
    if (
        program.accounting_method != AccountingMethod.intervention
        and program.accounting_method != AccountingMethod.inventory
    ):
        for elig_builder in elig_builders:
            elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.DISABLED
        return

    # Short-circuit if the check is disabled by a previous check.
    if any(
        [
            elig_builder.monitored_practice_eligibility == EligibilityDecisionOption.DISABLED
            for elig_builder in elig_builders
        ]
    ):
        return

    assign_practices_stage = await get_stage_by_program_id_and_stage_type(
        request=request, program_id=program.id, stage_type=StageTypes.ASSIGN_PRACTICES
    )
    if not assign_practices_stage:
        if program.accounting_method == AccountingMethod.intervention:
            raise Exception(f"Program {program.id} is missing an assigned practices stage.")
        else:
            for elig_builder in elig_builders:
                elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.DISABLED
            return

    await _rerun_e_phase_eligibility(
        request=request,
        program_id=program.id,
        project_id=project_id,
        assign_practices_stage_id=assign_practices_stage.id,
        elig_builders=elig_builders,
    )

    # Calculate and save the Monitored Practice Eligibility for all fields
    m_practice_eligibilities = await run_monitored_practice_eligibility(
        request, program, project_id, fields, assign_practices_stage
    )
    for m_practice_elig in m_practice_eligibilities:
        elig_builders_by_field[m_practice_elig.field_id].monitored_practice_eligibility = (
            EligibilityDecisionOption.ELIGIBLE
            if m_practice_elig.monitored_practice_eligible
            else EligibilityDecisionOption.INELIGIBLE
        )
        elig_builders_by_field[m_practice_elig.field_id].eligible_practice_combinations = (
            m_practice_elig.eligible_practice_combinations
        )
        elig_builders_by_field[m_practice_elig.field_id].monitored_practices = m_practice_elig.monitored_practices


async def check_monitored_commodity_crop_eligibility(
    request: Request,
    program: Programs,
    fields: list[Field],
    elig_builders: list[MeasurementEligibilityBuilder],
    elig_builders_by_field: dict[int, MeasurementEligibilityBuilder],
) -> None:
    if any(
        [
            elig_builder.monitored_commodity_crop_eligibility == EligibilityDecisionOption.DISABLED
            for elig_builder in elig_builders
        ]
    ):
        return

    for field in fields:
        commodity_crop_cultivation_cycles = await _get_reporting_period_commodity_crop_cultivation_cycles(
            request, program, field
        )
        if len(commodity_crop_cultivation_cycles) > 0:
            elig_builders_by_field[field.id].monitored_commodity_crop_eligibility = EligibilityDecisionOption.ELIGIBLE
        else:
            elig_builders_by_field[field.id].monitored_commodity_crop_eligibility = EligibilityDecisionOption.INELIGIBLE


async def check_field_boundary_eligibility(
    fields: list[Field], field_ids: list[int], elig_builders_by_field: dict[int, MeasurementEligibilityBuilder]
) -> None:
    mrv_to_core_field_id = {f.id: f.fs_field_id for f in fields}
    core_field_id_to_geometry = await fields_methods.get_geometries_for_core_fields(list(mrv_to_core_field_id.values()))
    for field_id in field_ids:
        core_field_id = mrv_to_core_field_id[field_id]
        geometry = core_field_id_to_geometry.get(core_field_id)
        if geometry is None:
            raise Exception(f"Field {field_id} is missing a boundary.")
        polygon = shape(geometry)
        is_valid, explanation = polygon.is_valid, explain_validity(polygon)
        if is_valid is False:
            elig_builders_by_field[field_id].field_boundary_eligibility = EligibilityDecisionOption.INELIGIBLE
            elig_builders_by_field[field_id].field_boundary_invalid_reason = explanation
            continue
        elig_builders_by_field[field_id].field_boundary_eligibility = EligibilityDecisionOption.ELIGIBLE


async def check_credited_crop_type_eligibility() -> None:
    pass


async def check_modelable_crop_type_eligibility(
    request: Request,
    program: Programs,
    fields: list[Field],
    elig_builders: list[MeasurementEligibilityBuilder],
    elig_builders_by_field: dict[int, MeasurementEligibilityBuilder],
) -> None:
    if any(
        [
            elig_builder.modelable_crop_type_eligibility == EligibilityDecisionOption.DISABLED
            for elig_builder in elig_builders
        ]
    ):
        return

    accounting_method = program.accounting_method
    field_id_to_regrow_crop_names = await _map_field_id_to_regrow_crop_names(request, program, fields)
    all_regrow_crop_names = {name for names in field_id_to_regrow_crop_names.values() for name in names}
    regrow_crop_name_to_enabled = await _map_regrow_crop_name_to_enabled(accounting_method, all_regrow_crop_names)
    for field_id, regrow_crop_names in field_id_to_regrow_crop_names.items():
        unmodelable_crop_names: set[str] = set()
        for name in regrow_crop_names:
            is_enabled = regrow_crop_name_to_enabled.get(name)
            if is_enabled is None or is_enabled is False:
                unmodelable_crop_names.add(name)
        if len(unmodelable_crop_names) > 0:
            elig_builders_by_field[field_id].modelable_crop_type_eligibility = EligibilityDecisionOption.INELIGIBLE
            elig_builders_by_field[field_id].unmodelable_crop_types = list(unmodelable_crop_names)
        else:
            elig_builders_by_field[field_id].modelable_crop_type_eligibility = EligibilityDecisionOption.ELIGIBLE


@elasticapm.async_capture_span()
async def run_monitored_practice_eligibility(
    request: Request, program: Programs, project_id: int, fields: list[Fields], assign_practices_stage: Stage
) -> list[MonitoredPracticeEligibilityResult]:
    """
    Calculate and store Monitored Practice Eligibility for all fields on the provided project.

    During the monitoring phase, determine practices for each field and whether they are eligible (e.g. for carbon
    credits). Only Tillage and Cover Crop practices are considered, not Nutrient Management or Irrigation.

    You can call this function as often as you like for a project. Newer results supersede any previous results saved.

    Assumes that eligibility was determined during enrolment phase, so we can compare actual practices to those
    deemed eligible during enrolment. However, this function will still work even if there are no eligibility values
    stored for a field - in that case the result will always be that the field is ineligible.

    For example, say during the enrolment phase the list of eligible practices for a given field are:
    [
       {"reduced till", "cover crops"},
       {"no till"},
       {"no till", "cover crops"},
    ]

    Then let's say that the actual practices carried out during monitoring are {"no till", "cover crops"}. In this
    case, the field is eligible because this set of practices appears in the above list.

    However, say instead the actual practices are {"convention till", "cover crops"}. This set of practices is not
    in the list of eligible practices, and so the field is marked as ineligible.

    Args:
        request: FastAPI request object for endpoint
        program: Program that fields belong to
        project_id: Project that fields belong to
        fields: Fields to determine monitor phase eligibilty for

    Returns:
        A list of MonitoredPracticeEligibilityResults, one per field

    """
    monitored_practice_eligibilities = await _calculate_project_monitored_practice_eligibility(
        request, program, fields, assign_practices_stage
    )
    monitored_phase_type = PhaseTypes.ENROLMENT if program.is_single_phase_data_collection else PhaseTypes.MONITORING
    monitored_phase = await get_phase_from_project_id(request, project_id, monitored_phase_type)
    await measurement_eligibility_db.save_monitored_practice_eligibilities(
        request, monitored_phase=monitored_phase, eligibility_results=monitored_practice_eligibilities
    )
    return monitored_practice_eligibilities


async def _rerun_e_phase_eligibility(
    request: Request,
    program_id: int,
    project_id: int,
    assign_practices_stage_id: int,
    elig_builders: list[MeasurementEligibilityBuilder],
) -> None:
    """
    Recalculate enrolment eligibility. In the past, there have been cases where eligible practices
    were generated but not saved, resulting in fields missing eligible practices.
    """
    if any(
        [
            elig_builder.enrollment_data_entry_complete != EligibilityDecisionOption.ELIGIBLE
            for elig_builder in elig_builders
        ]
    ):
        return

    await determine_project_eligibilities_at_enrolment(
        request=request, project_id=project_id, stage_id=assign_practices_stage_id
    )


async def _calculate_project_monitored_practice_eligibility(
    request: Request,
    program: Programs,
    fields: list[Fields],
    assign_practices_stage: Stage,
) -> list[MonitoredPracticeEligibilityResult]:
    """
    Given the current practices from the monitoring phase, for each field in this project, determine whether
    the practices are eligible, according to what was deemed eligible during the enrolment phase.

    Args:
        request: FastAPI request object for endpoint
        program: Program that fields belong to
        fields: Fields to check for eligibility
        actual_practices_by_field_id: A dict that maps field IDs to a set of practice changes for each field (i.e.
            actual practice changes carried out during the monitoring phase)

    Returns:
        A list of MonitoredPracticeEligibilityResults, one per field
    """
    from programs.methods import get_practice_change_set_by_program_id

    allowed_practices = await get_practice_change_set_by_program_id(request, program.id)
    actual_practices_by_field_id: dict[int, set[PracticeChange]] = defaultdict(set)
    await _get_monitored_practices_for_fields(request, program, fields, allowed_practices, actual_practices_by_field_id)

    practice_change_rules = []
    if assign_practices_stage.eligibility_method == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
        validator = PracticeChangeRuleValidator(request=request, stage_id=assign_practices_stage.id)
        practice_change_rules = await validator.get_practice_change_rules()

    eligibility_tasks = Tasks()
    for field in fields:
        eligible_practice_combinations = await get_eligible_enrolment_practices_for_field(request, field.id)
        eligibility_tasks.add(
            _calculate_field_monitored_practice_eligibility(
                field_id=field.id,
                allowed_practices=allowed_practices,
                monitored_practices=actual_practices_by_field_id[field.id],
                eligible_practice_combinations=eligible_practice_combinations,
                eligibility_type=assign_practices_stage.eligibility_method,
                practice_change_rules=practice_change_rules,
            )
        )
    return await eligibility_tasks.complete_all()


async def _calculate_field_monitored_practice_eligibility(
    field_id: int,
    allowed_practices: set[PracticeChange],
    monitored_practices: set[PracticeChange],
    eligible_practice_combinations: list[set[PracticeChange]],
    eligibility_type: EligibilityTypes,
    practice_change_rules: list[PracticeChangeRule],
) -> MonitoredPracticeEligibilityResult:
    """
    Determine MonitoredPracticeEligibilityResult for field_id by comparing its current practice changes against the list
    of eligibilities it was given during enrolment (if any).

    To understand why we do a set intersection with `allowed_practice_changes`, consider the following:
    In Program 68, we only allow the following practice changes:
      - reduced_till
      - no_till
      - cover_crops
    Note the absence of "conventional_till" - this is not an allowed practice change in program 68. But what
    if a user has done "conventional_till" and "cover_crops" during the monitoring phase?

    Let's say that during the e-phase, one of the sets of eligible practices was ["cover_crops"]. If we just
    compare eligibility ["cover_crops"] with measured practices ["conventional_till", "cover_crops"], the
    comparison fails and this field is deemed ineligible. So what we do, is we take the set of measured
    practices and intersect it with the same of allowed practice changes:

    {"conventional_till", "cover_crops"} & {"reduced_till", "no_till", "cover_crops"}

    This gives us:

    {"cover_crops"}

    Which is one of the e-phase eligibility values, so the field is deemed eligible.
    """
    filtered_monitored_practices = monitored_practices & allowed_practices
    # TODO (MRV-4267): determine whether to filter disallowed practices from eligible practice combinations
    # in E eligibility check.
    filtered_eligible_practice_combinations = [
        (set(eligible_practices) & allowed_practices) for eligible_practices in eligible_practice_combinations
    ]
    if eligibility_type == EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE:
        return MonitoredPracticeEligibilityResult(
            field_id=field_id,
            monitored_practices=monitored_practices,
            eligible_practice_combinations=eligible_practice_combinations,
            monitored_practice_eligible=True,
        )
    elif eligibility_type == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
        filtered_eligible_practices = {
            practice for practices in filtered_eligible_practice_combinations for practice in practices
        }
        if filtered_monitored_practices.intersection(filtered_eligible_practices) and all(
            rule.is_valid(filtered_monitored_practices) for rule in practice_change_rules
        ):
            return MonitoredPracticeEligibilityResult(
                field_id=field_id,
                monitored_practices=monitored_practices,
                eligible_practice_combinations=eligible_practice_combinations,
                monitored_practice_eligible=True,
            )
    else:
        for practice_combination in filtered_eligible_practice_combinations:
            if practice_combination == filtered_monitored_practices:
                return MonitoredPracticeEligibilityResult(
                    field_id=field_id,
                    monitored_practices=monitored_practices,
                    eligible_practice_combinations=eligible_practice_combinations,
                    monitored_practice_eligible=True,
                )
    return MonitoredPracticeEligibilityResult(
        field_id=field_id,
        monitored_practices=monitored_practices,
        eligible_practice_combinations=eligible_practice_combinations,
        monitored_practice_eligible=False,
    )


async def _get_monitored_practices_for_fields(
    request: Request,
    program: Programs,
    fields: list[Fields],
    allowed_practices: set[PracticeChange],
    actual_practices_by_field_id: dict[int, set[PracticeChange]],
) -> None:
    """
    Determine the actual tillage and cover crop practices during the monitoring period for each of the fields.

    Args:
        request: FastAPI request object for endpoint
        fields: Determine tillage and cover crop practice for each field
        actual_practices_by_field_id: Store determined practice for each field in this `dict`
    """
    for field in fields:
        if program.program_template == ProgramTemplate.event_based:
            events = await fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=False,
                is_single_phase_program=program.is_single_phase_data_collection,
            )
        else:
            phase_type = PhaseTypes.ENROLMENT if program.is_single_phase_data_collection else PhaseTypes.MONITORING
            phase_to_events = await get_entity_events_for_field(
                request=request,
                field=Field.from_orm(field),
                phase_type=phase_type,
                stage_types=[
                    StageTypes.HISTORICAL_TILLAGE,
                    StageTypes.TILLAGE,
                    StageTypes.TILLAGE_EVENTS,
                    StageTypes.HISTORICAL_CROP_ROTATION,
                    StageTypes.CROP_EVENTS,
                ],
            )
            events = phase_to_events[phase_type]
        cultivation_cycles, _ = get_cultivation_cycles(events)
        reporting_period_cultivation_cycles = get_reporting_period_cultivation_cycles(cultivation_cycles, program)
        tillage_practice = _get_tillage_practice(reporting_period_cultivation_cycles)
        actual_practices_by_field_id[field.id].add(tillage_practice)
        cover_crop_practice = _get_cover_crop_practice(program, reporting_period_cultivation_cycles, allowed_practices)
        actual_practices_by_field_id[field.id].add(cover_crop_practice)


def _get_tillage_practice(cultivation_cycles: list[CultivationCycle]) -> PracticeChange:
    """
    Go through all the tillage values in the monitoring period, and take note of the most
    intense tillage practice performed for each field.

    NOTE: If no tillage values are recorded for a field, then it ends up having a "no till"
    value.

    Args:
        cultivation_cycles: The cultivation cycles in the monitoring period for the field
    """
    maximum_tillage = PracticeChange.no_till
    for cycle in cultivation_cycles:
        for event in cycle.events:
            if not isinstance(event, TillageEvent):
                continue
            if not event.tillage_practice:
                continue
            tillage_practice = PRACTICE_FROM_TILLAGE[TillagePractice(event.tillage_practice)]
            if TILLAGE_INTENSITY[tillage_practice] > TILLAGE_INTENSITY[maximum_tillage]:
                maximum_tillage = tillage_practice
    return maximum_tillage


def _get_cover_crop_practice(
    program: Programs, cultivation_cycles: list[CultivationCycle], allowed_practices: set[PracticeChange]
) -> PracticeChange:
    """
    Go through all the crop values in the monitoring period, and determine whether a cover
    crop was grown for the field.

    NOTE: If no cover crop values are recorded for a field, then it ends up having a
    "no cover crop" value.

    Args:
        program: The program that the field belongs to
        cultivation_cycles: The cultivation cycles in the monitoring period for the field
    """
    use_granular_cover_crop_practice = (
        PracticeChange.basic_cover_crops in allowed_practices
        and PracticeChange.premium_cover_crops in allowed_practices
    )
    best_cover_crop_practice = PracticeChange.no_cover_crop
    for cycle in cultivation_cycles:
        for event in cycle.events:
            if not isinstance(event, CroppingEvent):
                continue
            if program.id in CARGILL_US_24_25_PROGRAM_IDS:
                if (
                    event.crop_usage == CropUsage.COMMODITY
                    and event.crop_type in CARGILL_US_24_25_COMMODITY_AS_COVER_CROP_TYPES
                    and event.get_interval_start_or_occurred_at().month in [7, 8, 9, 10, 11, 12, 1, 2]
                ):
                    cover_crop_practice = PracticeChange.cover_crops
                else:
                    cover_crop_practice = PRACTICE_FROM_COVER_CROP_USAGE.get(event.crop_usage)
            elif use_granular_cover_crop_practice:
                if event.crop_usage not in PRACTICE_FROM_COVER_CROP_USAGE:
                    continue
                if event.crop_type in PREMIUM_COVER_CROPS:
                    cover_crop_practice = PracticeChange.premium_cover_crops
                else:
                    cover_crop_practice = PracticeChange.basic_cover_crops
            else:
                cover_crop_practice = PRACTICE_FROM_COVER_CROP_USAGE.get(event.crop_usage)
            if (
                cover_crop_practice
                and COVER_CROP_INTENSITY[cover_crop_practice] > COVER_CROP_INTENSITY[best_cover_crop_practice]
            ):
                best_cover_crop_practice = cover_crop_practice
    return best_cover_crop_practice


async def _get_reporting_period_commodity_crop_cultivation_cycles(
    request: Request, program: Programs, field: Fields
) -> list[CultivationCycle]:
    if program.program_template == ProgramTemplate.event_based:
        events = await fetch_events_for_field_phase(
            request=request,
            field_id=field.id,
            enrollment_phase_only=False,
            is_single_phase_program=program.is_single_phase_data_collection,
        )
    else:
        phase_type = PhaseTypes.ENROLMENT if program.is_single_phase_data_collection else PhaseTypes.MONITORING
        phase_to_events = await get_entity_events_for_field(
            request=request,
            field=Field.from_orm(field),
            phase_type=phase_type,
            stage_types=[
                StageTypes.HISTORICAL_CROP_ROTATION,
                StageTypes.CROP_EVENTS,
                StageTypes.WINTER_CROPS,
                StageTypes.SUMMER_CROPS,
            ],
        )
        events = phase_to_events[phase_type]
    cultivation_cycles, _ = get_cultivation_cycles(events)
    reporting_period_cultivation_cycles = get_reporting_period_cultivation_cycles(cultivation_cycles, program)
    commodity_crop_cultivation_cycles = []
    for cycle in reporting_period_cultivation_cycles:
        for event in cycle.events:
            if isinstance(event, CroppingEvent) and event.crop_usage == CropUsage.COMMODITY:
                commodity_crop_cultivation_cycles.append(cycle)
                break
    return commodity_crop_cultivation_cycles


async def _map_field_id_to_regrow_crop_names(
    request: Request, program: Programs, fields: list[Fields]
) -> dict[int, set[str]]:
    field_id_to_regrow_crop_names: dict[int, set[str]] = defaultdict(set)
    for field in fields:
        if program.program_template == ProgramTemplate.event_based:
            events = await fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=False,
                is_single_phase_program=program.is_single_phase_data_collection,
            )
        else:
            phase_to_events = await get_entity_events_for_field(
                request=request,
                field=Field.from_orm(field),
                phase_type=None,
                stage_types=[
                    StageTypes.HISTORICAL_CROP_ROTATION,
                    StageTypes.CROP_EVENTS,
                    StageTypes.WINTER_CROPS,
                    StageTypes.SUMMER_CROPS,
                ],
            )
            events = [event for events in phase_to_events.values() for event in events]
        for event in events:
            if not isinstance(event, CroppingEvent):
                continue
            if program.program_template == ProgramTemplate.event_based:
                field_id_to_regrow_crop_names[field.id].add(event.crop_type)
            else:
                try:
                    regrow_crop_name = await translate_and_maybe_substitute_core_crop_name(
                        request=request,
                        core_crop_name=event.crop_type,
                        planting_date=event.interval.start,
                        field_id=field.id,
                    )
                    field_id_to_regrow_crop_names[field.id].add(regrow_crop_name)
                except LookupError:
                    field_id_to_regrow_crop_names[field.id].add(event.crop_type)
    return field_id_to_regrow_crop_names


async def _map_regrow_crop_name_to_enabled(
    accounting_method: AccountingMethod, regrow_crop_names: set[str]
) -> dict[str, bool]:
    if accounting_method == AccountingMethod.intervention or accounting_method == AccountingMethod.inventory:
        return await are_crops_measure_api_enabled(regrow_crop_names)
    elif accounting_method == AccountingMethod.biofuels:
        return await are_crops_biofuels_api_enabled(regrow_crop_names)
    raise Exception(f"Accounting method {accounting_method} is invalid.")
