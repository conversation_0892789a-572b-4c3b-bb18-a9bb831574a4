from sqlalchemy import func
from sqlalchemy.future import select
from starlette.requests import Request

from fields.enums import EntityDataType, FieldDataState
from fields.model import FieldFacts
from helper.helper import run_query
from phases.model import Phases
from projects.eligibility.measurement.model import MeasurementEligibility
from projects.eligibility.measurement.schema import (
    MeasurementEligibilityBuilder,
    MonitoredPracticeEligibilityResult,
)
from projects.model import Projects
from root_crud import create, delete


async def save_monitored_practice_eligibilities(
    request: Request, monitored_phase: Phases, eligibility_results: list[MonitoredPracticeEligibilityResult]
) -> None:
    """
    During the monitored phase, we determine the actual practices that were carried out for all the fields
    in a project, i.e. the practices that could be eligible for various carbon credits etc., and save them to
    mrv_field_eligibilities in this call. The monitored phase is E phase for single phase programs, and M
    phase for multi-phase programs.

    Args:
        monitored_phase: the monitored phase during which we determined the actual practices
        eligibility_results: maps field IDs to their Monitored Practice Eligibility results
    """
    if monitored_phase.deleted_at:
        raise ValueError(f"Phase {monitored_phase.id} has been deleted and cannot be used for measurement eligibility.")
    await create.bulk_insert(
        request=request,
        instances=[
            FieldFacts(
                phase_id=monitored_phase.id,
                field_id=eligibility_result.field_id,
                eligible=eligibility_result.monitored_practice_eligible,
                facts=[eligibility_result.monitored_practices],
                state=FieldDataState.measured,
                data_type=EntityDataType.entity_practices,
            )
            for eligibility_result in eligibility_results
        ],
        orm_type=FieldFacts,
    )


async def soft_delete_measurement_eligibilities(request: Request, project_id: int) -> None:
    return await delete.soft(
        request=request,
        ids=[project_id],
        orm_type=MeasurementEligibility,
        id_field=MeasurementEligibility.project_id,
        return_deleted=False,
    )


async def save_measurement_eligibilities(request: Request, elig_builders: list[MeasurementEligibilityBuilder]) -> None:
    project_id: int = elig_builders[0].project_id
    await soft_delete_measurement_eligibilities(request, project_id)
    await create.create(
        request=request,
        orm_type=MeasurementEligibility,
        type=MeasurementEligibilityBuilder,
        instances=elig_builders,
        no_return=True,
    )


async def get_measurement_eligible_field_ids_by_program_id(
    request: Request, program_id: int, measurement_eligible: bool
) -> list[int]:
    """
    Returns a list of the fields in the program that have the specified measurement eligible value.
    Args:
        program_id: The ID of the program that the fields belong to
        measurement_eligible: Whether to return fields that are measurement eligible or measurement ineligible
    """
    query = (
        select(MeasurementEligibility.field_id)
        .join(Projects, MeasurementEligibility.project_id == Projects.id)
        .where(MeasurementEligibility.deleted_at.is_(None))
        .where(MeasurementEligibility.measurement_eligible.is_(measurement_eligible))
        .where(Projects.program_id == program_id)
    )
    async with request.state.sql_session() as session:
        return (await run_query(query=query, s=session)).scalars().all()


async def get_count_of_measurement_eligible_fields_by_program_id(
    request: Request, program_id: int, measurement_eligible: bool
) -> int:
    """
    Returns a count of the fields in the program that have the specified measurement eligible value.
    Args:
        program_id: The ID of the program that the fields belong to
        measurement_eligible: Whether to return fields that are measurement eligible or measurement ineligible
    """
    query = (
        select(func.count(MeasurementEligibility.field_id))
        .join(Projects, MeasurementEligibility.project_id == Projects.id)
        .where(MeasurementEligibility.deleted_at.is_(None))
        .where(MeasurementEligibility.measurement_eligible.is_(measurement_eligible))
        .where(Projects.program_id == program_id)
    )
    async with request.state.sql_session() as session:
        return (await run_query(query=query, s=session)).scalar()
