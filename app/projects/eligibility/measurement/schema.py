import enum

from pydantic import BaseModel

from programs.enums import PracticeChange


class EligibilityDecisionOption(enum.StrEnum):
    ELIGIBLE = enum.auto()
    INELIGIBLE = enum.auto()
    DISABLED = enum.auto()


class MeasurementEligibilityBuilder(BaseModel):
    project_id: int
    field_id: int | None = None
    measurement_eligible: bool | None = None
    ineligibility_reasons: list[str] = []
    enrollment_data_entry_complete: EligibilityDecisionOption | None = None
    contract_signed: EligibilityDecisionOption | None = None
    measurement_data_entry_complete: EligibilityDecisionOption | None = None
    monitored_practice_eligibility: EligibilityDecisionOption | None = None
    monitored_practices: set[PracticeChange] | None = None
    monitored_commodity_crop_eligibility: EligibilityDecisionOption | None = None
    eligible_practice_combinations: list[set[PracticeChange]] | None = None
    field_boundary_eligibility: EligibilityDecisionOption | None = None
    field_boundary_invalid_reason: str | None = None
    credited_crop_type_eligibility: EligibilityDecisionOption | None = EligibilityDecisionOption.DISABLED
    credited_crop_type: str | None = None  # Convert to a list of crops when implementing this check
    modelable_crop_type_eligibility: EligibilityDecisionOption | None = None
    unmodelable_crop_types: list[str] | None = None

    def generate_ineligibility_reasons(self) -> None:
        """Generates a human-readable string for each reason that measurement_eligible resulted False. Returns empty
        list if the field is eligible."""
        ineligibility_reasons = []
        if self.enrollment_data_entry_complete == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append("Incomplete Enrollment Phase data")
        if self.contract_signed == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append("Contract is missing or unsigned")
        if self.measurement_data_entry_complete == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append("Incomplete Measurement Phase data")
        if self.monitored_practice_eligibility == EligibilityDecisionOption.INELIGIBLE:
            # Appease mypy linter
            assert isinstance(self.monitored_practices, set)
            assert isinstance(self.eligible_practice_combinations, list)
            eligible_practice_combinations = [
                {prac.value for prac in combo} for combo in self.eligible_practice_combinations
            ]
            monitored_practices = {prac.value for prac in self.monitored_practices}
            ineligibility_reasons.append(
                f"Monitored practices {monitored_practices} not included in eligible practices: {eligible_practice_combinations}"
            )
        if self.monitored_commodity_crop_eligibility == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append("No commodity crop in Reporting Period")
        if self.field_boundary_eligibility == EligibilityDecisionOption.INELIGIBLE:
            # Appease mypy linter
            assert isinstance(self.field_boundary_invalid_reason, str)
            ineligibility_reasons.append(self.field_boundary_invalid_reason)
        if self.credited_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append("No creditable crop in Reporting Period")
        if self.modelable_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE:
            ineligibility_reasons.append(f"Crop(s) not modelable: {self.unmodelable_crop_types}")
        self.ineligibility_reasons = ineligibility_reasons

    def compute_measurement_eligible(self) -> bool:
        self.measurement_eligible = all(
            check in (EligibilityDecisionOption.ELIGIBLE, EligibilityDecisionOption.DISABLED)
            for check in [
                self.enrollment_data_entry_complete,
                self.measurement_data_entry_complete,
                self.contract_signed,
                self.monitored_practice_eligibility,
                self.monitored_commodity_crop_eligibility,
                self.field_boundary_eligibility,
                self.credited_crop_type_eligibility,
                self.modelable_crop_type_eligibility,
            ]
        )
        self.generate_ineligibility_reasons()
        return self.measurement_eligible

    def set_enrollment_data_entry_complete(self, is_completed: bool) -> None:
        self.enrollment_data_entry_complete = (
            EligibilityDecisionOption.ELIGIBLE if is_completed else EligibilityDecisionOption.INELIGIBLE
        )
        if not is_completed:
            self.modelable_crop_type_eligibility = EligibilityDecisionOption.DISABLED

    def handle_measurement_phase_disabled(self) -> None:
        self.measurement_data_entry_complete = EligibilityDecisionOption.DISABLED
        self.credited_crop_type_eligibility = EligibilityDecisionOption.DISABLED

    def set_measurement_data_entry_complete(self, is_completed: bool) -> None:
        self.measurement_data_entry_complete = (
            EligibilityDecisionOption.ELIGIBLE if is_completed else EligibilityDecisionOption.INELIGIBLE
        )
        if not is_completed:
            self.monitored_practice_eligibility = EligibilityDecisionOption.DISABLED
            self.monitored_commodity_crop_eligibility = EligibilityDecisionOption.DISABLED
            self.credited_crop_type_eligibility = EligibilityDecisionOption.DISABLED
            self.modelable_crop_type_eligibility = EligibilityDecisionOption.DISABLED


class MonitoredPracticeEligibilityResult(BaseModel):
    field_id: int
    monitored_practices: set[PracticeChange]
    monitored_practice_eligible: bool
    eligible_practice_combinations: list[set[PracticeChange]]
