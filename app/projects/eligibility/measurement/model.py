import sqlalchemy as sa
from sqlalchemy import func, Grouping, Index

from db.mysql import Base
from projects.eligibility.measurement.schema import EligibilityDecisionOption


class MeasurementEligibility(Base):
    __tablename__ = "mrv_measurement_eligibility"

    id = sa.Column(sa.Integer, primary_key=True)
    project_id = sa.Column(sa.Integer, sa.ForeignKey("mrv_projects.id"), nullable=False)
    field_id = sa.Column(sa.Integer, sa.ForeignKey("mrv_fields.id"), nullable=False)
    measurement_eligible = sa.Column(sa.<PERSON>an, nullable=False)
    ineligibility_reasons = sa.Column(sa.JSON(), nullable=False)
    enrollment_data_entry_complete = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=False)
    contract_signed = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=False)
    measurement_data_entry_complete = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=False)
    monitored_practice_eligibility = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=False)
    monitored_practices = sa.Column(sa.JSON(), nullable=False)
    monitored_commodity_crop_eligibility = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=True)
    eligible_practice_combinations = sa.Column(sa.JSON(), nullable=False)
    field_boundary_eligibility = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=False)
    field_boundary_invalid_reason = sa.Column(sa.Text(), nullable=True)
    credited_crop_type_eligibility = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=True)
    credited_crop_type = sa.Column(sa.Text(), nullable=True)
    modelable_crop_type_eligibility = sa.Column(sa.Enum(EligibilityDecisionOption), nullable=True)
    unmodelable_crop_types = sa.Column(sa.JSON(), nullable=True)
    created_at = sa.Column(sa.TIMESTAMP, server_default=sa.sql.func.current_timestamp(), nullable=False)
    deleted_at = sa.Column(sa.TIMESTAMP, nullable=True, index=True)

    __table_args__ = (
        Index(
            "unique_undeleted_field_eligibility",
            field_id,
            Grouping(func.IF(deleted_at.is_(None), 1, None)),
            unique=True,
            info={"skip_autogenerate": True},
        ),
    )
