from __future__ import annotations

from typing import TYPE_CHECKING

import elasticapm

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from logger import get_logger
from projects.eligibility.measurement import methods as measurement_elig_methods
from slack_integration.integration import post_message

if TYPE_CHECKING:
    from fastapi import Request


logger = get_logger(__name__)
settings = get_settings()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def measurement_eligibility_task(
    self: DBTask,
    *,
    program_id: int,
    project_ids: list[int],
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    """
    Calculates and stores MeasurementEligibility and mrv_fields.measurement_eligibility
    for all fields in the provided program or project.
    """
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_MEASUREMENT_ELIGIBILITY,
        message=":repeat: Measurement eligibility run started.",
        dict_={"program_id": program_id, "project_ids": str(project_ids)},
    )
    failed_project_id_to_error = {}
    for project_id in project_ids:
        try:
            await measurement_elig_methods.calculate_field_measurement_eligibility(
                request=request, program_id=program_id, project_id=project_id
            )
        except Exception as e:
            logger.exception(e)
            failed_project_id_to_error[project_id] = str(e)
            continue
    if failed_project_id_to_error:
        message = ":x: Measurement eligibility run completed with failures."
        message_dict = {
            "program_id": program_id,
            "project_ids": str(project_ids),
            "project_errors": failed_project_id_to_error,
        }
    else:
        message = ":white_check_mark: Measurement eligibility run succeeded."
        message_dict = {"program_id": program_id, "project_ids": str(project_ids)}
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_MEASUREMENT_ELIGIBILITY,
        message=message,
        dict_=message_dict,
    )
