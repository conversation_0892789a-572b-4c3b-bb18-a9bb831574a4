from __future__ import annotations

import elasticapm
from fastapi import APIRouter, Depends
from starlette import status
from starlette.requests import Request

from helper.async_tools import batch_list
from permissions.enums import Permission
from permissions.resolver import Permissions
from programs import db as programs_db
from projects import paths as project_paths
from projects.eligibility.measurement import db as m_elig_db, tasks as m_elig_tasks
from projects.eligibility.measurement.constants import (
    MEASUREMENT_ELIGIBILITY_PROJECT_BATCH_SIZE,
)
from projects.router import producer, tags

router = APIRouter()


@elasticapm.async_capture_span()
@router.post(
    project_paths.single_program + project_paths.measurement_eligibility,
    # programs/{program_id}/eligibility/measurement
    tags=tags + producer,
    status_code=status.HTTP_200_OK,
    response_model=None,
    # Update this dependency to GET_PROGRAM_ELIGIBILITY and the other to GET_PROJECT_ELIGIBILITY
    dependencies=[Depends(Permissions([Permission.GET_ELIGIBILITY]))],
    description="Run measurement eligibility for all projects in a program.",
)
async def program_measurement_eligibility(request: Request, program_id: int) -> None:
    """
    For all projects, determine if the practices carried out in all the fields of the project, during the monitoring
    phase, are eligible (e.g. for carbon credits), by comparing the actual practices against the set of all eligible
    practices that were determined during the enrolment phase.
    """
    project_ids = await programs_db.get_project_ids_for_program(
        request=request, program_id=program_id, exclude_deleted=True
    )
    for project_id_batch in batch_list(project_ids, MEASUREMENT_ELIGIBILITY_PROJECT_BATCH_SIZE):
        m_elig_tasks.measurement_eligibility_task.delay(
            program_id=program_id,
            project_ids=project_id_batch,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )


@elasticapm.async_capture_span()
@router.post(
    f"{project_paths.single_project}{project_paths.measurement_eligibility}",
    # /projects/{project_id}/eligibility/measurement
    tags=tags + producer,
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.GET_ELIGIBILITY]))],
)
async def measurement_eligibility(request: Request, project_id: int) -> None:
    """
    For a given project, run Measurement Eligibility, checking whether each field satisfies all requirements to be
    quantified with an MRV Program.
    Measurement Eligibility includes...
        * E and M Phase Completion
        * Signed Contract Check
        * Monitored Practice Eligibility
    """
    program_id = await programs_db.get_program_id_by_project_id(request, project_id)
    m_elig_tasks.measurement_eligibility_task.delay(
        program_id=program_id,
        project_ids=[project_id],
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@elasticapm.async_capture_span()
@router.get(
    project_paths.single_program + project_paths.measurement_eligibility + project_paths.fields,
    # /programs/{program_id}/eligibility/measurement/fields
    tags=tags + producer,
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.GET_ELIGIBILITY]))],
)
async def get_measurement_eligible_field_ids_by_program_id(
    request: Request, program_id: int, measurement_eligible: bool = True
) -> list[int]:
    return await m_elig_db.get_measurement_eligible_field_ids_by_program_id(
        request=request, program_id=program_id, measurement_eligible=measurement_eligible
    )
