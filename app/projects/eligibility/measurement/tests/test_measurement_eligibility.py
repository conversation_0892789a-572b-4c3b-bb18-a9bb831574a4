from __future__ import annotations

from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest
import pytz
from sqlalchemy import desc, select

import projects
import projects.eligibility
import projects.eligibility.measurement
from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from defaults.attribute_options import CropClass, CropUsage, TillagePractice
from defaults.consts import RegrowCropName
from docusign.enums import EnvelopeStatus
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.tillage_event import TillageEvent
from fields.enums import EntityDataType, FieldDataState
from fields.model import FieldFacts, Fields
from helper.helper import run_query
from phases.enums import (
    AttributeTypes,
    EligibilityTypes,
    PhaseTypes,
    PracticeRuleConditions,
    PracticeRuleTypes,
    StageTypes,
)
from phases.model import Phases, Stage
from phases.schema import PracticeDependencyRule
from programs.enums import AccountingMethod, PracticeChange, ProgramTemplate
from programs.model import Programs
from projects import paths
from projects.eligibility.measurement import methods, tasks as m_elig_tasks
from projects.eligibility.measurement.db import save_monitored_practice_eligibilities
from projects.eligibility.measurement.methods import (
    _get_cover_crop_practice,
    _get_monitored_practices_for_fields,
    _get_reporting_period_commodity_crop_cultivation_cycles,
    calculate_field_measurement_eligibility,
    check_contract_signed,
    check_e_phase_completion,
    check_field_boundary_eligibility,
    check_modelable_crop_type_eligibility,
    check_monitored_commodity_crop_eligibility,
    check_monitored_practice_eligibility,
    run_monitored_practice_eligibility,
)
from projects.eligibility.measurement.model import MeasurementEligibility
from projects.eligibility.measurement.schema import (
    EligibilityDecisionOption,
    MeasurementEligibilityBuilder,
    MonitoredPracticeEligibilityResult,
)
from projects.model import Projects
from projects.schema import ProjectPhaseCompletionResponse
from root_crud import update
from scenarios_service.enums import ScenariosServiceApi


@dataclass
class TillageRow:
    tillage_date: str
    tillage_practice: TillagePractice


@dataclass
class CropRowWCC:
    record_year: int
    winter_crop_commitment: int | None


@dataclass
class CropRowCropClass:
    record_year: int
    crop_class: CropClass


@dataclass
class CropRowCropUsage:
    record_year: int
    planting_date: str
    harvest_date: str
    crop_usage: CropUsage
    crop_type: str


async def setup_generic_monitoring_events_data(mdl):
    program = await mdl.Programs(
        id=1,
        reporting_period_start_date=datetime(2023, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2023, 12, 31, 23, 59, 59),
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=ProgramTemplate.legacy,
    )
    # These are the practice changes that the producer can commit to. Set in the Assign practices stage
    # in program setup:
    allowed_practice_changes = {
        PracticeChange.no_till,
        PracticeChange.reduced_till,
        PracticeChange.cover_crops,
        PracticeChange.catch_crops,
        PracticeChange.companion_crops,
    }
    for practice_change in allowed_practice_changes:
        await mdl.ProgramPracticeChange(program_id=program.id, practice_change=practice_change)
    project = await mdl.Projects(program_id=program.id)
    enrolment_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    monitoring_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(
        phase_id=enrolment_phase.id,
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=EligibilityTypes.CUSTOM,
        enabled=True,
    )
    tillage_stage = await mdl.Stage(
        phase_id=monitoring_phase.id, type_=StageTypes.TILLAGE, year_start=2022, year_end=2023, enabled=True
    )
    crop_history_stage = await mdl.Stage(
        phase_id=monitoring_phase.id,
        type_=StageTypes.HISTORICAL_CROP_ROTATION,
        year_start=2022,
        year_end=2023,
        enabled=True,
    )

    tillage_date_attr = await mdl.Attribute(
        parent_stage_id=tillage_stage.id, type=AttributeTypes.tillage_date, enabled=True
    )
    tillage_practice_attr = await mdl.Attribute(
        parent_stage_id=tillage_stage.id, type=AttributeTypes.tillage_practice, enabled=True
    )
    crop_history_record_year_attr = await mdl.Attribute(
        parent_stage_id=crop_history_stage.id, type=AttributeTypes.record_year, enabled=True
    )
    planting_date_attr = await mdl.Attribute(
        parent_stage_id=crop_history_stage.id, type=AttributeTypes.planting_date, enabled=True
    )
    harvest_date_attr = await mdl.Attribute(
        parent_stage_id=crop_history_stage.id, type=AttributeTypes.harvest_date, enabled=True
    )
    crop_usage_attr = await mdl.Attribute(
        parent_stage_id=crop_history_stage.id, type=AttributeTypes.crop_usage, enabled=True
    )
    crop_type_attr = await mdl.Attribute(
        parent_stage_id=crop_history_stage.id, type=AttributeTypes.crop_type, enabled=True
    )

    fields = [await mdl.Fields(parent_project_id=project.id) for _ in range(6)]
    for field in fields:
        await mdl.FieldsBaseline(field_id=field.id, baseline_year=2022)

    tillage_values = [
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.reduced_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.conventional_till),
        ],
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.no_till),
        ],
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.reduced_till),
        ],
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.reduced_till),
        ],
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.reduced_till),
        ],
        [
            TillageRow(tillage_date="2023-03-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-02-01", tillage_practice=TillagePractice.no_till),
            TillageRow(tillage_date="2023-01-01", tillage_practice=TillagePractice.reduced_till),
        ],
    ]
    for field_num, field in enumerate(fields):
        for row_num, row in enumerate(tillage_values[field_num]):
            await mdl.Values(
                field_id=field.id,
                attribute_id=tillage_date_attr.id,
                value=row.tillage_date,
                row_id=row_num,
            )
            await mdl.Values(
                field_id=field.id,
                attribute_id=tillage_practice_attr.id,
                value=row.tillage_practice,
                row_id=row_num,
            )

    # fmt: off
    crop_rows = [
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-05-01", harvest_date="2023-06-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="beans", record_year=2023, planting_date="2023-03-01", harvest_date="2023-04-01", crop_usage=None),
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
        ],
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-03-01", harvest_date="2023-04-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
        ],
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-05-01", harvest_date="2023-06-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="clover", record_year=2023, planting_date="2023-03-01", harvest_date="2023-04-01", crop_usage=CropUsage.COVER),
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
        ],
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-05-01", harvest_date="2023-06-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="beans", record_year=2023, planting_date="2023-03-01", harvest_date="2023-04-01", crop_usage=CropUsage.CATCH_CROP),
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
        ],
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-05-01", harvest_date="2023-06-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="beans", record_year=2023, planting_date="2023-03-01", harvest_date="2023-04-01", crop_usage=CropUsage.COMPANION_CROP),
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
        ],
        # cover crop not in reporting period
        [
            CropRowCropUsage(crop_type="corn", record_year=2023, planting_date="2023-01-01", harvest_date="2023-02-01", crop_usage=CropUsage.COMMODITY),
            CropRowCropUsage(crop_type="clover", record_year=2022, planting_date="2021-12-01", harvest_date="2022-01-01", crop_usage=CropUsage.COVER),
        ],
    ]
    # fmt: on
    for field_num, field in enumerate(fields):
        for row_num, row in enumerate(crop_rows[field_num]):
            await mdl.Values(
                field_id=field.id,
                attribute_id=crop_history_record_year_attr.id,
                value=row.record_year,
                row_id=row_num,
            )
            await mdl.Values(
                field_id=field.id,
                attribute_id=planting_date_attr.id,
                value=row.planting_date,
                row_id=row_num,
            )
            await mdl.Values(
                field_id=field.id,
                attribute_id=harvest_date_attr.id,
                value=row.harvest_date,
                row_id=row_num,
            )
            await mdl.Values(
                field_id=field.id,
                attribute_id=crop_usage_attr.id,
                value=row.crop_usage,
                row_id=row_num,
            )
            await mdl.Values(
                field_id=field.id,
                attribute_id=crop_type_attr.id,
                value=row.crop_type,
                row_id=row_num,
            )

    enrolment_eligibilities = {
        fields[0].id: [[PracticeChange.cover_crops, PracticeChange.reduced_till]],
        fields[1].id: [[PracticeChange.cover_crops, PracticeChange.reduced_till]],
        # this example comes from a real field, based on field 39301 from program 68
        fields[2].id: [
            [PracticeChange.no_till, PracticeChange.cover_crops],
            [PracticeChange.no_till],
            [PracticeChange.reduced_till, PracticeChange.cover_crops],
            [PracticeChange.reduced_till],
            [PracticeChange.cover_crops],
        ],
        fields[3].id: [[PracticeChange.catch_crops, PracticeChange.reduced_till]],
        fields[4].id: [[PracticeChange.companion_crops, PracticeChange.reduced_till]],
        fields[5].id: [[PracticeChange.cover_crops, PracticeChange.reduced_till]],
    }
    for field_id, eligibility in enrolment_eligibilities.items():
        await mdl.FieldFacts(
            eligible=True,
            facts=eligibility,
            phase_id=enrolment_phase.id,
            field_id=field_id,
            state=FieldDataState.eligible,
            data_type=EntityDataType.entity_practices,
        )

    return project.id, fields, monitoring_phase, assign_practices_stage, program, allowed_practice_changes


async def test_get_monitored_practices_for_fields(
    mdl,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mocker,
    app_request,
):
    program, _, fields, _, _, _, allowed_practice_changes = await set_up_monitored_events(
        is_single_phase_data_collection=False,
        mdl=mdl,
        create_cropping_event_data=create_cropping_event_data,
        create_tillage_event_data=create_tillage_event_data,
        create_interval_data=create_interval_data,
        mocker=mocker,
    )

    actual_practices_by_field_id = defaultdict(set)
    await _get_monitored_practices_for_fields(
        request=app_request,
        program=program,
        fields=fields,
        allowed_practices=allowed_practice_changes,
        actual_practices_by_field_id=actual_practices_by_field_id,
    )
    assert actual_practices_by_field_id == {
        fields[0].id: {PracticeChange.conventional_till, PracticeChange.cover_crops},
        fields[1].id: {PracticeChange.no_till, PracticeChange.no_cover_crop},
    }


async def test_get_cover_crop_practice(mdl, create_cropping_event_data, create_interval_data):
    program = await mdl.Programs(id=1639, program_template=ProgramTemplate.event_based)
    cultivation_cycle = CultivationCycle(
        id=CultivationCycleId(crop_event_id=None),
        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 3, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type=RegrowCropName.basic_cover_crop,
                    reductions=[],
                ),
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 3, 2, tzinfo=timezone.utc),
                        end=datetime(2025, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type=RegrowCropName.alternative_blend_cover_crop,
                    reductions=[],
                ),
            ),
        ],
    )
    cover_crop_practice = _get_cover_crop_practice(
        program=program,
        cultivation_cycles=[cultivation_cycle],
        allowed_practices={PracticeChange.basic_cover_crops, PracticeChange.premium_cover_crops},
    )
    assert cover_crop_practice == PracticeChange.premium_cover_crops

    cultivation_cycle = CultivationCycle(
        id=CultivationCycleId(crop_event_id=None),
        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 3, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type=RegrowCropName.alfalfa,
                    reductions=[],
                ),
            )
        ],
    )
    cover_crop_practice = _get_cover_crop_practice(
        program=program,
        cultivation_cycles=[cultivation_cycle],
        allowed_practices={PracticeChange.basic_cover_crops, PracticeChange.premium_cover_crops},
    )
    assert cover_crop_practice == PracticeChange.basic_cover_crops

    cultivation_cycle = CultivationCycle(
        id=CultivationCycleId(crop_event_id=None),
        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 3, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type=RegrowCropName.premium_cover_crop_mix,
                    reductions=[],
                ),
            )
        ],
    )
    cover_crop_practice = _get_cover_crop_practice(
        program=program, cultivation_cycles=[cultivation_cycle], allowed_practices={PracticeChange.cover_crops}
    )
    assert cover_crop_practice == PracticeChange.cover_crops


async def test_get_cover_crop_practice_cargill_us_24_25(mdl, create_cropping_event_data, create_interval_data):
    program = await mdl.Programs(id=1649, program_template=ProgramTemplate.event_based)
    cultivation_cycle = CultivationCycle(
        id=CultivationCycleId(crop_event_id=None),
        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 2, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type=RegrowCropName.barley,
                    reductions=[],
                ),
            )
        ],
    )
    cover_crop_practice = _get_cover_crop_practice(
        program=program, cultivation_cycles=[cultivation_cycle], allowed_practices={PracticeChange.cover_crops}
    )
    assert cover_crop_practice == PracticeChange.cover_crops

    cultivation_cycle = CultivationCycle(
        id=CultivationCycleId(crop_event_id=None),
        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
        end=datetime(2025, 12, 31, tzinfo=timezone.utc),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 3, 31, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type=RegrowCropName.basic_cover_crop,
                    reductions=[],
                ),
            )
        ],
    )
    cover_crop_practice = _get_cover_crop_practice(
        program=program, cultivation_cycles=[cultivation_cycle], allowed_practices={PracticeChange.cover_crops}
    )
    assert cover_crop_practice == PracticeChange.cover_crops


async def test_run_monitored_practice_eligibility_legacy(app_request, mdl, db_session_maker):
    project_id, fields, monitoring_phase, assign_practices_stage, program, _ = (
        await setup_generic_monitoring_events_data(mdl)
    )
    monitored_practice_results = await run_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project_id,
        fields=fields,
        assign_practices_stage=assign_practices_stage,
    )

    expected_eligible = {
        fields[0].id: False,
        fields[1].id: False,
        fields[2].id: True,
        fields[3].id: True,
        fields[4].id: True,
        fields[5].id: False,
    }
    expected_practices = {
        fields[0].id: {PracticeChange.no_cover_crop, PracticeChange.conventional_till},
        fields[1].id: {PracticeChange.no_cover_crop, PracticeChange.no_till},
        fields[2].id: {PracticeChange.cover_crops, PracticeChange.reduced_till},
        fields[3].id: {PracticeChange.catch_crops, PracticeChange.reduced_till},
        fields[4].id: {PracticeChange.companion_crops, PracticeChange.reduced_till},
        fields[5].id: {PracticeChange.no_cover_crop, PracticeChange.reduced_till},
    }

    for result in monitored_practice_results:
        assert result.monitored_practice_eligible == expected_eligible[result.field_id]
        assert result.monitored_practices == expected_practices[result.field_id]

    async with db_session_maker() as s:
        for field in fields:
            query = select(FieldFacts).where(
                FieldFacts.field_id == field.id, FieldFacts.phase_id == monitoring_phase.id
            )
            result = (await run_query(query=query, s=s)).scalar_one()
            assert set(result.facts[0]) == expected_practices[field.id]


@pytest.mark.parametrize("is_single_phase_data_collection", [(True), (False)])
async def test_run_monitored_practice_eligibility(
    is_single_phase_data_collection,
    mdl,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mocker,
    app_request,
    db_session_maker,
):
    program, project, fields, e_phase, m_phase, assign_practices_stage, _ = await set_up_monitored_events(
        is_single_phase_data_collection=is_single_phase_data_collection,
        mdl=mdl,
        create_cropping_event_data=create_cropping_event_data,
        create_tillage_event_data=create_tillage_event_data,
        create_interval_data=create_interval_data,
        mocker=mocker,
    )

    monitored_practice_results = await run_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project.id,
        fields=fields,
        assign_practices_stage=assign_practices_stage,
    )

    expected_practices = {
        fields[0].id: {PracticeChange.conventional_till, PracticeChange.cover_crops},
        fields[1].id: {PracticeChange.no_till, PracticeChange.no_cover_crop},
    }
    assert monitored_practice_results == [
        MonitoredPracticeEligibilityResult(
            field_id=fields[0].id,
            monitored_practices=expected_practices[fields[0].id],
            eligible_practice_combinations=[{PracticeChange.no_till, PracticeChange.no_cover_crop}],
            monitored_practice_eligible=False,
        ),
        MonitoredPracticeEligibilityResult(
            field_id=fields[1].id,
            monitored_practices=expected_practices[fields[1].id],
            eligible_practice_combinations=[{PracticeChange.no_till, PracticeChange.no_cover_crop}],
            monitored_practice_eligible=True,
        ),
    ]
    monitored_phase = e_phase if is_single_phase_data_collection else m_phase
    async with db_session_maker() as s:
        for field in fields:
            query = (
                select(FieldFacts)
                .where(FieldFacts.field_id == field.id)
                .where(FieldFacts.phase_id == monitored_phase.id)
                .order_by(desc(FieldFacts.created_at))
                .limit(1)
            )
            result = (await run_query(query=query, s=s)).scalar_one()
            assert set(result.facts[0]) == expected_practices[field.id]


@patch("projects.eligibility.measurement.methods._get_monitored_practices_for_fields")
@patch("programs.methods.get_practice_change_set_by_program_id", return_value={PracticeChange.no_till})
@patch(
    "projects.eligibility.measurement.methods.get_eligible_enrolment_practices_for_field",
    return_value=[{PracticeChange.no_till}],
)
async def test_run_monitored_practice_eligibility_always_eligible(
    mock_get_eligible_enrolment_practices_for_field,
    mock_get_practice_change_set_by_program_id,
    mock_get_monitored_practices_for_fields,
    mdl,
    app_request,
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=ProgramTemplate.event_based,
    )
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(
        phase_id=e_phase.id,
        type_=StageTypes.ASSIGN_PRACTICES,
        enabled=True,
        eligibility_method=EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE,
    )
    fields = [await mdl.Fields(parent_project_id=project.id) for _ in range(4)]

    monitored_practice_results = await run_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project.id,
        fields=fields,
        assign_practices_stage=assign_practices_stage,
    )
    for result in monitored_practice_results:
        assert result.monitored_practice_eligible is True
        assert result.monitored_practices == set()
        assert result.eligible_practice_combinations == [{PracticeChange.no_till}]


async def test_run_monitored_practice_eligibility_parameterized_eligibility_ineligible(mocker, app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(
        phase_id=e_phase.id,
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=EligibilityTypes.PARAMETERISED_ELIGIBILITY,
        enabled=True,
    )
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.no_till, always_eligible=False
    )
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.cover_crops, always_eligible=False
    )
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.composting, always_eligible=False
    )

    async def mock_get_monitored_practices_for_fields(
        request, program, fields, allowed_practices, actual_practices_by_field_id
    ):
        actual_practices_by_field_id[field.id] = {PracticeChange.no_till, PracticeChange.cover_crops}

    mocker.patch.object(
        projects.eligibility.measurement.methods,
        "_get_monitored_practices_for_fields",
        side_effect=mock_get_monitored_practices_for_fields,
    )
    mocker.patch(
        "projects.eligibility.measurement.methods.get_eligible_enrolment_practices_for_field",
        return_value=[{PracticeChange.composting}],
    )

    monitored_practice_eligibilities = await run_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project.id,
        fields=[field],
        assign_practices_stage=assign_practices_stage,
    )
    assert monitored_practice_eligibilities[0] == MonitoredPracticeEligibilityResult(
        field_id=field.id,
        monitored_practices={PracticeChange.no_till, PracticeChange.cover_crops},
        monitored_practice_eligible=False,
        eligible_practice_combinations=[
            {PracticeChange.composting},
        ],
    )


async def test_run_monitored_practice_eligibility_parameterized_eligibility_rules_ineligible(mocker, app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(
        phase_id=e_phase.id,
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=EligibilityTypes.PARAMETERISED_ELIGIBILITY,
        enabled=True,
    )
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.no_till, always_eligible=False
    )
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.cover_crops, always_eligible=False
    )
    await mdl.ProgramPracticeChange(
        program_id=program.id, practice_change=PracticeChange.composting, always_eligible=False
    )
    await mdl.PracticeChangeRules(
        stage_id=assign_practices_stage.id,
        rule=PracticeDependencyRule(
            type=PracticeRuleTypes.PRACTICE_DEPENDENCY,
            target=PracticeChange.no_till,
            condition=PracticeRuleConditions.ALL,
            value=[PracticeChange.composting],
        ).dict(),
    )

    async def mock_get_monitored_practices_for_fields(
        request, program, fields, allowed_practices, actual_practices_by_field_id
    ):
        actual_practices_by_field_id[field.id] = {PracticeChange.no_till, PracticeChange.cover_crops}

    mocker.patch.object(
        projects.eligibility.measurement.methods,
        "_get_monitored_practices_for_fields",
        side_effect=mock_get_monitored_practices_for_fields,
    )
    mocker.patch(
        "projects.eligibility.measurement.methods.get_eligible_enrolment_practices_for_field",
        return_value=[{PracticeChange.no_till}, {PracticeChange.cover_crops}, {PracticeChange.composting}],
    )

    monitored_practice_eligibilities = await run_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project.id,
        fields=[field],
        assign_practices_stage=assign_practices_stage,
    )
    assert monitored_practice_eligibilities[0] == MonitoredPracticeEligibilityResult(
        field_id=field.id,
        monitored_practices={PracticeChange.no_till, PracticeChange.cover_crops},
        monitored_practice_eligible=False,
        eligible_practice_combinations=[
            {PracticeChange.no_till},
            {PracticeChange.cover_crops},
            {PracticeChange.composting},
        ],
    )


@patch("projects.eligibility.measurement.router.m_elig_tasks.measurement_eligibility_task")
async def test_program_measurement_eligibility(mock_measurement_eligibility, async_client, mdl):
    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    project_2 = await mdl.Projects(program_id=program_1.id)
    response = await async_client.post(f"programs/{program_1.id}{paths.measurement_eligibility}")
    assert response.status_code == 200
    mock_measurement_eligibility.delay.assert_called_once_with(
        program_id=program_1.id, project_ids=[project_1.id, project_2.id], fs_user_id=13, fs_impersonator_user_id=None
    )


async def test_project_measurement_eligibility(mocker, async_client, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    mock_m_elig_task = mocker.patch("projects.eligibility.measurement.router.m_elig_tasks.measurement_eligibility_task")

    response = await async_client.post(f"projects/{project.id}{paths.measurement_eligibility}")
    assert response.status_code == 200
    mock_m_elig_task.delay.assert_called_once_with(
        program_id=program.id, project_ids=[project.id], fs_user_id=13, fs_impersonator_user_id=None
    )


async def test_measurement_eligibility_builder_measurement_eligible(mdl):
    elig_builder = MeasurementEligibilityBuilder(project_id=1)
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False

    # Field is eligible when all checks pass
    elig_builder.enrollment_data_entry_complete = EligibilityDecisionOption.ELIGIBLE
    elig_builder.contract_signed = EligibilityDecisionOption.ELIGIBLE
    elig_builder.measurement_data_entry_complete = EligibilityDecisionOption.ELIGIBLE
    elig_builder.monitored_practices = set()
    elig_builder.eligible_practice_combinations = []
    elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.ELIGIBLE
    elig_builder.monitored_commodity_crop_eligibility = EligibilityDecisionOption.ELIGIBLE
    elig_builder.field_boundary_eligibility = EligibilityDecisionOption.ELIGIBLE
    elig_builder.modelable_crop_type_eligibility = EligibilityDecisionOption.ELIGIBLE
    elig_builder.credited_crop_type_eligibility = EligibilityDecisionOption.ELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is True

    # Field is not eligible if any checks fail
    elig_builder.enrollment_data_entry_complete = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.enrollment_data_entry_complete = EligibilityDecisionOption.ELIGIBLE

    elig_builder.contract_signed = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.contract_signed = EligibilityDecisionOption.ELIGIBLE

    elig_builder.measurement_data_entry_complete = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.measurement_data_entry_complete = EligibilityDecisionOption.ELIGIBLE

    elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.ELIGIBLE

    elig_builder.monitored_commodity_crop_eligibility = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.monitored_commodity_crop_eligibility = EligibilityDecisionOption.ELIGIBLE

    elig_builder.field_boundary_eligibility = EligibilityDecisionOption.INELIGIBLE
    elig_builder.field_boundary_invalid_reason = "Self-intersection"
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.field_boundary_eligibility = EligibilityDecisionOption.ELIGIBLE

    elig_builder.credited_crop_type_eligibility = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.credited_crop_type_eligibility = EligibilityDecisionOption.ELIGIBLE

    elig_builder.modelable_crop_type_eligibility = EligibilityDecisionOption.INELIGIBLE
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is False
    elig_builder.modelable_crop_type_eligibility = EligibilityDecisionOption.ELIGIBLE

    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is True

    elig_builder.contract_signed = EligibilityDecisionOption.DISABLED
    elig_builder.measurement_data_entry_complete = EligibilityDecisionOption.DISABLED
    elig_builder.monitored_practice_eligibility = EligibilityDecisionOption.DISABLED
    elig_builder.monitored_commodity_crop_eligibility = EligibilityDecisionOption.DISABLED
    elig_builder.credited_crop_type_eligibility = EligibilityDecisionOption.DISABLED
    elig_builder.modelable_crop_type_eligibility = EligibilityDecisionOption.DISABLED
    elig_builder.compute_measurement_eligible()
    assert elig_builder.measurement_eligible is True


async def test_calculate_field_measurement_eligibility_success(
    mdl, app_request, mocker, orm_select, create_cropping_event_data
):
    # Check the happy path where all checks are satisfied for a field
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=ProgramTemplate.legacy,
    )
    project = await mdl.Projects(program_id=program.id, reporting_enabled=True)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.ASSIGN_PRACTICES, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.CONTRACT, enabled=True)

    field = await mdl.Fields(parent_project_id=project.id)

    # Set up checks to all succeed
    mock_phase_completion({e_phase.id: True, m_phase.id: True}, mocker, orm_select)
    # Finish Stage has been completed
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=m_phase.id, is_completed=True)
    mock_rerun_e_phase_eligibility = mock_determine_project_eligibilities_at_enrolment(mocker=mocker)
    await setup_contract(mdl, project.id, e_phase.id, completed=True)
    mock_monitored_practice_eligibility(field.id, mocker, success=True)
    mock_get_reporting_period_commodity_crop_cultivation_cycles(
        has_cultivation_cycles=True, mocker=mocker, create_cropping_event_data=create_cropping_event_data
    )
    mock_get_geometries_for_core_fields(fs_field_id=field.fs_field_id, is_valid=True, mocker=mocker)
    set_up_crop_types(
        is_legacy=True,
        crop_types=["corn_wet"],
        translation_dict={"corn_wet": "corn"},
        enabled_dict={ScenariosServiceApi.measure_api: {"corn": True}, ScenariosServiceApi.biofuels_api: {"soy": True}},
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    measurement_eligibility = (
        await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == field.id])
    )[0]
    assert measurement_eligibility.contract_signed == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.enrollment_data_entry_complete == EligibilityDecisionOption.ELIGIBLE
    mock_rerun_e_phase_eligibility.assert_called_with(
        request=app_request, project_id=project.id, stage_id=assign_practices_stage.id
    )
    assert measurement_eligibility.measurement_data_entry_complete == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.monitored_practice_eligibility == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.monitored_commodity_crop_eligibility == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.field_boundary_eligibility == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.modelable_crop_type_eligibility == EligibilityDecisionOption.ELIGIBLE
    assert measurement_eligibility.measurement_eligible is True
    assert measurement_eligibility.ineligibility_reasons == []

    resulting_field = (await orm_select(Fields, where=[Fields.id == field.id]))[0]
    assert resulting_field.measurement_eligibility is True
    assert len((await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == field.id]))) == 1

    # Check that additional runs will still result in just one non-deleted record per field.
    await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    assert (
        len(
            (
                await orm_select(
                    MeasurementEligibility,
                    where=[MeasurementEligibility.field_id == field.id, MeasurementEligibility.deleted_at.is_(None)],
                )
            )
        )
        == 1
    )
    assert len((await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == field.id]))) == 2


async def test_calculate_field_measurement_eligibility_skipped_if_not_reporting_enabled(
    mdl, app_request, mocker, orm_select, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
    )
    project = await mdl.Projects(program_id=program.id, reporting_enabled=False)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.CONTRACT, enabled=True)

    field = await mdl.Fields(parent_project_id=project.id)

    result = await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    assert result == []

    assert (await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == field.id])) == []


async def test_calculate_field_measurement_eligibility_check_disablement(
    mdl, app_request, mocker, orm_select, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=ProgramTemplate.legacy,
    )
    project = await mdl.Projects(program_id=program.id, reporting_enabled=True)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.ASSIGN_PRACTICES, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)

    unrelated_project = await mdl.Projects(program_id=program.id, reporting_enabled=True)
    await mdl.Fields(parent_project_id=unrelated_project.id)

    mock_determine_project_eligibilities_at_enrolment(mocker=mocker)
    mock_monitored_practice_eligibility(field.id, mocker, success=True)
    mock_get_reporting_period_commodity_crop_cultivation_cycles(
        has_cultivation_cycles=True, mocker=mocker, create_cropping_event_data=create_cropping_event_data
    )
    mock_get_geometries_for_core_fields(fs_field_id=field.fs_field_id, is_valid=True, mocker=mocker)
    set_up_crop_types(
        is_legacy=True,
        crop_types=["corn_wet"],
        translation_dict={"corn_wet": "corn"},
        enabled_dict={ScenariosServiceApi.measure_api: {"corn": True}, ScenariosServiceApi.biofuels_api: {"soy": True}},
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    # If E Phase data entry incomplete, then modelable crop type check is disabled.
    mock_phase_completion({e_phase.id: False, m_phase.id: True}, mocker, orm_select)
    measurement_elig: MeasurementEligibilityBuilder = (
        await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    )[0]
    assert measurement_elig.modelable_crop_type_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.measurement_eligible is False
    assert measurement_elig.ineligibility_reasons == ["Incomplete Enrollment Phase data"]
    # mrv_fields.measurement_eligibility should be set
    assert (await orm_select(Fields, where=[Fields.id == field.id]))[0].measurement_eligibility is False

    # If M Phase data entry incomplete, then other M Phase checks are disabled.
    mock_phase_completion({e_phase.id: True, m_phase.id: False}, mocker, orm_select)
    measurement_elig: MeasurementEligibilityBuilder = (
        await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    )[0]
    assert measurement_elig.measurement_data_entry_complete == EligibilityDecisionOption.INELIGIBLE
    assert measurement_elig.monitored_practice_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.monitored_commodity_crop_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.credited_crop_type_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.modelable_crop_type_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.measurement_eligible is False
    assert measurement_elig.ineligibility_reasons == ["Incomplete Measurement Phase data"]
    # mrv_fields.measurement_eligibility should be set
    assert (await orm_select(Fields, where=[Fields.id == field.id]))[0].measurement_eligibility is False

    # If Contract Stage is missing or disabled, then contract check is disabled.
    measurement_elig: MeasurementEligibilityBuilder = (
        await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    )[0]
    assert measurement_elig.contract_signed == EligibilityDecisionOption.DISABLED
    # Stage present but disabled
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.CONTRACT, enabled=False)
    measurement_elig: MeasurementEligibilityBuilder = (
        await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    )[0]
    assert measurement_elig.contract_signed == EligibilityDecisionOption.DISABLED

    # If M Phase is disabled or missing, then M Phase checks are disabled.
    m_phase.enabled = False
    await update.update(request=app_request, instances=[m_phase], type=Phases)
    mock_phase_completion({e_phase.id: True}, mocker, orm_select)

    measurement_elig: MeasurementEligibilityBuilder = (
        await calculate_field_measurement_eligibility(app_request, program.id, project.id)
    )[0]
    assert measurement_elig.measurement_data_entry_complete == EligibilityDecisionOption.DISABLED
    assert measurement_elig.credited_crop_type_eligibility == EligibilityDecisionOption.DISABLED
    assert measurement_elig.measurement_eligible is True
    # mrv_fields.measurement_eligibility should be set
    assert (await orm_select(Fields, where=[Fields.id == field.id]))[0].measurement_eligibility is True


async def test_check_monitored_practice_eligibility_ineligible(mdl, app_request, mocker):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
    )
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.ASSIGN_PRACTICES, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)

    # Monitored Practice Eligibility fails
    mock_monitored_practice_eligibility(field.id, mocker, success=False)

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_monitored_practice_eligibility(
        request=app_request,
        program=program,
        project_id=project.id,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].monitored_practice_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].monitored_practices == {PracticeChange.composting}
    assert elig_builders[0].eligible_practice_combinations == [{PracticeChange.no_till}]
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == [
        "Monitored practices {'Composting'} not included in eligible practices: [{'No Till'}]"
    ]


@pytest.mark.parametrize("program_template", [ProgramTemplate.legacy, ProgramTemplate.event_based])
async def test_check_monitored_commodity_crop_eligibility_ineligible(
    program_template, mdl, app_request, mocker, create_cropping_event_data, create_interval_data
):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=program_template,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    set_up_monitored_commodity_crop(
        is_legacy=program_template == ProgramTemplate.legacy,
        has_monitored_commodity_crop=False,
        reporting_period_year=2025,
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
        create_interval_data=create_interval_data,
    )

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_monitored_commodity_crop_eligibility(
        request=app_request,
        program=program,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].monitored_commodity_crop_eligibility == EligibilityDecisionOption.INELIGIBLE
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["No commodity crop in Reporting Period"]


async def test_check_contract_signed_unsigned(mdl, app_request):
    program = await mdl.Programs(accounting_method=AccountingMethod.intervention)
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.CONTRACT, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)

    # Contract not yet signed
    await setup_contract(mdl, project.id, e_phase.id, completed=False)

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    await check_contract_signed(
        request=app_request, program_id=program.id, project_id=project.id, elig_builders=elig_builders
    )

    assert elig_builders[0].contract_signed == EligibilityDecisionOption.INELIGIBLE
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Contract is missing or unsigned"]


async def test_check_e_phase_completion_incomplete(mdl, app_request, mocker, orm_select):
    program = await mdl.Programs(accounting_method=AccountingMethod.intervention)
    project = await mdl.Projects(program_id=program.id)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    field = await mdl.Fields(parent_project_id=project.id)

    # E phase incomplete
    mock_phase_completion({e_phase.id: False, m_phase.id: True}, mocker, orm_select)
    mock_rerun_e_phase_eligibility = mock_determine_project_eligibilities_at_enrolment(mocker=mocker)

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    await check_e_phase_completion(
        request=app_request, project_id=project.id, e_phase=e_phase, elig_builders=elig_builders
    )

    mock_rerun_e_phase_eligibility.assert_not_called()
    assert elig_builders[0].enrollment_data_entry_complete == EligibilityDecisionOption.INELIGIBLE
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Incomplete Enrollment Phase data"]


async def test_check_field_boundary_eligibility_invalid(
    mdl, app_request, mocker, orm_select, create_cropping_event_data
):
    program = await mdl.Programs(accounting_method=AccountingMethod.intervention)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    # Geometry invalid
    mock_get_geometries_for_core_fields(fs_field_id=field.fs_field_id, is_valid=False, mocker=mocker)

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_field_boundary_eligibility(
        fields=[field], field_ids=[field.id], elig_builders_by_field=elig_builders_by_field
    )

    assert elig_builders[0].field_boundary_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].field_boundary_invalid_reason == "Self-intersection[0 1]"
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Self-intersection[0 1]"]


@pytest.mark.parametrize(
    "program_template,corn_crop_type", [(ProgramTemplate.legacy, "corn_wet"), (ProgramTemplate.event_based, "corn")]
)
async def test_check_modelable_crop_type_eligibility_unknown_crop_type_intervention(
    program_template, corn_crop_type, mdl, app_request, mocker, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=program_template,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    set_up_crop_types(
        is_legacy=program_template == ProgramTemplate.legacy,
        crop_types=[corn_crop_type, "other"],
        translation_dict={"corn_wet": "corn"},
        enabled_dict={ScenariosServiceApi.measure_api: {"corn": True}, ScenariosServiceApi.biofuels_api: {"soy": True}},
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_modelable_crop_type_eligibility(
        request=app_request,
        program=program,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].modelable_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].unmodelable_crop_types == ["other"]
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Crop(s) not modelable: ['other']"]


@pytest.mark.parametrize(
    "program_template,corn_crop_type", [(ProgramTemplate.legacy, "corn_wet"), (ProgramTemplate.event_based, "corn")]
)
async def test_check_modelable_crop_type_eligibility_unknown_crop_type_inventory(
    program_template, corn_crop_type, mdl, app_request, mocker, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.inventory,
        is_single_phase_data_collection=True,
        required_years_of_history=3,
        program_template=program_template,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    set_up_crop_types(
        is_legacy=program_template == ProgramTemplate.legacy,
        crop_types=[corn_crop_type, "other"],
        translation_dict={"corn_wet": "corn"},
        enabled_dict={ScenariosServiceApi.measure_api: {"corn": True}, ScenariosServiceApi.biofuels_api: {"soy": True}},
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_modelable_crop_type_eligibility(
        request=app_request,
        program=program,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].modelable_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].unmodelable_crop_types == ["other"]
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Crop(s) not modelable: ['other']"]


@pytest.mark.parametrize(
    "program_template,soy_crop_type", [(ProgramTemplate.legacy, "soy_wet"), (ProgramTemplate.event_based, "soy")]
)
async def test_check_modelable_crop_type_eligibility_unknown_crop_type_biofuels(
    program_template, soy_crop_type, mdl, app_request, mocker, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.biofuels,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=program_template,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    set_up_crop_types(
        is_legacy=program_template == ProgramTemplate.legacy,
        crop_types=[soy_crop_type, "other"],
        translation_dict={"soy_wet": "soy"},
        enabled_dict={ScenariosServiceApi.measure_api: {"corn": True}, ScenariosServiceApi.biofuels_api: {"soy": True}},
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_modelable_crop_type_eligibility(
        request=app_request,
        program=program,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].modelable_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].unmodelable_crop_types == ["other"]
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Crop(s) not modelable: ['other']"]


@pytest.mark.parametrize(
    "program_template,corn_crop_type", [(ProgramTemplate.legacy, "corn_wet"), (ProgramTemplate.event_based, "corn")]
)
async def test_check_modelable_crop_type_eligibility_disabled_crop_type_intervention(
    program_template, corn_crop_type, mdl, app_request, mocker, create_cropping_event_data
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
        program_template=program_template,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    set_up_crop_types(
        is_legacy=program_template == ProgramTemplate.legacy,
        crop_types=[corn_crop_type],
        translation_dict={"corn_wet": "corn"},
        enabled_dict={
            ScenariosServiceApi.measure_api: {"corn": False},
            ScenariosServiceApi.biofuels_api: {"soy": True},
        },
        mocker=mocker,
        create_cropping_event_data=create_cropping_event_data,
    )

    elig_builders = [MeasurementEligibilityBuilder(project_id=project.id, field_id=field.id)]
    elig_builders_by_field = {elig_builder.field_id: elig_builder for elig_builder in elig_builders}
    await check_modelable_crop_type_eligibility(
        request=app_request,
        program=program,
        fields=[field],
        elig_builders=elig_builders,
        elig_builders_by_field=elig_builders_by_field,
    )

    assert elig_builders[0].modelable_crop_type_eligibility == EligibilityDecisionOption.INELIGIBLE
    assert elig_builders[0].unmodelable_crop_types == ["corn"]
    elig_builders[0].compute_measurement_eligible()
    assert elig_builders[0].ineligibility_reasons == ["Crop(s) not modelable: ['corn']"]


def mock_phase_completion(phase_to_success: dict[int, bool], mocker, orm_select) -> None:
    async def mock_get_project_phase_completion(_request, project_id, phase_id) -> ProjectPhaseCompletionResponse:
        return ProjectPhaseCompletionResponse(
            project_id=project_id,
            phase_id=phase_id,
            is_completed=phase_to_success.get(phase_id),
            allow_post_close_edit=False,
        )

    async def mock_calculate_project_phase_completion(
        _request, project_id, phase_id, save_result=True
    ) -> ProjectPhaseCompletionResponse:
        phase_type = (await orm_select(Phases, where=[Phases.id == phase_id]))[0].type_
        if phase_type == PhaseTypes.MONITORING:
            # Shouldn't save the result for M Phase
            assert save_result is False
        return ProjectPhaseCompletionResponse(
            project_id=project_id,
            phase_id=phase_id,
            is_completed=phase_to_success.get(phase_id),
            allow_post_close_edit=False,
        )

    mocker.patch.object(projects.methods, "get_project_phase_completion", side_effect=mock_get_project_phase_completion)
    mocker.patch.object(
        projects.methods, "calculate_project_phase_completion", side_effect=mock_calculate_project_phase_completion
    )


def mock_determine_project_eligibilities_at_enrolment(mocker) -> MagicMock:
    return mocker.patch(
        "projects.eligibility.measurement.methods.determine_project_eligibilities_at_enrolment",
    )


async def setup_contract(mdl, project_id: int, phase_id: int, completed: bool):
    await mdl.ProjectContracts(
        project=project_id,
        docusign_status=EnvelopeStatus.completed if completed else EnvelopeStatus.sent,
        docusign_envelope_id="abc-123" if completed else None,
        phase_id=phase_id,
    )


def mock_monitored_practice_eligibility(field_id: int, mocker, success: bool) -> None:
    mocker.patch.object(
        methods,
        "_calculate_project_monitored_practice_eligibility",
        return_value=[
            MonitoredPracticeEligibilityResult(
                field_id=field_id,
                monitored_practices={PracticeChange.composting},
                monitored_practice_eligible=success,
                eligible_practice_combinations=[{PracticeChange.composting}] if success else [{PracticeChange.no_till}],
            )
        ],
    )


async def set_up_monitored_events(
    is_single_phase_data_collection,
    mdl,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mocker,
) -> tuple[Programs, Projects, list[Fields], Phases, Phases, Stage]:
    accounting_method = AccountingMethod.inventory if is_single_phase_data_collection else AccountingMethod.intervention
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        accounting_method=accounting_method,
        is_single_phase_data_collection=is_single_phase_data_collection,
        required_years_of_history=3,
        program_template=ProgramTemplate.event_based,
    )
    project = await mdl.Projects(program_id=program.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    assign_practices_stage = await mdl.Stage(
        phase_id=e_phase.id, type_=StageTypes.ASSIGN_PRACTICES, eligibility_method=EligibilityTypes.CUSTOM, enabled=True
    )
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    # allowed practice changes
    allowed_practice_changes = [
        PracticeChange.cover_crops,
        PracticeChange.no_cover_crop,
        PracticeChange.conventional_till,
        PracticeChange.reduced_till,
        PracticeChange.no_till,
    ]
    for practice_change in allowed_practice_changes:
        await mdl.ProgramPracticeChange(program_id=program.id, practice_change=practice_change)
    # eligible practice changes
    await mdl.FieldFacts(
        eligible=True,
        facts=[[PracticeChange.no_till, PracticeChange.no_cover_crop]],
        phase_id=e_phase.id,
        field_id=field_1.id,
        state=FieldDataState.eligible,
        data_type=EntityDataType.entity_practices,
    )
    await mdl.FieldFacts(
        eligible=True,
        facts=[[PracticeChange.no_till, PracticeChange.no_cover_crop]],
        phase_id=e_phase.id,
        field_id=field_2.id,
        state=FieldDataState.eligible,
        data_type=EntityDataType.entity_practices,
    )
    field_id_to_events = {
        field_1.id: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type="corn",
                    reductions=[],
                )
            ),
            TillageEvent.parse_obj(
                create_tillage_event_data(
                    occurred_at=datetime(2024, 12, 2, tzinfo=timezone.utc),
                    tillage_practice=TillagePractice.reduced_till,
                )
            ),
            TillageEvent.parse_obj(
                create_tillage_event_data(
                    occurred_at=datetime(2024, 12, 1, tzinfo=timezone.utc),
                    tillage_practice=TillagePractice.conventional_till,
                )
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2024, 6, 2, tzinfo=timezone.utc),
                        end=datetime(2024, 11, 30, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COVER,
                    crop_type="basic_cover_crop",
                    reductions=[],
                )
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type="corn",
                    reductions=[],
                )
            ),
        ],
        field_2.id: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2025, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2025, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type="corn",
                    reductions=[],
                )
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type="corn",
                    reductions=[],
                )
            ),
        ],
    }

    def mock_fetch_events_for_field_phase(request, field_id, enrollment_phase_only, is_single_phase_program):
        return field_id_to_events[field_id]

    mocker.patch(
        "projects.eligibility.measurement.methods.fetch_events_for_field_phase",
        side_effect=mock_fetch_events_for_field_phase,
    )
    return program, project, [field_1, field_2], e_phase, m_phase, assign_practices_stage, allowed_practice_changes


def set_up_monitored_commodity_crop(
    is_legacy: bool,
    has_monitored_commodity_crop: bool,
    reporting_period_year: int,
    mocker,
    create_cropping_event_data,
    create_interval_data,
):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(reporting_period_year - 1, 1, 1, tzinfo=timezone.utc),
                    end=datetime(reporting_period_year - 1, 6, 1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COMMODITY,
                crop_type="corn",
                reductions=[],
            )
        )
    ]
    if has_monitored_commodity_crop:
        cropping_events.append(
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(reporting_period_year, 1, 1, tzinfo=timezone.utc),
                        end=datetime(reporting_period_year, 6, 1, tzinfo=timezone.utc),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    crop_type="corn",
                    reductions=[],
                )
            )
        )
    if is_legacy:
        mocker.patch(
            "projects.eligibility.measurement.methods.get_entity_events_for_field",
            return_value={PhaseTypes.MONITORING: cropping_events},
        )
    else:
        mocker.patch(
            "projects.eligibility.measurement.methods.fetch_events_for_field_phase", return_value=cropping_events
        )


def mock_get_reporting_period_commodity_crop_cultivation_cycles(
    has_cultivation_cycles: bool, mocker, create_cropping_event_data
):
    cultivation_cycles = []
    if has_cultivation_cycles:
        cultivation_cycles.append(
            CultivationCycle(
                id=CultivationCycleId(
                    crop_event_id="1",
                    crop_type="corn",
                    start_date="2024-01-01T07:00:00+00:00",
                    end_date="2024-02-01T07:00:00+00:00",
                    harvest_year=2024,
                ),
                start=datetime(2024, 1, 1),
                end=datetime(2024, 2, 1),
                events=[CroppingEvent.parse_obj(create_cropping_event_data(crop_usage=CropUsage.COMMODITY))],
            )
        )
    mocker.patch(
        "projects.eligibility.measurement.methods._get_reporting_period_commodity_crop_cultivation_cycles",
        return_value=cultivation_cycles,
    )


def mock_get_geometries_for_core_fields(fs_field_id: int, is_valid: bool, mocker) -> None:
    test_geojson_valid = {
        "type": "MultiPolygon",
        "coordinates": [
            [
                [
                    [0.0, 0.0],
                    [0.0, 1.0],
                    [1.0, 1.0],
                    [1.0, 0.0],
                    [0.0, 0.0],
                ]
            ],
            [
                [
                    [1.0, 1.0],
                    [1.0, 2.0],
                    [2.0, 2.0],
                    [2.0, 1.0],
                    [1.0, 1.0],
                ]
            ],
        ],
    }
    test_geojson_invalid = {
        "type": "MultiPolygon",
        "coordinates": [
            [
                [
                    [0.0, 0.0],
                    [0.0, 1.0],
                    [1.0, 1.0],
                    [1.0, 0.0],
                    [0.0, 0.0],
                ]
            ],
            [
                [
                    [0.0, 0.0],
                    [0.0, 1.0],
                    [1.0, 1.0],
                    [1.0, 0.0],
                    [0.0, 0.0],
                ]
            ],
        ],
    }
    if is_valid:
        return_value = {fs_field_id: test_geojson_valid}
    else:
        return_value = {fs_field_id: test_geojson_invalid}
    mocker.patch(
        "projects.eligibility.measurement.methods.fields_methods.get_geometries_for_core_fields",
        return_value=return_value,
    )


def set_up_crop_types(
    is_legacy: bool,
    crop_types: list[str],
    translation_dict: dict[str, str],
    enabled_dict: dict[str, dict[str, bool]],
    mocker,
    create_cropping_event_data,
):
    cropping_events = [
        CroppingEvent.parse_obj(create_cropping_event_data(crop_type=crop_type)) for crop_type in crop_types
    ]
    if is_legacy:
        mocker.patch(
            "projects.eligibility.measurement.methods.get_entity_events_for_field",
            return_value={PhaseTypes.MONITORING: cropping_events},
        )
    else:
        mocker.patch(
            "projects.eligibility.measurement.methods.fetch_events_for_field_phase", return_value=cropping_events
        )

    def mock_translate_core_crop_name(request, core_crop_name, planting_date, field_id):
        return translation_dict[core_crop_name]

    mocker.patch(
        "projects.eligibility.measurement.methods.translate_and_maybe_substitute_core_crop_name",
        side_effect=mock_translate_core_crop_name,
    )

    mocker.patch(
        "projects.eligibility.measurement.methods.are_crops_measure_api_enabled",
        return_value=enabled_dict.get(ScenariosServiceApi.measure_api, {}),
    )
    mocker.patch(
        "projects.eligibility.measurement.methods.are_crops_biofuels_api_enabled",
        return_value=enabled_dict.get(ScenariosServiceApi.biofuels_api, {}),
    )


async def test_save_monitoring_eligibilities(app_request, mdl, db_session_maker):
    program = await mdl.Programs()

    deleted_phase = await mdl.Phases(program_id=program.id, deleted_at=datetime.now(), type_=PhaseTypes.MONITORING)
    with pytest.raises(ValueError) as exc_info:
        await save_monitored_practice_eligibilities(app_request, deleted_phase, [])
    assert (
        exc_info.value.args[0]
        == f"Phase {deleted_phase.id} has been deleted and cannot be used for measurement eligibility."
    )

    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    prac1 = [PracticeChange.composting, PracticeChange.cover_crops]
    prac2 = [PracticeChange.crop_rotation, PracticeChange.no_cover_crop]
    elig1 = MonitoredPracticeEligibilityResult(
        monitored_practices=set(prac1),
        monitored_practice_eligible=False,
        eligible_practice_combinations=[],
        field_id=field1.id,
    )
    elig2 = MonitoredPracticeEligibilityResult(
        monitored_practices=set(prac2),
        monitored_practice_eligible=True,
        eligible_practice_combinations=[],
        field_id=field2.id,
    )
    monitoring_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    await save_monitored_practice_eligibilities(app_request, monitoring_phase, [elig1, elig2])

    async with db_session_maker() as s:
        query = (
            select(FieldFacts).where(FieldFacts.phase_id == monitoring_phase.id).where(FieldFacts.field_id == field1.id)
        )
        result = (await run_query(query=query, s=s)).scalar_one()
        assert len(result.facts) == 1
        assert sorted(result.facts[0]) == sorted([prac.value for prac in prac1])

        query = (
            select(FieldFacts).where(FieldFacts.phase_id == monitoring_phase.id).where(FieldFacts.field_id == field2.id)
        )
        result = (await run_query(query=query, s=s)).scalar_one()
        assert len(result.facts) == 1
        assert sorted(result.facts[0]) == sorted([prac.value for prac in prac2])


async def test_measurement_eligibility_task_project_level(mocker, app_request):
    mock_calculate_eligibility = mocker.patch.object(
        projects.eligibility.measurement.tasks.measurement_elig_methods, "calculate_field_measurement_eligibility"
    )
    m_elig_tasks.measurement_eligibility_task(
        program_id=1, project_ids=[2], request=app_request, fs_user_id=3, fs_impersonator_user_id=4
    )
    mock_calculate_eligibility.assert_called_once_with(request=app_request, program_id=1, project_id=2)


async def test_measurement_eligibility_task_program_level(mocker, app_request):
    mock_calculate_eligibility = mocker.patch.object(
        projects.eligibility.measurement.tasks.measurement_elig_methods, "calculate_field_measurement_eligibility"
    )
    m_elig_tasks.measurement_eligibility_task(
        program_id=5, project_ids=[6], request=app_request, fs_user_id=3, fs_impersonator_user_id=4
    )
    mock_calculate_eligibility.assert_called_once_with(request=app_request, program_id=5, project_id=6)


@patch("projects.eligibility.measurement.methods.get_entity_events_for_field")
async def test_get_reporting_period_commodity_crop_cultivation_cycles(
    mock_get_entity_events_for_field,
    mdl,
    create_cropping_event_data,
    create_fallow_period_data,
    create_interval_data,
    app_request,
):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    mock_get_entity_events_for_field.return_value = {
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2023, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2023, 7, 1, 0, 0, 0, 0, pytz.UTC),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 7, 1, 0, 0, 0, 0, pytz.UTC),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
        ]
    }
    cultivation_cycles = await _get_reporting_period_commodity_crop_cultivation_cycles(app_request, program, field)
    assert len(cultivation_cycles) == 1

    mock_get_entity_events_for_field.return_value = {
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    interval=create_interval_data(
                        start=datetime(2023, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2023, 7, 1, 0, 0, 0, 0, pytz.UTC),
                    ),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
            FallowPeriod.parse_obj(
                create_fallow_period_data(
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 7, 1, 0, 0, 0, 0, pytz.UTC),
                    ),
                )
            ),
        ]
    }
    cultivation_cycles = await _get_reporting_period_commodity_crop_cultivation_cycles(app_request, program, field)
    assert len(cultivation_cycles) == 0
