import json
from datetime import datetime

from projects import paths as project_paths


async def test_get_measurement_eligible_field_ids_by_program_id(mdl, async_client):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    # field 1 ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_1.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )
    # field 2 deleted ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=datetime(2024, 1, 1),
    )
    # field 2 eligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=True,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )

    # test measurement eligible
    response = await async_client.get(
        f"programs/{program.id}{project_paths.measurement_eligibility}{project_paths.fields}?measurement_eligible=true"
    )
    assert response.status_code == 200
    assert json.loads(response.content) == [field_2.id]

    # test measurement ineligible
    response = await async_client.get(
        f"programs/{program.id}{project_paths.measurement_eligibility}{project_paths.fields}?measurement_eligible=false"
    )
    assert response.status_code == 200
    assert json.loads(response.content) == [field_1.id]

    # test no query param
    response = await async_client.get(
        f"programs/{program.id}{project_paths.measurement_eligibility}{project_paths.fields}"
    )
    assert response.status_code == 200
    assert json.loads(response.content) == [field_2.id]
