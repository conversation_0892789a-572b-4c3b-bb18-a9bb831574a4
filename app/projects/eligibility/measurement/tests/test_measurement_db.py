from datetime import datetime

from programs.enums import PracticeChange
from projects.eligibility.measurement.db import (
    get_count_of_measurement_eligible_fields_by_program_id,
    get_measurement_eligible_field_ids_by_program_id,
    save_measurement_eligibilities,
)
from projects.eligibility.measurement.model import MeasurementEligibility
from projects.eligibility.measurement.schema import (
    EligibilityDecisionOption,
    MeasurementEligibilityBuilder,
)


async def test_get_measurement_eligible_field_ids_by_program_id(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    # field 1 ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_1.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )
    # field 2 deleted ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=datetime(2024, 1, 1),
    )
    # field 2 eligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=True,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )

    # test get measurement eligible
    res = await get_measurement_eligible_field_ids_by_program_id(
        request=app_request, program_id=program.id, measurement_eligible=True
    )
    assert res == [field_2.id]

    # test get measurement ineligible
    res = await get_measurement_eligible_field_ids_by_program_id(
        request=app_request, program_id=program.id, measurement_eligible=False
    )
    assert res == [field_1.id]


async def test_get_count_measurement_of_eligible_fields_by_program_id(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_3 = await mdl.Fields(parent_project_id=project.id)
    # field 1 ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_1.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )
    # field 2 deleted ineligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=False,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=datetime(2024, 1, 1),
    )
    # field 2 eligible record
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_2.id,
        measurement_eligible=True,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=field_3.id,
        measurement_eligible=True,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )

    # test get measurement eligible
    res = await get_count_of_measurement_eligible_fields_by_program_id(
        request=app_request, program_id=program.id, measurement_eligible=True
    )
    assert res == 2

    # test get measurement ineligible
    res = await get_count_of_measurement_eligible_fields_by_program_id(
        request=app_request, program_id=program.id, measurement_eligible=False
    )
    assert res == 1


def get_eligible_measurement_elig_builder(field_id: int, project_id: int) -> MeasurementEligibilityBuilder:
    return MeasurementEligibilityBuilder(
        project_id=project_id,
        field_id=field_id,
        enrollment_data_entry_complete=EligibilityDecisionOption.ELIGIBLE,
        contract_signed=EligibilityDecisionOption.ELIGIBLE,
        measurement_data_entry_complete=EligibilityDecisionOption.ELIGIBLE,
        monitored_practice_eligibility=EligibilityDecisionOption.ELIGIBLE,
        monitored_practices={PracticeChange.no_till},
        eligible_practice_combinations=[{PracticeChange.no_till}],
        field_boundary_eligibility=EligibilityDecisionOption.ELIGIBLE,
        field_boundary_invalid_reason=None,
        credited_crop_type_eligibility=EligibilityDecisionOption.ELIGIBLE,
        credited_crop_type=None,
        modelable_crop_type_eligibility=EligibilityDecisionOption.ELIGIBLE,
        unmodelable_crop_types=None,
    )


async def test_save_measurement_eligibilities(mdl, app_request, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    deleted_field = await mdl.Fields(parent_project_id=project.id, deleted_at=datetime.now())

    # A pre-existing MeasurementEligibility for a now-deleted field in the project will be marked deleted
    await mdl.MeasurementEligibility(
        project_id=project.id,
        field_id=deleted_field.id,
        measurement_eligible=True,
        ineligibility_reasons=[],
        monitored_practices=[],
        eligible_practice_combinations=[],
        deleted_at=None,
    )

    eligible_m_elig_builder = get_eligible_measurement_elig_builder(field_id=field.id, project_id=project.id)
    eligible_m_elig_builder.compute_measurement_eligible()
    await save_measurement_eligibilities(request=app_request, elig_builders=[eligible_m_elig_builder])
    # Deleted field eligibility is soft deleted
    deleted_field_m_elig = (
        await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == deleted_field.id])
    )[0]
    assert deleted_field_m_elig.deleted_at is not None
    # New eligibility builder is persisted
    field_m_elig: MeasurementEligibility = (
        await orm_select(MeasurementEligibility, where=[MeasurementEligibility.field_id == field.id])
    )[0]
    assert field_m_elig.field_id == field.id
    assert field_m_elig.project_id == project.id
    assert field_m_elig.deleted_at is None
