import elasticapm
from fastapi import APIRouter, Depends, Request, status

from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from projects import methods, paths

logger = get_logger(__name__)
tags = ["scripts"]
super_admin = ["super_admin"]
router = APIRouter(prefix=paths.scripts_base)


@elasticapm.async_capture_span()
@router.post(
    paths.populate_all_project_stage_completion_summaries,  # /scripts/projects/populate_all_project_stage_completion_summaries
    tags=tags + super_admin,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.POPULATE_COMPLETION_SUMMARIES]))],
)
async def populate_all_project_stage_completion_summaries_router(request: Request) -> None:
    await methods.populate_all_project_stage_completion_summaries(request)
