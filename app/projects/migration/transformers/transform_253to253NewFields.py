from __future__ import annotations

from annotations import Any
from projects.migration.project_transformer import ProjectTransformer
from projects.migration.schema import (
    Field,
    FieldBaseline,
    ProjectMigrationCommand,
    Rule,
    RuleFunctionData,
    RuleSet,
)
from projects.migration.transformers import common_transforms


class Transform253to253New<PERSON>ields(ProjectTransformer):
    """
    Transform data from program 253 to program 253, but only for new fields
    """

    async def transform(self) -> ProjectMigrationCommand | None:
        if self.transform_request is None:
            return None

        self.transform_request.rulesets = self.translation_rules()
        result = await self.gather_data(transform_request=self.transform_request)
        return await self.create_data(result)

    async def field_allowed(self, field: Field | None = None, target_project_id: int | None = None) -> bool:
        if field is None:
            return False
        field_id = field.id
        # we only want to allow non-returning fields
        baseline: FieldBaseline = await self.get_field_baseline(field_id)
        if baseline is None:
            self.add_error(f"WARNING: Field {field_id} has no baseline")
            return True  # allow if no baseline

        is_returning = self.ov(baseline, "is_returning")
        if is_returning is None:
            self.add_error(f"WARNING: Field {field_id} has is_returning not set")
            return True

        self.add_error(f"INFO: Field {field_id} has is_returning {is_returning}")
        return not is_returning

    def get_cover_crop_mix(self, src: RuleFunctionData) -> str:
        return common_transforms.get_cover_crop_mix(src)

    def get_assigned_practices(self, src: RuleFunctionData) -> dict[int, str] | None:
        return common_transforms.get_assigned_practices(src)

    async def crop_type(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type(src)

    async def practice_to_croptype(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_croptype(src)

    async def practice_to_usage(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_usage(src)

    async def tillage_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_practice(src)

    async def tillage_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_practice(src)

    async def assigned_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_depth(src)

    async def assigned_practice_to_soil_inversion(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_soil_inversion(src)

    def translation_rules(self) -> list[RuleSet]:
        """
        Returns a list of rules to translate values from one program to another.
        """

        source_program_id = 253
        target_program_id = 253

        return [
            RuleSet(
                reference="1 - Crop Type",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_type",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/crop_type",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=5,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="2 - Crop Class",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/cover_crop_mix",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="cover_crop_mix",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/crop_usage",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=5,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="3 - Planting Date",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/planting_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="planting_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/planting_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=5,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="4 - Harvest Date",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/harvest_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="harvest_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=5,
                    ),
                ],
            ),
        ]
