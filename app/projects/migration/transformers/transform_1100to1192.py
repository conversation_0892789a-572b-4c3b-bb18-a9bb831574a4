from __future__ import annotations

from annotations import Any
from projects.migration.project_transformer import ProjectTransformer
from projects.migration.schema import (
    Field,
    ProjectMigrationCommand,
    Rule,
    RuleFunctionData,
    RuleSet,
)
from projects.migration.transformers import common_transforms


class Transform1100to1192(ProjectTransformer):
    """
    Transform data from program 1100 to program 1192
    """

    async def transform(self) -> ProjectMigrationCommand | None:
        if self.transform_request is None:
            return None

        self.transform_request.rulesets = self.translation_rules()
        result = await self.gather_data(transform_request=self.transform_request)
        return await self.create_data(result)

    def get_cover_crop_mix(self, src: RuleFunctionData) -> str:
        return common_transforms.get_cover_crop_mix(src)

    def get_assigned_practices(self, src: RuleFunctionData) -> dict[int, str] | None:
        return common_transforms.get_assigned_practices(src)

    async def crop_type(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type(src)

    async def crop_type_basic(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type_basic(src)

    async def practice_to_croptype(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_croptype(src)

    async def practice_to_usage(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_usage(src)

    async def tillage_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_practice(src)

    async def tillage_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_event(src)

    async def tillage_period_to_tillage_date(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_period_to_tillage_date(src)

    async def tillage_depth_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_depth_to_tillage_depth(src)

    async def tillage_date_period_to_tillage_date(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_date_period_to_tillage_date(src)

    async def tillage_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_depth(src)

    async def assigned_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_practice(src)

    async def assigned_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_depth(src)

    async def rule_value(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.rule_value(src)

    async def field_allowed(self, field: Field | None = None, target_project_id: int | None = None) -> bool:
        return await super().field_allowed(field, target_project_id)

    def translation_rules(self) -> list[RuleSet]:
        """
        Returns a list of rules to translate values from one program to another.
        """

        source_program_id = 1100
        target_program_id = 1192

        return [
            # 1 Enrolment - Historical Crop Rotation
            RuleSet(
                reference="1.1 - crop_type 2020-22",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_type",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        function_name="crop_type",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.2 - crop_usage 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/crop_usage",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_usage",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.3 - Planting Date 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/planting_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="planting_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.4 - Harvest Date 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/harvest_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="harvest_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.5 - winter_crop_termination 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/winter_crop_termination",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_termination",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/winter_crop_termination",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="winter_crop_termination",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.6 - crop_yield 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/crop_yield",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_yield",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_yield",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_yield",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.7 - yield_rate_unit 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/yield_rate_unit",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="yield_rate_unit",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/yield_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="yield_rate_unit",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="1.8 - residue_harvested 2020-22",
                source=Rule(
                    action="read from enrolment/historical_crop_rotation/residue_harvested",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="residue_harvested",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/residue_harvested",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="residue_harvested",
                        row_id_style="source",
                    ),
                ],
            ),
            # 2 Measurement - Historical Crop Rotation
            RuleSet(
                reference="2.1 - crop_type 2023",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/crop_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_type",
                    # Not sure if should create 2 rules for each year or just one is enough?
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        # value_blacklist=["fallow", "no cover"],
                        function_name="crop_type",
                        row_id_style="computed",
                        # row_id_offset=82, # what is this? What to set it to?
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.2 - crop_usage 2021-2022",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/crop_usage",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_usage",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        row_id_style="computed",
                        # row_id_offset=8, # What value?
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.3 - Planting Date 2021-2022",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/planting_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="planting_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        row_id_style="computed",
                        # row_id_offset=8,# what value?
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.4 - Harvest Date 2021-2022",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/harvest_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="harvest_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        row_id_style="computed",
                        # row_id_offset=8,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.5 - winter_crop_termination",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/winter_crop_termination",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_termination",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/winter_crop_termination",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="winter_crop_termination",
                        row_id_style="computed",
                        # row_id_offset=8,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.6 - crop_yield",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/crop_yield",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_yield",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_yield",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_yield",
                        row_id_style="computed",
                        # row_id_offset=8,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.7 - yield_rate_unit",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/yield_rate_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="yield_rate_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/yield_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="yield_rate_unit",
                        row_id_style="computed",
                        # row_id_offset=8,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.8 - residue_harvested",
                source=Rule(
                    action="read from MONITORING/historical_crop_rotation/residue_harvested",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="residue_harvested",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/residue_harvested",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="residue_harvested",
                        row_id_style="computed",
                        # row_id_offset=8,
                        record_years=[2023],
                    ),
                ],
            ),
            # 3 Enrolment - Historical tillage
            RuleSet(
                reference="3.1 - tillage_event 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_tillage/tillage_event",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_TILLAGE",
                    attribute_type="tillage_event",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="3.2 - tillage_date 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_tillage/tillage_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_TILLAGE",
                    attribute_type="tillage_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="3.3 - tillage_depth 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_tillage/tillage_depth",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_TILLAGE",
                    attribute_type="tillage_depth",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="3.4 - soil_inversion 2020-2022",
                source=Rule(
                    action="read from enrolment/historical_tillage/soil_inversion",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_TILLAGE",
                    attribute_type="soil_inversion",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/soil_inversion",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="soil_inversion",
                        row_id_style="source",
                    ),
                ],
            ),
            # # 4 Measurement - Historical tillage
            RuleSet(
                reference="4.1 - tillage_event 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_event",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_event",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="4.2 - tillage_date 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="4.3 - tillage_depth 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_depth",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_depth",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="4.4 - soil_inversion 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/soil_inversion",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="soil_inversion",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/soil_inversion",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="soil_inversion",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            # # 5 Enrolment - Nutrient_mgmt
            RuleSet(
                reference="5.1 - nutrient_management_enabled 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/nutrient_management_enabled",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="nutrient_management_enabled",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/nutrient_management_enabled",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="nutrient_management_enabled",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.2 - application_product 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_product",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_product",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_product",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_product",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.3 - application_date 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.4 - application_rate 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_rate",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_product",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.5 - application_rate_unit 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_rate_unit",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate_unit",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate_unit",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.6 - application_rate_type 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_rate_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate_type",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_rate_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate_type",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.7 - application_method 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_method",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_method",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_method",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_method",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.8 - application_depth 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/application_depth",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_depth",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_depth",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.9 - additives 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/additives",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="additives",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/additives",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="additives",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.10 - water_amount 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/water_amount",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="water_amount",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/water_amount",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="water_amount",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="5.11 - water_amount_unit 2020-2022",
                source=Rule(
                    action="read from enrolment/nutrient_mgmt/water_amount_unit",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="water_amount_unit",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/water_amount_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="water_amount_unit",
                        row_id_style="source",
                    ),
                ],
            ),
            # # 6 Measurement - Nutrient_mgmt
            RuleSet(
                reference="6.1 - nutrient_management_enabled 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/nutrient_management_enabled",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="nutrient_management_enabled",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/nutrient_management_enabled",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="nutrient_management_enabled",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.2 - application_product 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_product",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_product",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_product",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_product",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.3 - application_date 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_date",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.4 - application_rate 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_rate",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_product",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.5 - application_rate_unit 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_rate_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate_unit",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.6 - application_rate_type 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_rate_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_rate_type",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_rate_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_rate_type",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.7 - application_method 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_method",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_method",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_method",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_method",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.8 - application_depth 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/application_depth",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="application_depth",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/application_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="application_depth",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.9 - additives 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/additives",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="additives",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/additives",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="additives",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.10 - water_amount 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/water_amount",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="water_amount",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/water_amount",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="water_amount",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="6.11 - water_amount_unit 2023",
                source=Rule(
                    action="read from MONITORING/nutrient_mgmt/water_amount_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="NUTRIENT_MGMT",
                    attribute_type="water_amount_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/nutrient_mgmt/water_amount_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="NUTRIENT_MGMT",
                        attribute_type="water_amount_unit",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            # # 7 Enrolment - Irrigation
            RuleSet(
                reference="7.1 - irrigation_enabled 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/irrigation_enabled",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="irrigation_enabled",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/irrigation_enabled",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="irrigation_enabled",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.2 - start_date 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/start_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="start_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/start_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="start_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.3 - end_date 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/end_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="end_date",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/end_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="end_date",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.4 - irrigation_method 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/irrigation_method",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="irrigation_method",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/irrigation_method",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="irrigation_method",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.5 - subsurface_drip_depth 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/subsurface_drip_depth",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="subsurface_drip_depth",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/subsurface_drip_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="subsurface_drip_depth",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.6 - subsurface_drip_depth_unit 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/subsurface_drip_depth_unit",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="subsurface_drip_depth_unit",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/subsurface_drip_depth_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="subsurface_drip_depth_unit",
                        row_id_style="source",
                    ),
                ],
            ),
            RuleSet(
                reference="7.7 - flood_pct 2020-2022",
                source=Rule(
                    action="read from enrolment/irrigation/flood_pct",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="IRRIGATION",
                    attribute_type="flood_pct",
                    record_years=[2020, 2021, 2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/flood_pct",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="flood_pct",
                        row_id_style="source",
                    ),
                ],
            ),
            # 8 Measurement - Irrigation
            RuleSet(
                reference="8.1 - irrigation_enabled 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/irrigation_enabled",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="irrigation_enabled",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/irrigation_enabled",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="irrigation_enabled",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.2 - start_date 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/start_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="start_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/start_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="start_date",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.3 - end_date 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/end_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="end_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/end_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="end_date",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.4 - irrigation_method 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/irrigation_method",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="irrigation_method",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/irrigation/irrigation_method",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="irrigation_method",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.5 - subsurface_drip_depth 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/subsurface_drip_depth",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="subsurface_drip_depth",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/subsurface_drip_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="subsurface_drip_depth",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.6 - subsurface_drip_depth_unit 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/subsurface_drip_depth_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="subsurface_drip_depth_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/subsurface_drip_depth_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="subsurface_drip_depth_unit",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="8.7 - flood_pct 2023",
                source=Rule(
                    action="read from MONITORING/irrigation/flood_pct",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="IRRIGATION",
                    attribute_type="flood_pct",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/irrigation/flood_pct",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="IRRIGATION",
                        attribute_type="flood_pct",
                        row_id_style="computed",
                        record_years=[2023],
                    ),
                ],
            ),
        ]
