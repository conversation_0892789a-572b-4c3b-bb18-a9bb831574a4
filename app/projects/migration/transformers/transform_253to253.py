from __future__ import annotations

from annotations import Any
from projects.migration.project_transformer import ProjectTransformer
from projects.migration.schema import (
    ProjectMigrationCommand,
    Rule,
    RuleFunctionData,
    RuleSet,
)
from projects.migration.transformers import common_transforms


class Transform253to253(ProjectTransformer):
    """
    Transform data from program 253 to program 253
    """

    async def transform(self) -> ProjectMigrationCommand | None:
        if self.transform_request is None:
            return None

        self.transform_request.rulesets = self.translation_rules()
        result = await self.gather_data(transform_request=self.transform_request)
        return await self.create_data(result)

    def get_cover_crop_mix(self, src: RuleFunctionData) -> str:
        return common_transforms.get_cover_crop_mix(src)

    def get_assigned_practices(self, src: RuleFunctionData) -> dict[int, str] | None:
        return common_transforms.get_assigned_practices(src)

    async def crop_type(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type(src)

    async def practice_to_croptype(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_croptype(src)

    async def practice_to_usage(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_usage(src)

    async def tillage_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_practice(src)

    async def tillage_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_practice(src)

    async def assigned_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_depth(src)

    async def assigned_practice_to_soil_inversion(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_soil_inversion(src)

    def translation_rules(self) -> list[RuleSet]:
        """
        Returns a list of rules to translate values from one program to another.
        """

        source_program_id = 253
        target_program_id = 253

        return [
            RuleSet(
                reference="4 - Intended Crop Type",
                source=Rule(
                    action="read from enrolment/intended_commodity_crops/crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="INTENDED_COMMODITY_CROPS",
                    attribute_type="crop_type",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/crop_type",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        record_years=[2024],
                        row_id_style="increment",
                        row_id_offset=-1,
                        lock_value=True,
                    ),
                    Rule(
                        action="write into measurement/historical_crop_rotation/crop_usage",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        record_years=[2024],
                        row_id_style="increment",
                        row_id_offset=-1,
                        value="Commodity",
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="4 - Intended Planting Date",
                source=Rule(
                    action="read from enrolment/intended_commodity_crops/planting_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="INTENDED_COMMODITY_CROPS",
                    attribute_type="planting_date",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/planting_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        record_years=[2024],
                        row_id_style="increment",
                        row_id_offset=-1,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="4 - Intended Harvest Date",
                source=Rule(
                    action="read from enrolment/intended_commodity_crops/harvest_date",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="INTENDED_COMMODITY_CROPS",
                    attribute_type="harvest_date",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into measurement/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        record_years=[2024],
                        row_id_style="increment",
                        row_id_offset=-1,
                    ),
                ],
            ),
            RuleSet(
                reference="5 - Cover Crop Commitment from Assign Practices to Historical Crop Rotation as 2nd row",
                source=Rule(
                    action="read from enrolment/assign_practices/Practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="ASSIGN_PRACTICES",
                    attribute_type="Practice",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_type as 2nd row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        function_name="practice_to_croptype",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=2,
                        lock_value=True,
                    ),
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_usage as 2nd row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        function_name="practice_to_usage",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=2,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference=(
                    "Tillage Practice from Assign Practices to Tillage Event, Tillage Practice, "
                    "Tillage Depth and Soil Inversion"
                ),
                source=Rule(
                    action="read from ENROLMENT/ASSIGN_PRACTICES/Practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="ASSIGN_PRACTICES",
                    attribute_type="Practice",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/TILLAGE/tillage_event first row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="TILLAGE",
                        attribute_type="tillage_event",
                        function_name="assigned_practice_to_tillage_event",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=0,
                    ),
                    Rule(
                        action="write into MONITORING/TILLAGE/tillage_practice first row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="assigned_practice_to_tillage_practice",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=0,
                        lock_value=True,
                    ),
                    Rule(
                        action="write into MONITORING/TILLAGE/tillage_depth first row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="assigned_practice_to_tillage_depth",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=0,
                    ),
                    Rule(
                        action="write into MONITORING/TILLAGE/soil_inversion first row",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="TILLAGE",
                        attribute_type="soil_inversion",
                        function_name="assigned_practice_to_soil_inversion",
                        record_years=[2024],
                        row_id_style="fixed",
                        row_id=0,
                    ),
                ],
            ),
        ]
