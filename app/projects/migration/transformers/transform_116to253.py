from __future__ import annotations

from annotations import Any
from projects.migration.project_transformer import ProjectTransformer
from projects.migration.schema import (
    Field,
    ProjectMigrationCommand,
    Rule,
    RuleFunctionData,
    RuleSet,
)
from projects.migration.transformers import common_transforms


class Transform116to253(ProjectTransformer):
    """
    Transform data from program 116 to program 253
    """

    async def transform(self) -> ProjectMigrationCommand | None:
        if self.transform_request is None:
            return None

        self.transform_request.rulesets = self.translation_rules()
        result = await self.gather_data(transform_request=self.transform_request)
        return await self.create_data(result)

    def get_cover_crop_mix(self, src: RuleFunctionData) -> str:
        return common_transforms.get_cover_crop_mix(src)

    def get_assigned_practices(self, src: RuleFunctionData) -> dict[int, str] | None:
        return common_transforms.get_assigned_practices(src)

    async def crop_type(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type(src)

    async def crop_type_basic(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type_basic(src)

    async def practice_to_croptype(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_croptype(src)

    async def practice_to_usage(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_usage(src)

    async def tillage_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_practice(src)

    async def tillage_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_event(src)

    async def tillage_period_to_tillage_date(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_period_to_tillage_date(src)

    async def tillage_depth_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_depth_to_tillage_depth(src)

    async def tillage_date_period_to_tillage_date(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_date_period_to_tillage_date(src)

    async def tillage_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_depth(src)

    async def assigned_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_practice(src)

    async def assigned_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_depth(src)

    async def rule_value(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.rule_value(src)

    async def field_allowed(self, field: Field | None = None, target_project_id: int | None = None) -> bool:
        """
        Field is allowed if it's also in the detination project
        """
        if field is None or target_project_id is None:
            return False
        # This can be called both for source field and destination field, so if it's the destination field, we just
        # return True, because by definition the destination field is in the destination project
        if field.parent_project_id == target_project_id:
            return True
        existing_field = await self.get_existing_destination_field(
            target_project_id=target_project_id, source_field=field
        )
        return existing_field is not None

    def translation_rules(self) -> list[RuleSet]:
        """
        Returns a list of rules to translate values from one program to another.
        """

        source_program_id = 116
        target_program_id = 253

        return [
            RuleSet(
                reference="Crop Type",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/crop_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_type",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/crop_type",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="Cover crop mix",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/cover_crop_mix",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="cover_crop_mix",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="Planting date",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/planting_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="planting_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                        lock_value=True,
                    ),
                ],
            ),
            RuleSet(
                reference="Harvest date",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/harvest_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="harvest_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                    ),
                ],
            ),
            RuleSet(
                reference="Crop yield",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/crop_yield",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="crop_yield",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/crop_yield",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_yield",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                    ),
                ],
            ),
            RuleSet(
                reference="Yield rate unit",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/yield_rate_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="yield_rate_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/yield_rate_unit",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="yield_rate_unit",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                    ),
                ],
            ),
            RuleSet(
                reference="Winter Crop Termination",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/winter_crop_termination",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_termination",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/winter_crop_termination",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="winter_crop_termination",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                    ),
                ],
            ),
            RuleSet(
                reference="Residue Harvested",
                source=Rule(
                    action="read from MONITORING/HISTORICAL_CROP_ROTATION/residue_harvested",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="residue_harvested",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into MONITORING/HISTORICAL_CROP_ROTATION/residue_harvested",
                        program_id=target_program_id,
                        phase_type="MONITORING",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="residue_harvested",
                        record_years=[2023],
                        row_id_style="increment",
                        row_id_offset=3,
                    ),
                ],
            ),
        ]
