from unittest.mock import AsyncMock, MagicMock, patch

from phases.enums import EligibilityTypes
from programs.enums import PracticeChange
from projects.eligibility.enums import (
    CoverCropType,
    IrrigationIntensity,
    TillageIntensity,
)
from projects.methods import get_historic_practices_completion
from projects.schema import FieldEligiblity


async def test_is_eligible_no_eligibility_map():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = []
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = None
        mock_eligibility_map.return_value = None
        mock_assigned.return_value = {}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_field_not_in_eligibility_map():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.PARAMETERISED_ELIGIBILITY
        mock_eligibility_map.return_value = {}
        mock_assigned.return_value = {}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_parameterised_with_validator_success():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    field_eligibility = FieldEligiblity(
        eligible_practices=[[PracticeChange.no_till, PracticeChange.cover_crops]],
        eligible=None,
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.CONVENTIONAL_TILL,
        baseline_irrigation=IrrigationIntensity.TCF,
    )

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.PARAMETERISED_ELIGIBILITY
        mock_eligibility_map.return_value = {1: field_eligibility}
        mock_assigned.return_value = {1: [PracticeChange.no_till, PracticeChange.cover_crops]}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_parameterised_with_validator_failure():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    field_eligibility = FieldEligiblity(
        eligible_practices=[[PracticeChange.no_till, PracticeChange.cover_crops]],
        eligible=None,
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.CONVENTIONAL_TILL,
    )

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.PARAMETERISED_ELIGIBILITY
        mock_eligibility_map.return_value = {1: field_eligibility}
        mock_assigned.return_value = {1: [PracticeChange.no_till]}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_parameterised_practice_not_in_eligible_set():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    field_eligibility = FieldEligiblity(
        eligible_practices=[[PracticeChange.no_till, PracticeChange.cover_crops]],
        eligible=None,
    )

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.PARAMETERISED_ELIGIBILITY
        mock_eligibility_map.return_value = {1: field_eligibility}
        mock_assigned.return_value = {1: [PracticeChange.conventional_till]}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_non_parameterised_combination_match():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    field_eligibility = FieldEligiblity(
        eligible_practices=[
            [PracticeChange.no_till],
            [PracticeChange.cover_crops, PracticeChange.reduced_till],
        ],
        eligible=None,
    )

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.CARGILL_GRAIN_2024
        mock_eligibility_map.return_value = {1: field_eligibility}
        mock_assigned.return_value = {1: [PracticeChange.no_till]}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1


async def test_is_eligible_non_parameterised_combination_no_match():
    mock_request = MagicMock()
    mock_session = AsyncMock()
    mock_request.state.sql_session.return_value.__aenter__.return_value = mock_session
    mock_request.state.sql_session.return_value.__aexit__.return_value = None

    field_eligibility = FieldEligiblity(
        eligible_practices=[
            [PracticeChange.no_till],
            [PracticeChange.cover_crops, PracticeChange.reduced_till],
        ],
        eligible=None,
    )

    with (
        patch("projects.methods.get.generic_get") as mock_get,
        patch("projects.methods.run_query") as mock_run_query,
        patch("projects.methods.db.get_eligibility_method") as mock_get_eligibility,
        patch("projects.methods.get_field_id_eligibility_map") as mock_eligibility_map,
        patch("projects.methods.db.get_assigned_practice_values") as mock_assigned,
        patch("projects.methods.Tasks") as mock_tasks,
    ):
        mock_tasks_instance = AsyncMock()
        mock_tasks.return_value = mock_tasks_instance
        mock_tasks_instance.add.return_value = None
        mock_tasks_instance.complete_all.return_value = [MagicMock(id=1)]

        mock_get.return_value = [MagicMock(id=1)]
        mock_result = MagicMock()
        mock_result.all.return_value = [(1, 1)]
        mock_run_query.return_value = mock_result
        mock_get_eligibility.return_value = EligibilityTypes.CARGILL_GRAIN_2024
        mock_eligibility_map.return_value = {1: field_eligibility}
        mock_assigned.return_value = {1: [PracticeChange.conventional_till]}

        result = await get_historic_practices_completion(
            request=mock_request,
            stage_id=1,
            phase_id=1,
            project_id=1,
            stage_type="confirm_history",
            stage_year_start=2020,
            stage_year_end=2023,
        )

        assert result.completed == 1
