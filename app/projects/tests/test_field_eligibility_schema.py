from unittest.mock import patch

import pytest

from programs.enums import PracticeChange
from projects.eligibility.enums import (
    CoverCropType,
    IrrigationIntensity,
    TillageIntensity,
)
from projects.schema import FieldEligiblity


def test_get_baseline_practices_all_valid_practices():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.NO_TILL,
        baseline_irrigation=IrrigationIntensity.DRIP,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = PracticeChange.cover_crops
        mock_tillage.return_value = PracticeChange.no_till
        mock_irrigation.return_value = PracticeChange.drip_irrigation

        result = field_eligibility.baseline_practices

        assert result == {
            PracticeChange.no_till,
            PracticeChange.cover_crops,
            PracticeChange.drip_irrigation,
        }
        mock_cover_crop.assert_called_once_with(CoverCropType.FULL_COVER_CROP)
        mock_tillage.assert_called_once_with(TillageIntensity.NO_TILL)
        mock_irrigation.assert_called_once_with(IrrigationIntensity.DRIP)


def test_get_baseline_practices_all_none_baselines():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=None,
        baseline_tillage=None,
        baseline_irrigation=None,
    )

    result = field_eligibility.baseline_practices
    assert result == frozenset()


def test_get_baseline_practices_partial_baselines():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.BASIC_COVER_CROP,
        baseline_tillage=None,
        baseline_irrigation=IrrigationIntensity.AWD,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = PracticeChange.basic_cover_crops
        mock_irrigation.return_value = PracticeChange.alternating_wet_dry

        result = field_eligibility.baseline_practices

        assert result == {
            PracticeChange.basic_cover_crops,
            PracticeChange.alternating_wet_dry,
        }
        mock_cover_crop.assert_called_once_with(CoverCropType.BASIC_COVER_CROP)
        mock_irrigation.assert_called_once_with(IrrigationIntensity.AWD)


def test_get_baseline_practices_helper_returns_none():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.UNKNOWN,
        baseline_tillage=TillageIntensity.UNKNOWN,
        baseline_irrigation=IrrigationIntensity.UNKNOWN,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = None
        mock_tillage.return_value = None
        mock_irrigation.return_value = None

        result = field_eligibility.baseline_practices

        assert result == frozenset()
        mock_cover_crop.assert_called_once_with(CoverCropType.UNKNOWN)
        mock_tillage.assert_called_once_with(TillageIntensity.UNKNOWN)
        mock_irrigation.assert_called_once_with(IrrigationIntensity.UNKNOWN)


def test_get_baseline_practices_mixed_none_and_valid_returns():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.PREMIUM_COVER_CROP,
        baseline_tillage=TillageIntensity.UNKNOWN,
        baseline_irrigation=IrrigationIntensity.FURROW,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = PracticeChange.premium_cover_crops
        mock_tillage.return_value = None
        mock_irrigation.return_value = PracticeChange.furrow_irrigation

        result = field_eligibility.baseline_practices

        assert result == {
            PracticeChange.premium_cover_crops,
            PracticeChange.furrow_irrigation,
        }
        mock_cover_crop.assert_called_once_with(CoverCropType.PREMIUM_COVER_CROP)
        mock_tillage.assert_called_once_with(TillageIntensity.UNKNOWN)
        mock_irrigation.assert_called_once_with(IrrigationIntensity.FURROW)


@pytest.mark.parametrize(
    "cover_crop_type,expected_practice",
    [
        (CoverCropType.NO_COVER_CROP, PracticeChange.no_cover_crop),
        (CoverCropType.FULL_COVER_CROP, PracticeChange.cover_crops),
        (CoverCropType.BASIC_COVER_CROP, PracticeChange.basic_cover_crops),
        (CoverCropType.PREMIUM_COVER_CROP, PracticeChange.premium_cover_crops),
    ],
)
def test_get_baseline_practices_cover_crop_types(cover_crop_type, expected_practice):
    field_eligibility = FieldEligiblity(baseline_cover_crop=cover_crop_type)

    with patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop:
        mock_cover_crop.return_value = expected_practice

        result = field_eligibility.baseline_practices

        assert result == {expected_practice}
        mock_cover_crop.assert_called_once_with(cover_crop_type)


@pytest.mark.parametrize(
    "tillage_intensity,expected_practice",
    [
        (TillageIntensity.NO_TILL, PracticeChange.no_till),
        (TillageIntensity.REDUCED_TILL, PracticeChange.reduced_till),
        (TillageIntensity.CONVENTIONAL_TILL, PracticeChange.conventional_till),
    ],
)
def test_get_baseline_practices_tillage_intensities(tillage_intensity, expected_practice):
    field_eligibility = FieldEligiblity(baseline_tillage=tillage_intensity)

    with patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage:
        mock_tillage.return_value = expected_practice

        result = field_eligibility.baseline_practices

        assert result == {expected_practice}
        mock_tillage.assert_called_once_with(tillage_intensity)


@pytest.mark.parametrize(
    "irrigation_intensity,expected_practice",
    [
        (IrrigationIntensity.TCF, PracticeChange.tcf_irrigation),
        (IrrigationIntensity.AWD, PracticeChange.alternating_wet_dry),
        (IrrigationIntensity.FURROW, PracticeChange.furrow_irrigation),
        (IrrigationIntensity.DRIP, PracticeChange.drip_irrigation),
    ],
)
def test_get_baseline_practices_irrigation_intensities(irrigation_intensity, expected_practice):
    field_eligibility = FieldEligiblity(baseline_irrigation=irrigation_intensity)

    with patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation:
        mock_irrigation.return_value = expected_practice

        result = field_eligibility.baseline_practices

        assert result == {expected_practice}
        mock_irrigation.assert_called_once_with(irrigation_intensity)


def test_get_baseline_practices_frozenset_properties():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.REDUCED_TILL,
        baseline_irrigation=IrrigationIntensity.TCF,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = PracticeChange.cover_crops
        mock_tillage.return_value = PracticeChange.reduced_till
        mock_irrigation.return_value = PracticeChange.tcf_irrigation

        result = field_eligibility.baseline_practices

        assert isinstance(result, frozenset)
        assert result == {
            PracticeChange.reduced_till,
            PracticeChange.cover_crops,
            PracticeChange.tcf_irrigation,
        }


def test_get_baseline_practices_no_duplicate_imports():
    field_eligibility = FieldEligiblity(baseline_cover_crop=CoverCropType.FULL_COVER_CROP)

    try:
        result = field_eligibility.baseline_practices
        assert isinstance(result, frozenset)
    except ImportError as e:
        pytest.fail(f"Import error in get_baseline_practices: {e}")


def test_get_baseline_practices_empty_field_eligibility():
    field_eligibility = FieldEligiblity()

    result = field_eligibility.baseline_practices
    assert result == frozenset()


def test_get_baseline_practices_with_other_fields_set():
    field_eligibility = FieldEligiblity(
        eligible_practices=[[PracticeChange.cover_crops]],
        eligible=True,
        title="Test Field",
        message="Test message",
        crop_types=["corn", "soy"],
        hard_ineligible=True,
        baseline_cover_crop=CoverCropType.BASIC_COVER_CROP,
    )

    with patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop:
        mock_cover_crop.return_value = PracticeChange.basic_cover_crops

        result = field_eligibility.baseline_practices

        assert result == {PracticeChange.basic_cover_crops}
        mock_cover_crop.assert_called_once_with(CoverCropType.BASIC_COVER_CROP)


def test_get_baseline_practices_real_helper_functions_all_valid():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.NO_TILL,
        baseline_irrigation=IrrigationIntensity.DRIP,
    )

    result = field_eligibility.baseline_practices

    assert len(result) == 3
    assert all(isinstance(practice, PracticeChange) for practice in result)
    assert PracticeChange.cover_crops in result
    assert PracticeChange.no_till in result
    assert PracticeChange.drip_irrigation in result


def test_get_baseline_practices_real_helper_functions_unknown_values():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.UNKNOWN,
        baseline_tillage=TillageIntensity.UNKNOWN,
        baseline_irrigation=IrrigationIntensity.UNKNOWN,
    )

    result = field_eligibility.baseline_practices

    assert result == frozenset()


def test_get_baseline_practices_real_helper_functions_mixed_valid_unknown():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.PREMIUM_COVER_CROP,
        baseline_tillage=TillageIntensity.UNKNOWN,
        baseline_irrigation=IrrigationIntensity.FURROW,
    )

    result = field_eligibility.baseline_practices

    assert len(result) == 2
    assert PracticeChange.premium_cover_crops in result
    assert PracticeChange.furrow_irrigation in result
    assert all(practice is not None for practice in result)


@pytest.mark.parametrize(
    "baseline_values,expected_count",
    [
        ({"baseline_cover_crop": CoverCropType.BASIC_COVER_CROP}, 1),
        ({"baseline_tillage": TillageIntensity.REDUCED_TILL}, 1),
        ({"baseline_irrigation": IrrigationIntensity.AWD}, 1),
        (
            {
                "baseline_cover_crop": CoverCropType.NO_COVER_CROP,
                "baseline_tillage": TillageIntensity.CONVENTIONAL_TILL,
            },
            2,
        ),
        (
            {
                "baseline_tillage": TillageIntensity.NO_TILL,
                "baseline_irrigation": IrrigationIntensity.TCF,
            },
            2,
        ),
        (
            {
                "baseline_cover_crop": CoverCropType.FULL_COVER_CROP,
                "baseline_irrigation": IrrigationIntensity.DRIP,
            },
            2,
        ),
        ({}, 0),
    ],
)
def test_get_baseline_practices_real_helper_functions_combinations(baseline_values, expected_count):
    field_eligibility = FieldEligiblity(**baseline_values)

    result = field_eligibility.baseline_practices

    assert len(result) == expected_count
    assert all(isinstance(practice, PracticeChange) for practice in result)


def test_get_baseline_practices_zero_enum_values():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.NO_COVER_CROP,
        baseline_tillage=TillageIntensity.NO_TILL,
        baseline_irrigation=IrrigationIntensity.TCF,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = PracticeChange.no_cover_crop
        mock_tillage.return_value = PracticeChange.no_till
        mock_irrigation.return_value = PracticeChange.tcf_irrigation

        result = field_eligibility.baseline_practices

        assert len(result) == 3
        assert all(practice is not None for practice in result)


def test_get_baseline_practices_negative_enum_values():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.UNKNOWN,
        baseline_tillage=TillageIntensity.UNKNOWN,
        baseline_irrigation=IrrigationIntensity.UNKNOWN,
    )

    with (
        patch("projects.eligibility.helper.get_cover_crop_practice_from_cover_crop_type") as mock_cover_crop,
        patch("projects.eligibility.helper.get_tillage_practice_from_tillage_intensity") as mock_tillage,
        patch("projects.eligibility.helper.get_irrigation_practice_from_irrigation_intensity") as mock_irrigation,
    ):
        mock_cover_crop.return_value = None
        mock_tillage.return_value = None
        mock_irrigation.return_value = None

        result = field_eligibility.baseline_practices

        assert result == frozenset()
        mock_cover_crop.assert_called_once_with(CoverCropType.UNKNOWN)
        mock_tillage.assert_called_once_with(TillageIntensity.UNKNOWN)
        mock_irrigation.assert_called_once_with(IrrigationIntensity.UNKNOWN)


def test_baseline_practices_property_behavior():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.FULL_COVER_CROP,
        baseline_tillage=TillageIntensity.NO_TILL,
    )

    # Test that baseline_practices is a property, not a method
    assert hasattr(field_eligibility, "baseline_practices")
    assert not callable(getattr(field_eligibility, "baseline_practices"))  # noqa

    # Test that accessing the property multiple times returns consistent results
    result1 = field_eligibility.baseline_practices
    result2 = field_eligibility.baseline_practices
    assert result1 == result2
    assert isinstance(result1, frozenset)
    assert isinstance(result2, frozenset)


def test_baseline_practices_property_serialization():
    field_eligibility = FieldEligiblity(
        baseline_cover_crop=CoverCropType.BASIC_COVER_CROP,
        baseline_tillage=TillageIntensity.REDUCED_TILL,
    )

    # Test that the property is included in dict() serialization
    result_dict = field_eligibility.dict()
    assert "baseline_practices" in result_dict
    assert isinstance(result_dict["baseline_practices"], list)
    assert len(result_dict["baseline_practices"]) >= 0


def test_baseline_practices_property_with_dynamic_changes():
    field_eligibility = FieldEligiblity()

    # Initially empty
    assert field_eligibility.baseline_practices == frozenset()

    # Modify baseline values and check property updates
    field_eligibility.baseline_cover_crop = CoverCropType.FULL_COVER_CROP
    result_with_cover_crop = field_eligibility.baseline_practices
    assert len(result_with_cover_crop) >= 0

    # Add tillage and check again
    field_eligibility.baseline_tillage = TillageIntensity.NO_TILL
    result_with_both = field_eligibility.baseline_practices
    assert len(result_with_both) >= len(result_with_cover_crop)
