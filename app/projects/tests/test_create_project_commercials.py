from __future__ import annotations

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, TYPE_CHECKING
from unittest.mock import ANY, mock_open, patch

import pytest
from fastapi import status

from config import get_settings
from docusign.client import DSClient
from docusign.enums import EnvelopeStatus
from docusign.schema import FieldD<PERSON>
from helper import (
    external_api_mocked_responses,
    i18n,
)
from helper.external_api_mocked_responses import mocked_core_fields_get_api
from phases.enums import (
    AttributeTypes,
    CommercialsPaymentForType,
    ContractType,
    EligibilityTypes,
    PhaseTypes,
    StageTypes,
)
from programs import paths as programs_path
from programs.enums import PayoutStructure, PracticeChange, UnitsTypes
from programs.model import Programs
from projects import paths
from projects.commercials.schema import PayPerPracticeCommercialRule
from projects.eligibility.enums import CARGILL_EU_FR_PROGRAM_IDS
from projects.model import (
    ProjectContractLineItems,
    ProjectContracts,
)
from projects.schema import ProjectGeom
from scenarios_service.enums import DndcStatusChoices
from scenarios_service.schema import DndcResultsOutputPrice

if TYPE_CHECKING:
    from fastapi import Request

settings = get_settings()


@pytest.mark.parametrize("eligibility_method", [None, EligibilityTypes.CARGILL_EU_2022_FR])
@patch.object(
    i18n,
    "get_translations_from_bucket",
    external_api_mocked_responses.mocked_get_translations_from_bucket,
)
async def test_create_contract(
    eligibility_method,
    monkeypatch,
    async_client,
    cargill_contract_template,
    mdl,
    mock_external_api,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_net_ghg_reduced, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(
        program_id=program.id,
        start_date=(datetime.now() - timedelta(days=30)),
        end_date=(datetime.now() + timedelta(days=4)),
        show_contract=False,
        contract_type=ContractType.DOCUSIGN,
        params={
            "limit_program_ha": 1000,
            "limit_project_ha": 300,
            "outcome_estimation": False,
        },
        type_=PhaseTypes.ENROLMENT,
    )
    stage = await mdl.Stage(
        phase_id=phase.id,
        fmi_import_start_date=(datetime.now() + timedelta(days=6)),
        fmi_import_end_date=(datetime.now() + timedelta(days=10)),
        description="string",
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=eligibility_method,
    )
    practice_attribute, planting_attribute, unused_attribute = [
        await mdl.Attribute(parent_stage_id=stage.id, type=type_)
        for type_ in [
            AttributeTypes.practice,
            AttributeTypes.application_rate_unit,
            AttributeTypes.spring_tillage_practice,
        ]
    ]
    for attribute_id in [practice_attribute.id, planting_attribute.id]:
        await mdl.ContractAttributes(attribute_id=attribute_id, phase_id=phase.id, program_id=program.id)

    field_ids = []
    for practice_values, planting_values, unused_values, fs_field_id in [
        [["rush", "decrease"], ["history"], ["unused_01", "unused_02"], 1050],
        [["weave"], ["they", "alone"], ["unused_11", "unused_12"], 1067],
    ]:
        field = await mdl.Fields(parent_project_id=project.id, fs_field_id=fs_field_id)
        field_ids.append(field.id)

        for i, practice_value in enumerate(practice_values):
            await mdl.Values(field_id=field.id, value=practice_value, attribute_id=practice_attribute.id, row_id=i)
        for i, planting_value in enumerate(planting_values):
            await mdl.Values(field_id=field.id, value=planting_value, attribute_id=planting_attribute.id, row_id=i)
        # next values shouldn't appear in line items note because no contract attributes
        for i, unused_value in enumerate(unused_values):
            await mdl.Values(field_id=field.id, value=unused_value, attribute_id=unused_attribute.id, row_id=i)

    await mdl.ContractTemplate(template_blob=cargill_contract_template, phase_id=phase.id)
    dndc_task = await mdl.DndcTasks(
        id="task_id", project_id=project.id, program_id=project.program_id, status="finished"
    )

    dndc_results = DndcResultsOutputPrice(
        field_count=len(field_ids),
        fields_with_results={
            str(field_ids[0]): {"ghg": -1.1},
            str(field_ids[1]): {"ghg": 2.12},
        },
        fields_without_results=[],
        fields_with_errors={},
        soc=0.0,
        n2o=0.0,
        ch4=0.0,
        ghg=-42.2,
        indirect_n2o=0.0,
        total_n2o=0.0,
        ghg_soc=0.0,
        ghg_ch4=0.0,
        ghg_n2o=0.0,
        ghg_indirect_n2o=0.0,
        ghg_total_n2o=0.0,
        carbon_price_dollars=2,
        payment_dollars=0.0,
    )

    await mdl.PhaseCommercials(
        payment=10.99,
        payment_for=CommercialsPaymentForType.ESTIMATED_GHG,
        phase_id=phase.id,
    )

    contract = await mdl.ProjectContracts(project=project.id, phase_id=phase.id)
    # additional line items to test deletion
    for field_id in field_ids:
        await mdl.ProjectContractLineItems(
            contract_id=contract.id, field_id=field_id, value=11.1, quantity=22.2, note={}
        )

    monkeypatch.setattr("fields.methods.core_fields_get_api", mocked_core_fields_get_api)

    async def mocked_get_eu_project_carbon_results(
        request: Request,
        project_id: int,
        phase: PhaseTypes,
        eligibility_method: EligibilityTypes,
        fields_to_skip=None,
    ) -> DndcResultsOutputPrice:
        return dndc_results

    if eligibility_method == EligibilityTypes.CARGILL_EU_2022_FR:
        monkeypatch.setattr(
            "projects.commercials.shortcuts.get_eu_project_carbon_results",
            mocked_get_eu_project_carbon_results,
        )
    else:
        await mdl.DndcContractedResults(project_id=project.id, task_id=dndc_task.id, results=dndc_results.to_dict())

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 2
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == round(4.71 + 4.71, 2)
    assert line_items_summary["total_carbon_sequestered"] == 1.10
    assert line_items_summary["total_fields_number"] == 2
    # "ghg": 2.12 gives 0 (because 2.12 > 0); 10.99 * 0 = 0
    assert line_items_summary["total_payment"] == round((round(10.99 * 1.10, 2) + 0), 2)

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    deleted_line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.isnot(None),
        ],
    )
    assert len(list(deleted_line_items)) == 2

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 2
    for line_item in line_items:
        assert line_item.note["farm_name"] == "AusCott"
        assert line_item.note["field_name"] == "Phytec Field 6-2.kml"

    # If some Value=program.reporting_period_start_date.year-1 (crediting year)
    # and related to Attribute with type=AttributeTypes.record_year, its row_id
    # is used fo filter other values.

    # If there is no such value (this case), then values with row_id=0 should be used.
    # Values "alone" and "decrease" have row_id=1, and should be filtered, but additionally values of Attribute with
    # type=AttributeTypes.practice are added. That's why there is "decrease" below.
    assert line_items[0].note[practice_attribute.name] == "decrease, rush"
    assert line_items[0].note[planting_attribute.name] == "history"
    assert line_items[1].note[practice_attribute.name] == "weave"
    assert line_items[1].note[planting_attribute.name] == "they"

    assert float(line_items[0].value) == round(10.99 * 1.10, 2)
    assert float(line_items[0].quantity) == 1.1
    assert float(line_items[1].value) == 0.0  # because -2.12 < 0; 10.99 * 0 = 0
    assert float(line_items[1].quantity) == 0.0  # because -2.12 < 0

    response = await async_client.patch(
        f"{programs_path.base}/{program.id}",
        json={"docusign_client_id": "test_client_id"},
    )
    assert response.status_code == status.HTTP_200_OK, response.content

    response = await async_client.post(  # nosec B106
        "/docusign/access_details",
        json={
            "docusign_secret_id": "test",
            "docusign_authorization_server_url": "test",
            "docusign_target_account_id": "test",
        },
    )
    assert response.status_code == status.HTTP_200_OK, response.content

    await mdl.DocusignAccess(  # nosec B106
        access_token="access_token",
        docusign_client_id="test_client_id",
        refresh_token="refresh_token",
        docusign_target_account_id="test_target_account_id",
    )

    def _mocked_get_user(*args: tuple, **kwargs: dict[str, Any]):
        class Response_:
            status_code = status.HTTP_200_OK

            def json(self):
                return {
                    "accounts": [
                        {
                            "account_id": "test_target_account_id",
                            "base_uri": "test_base_uri",
                        }
                    ]
                }

        return Response_()

    monkeypatch.setattr(DSClient, "get_user", _mocked_get_user)
    completed_date = datetime.now()

    def mocked_envelopes_api(*args: tuple, **kwargs: dict[str, Any]):
        class MockedCreateEnvelopeResults:
            envelope_id = "burst"

        class MockedCreateRecipientViewResults:
            url = "cloth"

        class MockedGetEnvelopeResults:
            status = "completed"
            completed_date_time = completed_date.isoformat()

            class MockedRecipients:
                class MockedSigner:
                    status = "completed"
                    signed_date_time = completed_date.isoformat()

                signers = [MockedSigner()]

            recipients = MockedRecipients()

        class MockedGetFormDataResults:
            form_data = {}

        class MockedEnvelopesApi:
            def create_envelope(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedCreateEnvelopeResults()

            def create_recipient_view(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedCreateRecipientViewResults()

            def get_envelope(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedGetEnvelopeResults()

            def get_document(self, *args: tuple, **kwargs: dict[str, Any]):
                return "/tmp/this-file-does-not-exist.pdf"  # nosec B104, B108

            def get_form_data(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedGetFormDataResults()

        return MockedEnvelopesApi()

    monkeypatch.setattr("docusign.controller.EnvelopesApi", mocked_envelopes_api)

    async def mocked_add_initials_on_each_page(*args: tuple, **kwargs: dict[str, Any]):
        pass

    monkeypatch.setattr(
        "docusign.controller.Docusign.add_initials_on_each_page",
        mocked_add_initials_on_each_page,
    )

    response = await async_client.post(
        f"{paths.base}/{project.id}/phases/{phase.id}{paths.contract}",
        json={"redirect_url": "test"},
    )
    assert response.status_code == status.HTTP_201_CREATED, response.content

    # Check that `signed_at` column is set when docusign callback is called
    project_contract = (await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id]))[0]
    # To begin with it should be None
    assert project_contract.signed_at is None
    with patch("builtins.open", mock_open(read_data=b"data")):
        response = await async_client.get(f"/projects/{project.id}/docusign/callback?event=abc")
    assert response.status_code == status.HTTP_200_OK
    project_contract = (await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id]))[0]
    assert project_contract.signed_at == completed_date


@patch.object(
    i18n,
    "get_translations_from_bucket",
    external_api_mocked_responses.mocked_get_translations_from_bucket,
)
async def test_create_project_commercials_payment_for_field_area(
    monkeypatch,
    async_client,
    cargill_contract_template,
    mdl,
    mock_external_api,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_area_enrolled, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(
        program_id=program.id,
        start_date=(datetime.now() - timedelta(days=30)),
        end_date=(datetime.now() + timedelta(days=4)),
        show_contract=False,
        contract_type=ContractType.DOCUSIGN,
        params={
            "limit_program_ha": 1000,
            "limit_project_ha": 300,
            "outcome_estimation": False,
        },
        type_=PhaseTypes.ENROLMENT,
    )
    stage = await mdl.Stage(
        phase_id=phase.id,
        fmi_import_start_date=(datetime.now() + timedelta(days=6)),
        fmi_import_end_date=(datetime.now() + timedelta(days=10)),
        description="string",
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=None,
    )
    practice_attribute, planting_attribute, unused_attribute = [
        await mdl.Attribute(parent_stage_id=stage.id, type=type_)
        for type_ in [
            AttributeTypes.practice,
            AttributeTypes.application_rate_unit,
            AttributeTypes.spring_tillage_practice,
        ]
    ]
    for attribute_id in [practice_attribute.id, planting_attribute.id]:
        await mdl.ContractAttributes(attribute_id=attribute_id, phase_id=phase.id, program_id=program.id)

    field_ids = []
    for practice_values, planting_values, unused_values, fs_field_id in [
        [["rush", "decrease"], ["history"], ["unused_01", "unused_02"], 1050],
        [["weave"], ["they", "alone"], ["unused_11", "unused_12"], 1067],
    ]:
        field = await mdl.Fields(
            parent_project_id=project.id,
            fs_field_id=fs_field_id,
            created_at=datetime(2024, 1, 1),
            updated_at=datetime(2025, 1, 1),
        )
        field_ids.append(field.id)

        for i, practice_value in enumerate(practice_values):
            await mdl.Values(field_id=field.id, value=practice_value, attribute_id=practice_attribute.id, row_id=i)
        for i, planting_value in enumerate(planting_values):
            await mdl.Values(field_id=field.id, value=planting_value, attribute_id=planting_attribute.id, row_id=i)
        # next values shouldn't appear in line items note because no contract attributes
        for i, unused_value in enumerate(unused_values):
            await mdl.Values(field_id=field.id, value=unused_value, attribute_id=unused_attribute.id, row_id=i)

    await mdl.ContractTemplate(template_blob=cargill_contract_template, phase_id=phase.id)

    await mdl.PhaseCommercials(
        payment=15.9934,
        payment_for=CommercialsPaymentForType.FIELD_AREA,
        phase_id=phase.id,
    )

    contract = await mdl.ProjectContracts(project=project.id, phase_id=phase.id)
    # additional line items to test deletion
    for field_id in field_ids:
        await mdl.ProjectContractLineItems(
            contract_id=contract.id,
            field_id=field_id,
            value=11.1,
            quantity=22.2,
            note={},
            created_at=datetime(2024, 1, 1),
        )

    monkeypatch.setattr("fields.methods.core_fields_get_api", mocked_core_fields_get_api)

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 2
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == round(4.71 + 4.71, 2)
    assert line_items_summary["total_carbon_sequestered"] == 0.0
    assert line_items_summary["total_fields_number"] == 2
    assert line_items_summary["total_payment"] == round(15.9934 * 4.71, 2) * 2

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    deleted_line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.isnot(None),
        ],
    )
    assert len(list(deleted_line_items)) == 2

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 2
    for line_item in line_items:
        assert line_item.note["farm_name"] == "AusCott"
        assert line_item.note["field_name"] == "Phytec Field 6-2.kml"
        assert line_item.note["payout_per_hectare"] == 15.9934

    # If some Value=program.reporting_period_start_date.year-1 (crediting year)
    # and related to Attribute with type=AttributeTypes.record_year, its row_id
    # is used fo filter other values.

    # If there is no such value (this case), then values with row_id=0 should be used.
    # Values "alone" and "decrease" have row_id=1, and should be filtered, but additionally values of Attribute with
    # type=AttributeTypes.practice are added. That's why there is "decrease" below.
    assert line_items[0].note[practice_attribute.name] == "decrease, rush"
    assert line_items[0].note[planting_attribute.name] == "history"
    assert line_items[1].note[practice_attribute.name] == "weave"
    assert line_items[1].note[planting_attribute.name] == "they"

    assert float(line_items[0].value) == round(15.9934 * 4.71, 2)
    assert float(line_items[0].quantity) == 0.0
    assert float(line_items[1].value) == round(15.9934 * 4.71, 2)
    assert float(line_items[1].quantity) == 0.0


@patch("projects.router.collect_fields_data")
async def test_create_project_commercials_payment_for_field_area_in_acres(
    mock_collect_fields_data,
    mdl,
    async_client,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_area_enrolled, units=UnitsTypes.US_IMPERIAL)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, area=1)
    await mdl.PhaseCommercials(
        payment=100,
        payment_for=CommercialsPaymentForType.FIELD_AREA,
        phase_id=phase.id,
    )

    mock_collect_fields_data.return_value = {
        field.id: FieldData(
            farm_name="test_farm",
            field_area=field.area,
            field_id=field.id,
            field_name="test_field_1",
            field_region_id=None,
            attribute_values={},
        )
    }

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 1
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == 2.47
    assert line_items_summary["total_carbon_sequestered"] == 0.0
    assert line_items_summary["total_fields_number"] == 1
    assert line_items_summary["total_payment"] == 247.10

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 1
    assert (
        line_items[0].to_dict()
        == ProjectContractLineItems(
            id=line_items[0].id,
            contract_id=project_contracts[0].id,
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_acre": 100,
            },
            value=Decimal("247.1000"),
            field_id=field.id,
            field_area=2.47,
            quantity=0,
            units="acres",
            created_at=line_items[0].created_at,
            deleted_at=line_items[0].deleted_at,
        ).to_dict()
    )


@patch("projects.commercials.shortcuts.get_pay_per_practice_commercial_rules_for_program_from_csv")
@patch("projects.router.submit_project_fields_to_explore_if_none_or_invalidated")
@patch("projects.router.collect_fields_data")
async def test_create_project_commercials_payment_for_practices(
    mock_collect_fields_data,
    mock_submit_project_fields_to_explore_if_none_or_invalidated,
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv,
    mdl,
    async_client,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_practice, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)
    assign_practice_attr = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.practice, name="ASSIGN_PRACTICES"
    )
    targeted_region = await mdl.CoreRegions(name="targeted_region")
    non_targeted_region = await mdl.CoreRegions(name="non_targeted_region")
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)
    field_2 = await mdl.Fields(parent_project_id=project.id, area=2)

    mock_collect_fields_data.return_value = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
        field_2.id: FieldData(
            farm_name="test_farm",
            field_area=field_2.area,
            field_id=field_2.id,
            field_name="test_field_2",
            field_region_id=non_targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
    }
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv.return_value = [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=targeted_region.name,
            payment=100,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=non_targeted_region.name,
            payment=90,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=targeted_region.name,
            payment=80,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=non_targeted_region.name,
            payment=70,
        ),
    ]

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 2
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == 3
    assert line_items_summary["total_carbon_sequestered"] == 0.0
    assert line_items_summary["total_fields_number"] == 2
    assert line_items_summary["total_payment"] == 500.00

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 2
    assert (
        line_items[0].to_dict()
        == ProjectContractLineItems(
            id=line_items[0].id,
            contract_id=project_contracts[0].id,
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_hectare": 180,
                "field_region": targeted_region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=180.00,
            field_id=field_1.id,
            field_area=1,
            quantity=0,
            units="hectares",
            created_at=line_items[0].created_at,
            deleted_at=line_items[0].deleted_at,
        ).to_dict()
    )
    assert (
        line_items[1].to_dict()
        == ProjectContractLineItems(
            id=line_items[1].id,
            contract_id=project_contracts[0].id,
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_2",
                "payout_per_hectare": 160,
                "field_region": non_targeted_region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=320.00,
            field_id=field_2.id,
            field_area=2,
            quantity=0,
            units="hectares",
            created_at=line_items[1].created_at,
            deleted_at=line_items[1].deleted_at,
        ).to_dict()
    )

    mock_submit_project_fields_to_explore_if_none_or_invalidated.assert_called_with(
        request=ANY, project_id=project.id, field_ids=[field_1.id, field_2.id]
    )


@patch("projects.commercials.shortcuts.get_pay_per_practice_commercial_rules_for_program_from_csv")
@patch("projects.router.submit_project_fields_to_explore_if_none_or_invalidated")
@patch("projects.router.collect_fields_data")
async def test_create_project_commercials_payment_for_practices_in_acres(
    mock_collect_fields_data,
    mock_submit_project_fields_to_explore_if_none_or_invalidated,
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv,
    mdl,
    async_client,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_practice, units=UnitsTypes.US_IMPERIAL)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)
    assign_practice_attr = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.practice, name="ASSIGN_PRACTICES"
    )
    targeted_region = await mdl.CoreRegions(name="targeted_region")
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)

    mock_collect_fields_data.return_value = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
    }
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv.return_value = [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=targeted_region.name,
            payment=60,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=targeted_region.name,
            payment=40,
        ),
    ]

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 1
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == 2.47
    assert line_items_summary["total_carbon_sequestered"] == 0.0
    assert line_items_summary["total_fields_number"] == 1
    assert line_items_summary["total_payment"] == 247.1

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 1
    assert (
        line_items[0].to_dict()
        == ProjectContractLineItems(
            id=line_items[0].id,
            contract_id=project_contracts[0].id,
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_acre": 100,
                "field_region": targeted_region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=Decimal("247.1000"),
            field_id=field_1.id,
            field_area=2.47,
            quantity=0,
            units="acres",
            created_at=line_items[0].created_at,
            deleted_at=line_items[0].deleted_at,
        ).to_dict()
    )

    mock_submit_project_fields_to_explore_if_none_or_invalidated.assert_called_with(
        request=ANY, project_id=project.id, field_ids=[field_1.id]
    )


@patch("projects.commercials.shortcuts.get_pay_per_practice_commercial_rules_for_program_from_csv")
@patch("projects.router.collect_fields_data")
@patch("projects.commercials.shortcuts.submit_project_fields_to_explore_task")
@patch("projects.commercials.shortcuts.calculate_dndc_status")
async def test_create_project_commercials_payment_for_practices_no_explore(
    mock_calculate_dndc_status,
    mock_submit_project_fields_to_explore_task,
    mock_collect_fields_data,
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv,
    mdl,
    async_client,
    orm_select,
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_practice, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)
    assign_practice_attr = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.practice, name="ASSIGN_PRACTICES"
    )
    targeted_region = await mdl.CoreRegions(name="targeted_region")
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)

    mock_collect_fields_data.return_value = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
    }
    mock_calculate_dndc_status.return_value = ProjectGeom(program_id=program.id, dndc_status=DndcStatusChoices.success)
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv.return_value = [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=targeted_region.name,
            payment=60,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=targeted_region.name,
            payment=40,
        ),
    ]

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 1
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == 1
    assert line_items_summary["total_carbon_sequestered"] == 0.0
    assert line_items_summary["total_fields_number"] == 1
    assert line_items_summary["total_payment"] == 100.00

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 1
    assert (
        line_items[0].to_dict()
        == ProjectContractLineItems(
            id=line_items[0].id,
            contract_id=project_contracts[0].id,
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_hectare": 100,
                "field_region": targeted_region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=100.00,
            field_id=field_1.id,
            field_area=1,
            quantity=0,
            units="hectares",
            created_at=line_items[0].created_at,
            deleted_at=line_items[0].deleted_at,
        ).to_dict()
    )

    mock_submit_project_fields_to_explore_task.delay.assert_not_called()


@patch.object(
    i18n,
    "get_translations_from_bucket",
    external_api_mocked_responses.mocked_get_translations_from_bucket,
)
async def test_create_contract_with_skipped_fields(
    monkeypatch,
    async_client,
    cargill_contract_template,
    mdl,
    mdl_factory_ignore_ids,
    mock_external_api,
    orm_select,
):
    program = await mdl_factory_ignore_ids(
        Programs, ignore_ids=CARGILL_EU_FR_PROGRAM_IDS, payout_structure=PayoutStructure.pay_per_net_ghg_reduced
    )
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(
        program_id=program.id,
        start_date=(datetime.now() - timedelta(days=30)),
        end_date=(datetime.now() + timedelta(days=4)),
        show_contract=False,
        contract_type=ContractType.DOCUSIGN,
        params={
            "limit_program_ha": 1000,
            "limit_project_ha": 300,
            "outcome_estimation": False,
        },
        type_=PhaseTypes.ENROLMENT,
    )
    stage = await mdl.Stage(
        phase_id=phase.id,
        fmi_import_start_date=(datetime.now() + timedelta(days=6)),
        fmi_import_end_date=(datetime.now() + timedelta(days=10)),
        description="string",
        type_=StageTypes.ASSIGN_PRACTICES,
        eligibility_method=EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE,
    )
    practice_attribute, planting_attribute, unused_attribute = [
        await mdl.Attribute(parent_stage_id=stage.id, type=type_)
        for type_ in [
            AttributeTypes.practice,
            AttributeTypes.application_rate_unit,
            AttributeTypes.spring_tillage_practice,
        ]
    ]
    for attribute_id in [practice_attribute.id, planting_attribute.id]:
        await mdl.ContractAttributes(attribute_id=attribute_id, phase_id=phase.id, program_id=program.id)

    field_ids = []
    for practice_values, planting_values, unused_values, fs_field_id in [
        [["rush", "decrease"], ["history"], ["unused_01", "unused_02"], 1050],
        [["weave"], ["they", "alone"], ["unused_11", "unused_12"], 1067],
    ]:
        field = await mdl.Fields(parent_project_id=project.id, fs_field_id=fs_field_id)
        field_ids.append(field.id)

        for i, practice_value in enumerate(practice_values):
            await mdl.Values(field_id=field.id, value=practice_value, attribute_id=practice_attribute.id, row_id=i)
        for i, planting_value in enumerate(planting_values):
            await mdl.Values(field_id=field.id, value=planting_value, attribute_id=planting_attribute.id, row_id=i)
        # next values shouldn't appear in line items note because no contract attributes
        for i, unused_value in enumerate(unused_values):
            await mdl.Values(field_id=field.id, value=unused_value, attribute_id=unused_attribute.id, row_id=i)

    await mdl.ContractTemplate(template_blob=cargill_contract_template, phase_id=phase.id)
    dndc_task = await mdl.DndcTasks(
        id="task_id", project_id=project.id, program_id=project.program_id, status="finished"
    )
    await mdl.DndcContractedResults(
        project_id=project.id,
        task_id=dndc_task.id,
        results=DndcResultsOutputPrice(
            field_count=len(field_ids),
            fields_with_results={
                str(field_ids[0]): {"ghg": -2.1},
            },
            fields_without_results=[field_ids[1]],
            fields_with_errors={},
            soc=0.0,
            n2o=0.0,
            ch4=0.0,
            ghg=-42.2,
            indirect_n2o=0.0,
            total_n2o=0.0,
            ghg_soc=0.0,
            ghg_ch4=0.0,
            ghg_n2o=0.0,
            ghg_indirect_n2o=0.0,
            ghg_total_n2o=0.0,
            carbon_price_dollars=2,
            payment_dollars=0.0,
        ).to_dict(),
    )

    await mdl.PhaseCommercials(
        payment=10.99,
        payment_for=CommercialsPaymentForType.ESTIMATED_GHG,
        phase_id=phase.id,
    )

    monkeypatch.setattr("fields.methods.core_fields_get_api", mocked_core_fields_get_api)

    async def mocked_get_field_ids_to_skip_for_project(
        request: Request,
        project_id: int,
    ) -> list[int]:
        return [field_ids[1]]

    monkeypatch.setattr(
        "projects.router.get_field_ids_to_skip_for_project",
        mocked_get_field_ids_to_skip_for_project,
    )
    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 2
    line_items_summary = response.json()["line_items_summary"]
    assert line_items_summary["total_area"] == round(4.71 + 4.71, 2)
    assert line_items_summary["total_carbon_sequestered"] == 2.10
    assert line_items_summary["total_fields_number"] == 2
    # "ghg": 2.12 gives 0 (because 2.12 > 0); 10.99 * 0 = 0
    assert line_items_summary["total_payment"] == round((round(10.99 * 2.10, 2) + 0), 2)

    project_contracts = await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id])
    assert len(project_contracts) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == project_contracts[0].id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 2
    for line_item in line_items:
        assert line_item.note["farm_name"] == "AusCott"
        assert line_item.note["field_name"] == "Phytec Field 6-2.kml"

    assert float(line_items[0].value) == round(10.99 * 2.10, 2)
    assert float(line_items[0].quantity) == 2.1
    assert float(line_items[1].value) == 0.0
    assert float(line_items[1].quantity) == 0.0
    assert line_items[1].note["ASSIGN_PRACTICES"] == "-"

    response = await async_client.patch(
        f"{programs_path.base}/{program.id}",
        json={"docusign_client_id": "test_client_id"},
    )
    assert response.status_code == status.HTTP_200_OK, response.content

    response = await async_client.post(  # nosec B106
        "/docusign/access_details",
        json={
            "docusign_secret_id": "test",
            "docusign_authorization_server_url": "test",
            "docusign_target_account_id": "test",
        },
    )
    assert response.status_code == status.HTTP_200_OK, response.content

    await mdl.DocusignAccess(  # nosec B106
        access_token="access_token",
        docusign_client_id="test_client_id",
        refresh_token="refresh_token",
        docusign_target_account_id="test_target_account_id",
    )

    def _mocked_get_user(*args: tuple, **kwargs: dict[str, Any]):
        class Response_:
            status_code = status.HTTP_200_OK

            def json(self):
                return {
                    "accounts": [
                        {
                            "account_id": "test_target_account_id",
                            "base_uri": "test_base_uri",
                        }
                    ]
                }

        return Response_()

    monkeypatch.setattr(DSClient, "get_user", _mocked_get_user)
    completed_date = datetime.now()

    def mocked_envelopes_api(*args: tuple, **kwargs: dict[str, Any]):
        class MockedCreateEnvelopeResults:
            envelope_id = "burst"

        class MockedCreateRecipientViewResults:
            url = "cloth"

        class MockedGetEnvelopeResults:
            status = "completed"
            completed_date_time = completed_date.isoformat()

            class MockedRecipients:
                class MockedSigner:
                    status = "completed"
                    signed_date_time = completed_date.isoformat()

                signers = [MockedSigner()]

            recipients = MockedRecipients()

        class MockedGetFormDataResults:
            form_data = {}

        class MockedEnvelopesApi:
            def create_envelope(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedCreateEnvelopeResults()

            def create_recipient_view(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedCreateRecipientViewResults()

            def get_envelope(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedGetEnvelopeResults()

            def get_document(self, *args: tuple, **kwargs: dict[str, Any]):
                return "/tmp/this-file-does-not-exist.pdf"  # nosec B104, B108

            def get_form_data(self, *args: tuple, **kwargs: dict[str, Any]):
                return MockedGetFormDataResults()

        return MockedEnvelopesApi()

    monkeypatch.setattr("docusign.controller.EnvelopesApi", mocked_envelopes_api)

    async def mocked_add_initials_on_each_page(*args: tuple, **kwargs: dict[str, Any]):
        pass

    monkeypatch.setattr(
        "docusign.controller.Docusign.add_initials_on_each_page",
        mocked_add_initials_on_each_page,
    )

    response = await async_client.post(
        f"{paths.base}/{project.id}/phases/{phase.id}{paths.contract}",
        json={"redirect_url": "test"},
    )
    assert response.status_code == status.HTTP_201_CREATED, response.content

    # Check that `signed_at` column is set when docusign callback is called
    project_contract = (await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id]))[0]
    # To begin with it should be None
    assert project_contract.signed_at is None
    with patch("builtins.open", mock_open(read_data=b"data")):
        response = await async_client.get(f"/projects/{project.id}/docusign/callback?event=abc")
    assert response.status_code == status.HTTP_200_OK
    project_contract = (await orm_select(ProjectContracts, where=[ProjectContracts.project == project.id]))[0]
    assert project_contract.signed_at == completed_date


async def test_create_project_commercials_when_contract_completed(async_client, mdl, orm_select):
    """[FSB-14279] Test that line items are not deleted when contract is completed."""
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_net_ghg_reduced, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(
        program_id=program.id,
        start_date=(datetime.now() - timedelta(days=30)),
        end_date=(datetime.now() + timedelta(days=4)),
        show_contract=False,
        contract_type=ContractType.DOCUSIGN,
        params={
            "limit_program_ha": 1000,
            "limit_project_ha": 300,
            "outcome_estimation": False,
        },
        type_=PhaseTypes.ENROLMENT,
    )
    contract = await mdl.ProjectContracts(
        project=project.id, phase_id=phase.id, docusign_status=EnvelopeStatus.completed
    )
    field = await mdl.Fields(parent_project_id=project.id, fs_field_id=1050)
    await mdl.ProjectContractLineItems(
        contract_id=contract.id,
        note={},
        value=12.09,
        field_id=field.id,
        quantity=1.1,
        units="Tons CO2e",
    )

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    assert len(response.json()["line_items"]) == 1

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == contract.id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 1


@patch("projects.router.collect_fields_data")
async def test_create_project_commercials_when_contract_completed_and_no_contract_line_items(
    mock_collect_fields_data, async_client, mdl, orm_select
):
    program = await mdl.Programs(payout_structure=PayoutStructure.pay_per_area_enrolled, units=UnitsTypes.METRIC)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    await mdl.PhaseCommercials(phase_id=phase.id, payment_for=CommercialsPaymentForType.FIELD_AREA, payment=1)
    field = await mdl.Fields(parent_project_id=project.id, fs_field_id=1050, area=100)

    contract = await mdl.ProjectContracts(
        project=project.id, phase_id=phase.id, docusign_status=EnvelopeStatus.completed
    )

    mock_collect_fields_data.return_value = {
        field.id: FieldData(
            farm_name="test_farm",
            field_area=field.area,
            field_id=field.id,
            field_name="test_field",
            field_region_id=field.core_region_id,
            attribute_values={},
        )
    }

    response = await async_client.post(f"{paths.base}/{project.id}{paths.phases}/{phase.id}{paths.commercials}")
    assert response.status_code == status.HTTP_201_CREATED, response.content

    line_items = await orm_select(
        ProjectContractLineItems,
        where=[
            ProjectContractLineItems.contract_id == contract.id,
            ProjectContractLineItems.deleted_at.is_(None),
        ],
    )
    assert len(line_items) == 1
