from datetime import datetime
from unittest.mock import ANY, MagicMock, patch

import pytest
from fastapi import HTT<PERSON>Exception

from domain_event_bus.domain_event_bus import measurement_eligibility_event_handler
from domain_event_bus.domain_events import ProjectContractUpdatedEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.tillage_event import TillageEvent
from phases.enums import PhaseTypes, StageTypes
from projects.model import ProjectContracts
from projects.schema import SignerInfo
from projects.utils import (
    create_or_update_contract,
    filter_event_if_not_in_stage,
    process_field_events,
    update_project_contract,
)


async def test_filter_event_if_not_in_stage(mdl):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    stage = await mdl.Stage(
        phase_id=phase.id,
        optis_year_start=2018,
        optis_year_end=2023,
        optis_prefill=True,
        type_=StageTypes.TILLAGE_EVENTS,
    )

    event_type = EntityEventType.TILLAGE_EVENT

    assert await filter_event_if_not_in_stage(event_type, stage) is False

    event_type = EntityEventType.CROPPING_EVENT

    assert await filter_event_if_not_in_stage(event_type, stage) is True

    event_type = "fake_event"

    assert await filter_event_if_not_in_stage(event_type, stage) is True


async def test_process_field_events(mdl, create_tillage_event_data, create_cropping_event_data):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    stage = await mdl.Stage(
        phase_id=phase.id,
        optis_year_start=2018,
        optis_year_end=2023,
        optis_prefill=True,
        type_=StageTypes.TILLAGE_EVENTS,
    )

    tillage_event = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_event = CroppingEvent.parse_obj(create_cropping_event_data())

    events = [tillage_event, cropping_event]

    field_events = await process_field_events(events, stage, None, None)

    assert len(field_events[tillage_event.entity_id]) == 1

    field_events = await process_field_events(events, None, None, None)

    assert len(field_events[tillage_event.entity_id]) == 2


@patch.object(measurement_eligibility_event_handler, "handle_event")
async def test_update_contract(mock_handle_event, mdl, app_request, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    project_contract = await mdl.ProjectContracts(project=project.id, phase_id=phase.id, docusign_status=None)

    await create_or_update_contract(
        request=app_request,
        project_id=project.id,
        docusign_envelope_id=project_contract.docusign_envelope_id,
        docusign_status="signed",
        project_contract=project_contract,
    )

    updated_contract = (await orm_select(ProjectContracts, where=[ProjectContracts.id == project_contract.id]))[0]
    assert updated_contract.docusign_status == "signed"

    mock_handle_event.assert_called_with(
        event=ProjectContractUpdatedEvent(program_id=program.id, project_id=project.id), request=ANY
    )


def create_mock_envelope(
    status="completed",
    signer_status="completed",
    signed_date_time=None,
    completed_date_time=None,
):
    mock_signer = MagicMock()
    mock_signer.status = signer_status
    mock_signer.signed_date_time = signed_date_time or "2023-12-01T10:00:00Z"

    mock_recipients = MagicMock()
    mock_recipients.signers = [mock_signer]

    mock_envelope = MagicMock()
    mock_envelope.status = status
    mock_envelope.recipients = mock_recipients
    mock_envelope.completed_date_time = completed_date_time or "2023-12-01T10:00:00Z"

    return mock_envelope


def create_mock_form_data(marketing_value="Yes"):
    mock_form_item = MagicMock()
    mock_form_item.name = "marketing_texts"
    mock_form_item.value = marketing_value

    mock_form_data = MagicMock()
    mock_form_data.form_data = [mock_form_item]

    return mock_form_data


@patch("projects.methods.back_fill_project_status_for_projects")
@patch("fields.methods.back_fill_field_status")
@patch("projects.methods.back_fill_contract_status_for_projects")
@patch("projects.utils.upsert_project_phase_completion")
@patch("projects.utils.update.partial_update")
@patch("projects.utils.create_or_update_contract")
@patch("projects.utils.Docusign")
async def test_update_project_contract_success_status_change(
    mock_docusign_class,
    mock_create_or_update_contract,
    mock_partial_update,
    mock_upsert_phase_completion,
    mock_back_fill_contract,
    mock_back_fill_field,
    mock_back_fill_project,
    mdl,
    app_request,
):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    project_contract = await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status=None,  # Status will change from None to completed
    )
    mock_envelope = create_mock_envelope()
    mock_form_data = create_mock_form_data("Yes")

    mock_docusign_instance = MagicMock()
    mock_docusign_instance.get_args = MagicMock(
        return_value={
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }
    )

    async def async_get_args(request):
        return {
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }

    mock_docusign_instance.get_args = async_get_args
    mock_docusign_instance.get_envelope.return_value = mock_envelope
    mock_docusign_instance.get_pdf_document.return_value = {"data": "/tmp/test.pdf"}  # nosec B108
    mock_docusign_instance.get_form_data.return_value = mock_form_data
    mock_docusign_class.return_value = mock_docusign_instance

    with patch("builtins.open", create=True) as mock_open:
        mock_file = MagicMock()
        mock_file.read.return_value = b"test_pdf_content"
        mock_open.return_value.__enter__.return_value = mock_file

        await update_project_contract(app_request, project.id)

    mock_docusign_class.assert_called_once_with(
        client_id="test_client_id",
        client_secret="test_secret",
        authorization_server="test_server",
        target_account_id="test_account",
    )

    mock_docusign_instance.get_envelope.assert_called_once()
    mock_docusign_instance.get_pdf_document.assert_called_once()
    mock_docusign_instance.get_form_data.assert_called_once()

    mock_create_or_update_contract.assert_called_once()
    call_args = mock_create_or_update_contract.call_args
    assert call_args[1]["project_id"] == project.id
    assert call_args[1]["docusign_envelope_id"] == "test_envelope_id"
    assert call_args[1]["docusign_status"] == "completed"
    assert call_args[1]["project_contract"].id == project_contract.id

    mock_partial_update.assert_called_once()
    project_update_args = mock_partial_update.call_args
    assert project_update_args[1]["id"] == project.id
    assert project_update_args[1]["item"].send_marketing_data is True

    mock_upsert_phase_completion.assert_called_once_with(
        request=app_request, project_id=project.id, phase_id=phase.id, is_completed=True
    )

    mock_back_fill_contract.assert_called_once_with(request=app_request, project_ids=[project.id])
    mock_back_fill_field.assert_called_once_with(app_request, project_ids=[project.id])
    mock_back_fill_project.assert_called_once_with(app_request, project_ids=[project.id])


async def test_update_project_contract_no_docusign_credentials(mdl, app_request):
    program = await mdl.Programs(docusign_client_id="nonexistent_client_id")
    project = await mdl.Projects(program_id=program.id)

    with pytest.raises(HTTPException) as exc_info:
        await update_project_contract(app_request, project.id)

    assert exc_info.value.status_code == 400
    assert "No Docusign credentials found" in str(exc_info.value.detail)


async def test_update_project_contract_no_project_contracts(mdl, app_request):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )

    with pytest.raises(HTTPException) as exc_info:
        await update_project_contract(app_request, project.id)

    assert exc_info.value.status_code == 400
    assert "No Contract found for project" in str(exc_info.value.detail)


async def test_update_project_contract_no_docusign_envelope_id(mdl, app_request):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id=None,  # No envelope ID
        docusign_status="sent",
    )

    with pytest.raises(HTTPException) as exc_info:
        await update_project_contract(app_request, project.id)

    assert exc_info.value.status_code == 400
    assert "No Docusign ID found for project" in str(exc_info.value.detail)


@patch("projects.utils.Docusign")
async def test_update_project_contract_no_status_change(
    mock_docusign_class,
    mdl,
    app_request,
):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status="completed",  # Already completed
    )

    mock_envelope = create_mock_envelope(status="completed", signer_status="completed")

    mock_docusign_instance = MagicMock()

    async def async_get_args(request):
        return {
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }

    mock_docusign_instance.get_args = async_get_args
    mock_docusign_instance.get_envelope.return_value = mock_envelope
    mock_docusign_class.return_value = mock_docusign_instance

    await update_project_contract(app_request, project.id)

    mock_docusign_instance.get_pdf_document.assert_not_called()
    mock_docusign_instance.get_form_data.assert_not_called()


@patch("projects.methods.back_fill_project_status_for_projects")
@patch("fields.methods.back_fill_field_status")
@patch("projects.methods.back_fill_contract_status_for_projects")
@patch("projects.utils.upsert_project_phase_completion")
@patch("projects.utils.update.partial_update")
@patch("projects.utils.create_or_update_contract")
@patch("projects.utils.Docusign")
async def test_update_project_contract_producer_signed_cosigner_not(
    mock_docusign_class,
    mock_create_or_update_contract,
    mock_partial_update,
    mock_upsert_phase_completion,
    mock_back_fill_contract,
    mock_back_fill_field,
    mock_back_fill_project,
    mdl,
    app_request,
):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status="sent",
    )

    mock_envelope = create_mock_envelope(
        status="sent",
        signer_status="completed",
    )
    mock_form_data = create_mock_form_data("No")

    mock_docusign_instance = MagicMock()

    async def async_get_args(request):
        return {
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }

    mock_docusign_instance.get_args = async_get_args
    mock_docusign_instance.get_envelope.return_value = mock_envelope
    mock_docusign_instance.get_pdf_document.return_value = {"data": "/tmp/test.pdf"}  # nosec B108
    mock_docusign_instance.get_form_data.return_value = mock_form_data
    mock_docusign_class.return_value = mock_docusign_instance

    with patch("builtins.open", create=True) as mock_open:
        mock_file = MagicMock()
        mock_file.read.return_value = b"test_pdf_content"
        mock_open.return_value.__enter__.return_value = mock_file

        await update_project_contract(app_request, project.id)

    call_args = mock_create_or_update_contract.call_args
    assert call_args[1]["docusign_status"] == "completed"

    project_update_args = mock_partial_update.call_args
    assert project_update_args[1]["item"].send_marketing_data is False

    mock_upsert_phase_completion.assert_called_once_with(
        request=app_request, project_id=project.id, phase_id=phase.id, is_completed=True
    )


@patch("projects.methods.back_fill_project_status_for_projects")
@patch("fields.methods.back_fill_field_status")
@patch("projects.methods.back_fill_contract_status_for_projects")
@patch("projects.utils.upsert_project_phase_completion")
@patch("projects.utils.update.partial_update")
@patch("projects.utils.create_or_update_contract")
@patch("projects.utils.Docusign")
async def test_update_project_contract_webhook_event_all_parties_signed(
    mock_docusign_class,
    mock_create_or_update_contract,
    mock_partial_update,
    mock_upsert_phase_completion,
    mock_back_fill_contract,
    mock_back_fill_field,
    mock_back_fill_project,
    mdl,
    app_request,
):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    project_contract = await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status="completed",
    )
    mock_envelope = create_mock_envelope(status="completed", signer_status="completed")
    mock_form_data = create_mock_form_data("Yes")

    mock_docusign_instance = MagicMock()

    async def async_get_args(request):
        return {
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }

    mock_docusign_instance.get_args = async_get_args
    mock_docusign_instance.get_envelope.return_value = mock_envelope
    mock_docusign_instance.get_pdf_document.return_value = {"data": "/tmp/test.pdf"}  # nosec B108
    mock_docusign_instance.get_form_data.return_value = mock_form_data
    mock_docusign_class.return_value = mock_docusign_instance

    with patch("builtins.open", create=True) as mock_open:
        mock_file = MagicMock()
        mock_file.read.return_value = b"test_pdf_content"
        mock_open.return_value.__enter__.return_value = mock_file

        await update_project_contract(app_request, project.id, is_webhook_event=True)

    mock_create_or_update_contract.assert_called_once()
    call_args = mock_create_or_update_contract.call_args
    assert call_args[1]["project_id"] == project.id
    assert call_args[1]["docusign_envelope_id"] == "test_envelope_id"
    assert call_args[1]["docusign_status"] == "completed"
    assert call_args[1]["project_contract"].id == project_contract.id

    mock_docusign_instance.get_pdf_document.assert_called_once()
    mock_docusign_instance.get_form_data.assert_called_once()

    mock_partial_update.assert_called_once()
    project_update_args = mock_partial_update.call_args
    assert project_update_args[1]["id"] == project.id
    assert project_update_args[1]["item"].send_marketing_data is True

    mock_upsert_phase_completion.assert_called_once_with(
        request=app_request, project_id=project.id, phase_id=phase.id, is_completed=True
    )


@patch("projects.methods.back_fill_project_status_for_projects")
@patch("fields.methods.back_fill_field_status")
@patch("projects.methods.back_fill_contract_status_for_projects")
@patch("projects.utils.upsert_project_phase_completion")
@patch("projects.utils.update.partial_update")
@patch("projects.utils.create_or_update_contract")
@patch("projects.utils.Docusign")
async def test_update_project_contract_populates_signer_info(
    mock_docusign_class,
    mock_create_or_update_contract,
    mock_partial_update,
    mock_upsert_phase_completion,
    mock_back_fill_contract,
    mock_back_fill_field,
    mock_back_fill_project,
    mdl,
    app_request,
):
    program = await mdl.Programs(docusign_client_id="test_client_id")
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.DocusignAccess(
        docusign_client_id="test_client_id",
        docusign_secret_id="test_secret",
        docusign_authorization_server_url="test_server",
        docusign_target_account_id="test_account",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
    )
    await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status=None,
    )

    mock_envelope = create_mock_envelope(
        status="completed",
        signer_status="completed",
        signed_date_time="2023-12-01T10:00:00Z",
        completed_date_time="2023-12-01T10:00:00Z",
    )
    mock_form_data = create_mock_form_data("Yes")

    mock_docusign_instance = MagicMock()

    async def async_get_args(request):
        return {
            "base_path": "test_path",
            "access_token": "test_token",
            "account_id": "test_account",
        }

    mock_docusign_instance.get_args = async_get_args
    mock_docusign_instance.get_envelope.return_value = mock_envelope
    mock_docusign_instance.get_pdf_document.return_value = {"data": "/tmp/test.pdf"}  # nosec B108
    mock_docusign_instance.get_form_data.return_value = mock_form_data
    mock_docusign_class.return_value = mock_docusign_instance

    with patch("builtins.open", create=True) as mock_open:
        mock_file = MagicMock()
        mock_file.read.return_value = b"test_pdf_content"
        mock_open.return_value.__enter__.return_value = mock_file

        await update_project_contract(app_request, project.id)

    mock_create_or_update_contract.assert_called_once()
    call_args = mock_create_or_update_contract.call_args
    signer_info = call_args[1]["signer_info"]

    assert signer_info is not None
    assert isinstance(signer_info, SignerInfo)
    assert signer_info.producer_signed_at is not None


async def test_create_or_update_contract_with_signer_info(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    project_contract = await mdl.ProjectContracts(
        project=project.id,
        phase_id=phase.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status="sent",
    )

    signer_info = SignerInfo(
        producer_signed_at=datetime.fromisoformat("2023-12-01T10:00:00Z"),
        cosigner_signed_at=datetime.fromisoformat("2023-12-01T14:00:00Z"),
        all_parties_signed_at=datetime.fromisoformat("2023-12-01T14:00:00Z"),
    )

    await create_or_update_contract(
        request=app_request,
        project_id=project.id,
        docusign_envelope_id="test_envelope_id",
        docusign_status="completed",
        project_contract=project_contract,
        signer_info=signer_info,
    )

    from root_crud import get

    updated_contracts = await get.get(
        request=app_request,
        orm_type=ProjectContracts,
        ids=[project_contract.id],
        id_field=ProjectContracts.id,
    )
    updated_contract = updated_contracts[0]

    assert updated_contract.signer_info is not None
    assert isinstance(updated_contract.signer_info, dict)
    assert "producer_signed_at" in updated_contract.signer_info
    assert "cosigner_signed_at" in updated_contract.signer_info
    assert "all_parties_signed_at" in updated_contract.signer_info
    assert updated_contract.docusign_status == "completed"


async def test_signer_info_model_validation():
    from projects.schema import ProjectContractsNoBlobResponse

    contract_data = {
        "id": 1,
        "project": 123,
        "user": 456,
        "created_at": datetime.now(),
        "docusign_status": "completed",
        "deleted_at": None,
        "signer_info": {
            "producer_signed_at": "2023-12-01T10:00:00",
            "cosigner_signed_at": "2023-12-01T14:00:00",
            "all_parties_signed_at": "2023-12-01T14:00:00",
        },
        "deletion_reason": None,
    }

    response = ProjectContractsNoBlobResponse(**contract_data)

    assert response.signer_info is not None
    assert isinstance(response.signer_info, SignerInfo)
    assert response.signer_info.producer_signed_at is not None
    assert response.signer_info.cosigner_signed_at is not None
    assert response.signer_info.all_parties_signed_at is not None
