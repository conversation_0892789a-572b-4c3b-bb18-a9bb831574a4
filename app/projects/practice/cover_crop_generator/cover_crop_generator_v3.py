from __future__ import annotations

from collections import defaultdict
from typing import TYPE_CHECKING

from cultivation_cycles.schema import CultivationCycle
from defaults.attribute_options import CropUsage
from defaults.consts import COVER_CROP_USAGES
from entity_events.events.cropping_event import CroppingEvent
from phases.enums import CoverCropMappingConditions, EventTypes
from phases.schema import CoverCropMappingRule
from programs.enums import PracticeChange
from projects.practice.cover_crop_generator.base_cover_crop_generator import (
    CoverCropGenerator,
)
from projects.practice.enums import PracticeTypes
from projects.practice.schema import EntityPractice

if TYPE_CHECKING:
    from phases.schema import EventDetail

from logger import get_logger

logger = get_logger(__name__)

DEFAULT_COVER_CROP_TYPE = "cover_crop"


class CoverCropGeneratorV3(CoverCropGenerator):
    """
    This version of generator generates cover crop practices based on the cover crop usage returns cover crop for a year
    if cover crop usage is cover. If cover crop usage is commodity only, then it returns no cover crop for that year.
    """

    def __event_generator(
        self,
        historical_years: int,
        event_detail_config: EventDetail,
    ) -> dict[int, list[EntityPractice]]:
        """
        For the given EventDetail and number of historical_years, process `self.cultivation_cycles` into a collection of
        EntityPractices keyed by entity_id.
        1. Check for cover crop usage events and then cover crop practice.
        2. If cover crop usage events are set to Cover, then the cover crop practice is added to the year practice.
        3. Winter wheat is treated as no cover crop.

        Returns { entity_id: EntityPractice[] }
        """
        cover_crop_practice: dict[int, list[EntityPractice]] = defaultdict(list)
        entity_cover_crop_type: dict[int, dict[int, str | None]] = {}  # { entity_id: { year: cover_crop_type } }

        for cultivation_cycle in self.cultivation_cycles:
            # Use the cultivation cycle's end year
            cycle_year = cultivation_cycle.end.year

            # Process all cropping events in this cultivation cycle
            for event in cultivation_cycle.events:
                if not isinstance(event, CroppingEvent) or event.crop_usage is None:
                    continue

                entity_id = event.entity_id
                end_year = self.baseline_years.get(entity_id)
                if not end_year:
                    continue

                start_year = end_year - historical_years

                if start_year < cycle_year <= end_year:
                    """
                    "intensity" describes how better/worse the practice change is, so more intensity in an entity-year means
                    a "better" practice and creates a more intense PracticeChange.
                    Check if cover crop usage is set to Cover or Commodity for entity for a year.
                    If cover and commodity both present then save the cover crop type, default to "cover_crop".
                    If cover is present but not commodity then save the cover crop type, default to "cover_crop".
                    If commodity is present but not cover then mark cover crop type as None.
                    There should not be a case of neither commodity nor cover being present.
                    """
                    if entity_id not in entity_cover_crop_type:
                        entity_cover_crop_type[entity_id] = {}
                    if event.crop_usage in COVER_CROP_USAGES:
                        # save the cover crop type if current event usage is one of cover crop usages, default crop type "cover_crop"
                        entity_cover_crop_type[entity_id][cycle_year] = event.crop_type or DEFAULT_COVER_CROP_TYPE
                    elif event.crop_usage == CropUsage.COMMODITY:
                        # if cover crop already set, keep it as is. Otherwise set it to None.
                        entity_cover_crop_type[entity_id][cycle_year] = entity_cover_crop_type[entity_id].get(
                            cycle_year, None
                        )
                    else:
                        logger.error(f"Invalid value for crop usage: {event.crop_usage}")

        for entity_id, cover_crop_type_map in entity_cover_crop_type.items():
            for year, cover_crop_type in cover_crop_type_map.items():
                practice_value = PracticeChange.no_cover_crop
                if cover_crop_type:
                    if event_detail_config.config and event_detail_config.config.cover_crop_mapping:
                        # if mapping rule specified, use rules to map cover crop type to practice
                        practice_value = self.__map_cover_crop_practice_with_rules(
                            cover_crop_type, event_detail_config.config.cover_crop_mapping
                        )
                    else:
                        # default map to cover crops
                        practice_value = PracticeChange.cover_crops

                cover_crop_practice[entity_id].append(
                    EntityPractice(
                        year=year,
                        entity_id=entity_id,
                        entity_type=self.entity_type,
                        practice=practice_value,
                    )
                )
        return cover_crop_practice

    def __map_cover_crop_practice_with_rules(
        self, cover_crop_type: str, rules: list[CoverCropMappingRule]
    ) -> PracticeChange:
        """
        apply cover crop mapping rules, find the first match,
        default to cover crops if no match found, but we should not allow that to happen with the NOT_IN condition
        """
        for rule in rules:
            if rule.condition == CoverCropMappingConditions.IN:
                if cover_crop_type in rule.value:
                    return rule.target
            elif rule.condition == CoverCropMappingConditions.NOT_IN:
                if cover_crop_type not in rule.value:
                    return rule.target
        return PracticeChange.cover_crops

    async def generate_practice(
        self,
        cultivation_cycles: list[CultivationCycle],
    ) -> None:
        await self.pre_process(cultivation_cycles=cultivation_cycles)
        practice_config = await self.get_practice_type_config(practice_type=PracticeTypes.COVER_CROP)
        for event_detail in practice_config.event_details:
            logger.info(f"Generating cover crop for {event_detail.event_type}")
            logger.info(event_detail)
            if event_detail.event_type == EventTypes.HISTORICAL:
                self.historical_practices = self.__event_generator(
                    historical_years=event_detail.historical_years,
                    event_detail_config=event_detail,
                )
