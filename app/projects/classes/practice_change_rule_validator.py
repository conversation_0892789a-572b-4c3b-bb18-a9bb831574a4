from typing import Iterable

from fastapi import Request

from phases import schema
from phases.db import get_stage_practice_change_rules
from phases.enums import PracticeRuleConditions, PracticeRuleTypes
from phases.schema import GroupSelectionRule, StageEligibilityConfigResp
from programs.enums import PracticeChangeGroup
from projects.eligibility.classes.eligibility_config import EligibilityConfigGenerator


class PracticeChangeRuleValidator:
    """
    A class to handle practice change rule validation with eligibility configuration.

    This class encapsulates the logic for getting practice change rules and validating
    all practice rules for a given stage, with the eligibility configuration as a property.
    """

    def __init__(self, request: Request, stage_id: int):
        """
        Initialize the PracticeChangeRuleValidator.

        Args:
            request: FastAPI request object
            stage_id: The stage ID to get rules for
        """
        self.request = request
        self.stage_id = stage_id
        self._eligibility_config: StageEligibilityConfigResp | None = None

    @property
    async def eligibility_config(self) -> StageEligibilityConfigResp | None:
        """
        Get the eligibility configuration for the stage.

        Returns:
            StageEligibilityConfigResp or None if no configuration exists
        """
        if self._eligibility_config is None:
            eligibility_config_generator = EligibilityConfigGenerator(request=self.request, stage_id=self.stage_id)
            self._eligibility_config = await eligibility_config_generator.get_eligibility_config()
        return self._eligibility_config

    async def get_practice_change_rules(
        self,
    ) -> list[schema.PracticeDependencyRule | schema.GroupSelectionRule | schema.EligibilityMatrixRule]:
        """
        Get the practice change rules for a stage, including:
        - Database-stored rules (PracticeDependencyRule)
        - Default group rules (GroupSelectionRule - MAX 1 practice selected per group)
        - Eligibility matrix rule (EligibilityMatrixRule - if eligibility matrix is configured)
        """
        rows = await get_stage_practice_change_rules(request=self.request, stage_id=self.stage_id)

        # default group rules, currently only for tillage and crop practices
        group_max_rules = {
            group: GroupSelectionRule(target=group, condition=PracticeRuleConditions.MAX, value=1)
            for group in [PracticeChangeGroup.tillage, PracticeChangeGroup.crop]
        }

        rules = []
        for row in rows:
            rule = row.rule
            # override default group rules if corresponding rule is present
            if (
                rule.type == PracticeRuleTypes.GROUP_PRACTICE_SELECTION
                and isinstance(rule, schema.GroupSelectionRule)
                and rule.condition == PracticeRuleConditions.MAX
            ):
                group_max_rules[PracticeChangeGroup(rule.target)] = rule
            else:
                rules.append(rule)

        rules.extend(group_max_rules.values())

        # Add EligibilityMatrixRule if eligibility matrix is configured for this stage
        eligibility_config = await self.eligibility_config

        if eligibility_config and eligibility_config.eligibility_matrix:
            eligibility_matrix_rule = schema.EligibilityMatrixRule(value=eligibility_config.eligibility_matrix)
            rules.append(eligibility_matrix_rule)

        return rules

    async def validate_all_practice_rules(
        self,
        assigned_practices: Iterable[str],
        baseline_practices: Iterable[str] | None = None,
    ) -> bool:
        """
        Validate all practice rules for this stage with appropriate parameters based on rule types.

        Args:
            assigned_practices: The assigned practices
            baseline_practices: The baseline practices (required for EligibilityMatrixRule)

        Returns:
            bool: True if all rules are satisfied

        Raises:
            ValueError: If baseline_practices is required for any EligibilityMatrixRule but not provided
        """
        rules = await self.get_practice_change_rules()

        for rule in rules:
            # Inline validation logic from validate_practice_rule
            if isinstance(rule, schema.EligibilityMatrixRule):
                if baseline_practices is None:
                    raise ValueError("EligibilityMatrixRule requires baseline_practices parameter")
                if not rule.is_valid(assigned_practices, baseline_practices):
                    return False
            else:
                # Other rules (PracticeDependencyRule, GroupSelectionRule)
                if not rule.is_valid(assigned_practices):
                    return False
        return True
