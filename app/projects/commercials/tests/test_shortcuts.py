import os
from datetime import datetime
from unittest.mock import patch

import pytest

from docusign.schema import FieldData
from fields.enums import EntityDataType, FieldDataState
from phases.enums import (
    AttributeTypes,
    CommercialsPaymentForType,
    PhaseTypes,
    StageTypes,
)
from programs.enums import PracticeChange, UnitsTypes
from projects.commercials.schema import PayPerPracticeCommercialRule
from projects.commercials.shortcuts import (
    create_pay_per_area_line_items,
    create_pay_per_practice_line_items,
    create_uncompleted_estimated_ghg_line_items,
    get_pay_per_practice_commercial_rules_for_program_from_csv,
    is_pay_per_area_contract_line_items_invalidated,
    is_pay_per_practice_contract_line_items_invalidated,
    submit_project_fields_to_explore_if_none_or_invalidated,
)
from projects.consts import TONS_CO2_UNIT
from projects.schema import ProjectContractLineItemsRequest
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration.schema import ExploreRequestBody
from scenarios_service.schema import DndcResultsOutputPrice


async def test_create_uncompleted_estimated_ghg_line_items(mdl, app_request, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.ASSIGN_PRACTICES, eligibility_method=None)
    field = await mdl.Fields(parent_project_id=project.id)
    dndc_task = await mdl.DndcTasks(
        program_id=program.id,
        project_id=project.id,
        phase=PhaseTypes.ENROLMENT,
        status=DndcTaskStatusChoices.finished,
        created_at=datetime.now(),
    )
    await mdl.DndcContractedResults(
        project_id=project.id,
        task_id=dndc_task.id,
        results=DndcResultsOutputPrice(
            field_count=1,
            fields_with_results={
                field.id: {"ghg": -1.0},
            },
            fields_without_results=[],
            fields_with_errors={},
            soc=1.0,
            n2o=1.0,
            ch4=1.0,
            ghg=1.0,
            indirect_n2o=1.0,
            total_n2o=1.0,
            ghg_soc=1.0,
            ghg_ch4=1.0,
            ghg_n2o=1.0,
            ghg_indirect_n2o=1.0,
            ghg_total_n2o=1.0,
            carbon_price_dollars=50,
            payment_dollars=50.0,
        ).to_dict(),
    )

    line_items = await create_uncompleted_estimated_ghg_line_items(
        request=app_request,
        project_id=project.id,
        fields_data={
            field.id: FieldData(
                farm_name="test_farm",
                field_area=100.0,
                field_id=field.id,
                field_name="test_field",
                field_region_id=field.core_region_id,
                attribute_values={},
            )
        },
        field_ids_to_skip=[],
        program_locale=program.locale,
    )
    expected_line_items = [
        ProjectContractLineItemsRequest(
            note={"farm_name": "test_farm", "field_name": "test_field"},
            value=None,
            field_id=field.id,
            field_area=100.0,
            quantity=1.0,
            units=TONS_CO2_UNIT,
        )
    ]
    assert line_items == expected_line_items


async def test_create_pay_per_area_line_items(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)
    commercial = await mdl.PhaseCommercials(
        phase_id=phase.id, payment_for=CommercialsPaymentForType.FIELD_AREA, payment=100
    )

    fields_data = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=1,
            attribute_values={},
        )
    }
    field_ids_to_skip = []

    line_items = await create_pay_per_area_line_items(
        request=app_request,
        program_units=UnitsTypes.METRIC,
        fields_data=fields_data,
        field_ids_to_skip=field_ids_to_skip,
        commercial=commercial,
        program_locale=program.locale,
    )
    assert line_items == [
        ProjectContractLineItemsRequest(
            note={"farm_name": "test_farm", "field_name": "test_field_1", "payout_per_hectare": 100},
            value=100.00,
            field_id=field_1.id,
            field_area=round(field_1.area, 2),
            units="hectares",
            quantity=0,
        ),
    ]


@patch("projects.commercials.shortcuts.get_pay_per_practice_commercial_rules_for_program_from_csv")
async def test_create_pay_per_practice_line_items(
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv, mdl, app_request
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)
    assign_practice_attr = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.practice, name="ASSIGN_PRACTICES"
    )
    targeted_region = await mdl.CoreRegions(name="targeted_region")
    non_targeted_region = await mdl.CoreRegions(name="non_targeted_region")
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)
    field_2 = await mdl.Fields(parent_project_id=project.id, area=2)

    mock_get_pay_per_practice_commercial_rules_for_program_from_csv.return_value = [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=targeted_region.name,
            payment=100,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=non_targeted_region.name,
            payment=90,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=targeted_region.name,
            payment=80,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=non_targeted_region.name,
            payment=70,
        ),
    ]

    fields_data = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
        field_2.id: FieldData(
            farm_name="test_farm",
            field_area=field_2.area,
            field_id=field_2.id,
            field_name="test_field_2",
            field_region_id=non_targeted_region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join([PracticeChange.cover_crops.value, PracticeChange.no_till.value])
            },
        ),
    }
    field_ids_to_skip = []

    line_items = await create_pay_per_practice_line_items(
        request=app_request,
        program_id=program.id,
        program_units=UnitsTypes.METRIC,
        fields_data=fields_data,
        field_ids_to_skip=field_ids_to_skip,
        program_locale=program.locale,
    )
    assert line_items == [
        ProjectContractLineItemsRequest(
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_hectare": 180,
                "field_region": "targeted_region",
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=180.00,
            field_id=field_1.id,
            field_area=round(field_1.area, 2),
            units="hectares",
            quantity=0,
        ),
        ProjectContractLineItemsRequest(
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_2",
                "payout_per_hectare": 160,
                "field_region": "non_targeted_region",
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value]
                ),
            },
            value=320.00,
            field_id=field_2.id,
            field_area=round(field_2.area, 2),
            units="hectares",
            quantity=0,
        ),
    ]


@patch("projects.commercials.shortcuts.get_pay_per_practice_commercial_rules_for_program_from_csv")
async def test_create_pay_per_practice_line_items_flat_payment(
    mock_get_pay_per_practice_commercial_rules_for_program_from_csv, mdl, app_request
):
    program = await mdl.Programs(id=1639)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)
    assign_practice_attr = await mdl.Attribute(
        parent_stage_id=stage.id, type=AttributeTypes.practice, name="ASSIGN_PRACTICES"
    )
    region = await mdl.CoreRegions()
    field_1 = await mdl.Fields(parent_project_id=project.id, area=1)
    field_2 = await mdl.Fields(parent_project_id=project.id, area=2)

    mock_get_pay_per_practice_commercial_rules_for_program_from_csv.return_value = [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region=region.name,
            payment=100,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region=region.name,
            payment=90,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.pollinator_hotspot,
            region=region.name,
            payment=0,
        ),
    ]

    fields_data = {
        field_1.id: FieldData(
            farm_name="test_farm",
            field_area=field_1.area,
            field_id=field_1.id,
            field_name="test_field_1",
            field_region_id=region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value, PracticeChange.pollinator_hotspot]
                )
            },
        ),
        field_2.id: FieldData(
            farm_name="test_farm",
            field_area=field_2.area,
            field_id=field_2.id,
            field_name="test_field_2",
            field_region_id=region.id,
            attribute_values={
                assign_practice_attr.name: ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value, PracticeChange.pollinator_hotspot]
                )
            },
        ),
    }
    field_ids_to_skip = []

    line_items = await create_pay_per_practice_line_items(
        request=app_request,
        program_id=program.id,
        program_units=UnitsTypes.METRIC,
        fields_data=fields_data,
        field_ids_to_skip=field_ids_to_skip,
        program_locale=program.locale,
    )
    assert line_items == [
        ProjectContractLineItemsRequest(
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_1",
                "payout_per_hectare": 190.00,
                "field_region": region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value, PracticeChange.pollinator_hotspot]
                ),
            },
            value=440.00,
            field_id=field_1.id,
            field_area=round(field_1.area, 2),
            units="hectares",
            quantity=0,
        ),
        ProjectContractLineItemsRequest(
            note={
                "farm_name": "test_farm",
                "field_name": "test_field_2",
                "payout_per_hectare": 190.00,
                "field_region": region.name,
                f"{assign_practice_attr.name}": ", ".join(
                    [PracticeChange.cover_crops.value, PracticeChange.no_till.value, PracticeChange.pollinator_hotspot]
                ),
            },
            value=380.00,
            field_id=field_2.id,
            field_area=round(field_2.area, 2),
            units="hectares",
            quantity=0,
        ),
    ]


@patch("projects.commercials.shortcuts.PAY_PER_PRACTICE_CSV_DIRNAME", os.path.dirname(__file__))
async def test_get_commercial_rules_for_program_from_csv():
    commercial_rules = get_pay_per_practice_commercial_rules_for_program_from_csv(program_id=1)
    assert commercial_rules == [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region="California",
            payment=100,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region="Washington",
            payment=90,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.reduced_till,
            region="California",
            payment=80,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.reduced_till,
            region="Washington",
            payment=70,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.conventional_till,
            region="California",
            payment=60,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.conventional_till,
            region="Washington",
            payment=50,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region="California",
            payment=40,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region="Washington",
            payment=30,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_cover_crop,
            region="California",
            payment=20,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_cover_crop,
            region="Washington",
            payment=10,
        ),
    ]

    commercial_rules = get_pay_per_practice_commercial_rules_for_program_from_csv(program_id=2)
    assert commercial_rules == [
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region="Nevada",
            payment=100,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_till,
            region="Utah",
            payment=90,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.reduced_till,
            region="Nevada",
            payment=80,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.reduced_till,
            region="Utah",
            payment=70,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.conventional_till,
            region="Nevada",
            payment=60,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.conventional_till,
            region="Utah",
            payment=50,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region="Nevada",
            payment=40,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.cover_crops,
            region="Utah",
            payment=30,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_cover_crop,
            region="Nevada",
            payment=20,
        ),
        PayPerPracticeCommercialRule(
            practice_change=PracticeChange.no_cover_crop,
            region="Utah",
            payment=10,
        ),
    ]


@pytest.mark.parametrize(
    "contract_line_item_created_at, field_updated_at, expected",
    (
        (datetime(2025, 1, 1), datetime(2025, 1, 2), True),
        (datetime(2025, 1, 1), datetime(2025, 1, 1), True),
        (datetime(2025, 1, 2), datetime(2025, 1, 1), False),
    ),
)
async def test_is_pay_per_area_contract_line_items_invalidated(
    mdl, app_request, contract_line_item_created_at, field_updated_at, expected
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, updated_at=field_updated_at)
    contract = await mdl.ProjectContracts(project=project.id, phase_id=phase.id)
    await mdl.ProjectContractLineItems(
        contract_id=contract.id,
        field_id=field.id,
        field_area=1,
        value=0,
        quantity=0,
        created_at=contract_line_item_created_at,
    )
    is_invalidated = await is_pay_per_area_contract_line_items_invalidated(request=app_request, project_id=project.id)
    assert is_invalidated is expected


@pytest.mark.parametrize(
    "contract_line_item_created_at, field_fact_created_at, field_updated_at, expected",
    (
        (datetime(2025, 1, 1), datetime(2025, 1, 2), datetime(2024, 12, 31), True),
        (datetime(2025, 1, 1), datetime(2025, 1, 1), datetime(2024, 12, 31), True),
        (datetime(2025, 1, 1), datetime(2024, 12, 31), datetime(2025, 1, 2), True),
        (datetime(2025, 1, 1), datetime(2024, 12, 31), datetime(2025, 1, 1), True),
        (datetime(2025, 1, 2), datetime(2025, 1, 1), datetime(2024, 12, 31), False),
    ),
)
async def test_is_pay_per_practice_contract_line_items_invalidated(
    mdl, app_request, contract_line_item_created_at, field_fact_created_at, field_updated_at, expected
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, updated_at=field_updated_at)
    contract = await mdl.ProjectContracts(project=project.id, phase_id=phase.id)
    await mdl.ProjectContractLineItems(
        contract_id=contract.id,
        field_id=field.id,
        field_area=1,
        value=0,
        quantity=0,
        created_at=contract_line_item_created_at,
    )
    await mdl.FieldFacts(
        field_id=field.id,
        facts=[],
        data_type=EntityDataType.entity_practices,
        state=FieldDataState.assigned,
        created_at=field_fact_created_at,
    )
    is_invalidated = await is_pay_per_practice_contract_line_items_invalidated(
        request=app_request,
        project_id=project.id,
    )
    assert is_invalidated is expected


@patch("projects.commercials.shortcuts.submit_project_fields_to_explore_task")
async def test_submit_project_fields_to_explore_if_none_or_invalidated(
    mock_submit_project_fields_to_explore_task, mdl, app_request
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    await submit_project_fields_to_explore_if_none_or_invalidated(
        request=app_request, project_id=project.id, field_ids=[field.id]
    )
    mock_submit_project_fields_to_explore_task.delay.assert_called_with(
        body=ExploreRequestBody(
            project_id=project.id,
            field_ids=[field.id],
        ),
        fs_user_id=app_request.state.fs_user_id,
        fs_impersonator_user_id=app_request.state.fs_impersonator_user_id,
    )
