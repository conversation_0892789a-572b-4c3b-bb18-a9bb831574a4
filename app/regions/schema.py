import enum
import json
from typing import Any

from pydantic import BaseModel, Field, root_validator, validator

from helper.custom_typing import CustomMultiPolygon
from logger import get_logger
from regions import model

logger = get_logger(__name__)


class RegionSchema(BaseModel):
    id: int | None = None
    level: model.LevelTypes = model.LevelTypes.CUSTOM
    country: str | None = None

    name: str
    geom: CustomMultiPolygon

    class Config:
        orm_mode = True

    @classmethod
    def _convert_geom_from_str(cls, value: str) -> CustomMultiPolygon:
        if isinstance(value, str):
            try:
                value = json.loads(value)
            except json.JSONDecodeError:
                raise ValueError("geom value is not valid json")
        return value

    @validator("geom", pre=True)
    def convert_geom_from_str(cls, value: str) -> CustomMultiPolygon:
        return cls._convert_geom_from_str(value)


class RegionSchemaSimplified(RegionSchema):
    # alias='geom_simplified' is used to get data from column "geom_simplified" in DB
    # then "geom_simplified" is renamed to "geom"
    # this behaviour is achieved in router.py in Response by using ".json(by_alias=False)"
    geom: CustomMultiPolygon | None = Field(alias="geom_simplified")

    # original_geom is used to get original "geom" data from DB
    # original_geom is used when there is no "geom_simplified" data in DB
    # original_geom is excluded from API response
    original_geom: CustomMultiPolygon | None = Field(alias="geom", exclude=True)

    @validator("original_geom", pre=True)
    def convert_original_geom_from_str(cls, value: str) -> CustomMultiPolygon:
        return cls._convert_geom_from_str(value)

    @root_validator
    def use_geom_simplified_or_geom(cls, values: dict[str, Any]) -> dict[str, Any]:
        if not values["geom"]:
            values["geom"] = values["original_geom"]
        return values


class RegionSchemaProgram(RegionSchema):
    program_id: int


class GeomSearchAlgorithmType(enum.StrEnum):
    # geom must contain searched areas (geom is bigger than searched areas)
    GEOM_CONTAINS = "GEOM_CONTAINS"
    # geom must be in searched areas (result areas contain geom)
    GEOM_IN = "GEOM_IN"


class RegionFilter(BaseModel):
    ids: list[int] | None = None
    country: str | None = None
    level: model.LevelTypes | None = None
    name: str | None = None
    geom: dict | None = None
    geom_search_algorithm: GeomSearchAlgorithmType = GeomSearchAlgorithmType.GEOM_CONTAINS


class RegionFilterProgram(RegionFilter):
    program_id: int | None = None


class RegionName(BaseModel):
    id: int
    level: model.LevelTypes
    name: str
    country: str

    class Config:
        orm_mode = True
