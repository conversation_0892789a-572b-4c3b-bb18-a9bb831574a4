from sqlalchemy import select
from starlette.requests import Request

from helper.helper import run_query
from projects.eligibility.model import LUT


async def lookup_in_lut(
    request: Request,
    year: int,
    region: str,
    baseline_tillage: str,
    baseline_cover_crop: str,
    practice_change: str,
    crop: str | None = None,
    cfg: str | None = None,
    crop_season: str | None = None,
    # as of 2025, there is only this one scenario in the table, we could drop it at some point
    scenario: str = "s1",
) -> LUT | None:
    query = (
        select(LUT)
        .where(LUT.year == year)
        .where(LUT.scenario == scenario)
        .where(LUT.region == region)
        .where(LUT.baseline_tillage == baseline_tillage)
        .where(LUT.baseline_cover_crop == baseline_cover_crop)
        .where(LUT.practice_change == practice_change)
        .order_by(LUT.ghg)
    )

    if crop:
        query = query.where(LUT.crop == crop)
    else:
        query = query.where(LUT.cfg == cfg)

    if crop_season:
        query = query.where(LUT.crop_season == crop_season)

    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).scalar()
