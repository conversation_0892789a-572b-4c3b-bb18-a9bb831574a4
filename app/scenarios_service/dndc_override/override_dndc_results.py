from __future__ import annotations

from typing import <PERSON>ple

from starlette.requests import Request

from config import get_dndc_override
from fields.db import get_field_facts_for_fields, get_fields_by_project_id
from fields.enums import EntityDataType, FieldDataState
from fields.schema import Field
from logger import get_logger
from phases.enums import EligibilityTypes, PhaseTypes, StageTypes
from phases.methods import get_eligibility_type, get_stages
from phases.schema import StageWithEligibilityPhaseEntityType
from programs.constants import COVER_CROP_PRACTICE_CHANGES
from programs.db import get_program_id_by_project_id, get_program_reporting_period
from programs.enums import PracticeChange, PracticeChangeGroup
from projects.eligibility.constants import EU_2025_REGION_MAPPING
from projects.eligibility.enums import (
    CoverCropType,
    EURegions,
    TillageIntensity,
)
from projects.eligibility.helper import (
    get_cover_crop_intensity,
    get_tillage_intensity,
)
from projects.enums import AmbiguousCrops
from scenarios_service.dndc_override.db import lookup_in_lut
from scenarios_service.enums import LutUsageReasons
from scenarios_service.generalized_integration.db import (
    get_program_modeling_configuration,
)
from scenarios_service.generalized_integration.fetch_events_for_simulations import (
    get_intended_commodity_crops,
)
from scenarios_service.schema import DndcResultsOutputPrice

logger = get_logger(__name__)

# mappings from the int value to what is found in the LUT
LUT_TILLAGE_MAPPING = {
    TillageIntensity.NO_TILL: "no till",
    TillageIntensity.REDUCED_TILL: "reduced till",
    TillageIntensity.CONVENTIONAL_TILL: "conventional till",
}

LUT_COVER_CROP_MAPPING = {
    CoverCropType.NO_COVER_CROP: "no cover crop",
    CoverCropType.BASIC_COVER_CROP: "basic cover crop",
    CoverCropType.PREMIUM_COVER_CROP: "premium cover crop",
}

# regrow crop name map to crop name in LUT
LUT_INTENDED_CROP_MAPPING = {
    "sunflowers": "sunflower",
    "wheat_winter": "winter_wheat",
    "wheat_spring": "spring_wheat",
    "sugar_beets": "sugarbeet",
    "soybeans": "soybean",
    "winter_barley": "barley",
    "spring_barley": "barley",
    "potatoes": "potato",
}

# regrow crop name to crop functional group in LUT
LUT_CROP_CFG_MAPPING = {
    "alfalfa": "C3PN",
    "winter_barley": "C3A",
    "spring_barley": "C3A",
    "barley": "C3A",
    "buckwheat": "C3A",
    "canola": "C3A",
    "corn": "C4A",
    "dry_beans": "C3A",
    "faba_beans": "C3AN",
    "flax": "C3A",
    "lentils": "C3AN",
    "millet": "C4A",
    "oats": "C3A",
    "peas": "C3AN",
    "potatoes": "C3A",
    "rye": "C3A",
    "sorghum": "C4A",
    "soybeans": "C3AN",
    "sugar_beets": "C3A",
    "turnip": "C3A",
    "vetch": "C3AN",
    "wheat_winter": "C3A",
    "wheat_spring": "C3A",
    "sunflowers": "C3A",
    "clover": "C3A",
    "peanuts": "C3AN",
    "pumpkins": "C3A",
    "radishes": "C3A",
    "triticale": "C3A",
    "beans": "C3A",
    "mungbeans": "C3A",
    "chickpeas": "C3A",
}


async def override_dndc_results(
    results_without_overrides: DndcResultsOutputPrice,
    field_ids: set[int],
    field_areas: dict[int, float],
    field_fs_field_map: dict[int, int],
    project_id: int,
    request: Request,
) -> DndcResultsOutputPrice:
    program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
    if program_id == 1119:
        # Cargill Regen Connect 2024 (US) - once this is complete we can remove this conditional and collapse this
        # method with get_dndc_override_results below
        from scenarios_service.program_1119.methods import (
            get_dndc_override_results as get_dndc_override_results_1119,
        )

        results = await get_dndc_override_results_1119(
            field_ids=field_ids,
            fields_with_results=results_without_overrides.fields_with_results,
            field_areas=field_areas,
            fields_with_errors=results_without_overrides.fields_with_errors,
            fields_without_results=results_without_overrides.fields_without_results,
            carbon_price_dollars=results_without_overrides.carbon_price_dollars,
            program_id=program_id,
            project_id=project_id,
            request=request,
            field_fs_field_map=field_fs_field_map,
        )
    else:
        results = await get_dndc_override_results(
            field_ids=field_ids,
            fields_with_results=results_without_overrides.fields_with_results,
            field_areas=field_areas,
            fields_without_results=results_without_overrides.fields_without_results,
            carbon_price_dollars=results_without_overrides.carbon_price_dollars,
            program_id=program_id,
            project_id=project_id,
            request=request,
        )
    return results


async def get_dndc_override_results(
    field_ids: set[int],
    fields_with_results: dict,
    field_areas: dict[int, float],
    fields_without_results: list[int],
    carbon_price_dollars: int,
    program_id: int,
    project_id: int,
    request: Request,
) -> DndcResultsOutputPrice:
    fields_with_errors: dict[int, dict] = {}
    await _apply_lut_override_to_dndc_results(
        [fid for fid in field_ids if fid not in fields_with_results or "ghg" not in fields_with_results[fid]],
        fields_with_results,
        fields_with_errors,
        field_areas,
        fields_without_results,
        program_id,
        project_id,
        request,
    )

    # fields_with_results will now contain the fields populated from LUT
    await _apply_bounds_to_dndc_results(fields_with_results, field_areas, program_id, request)

    ghg_delta_all_fields = sum(field["ghg"] for field in fields_with_results.values())

    return DndcResultsOutputPrice(
        field_count=len(field_ids),
        fields_with_results=fields_with_results,
        fields_without_results=[],
        fields_with_errors=fields_with_errors,
        carbon_price_dollars=carbon_price_dollars,
        payment_dollars=-round(ghg_delta_all_fields * carbon_price_dollars, 2),
        ghg=round(ghg_delta_all_fields, 2),
        soc=-1,
        n2o=-1,
        ch4=-1,
        indirect_n2o=-1,
        total_n2o=-1,
        ghg_soc=-1,
        ghg_ch4=-1,
        ghg_n2o=-1,
        ghg_indirect_n2o=-1,
        ghg_total_n2o=-1,
    )


async def _apply_bounds_to_dndc_results(
    fields_with_results: dict,
    field_areas: dict[int, float],
    program_id: int,
    request: Request,
) -> None:
    modeling_config = await get_program_modeling_configuration(request, program_id)

    for field_id, result in fields_with_results.items():
        logger.debug(field_id)
        ghg_for_field: float = result["ghg"]
        logger.debug("%s ghg", ghg_for_field)
        area = field_areas[field_id]

        override = result.get("override", False)
        existing_lut_usage_reason = result.get("lut_usage_reason", None)
        lut_usage_reason = None

        ghg_per_acre = result.get("multiplier", (ghg_for_field / area) * -1)
        if modeling_config.apply_ghg_per_acre_bounds:
            # Apply the upper bound
            if ghg_per_acre > 2:
                ghg_per_acre = 2.0
                ghg_for_field = -1 * ghg_per_acre * area
                override = True
                lut_usage_reason = LutUsageReasons.ghg_too_high
            # Apply the lower bound of 0 ton GHG/acre, bounding any negative reductions to 0
            elif ghg_per_acre <= 0:
                ghg_per_acre = 0.0
                ghg_for_field = -1 * ghg_per_acre * area
                override = True
                lut_usage_reason = LutUsageReasons.ghg_too_low

        if existing_lut_usage_reason and lut_usage_reason:
            lut_usage_reason = f"{existing_lut_usage_reason}, {lut_usage_reason}"
        elif existing_lut_usage_reason:
            lut_usage_reason = existing_lut_usage_reason

        logger.debug(
            "case: program: %s, multiplier: %s\nghg: %s\narea_ac:%s",
            program_id,
            ghg_per_acre,
            ghg_for_field,
            area,
        )

        fields_with_results[field_id] = {
            **result,
            "lut_usage_reason": lut_usage_reason,
            "ghg": round(ghg_for_field, 2),
            "multiplier": round(ghg_per_acre, 2),
            "override": override,
        }


async def _apply_lut_override_to_dndc_results(
    field_ids: list[int],
    fields_with_results: dict,
    fields_with_errors: dict,
    field_areas: dict[int, float],
    fields_without_results: list[int],
    program_id: int,
    project_id: int,
    request: Request,
) -> None:
    if not field_ids:
        return

    eligibility_details = await _get_eligibility_details(request, project_id, PhaseTypes.ENROLMENT)
    if (
        eligibility_details is not None
        and eligibility_details.eligibility_method == EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE
    ):
        raise ValueError(
            "Eligibility method is always true, so no baseline practices have been derived and therefore cannot proceed with LUT lookup"
        )

    baseline_practices_for_fields = await get_field_facts_for_fields(
        request=request,
        field_ids=list(field_ids),
        state=FieldDataState.baseline,
        data_type=EntityDataType.entity_practices,
        fact_transformer=lambda facts: {PracticeChange(practice) for practice in facts},
    )
    assigned_practices_for_fields = await get_field_facts_for_fields(
        request=request,
        field_ids=list(field_ids),
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
        fact_transformer=lambda facts: {PracticeChange(practice) for practice in facts},
    )

    # if an EU program (need better way to determine)
    is_eu_program = 1639 <= program_id <= 1644

    if is_eu_program:
        fields_by_id = {
            field.id: field
            for field in await get_fields_by_project_id(request=request, project_id=project_id, field_ids=field_ids)
        }
    else:
        fields_by_id = {}

    reporting_year = (await get_program_reporting_period(request, program_id)).end.year

    for field_id in field_ids:
        logger.debug(field_id)
        area = field_areas[field_id]

        # if ghg is None, we need to use the default LUT
        if field_id in fields_without_results:
            logger.exception(
                "Contracted results was called before all results returned from Explore API.",
                extra={"field_id": field_id},
            )
            lut_usage_reason = LutUsageReasons.explore_result_missing
        else:  # An error occurred
            lut_usage_reason = LutUsageReasons.generic_error

        base_tillage = _get_tillage_practice_for_field(baseline_practices_for_fields.get(field_id, []))
        base_cover_crop = _get_cover_crop_practice_for_field(baseline_practices_for_fields.get(field_id, []))
        new_tillage = _get_tillage_practice_for_field(
            assigned_practices_for_fields.get(field_id, []), default=base_tillage
        )
        new_cover_crop = _get_cover_crop_practice_for_field(
            assigned_practices_for_fields.get(field_id, []), default=base_cover_crop
        )

        try:
            if is_eu_program:
                ghg_per_acre, lut_case = await _get_ghg_from_dndc_lookup_table(
                    program_id=program_id,
                    field=fields_by_id[field_id],
                    reporting_year=reporting_year,
                    base_tillage=base_tillage,
                    base_cover_crop=base_cover_crop,
                    new_tillage=new_tillage,
                    new_cover_crop=new_cover_crop,
                    request=request,
                )
            else:
                ghg_per_acre, lut_case = await _get_ghg_from_dndc_override_file(
                    program_id=program_id,
                    base_tillage=base_tillage,
                    base_cover_crop=base_cover_crop,
                    new_tillage=new_tillage,
                    new_cover_crop=new_cover_crop,
                )
            ghg_for_field = area * ghg_per_acre * -1

            logger.debug(
                "case: program: %s, %s\nmultipler: %s\nghg: %s\narea_ac:%s",
                program_id,
                lut_case,
                ghg_per_acre,
                ghg_for_field,
                area,
            )
            fields_with_results[field_id] = {
                "ghg": round(ghg_for_field, 2),
                "multiplier": round(ghg_per_acre, 2),
                "override": True,
                "case": lut_case,
                "lut_usage_reason": lut_usage_reason,
            }
        except Exception as e:
            fields_with_errors[field_id] = {"error": f"Could not override DNDC results: {e}", "error_code": None}


async def _get_ghg_from_dndc_override_file(
    program_id: int,
    base_tillage: TillageIntensity,
    base_cover_crop: CoverCropType,
    new_tillage: TillageIntensity,
    new_cover_crop: CoverCropType,
) -> tuple[float, str]:
    dndc_override: dict = get_dndc_override()
    if dndc_override.get(str(program_id)) is None:
        # Uses the default (ie clone of program 68) LUT when
        # program specific one is not available.
        # This keeps the tests working. This may need to change
        # in the future.
        program_id = -1

    base_tillage = _tillage_intensity_to_dndc_override_key(base_tillage)
    base_cover_crop = _cover_crop_type_to_dndc_override_key(base_cover_crop)
    new_tillage = _tillage_intensity_to_dndc_override_key(new_tillage)
    new_cover_crop = _cover_crop_type_to_dndc_override_key(new_cover_crop)
    try:
        ghg = dndc_override[str(program_id)][base_tillage][base_cover_crop][new_tillage][new_cover_crop]
    except KeyError:
        logger.exception(
            "Failed lookup with KeyError: %s-%s-%s-%s-%s",
            program_id,
            base_tillage,
            base_cover_crop,
            new_tillage,
            new_cover_crop,
        )
        ghg = 0.0
    lut_case = f"{program_id}-{base_tillage}-{base_cover_crop}-{new_tillage}-{new_cover_crop}"

    return ghg, lut_case


def _tillage_intensity_to_dndc_override_key(tillage: TillageIntensity) -> str:
    if tillage == TillageIntensity.CONVENTIONAL_TILL:
        return "conventional till"
    if tillage == TillageIntensity.REDUCED_TILL:
        return "reduced till"
    return "no till"


def _cover_crop_type_to_dndc_override_key(cover_crop: CoverCropType) -> str:
    if cover_crop == CoverCropType.NO_COVER_CROP:
        return "no cover"
    return "cover crops"


async def _get_ghg_from_dndc_lookup_table(
    program_id: int,
    field: Field,
    reporting_year: int,
    base_tillage: TillageIntensity,
    base_cover_crop: CoverCropType,
    new_tillage: TillageIntensity,
    new_cover_crop: CoverCropType,
    request: Request,
) -> tuple[float, str]:
    if program_id not in EU_2025_REGION_MAPPING:
        raise ValueError(f"No region mapping for EU program {program_id}")
    region = EU_2025_REGION_MAPPING[program_id]
    (intended_crop, crop_season) = await get_intended_crop_and_season(request, field)
    ghg = await lookup_ghg(
        request=request,
        region=region,
        intended_crop=intended_crop,
        crop_season=crop_season,
        baseline_tillage=base_tillage,
        baseline_cover_crop=base_cover_crop,
        new_tillage=new_tillage,
        new_cover_crop=new_cover_crop,
        reporting_year=reporting_year,
    )

    season_str = "" if crop_season is None else f"{crop_season}-"
    lut_case = f"{program_id}-{region}-{reporting_year}-{season_str}{intended_crop}-{base_tillage.name.lower()}-{base_cover_crop.name.lower()}-{new_tillage.name.lower()}-{new_cover_crop.name.lower()}"

    return ghg, lut_case


async def lookup_ghg(
    request: Request,
    region: EURegions,
    intended_crop: str,
    crop_season: str | None,
    baseline_tillage: TillageIntensity,
    baseline_cover_crop: CoverCropType,
    new_tillage: TillageIntensity,
    new_cover_crop: CoverCropType,
    reporting_year: int,
) -> float:
    if new_tillage not in LUT_TILLAGE_MAPPING:
        raise ValueError(f"Invalid tillage for lut: {new_tillage}")
    if new_cover_crop not in LUT_COVER_CROP_MAPPING:
        raise ValueError(f"Invalid cover crop for lut: {new_cover_crop}")

    # lookup table has different crop type with types we receive
    crop_lut_value = LUT_INTENDED_CROP_MAPPING.get(intended_crop, intended_crop)
    baseline_tillage_lut_value = LUT_TILLAGE_MAPPING[baseline_tillage]
    cover_crop_lut_value = LUT_COVER_CROP_MAPPING[baseline_cover_crop]
    practice_change = (
        LUT_TILLAGE_MAPPING[new_tillage] + " + " + LUT_COVER_CROP_MAPPING[new_cover_crop].replace(" crop", "")
    )
    year = reporting_year - 1

    lut_record = await lookup_in_lut(
        request=request,
        year=year,
        region=region,
        crop=crop_lut_value,
        baseline_tillage=baseline_tillage_lut_value,
        baseline_cover_crop=cover_crop_lut_value,
        practice_change=practice_change,
        crop_season=crop_season,
    )
    # if cannot find crop in LUT, try to use cfg to query LUT
    if lut_record is None:
        cfg = LUT_CROP_CFG_MAPPING[intended_crop]
        lut_record = await lookup_in_lut(
            request=request,
            year=year,
            region=region,
            cfg=cfg,
            baseline_tillage=baseline_tillage_lut_value,
            baseline_cover_crop=cover_crop_lut_value,
            practice_change=practice_change,
        )

        # send message if crop still cannot be found in LUT with cfg
        if lut_record is None:
            lut_miss_data = {
                "project": request.path_params.get("project_id", "missing"),
                "crop": crop_lut_value,
                "cfg": cfg,
                "cover_crop": cover_crop_lut_value,
                "crop_season": crop_season,
                "practice_chg": practice_change,
                "region": region,
                "tillage": baseline_tillage_lut_value,
            }
            logger.error(f"LUT miss for: {lut_miss_data}")
            raise ValueError("Field parameters incorrect, LUT record missing, please contact support")
    return float(lut_record.ghg)


async def get_intended_crop_and_season(request: Request, field: Field) -> Tuple[str, str | None]:
    intended_crops = await get_intended_commodity_crops(request, field)
    intended_crop_type = None
    season = None
    for intended_crop in intended_crops:
        intended_crop_type = intended_crop.crop_type
        # barley is the only crop that need to be queried with crop_season in the LUT
        if intended_crop_type == AmbiguousCrops.BARLEY:
            planting_date = intended_crop.interval.start
            season = "spring" if planting_date.month <= 6 else "winter"
    if intended_crop_type is None:
        raise ValueError("No intended crop found")
    return intended_crop_type, season


def _get_tillage_practice_for_field(
    practices_for_field: list[set[PracticeChange]], default: TillageIntensity = TillageIntensity.NO_TILL
) -> TillageIntensity:
    """
    This will return the most intense tillage practice for a field, if any. Note that eligibility will now store only
    one value for type of practice change in mrv_field_facts, so for the most part there will be only one or zero
    tillage practices
    """
    tillage = TillageIntensity.UNKNOWN

    for practices in practices_for_field:
        for practice in practices:
            if practice.group == PracticeChangeGroup.tillage:
                cycle_tillage_practice = get_tillage_intensity(practice)
                if cycle_tillage_practice > tillage:
                    tillage = cycle_tillage_practice
    return default if tillage == TillageIntensity.UNKNOWN else tillage


def _get_cover_crop_practice_for_field(
    practices_for_field: list[set[PracticeChange]], default: CoverCropType = CoverCropType.NO_COVER_CROP
) -> CoverCropType:
    """
    This will return the most intense cover crop practice for a field, if any. Note that eligibility will now store only
    one value for type of practice change in mrv_field_facts, so for the most part there will be only one or zero
    cover crop practices
    """
    cover_crop = CoverCropType.UNKNOWN

    for practices in practices_for_field:
        for practice in practices:
            if practice in COVER_CROP_PRACTICE_CHANGES:
                # wheat winter will not be automatically treated as no cover going forward
                cycle_cover_crop_practice = get_cover_crop_intensity(practice, wheat_winter_as_no_cover=False)
                if cycle_cover_crop_practice > cover_crop:
                    cover_crop = cycle_cover_crop_practice

    return default if cover_crop == CoverCropType.UNKNOWN else cover_crop


async def _get_eligibility_details(
    request: Request, project_id: int, phase_type: PhaseTypes
) -> StageWithEligibilityPhaseEntityType | None:
    stages = await get_stages(request=request, project_id=project_id, stage_type=StageTypes.ASSIGN_PRACTICES)
    assign_practices_stage = next((stage for stage in stages if stage.phase_type == phase_type), None)
    if assign_practices_stage is None:
        return None

    return await get_eligibility_type(request=request, stage_id=assign_practices_stage.id)
