import random
from decimal import Decimal
from typing import Final
from unittest.mock import patch

import pytest
from fastapi import Request

from defaults.consts import RegrowCropName
from defaults.tests.mock_defaults_translator import (
    mock_translate_core_crop_name_to_regrow_name,
)
from fields.enums import EntityDataType, FieldDataState
from fields.model import FieldFacts
from helper import (
    crop_type_selections,
    crop_type_selections_mocked,
    external_api,
    external_api_mocked_responses,
)
from logger import get_logger
from phases.db import get_stage_by_id
from phases.enums import AttributeTypes, EligibilityTypes, PhaseTypes, StageTypes
from phases.methods import create_attributes, update_attribute, update_stage
from phases.schema import (
    AttributeRequest,
    AttributeRequestUpdate,
    PhaseResponse,
    StageRequestNoParentUpdate,
)
from phases.tests.test_async_phases import (
    create_field,
    create_program,
    create_program_enrolment,
    create_project,
    create_value,
)
from programs.enums import PracticeChange
from projects.eligibility.methods import create_multiple_field_facts
from scenarios_service.dndc_override.override_dndc_results import (
    get_dndc_override_results,
)
from scenarios_service.enums import LutUsageReasons

logger = get_logger(__name__)

CARBON_PRICE: Final[int] = 10
EU_PROGRAM_ID: Final[int] = 1643

base_expectation = {
    "field_count": 1,
    "fields_with_results": {},
    "fields_without_results": [],
    "fields_with_errors": {},
    "soc": -1.0,
    "n2o": -1.0,
    "ch4": -1.0,
    "indirect_n2o": -1.0,
    "total_n2o": -1.0,
    "ghg_soc": -1.0,
    "ghg_ch4": -1.0,
    "ghg_n2o": -1.0,
    "ghg_indirect_n2o": -1.0,
    "ghg_total_n2o": -1.0,
    "carbon_price_dollars": 10,
}


@pytest.fixture(scope="function")
async def lookup_table(mdl):
    # setup database LUT for EU
    await mdl.LUT(
        year=2025,
        scenario="s1",
        region="Hungary",
        crop="corn",
        cfg="C4A",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="no cover crop",
        practice_change="no till + basic cover",
        ghg=Decimal("0.142"),
    )
    await mdl.LUT(
        year=2025,
        scenario="s1",
        region="Hungary",
        crop="barley",
        cfg="C3A",
        crop_season="spring",
        baseline_tillage="conventional till",
        baseline_cover_crop="no cover crop",
        practice_change="no till + basic cover",
        ghg=Decimal("0.232"),
    )
    await mdl.LUT(
        year=2025,
        scenario="s1",
        region="Hungary",
        crop="barley",
        cfg="C3A",
        crop_season="winter",
        baseline_tillage="conventional till",
        baseline_cover_crop="no cover crop",
        practice_change="no till + basic cover",
        ghg=Decimal("0.354"),
    )
    await mdl.LUT(
        year=2025,
        scenario="s1",
        region="Hungary",
        crop="canola",
        cfg="C3AN",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="no cover crop",
        practice_change="no till + basic cover",
        ghg=Decimal("3.2"),
    )
    await mdl.LUT(
        year=2025,
        scenario="s1",
        region="Hungary",
        crop="alfalfa-like",
        cfg="C3PN",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="no cover crop",
        practice_change="no till + basic cover",
        ghg=Decimal("0.481"),
    )


@patch.object(
    crop_type_selections,
    "get_summer_crops",
    crop_type_selections_mocked.mocked_get_summer_crops,
)
@patch.object(
    crop_type_selections,
    "get_winter_crops",
    crop_type_selections_mocked.mocked_get_winter_crops,
)
@patch.object(
    external_api,
    "core_crops_get_api",
    external_api_mocked_responses.mocked_core_crops_get_api,
)
@patch.object(
    external_api,
    "core_field_area_get_api",
    external_api_mocked_responses.mocked_core_field_area_get_api,
)
@pytest.mark.parametrize(
    "input_, expected",
    (
        (  # Index 0 - 1 field, cover and tillage practices assigned: no LUT usage
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [{"ghg": -0.3}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -0.3,
                "payment_dollars": 3,
            },
        ),
        (  # Index 1 - 1 field, no tillage practice assigned: use baseline tillage: no LUT usage
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops,
                ],
                "fields_with_results": [{"ghg": -0.3}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -0.3,
                "payment_dollars": 3,
            },
        ),
        (  # Index 2 - 1 field, no cover crop practice assigned: use baseline cover crop: no LUT usage
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.reduced_till,
                ],
                "fields_with_results": [{"ghg": -0.3}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -0.3,
                "payment_dollars": 3,
            },
        ),
        (  # Index 3 - 1 field, ghg multiplied by area: no LUT usage
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [PracticeChange.reduced_till, PracticeChange.cover_crops],
                "fields_with_results": [{"ghg": -0.75}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [2.5],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -0.75,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -0.75,
                "payment_dollars": 7.5,
            },
        ),
        (  # Index 4 - 1 field, cover and tillage practices assigned: lower bound enforced
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": {
                    0: {"ghg": 0.3},
                },
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": 0,
                        "lut_usage_reason": "GHG too low",
                        "multiplier": 0,
                        "override": True,
                    },
                ],
                "ghg": 0,
                "payment_dollars": 0,
            },
        ),
        (  # Index 5 - 1 field, cover and tillage practices assigned: upper bound enforced
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [{"ghg": -2.1}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -2,
                        "lut_usage_reason": "GHG too high",
                        "multiplier": 2,
                        "override": True,
                    },
                ],
                "ghg": -2,
                "payment_dollars": 20,
            },
        ),
        (  # Index 6 - 1 field, cover and tillage practices assigned: upper bound not enforced because of size of field
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [{"ghg": -2.1}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [2],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -2.1,
                        "lut_usage_reason": None,
                        "multiplier": 1.05,
                        "override": False,
                    },
                ],
                "ghg": -2.1,
                "payment_dollars": 21,
            },
        ),
        (  # Index 7 - 2 fields, cover and tillage practices assigned: total is summ of all fields: no LUT usage
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [
                    {"ghg": -0.3},
                    {"ghg": -1.2},
                ],
                "fields_without_results": [None, None],
                "fields_with_errors": [None, None],
                "field_areas": [1, 1],
            },
            {
                **base_expectation,
                "field_count": 2,
                "fields_with_results": [
                    {
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                    {
                        "ghg": -1.2,
                        "multiplier": 1.2,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -1.5,
                "payment_dollars": 15,
            },
        ),
        (  # Index 8 - 2 fields, cover and tillage practices assigned: upper bound not enforced because of # of fields
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [
                    {"ghg": -1},
                    {"ghg": -1.1},
                ],
                "fields_without_results": [None, None],
                "fields_with_errors": [None, None],
                "field_areas": [1, 1],
            },
            {
                **base_expectation,
                "field_count": 2,
                "fields_with_results": [
                    {
                        "ghg": -1,
                        "lut_usage_reason": None,
                        "multiplier": 1,
                        "override": False,
                    },
                    {
                        "ghg": -1.1,
                        "lut_usage_reason": None,
                        "multiplier": 1.1,
                        "override": False,
                    },
                ],
                "ghg": -2.1,
                "payment_dollars": 21,
            },
        ),
        (  # Index 9 - 2 fields, cover and tillage practices assigned: upper bound enforced on 1 field
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [
                    {"ghg": -1},
                    {"ghg": -2.1},
                ],
                "fields_without_results": [None, None],
                "fields_with_errors": [None, None],
                "field_areas": [1, 1],
            },
            {
                **base_expectation,
                "field_count": 2,
                "fields_with_results": [
                    {
                        "ghg": -1,
                        "lut_usage_reason": None,
                        "multiplier": 1,
                        "override": False,
                    },
                    {
                        "ghg": -2,
                        "lut_usage_reason": "GHG too high",
                        "multiplier": 2,
                        "override": True,
                    },
                ],
                "ghg": -3,
                "payment_dollars": 30,
            },
        ),
        (  # Index 10 - 1 field with error; use file-based LUT
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.reduced_till,
                    PracticeChange.premium_cover_crops,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                "fields_with_results": [None],
                "fields_without_results": [None],
                "fields_with_errors": {0: {"error": "Some error"}},
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.11,
                        "multiplier": 0.11,
                        "case": "-1-reduced till-cover crops-no till-cover crops",
                        "lut_usage_reason": LutUsageReasons.generic_error,
                    },
                ],
                "ghg": -0.11,
                "payment_dollars": 1.1,
            },
        ),
        (  # Index 11 - 1 field with no result; use file-based LUT
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.cover_crops,
                ],
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.58,
                        "multiplier": 0.58,
                        "case": "-1-conventional till-no cover-no till-cover crops",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.58,
                "payment_dollars": 5.8,
            },
        ),
        (  # Index 12 - 2 fields, one with no result; only use file-based LUT on one
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                "fields_with_results": [{"ghg": -0.3}, None],
                "fields_without_results": [1],
                "fields_with_errors": [None, None],
                "field_areas": [1, 1],
            },
            {
                **base_expectation,
                "field_count": 2,
                "fields_with_results": [
                    {
                        "override": False,
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                    },
                    {
                        "override": True,
                        "ghg": -0.58,
                        "multiplier": 0.58,
                        "case": "-1-conventional till-no cover-no till-cover crops",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.88,
                "payment_dollars": 8.8,
            },
        ),
        (  # Index 13 - EU program, 1 field, cover and tillage practices assigned: no LUT usage
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [{"ghg": -0.3}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -0.3,
                        "multiplier": 0.3,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -0.3,
                "payment_dollars": 3,
            },
        ),
        (  # Index 14 - EU program, 1 field, cover and tillage practices assigned: lower bound enforced
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": {
                    0: {"ghg": 0.3},
                },
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": 0,
                        "lut_usage_reason": "GHG too low",
                        "multiplier": 0,
                        "override": True,
                    },
                ],
                "ghg": 0,
                "payment_dollars": 0,
            },
        ),
        (  # Index 15 - EU program, 1 field with no result; use database LUT
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                StageTypes.INTENDED_COMMODITY_CROPS: {
                    AttributeTypes.crop_type: RegrowCropName.corn,
                    AttributeTypes.planting_date: "2025-03-01",
                    AttributeTypes.harvest_date: "2025-09-01",
                },
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.14,
                        "multiplier": 0.14,
                        "case": "1643-Hungary-2026-corn-conventional_till-no_cover_crop-no_till-basic_cover_crop",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.14,
                "payment_dollars": 1.4,
            },
        ),
        (  # Index 16 - EU program, 1 field with no result; spring barley crop. use database LUT
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                StageTypes.INTENDED_COMMODITY_CROPS: {
                    AttributeTypes.crop_type: RegrowCropName.barley,
                    AttributeTypes.planting_date: "2025-03-01",
                    AttributeTypes.harvest_date: "2025-09-01",
                },
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.23,
                        "multiplier": 0.23,
                        "case": "1643-Hungary-2026-spring-barley-conventional_till-no_cover_crop-no_till-basic_cover_crop",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.23,
                "payment_dollars": 2.3,
            },
        ),
        (  # Index 17 - EU program, 1 field with no result; winter barley crop. use database LUT
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                StageTypes.INTENDED_COMMODITY_CROPS: {
                    AttributeTypes.crop_type: RegrowCropName.barley,
                    AttributeTypes.planting_date: "2025-10-01",
                    AttributeTypes.harvest_date: "2026-04-01",
                },
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.35,
                        "multiplier": 0.35,
                        "case": "1643-Hungary-2026-winter-barley-conventional_till-no_cover_crop-no_till-basic_cover_crop",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.35,
                "payment_dollars": 3.5,
            },
        ),
        (  # Index 17 - EU program, 1 field with no result; crop not in LUT. use CFG column in database LUT
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                StageTypes.INTENDED_COMMODITY_CROPS: {
                    AttributeTypes.crop_type: "alfalfa",
                    AttributeTypes.planting_date: "2025-10-01",
                    AttributeTypes.harvest_date: "2026-04-01",
                },
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -0.48,
                        "multiplier": 0.48,
                        "case": "1643-Hungary-2026-alfalfa-conventional_till-no_cover_crop-no_till-basic_cover_crop",
                        "lut_usage_reason": LutUsageReasons.explore_result_missing,
                    },
                ],
                "ghg": -0.48,
                "payment_dollars": 4.8,
            },
        ),
        (  # Index 18 - EU program, 1 field with no result; crop LUT. bounds applied to LUT ghg value
            {
                "program_id": EU_PROGRAM_ID,
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.no_till,
                    PracticeChange.basic_cover_crops,
                ],
                StageTypes.INTENDED_COMMODITY_CROPS: {
                    AttributeTypes.crop_type: "canola",
                    AttributeTypes.planting_date: "2025-10-01",
                    AttributeTypes.harvest_date: "2026-04-01",
                },
                "fields_with_results": [None],
                "fields_without_results": [0],
                "fields_with_errors": [None],
                "field_areas": [1],
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "override": True,
                        "ghg": -2.0,
                        "multiplier": 2.0,
                        "case": "1643-Hungary-2026-canola-conventional_till-no_cover_crop-no_till-basic_cover_crop",
                        "lut_usage_reason": f"{LutUsageReasons.explore_result_missing}, {LutUsageReasons.ghg_too_high}",
                    },
                ],
                "ghg": -2.0,
                "payment_dollars": 20,
            },
        ),
        (  # Index 19 - 1 field, cover and tillage practices assigned: no LUT usage, don't apply bounds
            {
                StageTypes.CONFIRM_HISTORY: [
                    PracticeChange.conventional_till,
                    PracticeChange.no_cover_crop,
                ],
                StageTypes.ASSIGN_PRACTICES: [
                    PracticeChange.cover_crops.value,
                    PracticeChange.no_till.value,
                ],
                "fields_with_results": [{"ghg": -2.1}],
                "fields_without_results": [None],
                "fields_with_errors": [None],
                "field_areas": [1],
                "do_not_apply_ghg_per_acre_bounds": True,
            },
            {
                **base_expectation,
                "fields_with_results": [
                    {
                        "ghg": -2.1,
                        "multiplier": 2.1,
                        "lut_usage_reason": None,
                        "override": False,
                    },
                ],
                "ghg": -2.1,
                "payment_dollars": 21,
            },
        ),
    ),
)
async def test_get_dndc_override_results(
    mdl, mocker, async_client, lookup_table, app_request: Request, input_: dict, expected: dict
):
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.defaults_translator.translate_core_crop_name_to_regrow_name",
        mock_translate_core_crop_name_to_regrow_name,
    )

    # It randomly generated program 68 and failed, so now I get a random big number
    # so it doesn't break that way.
    # MAYBE we should have used program code and not program ID for this.
    program_id = input_.get("program_id", random.randrange(10000, 30000))
    await create_program(
        async_client=async_client,
        id=program_id,
        reporting_period_start_date="2026-01-01 00:00:00",
        reporting_period_end_date="2026-12-31 23:59:59",
    )
    apply_ghg_per_acre_bounds = not input_.get("do_not_apply_ghg_per_acre_bounds", False)
    await mdl.ProgramModelingConfigurations(program_id=program_id, apply_ghg_per_acre_bounds=apply_ghg_per_acre_bounds)

    project_id = await create_project(async_client=async_client, program_id=program_id)
    raw_program = await create_program_enrolment(
        async_client=async_client,
        program_id=program_id,
        outcome_estimation=True,
    )
    phase: PhaseResponse = PhaseResponse(**raw_program)

    history_stage = next(stage for stage in phase.stages if stage.type_ == StageTypes.CONFIRM_HISTORY)
    await update_stage(app_request, history_stage.id, StageRequestNoParentUpdate(enabled=True))
    practices_stage = next(stage for stage in phase.stages if stage.type_ == StageTypes.ASSIGN_PRACTICES)
    await update_stage(app_request, practices_stage.id, StageRequestNoParentUpdate(enabled=True))
    intended_crops_stage = next((stage for stage in phase.stages if stage.type_ == StageTypes.INTENDED_COMMODITY_CROPS))
    await update_stage(app_request, intended_crops_stage.id, StageRequestNoParentUpdate(enabled=True))
    for attribute in intended_crops_stage.attributes:
        if attribute.type == AttributeTypes.crop_type or attribute.type == AttributeTypes.planting_date:
            await update_attribute(app_request, attribute.id, AttributeRequestUpdate(enabled=True))
    await create_attributes(
        app_request,
        [
            AttributeRequest(
                parent_stage_id=intended_crops_stage.id,
                type=AttributeTypes.harvest_date,
                order=1,
                locked=False,
                enabled=True,
                description="",
            )
        ],
    )
    intended_crops_stage = await get_stage_by_id(app_request, intended_crops_stage.id)

    field_ids = []
    for i in range(expected["field_count"]):
        field_ids.append(int(await create_field(async_client=async_client, project_id=project_id, fs_field_id=i + 1)))

    # First we create the test data
    for field_ref in range(len(field_ids)):
        facts = input_[StageTypes.CONFIRM_HISTORY]
        await create_multiple_field_facts(
            app_request,
            [
                FieldFacts(
                    field_id=field_ids[field_ref],
                    phase_id=phase.id,
                    stage_id=history_stage.id,
                    eligible=True,
                    state=FieldDataState.baseline,
                    data_type=EntityDataType.entity_practices,
                    facts=[[fact] for fact in facts],
                )
            ],
        )
        facts = input_[StageTypes.ASSIGN_PRACTICES]
        await create_multiple_field_facts(
            app_request,
            [
                FieldFacts(
                    field_id=field_ids[field_ref],
                    phase_id=phase.id,
                    stage_id=practices_stage.id,
                    eligible=True,
                    state=FieldDataState.assigned,
                    data_type=EntityDataType.entity_practices,
                    facts=[[fact] for fact in facts],
                )
            ],
        )

        if StageTypes.INTENDED_COMMODITY_CROPS in input_:
            for att_type in [AttributeTypes.crop_type, AttributeTypes.planting_date, AttributeTypes.harvest_date]:
                att = next(att for att in intended_crops_stage.attributes if att.type == att_type)
                await create_value(
                    async_client=async_client,
                    project_id=project_id,
                    field_id=field_ids[field_ref],
                    progress="Enrolment",
                    value=input_[StageTypes.INTENDED_COMMODITY_CROPS][att_type],
                    attribute_id=att.id,
                    row_id=1,
                )

    # Now we run the method:
    field_areas_mapped = {
        field_ids[i]: input_["field_areas"][i] for i in range(len(field_ids)) if input_["field_areas"][i] is not None
    }
    fields_with_results_mapped = (
        {
            field_ids[i]: input_["fields_with_results"][i]
            for i in range(len(field_ids))
            if input_["fields_with_results"][i] is not None
        }
        if input_["fields_with_results"]
        else {}
    )
    fields_without_results_mapped = (
        [field_ids[i] for i in range(len(field_ids)) if i in input_["fields_without_results"]]
        if input_["fields_without_results"]
        else []
    )
    output = await get_dndc_override_results(
        field_ids=set(field_ids),
        fields_with_results=fields_with_results_mapped,
        field_areas=field_areas_mapped,
        fields_without_results=fields_without_results_mapped,
        carbon_price_dollars=CARBON_PRICE,
        program_id=program_id,
        project_id=project_id,
        request=app_request,
    )

    # Adjust field ids
    fields_with_results = expected.pop("fields_with_results")
    expected["fields_with_results"] = {}
    for i in range(len(field_ids)):
        expected["fields_with_results"][field_ids[i]] = fields_with_results[i]

    expected |= {"analyze_data": None}

    assert output.dict() == expected


@patch("scenarios_service.dndc_override.override_dndc_results._apply_bounds_to_dndc_results")
@patch("scenarios_service.dndc_override.override_dndc_results._get_ghg_from_dndc_override_file")
async def test_get_dndc_override_results_error(
    mock_get_ghg_from_dndc_override_file, mock_apply_bounds_to_dndc_results, mdl, app_request
):
    program = await mdl.Programs()
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.ASSIGN_PRACTICES, eligibility_method=EligibilityTypes.CUSTOM)
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)

    mock_get_ghg_from_dndc_override_file.side_effect = [(10, LutUsageReasons.generic_error), Exception()]

    results = await get_dndc_override_results(
        field_ids={field_1.id, field_2.id},
        fields_with_results={},
        field_areas={field_1.id: field_1.area, field_2.id: field_2.area},
        fields_without_results=[field_1.id, field_2.id],
        carbon_price_dollars=10,
        program_id=program.id,
        project_id=project.id,
        request=app_request,
    )

    assert len(results.fields_with_results) == 1
    assert len(results.fields_with_errors) == 1
