from decimal import Decimal

from scenarios_service.dndc_override.db import lookup_in_lut


async def test_lookup_in_lut(app_request, mdl):
    await mdl.LUT(
        year=2023,
        scenario="s1",
        region="Germany",
        crop="canola",
        cfg="C3A",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="basic cover crop",
        practice_change="reduced till + basic cover",
        ghg=Decimal("0.2969773333994955"),
    )
    row2 = await mdl.LUT(
        year=2023,
        scenario="s1",
        region="Germany",
        crop="winter_wheat",
        cfg="C3A",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="basic cover crop",
        practice_change="reduced till + premium cover",
        ghg=Decimal("0.4593598812235409"),
    )
    row3 = await mdl.LUT(
        year=2023,
        scenario="s1",
        region="Germany",
        crop="canola",
        cfg="C3A",
        crop_season="",
        baseline_tillage="conventional till",
        baseline_cover_crop="basic cover crop",
        practice_change="reduced till + premium cover",
        ghg=Decimal("0.4675002420275244"),
    )

    # query by crop, find the exact match
    record = await lookup_in_lut(
        request=app_request,
        year=2023,
        scenario="s1",
        region="Germany",
        crop="canola",
        baseline_tillage="conventional till",
        baseline_cover_crop="basic cover crop",
        practice_change="reduced till + premium cover",
    )
    assert record.ghg == row3.ghg

    # query by cfg (take the record with the lowest ghg)
    record = await lookup_in_lut(
        request=app_request,
        year=2023,
        scenario="s1",
        region="Germany",
        cfg="C3A",
        baseline_tillage="conventional till",
        baseline_cover_crop="basic cover crop",
        practice_change="reduced till + premium cover",
    )
    assert record.ghg == row2.ghg
