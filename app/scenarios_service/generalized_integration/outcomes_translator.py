from datetime import datetime
from typing import Callable

from fastapi import Request
from scenarios_service_schema import scope_1, scope_3
from scenarios_service_schema.biofuels import BiofuelsFieldOutput
from scenarios_service_schema.inventory import FieldLevelValue, InventoryProjectAsset
from scenarios_service_schema.scope_1 import ProjectScope1Response
from scenarios_service_schema.scope_3 import ProjectScope3Response

from fields.db import build_field_to_project_lookup
from logger import get_logger
from reported_outcomes.schema import (
    CropLevelInventoryOutcomesRequest,
    CropLevelOutcomesRequest,
    FieldLevelBiofuelsOutcomesRequest,
    FieldLevelInventoryOutcomesRequest,
    FieldLevelOutcomesRequest,
    ProgramLevelOutcomesRequest,
    ReportedBiofuelsProgramOutcomes,
    ReportedInventoryProgramOutcomes,
    ReportedProgramOutcomes,
)
from scenarios_service.generalized_integration.db import (
    build_mrv_field_id_to_ss_field_id_lookup,
)

logger = get_logger(__name__)


def _build_mrv_field_id_to_ss_field_id_lookup(
    ss_fields: list[int | str], field_level_responses: dict
) -> dict[int, int | str] | None:
    mrv_field_id_to_ss_field_id_lookup: dict[int, int | str] = {}
    for ss_field_id in ss_fields:
        field_resp = field_level_responses[ss_field_id]
        split_name = field_resp.field_name.split("_")
        # if we don't have a field name with the convention of
        # field_{field_id}_task_{task_id} we can't trust extracting
        # the field id from it
        if len(split_name) < 4 or split_name[0] != "field" or split_name[2] != "task":
            return None
        field_id = int(split_name[1])
        mrv_field_id_to_ss_field_id_lookup[field_id] = ss_field_id
    return mrv_field_id_to_ss_field_id_lookup


class FieldOutcomeBuilder:
    def __init__(self, request: Request, outcome_translator: Callable, program_id: int, task_id: str) -> None:
        self.request = request
        self.outcome_translator = outcome_translator
        self.program_id = program_id
        self.task_id = task_id

    async def build_field_level_outcomes(
        self, ss_fields: list[int | str], field_level_responses: dict
    ) -> dict[int, FieldLevelOutcomesRequest]:
        if not ss_fields:
            return {}

        mrv_field_id_to_ss_field_id_lookup = _build_mrv_field_id_to_ss_field_id_lookup(ss_fields, field_level_responses)
        if mrv_field_id_to_ss_field_id_lookup is None:
            mrv_field_id_to_ss_field_id_lookup = await build_mrv_field_id_to_ss_field_id_lookup(
                self.request, self.task_id, ss_fields
            )
            # Scope 1 returns field ids as strings and Scope 3 returns them as integers
            if isinstance(ss_fields[0], str):
                mrv_field_id_to_ss_field_id_lookup = {k: str(v) for k, v in mrv_field_id_to_ss_field_id_lookup.items()}
        ss_fields_without_mrv_field = set(ss_fields) - set(mrv_field_id_to_ss_field_id_lookup.values())
        if len(ss_fields_without_mrv_field) > 0:
            logger.error(
                f"Found no MRV fields in program {self.program_id} task {self.task_id} for SS fields: {ss_fields_without_mrv_field}"
            )

        mrv_fields = mrv_field_id_to_ss_field_id_lookup.keys()
        mrv_field_to_project_lookup = await build_field_to_project_lookup(self.request, mrv_fields)
        mrv_fields_with_projects = mrv_field_to_project_lookup.keys()
        mrv_fields_without_projects = set(mrv_fields) - set(mrv_fields_with_projects)
        if len(mrv_fields_without_projects) > 0:
            logger.error(
                f"Found program {self.program_id} fields without associated projects: {mrv_fields_without_projects}"
            )

        return {
            field: self.outcome_translator(
                field,
                field_level_responses[mrv_field_id_to_ss_field_id_lookup[field]],
                self.task_id,
                mrv_field_to_project_lookup[field],
                self.program_id,
            )
            for field in mrv_fields_with_projects
        }


async def translate_scope_3_to_reported_outcomes(
    request: Request, program_id: int, scope_3_response: ProjectScope3Response, task_id: str, is_test_run: bool
) -> ReportedProgramOutcomes:
    crops = scope_3_response.crop_level.keys()
    crop_outcomes = {
        crop: _translate_scope_3_crop_credits(crop, scope_3_response.crop_level[crop], task_id, program_id)
        for crop in crops
    }
    field_outcome_builder = FieldOutcomeBuilder(request, _translate_scope_3_field_outcomes, program_id, task_id)
    field_outcomes = await field_outcome_builder.build_field_level_outcomes(
        list(scope_3_response.field_level.keys()), scope_3_response.field_level
    )
    return ReportedProgramOutcomes(
        program_id=program_id,
        field_outcomes=field_outcomes,
        crop_outcomes=crop_outcomes,
        task_id=task_id,
        is_test_run=is_test_run,
    )


def _translate_scope_3_crop_credits(
    crop: str, s3_crop_outcome: scope_3.CropLevelCredits, task_id: str, program_id: int
) -> CropLevelOutcomesRequest:
    return CropLevelOutcomesRequest(
        program_id=program_id,
        crop_type=crop,
        start_date=s3_crop_outcome.reporting_information.start_date,
        end_date=s3_crop_outcome.reporting_information.end_date,
        total_yield=s3_crop_outcome.reporting_information.total_yield,
        number_of_fields=s3_crop_outcome.credit.number_of_fields,
        number_of_acres=s3_crop_outcome.credit.number_of_acres,
        total_reversible_credit=s3_crop_outcome.credit.total_reversible_credit,
        total_non_reversible_credit=s3_crop_outcome.credit.total_non_reversible_credit,
        total_credit=s3_crop_outcome.credit.total_credit,
        reversible_credit_mean=s3_crop_outcome.credit.reversible_credits.mean,
        reversible_credit_standard_deviation=s3_crop_outcome.credit.reversible_credits.standard_deviation,
        total_reversible_emissions_reductions=s3_crop_outcome.credit.reversible_credits.total_reversible_emissions_reductions,
        non_reversible_credit_mean=s3_crop_outcome.credit.non_reversible_credits.mean,
        non_reversible_credit_standard_deviation=s3_crop_outcome.credit.non_reversible_credits.standard_deviation,
        total_non_reversible_emissions_reductions=s3_crop_outcome.credit.non_reversible_credits.total_non_reversible_emissions_reductions,
        created_at=datetime.now(),
        task_id=task_id,
    )


def _translate_scope_3_field_outcomes(
    mrv_field_id: int, s3_field_outcome: scope_3.FieldLevelOutcomes, task_id: str, project_id: int, program_id: int
) -> FieldLevelOutcomesRequest:
    non_reversible_soil_ch4_baseline = None
    non_reversible_soil_ch4_practice_change = None
    non_reversible_soil_ch4_credit = None

    non_reversible_soil_ch4 = s3_field_outcome.outcomes.non_reversible_outcomes.soil_ch4
    if non_reversible_soil_ch4:
        non_reversible_soil_ch4_baseline = non_reversible_soil_ch4.baseline
        non_reversible_soil_ch4_practice_change = non_reversible_soil_ch4.practice_change
        non_reversible_soil_ch4_credit = non_reversible_soil_ch4.credit

    non_reversible_fossil_fuel_co2_credit = None
    non_reversible_fossil_fuel_co2_baseline = None
    non_reversible_fossil_fuel_co2_practice_change = None

    if s3_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2:
        non_reversible_fossil_fuel_co2_baseline = (
            s3_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.baseline
        )
        non_reversible_fossil_fuel_co2_practice_change = (
            s3_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.practice_change
        )
        non_reversible_fossil_fuel_co2_credit = s3_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.credit

    return FieldLevelOutcomesRequest(
        program_id=program_id,
        project_id=project_id,
        field_id=mrv_field_id,
        start_date=s3_field_outcome.reporting_information.start_date,
        end_date=s3_field_outcome.reporting_information.end_date,
        crop_name=s3_field_outcome.reporting_information.crop_name,
        crop_yield=s3_field_outcome.reporting_information.crop_yield,
        credit_share=s3_field_outcome.outcomes.credit_share,
        reversible_credit_share=s3_field_outcome.outcomes.reversible_outcomes.credit_share,
        reversible_dsoc_baseline=s3_field_outcome.outcomes.reversible_outcomes.dsoc.baseline,
        reversible_dsoc_practice_change=s3_field_outcome.outcomes.reversible_outcomes.dsoc.practice_change,
        reversible_dsoc_preliminary_credit=s3_field_outcome.outcomes.reversible_outcomes.dsoc.preliminary_credit,
        reversible_dsoc_mean=s3_field_outcome.outcomes.reversible_outcomes.dsoc.mean,
        reversible_dsoc_standard_deviation=s3_field_outcome.outcomes.reversible_outcomes.dsoc.standard_deviation,
        non_reversible_credit_share=s3_field_outcome.outcomes.non_reversible_outcomes.credit_share,
        non_reversible_mean=s3_field_outcome.outcomes.non_reversible_outcomes.mean,
        non_reversible_standard_deviation=s3_field_outcome.outcomes.non_reversible_outcomes.standard_deviation,
        non_reversible_direct_n2o_baseline=s3_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.baseline,
        non_reversible_direct_n2o_practice_change=s3_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.practice_change,
        non_reversible_direct_n2o_preliminary_credit=s3_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.preliminary_credit,
        non_reversible_direct_n2o_mean=s3_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.mean,
        non_reversible_direct_n2o_standard_deviation=s3_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.standard_deviation,
        non_reversible_indirect_n2o_baseline=s3_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.baseline,
        non_reversible_indirect_n2o_practice_change=s3_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.practice_change,
        non_reversible_indirect_n2o_credit=s3_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.credit,
        non_reversible_soil_ch4_baseline=non_reversible_soil_ch4_baseline,
        non_reversible_soil_ch4_practice_change=non_reversible_soil_ch4_practice_change,
        non_reversible_soil_ch4_credit=non_reversible_soil_ch4_credit,
        non_reversible_fossil_fuel_co2_baseline=non_reversible_fossil_fuel_co2_baseline,
        non_reversible_fossil_fuel_co2_practice_change=non_reversible_fossil_fuel_co2_practice_change,
        non_reversible_fossil_fuel_co2_credit=non_reversible_fossil_fuel_co2_credit,
        created_at=datetime.now(),
        task_id=task_id,
    )


async def translate_scope_1_to_reported_outcomes(
    request: Request, program_id: int, scope_1_response: ProjectScope1Response, task_id: str, is_test_run: bool
) -> ReportedProgramOutcomes:
    program_outcome = _translate_scope_1_program_outcomes(scope_1_response.project_level, task_id, program_id)
    field_outcome_builder = FieldOutcomeBuilder(request, _translate_scope_1_field_outcomes, program_id, task_id)
    field_outcomes = await field_outcome_builder.build_field_level_outcomes(
        list(scope_1_response.field_level.keys()), scope_1_response.field_level
    )
    return ReportedProgramOutcomes(
        program_id=program_id,
        field_outcomes=field_outcomes,
        crop_outcomes={},
        program_outcomes=program_outcome,
        task_id=task_id,
        is_test_run=is_test_run,
    )


def _translate_scope_1_field_outcomes(
    mrv_field_id: int, s1_field_outcome: scope_1.FieldLevelOutcomes, task_id: str, project_id: int, program_id: int
) -> FieldLevelOutcomesRequest:
    non_reversible_direct_n2o_preliminary_credit = (
        s1_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.preliminary_credit
    )
    non_reversible_indirect_n2o_credit = s1_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.credit
    non_reversible_soil_ch4_credit = s1_field_outcome.outcomes.non_reversible_outcomes.soil_ch4.preliminary_credit
    non_reversible_fossil_fuel_co2_credit = None
    non_reversible_fossil_fuel_co2_baseline = None
    non_reversible_fossil_fuel_co2_practice_change = None
    if s1_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2:
        non_reversible_fossil_fuel_co2_credit = s1_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.credit
        non_reversible_fossil_fuel_co2_baseline = (
            s1_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.baseline
        )
        non_reversible_fossil_fuel_co2_practice_change = (
            s1_field_outcome.outcomes.non_reversible_outcomes.fossil_fuel_co2.practice_change
        )
    return FieldLevelOutcomesRequest(
        program_id=program_id,
        project_id=project_id,
        field_id=mrv_field_id,
        start_date=s1_field_outcome.reporting_information.start_date,
        end_date=s1_field_outcome.reporting_information.end_date,
        crop_name=None,
        crop_yield=None,
        credit_share=s1_field_outcome.outcomes.credit_share,
        reversible_credit_share=s1_field_outcome.outcomes.reversible_outcomes.credit_share,
        reversible_dsoc_baseline=s1_field_outcome.outcomes.reversible_outcomes.dsoc.baseline,
        reversible_dsoc_practice_change=s1_field_outcome.outcomes.reversible_outcomes.dsoc.practice_change,
        reversible_dsoc_preliminary_credit=s1_field_outcome.outcomes.reversible_outcomes.dsoc.preliminary_credit,
        reversible_dsoc_mean=s1_field_outcome.outcomes.reversible_outcomes.dsoc.mean,
        reversible_dsoc_standard_deviation=s1_field_outcome.outcomes.reversible_outcomes.dsoc.standard_deviation,
        non_reversible_credit_share=s1_field_outcome.outcomes.non_reversible_outcomes.credit_share,
        non_reversible_mean=s1_field_outcome.outcomes.non_reversible_outcomes.mean,
        non_reversible_standard_deviation=s1_field_outcome.outcomes.non_reversible_outcomes.standard_deviation,
        non_reversible_direct_n2o_baseline=s1_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.baseline,
        non_reversible_direct_n2o_practice_change=s1_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.practice_change,
        non_reversible_direct_n2o_preliminary_credit=non_reversible_direct_n2o_preliminary_credit,
        non_reversible_direct_n2o_mean=s1_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.mean,
        non_reversible_direct_n2o_standard_deviation=s1_field_outcome.outcomes.non_reversible_outcomes.direct_n2o.standard_deviation,
        non_reversible_indirect_n2o_baseline=s1_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.baseline,
        non_reversible_indirect_n2o_practice_change=s1_field_outcome.outcomes.non_reversible_outcomes.indirect_n2o.practice_change,
        non_reversible_indirect_n2o_credit=non_reversible_indirect_n2o_credit,
        non_reversible_soil_ch4_baseline=s1_field_outcome.outcomes.non_reversible_outcomes.soil_ch4.baseline,
        non_reversible_soil_ch4_practice_change=s1_field_outcome.outcomes.non_reversible_outcomes.soil_ch4.practice_change,
        non_reversible_soil_ch4_credit=non_reversible_soil_ch4_credit,
        non_reversible_fossil_fuel_co2_baseline=non_reversible_fossil_fuel_co2_baseline,
        non_reversible_fossil_fuel_co2_practice_change=non_reversible_fossil_fuel_co2_practice_change,
        non_reversible_fossil_fuel_co2_credit=non_reversible_fossil_fuel_co2_credit,
        created_at=datetime.now(),
        task_id=task_id,
    )


def _translate_scope_1_program_outcomes(
    s1_program_outcome: scope_1.ProjectLevelCredits, task_id: str, program_id: int
) -> ProgramLevelOutcomesRequest:
    reversible_credit_mean = None
    reversible_credit_standard_deviation = None
    total_reversible_emissions_reductions = None
    if s1_program_outcome.credit.reversible_credits:
        reversible_credit_mean = s1_program_outcome.credit.reversible_credits.mean
        reversible_credit_standard_deviation = s1_program_outcome.credit.reversible_credits.standard_deviation
        total_reversible_emissions_reductions = s1_program_outcome.credit.reversible_credits.credit
    non_reversible_credit_mean = None
    non_reversible_credit_standard_deviation = None
    total_non_reversible_emissions_reductions = None
    if s1_program_outcome.credit.non_reversible_credits:
        non_reversible_credit_mean = s1_program_outcome.credit.non_reversible_credits.mean
        non_reversible_credit_standard_deviation = s1_program_outcome.credit.non_reversible_credits.standard_deviation
        total_non_reversible_emissions_reductions = s1_program_outcome.credit.non_reversible_credits.credit
    return ProgramLevelOutcomesRequest(
        program_id=program_id,
        start_date=s1_program_outcome.reporting_information.start_date,
        end_date=s1_program_outcome.reporting_information.end_date,
        number_of_fields=s1_program_outcome.credit.number_of_fields,
        number_of_acres=s1_program_outcome.credit.number_of_acres,
        total_reversible_credit=s1_program_outcome.credit.reversible_credit,
        total_non_reversible_credit=s1_program_outcome.credit.non_reversible_credit,
        total_credit=s1_program_outcome.credit.total_credit,
        reversible_credit_mean=reversible_credit_mean,
        reversible_credit_standard_deviation=reversible_credit_standard_deviation,
        total_reversible_emissions_reductions=total_reversible_emissions_reductions,
        non_reversible_credit_mean=non_reversible_credit_mean,
        non_reversible_credit_standard_deviation=non_reversible_credit_standard_deviation,
        total_non_reversible_emissions_reductions=total_non_reversible_emissions_reductions,
        created_at=datetime.now(),
        task_id=task_id,
    )


async def translate_inventory_to_reported_outcomes(
    request: Request, program_id: int, raw_inventory_results: dict, task_id: str, is_test_run: bool
) -> ReportedInventoryProgramOutcomes:
    field_inventory_outcomes: dict[int, FieldLevelInventoryOutcomesRequest] = {}

    mrv_field_ids = [int(f["field_name"]) for f in raw_inventory_results["field_level_value"]]
    mrv_field_to_project_lookup = await build_field_to_project_lookup(request, mrv_field_ids)

    field_id_to_percentage_lookup = {
        int(fd["field_name"]): fd["net_emissions_percentage"] for fd in raw_inventory_results["field_level"]
    }

    for field_data in raw_inventory_results["field_level_value"]:
        field_id = int(field_data["field_name"])
        project_id = mrv_field_to_project_lookup[field_id]
        fo = _translate_inventory_field_outcomes(
            field_id, field_data, task_id, program_id, project_id, field_id_to_percentage_lookup
        )
        field_inventory_outcomes[fo.field_id] = fo

    crop_inventory_outcomes: dict[str, CropLevelInventoryOutcomesRequest] = {}
    crops = raw_inventory_results["crop_level"].keys()
    for crop in crops:
        crop_data = raw_inventory_results["crop_level"][crop]
        co = _translate_inventory_crop_outcomes(crop, crop_data, task_id, program_id)
        crop_inventory_outcomes[crop] = co

    return ReportedInventoryProgramOutcomes(
        program_id=program_id,
        field_inventory_outcomes=field_inventory_outcomes,
        crop_inventory_outcomes=crop_inventory_outcomes,
        task_id=task_id,
        is_test_run=is_test_run,
    )


def _translate_inventory_field_outcomes(
    field_id: int,
    raw_field_data: dict,
    task_id: str,
    program_id: int,
    project_id: int,
    field_id_to_percentage_lookup: dict[int, float],
) -> FieldLevelInventoryOutcomesRequest:
    parsed_share = FieldLevelValue(**raw_field_data)
    bushels = parsed_share.crop_yield_bushels_per_acre * parsed_share.area_acres
    return FieldLevelInventoryOutcomesRequest(
        field_id=field_id,
        program_id=program_id,
        project_id=project_id,
        crop_name=parsed_share.crediting_crop,
        mapped_acres=parsed_share.area_acres,
        total_yield=bushels,
        net_emissions_percentage=field_id_to_percentage_lookup[field_id],
        net_emissions_factor=parsed_share.net_emissions_factor,
        ghg_emissions_factor=parsed_share.ghg_emissions_factor,
        soc_emissions_factor=parsed_share.soc_emissions_factor,
        created_at=datetime.now(),
        task_id=task_id,
    )


def _translate_inventory_crop_outcomes(
    crop_name: str, raw_crop_data: dict, task_id: str, program_id: int
) -> CropLevelInventoryOutcomesRequest:
    parsed_asset = InventoryProjectAsset(**raw_crop_data)
    net_impact = parsed_asset.total_emissions + parsed_asset.soc
    net_impact_per_bushel = parsed_asset.total_emissions_per_bushel + parsed_asset.soc_per_bushel
    return CropLevelInventoryOutcomesRequest(
        crop_type=crop_name,
        program_id=program_id,
        number_of_fields=parsed_asset.number_of_fields,
        mapped_acres=parsed_asset.number_of_acres,
        total_yield=parsed_asset.crop_yield,
        total_emissions=net_impact,
        total_emissions_per_bushel=net_impact_per_bushel,
        direct_n2o_emissions=parsed_asset.direct_n2o_emissions,
        indirect_n2o_emissions=parsed_asset.indirect_n2o_emissions,
        soil_ch4_emissions=parsed_asset.soil_ch4_emissions,
        soc=parsed_asset.soc,
        soc_per_bushel=parsed_asset.soc_per_bushel,
        created_at=datetime.now(),
        task_id=task_id,
    )


async def translate_biofuels_to_reported_outcomes(
    request: Request, program_id: int, raw_biofuels_results: dict, task_id: str, is_test_run: bool
) -> ReportedBiofuelsProgramOutcomes:
    mrv_field_ids = [int(f["field_name"]) for f in raw_biofuels_results["field_level"]]
    mrv_field_to_project_lookup = await build_field_to_project_lookup(request, mrv_field_ids)

    # TO-DO:  do something with raw_biofuels_results
    for field_data in raw_biofuels_results["field_level"]:
        field_id = int(field_data["field_name"])
        project_id = mrv_field_to_project_lookup[field_id]
        parsed_field_data = BiofuelsFieldOutput(**field_data)
        _translate_biofuels_field_outcomes(field_id, parsed_field_data, task_id, program_id, project_id)
    return ReportedBiofuelsProgramOutcomes(
        program_id=program_id,
        task_id=task_id,
        is_test_run=is_test_run,
    )


def _translate_biofuels_field_outcomes(
    field_id: int,
    parsed_field_data: BiofuelsFieldOutput,
    task_id: str,
    program_id: int,
    project_id: int,
) -> FieldLevelBiofuelsOutcomesRequest:

    raise NotImplementedError("not yet translating field-level biofuels")
