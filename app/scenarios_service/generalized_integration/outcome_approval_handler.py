from typing import Union

from fastapi import Request

from reported_outcomes.db import revoke_program_outcome_approval
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration import tasks
from scenarios_service.generalized_integration.biofuels_integration import (
    BiofuelsOutputIntegration,
)
from scenarios_service.generalized_integration.db import get_dndc_task_by_id
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.measure_integration import (
    MeasureOutputIntegration,
)
from scenarios_service.generalized_integration.outcome_interfaces import (
    OutputIntegration,
)


async def handle_outcome_approval(
    request: Request, program_id: int, task_id: str | None, is_test_run: bool, is_async: bool
) -> None:
    if task_id is None:
        await revoke_existing_approval(request, program_id, is_test_run, is_async)
    elif is_async:
        tasks.approve_outcomes_for_program.delay(
            program_id=program_id,
            task_id=task_id,
            is_test_run=is_test_run,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )
    else:
        await process_outcome_approval(request, program_id, task_id, is_test_run)


async def revoke_existing_approval(request: Request, program_id: int, is_test_run: bool, is_async: bool) -> None:
    if is_async:
        tasks.revoke_approval_for_program.delay(
            program_id=program_id,
            is_test_run=is_test_run,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )
    else:
        await process_revoking_outcome(request, program_id, is_test_run)


async def process_outcome_approval(request: Request, program_id: int, task_id: str, is_test_run: bool) -> None:
    outcome_integration = await _obtain_outcome_integration(request, task_id)
    await outcome_integration.approve_outcomes_for_program(program_id, task_id, is_test_run)


async def process_revoking_outcome(request: Request, program_id: int, is_test_run: bool) -> None:
    if not is_test_run:
        return await revoke_program_outcome_approval(request, program_id)


AnyOutputIntegration = Union[BiofuelsOutputIntegration | InventoryOutputIntegration | MeasureOutputIntegration]


# this is separated out to have a testable unit
async def _obtain_outcome_integration(request: Request, task_id: str) -> AnyOutputIntegration:
    task = await get_dndc_task_by_id(request, task_id)
    return _select_integration_by_ssapi_type(request, task.scenarios_service_api)


def _select_integration_by_ssapi_type(
    request: Request, scenarios_service_api: ScenariosServiceApi
) -> OutputIntegration:
    if scenarios_service_api == ScenariosServiceApi.measure_api:
        return MeasureOutputIntegration(request)
    elif scenarios_service_api == ScenariosServiceApi.inventory_api:
        return InventoryOutputIntegration(request)
    elif scenarios_service_api == ScenariosServiceApi.biofuels_api:
        # to be added as part of MRV-4621
        raise NotImplementedError(
            "Outcome reporting for biofuels programs is not yet provided (see MRV-4621 for status)"
        )
        # return BiofuelsOutputIntegration(request)
    else:
        raise NotImplementedError(
            f"No output integration is available for scenarios service API: {scenarios_service_api}"
        )
