import asyncio
import json
from typing import Optional

import httpx
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from httpx import HTTPStatusError
from scenarios_service_client.biofuels_api_client import BiofuelsApiAsyncClient
from scenarios_service_schema.biofuels import (
    BiofuelsInput,
    BiofuelsInterventionEnum,
    BiofuelsProjectMethodEnum,
)
from scenarios_service_schema.schema import FieldRequestOutputResponse

from config import get_settings
from fields.model import Fields
from fields.schema import Field
from helper.async_tools import batch_async_runner
from logger import get_logger
from phases.enums import PhaseTypes
from programs.db import get_program
from programs.model import Programs
from reported_outcomes.db import store_biofuels_program_outcomes
from reported_outcomes.schema import ReportedBiofuelsProgramOutcomes
from root_crud import get
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration import db, helpers
from scenarios_service.generalized_integration.biofuels_inputs_translator import (
    BiofuelsInputsTranslator,
)
from scenarios_service.generalized_integration.db import (
    record_dndc_simulation_request_error,
    record_dndc_simulation_request_success,
)
from scenarios_service.generalized_integration.outcome_integration_common import (
    read_url_result,
)
from scenarios_service.generalized_integration.outcomes_translator import (
    translate_biofuels_to_reported_outcomes,
)
from scenarios_service.generalized_integration.schema import (
    FieldSimulationContext,
    MeasureSubmissionResult,
    ScenariosServiceIntegrationError,
    ScenariosServiceSubmissionResult,
)
from scenarios_service.model import (
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)
from scenarios_service.utils import slugify

settings = get_settings()
logger = get_logger(__name__)


class BiofuelsIntegration:

    def __init__(
        self,
        request: Request,
        biofuels_inputs_translator: BiofuelsInputsTranslator | None = None,
        biofuels_client: BiofuelsApiAsyncClient | None = None,
    ):
        self.request = request
        self.biofuels_inputs_translator = biofuels_inputs_translator or BiofuelsInputsTranslator(request)
        self.task_id: str | None = None
        self.program: Programs | None = None
        self.ss_consumer_id: str | None = None
        self.biofuels_client = biofuels_client or BiofuelsApiAsyncClient(url=settings.SCENARIOS_SERVICE_INTERNAL_URL)
        self.simulation_contexts_by_field: dict[int, FieldSimulationContext] = {}

    async def submit_program_for_biofuels_outcomes(
        self, program_id: int, field_ids: list[int], dry_run: bool = False
    ) -> MeasureSubmissionResult:
        """
        This is the entrypoint for submitting all fields in a program to Measure's Biofuels API. Expected to be
        initiated as a manual process soon after the end of a program Reporting Period. If all fields are submitted
        successfully, the POST /api/projects/{project_name}/finalize endpoint will be called.

        A new SS Consumer must be created for each MRV Program, and its model versions configured.

        Returns a MeasureSubmissionResult object comprised of:
        - finalize_success (bool): True if the program was successfully finalized, False otherwise
        - field_submission_results (list[ScenariosServiceSubmissionResult): list of "results" (success/error+msg)
          for each field submitted.
        """
        self.program = await get_program(request=self.request, program_id=program_id)  # Raises a 404 if not found
        self.ss_consumer_id = await db.get_program_modeling_consumer_id(request=self.request, program_id=program_id)
        # This function should interrupt any in-progress tasks, like in ExploreIntegration
        self.task_id = (
            await db.create_dndc_task(
                request=self.request,
                program_id=program_id,
                project_id=None,
                phase=PhaseTypes.MONITORING,
                is_dry_run=dry_run,
                scenarios_service_api=ScenariosServiceApi.biofuels_api,
            )
        ).id
        await helpers.validate_fields(request=self.request, program_id=program_id, submitted_field_ids=field_ids)
        fields: list[Field] = await get.get(
            request=self.request,
            orm_type=Fields,
            type_=Field,
            id_field=Fields.id,
            ids=field_ids,
            empty_return=True,
            filter_deleted=False,
        )

        biofuels_inputs, error_responses = await self._get_biofuels_inputs(fields)
        ss_project_name: str = await self.create_ss_project_returning_name(self.program, self.task_id, self.ss_consumer_id)  # type: ignore[arg-type]

        # Submit each of the generated SessionInputs
        submission_responses = await self.submit_biofuels_inputs(biofuels_inputs, ss_project_name, self.ss_consumer_id)  # type: ignore[arg-type]
        await db.set_dndc_task_status(self.request, self.task_id, DndcTaskStatusChoices.finished)

        # Finalize only if there are no errors and this is not a dry run
        if len(error_responses) > 0 or any(response.is_error for response in submission_responses) is True:
            finalize_success = False
            logger.exception(f"Error(s) in Biofuels submission: {error_responses}")
        elif dry_run is True:
            finalize_success = False
            logger.info(f"Dry run completed for program {program_id}. Program not finalized.")
        else:
            try:
                await self.biofuels_client.finalize_project(
                    project_name=ss_project_name, x_consumer_id=self.ss_consumer_id
                )
                finalize_success = True
            except httpx.HTTPStatusError as e:
                finalize_success = False
                logger.exception(f"Error finalizing project {ss_project_name}: {e.response.status_code}")

        return MeasureSubmissionResult(
            finalize_success=finalize_success,
            field_submission_results=sorted(error_responses + submission_responses, key=lambda x: x.field_id),
        )

    async def _get_biofuels_inputs(
        self, fields: list[Field]
    ) -> tuple[list[BiofuelsInput], list[ScenariosServiceSubmissionResult]]:
        """Makes batched async calls to the SessionInputsTranslator and handles results."""

        async def _get_biofuels_input(
            field: Field,
        ) -> BiofuelsInput | ScenariosServiceSubmissionResult:
            # Biofuels has changed this from session_name to field_name
            ss_field_name = str(field.id)
            sim_context = FieldSimulationContext(
                field_id=field.id,
                task_id=self.task_id,
                ss_api=ScenariosServiceApi.biofuels_api,
                ss_session_name=ss_field_name,
            )
            self.simulation_contexts_by_field[field.id] = sim_context
            try:
                return await self.biofuels_inputs_translator.get_biofuels_input_for_field(field=field)
            except Exception as er:
                error_code = None
                if isinstance(er, ScenariosServiceIntegrationError):
                    error_code = er.code
                    error_message = er.message
                else:
                    error_message = f"Error generating BiofuelsInput: {repr(er)}."
                await self._record_simulation_request_error(
                    field_id=field.id, error_message=error_message, error_code=error_code
                )
                logger.exception(er)
                return ScenariosServiceSubmissionResult(
                    field_id=field.id,
                    session_name=ss_field_name,
                    is_error=True,
                    error_message=error_message,
                    error_code=error_code,
                )

        results = await batch_async_runner([_get_biofuels_input(field) for field in fields])
        return (
            [res for res in results if isinstance(res, BiofuelsInput)],
            [res for res in results if isinstance(res, ScenariosServiceSubmissionResult)],
        )

    async def create_ss_project_returning_name(self, program: Programs, task_id: str, ss_consumer_id: str) -> str:
        """Scenarios Service has a 'project' concept that is different from MRV projects. SS projects are just a
        container for all the Field SessionInputs that will be simulated with the same configuration (protocol and
        baseline method). The SS project maps to an MRV program. Once fields are submitted to a project, they can't be
        edited, so we create a new project with every run."""
        project_name: str = f"{slugify(program.name)}_{task_id}"
        await self.biofuels_client.create_project(
            project_name=project_name,
            x_consumer_id=ss_consumer_id,
            method=BiofuelsProjectMethodEnum.FDCIC,
            # LDC is doing an exploration of possible interventions and their impacts on CI outcomes
            interventions=(
                [
                    BiofuelsInterventionEnum.INCREASE_YIELD_10_PERCENT.value,
                    BiofuelsInterventionEnum.CONSERVATION_TILLAGE.value,
                    BiofuelsInterventionEnum.ADD_COVER_CROP.value,
                    BiofuelsInterventionEnum.REDUCE_FERTILIZER_AND_YIELD_10_PERCENT.value,
                ]
                if program.id == 1147
                else None
            ),
        )
        return project_name

    async def submit_biofuels_inputs(
        self,
        biofuels_inputs: list[BiofuelsInput],
        ss_project_name: str,
        ss_consumer_id: str,
    ) -> list[ScenariosServiceSubmissionResult]:
        return await batch_async_runner(
            [
                self.submit_field_biofuels_input(ss_project_name, ss_consumer_id, biofuels_input)
                for biofuels_input in biofuels_inputs
            ]
        )

    async def submit_field_biofuels_input(
        self,
        ss_project_name: str,
        ss_consumer_id: str,
        biofuels_input: BiofuelsInput,
    ) -> ScenariosServiceSubmissionResult:
        context = self.get_sim_context_by_field_name(biofuels_input.field_name)
        logger.debug(f"Submitting field BiofuelsInput to Measure API for field {context.field_id}.")
        try:
            biofuels_response: httpx.Response = await self.biofuels_client.submit_field(
                project_name=ss_project_name, x_consumer_id=ss_consumer_id, biofuels_input=biofuels_input
            )
            response_data = FieldRequestOutputResponse.parse_obj(biofuels_response.json())
            context.ss_field_request_id = response_data.id
            await self._record_simulation_request_success(context=context, biofuels_input=biofuels_input)
            return ScenariosServiceSubmissionResult(
                field_id=int(response_data.name),  # BiofuelsInput.name is always set to Field ID
                is_error=False,
                session_name=context.ss_session_name,
                project_name=ss_project_name,
                field_request_id=response_data.id,
            )
        except Exception as e:
            logger.exception(e)
            if isinstance(e, HTTPStatusError) and e.response.status_code == 422:
                error_message = f"The BiofuelsInput failed Biofuels API validation: {e.response.content}"
                error_code = ScenariosServiceIntegrationErrorCode.scenarios_service_validation_failure
            else:  # http_e.response.status_code >= 500:
                error_message = f"Biofuels API experienced a server error: {repr(e)}"
                error_code = ScenariosServiceIntegrationErrorCode.generic_scenarios_service_failure
            return await self._record_simulation_request_error(
                context.field_id, error_message, error_code, biofuels_input
            )

    async def _record_simulation_request_success(
        self, context: FieldSimulationContext, biofuels_input: BiofuelsInput | None = None
    ) -> None:
        tasks = [
            record_dndc_simulation_request_success(
                request=self.request,
                context=context,
                ss_api=ScenariosServiceApi.biofuels_api,
            )
        ]
        if biofuels_input:
            tasks.append(
                helpers.store_session_input_in_bucket(
                    request=self.request,
                    task_id=context.task_id,
                    field_id=context.field_id,
                    session_input=biofuels_input,
                )
            )
        await asyncio.gather(*tasks)

    async def _record_simulation_request_error(
        self,
        field_id: int,
        error_message: str,
        error_code: ScenariosServiceIntegrationErrorCode | None,
        biofuels_input: BiofuelsInput | None = None,
    ) -> ScenariosServiceSubmissionResult:
        context = self.simulation_contexts_by_field[field_id]
        context.error_message = error_message
        context.error_code = error_code
        context.is_error = True

        async_tasks = [
            record_dndc_simulation_request_error(
                request=self.request,
                sim_context=context,
                ss_api=ScenariosServiceApi.biofuels_api,
            )
        ]
        if biofuels_input:
            async_tasks.append(
                helpers.store_session_input_in_bucket(
                    request=self.request, task_id=context.task_id, field_id=field_id, session_input=biofuels_input
                )
            )
        await asyncio.gather(*async_tasks)
        return ScenariosServiceSubmissionResult(
            field_id=context.field_id,
            is_error=True,
            error_message=error_message,
            error_code=error_code,
            session_name=context.ss_session_name,
        )

    def get_sim_context_by_field_name(self, field_name: str) -> FieldSimulationContext:
        return next(
            context for context in self.simulation_contexts_by_field.values() if context.ss_session_name == field_name
        )


class BiofuelsOutputIntegration:
    def __init__(
        self,
        request: Request,
        biofuels_client: BiofuelsApiAsyncClient | None = None,
    ):
        self.request = request
        self.biofuels_client = biofuels_client or BiofuelsApiAsyncClient(settings.SCENARIOS_SERVICE_INTERNAL_URL)

    async def approve_outcomes_for_program(
        self, program_id: int, selected_task_id: Optional[str], is_test_run: bool
    ) -> None:
        if selected_task_id is None:
            program_outcomes = ReportedBiofuelsProgramOutcomes(
                program_id=program_id,
                task_id=None,
                # to fill-in here
                is_test_run=is_test_run,
            )
        else:
            program_outcomes = await self._obtain_biofuels_outcomes(program_id, selected_task_id, is_test_run)
        await store_biofuels_program_outcomes(self.request, program_outcomes)

    async def _obtain_biofuels_outcomes(
        self, program_id: int, selected_task_id: str, is_test_run: bool
    ) -> ReportedBiofuelsProgramOutcomes:
        program: Programs = await get_program(request=self.request, program_id=program_id)
        ss_consumer_id: str = await db.get_program_modeling_consumer_id(request=self.request, program_id=program_id)
        ss_project_name = helpers.build_ss_project_name(program, selected_task_id)
        try:
            biofuels_signed_res = await self.biofuels_client.get_project_results(
                ss_project_name, ss_consumer_id, signed_url=True
            )
        except httpx.HTTPStatusError:
            error_msg = f"Scenario service did not handle providing results from ss_project_name={ss_project_name} ss_consumer_id={ss_consumer_id} for project task {selected_task_id}"
            logger.error(error_msg)
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": error_msg})

        if biofuels_signed_res.status_code == 202:
            error_msg = f"Results are not finalized from ss_project_name={ss_project_name} ss_consumer_id={ss_consumer_id} for task {selected_task_id} and are not ready to be used"
            logger.error(error_msg)
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": error_msg})

        biofuels_signed_url = biofuels_signed_res.json()["download_link"]
        biofuels_result_str = read_url_result(biofuels_signed_url)
        biofuels_results = json.loads(biofuels_result_str)
        del biofuels_result_str
        return await translate_biofuels_to_reported_outcomes(
            self.request, program_id, biofuels_results, selected_task_id, is_test_run
        )
