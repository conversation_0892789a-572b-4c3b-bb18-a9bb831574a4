# Notes about modeling configuration

Copying [notes from slack](https://regrowag.slack.com/archives/C07KKQASVF1/p1732837859291389?thread_ts=1732835777.639429&cid=C07KKQASVF1),
in case it's helpful.

Feel free to update the notes as needed.

When we call [POST /api/projects](http://dndc-scenarios-service.int.dev.regrow.cloud/docs#/Measure%20API/create_project_api_projects_post)
in Scenarios Service, we need to send `x-consumer-id` (in the header) and `protocol`.

The `x-consumer-id` we send comes from the db, e.g.:

```sql
select *
from mrv_program_modeling_configuration
where program_id = 155;

+-----+------------+-----------------------------+----------------------------------+--------+
| id  | program_id | consumer_id                 | intentions_data_collection_style | method |
|-----+------------+-----------------------------+----------------------------------+--------|
| 11  | 155        | mrv-cargill-regenconnect-us | PRACTICES                        | DNDC   |
| 109 | 155        | mrv-cargill-regenconnect-us | EVENTS                           | DNDC   |
+-----+------------+-----------------------------+----------------------------------+--------+
```

The `protocol` requested comes from `mrv_programs`:

```sql
select protocol
from mrv_programs
where id = 155;

+-----------------+
| protocol        |
|-----------------|
| GENERAL_SCOPE_3 |
+-----------------+
```

Bear in mind that SS doesn't give every `protocol` to every `x-consumer-id`. For example, at the time of writing
`mrv-cargill-regenconnect-us` doesn't have access to `GENERAL_SCOPE_3`.

The CRUD calls to update `mrv_program_modeling_configuration` are in
`app/scenarios_service/generalized_integration/router.py`, e.g.

- `get_modeling_configuration`
- `submit_modeling_configuration`
- `patch_modeling_configuration`
