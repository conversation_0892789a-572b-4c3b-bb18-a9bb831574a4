from typing import Any, Generator

import backoff
from httpx import HTTPStatusError, Response
from scenarios_service_client.explore_api_client import ExploreApiAsyncClient
from scenarios_service_schema.explore_api import ExploreAPIInput

from annotations import DictStrAny


def is_not_502_503(exception: Exception) -> bool:
    return not isinstance(exception, HTTPStatusError) or exception.response.status_code not in {502, 503}


#  We use exponential backoff with jitter to mitigate contention for a shared resource
def backoff_expo() -> Generator[float, Any, None]:
    # The first wait will be between 0 and 5 seconds, then 0 and 10 seconds, 20 seconds, etc.
    return backoff.expo(factor=5)


class RetryingExploreApiClient(ExploreApiAsyncClient):
    """
    A wrapper for the ExploreApiAsyncClient that adds retries on 502 and 503 status codes.
    """

    def __init__(self, **kwargs: DictStrAny) -> None:
        super().__init__(**kwargs)

    @backoff.on_exception(exception=HTTPStatusError, wait_gen=backoff_expo, giveup=is_not_502_503, max_tries=4)
    async def upload_field(self, session_input: ExploreAPIInput, ss_consumer_id: str) -> Response:
        """See the FieldRequestIdOutputResponse schema for the json format of the Response"""
        return await super().upload_field(session_input=session_input, x_consumer_id=ss_consumer_id)

    @backoff.on_exception(exception=HTTPStatusError, wait_gen=backoff_expo, giveup=is_not_502_503, max_tries=4)
    async def get_field_results(self, ss_field_request_id: int, ss_consumer_id: str) -> Response:
        return await super().get_field_results(field_request_id=ss_field_request_id, x_consumer_id=ss_consumer_id)

    @backoff.on_exception(exception=HTTPStatusError, wait_gen=backoff_expo, giveup=is_not_502_503, max_tries=4)
    async def get_field_simulation_status(self, ss_field_request_id: int, ss_consumer_id: str) -> Response:
        return await super().get_field_simulation_status(
            field_request_id=ss_field_request_id, x_consumer_id=ss_consumer_id
        )
