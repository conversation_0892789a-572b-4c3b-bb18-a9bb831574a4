import itertools
from typing import Union

import elasticapm
from fastapi import Request
from pydantic import ValidationError
from scenarios_service_schema.biofuels import (
    BiofuelsInput,
    CropNameEnum,
    Manure,
    NitrogenFertilizer,
    RateTypeEnum,
    TillageEnum,
)
from scenarios_service_schema.schema import ApplicationMethodEnum

from config import get_settings
from cultivation_cycles import methods as cc_methods
from cultivation_cycles.schema import CultivationCycle, CultivationCycleGenerationConfig
from defaults import defaults_translator
from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    NO_ADDITIVES_OPTION,
)
from defaults.schema import NutrientProductType
from entity_events import methods as entity_events_methods
from entity_events.events.application_event import (
    ApplicationEvent as MRVApplicationEvent,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.schema import ApplicationInput
from entity_events.events.tillage_event import TillageEvent
from entity_events.units import AreaUnit, VolumeUnit
from fields.schema import Field
from logger import get_logger
from pint_configuration import ureg
from programs.db import get_program_by_project_id
from programs.enums import AccountingMethod
from programs.model import Programs
from scenarios_service.generalized_integration import (
    fetch_events_for_simulations,
    helpers,
)
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.model import (
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)

Quantity = ureg.Quantity
settings = get_settings()
logger = get_logger(__name__)


class BiofuelsInputsTranslator:

    def __init__(self, request: Request):
        self.request = request

    @elasticapm.async_capture_span()
    async def get_biofuels_input_for_field(self, field: Field) -> BiofuelsInput:
        """
        Take a single field and derive the BiofuelsInput based on the cultivation cycle that ends during the reporting
        year.

        For now, events must be read via the MRV Structured Events Facade (SEF), where we use entity_events.methods to
        that interpret mrv_values and format them as event instances.
        """
        try:
            events_by_phase = await entity_events_methods.get_entity_events_for_field(self.request, field)
        except ValidationError as validation_err:
            errors = validation_err.json()
            raise ScenariosServiceIntegrationError(
                "Unable to assemble MRV Structured Events because data failed validation: " + errors,
                ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )

        events_by_phase, _, _ = fetch_events_for_simulations.adjust_overlapping_cropping_events_by_phase(
            events_by_phase
        )

        await fetch_events_for_simulations._validate_field_events_for_simulations(
            request=self.request, events_by_phase=events_by_phase, field_id=field.id
        )

        events = list(itertools.chain.from_iterable(events_by_phase.values()))

        # Group the events into cultivation cycles, splitting events that cross CC boundaries
        cultivation_cycles, unbinned_events = cc_methods.get_cultivation_cycles(
            events=events, generation_config=CultivationCycleGenerationConfig(should_split_irrigation_events=True)
        )
        if unbinned_events:
            # Log to keep an eye out for unbinnable events.
            unbinned_events_time_delta = cc_methods.get_unbinned_events_time_delta_from_cultivation_cycles(
                cultivation_cycles=cultivation_cycles, unbinned_events=unbinned_events
            )
            logger.exception(
                f"Field {field.id} has events {unbinned_events_time_delta[0].days} days before"
                + f" and {unbinned_events_time_delta[1].days} days after cultivation cycles."
            )

        # Make sure all crops used are biofuels_api_enabled
        await helpers.verify_crops_are_api_enabled(self.request, cultivation_cycles, ScenariosServiceApi.biofuels_api)

        # Get ApplicationEvents
        mrv_application_events: list[MRVApplicationEvent] = [
            event for event in events if isinstance(event, MRVApplicationEvent)
        ]

        # Make sure all products used are biofuels_api_enabled
        await self.verify_products_are_biofuels_api_enabled(
            mrv_application_events, await get_program_by_project_id(self.request, project_id=field.parent_project_id)
        )

        historical_ccs, monitored_ccs = await helpers.group_historical_and_monitored_cultivation_cycles(
            self.request, cultivation_cycles, field
        )
        final_cultivation_cycle = monitored_ccs[-1]
        location_data = await helpers.get_field_location_data(field.fs_field_id, allow_multipolygons=True)
        crop_name, crop_yield = await self._get_commodity_crop_name_yield(monitored_ccs[-1])

        application_events_nested = [
            await self.translate_application_event_to_ss_event(app_event)
            for app_event in final_cultivation_cycle.events
            if isinstance(app_event, MRVApplicationEvent)
        ]
        application_events = list(itertools.chain(*application_events_nested))

        try:
            return BiofuelsInput(
                field_name=str(field.id),
                crop_name=crop_name,
                crop_yield=crop_yield,
                boundary=location_data["boundary"],
                nitrogen_fertilizer=[
                    nitrogen_fert_event
                    for nitrogen_fert_event in application_events
                    if isinstance(nitrogen_fert_event, NitrogenFertilizer)
                ],
                manure=[manure_event for manure_event in application_events if isinstance(manure_event, Manure)],
                tillage=self._derive_tillage_practice(final_cultivation_cycle),
                cover_crop=self._derive_cover_crop_practice(final_cultivation_cycle),
            )
        except ValidationError as validation_err:
            errors = validation_err.json()
            error_code = ScenariosServiceIntegrationErrorCode.scenarios_service_validation_failure

            if "Self-intersection" in errors:
                error_code = ScenariosServiceIntegrationErrorCode.field_boundary_self_intersection
            raise ScenariosServiceIntegrationError(validation_err.json(), error_code)

    async def _get_commodity_crop_name_yield(self, cult_cycle: CultivationCycle) -> tuple[CropNameEnum, float | None]:
        commodity_crop_events = [
            ev
            for ev in cult_cycle.events[::-1]
            if isinstance(ev, CroppingEvent) and ev.crop_usage == CropUsage.COMMODITY
        ]
        if len(commodity_crop_events) > 1:
            raise ScenariosServiceIntegrationError(
                "Multiple reporting period commodity crops for field.",
                code=ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )
        com_crop_event = commodity_crop_events[0]
        biofuels_crop_type = self._get_biofuels_crop_type(com_crop_event.crop_type)

        crop_yield = (
            await com_crop_event.crop_yield.convert_value_to_unit(
                VolumeUnit.BUSHEL, AreaUnit.ACRE, com_crop_event.crop_type
            )
            if com_crop_event.crop_yield
            else None
        )
        return biofuels_crop_type, crop_yield

    @staticmethod
    def _get_biofuels_crop_type(crop_type: str) -> CropNameEnum:
        # Not handling KeyError because biofuels_api_enabled is checked upstream.
        return {
            "corn": CropNameEnum.CORN,
            "sorghum": CropNameEnum.SORGHUM,
            "soybeans": CropNameEnum.SOYBEAN,
            "wheat_spring": CropNameEnum.WHEAT_SPRING,
            "wheat_winter": CropNameEnum.WHEAT_WINTER,
        }[crop_type]

    @staticmethod
    def _derive_tillage_practice(cultivation_cycle: CultivationCycle) -> TillageEnum:
        """Determine which tillage practice was used in the last cultivation cycle."""
        if any(
            [
                ev
                for ev in cultivation_cycle.events
                if isinstance(ev, TillageEvent) and ev.tillage_practice.lower() == "conventional till"
            ]
        ):
            return TillageEnum.CONVENTIONAL_TILL
        if any(
            [
                ev
                for ev in cultivation_cycle.events
                if isinstance(ev, TillageEvent) and ev.tillage_practice.lower() == "reduced till"
            ]
        ):
            return TillageEnum.REDUCED_TILL
        # We expect No Till not to specify any TillageEvents, but this will work fine even if they are included.
        return TillageEnum.NO_TILL

    @staticmethod
    def _derive_cover_crop_practice(cultivation_cycle: CultivationCycle) -> bool:
        return any(
            [
                ev
                for ev in cultivation_cycle.events
                if isinstance(ev, CroppingEvent) and ev.crop_usage == CropUsage.COVER
            ]
        )

    async def translate_application_event_to_ss_event(
        self, application_event: MRVApplicationEvent
    ) -> list[Union[Manure, NitrogenFertilizer]]:
        """
        This function takes a provided MRVApplicationEvent, which can potentially represent an application of multiple
        products (which represents the real-world concept of a "tank-mix"), and converts it to a list of
        ManureEvents and NitrogenFertilizers. ManureEvents and NitrogenFertilizers represent only a single
        product each (plus additive(s)), so multiple events in the response are necessary.
        """
        ss_events = []
        actual_products = [p for p in application_event.products if p.product_name != NO_ADDITIVES_OPTION]
        # Only include Biofuels API-enabled products
        ss_additive_product_regrow_names = (
            [
                product_name
                for product_name in application_event.additives.split(",")
                if await defaults_translator.is_biofuels_api_enabled(product_name)
            ]
            if application_event.additives
            else []
        )

        fertilizer_products: list[ApplicationInput] = [
            product
            for product in actual_products
            if await defaults_translator.is_regrow_product_name_valid_fertilizer_product(product.product_name)
            and await self._get_biofuels_api_enabled_with_exceptions(product.product_name)
        ]
        for product in fertilizer_products:
            ss_events.append(
                await self._create_nitrogen_fertilizer(
                    product=product,
                    application_method=application_event.method,
                    ss_additives=ss_additive_product_regrow_names,
                )
            )

        organic_products: list[ApplicationInput] = [
            product
            for product in actual_products
            if await defaults_translator.is_regrow_product_name_valid_organic_product(product.product_name)
            and await self._get_biofuels_api_enabled_with_exceptions(product.product_name)
        ]
        for product in organic_products:
            ss_events.append(await self._create_manure_event(product))

        return ss_events

    async def _create_nitrogen_fertilizer(
        self,
        product: ApplicationInput,
        application_method: ApplicationMethod,
        ss_additives: list[str],
    ) -> NitrogenFertilizer:
        ss_nutrient_product_type = await defaults_translator.get_product_category_by_regrow_name(product.product_name)
        ss_additives = (
            ss_additives
            if ss_nutrient_product_type != NutrientProductType.EENF_INORGANIC
            and application_method != ApplicationMethod.FERTIGATION
            else []
        )
        return NitrogenFertilizer.parse_obj(
            {
                "name": product.product_name,
                "amount": await self._standardize_application_rate(product),
                "rate_unit": "kilogram",
                "area_unit": "hectare",
                "rate_type": self._translate_rate_type(product.application_rate.rate_type),
                "additives": ss_additives,
            }
        )

    async def _create_manure_event(self, product: ApplicationInput) -> Manure:
        return Manure.parse_obj(
            {
                "name": product.product_name,
                "amount": await self._standardize_application_rate(product),
                "rate_unit": "kilogram",
                "area_unit": "hectare",
                "rate_type": self._translate_rate_type(product.application_rate.rate_type),
            }
        )

    async def _standardize_application_rate(self, product: ApplicationInput) -> float:
        """
        Scenarios Service doesn't necessarily accept all the units that our events can use,
        so just convert everything to kg/ha.
        """
        application_rate = product.application_rate.value
        rate_unit_numerator = product.application_rate.numerator_unit
        starting_unit_denominator = product.application_rate.denominator_unit.value.lower()

        product_is_dry = await defaults_translator.is_regrow_product_name_dry(product.product_name)
        application_rate_quantity = Quantity(
            application_rate, rate_unit_numerator.value.lower() + "/" + starting_unit_denominator
        )

        # We collect volumetric units for liquid products, but Biofuels API only accepts mass units, so convert here.
        if not product_is_dry and product.application_rate.rate_type == ApplicationRateType.PRODUCT_RATE:
            if not isinstance(rate_unit_numerator, VolumeUnit):
                raise ScenariosServiceIntegrationError(
                    message="Unexpected rate unit for liquid product",
                    code=ScenariosServiceIntegrationErrorCode.invalid_management_data,
                )
            # Fetch liquid density in lbs per gallon
            liquid_density = await defaults_translator.get_regrow_product_name_liquid_density(product.product_name)
            # Convert to gallons/area
            application_rate_quantity = application_rate_quantity.to(f"gallons/{starting_unit_denominator}")
            # Convert to lbs/area
            application_rate_quantity = application_rate_quantity * Quantity(liquid_density, "lbs/gallon")
        elif isinstance(rate_unit_numerator, VolumeUnit):
            raise ScenariosServiceIntegrationError(
                "Invalid volumetric application rate unit", ScenariosServiceIntegrationErrorCode.invalid_management_data
            )
        return application_rate_quantity.to("kg/ha").magnitude

    def _translate_application_method(self, mrv_application_method: ApplicationMethod) -> ApplicationMethodEnum:
        if mrv_application_method in [ApplicationMethod.INCORPORATED]:
            raise ValueError(f"Unexpected application method: {mrv_application_method}")
        translations = {
            ApplicationMethod.BROADCASTED: ApplicationMethodEnum.BROADCAST,
            ApplicationMethod.INCORPORATED: ApplicationMethodEnum.INCORPORATE,
            ApplicationMethod.INJECTED: ApplicationMethodEnum.INJECT,
            ApplicationMethod.FERTIGATION: ApplicationMethodEnum.FERTIGATION_SUBSURFACE_DRIP,
            ApplicationMethod.FERTIGATION_FURROW: ApplicationMethodEnum.FERTIGATION_FURROW,
            ApplicationMethod.FERTIGATION_SPRINKLER: ApplicationMethodEnum.FERTIGATION_SPRINKLER,
            ApplicationMethod.FERTIGATION_DRIP: ApplicationMethodEnum.FERTIGATION_DRIP,
            ApplicationMethod.SUBSURFACE: ApplicationMethodEnum.SUBSURFACE,
        }
        return translations[mrv_application_method]

    @staticmethod
    def _translate_rate_type(mrv_rate_type: ApplicationRateType) -> RateTypeEnum:
        translations = {
            ApplicationRateType.PRODUCT_RATE: RateTypeEnum.PRODUCT,
            ApplicationRateType.NITROGEN_RATE: RateTypeEnum.NITROGEN,
        }
        return translations[mrv_rate_type]

    @staticmethod
    async def _get_biofuels_api_enabled_with_exceptions(product_name: str) -> bool:
        """
        monoammonium_phosphate, di_ammonium_phosphate, potassium_chloride, and lime are technically accepted by the
        Biofuels API but ESE has chosen to use the built-in defaults for these products rather than user values.
        This seems likely to be temporary.
        """
        if product_name in {"monoammonium_phosphate", "di_ammonium_phosphate", "potassium_chloride", "lime"}:
            return False
        return await defaults_translator.is_biofuels_api_enabled(product_name)

    @staticmethod
    async def verify_products_are_biofuels_api_enabled(
        mrv_application_events: list[MRVApplicationEvent],
        program: Programs,
    ) -> None:
        """
        Check all products in the MRVApplicationEvents to make sure they are marked enabled for modeling on the Defaults
        Service. Raise an integration error if not.
        """
        # Only verify products are supported on Biofuels-only programs, and exclude LDC from this check.
        # Unsupported products will be filtered out farther downstream when this check doesn't run.
        if program.accounting_method == AccountingMethod.biofuels and program.id != 1147:
            product_regrow_names = set()
            for event in mrv_application_events:
                product_regrow_names.update({product.product_name for product in event.products})

            enabled_by_product_name: dict[str, bool] = await defaults_translator.are_products_biofuels_api_enabled(
                product_regrow_names
            )
            products_not_enabled = [product for product, enabled in enabled_by_product_name.items() if not enabled]
            if products_not_enabled:
                error_message = f"Fertilizer products can't be submitted for modeling: {products_not_enabled} are not supported by Biofuels API."
                raise ScenariosServiceIntegrationError(
                    error_message, ScenariosServiceIntegrationErrorCode.unsupported_product_type
                )
