from typing import Any, Generator

from fastapi import Request

from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.db import (
    get_dndc_tasks_for_program,
    get_simulation_requests_for_tasks,
)
from scenarios_service.schema import (
    DndcSimulationRequest,
    DndcTaskWithSimulationRequests,
)


async def get_program_simulation_tasks(
    request: Request,
    program_id: int,
    scenarios_service_api: ScenariosServiceApi | None = None,
    eligible_field_count: int | None = None,
) -> list[DndcTaskWithSimulationRequests]:
    dndc_tasks = await get_dndc_tasks_for_program(
        request=request, program_id=program_id, scenarios_service_api=scenarios_service_api
    )
    if dndc_tasks is None:
        return []

    simulation_requests_for_program_tasks = await _get_simulation_requests_for_program_tasks(
        request, [dndc_task.id for dndc_task in dndc_tasks], scenarios_service_api
    )

    dndc_tasks_with_simulation_requests: list[DndcSimulationRequest] = []
    for dndc_task in dndc_tasks:
        # bandit thinks this might be a get call without a timeout
        # (https://cwe.mitre.org/data/definitions/400.html) which this is not
        simulation_requests_for_task = simulation_requests_for_program_tasks.get(dndc_task.id, [])  # nosec B113
        task_with_simulation_requests = DndcTaskWithSimulationRequests(
            **dndc_task.dict(), simulation_requests=simulation_requests_for_task
        )
        task_with_simulation_requests.eligible_field_count = eligible_field_count
        dndc_tasks_with_simulation_requests.append(task_with_simulation_requests)

    return dndc_tasks_with_simulation_requests


async def _get_simulation_requests_for_program_tasks(
    request: Request, task_ids: list[str], scenarios_service_api: ScenariosServiceApi | None
) -> dict[str, list[DndcSimulationRequest]]:
    simulation_requests_for_program_tasks: dict[str, list[DndcSimulationRequest]] = {}
    # take advantage of batching, but don't bring down the DB
    for task_chunk in _lst_to_chunks(task_ids, 50):
        simulation_requests_for_program_tasks.update(
            await get_simulation_requests_for_tasks(
                request=request, task_ids=task_chunk, scenarios_service_api=scenarios_service_api
            )
        )
    return simulation_requests_for_program_tasks


def _lst_to_chunks(lst: list[Any], chunk_size: int) -> Generator[list[Any], None, None]:
    for i in range(0, len(lst), chunk_size):
        yield lst[i : i + chunk_size]
