from datetime import datetime, timezone

from cultivation_cycles.schema import CultivationCycle
from programs.model import Programs


def filter_non_required_historical_cultivation_cycles(
    program: Programs, historical_ccs: list[CultivationCycle]
) -> list[CultivationCycle]:
    """
    Filters historical cultivation cycles based on the program's required_years_of_history. This is determined by
    calculating a date range: from the start of the year (n-1 years prior) to the end of the year of the latest
    historical cycle. Any cycles ending before this range are excluded.

    Note: This function assumes the cycles are already identified as historical, and are complete and sufficient for the
    required years.

    :param program: Program object containing required_years_of_history.
    :param historical_ccs: List of historical cultivation cycles.
    :return: Filtered list of historical cultivation cycles. Returns unfiltered list if required_years_of_history is not
             set.
    """
    if not program.required_years_of_history or not historical_ccs:
        return historical_ccs

    end_year = max(cc.end for cc in historical_ccs).year
    start_year = end_year - program.required_years_of_history + 1
    start_date = datetime(start_year, 1, 1, tzinfo=timezone.utc)

    return [cc for cc in historical_ccs if cc.end >= start_date]
