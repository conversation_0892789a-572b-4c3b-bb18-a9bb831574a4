SELECT mdsr.task_id,
       mdsr.field_id,
       MAX(mdcr.created_at)                                                                            AS created_at,
       mdcr.deleted_at                                                                                 AS deleted_at,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".ghg'))              AS ghg,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".ghg')) / (mrv_fields.area *  2.47105) as ghg_per_ac,
       JSON_EXTRACT(mdcr.original_results, CONCAT('$.fields_with_results.\"', field_id, '\".ghg'))     AS orig_ghg,
       JSON_EXTRACT(mdcr.original_results, CONCAT('$.fields_with_results.\"', field_id, '\".ghg')) / (mrv_fields.area *  2.47105) as orig_ghg_per_ac,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".multiplier'))       AS multiplier,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".override'))         AS override,
       JSON_EXTRACT(mdcr.results,
                    CONCAT('$.fields_with_results.\"', field_id, '\".lut_usage_reason'))               AS lut_usage_reason,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".error_code'))       AS error_code,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".case'))             AS case_,
       JSON_EXTRACT(mdcr.results, CONCAT('$.fields_with_results.\"', field_id, '\".error'))            AS error_message,
       mrv_projects.program_id,
       mrv_projects.contract_status,
       users.name                                                                                      AS user_name,
       users.surname                                                                                   AS user_surname,
       mrv_fields.parent_project_id                                                                    AS project_id,
       mrv_fields.area *  2.47105                                                                      AS area_ac
FROM (SELECT task_id, field_id, deleted_at, created_at, RANK() OVER (PARTITION BY field_id ORDER BY created_at DESC) field_rank
      FROM mrv_dndc_simulation_requests) mdsr
         JOIN mrv_dndc_contracted_results AS mdcr ON mdsr.task_id = mdcr.task_id
         JOIN mrv_fields ON mdsr.field_id = mrv_fields.id
         JOIN mrv_projects ON mrv_fields.parent_project_id = mrv_projects.id
         JOIN mrv_user_project_permissions ON mrv_projects.id = mrv_user_project_permissions.project and
                                              mrv_user_project_permissions.deleted_at is null
         JOIN users ON users.id = mrv_user_project_permissions.user
WHERE DATE (mdsr.created_at) > \"2024-04-29\" and field_rank = 1 GROUP BY mdsr.field_id;
