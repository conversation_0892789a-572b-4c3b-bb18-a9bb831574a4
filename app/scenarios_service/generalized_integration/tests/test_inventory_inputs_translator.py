from datetime import datetime, timezone
from typing import Any

import pytest
from scenarios_service_schema.schema import FieldLocation

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from defaults.tests import mock_defaults_translator
from entity_events.tests.helpers import create_structured_program_and_field
from fields.model import Fields
from fields.schema import Field
from programs.model import Programs
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.inventory_inputs_translator import (
    InventoryInputsTranslator,
)
from scenarios_service.generalized_integration.tests.conftest import (
    mock_fetch_field_geometry_and_area,
)


@pytest.fixture
def inventory_inputs_translator(app_request):
    return InventoryInputsTranslator(
        app_request,
    )


async def mock_are_crops_enabled(regrow_crop_names: list[str]) -> dict[str, bool]:
    return {crop_name: True for crop_name in regrow_crop_names}


def identity_fn(arg1: Any) -> Any:
    return arg1


async def test_get_inventory_input_for_field(
    mdl,
    mdl_factory_ignore_ids,
    orm_select,
    inventory_inputs_translator,
    comprehensive_mrv_values_lookup,
    mocker,
):
    # First set up a Structured Stages program so the config is compatible with the Structured Events Facade
    program_id, _, field_id = await create_structured_program_and_field(
        mdl, mdl_factory_ignore_ids, comprehensive_mrv_values_lookup
    )

    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == field_id]))[0])
    program: Programs = (await orm_select(Programs, where=[Programs.id == program_id]))[0]
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.ss_methods.fetch_field_geometry_and_area",
        side_effect=mock_fetch_field_geometry_and_area,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.input_translator_helpers.fetch_field_geometry_and_area",
        side_effect=mock_fetch_field_geometry_and_area,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
    )
    mock_get_start_year = mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.get_historical_data_start_year_for_field",
        return_value=2019,
    )

    session_input = await inventory_inputs_translator.get_inventory_input_for_field(
        field, ScenariosServiceApi.inventory_api, program
    )

    mock_get_start_year.assert_called()

    assert session_input.field_name == str(field_id)
    assert isinstance(session_input.location, FieldLocation)
    assert len(session_input.cultivation_cycles) == 6
    assert session_input.reporting_information.start_date == "2022-07-16"
    assert session_input.reporting_information.end_date == "2023-07-14"
    assert session_input.reporting_information.crop_name == "corn"
    assert session_input.reporting_information.crop_yield == 150.0
    assert session_input.start_year == 2018  # Earliest event date is used if less than start_year


async def test_get_inventory_input_earlier_start_year(
    mdl,
    mdl_factory_ignore_ids,
    orm_select,
    inventory_inputs_translator,
    comprehensive_mrv_values_lookup,
    mocker,
):
    # First set up a Structured Stages program so the config is compatible with the Structured Events Facade
    program_id, _, field_id = await create_structured_program_and_field(
        mdl, mdl_factory_ignore_ids, comprehensive_mrv_values_lookup
    )

    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == field_id]))[0])
    program: Programs = (await orm_select(Programs, where=[Programs.id == program_id]))[0]
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.ss_methods.fetch_field_geometry_and_area",
        side_effect=mock_fetch_field_geometry_and_area,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.input_translator_helpers.fetch_field_geometry_and_area",
        side_effect=mock_fetch_field_geometry_and_area,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
    )
    mock_get_start_year = mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.get_historical_data_start_year_for_field",
        return_value=2017,
    )

    session_input = await inventory_inputs_translator.get_inventory_input_for_field(
        field, ScenariosServiceApi.inventory_api, program
    )

    mock_get_start_year.assert_called()

    assert session_input.field_name == str(field_id)
    assert isinstance(session_input.location, FieldLocation)
    assert len(session_input.cultivation_cycles) == 7
    assert session_input.reporting_information.start_date == "2022-07-16"
    assert session_input.reporting_information.end_date == "2023-07-14"
    assert session_input.reporting_information.crop_name == "corn"
    assert session_input.reporting_information.crop_yield == 150.0
    assert session_input.start_year == 2017  # Crop stage start year is used if earlier than events


async def test_get_cultivation_cycles_min_event_date_no_events():
    start = datetime(2022, 1, 1)
    end = datetime(2022, 12, 1)
    min_date = InventoryInputsTranslator.get_cultivation_cycles_min_event_date(
        [CultivationCycle(id=CultivationCycleId(start_date=start, end_date=end), start=start, end=end)],
    )
    assert min_date == datetime.max.replace(tzinfo=timezone.utc)
