import itertools
import json
import uuid
from datetime import datetime
from typing import Any, Callable, Set, Union
from unittest.mock import Mock

import httpx
import pytest
from pydantic import BaseModel, root_validator
from scenarios_service_schema.biofuels import (
    BiofuelsInput,
    BiofuelsInterventionEnum,
    BiofuelsProjectMethodEnum,
    CropNameEnum,
    TillageEnum,
)
from scenarios_service_schema.examples import (
    SCOPE_1_ASSETS_RESPONSE,
    SCOPE_3_ASSETS_RESPONSE,
)
from scenarios_service_schema.explore_api import (
    BaselineMethodEnum,
    EmissionReductionEntity,
    EmissionReductionEntityWithConservativeDeduction,
    EmissionReductionEstimatesWithConservativeDeduction,
    ExploreAPIFieldSimulationResponseStatusEnum,
    ExploreAPIInput,
    ExploreAPIResultsResponse,
    ReportingInformationWithAreaAndCrop,
)
from scenarios_service_schema.inventory import (
    CultivationCycle as SSCultivationCycle,
    InventoryInput,
)
from scenarios_service_schema.public.intervention.v0.measure.input import (
    InterventionInput,
)
from scenarios_service_schema.schema import (
    CroppingEvent as SSCroppingEvent,
    Events,
    FieldLocation,
    ReportingInformation,
    Rotation,
    RotationPhase,
    Scenario,
    ScenarioTypeEnum,
    SessionInput,
    Soil,
)

from cultivation_cycles import methods as cc_methods
from cultivation_cycles.schema import CultivationCycle
from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    NO_ADDITIVES_OPTION,
    TerminationMethods,
)
from defaults.consts import RegrowCropName, RegrowProductName
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.schema import ApplicationInput, ApplicationRate
from entity_events.events.tillage_event import TillageEvent
from entity_events.events.yield_rate import YieldRate
from entity_events.measures import Depth, Interval
from entity_events.units import AreaUnit, LengthUnit, MassUnit, VolumeUnit
from fields.model import Fields
from fields.schema import Field
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs.schema import Program
from projects.model import Projects
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.biofuels_inputs_translator import (
    BiofuelsInputsTranslator,
)
from scenarios_service.generalized_integration.inventory_inputs_translator import (
    InventoryInputsTranslator,
)
from scenarios_service.generalized_integration.retrying_explore_api_client import (
    RetryingExploreApiClient,
)
from values.enums import EntityTypeChoices

TEST_INVENTORY_RESPONSE = {
    "crop_level": {
        "wheat_winter": {
            "crop_yield": 6332.422405761501,
            "number_of_fields": 1,
            "number_of_acres": 158.31056014403754,
            "direct_n2o_emissions": 13.369911577457772,
            "indirect_n2o_emissions": 5.780022118780792,
            "soil_ch4_emissions": 0,
            "total_emissions": 19.149933696238563,
            "total_emissions_per_bushel": 0.0030241087010896743,
            "soc": -43.69954593511091,
            "soc_per_bushel": -0.0069009208696108534,
            "ghg_emissions_factor": 0.0030241087010896743,
            "soc_emissions_factor": -0.0069009208696108534,
            "net_emissions_factor": -0.003876812168521179,
        }
    },
    "field_level": [
        {
            "field_name": "117764",
            "crediting_crop": "wheat_winter",
            "net_emissions_percentage": 100.0,
            "area_acres": 158.31056014403754,
            "crop_yield_bushels_per_acre": 40.0,
        }
    ],
    "field_level_value": [
        {
            "field_name": "117764",
            "crediting_crop": "wheat_winter",
            "net_emissions_factor": -0.005458378148988285,
            "area_acres": 11.6875,
            "crop_yield_bushels_per_acre": 40.0,
            "indirect_n2o_emissions": 1.0686627939253235,
            "direct_n2o_emissions": 2.1183609316717105,
            "ch4_emissions": -0.08163080782744209,
            "soc": -9.**************,
            "ghg_emissions_factor": 0.0026570206783055332,
            "soc_emissions_factor": -0.008115398827293818,
        }
    ],
}


@pytest.fixture(scope="function")
def mock_biofuels_inputs_translator(biofuels_input):
    mock = Mock(BiofuelsInputsTranslator, autospec=True)

    async def get_biofuels_input_for_field(field: Field) -> BiofuelsInput:
        assert field.id is not None
        biofuels_input.field_name = str(field.id)
        return biofuels_input

    mock.get_biofuels_input_for_field.side_effect = get_biofuels_input_for_field
    return mock


@pytest.fixture(scope="function")
def mock_inventory_inputs_translator(inventory_input):
    mock = Mock(InventoryInputsTranslator, autospec=True)

    async def get_inventory_input_for_field(
        field: Field, scenarios_service_api: ScenariosServiceApi, program: Program
    ) -> InventoryInput:
        assert field.id is not None
        inventory_input.field_name = str(field.id)
        return inventory_input

    mock.get_inventory_input_for_field.side_effect = get_inventory_input_for_field
    return mock


@pytest.fixture(scope="function")
def mock_measure_api_async_client():
    mock = Mock(autospec=True)

    async def create_project(project_name, x_consumer_id, protocol, baseline_method):
        assert not x_consumer_id == ""
        assert not project_name == ""
        return httpx.Response(status_code=200, content='{"data": {' f'"project_name":"{project_name}"}}')

    mock.create_project.side_effect = create_project

    async def submit_field(
        project_name: str, x_consumer_id: str, intervention_input: InterventionInput
    ) -> httpx.Response:
        return httpx.Response(
            status_code=200,
            content=json.dumps(
                {"data": {"project_name": project_name, "session_name": "", "field_request_id": 1234567}}
            ),
        )

    mock.submit_field.side_effect = submit_field

    async def finalize_project(project_name: str, x_consumer_id: str) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        return httpx.Response(status_code=200, content=json.dumps({"data": {"message": "Project finalized"}}))

    mock.finalize_project.side_effect = finalize_project

    async def project_level_scope_3(project_name: str, x_consumer_id: str, signed_url: bool) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        if signed_url:
            content = {"download_link": "mock_scope_3_link"}
        else:
            content = {SCOPE_3_ASSETS_RESPONSE}
        return httpx.Response(status_code=200, content=json.dumps(content))

    mock.project_level_scope_3.side_effect = project_level_scope_3

    async def project_level_scope_1(project_name: str, x_consumer_id: str, signed_url: bool) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        if signed_url:
            content = {"download_link": "mock_scope_1_link"}
        else:
            content = TEST_INVENTORY_RESPONSE
        return httpx.Response(status_code=200, content=json.dumps(content))

    mock.project_level_scope_1.side_effect = project_level_scope_1

    return mock


@pytest.fixture(scope="function")
def mock_biofuels_api_async_client():
    mock = Mock(autospec=True)

    async def create_project(
        project_name: str,
        x_consumer_id: str,
        method: BiofuelsProjectMethodEnum,
        interventions: Union[Set[BiofuelsInterventionEnum], None],
    ) -> httpx.Response:
        assert not x_consumer_id == ""
        assert not project_name == ""
        assert method == BiofuelsProjectMethodEnum.FDCIC
        return httpx.Response(status_code=200, content="{}")

    mock.create_project.side_effect = create_project

    async def submit_field(project_name: str, x_consumer_id: str, biofuels_input: BiofuelsInput) -> httpx.Response:
        assert x_consumer_id is not None and x_consumer_id != ""
        assert biofuels_input is not None
        return httpx.Response(
            status_code=200,
            content=json.dumps({"message": "Field submitted", "name": "12345678", "id": 1234567}),
        )

    mock.submit_field.side_effect = submit_field

    async def finalize_project(project_name: str, x_consumer_id: str) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        return httpx.Response(status_code=200, content="{}")

    mock.finalize_project.side_effect = finalize_project

    return mock


@pytest.fixture(scope="function")
def mock_inventory_api_async_client():
    mock = Mock(autospec=True)

    async def create_project(
        project_name: str,
        x_consumer_id: str,
    ) -> httpx.Response:
        assert not x_consumer_id == ""
        assert not project_name == ""
        return httpx.Response(status_code=200, content="{}")

    mock.create_project.side_effect = create_project

    async def submit_field(project_name: str, x_consumer_id: str, inventory_input: InventoryInput) -> httpx.Response:
        return httpx.Response(
            status_code=200,
            content=json.dumps(
                {
                    "data": {
                        "id": inventory_input.field_name,
                        "project_name": project_name,
                        "session_name": "",
                        "field_request_id": 1234567,
                    }
                }
            ),
        )

    mock.submit_field.side_effect = submit_field

    async def finalize_project(project_name: str, x_consumer_id: str) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        return httpx.Response(status_code=200, content="{}")

    mock.finalize_project.side_effect = finalize_project

    async def get_project_results(project_name: str, x_consumer_id: str, signed_url: bool) -> httpx.Response:
        assert project_name is not None and project_name != ""
        assert x_consumer_id is not None and x_consumer_id != ""
        if signed_url:
            content = {"download_link": "mock_inventory_link"}
        else:
            content = {SCOPE_1_ASSETS_RESPONSE}
        return httpx.Response(status_code=200, content=json.dumps(content))

    mock.get_project_results.side_effect = get_project_results

    return mock


@pytest.fixture(scope="function")
def mock_explore_api_async_client(faker):
    """This is a reusable mock implementation of the RetryingExploreApiClient. Because it uses autospec, the mock has the
    same API as the implementation. We implement basic versions of the functions that perform simple input validation
    and return reasonable responses without having to rewrite the mocks in every test. These side_effects can be removed
    or overwritten if desired. We also expose additional functions that allow inspection and configuration of the mock.
    """
    mock = Mock(RetryingExploreApiClient, autospec=True)

    fields_uploaded = []
    mock_configs = {"status_to_return": ExploreAPIFieldSimulationResponseStatusEnum.AVAILABLE}

    async def upload_field(session_input: ExploreAPIInput, ss_consumer_id: str) -> httpx.Response:
        assert ss_consumer_id is not None and not ss_consumer_id == ""
        field_request_id = faker.random_number(4)
        fields_uploaded.append(field_request_id)
        return httpx.Response(
            status_code=200,
            content=json.dumps({"name": session_input.session_name, "id": field_request_id}),
        )

    def get_latest_field_request_id():
        return fields_uploaded[-1]

    async def get_field_simulation_status(ss_field_request_id: int, ss_consumer_id: str) -> httpx.Response:
        assert ss_consumer_id is not None and not ss_consumer_id == ""
        if ss_field_request_id not in fields_uploaded:
            raise AssertionError("Simulation status was requested for a field that wasn't submitted.")
        return httpx.Response(
            status_code=200, content=json.dumps({"response_status": mock_configs["status_to_return"]})
        )

    def set_simulation_statuses_to_pending():
        mock_configs["status_to_return"] = ExploreAPIFieldSimulationResponseStatusEnum.AVAILABLE

    async def get_field_results(ss_field_request_id: int, ss_consumer_id: str) -> httpx.Response:
        assert ss_consumer_id is not None and not ss_consumer_id == ""
        if ss_field_request_id not in fields_uploaded:
            raise AssertionError("Simulation results were requested for a field that wasn't submitted.")

        return httpx.Response(
            status_code=200,
            content=json.dumps(
                ExploreAPIResultsResponse(
                    field_name="task_x_field_y",
                    reporting_information=ReportingInformationWithAreaAndCrop(
                        start_date="2020-01-01", end_date="2021-01-01", crop_name="barley", acres=10
                    ),
                    field_id=ss_field_request_id,
                    conservative_deduction=0.25,
                    emissions_reductions_estimates=EmissionReductionEstimatesWithConservativeDeduction(
                        total_emissions_reductions_estimate=1234.50,
                        dsoc=EmissionReductionEntityWithConservativeDeduction.from_emission_reduction_entity(
                            EmissionReductionEntity(baseline=1, practice_change=2, offset=1),
                            conservative_deduction_percent=25,
                        ),
                        indirect_n2o=EmissionReductionEntityWithConservativeDeduction.from_emission_reduction_entity(
                            EmissionReductionEntity(baseline=1, practice_change=2, offset=1),
                            conservative_deduction_percent=25,
                        ),
                        direct_n2o=EmissionReductionEntityWithConservativeDeduction.from_emission_reduction_entity(
                            EmissionReductionEntity(baseline=1, practice_change=2, offset=1),
                            conservative_deduction_percent=25,
                        ),
                        soil_ch4=EmissionReductionEntityWithConservativeDeduction.from_emission_reduction_entity(
                            EmissionReductionEntity(baseline=1, practice_change=2, offset=1),
                            conservative_deduction_percent=25,
                        ),
                    ),
                ).dict()
            ),
        )

    # Inspection functions
    mock.set_simulation_statuses_to_pending = set_simulation_statuses_to_pending
    mock.get_latest_field_request_id = get_latest_field_request_id

    # Mock implementations of client functions
    mock.get_field_results.side_effect = get_field_results
    mock.get_field_simulation_status.side_effect = get_field_simulation_status
    mock.upload_field.side_effect = upload_field

    return mock


@pytest.fixture
def explore_api_input(session_input):
    return ExploreAPIInput.from_session_input(
        session_input=session_input,
        baseline_method=BaselineMethodEnum.BLENDED,
        optional_ghg_boundaries=[],
        conservative_deduction=0.25,
    )


@pytest.fixture
def session_input(faker):
    field_id = faker.random_number(6)
    task_id = str(uuid.uuid4())
    return SessionInput(
        session_name=f"field_{field_id}_task_{task_id}",
        reporting_information=ReportingInformation(crop_name="corn", start_date="2021-09-04", end_date="2021-09-06"),
        scenarios=[
            Scenario(
                name=f"{task_id}_{field_id}_baseline_scenario",
                location=FieldLocation(
                    latlon=None,
                    boundary={
                        "type": "Polygon",
                        "coordinates": [
                            [
                                [149.079053, -21.231402],
                                [149.078666, -21.231351],
                                [149.078636, -21.23129],
                                [149.078908, -21.229404],
                                [149.078944, -21.22937],
                                [149.079352, -21.229102],
                                [149.080391, -21.228439],
                                [149.081352, -21.227752],
                                [149.080833, -21.231613],
                                [149.079973, -21.231518],
                                [149.079053, -21.231402],
                            ]
                        ],
                    },
                    area=4.71,
                    area_unit="hectare",
                ),
                soil=Soil(topsoil_depth=30, total_depth=50),
                rotations=[
                    Rotation(
                        phases=[
                            RotationPhase(
                                start_date="2017-09-04",
                                end_date="2021-09-03",
                                management_events=Events(
                                    fertilizer=[],
                                    irrigation=[],
                                    organic_amendment=[],
                                    till=[],
                                    graze=[],
                                    flood=[],
                                    cropping=[
                                        SSCroppingEvent(
                                            start_date="2020-09-04",
                                            end_date="2020-09-06",
                                            name="corn",
                                        )
                                    ],
                                ),
                            )
                        ]
                    )
                ],
                weather_sources=["PRISM"],
                soil_sources=["SSURGO"],
                scenario_type=ScenarioTypeEnum.BASELINE,
                area_unit="hectare",
            ),
            Scenario(
                name="f{task_id}_{field_id}_project_scenario",
                location=FieldLocation(
                    latlon=None,
                    boundary={
                        "type": "Polygon",
                        "coordinates": [
                            [
                                [149.079053, -21.231402],
                                [149.078666, -21.231351],
                                [149.078636, -21.23129],
                                [149.078908, -21.229404],
                                [149.078944, -21.22937],
                                [149.079352, -21.229102],
                                [149.080391, -21.228439],
                                [149.081352, -21.227752],
                                [149.080833, -21.231613],
                                [149.079973, -21.231518],
                                [149.079053, -21.231402],
                            ]
                        ],
                    },
                    area=4.71,
                    area_unit="hectare",
                ),
                size=None,
                soil=Soil(topsoil_depth=30, total_depth=50),
                rotations=[
                    Rotation(
                        phases=[
                            RotationPhase(
                                start_date="2017-09-04",
                                end_date="2026-09-06",
                                management_events=Events(
                                    fertilizer=[],
                                    irrigation=[],
                                    organic_amendment=[],
                                    till=[],
                                    graze=[],
                                    flood=[],
                                    cropping=[
                                        SSCroppingEvent(
                                            start_date="2021-09-04",
                                            end_date="2021-09-06",
                                            name="barley",
                                        )
                                    ],
                                ),
                            )
                        ]
                    )
                ],
                weather_sources=["PRISM"],
                soil_sources=["SSURGO"],
                scenario_type=ScenarioTypeEnum.PRACTICE_CHANGE,
                area_unit="hectare",
            ),
        ],
    )


@pytest.fixture
def biofuels_input(faker):
    field_id = faker.random_number(6)
    return BiofuelsInput(
        field_name=str(field_id),
        crop_name=CropNameEnum.CORN,
        crop_yield=1.0,
        boundary={
            "type": "Polygon",
            "coordinates": [
                [
                    [149.079053, -21.231402],
                    [149.078666, -21.231351],
                    [149.078636, -21.23129],
                    [149.078908, -21.229404],
                    [149.078944, -21.22937],
                    [149.079352, -21.229102],
                    [149.080391, -21.228439],
                    [149.081352, -21.227752],
                    [149.080833, -21.231613],
                    [149.079973, -21.231518],
                    [149.079053, -21.231402],
                ]
            ],
        },
        fertilizer_events=[],
        organic_amendment_events=[],
        tillage=TillageEnum.CONVENTIONAL_TILL,
        cover_crop=True,
    )


@pytest.fixture
def inventory_input(faker, create_cropping_event_data):
    field_id = faker.random_number(6)
    task_id = str(uuid.uuid4())
    return InventoryInput(
        field_name=f"field_{field_id}_task_{task_id}",
        location=FieldLocation(
            latlon=None,
            boundary={
                "type": "Polygon",
                "coordinates": [
                    [
                        [149.079053, -21.231402],
                        [149.078666, -21.231351],
                        [149.078636, -21.23129],
                        [149.078908, -21.229404],
                        [149.078944, -21.22937],
                        [149.079352, -21.229102],
                        [149.080391, -21.228439],
                        [149.081352, -21.227752],
                        [149.080833, -21.231613],
                        [149.079973, -21.231518],
                        [149.079053, -21.231402],
                    ]
                ],
            },
            area=4.71,
            area_unit="hectare",
        ),
        soil=Soil(topsoil_depth=30, total_depth=50),
        cultivation_cycles=[
            SSCultivationCycle(
                start_date="2017-09-04",
                end_date="2026-09-06",
                management_events=Events(
                    fertilizer=[],
                    irrigation=[],
                    organic_amendment=[],
                    till=[],
                    graze=[],
                    flood=[],
                    cropping=[
                        SSCroppingEvent(
                            start_date="2020-09-04",
                            end_date="2020-09-06",
                            name="corn",
                        )
                    ],
                ),
            )
        ],
        reporting_information=ReportingInformation(
            crop_name="corn", start_date="2021-09-04", end_date="2021-09-06", crop_yield=2.0
        ),
    )


async def mock_fetch_field_geometry_and_area(field_id: int) -> tuple[dict, float]:
    return (
        {
            "type": "Polygon",
            "coordinates": [
                [
                    [-109.072265625, 37.020098201368114],
                    [-102.0849609375, 37.020098201368114],
                    [-102.0849609375, 41.04621681452063],
                    [-109.072265625, 41.04621681452063],
                    [-109.072265625, 37.020098201368114],
                ]
            ],
        },
        5.0,
    )


@pytest.fixture
def get_biofuels_field_events() -> Callable[[Any], dict[PhaseTypes, list[EntityEvent]]]:
    """Returns a function that returns events_by_phase with data relevant to the Biofuels integration"""

    def _biofuels_field_events(
        field_id: int, max_tillage: str = "conventional till", cover_crop: bool = True
    ) -> dict[PhaseTypes, list[EntityEvent]]:
        phase_events_lookup = {
            # For a single-year Biofuels program all events should be in the M Phase
            PhaseTypes.ENROLMENT: [],
            PhaseTypes.MONITORING: [
                # Irrigation events are not used by the Biofuels API.
                # CROPPING EVENTS:
                CroppingEvent(
                    entity_id=field_id,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(start=datetime(2023, 5, 1), end=datetime(2023, 10, 1)),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=2.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                ),
                (
                    CroppingEvent(
                        entity_id=field_id,
                        entity_type=EntityTypeChoices.field,
                        interval=Interval(start=datetime(2022, 12, 1), end=datetime(2023, 3, 1)),
                        crop_type=RegrowCropName.basic_cover_crop,
                        crop_usage=CropUsage.COVER,
                    )
                    if cover_crop
                    else None
                ),
                # TILLAGE EVENTS:
                (
                    TillageEvent(
                        entity_id=field_id,
                        entity_type=EntityTypeChoices.field,
                        occurred_at=datetime(2021, 11, 1),
                        depth=Depth(value=8.0, unit=LengthUnit.INCH),
                        soil_inversion=True,
                        strip_fraction=None,
                        tillage_practice="conventional till",
                    )
                    if max_tillage == "conventional till"
                    else None
                ),
                (
                    TillageEvent(
                        entity_id=field_id,
                        entity_type=EntityTypeChoices.field,
                        occurred_at=datetime(2022, 4, 1),
                        depth=Depth(value=2.0, unit=LengthUnit.INCH),
                        soil_inversion=False,
                        strip_fraction=0.5,
                        tillage_practice="reduced till",
                    )
                    if max_tillage != "no till"
                    else None
                ),
                (
                    TillageEvent(
                        entity_id=field_id,
                        entity_type=EntityTypeChoices.field,
                        occurred_at=datetime(2022, 11, 1),
                        depth=Depth(value=8.0, unit=LengthUnit.INCH),
                        soil_inversion=True,
                        strip_fraction=None,
                        tillage_practice="conventional till",
                    )
                    if max_tillage == "conventional till"
                    else None
                ),
                (
                    TillageEvent(
                        entity_id=field_id,
                        entity_type=EntityTypeChoices.field,
                        occurred_at=datetime(2023, 4, 1),
                        depth=Depth(value=2.0, unit=LengthUnit.INCH),
                        soil_inversion=False,
                        strip_fraction=None,
                        tillage_practice="reduced till",
                    )
                    if max_tillage != "no till"
                    else None
                ),
                # APPLICATION EVENTS:
                ApplicationEvent(
                    entity_id=field_id,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2023, 6, 1),
                    method=ApplicationMethod.SUBSURFACE,
                    depth=Depth(value=2.0, unit=LengthUnit.INCH),
                    products=[
                        ApplicationInput(
                            product_name=RegrowProductName.ammonia_aqueous,
                            application_rate=ApplicationRate(
                                value=5,
                                numerator_unit=MassUnit.POUND,
                                denominator_unit=AreaUnit.HECTARE,
                                rate_type=ApplicationRateType.NITROGEN_RATE,
                            ),
                        )
                    ],
                ),
            ],
        }

        # Filter out the Nones and return
        return {phase: [ev for ev in events if ev is not None] for phase, events in phase_events_lookup.items()}

    return _biofuels_field_events


@pytest.fixture
def get_biofuels_cultivation_cycles(get_biofuels_field_events) -> Callable[[Any], list[CultivationCycle]]:
    def _biofuels_cultivation_cycles(
        field_id: int, max_tillage: str = "conventional till", cover_crop: bool = True
    ) -> list[CultivationCycle]:
        events = list(
            itertools.chain.from_iterable(get_biofuels_field_events(field_id, max_tillage, cover_crop).values())
        )
        cultivation_cycles, _ = cc_methods.get_cultivation_cycles(events=events)
        return cultivation_cycles

    return _biofuels_cultivation_cycles


class AlwaysInvalidModel(BaseModel):
    @root_validator
    @classmethod
    def always_invalid(cls, values: dict[str, Any]) -> dict[str, Any]:
        raise ValueError("It's invalid")


def instantiate_invalid_model(*args, **kwargs) -> None:
    # Throws pydantic.ValidationError
    AlwaysInvalidModel()


# Copied from entity-events/conftest.py
# It doesn't really make sense for the GI to know about MRV Values representations, so this should be converted to an
# event representation when possible. Duplicating this here is a first step in decoupling GI from Facade.
@pytest.fixture
def comprehensive_mrv_values_lookup() -> dict[PhaseTypes, dict[StageTypes, dict[AttributeTypes, dict[int, str]]]]:
    return {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_TILLAGE: {
                AttributeTypes.tillage_event: {
                    0: "0",
                    1: "1",
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "1",
                    6: "1",
                },
                AttributeTypes.tillage_date: {
                    # 0: XXX, # There would be no value as a result of the tillage_event dependency
                    1: "2021-01-06T08:00:00.000Z",
                    2: "2020-01-06T08:00:00.000Z",
                    3: "2019-01-01T08:00:00.000Z",
                    4: "2018-01-03T08:00:00.000Z",
                    5: "2021-08-15T07:00:00.000Z",
                    6: "2019-08-14T07:00:00.000Z",
                },
                AttributeTypes.tillage_depth: {
                    # 0: XXX, # There would be no value as a result of the tillage_event dependency
                    1: "6",
                    2: "18",
                    3: "25",
                    4: "38.1",
                    5: "3",
                    6: "6",
                },
                AttributeTypes.soil_inversion: {
                    # 0: XXX,  # There would be no value as a result of the tillage_event dependency
                    1: "0",
                    2: "0",
                    3: "1",  # Changed this in Prod to 1
                    4: "1",  # Changed this in Prod to 1
                    5: "0",
                    6: "0",
                },
                AttributeTypes.strip_fraction: {
                    # This attribute isn't usable in the UI, so I'm manually adding this here
                    # 0: XXX,  # There would be no value as a result of the tillage_event dependency
                    1: ".25",
                    2: ".5",
                    3: ".75",
                    4: ".5",
                    5: ".25",
                    6: ".5",
                },
            },
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2022-02-01T08:00:00.000Z",
                    1: "2021-02-03T08:00:00.000Z",
                    2: "2020-02-02T08:00:00.000Z",
                    3: "2019-02-01T08:00:00.000Z",
                    4: "2018-02-01T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2022-07-15T07:00:00.000Z",
                    1: "2021-07-14T07:00:00.000Z",
                    2: "2020-07-06T07:00:00.000Z",
                    3: "2019-07-15T07:00:00.000Z",
                    4: "2018-07-15T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "sorghum",
                    1: "corn",
                    2: "soybeans",
                    3: "wheat_winter",
                    4: "fallow",
                },
                AttributeTypes.crop_yield: {
                    0: "125",
                    # 1: "150",  # There would be no value here because Termination method is Herbicide
                    2: "185.500",
                    3: "20",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.yield_rate_unit: {
                    0: "bu/ac",
                    # 1: "T/ac",  #  # There would be no value here because Termination method is Herbicide
                    2: "bu/ac",
                    3: "bu/ac",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.winter_crop_termination: {
                    0: None,  # Because crop_usage is Commodity
                    1: TerminationMethods.herbicide,
                    2: TerminationMethods.grain_harvest,
                    3: None,  # Because crop_usage is Commodity
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.crop_usage: {
                    0: "Commodity",
                    1: "Cover",
                    2: "Cover",
                    3: "Commodity",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.residue_harvested: {
                    0: "All residue harvested",
                    1: None,  # Because crop_usage is Cover
                    2: None,  # Because crop_usage is Cover
                    3: "75% Harvested",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.planting_method: {
                    0: "Transplant (wet)",
                    1: "Direct seeding (wet)",
                    2: "Direct seeding (dry)",
                    # 4: None, # There would be no value here because crop is fallow
                },
            },
            StageTypes.NUTRIENT_MGMT: {
                AttributeTypes.nutrient_management_enabled: {
                    0: "1",
                    1: "1",
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "0",
                    # Added this to Prod as an additional row for 2018. As a result, NULL values inserted for other values in row.
                },
                AttributeTypes.application_date: {
                    0: "2022-06-26T07:00:00.000Z",  # Changed this in prod from /2023
                    1: "2021-06-30T07:00:00.000Z",  # Changed this is prod from /2022
                    2: "2020-06-15T07:00:00.000Z",
                    3: "2019-06-18T07:00:00.000Z",
                    4: "2018-06-13T07:00:00.000Z",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_product: {
                    0: "aqamm",
                    1: "tap",
                    2: "ammbic",
                    3: "ammnit",
                    4: "uan",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_rate: {
                    0: "4",
                    1: "15",
                    2: "15",
                    3: "23",
                    4: "30",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_rate_unit: {
                    0: "qt1ac-1",  # Note that this is a frontend-hardcoded-only option
                    1: "lb1ac-1",
                    2: "lb1ac-1",
                    3: "lb1ac-1",  # Changed this in prod from nlb1ac-1, since we aren't going to support it
                    4: "gal1ac-1",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_method: {
                    0: "Injected",
                    1: "Fertigation",
                    2: "Broadcasted",  # Changed this in prod from Fertigation
                    3: "Fertigation",
                    4: "Fertigation",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_depth: {
                    0: "11",
                    1: "11",
                    # 2: XXX, # There would be no Row 2 value because of the broadcast dependency
                    3: "18",
                    4: "6",
                    5: None,
                },
                AttributeTypes.water_amount: {
                    # 0: 13, # There would be no Row 0 value because of the injection dependency
                    1: 15,
                    # 2: 15,  # There would be no Row 2 value because of the broadcast dependency
                    3: 15,
                    4: 15,
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.water_amount_unit: {
                    # 0: "gal",  # There would be no Row 0 value because of the injection dependency
                    1: "in",
                    # 2: "mm",  # There would be no Row 2 value because of the broadcast dependency
                    3: "mm",
                    4: "gal",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.additives: {
                    0: "anvol",
                    1: "N-SERVE",
                    2: "N-SERVE",
                    3: "anvol,N-SERVE",
                    4: NO_ADDITIVES_OPTION,
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
            },
            StageTypes.IRRIGATION: {
                AttributeTypes.irrigation_enabled: {
                    0: "1",
                    1: "0",  # Changed this in prod from 1 -> 0
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "1",
                    6: "1",
                },
                AttributeTypes.start_date: {
                    0: "2022-06-01T07:00:00.000Z",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "2020-05-01T07:00:00.000Z",
                    3: "2019-04-13T07:00:00.000Z",
                    4: "2018-05-03T07:00:00.000Z",
                    5: "2020-05-16T07:00:00.000Z",
                    6: "2020-05-27T07:00:00.000Z",
                },
                AttributeTypes.end_date: {
                    0: "2022-06-08T07:00:00.000Z",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "2020-05-08T07:00:00.000Z",
                    3: "2019-04-17T07:00:00.000Z",
                    4: "2018-06-10T07:00:00.000Z",
                    5: "2020-05-20T07:00:00.000Z",
                    6: "2020-05-31T07:00:00.000Z",
                },
                AttributeTypes.irrigation_method: {
                    0: "Furrow",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "Flood",
                    3: "Subsurface drip",
                    4: "Flood",
                    5: "Flood",
                    6: "Sprinkler",  # Changed in prod from Flood -> Sprinkler
                },
                AttributeTypes.subsurface_drip_depth: {
                    # 0: XXX,  # There won't be a value for Row 0 because of the dependency on irrigation_method
                    # 1: XXX, # There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    3: "3",
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
                AttributeTypes.subsurface_drip_depth_unit: {
                    # 0: XXX,  # There won't be a value for Row 0 because of the dependency on irrigation_method
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    3: "cm",
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
                AttributeTypes.flood_pct: {
                    0: "45",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    # 3: XXX,  # There won't be a value for Row 3 because of the dependency on irrigation_method
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
            },
            StageTypes.INTENDED_COMMODITY_CROPS: {
                AttributeTypes.planting_date: {
                    0: "2023-02-01T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2023-07-15T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "sorghum",
                },
            },
        },
        PhaseTypes.MONITORING: {
            StageTypes.HISTORICAL_TILLAGE: {
                AttributeTypes.tillage_event: {
                    0: "1",
                },
                AttributeTypes.tillage_date: {
                    0: "2023-01-06T08:00:00.000Z",
                },
                AttributeTypes.tillage_depth: {
                    0: "6",
                },
                AttributeTypes.soil_inversion: {
                    0: "0",
                },
                AttributeTypes.strip_fraction: {
                    0: ".25",
                },
            },
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2023-02-03T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2023-07-14T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "corn",
                },
                AttributeTypes.crop_yield: {
                    0: "150",
                },
                AttributeTypes.yield_rate_unit: {
                    0: "bu/ac",
                },
                AttributeTypes.winter_crop_termination: {
                    # 0: "",  # There would be no value here because Crop Usage is Commodity
                },
                AttributeTypes.crop_usage: {
                    0: "Commodity",
                },
                AttributeTypes.residue_harvested: {
                    0: "All residue harvested",
                },
            },
            StageTypes.NUTRIENT_MGMT: {
                AttributeTypes.nutrient_management_enabled: {
                    0: "1",
                },
                AttributeTypes.application_date: {
                    0: "2023-06-26T07:00:00.000Z",
                },
                AttributeTypes.application_product: {
                    0: "aqamm",
                },
                AttributeTypes.application_rate: {
                    0: "4",
                },
                AttributeTypes.application_rate_unit: {
                    0: "qt1ac-1",
                },
                AttributeTypes.application_method: {
                    0: "Injected",
                },
                AttributeTypes.application_depth: {
                    0: "11",
                },
                AttributeTypes.water_amount: {
                    # 0: 13, # There would be no Row 0 value because of the injection dependency
                },
                AttributeTypes.water_amount_unit: {
                    # 0: "gal",  # There would be no Row 0 value because of the injection dependency
                },
                AttributeTypes.additives: {
                    0: "anvol",
                },
            },
            StageTypes.IRRIGATION: {
                AttributeTypes.irrigation_enabled: {
                    0: "1",
                },
                AttributeTypes.start_date: {
                    0: "2023-06-01T07:00:00.000Z",
                },
                AttributeTypes.end_date: {
                    0: "2023-06-08T07:00:00.000Z",
                },
                AttributeTypes.irrigation_method: {
                    0: "Subsurface drip",
                },
                AttributeTypes.subsurface_drip_depth: {
                    0: 10,
                },
                AttributeTypes.subsurface_drip_depth_unit: {
                    0: "cm",
                },
                AttributeTypes.flood_pct: {
                    0: "45",
                },
            },
        },
    }


@pytest.fixture
def fallows_mrv_values_lookup() -> dict:
    return {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2022-02-01T08:00:00.000Z",
                    1: None,
                    2: "2020-02-02T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2022-07-15T07:00:00.000Z",
                    1: "2021-07-14T07:00:00.000Z",
                    2: None,
                },
                AttributeTypes.crop_type: {
                    0: "fallow",
                    1: "no cover",  # equivalent to fallow
                    2: "fallow",
                },
            }
        }
    }


@pytest.fixture
def mock_reporting_info() -> ReportingInformation:
    return ReportingInformation(
        start_date="2023-10-10",
        end_date="2024-09-15",
        crop_name="barley",
        crop_yield=10.2,
    )


@pytest.fixture
def mock_field_location_data() -> dict:
    return {"boundary": {"type": "Polygon", "coordinates": [[[1, 1], [1, 2], [2, 2], [2, 1], [1, 1]]]}}


def mock_adjust_overlaps(
    events_by_phase: dict[PhaseTypes, list[EntityEvent]],
) -> tuple[dict[PhaseTypes, list[EntityEvent]], list[EntityEvent], list[EntityEvent]]:
    return events_by_phase, [], []


@pytest.fixture
async def project(mdl) -> Projects:
    program = await mdl.Programs()
    return await mdl.Projects(program_id=program.id)


@pytest.fixture
async def field(mdl, project) -> Fields:
    return await mdl.Fields(parent_project_id=project.id)
