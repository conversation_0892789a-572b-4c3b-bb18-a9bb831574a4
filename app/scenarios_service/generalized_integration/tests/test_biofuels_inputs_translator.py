from typing import Any
from unittest.mock import patch

import pytest
from scenarios_service_schema.biofuels import (
    BiofuelsInput,
    CropNameEnum,
    Manure,
    NitrogenFertilizer,
    RateTypeEnum,
    TillageEnum,
)

from defaults.attribute_options import ApplicationMethod, ApplicationRateType
from defaults.consts import RegrowProductName
from defaults.tests import mock_defaults_translator
from defaults.tests.mock_defaults_translator import mock_is_regrow_product_name_dry
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.schema import ApplicationInput, ApplicationRate
from entity_events.units import AreaUnit, MassUnit, VolumeUnit
from fields.model import Fields
from fields.schema import Field
from programs.enums import AccountingMethod, ProgramTemplate
from scenarios_service.generalized_integration.biofuels_inputs_translator import (
    BiofuelsInputsTranslator,
)
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.generalized_integration.tests.conftest import (
    instantiate_invalid_model,
    mock_adjust_overlaps,
    mock_fetch_field_geometry_and_area,
)
from scenarios_service.model import ScenariosServiceIntegrationErrorCode


@pytest.fixture
def biofuels_inputs_translator(app_request):
    return BiofuelsInputsTranslator(
        app_request,
    )


async def mock_are_crops_enabled(regrow_crop_names: list[str]) -> dict[str, bool]:
    return {crop_name: True for crop_name in regrow_crop_names}


async def mock_are_products_biofuels_api_enabled(regrow_product_names: list[str]) -> dict[str, bool]:
    return {product_name: True for product_name in regrow_product_names}


async def mock_products_not_biofuels_api_enabled(regrow_product_names: list[str]) -> dict[str, bool]:
    return {product_name: False for product_name in regrow_product_names}


def identity_fn(arg1: Any) -> Any:
    return arg1


async def test_translate_application_event(
    mocker,
    biofuels_inputs_translator,
    create_application_event_data,
    create_application_products_data,
    create_application_rate_data,
):
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_dry",
        mock_defaults_translator.mock_is_regrow_product_name_dry,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_valid_additive_product",
        mock_defaults_translator.mock_is_regrow_product_name_valid_additive_product,
    )
    mocker.patch("scenarios_service.methods", mock_fetch_field_geometry_and_area)
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_biofuels_api_enabled",
        mock_defaults_translator.mock_is_biofuels_api_enabled,
    )

    # Basic test cases
    # Broadcast of a dry basic_inorganic
    application_event_basic_inorg = ApplicationEvent.parse_obj(
        create_application_event_data(
            products=create_application_products_data(main_product_name=RegrowProductName.ammonium_nitrate),
            additives="anvol,centuro",
        )
    )
    # Dry OA product
    application_event_organic = ApplicationEvent.parse_obj(
        create_application_event_data(
            products=create_application_products_data(
                main_product_name=RegrowProductName.manure_poultry_solid,
                main_product_application_rate=create_application_rate_data(rate_type=ApplicationRateType.NITROGEN_RATE),
            ),
        ),
    )

    # Interesting test cases
    # 1. An ApplicationEvent with multiple "main" products
    # Broadcast of two dry basic inorganic products
    two_main_products_data = [
        *create_application_products_data(),
        *create_application_products_data(main_product_name=RegrowProductName.ammonium_bicarbonate),
    ]
    application_event_two_main_products = ApplicationEvent.parse_obj(
        {**create_application_event_data(products=two_main_products_data), "additives": "anvol,centuro"}
    )

    # 2. An EENF product or fertigation application method should have its additives stripped out
    application_event_fertigated = ApplicationEvent.parse_obj(
        create_application_event_data(method=ApplicationMethod.FERTIGATION)
    )
    application_event_eenf = ApplicationEvent.parse_obj(
        create_application_event_data(
            products=create_application_products_data(main_product_name="superu"), additives="anvol,centuro"
        )
    )

    # (1) Check the basic test cases
    ss_event_basic_inorg = (
        await biofuels_inputs_translator.translate_application_event_to_ss_event(application_event_basic_inorg)
    )[0]
    assert isinstance(ss_event_basic_inorg, NitrogenFertilizer)
    assert ss_event_basic_inorg.rate_type == RateTypeEnum.PRODUCT
    assert ss_event_basic_inorg.name == "ammonium_nitrate"
    assert ss_event_basic_inorg.area_unit == "hectare"
    assert ss_event_basic_inorg.rate_unit == "kilogram"
    assert len(ss_event_basic_inorg.additives) == 2

    ss_event_organic = (
        await biofuels_inputs_translator.translate_application_event_to_ss_event(application_event_organic)
    )[0]
    assert isinstance(ss_event_organic, Manure)
    assert ss_event_organic.rate_type == RateTypeEnum.NITROGEN
    assert ss_event_organic.name == "manure_poultry_solid"
    assert ss_event_organic.area_unit == "hectare"
    assert ss_event_organic.rate_unit == "kilogram"

    # (2) Check the interesting test cases
    # Does the pair of main products get split correctly?
    ss_event_pair = await biofuels_inputs_translator.translate_application_event_to_ss_event(
        application_event_two_main_products
    )
    assert len(ss_event_pair) == 2
    (
        ss_application_event_two_main_products_one,
        ss_application_event_two_main_products_two,
    ) = ss_event_pair
    assert ss_application_event_two_main_products_one.name != ss_application_event_two_main_products_two.name
    # Are the additives shared across the two events?
    assert len(ss_application_event_two_main_products_one.additives) == 2
    assert len(ss_application_event_two_main_products_two.additives) == 2

    # Do the EENF and Fertigation events have additives removed?
    ss_application_event_fertigated = (
        await biofuels_inputs_translator.translate_application_event_to_ss_event(application_event_fertigated)
    )[0]
    assert isinstance(ss_application_event_fertigated, NitrogenFertilizer)
    assert ss_application_event_fertigated.additives == []
    ss_application_event_eenf = (
        await biofuels_inputs_translator.translate_application_event_to_ss_event(application_event_eenf)
    )[0]
    assert isinstance(ss_application_event_eenf, NitrogenFertilizer)
    assert ss_application_event_eenf.additives == []


@patch(
    "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_dry",
    mock_is_regrow_product_name_dry,
)
async def test_standardize_application_rate(biofuels_inputs_translator):
    application_input_dap = ApplicationInput(
        product_name=RegrowProductName.di_ammonium_phosphate,
        application_rate=ApplicationRate(
            value=5,
            numerator_unit=MassUnit.POUND,
            denominator_unit=AreaUnit.HECTARE,
            rate_type=ApplicationRateType.PRODUCT_RATE,
        ),
    )
    amount_hectares = await biofuels_inputs_translator._standardize_application_rate(application_input_dap)
    assert pytest.approx(amount_hectares, 0.00001) == 2.26796  # 5 lbs / 2.20462 lbs/kg
    # Make sure denominator is taken into account by the conversion
    application_input_dap.application_rate.denominator_unit = AreaUnit.ACRE
    amount_acres = await biofuels_inputs_translator._standardize_application_rate(application_input_dap)
    assert amount_acres > amount_hectares

    application_input_aqamm = ApplicationInput(
        product_name=RegrowProductName.ammonia_aqueous,
        application_rate=ApplicationRate(
            value=5,
            numerator_unit=VolumeUnit.LITRE,
            denominator_unit=AreaUnit.HECTARE,
            rate_type=ApplicationRateType.PRODUCT_RATE,
        ),
    )
    amount_liters = await biofuels_inputs_translator._standardize_application_rate(application_input_aqamm)
    assert (
        pytest.approx(amount_liters, 0.00001) == 4.55341
    )  # ((5 liters / 3.78541 [L/gal]) * (7.6 [lbs/gal])) / (2.20462 lbs/kg)

    # Make sure gallons results in a greater result than liters
    application_input_aqamm_gal = ApplicationInput(
        product_name=RegrowProductName.ammonia_aqueous,
        application_rate=ApplicationRate(
            value=5,
            numerator_unit=VolumeUnit.US_GALLON,
            denominator_unit=AreaUnit.HECTARE,
            rate_type=ApplicationRateType.PRODUCT_RATE,
        ),
    )
    amount_gal = await biofuels_inputs_translator._standardize_application_rate(application_input_aqamm_gal)
    assert amount_gal > amount_liters

    # Liquid product with a rate type of Nitrogen should not get converted, since they're already in kg/ha
    application_input_aqamm = ApplicationInput(
        product_name=RegrowProductName.ammonia_aqueous,
        application_rate=ApplicationRate(
            value=5,
            numerator_unit=MassUnit.KILOGRAM,
            denominator_unit=AreaUnit.HECTARE,
            rate_type=ApplicationRateType.NITROGEN_RATE,
        ),
    )
    amount = await biofuels_inputs_translator._standardize_application_rate(application_input_aqamm)
    assert amount == 5


async def test_get_biofuels_input_for_field(
    biofuels_inputs_translator, get_biofuels_field_events, mdl, orm_select, mocker
):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy, accounting_method=AccountingMethod.biofuels)
    project = await mdl.Projects(program_id=program.id)
    fields: Fields = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=fields.id, baseline_year=2022)
    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == fields.id]))[0])

    mock_validate_field_events = mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.fetch_events_for_simulations._validate_field_events_for_simulations"
    )
    mock_are_crops_biofuels_enabled = mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.helpers.defaults_translator.are_crops_biofuels_api_enabled",
        side_effect=mock_are_crops_enabled,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.helpers.ss_methods.fetch_field_geometry_and_area",
        side_effect=mock_fetch_field_geometry_and_area,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.fetch_events_for_simulations.adjust_overlapping_cropping_events_by_phase",
        side_effect=mock_adjust_overlaps,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.entity_events_methods.get_entity_events_for_field",
        # Get reasonable Biofuels field events from fixture
        return_value=get_biofuels_field_events(field_id=field.id, max_tillage="conventional till", cover_crop=True),
    )
    mocker.patch(
        "defaults.defaults_translator.are_products_biofuels_api_enabled",
        side_effect=mock_are_products_biofuels_api_enabled,
    )
    mock_translate_application = mocker.patch.object(
        biofuels_inputs_translator, "translate_application_event_to_ss_event", return_value=[]
    )
    result: BiofuelsInput = await biofuels_inputs_translator.get_biofuels_input_for_field(field)

    mock_validate_field_events.assert_called_once()
    assert len(mock_validate_field_events.call_args_list[0].kwargs["events_by_phase"]) == 2
    assert mock_validate_field_events.call_args_list[0].kwargs["field_id"] == field.id
    mock_are_crops_biofuels_enabled.assert_called_once()

    mock_translate_application.assert_called()
    assert result.dict() == {
        "field_name": str(field.id),
        "crop_name": CropNameEnum.CORN,
        "crop_yield": 2.0,
        "boundary": {
            "coordinates": [
                [
                    [
                        -109.072265625,
                        37.020098201368114,
                    ],
                    [
                        -102.0849609375,
                        37.020098201368114,
                    ],
                    [
                        -102.0849609375,
                        41.04621681452063,
                    ],
                    [
                        -109.072265625,
                        41.04621681452063,
                    ],
                    [
                        -109.072265625,
                        37.020098201368114,
                    ],
                ],
            ],
            "type": "Polygon",
        },
        "nitrogen_fertilizer": [],
        "manure": [],
        "tillage": TillageEnum.CONVENTIONAL_TILL,
        "cover_crop": True,
        "consider_climate_zone": False,
    }


async def test_get_biofuels_input_for_field_mrv_validation_failure(biofuels_inputs_translator, mdl, orm_select, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy)
    project = await mdl.Projects(program_id=program.id)
    fields: Fields = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=fields.id, baseline_year=2022)
    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == fields.id]))[0])

    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.entity_events_methods.get_entity_events_for_field",
        # Get reasonable Biofuels field events from fixture
        side_effect=instantiate_invalid_model,
    )

    with pytest.raises(ScenariosServiceIntegrationError) as exc:
        await biofuels_inputs_translator.get_biofuels_input_for_field(field)
    assert "It's invalid" in exc.value.message
    assert exc.value.code == ScenariosServiceIntegrationErrorCode.invalid_management_data


async def test_get_biofuels_input_for_field_invalid_products(
    biofuels_inputs_translator, get_biofuels_field_events, mdl, orm_select, mocker
):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy, accounting_method=AccountingMethod.biofuels)
    project = await mdl.Projects(program_id=program.id)
    fields: Fields = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=fields.id, baseline_year=2022)
    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == fields.id]))[0])

    # Mock are_products_biofuels_api_enabled with a mock that always returns False.
    mocker.patch(
        "defaults.defaults_translator.are_products_biofuels_api_enabled",
        side_effect=mock_products_not_biofuels_api_enabled,
    )

    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.fetch_events_for_simulations._validate_field_events_for_simulations"
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.helpers.defaults_translator.are_crops_biofuels_api_enabled",
        side_effect=mock_are_crops_enabled,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.fetch_events_for_simulations.adjust_overlapping_cropping_events_by_phase",
        side_effect=mock_adjust_overlaps,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.entity_events_methods.get_entity_events_for_field",
        # Get reasonable Biofuels field events from fixture
        return_value=get_biofuels_field_events(field_id=field.id, max_tillage="conventional till", cover_crop=True),
    )
    mocker.patch.object(biofuels_inputs_translator, "translate_application_event_to_ss_event", return_value=[])
    with pytest.raises(ScenariosServiceIntegrationError) as integration_error:
        await biofuels_inputs_translator.get_biofuels_input_for_field(field)
    assert (
        integration_error.value.message
        == "Fertilizer products can't be submitted for modeling: ['ammonia_aqueous'] are not supported by Biofuels API."
    )
    assert integration_error.value.code == ScenariosServiceIntegrationErrorCode.unsupported_product_type


async def test_derive_cover_crop_practice(biofuels_inputs_translator, get_biofuels_cultivation_cycles, mdl, orm_select):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy)
    project = await mdl.Projects(program_id=program.id)
    fields: Fields = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=fields.id, baseline_year=2022)
    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == fields.id]))[0])

    result = biofuels_inputs_translator._derive_cover_crop_practice(
        get_biofuels_cultivation_cycles(field_id=field.id, max_tillage="conventional till", cover_crop=True)[0]
    )
    assert result is True

    result = biofuels_inputs_translator._derive_cover_crop_practice(
        get_biofuels_cultivation_cycles(field_id=field.id, max_tillage="conventional till", cover_crop=False)[0]
    )
    assert result is False


async def test_derive_tillage_practice(biofuels_inputs_translator, get_biofuels_cultivation_cycles, mdl, orm_select):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = biofuels_inputs_translator._derive_tillage_practice(
        get_biofuels_cultivation_cycles(field_id=field.id, max_tillage="conventional till")[0]
    )
    assert result == TillageEnum.CONVENTIONAL_TILL

    result = biofuels_inputs_translator._derive_tillage_practice(
        get_biofuels_cultivation_cycles(field_id=field.id, max_tillage="reduced till")[0]
    )
    assert result == TillageEnum.REDUCED_TILL

    result = biofuels_inputs_translator._derive_tillage_practice(
        get_biofuels_cultivation_cycles(field_id=field.id, max_tillage="no till")[0]
    )
    assert result == TillageEnum.NO_TILL


async def test_get_biofuels_api_enabled_with_exceptions(mocker):
    async def mock_is_biofuels_api_enabled(product_name) -> bool:
        if product_name == RegrowProductName.manure_poultry_liquid:
            return False
        elif product_name == RegrowProductName.ammonium_nitrate:
            return True
        else:
            pytest.fail("unexpected product")

    mocker.patch(
        "scenarios_service.generalized_integration.biofuels_inputs_translator.defaults_translator.is_biofuels_api_enabled",
        side_effect=mock_is_biofuels_api_enabled,
    )
    # Approved products should be True
    assert (
        await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(RegrowProductName.ammonium_nitrate)
        is True
    )
    # Explicitly unsupported or products marked unsupported in Defaults Service should be False
    assert (
        await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(
            RegrowProductName.monoammonium_phosphate
        )
        is False
    )
    assert (
        await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(
            RegrowProductName.di_ammonium_phosphate
        )
        is False
    )
    assert await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(RegrowProductName.lime) is False
    assert (
        await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(RegrowProductName.potassium_chloride)
        is False
    )
    assert (
        await BiofuelsInputsTranslator._get_biofuels_api_enabled_with_exceptions(
            RegrowProductName.manure_poultry_liquid
        )
        is False
    )
