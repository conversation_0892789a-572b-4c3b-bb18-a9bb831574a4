from datetime import datetime, timezone

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from programs.model import Programs
from scenarios_service.generalized_integration.session_inputs_helpers import (
    filter_non_required_historical_cultivation_cycles,
)


async def test_filter_non_required_historical_cultivation_cycles_does_not_filter_when_no_required_historical_years():
    program = Programs()
    cultivation_cycles = [
        _cc(datetime(2022, 1, 1), datetime(2022, 12, 31)),
        _cc(datetime(2023, 1, 1), datetime(2023, 12, 31)),
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles


async def test_filter_non_required_historical_cultivation_cycles_when_ccs_are_less_than_required_years():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles


async def test_filter_non_required_historical_cultivation_cycles_filters_when_required_historical_years():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2022, 1, 1), datetime(2022, 12, 31)),
        _cc(datetime(2023, 1, 1), datetime(2023, 12, 31)),
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


async def test_filter_non_required_historical_cultivation_cycles_filters_when_ccs_years_are_offset():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2021, 11, 1), datetime(2022, 10, 31)),
        _cc(datetime(2022, 11, 1), datetime(2023, 10, 31)),
        _cc(datetime(2023, 11, 1), datetime(2024, 10, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


async def test_filter_non_required_historical_cultivation_cycles_filters_when_ccs_are_less_than_a_year():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2020, 2, 1), datetime(2021, 10, 31)),
        _cc(datetime(2021, 11, 1), datetime(2022, 1, 31)),
        _cc(datetime(2022, 2, 1), datetime(2022, 10, 31)),
        _cc(datetime(2022, 11, 1), datetime(2023, 1, 31)),
        _cc(datetime(2023, 2, 1), datetime(2023, 10, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


def _cc(start: datetime, end: datetime) -> CultivationCycle:
    return CultivationCycle(
        id=CultivationCycleId(start_date=start, end_date=end),
        start=start.replace(tzinfo=timezone.utc),
        end=end.replace(tzinfo=timezone.utc),
    )
