from phases.enums import PhaseTypes
from scenarios_service.generalized_integration.db import (
    create_dndc_task,
    record_dndc_simulation_request_error,
)
from scenarios_service.generalized_integration.program_simulation_tasks import (
    get_program_simulation_tasks,
)
from scenarios_service.generalized_integration.schema import FieldSimulationContext
from scenarios_service.model import ScenariosServiceApi


async def test_get_program_simulation_tasks(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    task = await create_dndc_task(
        app_request,
        program.id,
        project.id,
        PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    context = FieldSimulationContext(
        field_id=field.id,
        task_id=task.id,
        ss_field_request_id=12,
        ss_session_name="task_1_field_1",
        error_message="e",
        is_error=True,
    )
    created_simulation_request = await record_dndc_simulation_request_error(
        app_request, context, ScenariosServiceApi.measure_api
    )
    tasks = await get_program_simulation_tasks(request=app_request, program_id=program.id, eligible_field_count=1)
    assert len(tasks) == 1
    assert tasks[0].id == task.id
    assert len(tasks[0].simulation_requests) == 1
    assert tasks[0].simulation_requests[0].id == created_simulation_request.id


async def test_get_program_simulation_tasks_empty_tasks(app_request, mdl):
    program = await mdl.Programs()
    tasks = await get_program_simulation_tasks(request=app_request, program_id=program.id, eligible_field_count=0)
    assert len(tasks) == 0


async def test_get_program_simulation_tasks_empty_simulations(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    task = await create_dndc_task(
        app_request,
        program.id,
        project.id,
        PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    tasks = await get_program_simulation_tasks(request=app_request, program_id=program.id, eligible_field_count=0)
    assert len(tasks) == 1
    assert tasks[0].id == task.id
    assert len(tasks[0].simulation_requests) == 0
