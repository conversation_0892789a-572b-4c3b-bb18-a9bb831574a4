import json
from math import isclose

import pytest
from scenarios_service_schema.examples import (
    SCOPE_1_ASSETS_RESPONSE,
    SCOPE_3_ASSETS_RESPONSE,
)

# from scenarios_service_schema.inventory import ProjectInventoryResponse
from scenarios_service_schema.scope_1 import ProjectScope1Response
from scenarios_service_schema.scope_3 import ProjectScope3Response

from entity_events.event_creators.event_creator_lookup import EVENT_CREATOR_LOOKUP
from phases.enums import PhaseTypes
from programs.enums import BaselineMethod, Protocols
from programs.model import Programs
from scenarios_service.enums import DndcTaskStatusChoices, ScenariosServiceApi
from scenarios_service.generalized_integration.outcomes_translator import (
    translate_inventory_to_reported_outcomes,
    translate_scope_1_to_reported_outcomes,
    translate_scope_3_to_reported_outcomes,
)
from scenarios_service.generalized_integration.tests.conftest import (
    TEST_INVENTORY_RESPONSE,
)


async def test_translate_scope_3_to_reported_outcomes(app_request, mdl, mdl_factory_ignore_ids):
    program = await mdl_factory_ignore_ids(
        Programs,
        ignore_ids=EVENT_CREATOR_LOOKUP.keys(),
        protocol=Protocols.GENERAL_SCOPE_3,
        baseline_method=BaselineMethod.BLENDED,
        crediting_year=2022,
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="regrow_mrv_test",
    )
    project = await mdl.Projects(program_id=program.id)
    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    ss_field_id_list = list(SCOPE_3_ASSETS_RESPONSE["field_level"].keys())
    for ss_field_id in ss_field_id_list:
        field = await mdl.Fields(parent_project_id=project.id)
        await mdl.DndcSimulationRequests(field_id=field.id, task_id=task.id, ss_field_request_id=ss_field_id)
    is_test_run = False
    reported_outcomes = await translate_scope_3_to_reported_outcomes(
        app_request, program.id, ProjectScope3Response(**SCOPE_3_ASSETS_RESPONSE), task.id, is_test_run
    )
    assert len(reported_outcomes.field_outcomes) == len(ss_field_id_list)


# this code is really handy for testing when handling a particular scope 3 response
# is producing an exception
@pytest.mark.skip(reason="use when you want to test a particular Scope 3 output")
async def test_translate_scope_3_to_reported_outcomes_from_file(app_request, mdl, mdl_factory_ignore_ids):
    program = await mdl_factory_ignore_ids(
        Programs,
        ignore_ids=EVENT_CREATOR_LOOKUP.keys(),
        protocol=Protocols.GENERAL_SCOPE_3,
        baseline_method=BaselineMethod.BLENDED,
        crediting_year=2022,
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="regrow_mrv_test",
    )
    project = await mdl.Projects(program_id=program.id)
    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    # this should be set from input to input
    ss_field_id_list = []
    for ss_field_id in ss_field_id_list:
        fs_field = await mdl.KMLGroups()
        field = await mdl.Fields(parent_project_id=project.id, fs_field_id=fs_field.id)
        await mdl.DndcSimulationRequests(field_id=field.id, task_id=task.id, ss_field_request_id=ss_field_id)
    # you will need to change this filename
    with open("/results/file/you/have/downloaded") as json_data:
        sample_json = json.load(json_data)
    sample_response = ProjectScope3Response(**sample_json)
    is_test_run = False
    reported_outcomes = await translate_scope_3_to_reported_outcomes(
        app_request, program.id, sample_response, task.id, is_test_run
    )
    assert len(reported_outcomes.field_outcomes) == len(ss_field_id_list)


async def test_translate_scope_1_to_reported_outcomes(app_request, mdl, mdl_factory_ignore_ids):
    program = await mdl_factory_ignore_ids(
        Programs,
        ignore_ids=EVENT_CREATOR_LOOKUP.keys(),
        protocol=Protocols.VERRA,
        baseline_method=BaselineMethod.BLENDED,
        crediting_year=2022,
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="regrow_mrv_test",
    )
    project = await mdl.Projects(program_id=program.id)
    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    parsed_assets = ProjectScope1Response(**SCOPE_1_ASSETS_RESPONSE)
    ss_field_id_list = list(parsed_assets.field_level.keys())
    for ss_field_id in ss_field_id_list:
        field = await mdl.Fields(parent_project_id=project.id)
        await mdl.DndcSimulationRequests(field_id=field.id, task_id=task.id, ss_field_request_id=ss_field_id)
    is_test_run = False
    reported_outcomes = await translate_scope_1_to_reported_outcomes(
        app_request, program.id, parsed_assets, task.id, is_test_run
    )
    assert len(reported_outcomes.field_outcomes) == len(ss_field_id_list)


# this code is really handy for testing when handling a particular scope 1 response
# is producing an exception
@pytest.mark.skip(reason="use when you want to test a particular Scope 1 output")
async def test_translate_scope_1_to_reported_outcomes_from_file(app_request, mdl, mdl_factory_ignore_ids):
    program = await mdl_factory_ignore_ids(
        Programs,
        ignore_ids=EVENT_CREATOR_LOOKUP.keys(),
        protocol=Protocols.VERRA,
        baseline_method=BaselineMethod.BLENDED,
        crediting_year=2022,
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="regrow_mrv_test",
    )
    project = await mdl.Projects(program_id=program.id)
    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    # this should be set from input to input
    ss_field_id_list = []
    for ss_field_id in ss_field_id_list:
        fs_field = await mdl.KMLGroups()
        field = await mdl.Fields(parent_project_id=project.id, fs_field_id=fs_field.id)
        await mdl.DndcSimulationRequests(field_id=field.id, task_id=task.id, ss_field_request_id=ss_field_id)
    # you will need to change this filename
    with open("/results/file/you/have/downloaded") as json_data:
        sample_json = json.load(json_data)
    sample_response = ProjectScope1Response(**sample_json)
    is_test_run = False
    reported_outcomes = await translate_scope_1_to_reported_outcomes(
        app_request, program.id, sample_response, task.id, is_test_run
    )
    assert len(reported_outcomes.field_outcomes) == len(ss_field_id_list)


async def test_translate_inventory_to_reported_outcomes(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
    )

    for ss_field_response in TEST_INVENTORY_RESPONSE["field_level"]:
        field_id = int(ss_field_response["field_name"])
        await mdl.Fields(parent_project_id=project.id, id=field_id)
    is_test_run = False
    reported_outcomes = await translate_inventory_to_reported_outcomes(
        app_request, program.id, TEST_INVENTORY_RESPONSE, task.id, is_test_run
    )
    assert len(reported_outcomes.field_inventory_outcomes) == len(TEST_INVENTORY_RESPONSE["field_level"])
    assert len(reported_outcomes.crop_inventory_outcomes) == len(TEST_INVENTORY_RESPONSE["crop_level"])

    # ** test result manipulations to include SOC **
    # crop-level SOC inclusion
    translated_crop_outcome = reported_outcomes.crop_inventory_outcomes["wheat_winter"]
    original_crop_outcomes = TEST_INVENTORY_RESPONSE["crop_level"]["wheat_winter"]
    assert isclose(
        translated_crop_outcome.total_emissions,
        original_crop_outcomes["total_emissions"] + original_crop_outcomes["soc"],
    )
    assert isclose(
        translated_crop_outcome.total_emissions_per_bushel,
        original_crop_outcomes["total_emissions_per_bushel"] + original_crop_outcomes["soc_per_bushel"],
    )

    # field-level SOC inclusion
    translated_field_outcome = reported_outcomes.field_inventory_outcomes[117764]
    # only true if there is one project in the field
    assert isclose(translated_field_outcome.net_emissions_percentage, 100.0)
