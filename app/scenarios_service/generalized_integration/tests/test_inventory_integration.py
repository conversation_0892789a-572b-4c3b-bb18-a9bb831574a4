import json
from unittest.mock import patch

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

from fields.enums import FieldStatus
from phases.enums import PhaseTypes
from reported_outcomes.db import (
    get_crop_inventory_outcomes_for_program,
    get_field_inventory_outcomes_for_program,
)
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryIntegration,
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.generalized_integration.tests.conftest import (
    TEST_INVENTORY_RESPONSE,
)
from scenarios_service.model import (
    DndcSimulationRequests,
    DndcTasks,
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)


@pytest.mark.parametrize("dry_run", [True, False])
async def test_submit_program_for_inventory_outcomes(
    dry_run,
    mocker,
    mock_inventory_api_async_client,
    mock_inventory_inputs_translator,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_validate_fields = mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")
    mock_store_session_input_in_bucket = mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.helpers.store_session_input_in_bucket"
    )

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, status=FieldStatus.enrolled)

    integration = InventoryIntegration(
        request=app_request,
        inventory_client=mock_inventory_api_async_client,
        inventory_inputs_translator=mock_inventory_inputs_translator,
    )
    program_submission_result = await integration.submit_program_for_inventory(program.id, [field.id], dry_run=dry_run)

    task = (await orm_select(DndcTasks))[0]
    assert task.status == DndcTaskStatusChoices.finished if dry_run else DndcTaskStatusChoices.finalized

    mock_validate_fields.assert_called_once()
    assert mock_validate_fields.call_args_list[0].kwargs["request"] == app_request
    assert mock_validate_fields.call_args_list[0].kwargs["program_id"] == program.id
    assert mock_validate_fields.call_args_list[0].kwargs["submitted_field_ids"] == [field.id]

    mock_inventory_inputs_translator.get_inventory_input_for_field.assert_called_once()

    # Persisted DndcSimulationRequest assertions:
    simulation_request = (
        await orm_select(DndcSimulationRequests, where=[DndcSimulationRequests.field_id == field.id])
    )[0]
    assert simulation_request.error_message is None
    assert simulation_request.is_error is False
    assert simulation_request.field_id == field.id
    assert simulation_request.ss_api == ScenariosServiceApi.inventory_api
    assert simulation_request.ss_field_request_id == 1234567
    assert field.id == simulation_request.field_id
    assert simulation_request.task_id == task.id

    # Submit Inventory field assertions:
    mock_inventory_api_async_client.submit_field.assert_called_once()
    assert task.id in mock_inventory_api_async_client.submit_field.call_args.kwargs["project_name"]
    assert mock_inventory_api_async_client.submit_field.call_args.kwargs["x_consumer_id"] == "test_consumer"
    assert mock_inventory_api_async_client.submit_field.call_args.kwargs["inventory_input"].field_name == str(field.id)

    # Store InventoryInput in bucket assertions:
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["request"] is app_request
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["session_input"].field_name == str(field.id)
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["field_id"] == field.id
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["task_id"] == task.id

    # Submission result assertions:
    assert program_submission_result.field_submission_results[0].field_id == field.id
    assert task.id in program_submission_result.field_submission_results[0].project_name
    assert str(field.id) in program_submission_result.field_submission_results[0].session_name
    assert program_submission_result.field_submission_results[0].field_request_id == 1234567
    assert program_submission_result.field_submission_results[0].is_error is False
    assert program_submission_result.finalize_success != dry_run  # Finalize only if not doing a dry_run

    # Finalize endpoint was called, unless we're doing a dry run.
    if dry_run:
        mock_inventory_api_async_client.finalize_project.assert_not_called()
    else:
        mock_inventory_api_async_client.finalize_project.assert_called_with(
            project_name=program_submission_result.field_submission_results[0].project_name,
            x_consumer_id="test_consumer",
        )


async def test_submit_program_for_inventory_outcomes_dont_finalize(
    mocker,
    mock_inventory_api_async_client,
    mock_inventory_inputs_translator,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    # InventoryInputsTranslator throws an error
    mock_inventory_inputs_translator.get_inventory_input_for_field.side_effect = ScenariosServiceIntegrationError(
        message="Error building InventoryInput", code=ScenariosServiceIntegrationErrorCode.invalid_management_data
    )

    integration = InventoryIntegration(
        request=app_request,
        inventory_client=mock_inventory_api_async_client,
        inventory_inputs_translator=mock_inventory_inputs_translator,
    )
    program_submission_result = await integration.submit_program_for_inventory(program.id, [field.id], dry_run=False)

    task = (await orm_select(DndcTasks))[0]
    assert task.status == DndcTaskStatusChoices.finished

    # Submission result assertions:
    assert program_submission_result.field_submission_results[0].field_id == field.id
    assert str(field.id) in program_submission_result.field_submission_results[0].session_name
    assert program_submission_result.field_submission_results[0].field_request_id is None
    assert program_submission_result.field_submission_results[0].is_error is True
    assert (
        program_submission_result.field_submission_results[0].error_code
        == ScenariosServiceIntegrationErrorCode.invalid_management_data
    )
    assert program_submission_result.field_submission_results[0].error_message == "Error building InventoryInput"
    assert program_submission_result.finalize_success is False

    # Finalize endpoint was not called since an error occurred
    mock_inventory_api_async_client.finalize_project.assert_not_called()


async def test_approve_outcomes_for_inventory_program_succeeds(
    mocker, app_request, mock_inventory_api_async_client, mdl, mdl_factory_ignore_ids
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )

    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
    )

    for ss_field_response in TEST_INVENTORY_RESPONSE["field_level"]:
        field_id = int(ss_field_response["field_name"])
        await mdl.Fields(parent_project_id=project.id, id=field_id)

    is_test_run = False
    selected_task_id = task.id
    inventory_output_integration = InventoryOutputIntegration(
        request=app_request, inventory_client=mock_inventory_api_async_client
    )
    with patch(
        "scenarios_service.generalized_integration.inventory_integration.read_url_result",
        return_value=json.dumps(TEST_INVENTORY_RESPONSE),
    ):
        await inventory_output_integration.approve_outcomes_for_program(program.id, selected_task_id, is_test_run)

    field_outcomes = await get_field_inventory_outcomes_for_program(app_request, program.id)
    assert len(field_outcomes) == 1
    assert field_outcomes[0].ghg_emissions_factor is not None
    crop_outcomes = await get_crop_inventory_outcomes_for_program(app_request, program.id)
    assert len(crop_outcomes) == 1


async def test_submit_program_for_inventory_outcomes_validates_inputs(
    mocker,
    mock_inventory_api_async_client,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)

    # If the program doesn't exist, we 404
    with pytest.raises(HTTPException) as exc_info:
        await integration.submit_program_for_inventory(program_id=-2, field_ids=[], dry_run=False)
    assert exc_info.value.status_code == 404

    # If field_validation fails, we allow the 404 to propagate
    program = await mdl.Programs()
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_validate_fields = mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")
    mock_validate_fields.side_effect = HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    with pytest.raises(HTTPException) as exc_info:
        await integration.submit_program_for_inventory(program_id=program.id, field_ids=[1], dry_run=False)
    assert exc_info.value.status_code == 404
    mock_validate_fields.assert_called_once_with(request=app_request, program_id=program.id, submitted_field_ids=[1])
