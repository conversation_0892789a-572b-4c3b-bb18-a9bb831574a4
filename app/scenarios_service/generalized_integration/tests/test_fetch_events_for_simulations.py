from datetime import datetime
from typing import Callable
from unittest.mock import patch

import pytest
import pytz
from starlette.requests import Request

import scenarios_service.generalized_integration
from defaults.attribute_options import CropUsage, NO_ADDITIVES_OPTION
from defaults.consts import RegrowCropName, RegrowProductName
from defaults.tests import mock_defaults_translator
from defaults.tests.mock_defaults_translator import (
    mock_translate_core_crop_name_to_regrow_name,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.reduction_event import ReductionEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from entity_events.tests.helpers import create_structured_program_and_field
from fields.model import Fields
from fields.schema import Field
from helper.pytest_models_factory import ModelsFactory
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from programs.model import Programs
from projects.model import Projects
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.enums import CroppingEventAdjustment
from scenarios_service.generalized_integration.fetch_events_for_simulations import (
    _validate_intercropping,
    adjust_overlapping_cropping_events_by_phase,
    get_adjusted_cropping_events,
    get_events,
    get_intended_commodity_crops,
)
from scenarios_service.generalized_integration.schema import (
    AdjustedCroppingEvent,
    ScenariosServiceIntegrationError,
)


def get_mock_gapfill_events_fn(expected_raise_if: bool) -> Callable:
    def mock_gapfill_events(
        request: Request,
        field: Field,
        events_by_phase: dict[PhaseTypes, list[EntityEvent]],
        raise_if_no_expected_data: bool,
    ) -> dict[PhaseTypes, list[EntityEvent]]:
        assert raise_if_no_expected_data == expected_raise_if
        return events_by_phase

    return mock_gapfill_events


async def test_get_events_legacy_program(
    mdl, mdl_factory_ignore_ids, app_request, orm_select, mocker, comprehensive_mrv_values_lookup
):
    # First set up a Structured Stages program so the config is compatible with the Structured Events Facade
    program_id, _, field_id = await create_structured_program_and_field(
        mdl, mdl_factory_ignore_ids, comprehensive_mrv_values_lookup
    )

    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == field_id]))[0])
    program: Programs = (await orm_select(Programs, where=[Programs.id == program_id]))[0]
    assert program.program_template == ProgramTemplate.legacy

    # For Measure API we do expect gapfilling to raise if there's missing data
    mocker.patch(
        "scenarios_service.generalized_integration.fetch_events_for_simulations.gapfilling.gapfill_events_by_phase",
        side_effect=get_mock_gapfill_events_fn(expected_raise_if=True),
    )
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_translate_core_crop_name_to_regrow_name,
    )
    events = await get_events(request=app_request, field=field, scenarios_service_api=ScenariosServiceApi.measure_api)
    assert len(events) > 0

    # Intended events should have been filtered out
    assert not any(ev.is_intended for ev in events)
    # Make sure all ev.crop_type for CroppingEvent are values in RegrowCropName
    assert all(ev.crop_type in RegrowCropName.__members__.values() for ev in events if isinstance(ev, CroppingEvent))
    # Make sure all app_event.products[].product_name are values in RegrowProductName
    for ev in events:
        if isinstance(ev, ApplicationEvent):
            assert all(
                prod.product_name in RegrowProductName.__members__.values()
                for prod in ev.products
                if prod.product_name != NO_ADDITIVES_OPTION
            )


async def test_get_events_event_based_program_explore_api(
    mdl,
    mdl_factory_ignore_ids,
    app_request,
    orm_select,
    mocker,
    comprehensive_mrv_values_lookup,
    create_cropping_event_data,
    create_application_event_data,
    create_tillage_event_data,
    create_irrigation_event_data,
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        reporting_period_start_date=datetime(2024, 1, 1),
        reporting_period_end_date=datetime(2024, 12, 31),
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    events = [
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2022, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2022, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=RegrowCropName.corn,
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        ApplicationEvent(
            **create_application_event_data(
                entity_id=field.id,
                occurred_at=datetime(2022, 2, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        TillageEvent(
            **create_tillage_event_data(
                entity_id=field.id,
                occurred_at=datetime(2022, 3, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        IrrigationEvent(
            **create_irrigation_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2022, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2022, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
            )
        ),
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2023, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2023, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=RegrowCropName.sorghum,
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        ApplicationEvent(
            **create_application_event_data(
                entity_id=field.id,
                occurred_at=datetime(2023, 2, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        TillageEvent(
            **create_tillage_event_data(
                entity_id=field.id,
                occurred_at=datetime(2023, 3, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        IrrigationEvent(
            **create_irrigation_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2023, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2023, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
            )
        ),
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2024, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2024, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=RegrowCropName.barley,
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        ApplicationEvent(
            **create_application_event_data(
                entity_id=field.id,
                occurred_at=datetime(2024, 2, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        TillageEvent(
            **create_tillage_event_data(
                entity_id=field.id,
                occurred_at=datetime(2024, 3, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        IrrigationEvent(
            **create_irrigation_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2024, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2024, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
            )
        ),
        # Rogue incomplete events shouldn't break anything
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2024, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=None,
                ),
                crop_type=RegrowCropName.alfalfa,
                crop_usage=None,
                reductions=[],
            )
        ),
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=None,
                    end=datetime(2024, 5, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=None,
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]

    mocker.patch(
        "scenarios_service.generalized_integration.fetch_events_for_simulations.fetch_entity_events.fetch_events_for_field_phase",
        return_value=events,
    )
    ret_events = await get_events(
        request=app_request, field=Field.from_orm(field), scenarios_service_api=ScenariosServiceApi.explore_api
    )
    assert len(ret_events) == 8
    # Ensure barley cropping event was dropped, as it is part of a monitor phase in the reporting year
    assert [ev for ev in ret_events if isinstance(ev, CroppingEvent) and ev.crop_type == RegrowCropName.barley] == []


async def test_get_events_event_based_program_measure_api(
    mdl,
    mdl_factory_ignore_ids,
    app_request,
    orm_select,
    mocker,
    comprehensive_mrv_values_lookup,
    create_cropping_event_data,
    create_application_event_data,
    create_tillage_event_data,
    create_irrigation_event_data,
):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    events = [
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2022, 5, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2022, 10, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=RegrowCropName.corn,
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent(
            **create_cropping_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2022, 8, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2023, 5, 5, tzinfo=pytz.timezone("UTC")),
                ),
                crop_type=RegrowCropName.sorghum,
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        ApplicationEvent(
            **create_application_event_data(
                entity_id=field.id,
                occurred_at=datetime(2021, 2, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        TillageEvent(
            **create_tillage_event_data(
                entity_id=field.id,
                occurred_at=datetime(2021, 3, 1, tzinfo=pytz.timezone("UTC")),
            )
        ),
        IrrigationEvent(
            **create_irrigation_event_data(
                entity_id=field.id,
                interval=Interval(
                    start=datetime(2022, 8, 5, tzinfo=pytz.timezone("UTC")),
                    end=datetime(2023, 5, 5, tzinfo=pytz.timezone("UTC")),
                ),
            )
        ),
    ]

    mocker.patch(
        "scenarios_service.generalized_integration.fetch_events_for_simulations.fetch_entity_events.fetch_events_for_field_phase",
        return_value=events,
    )
    ret_events = await get_events(
        request=app_request, field=Field.from_orm(field), scenarios_service_api=ScenariosServiceApi.measure_api
    )
    assert len(ret_events) == 5
    # Ensure sorghum cover crop was adjusted to remove overlap with corn
    sorghum_event = next(
        ev for ev in ret_events if isinstance(ev, CroppingEvent) and ev.crop_type == RegrowCropName.sorghum
    )
    assert sorghum_event.interval.start == datetime(2022, 10, 6, tzinfo=pytz.timezone("UTC"))


async def test_get_intended_commodity_crops(mocker, app_request, cropping_event_data, mdl, field):
    cropping_event1 = CroppingEvent.parse_obj(
        {**cropping_event_data, "crop_type": "peas", "is_intended": True, "entity_id": field.id}
    )
    cropping_event2 = CroppingEvent.parse_obj(
        {**cropping_event_data, "crop_type": "soybeans", "is_intended": True, "entity_id": field.id}
    )
    mocker.patch(
        "scenarios_service.generalized_integration.fetch_events_for_simulations.get_entity_events_for_field",
        return_value={PhaseTypes.ENROLMENT: [cropping_event1, cropping_event2]},
    )
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_translate_core_crop_name_to_regrow_name,
    )
    crop_events = await get_intended_commodity_crops(app_request, Field.from_orm(field))

    assert len(crop_events) == 2
    # Crop types have been translated to Regrow names
    assert {ev.crop_type for ev in crop_events} == {"pea", "soybean"}
    assert all(ev.is_intended for ev in crop_events)


async def test_get_intended_commodity_crops_no_events(mocker, app_request, mdl, field):
    mocker.patch(
        "scenarios_service.generalized_integration.fetch_events_for_simulations.get_entity_events_for_field",
        return_value={PhaseTypes.ENROLMENT: []},
    )

    with pytest.raises(ScenariosServiceIntegrationError) as exc_info:
        await get_intended_commodity_crops(app_request, Field.from_orm(field))

    assert str(exc_info.value) == f"No intended commodity crops found for field {field.id}"


def test_adjust_overlapping_cropping_events_no_overlap(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 4, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    assert adjusted_events_by_phase == events_by_phase
    assert deleted_events == []
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_start_date_on_end_date(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COMMODITY,
                    interval=create_interval_data(
                        start=datetime(2024, 2, 2, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC)
                    ),
                    reductions=[],
                )
            )
        ],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == []
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_commodity_within_cover(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 10, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 1, 30, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [cropping_2, cropping_3, cropping_4],
    }
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1],
        PhaseTypes.MONITORING: [cropping_2, cropping_3, cropping_4],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == [cropping_1]
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_cover_within_commodity(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 10, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 1, 30, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [cropping_2, cropping_3, cropping_4],
    }
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [
            # cropping_4 should be compared against cropping_1 for overlap
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COMMODITY,
                    interval=create_interval_data(
                        start=datetime(2024, 2, 2, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC)
                    ),
                    reductions=[],
                )
            )
        ],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == [cropping_2, cropping_3]
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_commodity_into_fallow_overlap(
    create_fallow_period_data, create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    fallow_1 = FallowPeriod.parse_obj(
        create_fallow_period_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            )
        )
    )
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 15, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, fallow_1], PhaseTypes.MONITORING: [cropping_1]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [
            tillage_1,
            FallowPeriod.parse_obj(
                create_fallow_period_data(
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 1, 14, 0, 0, 0, 0, pytz.UTC),
                    )
                )
            ),
        ],
        PhaseTypes.MONITORING: [cropping_1],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == []
    assert adjusted_events == [fallow_1]


def test_adjust_overlapping_cropping_events_commodity_into_cover_overlap(
    create_cropping_event_data, create_reduction_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[
                ReductionEvent.parse_obj(
                    create_reduction_event_data(occurred_at=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC))
                )
            ],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 10, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 30, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )

    # overlap > 14 days
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [
            tillage_1,
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COVER,
                    interval=create_interval_data(
                        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 1, 23, 0, 0, 0, 0, pytz.UTC),
                    ),
                    reductions=[
                        ReductionEvent.parse_obj(
                            create_reduction_event_data(occurred_at=datetime(2024, 1, 23, 0, 0, 0, 0, pytz.UTC))
                        )
                    ],
                )
            ),
        ],
        PhaseTypes.MONITORING: [cropping_2],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert adjusted_events_by_phase == events_by_phase
    assert deleted_events == []
    assert adjusted_events == [cropping_1]

    # overlap <= 14 days
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_3]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    assert adjusted_events_by_phase == events_by_phase
    assert deleted_events == []
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_cover_into_cover_overlap(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 10, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 30, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )

    # overlap > 14 days
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COVER,
                    interval=create_interval_data(
                        start=datetime(2024, 1, 19, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC),
                    ),
                    reductions=[],
                )
            )
        ],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == []
    assert adjusted_events == [cropping_2]

    # overlap <= 14 days
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_3]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    assert adjusted_events_by_phase == events_by_phase
    assert deleted_events == []
    assert adjusted_events == []


def test_adjust_overlapping_cropping_events_commodity_into_commodity_overlap(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 15, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COMMODITY,
                    interval=create_interval_data(
                        start=datetime(2024, 2, 2, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC),
                    ),
                    reductions=[],
                )
            )
        ],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == []
    assert adjusted_events == [cropping_2]


def test_adjust_overlapping_cropping_events_cover_into_commodity_overlap(
    create_cropping_event_data, create_tillage_event_data, create_interval_data
):
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 15, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events_by_phase = {PhaseTypes.ENROLMENT: [tillage_1, cropping_1], PhaseTypes.MONITORING: [cropping_2]}
    adjusted_events_by_phase, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
        events_by_phase
    )
    expected_events_by_phase = {
        PhaseTypes.ENROLMENT: [tillage_1, cropping_1],
        PhaseTypes.MONITORING: [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    crop_usage=CropUsage.COVER,
                    interval=create_interval_data(
                        start=datetime(2024, 2, 2, 0, 0, 0, 0, pytz.UTC),
                        end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC),
                    ),
                    reductions=[],
                )
            )
        ],
    }
    assert adjusted_events_by_phase == expected_events_by_phase
    assert deleted_events == []
    assert adjusted_events == [cropping_2]


def test_validate_intercropping(create_cropping_event_data, create_tillage_event_data, create_interval_data):
    # test no intercropping
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 4, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2]
    _validate_intercropping(events)

    # test valid intercropping (cover -> commodity)
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2]
    _validate_intercropping(events)

    # test valid intercropping (cover -> cover)
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2]
    _validate_intercropping(events)

    # test invalid crop usages
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2]
    with pytest.raises(Exception) as exc_info:
        _validate_intercropping(events)
    assert exc_info.value.args[0] == "A Cover crop planted into a Commodity crop is not allowed."

    # test invalid event interval
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2]
    with pytest.raises(Exception) as exc_info:
        _validate_intercropping(events)
    assert exc_info.value.args[0] == "A cropping event contained within another cropping event is not allowed."

    # test valid and invalid
    tillage_1 = TillageEvent.parse_obj(create_tillage_event_data())
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 3, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 4, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 3, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 4, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    events = [tillage_1, cropping_1, cropping_2, cropping_3, cropping_4]
    with pytest.raises(Exception) as exc_info:
        _validate_intercropping(events)
    assert exc_info.value.args[0] == "A Cover crop planted into a Commodity crop is not allowed."


@patch("scenarios_service.generalized_integration.fetch_events_for_simulations.get_entity_events_for_field")
async def test_get_adjusted_cropping_events(
    mock_get_entity_events_for_field, mdl, create_cropping_event_data, create_interval_data, app_request
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_type="corn",
            crop_usage=CropUsage.COMMODITY,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    # deleted
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_type="alfalfa",
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 1, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    # adjusted
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            crop_type="barley",
            crop_usage=CropUsage.COVER,
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC)
            ),
            reductions=[],
        )
    )
    mock_get_entity_events_for_field.return_value = {PhaseTypes.MONITORING: [cropping_1, cropping_2, cropping_3]}

    response = await get_adjusted_cropping_events(request=app_request, program_id=program.id)
    expected_response = [
        AdjustedCroppingEvent(
            field_id=field.id,
            crop_type="alfalfa",
            crop_usage=CropUsage.COVER,
            start_date=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
            end_date=datetime(2024, 1, 15, 0, 0, 0, 0, pytz.UTC),
            adjustment=CroppingEventAdjustment.DELETED,
        ),
        AdjustedCroppingEvent(
            field_id=field.id,
            crop_type="barley",
            crop_usage=CropUsage.COVER,
            start_date=datetime(2024, 2, 2, 0, 0, 0, 0, pytz.UTC),
            end_date=datetime(2024, 2, 15, 0, 0, 0, 0, pytz.UTC),
            adjustment=CroppingEventAdjustment.ADJUSTED,
        ),
    ]
    assert response == expected_response


@patch(
    "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
    side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
)
async def test_translate_and_maybe_substitute_core_crop_name(mock_translate, app_request, mdl: ModelsFactory):
    program: Programs = await mdl.Programs()
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)

    assert (
        "barley"
        == await scenarios_service.generalized_integration.fetch_events_for_simulations.translate_and_maybe_substitute_core_crop_name(
            app_request, "spring_barley", datetime(2024, 3, 1), field.id
        )
    )
    assert (
        "barley"
        == await scenarios_service.generalized_integration.fetch_events_for_simulations.translate_and_maybe_substitute_core_crop_name(
            app_request, "winter_barley", datetime(2024, 12, 1), field.id
        )
    )


@patch(
    "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
    side_effect=mock_translate_core_crop_name_to_regrow_name,
)
@pytest.mark.parametrize(
    "program_id, core_crop_and_date_to_regrow_name",
    [
        (
            116,
            {
                ("other", "2024-01-01"): "wheat_winter",
                ("full_cover", "2024-01-01"): "rye",
            },
        ),
        (
            117,
            {
                ("other", "2024-01-01"): "wheat_winter",
                ("full_cover", "2024-01-01"): "rye",
            },
        ),
        (
            253,
            {
                ("full_cover", "2024-01-01"): "basic_cover_crop",
                ("chickpeas", "2024-01-01"): "dry_bean",
                ("beans", "2024-01-01"): "dry_bean",
                ("other", "2024-02-01"): "barley",
                ("other", "2024-09-01"): "wheat_winter",
            },
        ),
        (
            254,
            {
                ("other", "2024-01-01"): "wheat_winter",
                ("full_cover", "2024-01-01"): "basic_cover_crop",
                ("chickpeas", "2024-01-01"): "dry_bean",
                ("beans", "2024-01-01"): "dry_bean",
            },
        ),
        (
            257,
            {
                ("full_cover", "2024-01-01"): "basic_cover_crop",
                ("chickpeas", "2024-01-01"): "dry_bean",
                ("beans", "2024-01-01"): "dry_bean",
            },
        ),
        (
            258,
            {
                ("other", "2024-01-01"): "corn",
                ("full_cover", "2024-01-01"): "basic_cover_crop",
                ("chickpeas", "2024-01-01"): "dry_bean",
                ("beans", "2024-01-01"): "dry_bean",
            },
        ),
        (
            259,
            {
                ("full_cover", "2024-01-01"): "basic_cover_crop",
                ("chickpeas", "2024-01-01"): "dry_bean",
                ("beans", "2024-01-01"): "dry_bean",
            },
        ),
        (
            583,
            {
                ("grass_pasture", "2024-01-01"): "pasture",
                ("chickpeas", "2024-01-01"): "dry_bean",
            },
        ),
        (
            275,
            {
                ("chickpeas", "2024-01-01"): "dry_bean",
            },
        ),
        (
            1215,
            {
                ("chickpeas", "2024-01-01"): "dry_bean",
            },
        ),
    ],
)
async def test_translate_and_maybe_substitute_core_crop_name_program_specific(
    mock_translate,
    app_request: Request,
    db_session_maker,
    mdl: ModelsFactory,
    program_id,
    core_crop_and_date_to_regrow_name,
):
    program: Programs = await mdl.Programs(id=program_id)
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)

    for (core_crop_name, planting_date), expected_translation in core_crop_and_date_to_regrow_name.items():
        assert (
            expected_translation
            == await scenarios_service.generalized_integration.fetch_events_for_simulations.translate_and_maybe_substitute_core_crop_name(
                app_request, core_crop_name, datetime.fromisoformat(planting_date), field.id
            )
        )
