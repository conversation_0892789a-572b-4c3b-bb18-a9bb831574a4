from reported_outcomes.db import query_outcome_approvals_for_program
from reported_outcomes.enums import OutcomeApprovalStatusType
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.measure_integration import (
    MeasureOutputIntegration,
)
from scenarios_service.generalized_integration.outcome_approval_handler import (
    _obtain_outcome_integration,
    handle_outcome_approval,
)


async def test_obtain_outcome_integration(app_request, mdl):
    program = await mdl.Programs()
    measure_task = await mdl.DndcTasks(
        program_id=program.id,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    integration = await _obtain_outcome_integration(app_request, measure_task.id)
    assert isinstance(integration, MeasureOutputIntegration)
    inventory_task = await mdl.DndcTasks(
        program_id=program.id,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
    )
    integration = await _obtain_outcome_integration(app_request, inventory_task.id)
    assert isinstance(integration, InventoryOutputIntegration)


async def test_revoking_pathway(app_request, mdl):
    program = await mdl.Programs()

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=test_task.id, outcome_approval_status=OutcomeApprovalStatusType.TEST
    )
    await handle_outcome_approval(app_request, program.id, None, False, False)

    approvals = await query_outcome_approvals_for_program(app_request, program.id)
    assert len(approvals) == 2
    for approval in approvals:
        assert approval.outcome_approval_status == OutcomeApprovalStatusType.REVOKED
