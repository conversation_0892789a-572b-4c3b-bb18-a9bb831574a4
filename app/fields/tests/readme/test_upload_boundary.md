
## Calling update_field_geometry api to update a fields geometry
## Inputs
- Field Id: field.id
- Field Name: Name to be stored in the kml_files
- kml_file: representing the geometry

### What it does
- Create an entry in the kml_files table for the file
- Create a corresponding entry in the kml_group table
- Update mrv_fields entry corresponding to the field id
- Create an entry in the mrv_field_history with reason : api_internal


__curl command example__

 ```cd into the current readme directory```

```bash
curl -X 'POST' \
  '${endpoint}/fields/update_field_geometry?field_id=115479&field_name=dummy_xyz' \
  -H 'accept: application/json' \
  -H 'fs-user-id: 13' \
  -H 'Content-Type: multipart/form-data' \
  -F 'geom_file=@test_geometry.kml'
```

__response__
```status: 200 ok (if success)```

#### db_changes_verification
```select * from mrv_fields_history mfh where mfh.reason = 'api_internal' order by created_at desc;```

This should show a recent entry if the api succeeds along with kml_id, fs_field_id which can be used to further confirm.
