from datetime import datetime

from sqlalchemy import select

from fields.baselines.db import (
    bulk_create_or_update_field_baselines,
    get_field_baseline,
    update_or_create_field_baseline_data,
)
from fields.baselines.methods import (
    create_fields_baseline,
    get_or_create_fields_baseline,
    get_or_create_fields_baseline_years,
    get_or_create_fields_baselines,
)
from fields.model import FieldsBaseline
from fields.schema import FieldBaselineUpdateRequest
from helper.helper import run_query


async def test_update_or_create_field_baseline_data(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    # create
    update_request = FieldBaselineUpdateRequest(field_id=field.id, baseline_year=2020)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2020
    assert not baseline.is_returning

    # update
    update_request = FieldBaselineUpdateRequest(field_id=field.id, baseline_year=2021, is_returning=1)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2021
    assert baseline.is_returning

    # update
    update_request = FieldBaselineUpdateRequest(field_id=field.id, is_returning=0)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2021
    assert not baseline.is_returning


async def test_create_fields_baseline(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)

    # different existing field with baseline
    field2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field2.id, baseline_year=2020, is_returning=True)

    await create_fields_baseline(
        request=app_request,
        field_id=field1.id,
        program_id=program.id,
    )

    res = await get_field_baseline(app_request, field1.id)

    assert res.field_id == field1.id
    assert res.baseline_year == 2021  # program enrollment year
    assert res.is_returning is False


async def test_get_or_create_fields_baseline_gets_existing(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
    )
    project = await mdl.Projects(program_id=program.id)

    field1 = await mdl.Fields(parent_project_id=project.id)
    baseline1 = await mdl.FieldsBaseline(field_id=field1.id, baseline_year=2021, is_returning=False)

    # different existing field with baseline
    field2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field2.id, baseline_year=2022, is_returning=False)

    res = await get_or_create_fields_baseline(
        request=app_request,
        field_id=field1.id,
    )
    assert (
        res.to_dict()
        == FieldsBaseline(
            id=baseline1.id,
            field_id=baseline1.field_id,
            baseline_year=baseline1.baseline_year,
            is_returning=baseline1.is_returning,
        ).to_dict()
    )


async def test_get_or_create_fields_baseline_creates_new(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)

    # different existing field with baseline
    field2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field2.id, baseline_year=2022, is_returning=True)

    res = await get_or_create_fields_baseline(
        request=app_request,
        field_id=field1.id,
    )

    assert res.field_id == field1.id
    assert res.baseline_year == 2021  # program enrollment year
    assert res.is_returning is False


async def test_get_or_create_fields_baselines_gets_and_creates(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)
    baseline1 = await mdl.FieldsBaseline(field_id=field1.id, baseline_year=2021, is_returning=True)

    field2 = await mdl.Fields(parent_project_id=project.id)

    field3 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field3.id, baseline_year=2022, is_returning=True)
    baseline3 = await get_field_baseline(app_request, field3.id)

    field4 = await mdl.Fields(parent_project_id=project.id)

    res = await get_or_create_fields_baselines(
        request=app_request,
        field_ids=[field1.id, field2.id, field3.id, field4.id],
    )
    assert len(res) == 4

    assert field1.id in res
    assert res[field1.id].id == baseline1.id
    assert res[field1.id].field_id == field1.id
    assert res[field1.id].baseline_year == 2021
    assert res[field1.id].is_returning is True

    assert field2.id in res
    assert res[field2.id].field_id == field2.id
    assert res[field2.id].baseline_year == 2023
    assert res[field2.id].is_returning is False

    assert field3.id in res
    assert res[field3.id].id == baseline3.id
    assert res[field3.id].field_id == field3.id
    assert res[field3.id].baseline_year == 2022
    assert res[field3.id].is_returning is True

    assert field4.id in res
    assert res[field4.id].field_id == field4.id
    assert res[field4.id].baseline_year == 2023
    assert res[field4.id].is_returning is False


async def test_get_or_create_fields_baselines_creates_new_from_multiple_projects(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
        previous_program_id=None,
    )
    project1 = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project1.id)
    project2 = await mdl.Projects(program_id=program.id)
    field2 = await mdl.Fields(parent_project_id=project2.id)

    res = await get_or_create_fields_baselines(request=app_request, field_ids=[field1.id, field2.id])
    assert len(res) == 2

    assert field1.id in res
    assert res[field1.id].field_id == field1.id
    assert res[field1.id].baseline_year == 2023
    assert res[field1.id].is_returning is False

    assert field2.id in res
    assert res[field2.id].field_id == field2.id
    assert res[field2.id].baseline_year == 2023
    assert res[field2.id].is_returning is False


async def test_get_or_create_fields_baseline_years(mdl, app_request):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field1.id, baseline_year=2021, is_returning=True)

    field2 = await mdl.Fields(parent_project_id=project.id)

    res = await get_or_create_fields_baseline_years(request=app_request, field_ids=[field1.id, field2.id])
    assert res == {field1.id: 2021, field2.id: 2023}


async def test_bulk_create_or_update_field_baselines(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_3 = await mdl.Fields(parent_project_id=project.id)
    field_4 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    await bulk_create_or_update_field_baselines(
        request=app_request,
        field_ids=[field_1.id, field_2.id, field_3.id, field_4.id],
        baseline_year=2025,
        is_returning=False,
    )

    async with db_session_maker() as s:
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id, field_3.id, field_4.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 4
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_3.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[3].to_dict()
            == FieldsBaseline(id=res[3].id, field_id=field_4.id, baseline_year=2025, is_returning=False).to_dict()
        )
