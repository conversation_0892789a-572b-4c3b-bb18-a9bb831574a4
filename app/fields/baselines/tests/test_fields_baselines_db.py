from sqlalchemy import select

from fields.baselines.db import (
    bulk_create_or_update_field_baselines,
    get_field_baseline,
    get_field_baselines_by_field_ids,
    update_or_create_field_baseline_data,
)
from fields.model import FieldsBaseline
from fields.schema import FieldBaselineUpdateRequest
from helper.helper import run_query


async def test_get_field_baselines_by_field_ids(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_baseline_1 = await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2025, is_returning=False)
    field_baseline_2 = await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2024, is_returning=True)

    field_baselines = await get_field_baselines_by_field_ids(request=app_request, field_ids=[field_1.id, field_2.id])
    assert sorted([baseline.to_dict() for baseline in field_baselines], key=lambda x: x["field_id"]) == [
        field_baseline_1.to_dict(),
        field_baseline_2.to_dict(),
    ]


async def test_get_field_baseline(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    assert await get_field_baseline(request=app_request, field_id=field.id) is None

    field_baseline = await mdl.FieldsBaseline(field_id=field.id, baseline_year=2025, is_returning=False)
    assert (await get_field_baseline(request=app_request, field_id=field.id)).to_dict() == field_baseline.to_dict()


async def test_update_or_create_field_baseline_data(mdl, app_request):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    update_request = FieldBaselineUpdateRequest(field_id=field.id, baseline_year=2020)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2020
    assert not baseline.is_returning

    update_request = FieldBaselineUpdateRequest(field_id=field.id, baseline_year=2021, is_returning=1)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2021
    assert baseline.is_returning

    update_request = FieldBaselineUpdateRequest(field_id=field.id, is_returning=0)
    await update_or_create_field_baseline_data(app_request, [update_request])
    baseline = await get_field_baseline(app_request, field.id)
    assert baseline is not None
    assert baseline.baseline_year == 2021
    assert not baseline.is_returning


async def test_bulk_create_or_update_field_baselines(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    field_3 = await mdl.Fields(parent_project_id=project.id)
    field_4 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    await bulk_create_or_update_field_baselines(
        request=app_request,
        field_ids=[field_1.id, field_2.id, field_3.id, field_4.id],
        baseline_year=2025,
        is_returning=False,
    )

    async with db_session_maker() as s:
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id, field_3.id, field_4.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 4
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_3.id, baseline_year=2025, is_returning=False).to_dict()
        )
        assert (
            res[3].to_dict()
            == FieldsBaseline(id=res[3].id, field_id=field_4.id, baseline_year=2025, is_returning=False).to_dict()
        )
