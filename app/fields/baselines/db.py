from sqlalchemy import select, update
from starlette.requests import Request

from fields.model import FieldsBaseline
from fields.schema import FieldBaselineUpdateRequest
from helper.helper import run_query
from root_crud import create


async def get_field_baselines_by_field_ids(request: Request, field_ids: list[int]) -> list[FieldsBaseline]:
    query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_(field_ids))
    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).scalars().all()


async def get_field_baseline(request: Request, field_id: int) -> FieldsBaseline | None:
    field_baselines = await get_field_baselines_by_field_ids(request=request, field_ids=[field_id])
    return field_baselines[0] if field_baselines else None


async def create_field_baseline(request: Request, field_id: int, baseline_year: int, is_returning: int) -> None:
    await create.create(
        request=request,
        instances=[FieldsBaseline(field_id=field_id, baseline_year=baseline_year, is_returning=is_returning)],
        orm_type=FieldsBaseline,
        no_return=True,
        translate=False,
    )


async def update_field_baseline(
    request: Request, field_id: int, baseline_year: int | None = None, is_returning: int | None = None
) -> None:
    async with request.state.sql_session.begin() as s:
        stmt = update(FieldsBaseline)
        if baseline_year:
            stmt = stmt.values(baseline_year=baseline_year)
        if is_returning is not None:
            stmt = stmt.values(is_returning=is_returning)
        stmt = stmt.where(FieldsBaseline.field_id == field_id).execution_options(synchronize_session="fetch")
        await s.execute(stmt)


async def update_or_create_field_baseline_data(
    request: Request, update_baseline_data: list[FieldBaselineUpdateRequest]
) -> None:
    for field_baseline_item in update_baseline_data:
        existing_baseline = await get_field_baseline(request, field_baseline_item.field_id)

        if existing_baseline:
            # update if exists
            await update_field_baseline(
                request,
                field_baseline_item.field_id,
                field_baseline_item.baseline_year,
                field_baseline_item.is_returning,
            )
        else:
            # create if not
            await create_field_baseline(
                request,
                field_baseline_item.field_id,
                field_baseline_item.baseline_year,
                field_baseline_item.is_returning,
            )


async def bulk_create_or_update_field_baselines(
    request: Request, field_ids: list[int], baseline_year: int, is_returning: bool
) -> None:
    existing_field_baselines = await get_field_baselines_by_field_ids(request=request, field_ids=field_ids)
    to_update_field_ids = [baseline.field_id for baseline in existing_field_baselines]
    for field_id in to_update_field_ids:
        await update_field_baseline(
            request=request, field_id=field_id, baseline_year=baseline_year, is_returning=is_returning
        )
    to_create_field_ids = set(field_ids) - set(to_update_field_ids)
    if to_create_field_ids:
        await create.bulk_insert(
            request=request,
            instances=[
                FieldsBaseline(field_id=field_id, baseline_year=baseline_year, is_returning=is_returning)
                for field_id in to_create_field_ids
            ],
            orm_type=FieldsBaseline,
        )
