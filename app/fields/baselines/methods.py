from __future__ import annotations

from collections import defaultdict

from starlette.requests import Request

from field_lineage.methods import set_field_baselines
from fields.baselines.db import get_field_baseline, get_field_baselines_by_field_ids
from fields.db import (
    get_fields_orm_by_field_ids,
    get_program_ids_by_field_ids,
    get_project_ids_by_field_ids,
)
from fields.model import FieldsBaseline


# TODO: remove this after _create_fields stops using this.
async def create_fields_baseline(request: Request, field_id: int, program_id: int) -> None:
    # we shouldn't be creating baselines for a field if it already exists
    existing_baseline = await get_field_baseline(request, field_id)
    if existing_baseline:
        return
    project_ids = await get_project_ids_by_field_ids(request=request, field_ids=[field_id])
    await set_field_baselines(request=request, program_id=program_id, project_id=project_ids[0], field_ids=[field_id])


async def get_or_create_fields_baselines(request: Request, field_ids: list[int]) -> dict[int, FieldsBaseline]:
    """
    Get fields baselines for the given field ids, creating them if they don't exist
    :return: dict of field ids to baselines
    """
    field_baselines = await get_field_baselines_by_field_ids(request=request, field_ids=field_ids)

    if len(field_baselines) < len(field_ids):
        no_baseline_field_ids = list(set(field_ids) - {baseline.field_id for baseline in field_baselines})
        no_baseline_fields = await get_fields_orm_by_field_ids(request=request, field_ids=no_baseline_field_ids)
        field_to_program_id = await get_program_ids_by_field_ids(request=request, field_ids=no_baseline_field_ids)
        project_id_to_no_baseline_field_ids = defaultdict(list)
        for field in no_baseline_fields:
            project_id_to_no_baseline_field_ids[field.parent_project_id].append(field.id)
        for project_id, no_baseline_field_ids in project_id_to_no_baseline_field_ids.items():
            program_id = field_to_program_id[no_baseline_field_ids[0]]
            await set_field_baselines(
                request=request, program_id=program_id, project_id=project_id, field_ids=no_baseline_field_ids
            )

    field_baselines = await get_field_baselines_by_field_ids(request=request, field_ids=field_ids)
    return {baseline.field_id: baseline for baseline in field_baselines}


async def get_or_create_fields_baseline(request: Request, field_id: int) -> FieldsBaseline:
    """
    Get fields baseline for the given field id, creating it if it doesn't exist
    :return: FieldsBaseline if Field exists, otherwise None
    """
    field_id_baseline_map = await get_or_create_fields_baselines(request=request, field_ids=[field_id])
    if field_id not in field_id_baseline_map:
        return None
    return field_id_baseline_map[field_id]


async def get_or_create_fields_baseline_years(request: Request, field_ids: list[int]) -> dict[int, int]:
    field_baselines_map = await get_or_create_fields_baselines(request, field_ids)
    return {field_id: baseline.baseline_year for field_id, baseline in field_baselines_map.items()}
