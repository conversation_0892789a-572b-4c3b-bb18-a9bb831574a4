from __future__ import annotations

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

import elasticapm
from async_property import async_cached_property

from config import get_settings
from db.transaction import Transacted
from fields import paths
from fields.db import (
    get_deleted_fields,
    get_program_ids_by_field_ids,
    get_project_ids_by_field_ids,
)
from fields.methods import hard_delete_field_values
from fields.validators import (
    get_validate_value_progress_by_phase_type,
    validate_program_ids_are_same,
)
from logger import get_logger
from notifications.enums import (
    NotificationCodes,
    NotificationEntityTypes,
    NotificationSources,
)
from notifications.schema import Notification, NotificationBody
from notifications.tasks import create_notification
from phases.db import get_attributes_by_stage_ids
from projects.db import get_field_ids_by_project_ids, get_program_ids_by_project_ids
from values.db import create_values, get_values_by_field_ids
from values.model import Values

logger = get_logger(__name__)

if TYPE_CHECKING:
    from fastapi import Request

    from celery_helper.decorator_class import State
    from phases.enums import PhaseTypes

settings = get_settings()


class BaseOverwriter(ABC):
    def __init__(
        self,
        program_id: int,
        phase_type: PhaseTypes,
        source_field_id: int,
        target_ids: list[int],
        stage_ids: list[int] | None,
        fs_user_id: int,
        fs_impersonator_user_id: int | None = None,
        request: Request | State | None = None,
    ):
        self.program_id = program_id
        self.phase_type = phase_type
        self.source_field_id = source_field_id
        self._target_ids = target_ids
        self.stage_ids = stage_ids
        self.fs_user_id = fs_user_id
        self.fs_impersonator_user_id = fs_impersonator_user_id
        self.request = request

    @property
    @abstractmethod
    async def target_program_ids(self) -> set[int]: ...

    @property
    @abstractmethod
    async def source_field_ids(self) -> list[int]: ...

    async def validate_field_ids(self) -> None:
        validate_program_ids_are_same(
            source_program_id=self.program_id, target_program_ids=await self.target_program_ids
        )

    @elasticapm.async_capture_span()
    async def _overwrite_field_values(self) -> None:
        await self.validate_field_ids()
        progress = get_validate_value_progress_by_phase_type(phase_type=self.phase_type)
        attribute_ids = None
        if self.stage_ids:
            attributes = await get_attributes_by_stage_ids(request=self.request, stage_ids=self.stage_ids)
            attribute_ids = [x.id for x in attributes]
        source_values = await get_values_by_field_ids(
            request=self.request,
            field_ids=[self.source_field_id],
            progresses=[progress],
            attribute_ids=attribute_ids,
            empty_return=True,
        )
        exclude = {"id", "field_id", "updated_at"}
        source_value_dicts = [{k: v for k, v in value.to_dict().items() if k not in exclude} for value in source_values]
        field_ids = await self.source_field_ids

        async with Transacted(self.request) as fake_request:
            await hard_delete_field_values(
                request=fake_request, field_ids=field_ids, progresses=[progress], attribute_ids=attribute_ids
            )

            values_to_create = []
            for field_id in field_ids:
                for source_value_dict in source_value_dicts:
                    values_to_create.append(Values(**source_value_dict, field_id=field_id))

            await create_values(request=fake_request, values=values_to_create)

    @async_cached_property
    @elasticapm.async_capture_span()
    async def source_project_id(self) -> int:
        return (await get_project_ids_by_field_ids(request=self.request, field_ids=[self.source_field_id]))[0]

    async def _create_notification(self) -> Notification:
        return Notification(
            program_id=self.program_id,
            project_id=await self.source_project_id,
            entity_id=self.program_id,
            entity=NotificationEntityTypes.Programs,
            body=NotificationBody(
                url=f"{settings.MRV_INTERNAL_URL}{paths.base}{paths.values}{paths.overwrite}",
                method="POST",
                message="",
            ),
            source=NotificationSources.MRV_VALUES_OVERWRITE,
        )

    @elasticapm.async_capture_span()
    async def run(self) -> None:
        notification = await self._create_notification()
        try:
            notification.body.code = NotificationCodes.PROCESSING
            create_notification.delay(
                notification=notification,
                fs_user_id=self.fs_user_id,
                fs_impersonator_id=self.fs_impersonator_user_id,
            )
            await self._overwrite_field_values()
        except Exception as e:
            notification.body.code = NotificationCodes.ERROR
            raise e
        else:
            notification.body.code = NotificationCodes.ALL_DONE
        finally:
            create_notification.delay(
                notification=notification,
                fs_user_id=self.fs_user_id,
                fs_impersonator_id=self.fs_impersonator_user_id,
            )


class OverwriterByFields(BaseOverwriter):
    @async_cached_property
    @elasticapm.async_capture_span()
    async def target_program_ids(self) -> set[int]:
        return set(
            (await get_program_ids_by_field_ids(request=self.request, field_ids=await self.source_field_ids)).values()
        )

    @async_cached_property
    @elasticapm.async_capture_span()
    async def source_field_ids(self) -> list[int]:
        deleted_fields = await get_deleted_fields(request=self.request, field_ids=self._target_ids)
        return list(set(self._target_ids) - {x.id for x in deleted_fields})


class OverwriterByProjects(BaseOverwriter):
    @async_cached_property
    @elasticapm.async_capture_span()
    async def target_program_ids(self) -> set[int]:
        return set(await get_program_ids_by_project_ids(request=self.request, project_ids=self._target_ids))

    @async_cached_property
    @elasticapm.async_capture_span()
    async def source_field_ids(self) -> list[int]:
        return await get_field_ids_by_project_ids(request=self.request, project_ids=self._target_ids)
