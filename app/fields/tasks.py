from __future__ import annotations

from typing import Any, TYPE_CHECKING

import elasticapm

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from fields.overwrite_values import OverwriterByFields, OverwriterByProjects

if TYPE_CHECKING:
    from fastapi import Request

    from celery_helper.decorator_class import State
    from phases.enums import PhaseTypes

settings = get_settings()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def overwrite_field_values(
    self: Any,
    *,
    program_id: int,
    phase_type: PhaseTypes,
    source_field_id: int,
    target_field_ids: list[int] | None = None,
    target_project_ids: list[int] | None = None,
    target_stage_ids: list[int] | None = None,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | State | None = None,
) -> None:
    kwargs = {
        "program_id": program_id,
        "phase_type": phase_type,
        "source_field_id": source_field_id,
        "stage_ids": target_stage_ids,
        "fs_user_id": fs_user_id,
        "fs_impersonator_user_id": fs_impersonator_user_id,
        "request": request,
    }
    if target_field_ids:
        overwriter = OverwriterByFields(target_ids=target_field_ids, **kwargs)
    else:
        overwriter = OverwriterByProjects(target_ids=target_project_ids, **kwargs)

    await overwriter.run()
