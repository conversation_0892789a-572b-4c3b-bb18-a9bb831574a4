from datetime import datetime

from sqlalchemy import select

from helper.helper import run_query
from projects.farms.model import Farms
from root_crud.create import bulk_insert


async def test_bulk_insert_generic(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    core_farm = await mdl.Groups()
    farm = Farms(
        parent_project_id=project.id,
        core_farm_group_id=core_farm.id,
        farm_name="test_farm",
        created_at=datetime(2025, 1, 1),
        updated_at=datetime(2025, 1, 1),
        deleted_at=None,
    )

    await bulk_insert(request=app_request, instances=[farm], orm_type=Farms)

    async with db_session_maker() as s:
        query = select(Farms).where(Farms.core_farm_group_id == core_farm.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == Farms(
                id=res[0].id,
                parent_project_id=project.id,
                core_farm_group_id=core_farm.id,
                farm_name="test_farm",
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert res[0].created_at > farm.created_at
        assert res[0].updated_at > farm.updated_at
