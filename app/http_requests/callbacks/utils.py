import hashlib
from collections import defaultdict

from fastapi import Request

from boundaries_service.schema import FeatureIntersections, Intersection
from fields.db import get_fields_by_md5s_and_program_id
from fields.model import Fields
from helper.async_tools import Tasks
from http_requests.callbacks.constants import M2_PER_HECTARE
from http_requests.crons.enums import BoundariesServiceAPICall
from http_requests.crons.model import FieldO<PERSON>lapResult
from logger import get_logger
from root_crud import create

logger = get_logger(__name__)


def m2_to_hectare(area_m2: float) -> float:
    """
    Convert area in square meters to hectares
    """
    return area_m2 / M2_PER_HECTARE


async def process_feature_intersection_results(
    *, request: Request, intersections: FeatureIntersections, program_id: int, previous_program_id: int, task_id: int
) -> None:
    """
    Process the results of a Boundaries Service feature intersection API call, and create FieldOverlapResult instances.
    This is to determine the overlap between fields in two programs, one succeeding the other.

    Args:
        request: The FastAPI request object
        intersections: The results of the feature intersection API call
        program_id: The ID of the program for which the feature intersection was called
        previous_program_id: The ID of the program that preceded the program for which the feature intersection was
            called
        task_id: The ID of the task that initiated the feature intersection API call
    """
    prog_md5s, prev_prog_md5s = get_md5s_from_intersections(intersections)
    prog_fields_by_md5 = await get_fields_by_md5s(request, prog_md5s, program_id)
    prev_prog_fields_by_md5 = await get_fields_by_md5s(request, prev_prog_md5s, previous_program_id)
    instances: list[FieldOverlapResult] = []
    for md5, field_intersections in intersections.feature_intersections.items():
        for intersection in field_intersections:
            if intersection.percent_second_rounded or intersection.intersection_m2_rounded:
                prev_md5 = intersection.intersecting_id
                fields = prog_fields_by_md5.get(md5)
                if not fields:
                    logger.error(f"Cannot find field {md5} in program {program_id}")
                    continue
                prev_fields = prev_prog_fields_by_md5.get(prev_md5)
                if not prev_fields:
                    logger.error(f"Cannot find field {prev_md5} in program {previous_program_id}")
                    continue
                _add_field_overlaps(fields, prev_fields, md5, intersection, task_id, instances)
    if instances:
        await create.create(
            request=request, instances=instances, orm_type=FieldOverlapResult, translate=False, no_return=True
        )


def get_md5s_from_intersections(intersections: FeatureIntersections) -> tuple[set[str], set[str]]:
    """
    Extract the MD5s of the fields involved in the feature intersections, where the overlap is non-zero.

    Args:
        intersections: The results of the feature intersection API call

    Returns:
        A tuple containing two sets of MD5s:
            - The MD5s of the fields in the program for which the feature intersection was called
            - The MD5s of the fields in the program that preceded the program for which the feature intersection was
                called
    """
    prog_md5s = set()
    prev_prog_md5s = set()
    for md5, field_intersections in intersections.feature_intersections.items():
        for intersection in field_intersections:
            if intersection.percent_second_rounded or intersection.intersection_m2_rounded:
                prog_md5s.add(md5)
                prev_prog_md5s.add(intersection.intersecting_id)
    return prog_md5s, prev_prog_md5s


def _add_field_overlaps(
    fields: list[Fields],
    previous_fields: list[Fields],
    md5: str,
    intersection: Intersection,
    task_id: int,
    instances: list[FieldOverlapResult],
) -> None:
    """
    Add FieldOverlapResult instances to the list of instances, based on the intersection between fields in two programs.

    Args:
        fields: List of fields corresponding to `md5` - usually contains just one element
        previous_fields: List of fields corresponding to `intersection.intersecting_id` - usually contains just one
            element
        md5: The MD5 of the field in current program which is intersecting with a field in the previous program
        intersection: The intersection between the field in the current program and the field in the previous program
        task_id: The ID of the task that initiated the feature intersection API call
        instances: The list of FieldOverlapResult instances to which the new instances should be added
    """
    percent_intersected = intersection.percent_second_rounded
    intersection_area = intersection.intersection_m2_rounded
    if not (percent_intersected or intersection_area):
        raise ValueError(
            "_add_field_overlaps: percentage or area (or both) must be non-zero "
            f"{percent_intersected=} {intersection_area}"
        )
    for field in fields:
        for prev_field in previous_fields:
            instances.append(
                FieldOverlapResult(
                    task_id=task_id,
                    field_id=field.id,
                    field_md5=md5,
                    previous_field_id=prev_field.id,
                    previous_field_md5=intersection.intersecting_id,
                    percentage_overlap=percent_intersected,
                    area_intersection_ha=m2_to_hectare(intersection_area),
                )
            )


async def get_fields_by_md5s(request: Request, md5s: set[str], program_id: int) -> dict[str, list[Fields]]:
    """
    Given a list of md5s and a program id, find all non-deleted fields, in non-deleted projects, in this program
    that have those md5s, and return them in a dictionary where the keys are the md5s and the values are the fields.

    Given that multiple fields can have the same md5, the values in the dictionary are lists of fields.

    Args:
        request: The FastAPI request object
        md5s: A set of md5s to search for
        program_id: The ID of the program to search in
    """
    fields = await get_fields_by_md5s_and_program_id(request, list(md5s), program_id)
    fields_ids_by_md5 = defaultdict(list)
    for field in fields:
        fields_ids_by_md5[field.md5].append(field)
    return fields_ids_by_md5


def sha256_param(api_call: BoundariesServiceAPICall, md5s_1: list[str], md5s_2: list[str]) -> str:
    """
    Generate a SHA-256 hash based on the API call, the first set of MD5s, and the second set of MD5s.

    The hash is generated by sorting the MD5s (so it doesn't matter what order they're in), concatenating the MD5s in
    both sets, and adding the name of the API call in between the two sets of MD5s.
    """
    hasher = hashlib.sha256()
    # We need to sort the md5s to ensure the same hash is generated regardless of the order of the md5s
    for md5 in sorted(md5s_1):
        hasher.update(md5.encode())
    hasher.update(api_call.value.encode())
    for md5 in sorted(md5s_2):
        hasher.update(md5.encode())
    return hasher.hexdigest()


async def create_fully_overlapped_field_results(
    *,
    request: Request,
    md5s: list[str],
    previous_md5s: list[str],
    program_id: int,
    previous_program_id: int,
    task_id: int,
) -> None:
    """
    Fields that carry on from one program to the next (i.e. fields with the same md5) are considered to be fully
    overlapped. This function creates FieldOverlapResult instances for those fields and saves them to the DB.
    """
    md5s_in_common = set(md5s) & set(previous_md5s)
    if not md5s_in_common:
        return
    tasks = Tasks()
    tasks.add(get_fields_by_md5s(request, md5s_in_common, program_id))
    tasks.add(get_fields_by_md5s(request, md5s_in_common, previous_program_id))
    prog_fields_by_md5, prev_prog_fields_by_md5 = await tasks.complete_all()
    instances = []
    for md5, fields in prog_fields_by_md5.items():
        prev_fields = prev_prog_fields_by_md5.get(md5)
        if fields and prev_fields:
            for field in fields:
                for prev_field in prev_fields:
                    instances.append(
                        FieldOverlapResult(
                            task_id=task_id,
                            field_id=field.id,
                            field_md5=md5,
                            previous_field_id=prev_field.id,
                            previous_field_md5=md5,
                            percentage_overlap=100,
                            area_intersection_ha=field.area,
                        )
                    )
    if instances:
        await create.create(
            request=request, instances=instances, orm_type=FieldOverlapResult, translate=False, no_return=True
        )
