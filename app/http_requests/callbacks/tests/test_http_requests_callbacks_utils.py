import random
import re
from collections import defaultdict
from datetime import datetime

import sqlalchemy as sa

from boundaries_service.schema import FeatureIntersections, Intersection
from fields.model import Fields
from helper.helper import run_query
from http_requests.callbacks.utils import (
    _add_field_overlaps,
    create_fully_overlapped_field_results,
    get_fields_by_md5s,
    get_md5s_from_intersections,
    process_feature_intersection_results,
    sha256_param,
)
from http_requests.crons.enums import BoundariesServiceAPICall
from http_requests.crons.model import FieldOverlapResult


async def test_get_fields_by_md5s(mdl, app_request, faker):
    prog1 = await mdl.Programs()
    prog2 = await mdl.Programs()
    proj1_1 = await mdl.Projects(program_id=prog1.id)
    proj1_2 = await mdl.Projects(program_id=prog1.id)
    proj1_3 = await mdl.Projects(program_id=prog1.id, deleted_at=datetime.now())

    proj2_1 = await mdl.Projects(program_id=prog2.id)

    md5s_to_check = set()
    expected_result = defaultdict(set)

    field1_1_1 = await mdl.Fields(parent_project_id=proj1_1.id, md5=faker.md5())
    md5s_to_check.add(field1_1_1.md5)
    expected_result[field1_1_1.md5].add(field1_1_1.id)

    # Add a field with the same md5 to another project
    field1_2_1 = await mdl.Fields(parent_project_id=proj1_2.id, md5=field1_1_1.md5)
    expected_result[field1_1_1.md5].add(field1_2_1.id)

    # Add a field with another md5 to the first project
    field1_1_2 = await mdl.Fields(parent_project_id=proj1_1.id, md5=faker.md5())
    md5s_to_check.add(field1_1_2.md5)
    expected_result[field1_1_2.md5].add(field1_1_2.id)

    # Add a deleted field to first project - shouldn't appear in the result
    field1_1_3 = await mdl.Fields(parent_project_id=proj1_1.id, md5=faker.md5(), deleted_at=datetime.now())
    md5s_to_check.add(field1_1_3.md5)

    # Add a non-deleted field to third (deleted) project - shouldn't appear in the result
    field1_3_1 = await mdl.Fields(parent_project_id=proj1_3.id, md5=faker.md5())
    md5s_to_check.add(field1_3_1.md5)

    # Add a field to the second program - shouldn't appear in the result
    field2_1_1 = await mdl.Fields(parent_project_id=proj2_1.id, md5=faker.md5())
    md5s_to_check.add(field2_1_1.md5)

    # Add a field to the first project without checking for it - shouldn't appear in the result
    await mdl.Fields(parent_project_id=proj1_1.id, md5=faker.md5())

    # Add non-existing md5 - shouldn't appear in the result
    md5s_to_check |= {faker.md5() for _ in range(3)}

    result = await get_fields_by_md5s(app_request, md5s_to_check, prog1.id)
    assert set(result.keys()) == set(expected_result.keys())
    for md5, fields in result.items():
        assert {field.id for field in fields} == expected_result[md5]


async def test_get_md5s_from_intersections(faker):
    m1, m2, m3, m4, m5, m6, m7 = (faker.md5() for _ in range(7))
    intersections = FeatureIntersections.parse_obj(
        {
            "feature_intersections": {
                m1: [
                    {
                        "intersecting_id": m2,
                        "percent_intersection_first": 0,
                        "percent_intersection_second": 0,
                        "area_intersection_m2": 0,
                    },
                ],
                m3: [
                    {
                        "intersecting_id": m4,
                        "percent_intersection_first": 1,
                        "percent_intersection_second": 0,
                        "area_intersection_m2": 1,
                    },
                    {
                        "intersecting_id": m5,
                        "percent_intersection_first": 0,
                        "percent_intersection_second": 1,
                        "area_intersection_m2": 1,
                    },
                ],
                m6: [
                    {
                        "intersecting_id": m7,
                        "percent_intersection_first": 1,
                        "percent_intersection_second": 1,
                        "area_intersection_m2": 1,
                    },
                ],
            }
        }
    )
    prog_md5s, prev_prog_md5s = get_md5s_from_intersections(intersections)
    assert prog_md5s == {m3, m6}
    assert prev_prog_md5s == {m4, m5, m7}


def test_add_field_overlaps(faker):
    m1 = faker.md5()
    m2 = faker.md5()
    # 2 fields in the first program with the same md5
    fields = [Fields(id=i, md5=m1) for i in range(1, 3)]
    # and 3 fields in the previous program with the same md5
    prev_fields = [Fields(id=i, md5=m2) for i in range(3, 6)]
    intersection = Intersection(
        intersecting_id=m2,
        percent_intersection_first=50,
        percent_intersection_second=25,
        area_intersection_m2=20_000,
    )
    instances = []
    _add_field_overlaps(fields, prev_fields, m1, intersection, 1, instances)
    # There should be 6 instances created, representing two fields in the first program and overlapping with
    # the three previous program (2 * 3 = 6)
    assert len(instances) == 6
    instance_dicts = [instance.to_dict() for instance in instances]
    for field in fields:
        for prev_field in prev_fields:
            assert {
                "task_id": 1,
                "field_id": field.id,
                "field_md5": m1,
                "previous_field_id": prev_field.id,
                "previous_field_md5": m2,
                "percentage_overlap": 25,
                "area_intersection_ha": 2,
                "id": None,
                "created_at": None,
            } in instance_dicts


async def test_process_feature_intersection_results(mdl, db_session_maker, app_request, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prev_prog.id)
    field = await mdl.Fields(parent_project_id=proj.id, md5=faker.md5())
    prev_field = await mdl.Fields(parent_project_id=prev_proj.id, md5=faker.md5())
    intersections = FeatureIntersections.parse_obj(
        {
            "feature_intersections": {
                field.md5: [
                    {
                        "intersecting_id": prev_field.md5,
                        "percent_intersection_first": 50,
                        "percent_intersection_second": 25,
                        "area_intersection_m2": 20_000,
                    },
                ],
            }
        }
    )
    await process_feature_intersection_results(
        request=app_request,
        intersections=intersections,
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        task_id=task.id,
    )
    async with db_session_maker() as s:
        query = sa.select(FieldOverlapResult).where(
            FieldOverlapResult.task_id == task.id,
            FieldOverlapResult.field_id == field.id,
            FieldOverlapResult.previous_field_id == prev_field.id,
            FieldOverlapResult.field_md5 == field.md5,
            FieldOverlapResult.previous_field_md5 == prev_field.md5,
            FieldOverlapResult.percentage_overlap == 25,
            FieldOverlapResult.area_intersection_ha == 2,
        )
        result = (await run_query(s, query)).all()
        assert len(result) == 1


def test_sha256_param(faker):
    m1, m2, m3, m4 = (faker.md5() for _ in range(4))

    # Check that the hash is a valid SHA-256 hash
    hash1 = sha256_param(BoundariesServiceAPICall.union_areas, [m1, m2], [m3, m4])
    assert re.match(r"^[0-9a-f]{64}$", hash1)

    # Check that hash is the same with the same inputs
    hash2 = sha256_param(BoundariesServiceAPICall.union_areas, [m1, m2], [m3, m4])
    assert hash1 == hash2

    # Check that hash the same with different order of md5s
    hash3 = sha256_param(BoundariesServiceAPICall.union_areas, [m2, m1], [m4, m3])
    assert hash1 == hash3

    # Check that hash is different with different API call
    hash4 = sha256_param(BoundariesServiceAPICall.feature_intersections, [m1, m2], [m3, m4])
    assert hash1 != hash4


async def test_create_fully_overlapped_field_results(mdl, db_session_maker, app_request, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prev_prog.id)
    md5 = faker.md5()
    # Create 3 fields in current program with the same md5
    area = random.randint(10_000, 100_000) / 10_000
    fields = [await mdl.Fields(parent_project_id=proj.id, md5=md5, area=area) for _ in range(3)]
    # Create 2 fields in previous program with the same md5
    prev_fields = [await mdl.Fields(parent_project_id=prev_proj.id, md5=md5, area=area) for _ in range(2)]
    await create_fully_overlapped_field_results(
        request=app_request,
        md5s=[md5],
        previous_md5s=[md5],
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        task_id=task.id,
    )
    async with db_session_maker() as s:
        # 3 fields in current program, should overlap with all 2 fields in previous program - 6 overlaps in total
        for field in fields:
            for prev_field in prev_fields:
                query = sa.select(FieldOverlapResult).where(
                    FieldOverlapResult.task_id == task.id,
                    FieldOverlapResult.field_id == field.id,
                    FieldOverlapResult.previous_field_id == prev_field.id,
                    FieldOverlapResult.field_md5 == md5,
                    FieldOverlapResult.previous_field_md5 == md5,
                    FieldOverlapResult.percentage_overlap == 100,
                    FieldOverlapResult.area_intersection_ha == area,
                )
                result = (await run_query(s, query)).all()
                assert len(result) == 1
