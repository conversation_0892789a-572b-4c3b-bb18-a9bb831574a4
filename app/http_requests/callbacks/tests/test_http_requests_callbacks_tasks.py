import random
import uuid
from unittest.mock import patch

from boundaries_service.enums import JobStatus
from boundaries_service.schema import FeatureIntersections, UnionArea
from helper.helper import assert_dict_contains_subset
from http_requests.callbacks.tasks import (
    _cache_api_result,
    _get_job_result_for_db,
    _update_and_return_request,
    get_and_process_feature_intersections_results,
    get_and_process_program_union_results_task,
    get_and_process_project_union_results_task,
    get_and_process_region_union_results_task,
    get_and_process_union_area_results,
    get_program_result_from_union_area,
    get_project_result_from_union_area,
    get_region_result_from_union_area,
)
from http_requests.crons.model import (
    BoundariesServiceResult,
    ProgramOverlapRequest,
    ProgramOverlapResult,
    ProjectOverlapRequest,
    ProjectOverlapResult,
    RegionOverlapRequest,
    RegionOverlapResult,
)
from root_crud import delete, get


async def test_cache_api_result(mdl, app_request, faker):
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    task = await mdl.DetermineOverlapTask()
    request = await mdl.RegionOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, core_region_id=1
    )
    fake_result = {faker.name(): faker.random_number()}

    # Check that it works in the absence of a cache row
    await _cache_api_result(app_request, request, fake_result)
    rows = await get.get(request=app_request, orm_type=BoundariesServiceResult, empty_return=True)
    assert len(rows) == 0

    # Check that object is saved in cache
    result_cache = await mdl.BoundariesServiceResult(params_sha256="bogus shasum")
    request = await mdl.RegionOverlapRequest(
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        task_id=task.id,
        core_region_id=1,
        cache_id=result_cache.id,
    )
    fake_result = {faker.name(): faker.random_number()}
    await _cache_api_result(app_request, request, fake_result)
    rows = await get.get(request=app_request, orm_type=BoundariesServiceResult, empty_return=True)
    assert len(rows) == 1
    assert rows[0].result == fake_result

    # Check that if cache is deleted, it still works
    await delete.hard_orm(request=app_request, instances=[result_cache])
    await _cache_api_result(app_request, request, fake_result)


def test_get_program_result_from_union_area(faker):
    request = ProgramOverlapRequest(
        task_id=faker.random_number(), program_id=faker.random_number(), previous_program_id=faker.random_number()
    )
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=faker.random_number(),
    )
    obj, cls = get_program_result_from_union_area(request, union_area)
    assert cls == ProgramOverlapResult
    assert (
        obj.to_dict()
        == ProgramOverlapResult(
            program_id=request.program_id,
            previous_program_id=request.previous_program_id,
            task_id=request.task_id,
            program_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_program_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=union_area.area_intersection_union_m2_both / 10000,
        ).to_dict()
    )

    # Test that None intersection value is handled
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=None,
    )
    obj, _ = get_program_result_from_union_area(request, union_area)
    assert (
        obj.to_dict()
        == ProgramOverlapResult(
            program_id=request.program_id,
            previous_program_id=request.previous_program_id,
            task_id=request.task_id,
            program_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_program_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=0,
        ).to_dict()
    )


def test_get_project_result_from_union_area(faker):
    request = ProjectOverlapRequest(
        task_id=faker.random_number(), project_id=faker.random_number(), previous_project_id=faker.random_number()
    )
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=faker.random_number(),
    )
    obj, cls = get_project_result_from_union_area(request, union_area)
    assert cls == ProjectOverlapResult
    assert (
        obj.to_dict()
        == ProjectOverlapResult(
            project_id=request.project_id,
            previous_project_id=request.previous_project_id,
            task_id=request.task_id,
            project_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_project_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=union_area.area_intersection_union_m2_both / 10000,
        ).to_dict()
    )

    # Test that None intersection value is handled
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=None,
    )
    obj, _ = get_project_result_from_union_area(request, union_area)
    assert (
        obj.to_dict()
        == ProjectOverlapResult(
            project_id=request.project_id,
            previous_project_id=request.previous_project_id,
            task_id=request.task_id,
            project_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_project_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=0,
        ).to_dict()
    )


def test_get_region_result_from_union_area(faker):
    request = RegionOverlapRequest(
        task_id=faker.random_number(),
        program_id=faker.random_number(),
        previous_program_id=faker.random_number(),
        core_region_id=faker.random_number(),
    )
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=faker.random_number(),
    )
    obj, cls = get_region_result_from_union_area(request, union_area)
    assert cls == RegionOverlapResult
    assert (
        obj.to_dict()
        == RegionOverlapResult(
            program_id=request.program_id,
            previous_program_id=request.previous_program_id,
            task_id=request.task_id,
            core_region_id=request.core_region_id,
            program_region_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_program_region_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=union_area.area_intersection_union_m2_both / 10000,
        ).to_dict()
    )

    # Test that None intersection value is handled
    union_area = UnionArea(
        list_one_union_area_m2=faker.random_number(),
        list_two_union_area_m2=faker.random_number(),
        area_intersection_union_m2_both=None,
    )
    obj, _ = get_region_result_from_union_area(request, union_area)
    assert (
        obj.to_dict()
        == RegionOverlapResult(
            program_id=request.program_id,
            previous_program_id=request.previous_program_id,
            task_id=request.task_id,
            core_region_id=request.core_region_id,
            program_region_area_ha=union_area.list_one_union_area_m2 / 10000,
            previous_program_region_area_ha=union_area.list_two_union_area_m2 / 10000,
            area_intersection_ha=0,
        ).to_dict()
    )


async def test_update_and_return_request(app_request, mdl, faker):
    job_id = str(uuid.uuid4())
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    task = await mdl.DetermineOverlapTask()

    # Check that successful request is handled correctly
    async_request_successful = await mdl.RegionOverlapRequest(
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        task_id=task.id,
        core_region_id=1,
        status=JobStatus.successful,
    )
    bs_result = {
        "list_one_union_area_m2": faker.random_number(),
        "list_two_union_area_m2": faker.random_number(),
        "area_intersection_union_m2_both": faker.random_number(),
    }
    with patch("http_requests.callbacks.tasks.get_job_results", return_value=bs_result):
        res, api_result = await _update_and_return_request(
            request_id=async_request_successful.id,
            job_id=job_id,
            request_orm_type=RegionOverlapRequest,
            request=app_request,
        )
    async_request = (
        await get.get(
            request=app_request,
            orm_type=RegionOverlapRequest,
            id_field=RegionOverlapRequest.id,
            ids=[async_request_successful.id],
        )
    )[0]
    assert res.id == async_request.id
    assert api_result == bs_result
    assert async_request.result == bs_result

    # Check that failed request is handled correctly
    async_request_failed = await mdl.RegionOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, core_region_id=1, status=JobStatus.failed
    )
    traceback = faker.text()
    job_info = {"tracebacks": [{"traceback": traceback}]}
    with patch("http_requests.callbacks.tasks.get_job", return_value=job_info):
        res, api_result = await _update_and_return_request(
            request_id=async_request_failed.id,
            job_id=job_id,
            request_orm_type=RegionOverlapRequest,
            request=app_request,
        )
        async_request = (
            await get.get(
                request=app_request,
                orm_type=RegionOverlapRequest,
                id_field=RegionOverlapRequest.id,
                ids=[async_request_failed.id],
            )
        )[0]
    assert res.id == async_request.id
    assert api_result == {"traceback": traceback}
    assert async_request.result == {"traceback": traceback}


async def test_update_and_return_request_with_cache(app_request, mdl, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    union_area_result = {
        "list_one_union_area_m2": faker.random_number(),
        "list_two_union_area_m2": faker.random_number(),
        "area_intersection_union_m2_both": faker.random_number(),
    }

    # What happens when boundaries service endpoint failed?
    request = await mdl.ProgramOverlapRequest(
        task_id=task.id, program_id=prog.id, previous_program_id=prev_prog.id, status=JobStatus.failed
    )
    job_info = {"tracebacks": [{"traceback": faker.text()}]}
    with patch("http_requests.callbacks.tasks.get_job", return_value=job_info):
        await get_and_process_union_area_results(
            request_id=request.id,
            job_id=str(uuid.uuid4()),
            request_orm_type=ProgramOverlapRequest,
            get_result_func=get_program_result_from_union_area,
            request=app_request,
        )
    # No ProgramOverlapResult will have been created, because the request was marked as failed
    assert len(await get.get(request=app_request, orm_type=ProgramOverlapResult, empty_return=True)) == 0

    request = await mdl.ProgramOverlapRequest(
        task_id=task.id, program_id=prog.id, previous_program_id=prev_prog.id, status=JobStatus.successful
    )
    with patch("http_requests.callbacks.tasks.get_job_results", return_value=union_area_result):
        await get_and_process_union_area_results(
            request_id=request.id,
            job_id=str(uuid.uuid4()),
            request_orm_type=ProgramOverlapRequest,
            get_result_func=get_program_result_from_union_area,
            request=app_request,
        )
    # We should get a ProgramOverlapResult in the DB
    rows = await get.get(request=app_request, orm_type=ProgramOverlapResult, empty_return=True)
    assert len(rows) == 1
    result = rows[0]
    result_dict = result.to_dict()
    # Convert Decimal values to float for comparison
    for key_name in ["program_area_ha", "previous_program_area_ha", "area_intersection_ha"]:
        result_dict[key_name] = float(result_dict[key_name])
    assert_dict_contains_subset(
        result_dict,
        {
            "program_id": prog.id,
            "previous_program_id": prev_prog.id,
            "task_id": task.id,
            "program_area_ha": union_area_result["list_one_union_area_m2"] / 10000,
            "previous_program_area_ha": union_area_result["list_two_union_area_m2"] / 10000,
            "area_intersection_ha": union_area_result["area_intersection_union_m2_both"] / 10000,
        },
    )


async def test_get_and_process_feature_intersections_results(mdl, app_request, faker):
    def area():
        return faker.random_number()

    def pc():
        return random.randint(1, 9999) / 100

    feature_intersections_result = {
        "feature_intersections": {
            faker.md5(): [
                {
                    "intersecting_id": faker.md5(),
                    "area_intersection_m2": area(),
                    "percent_intersection_first": pc(),
                    "percent_intersection_second": pc(),
                },
                {
                    "intersecting_id": faker.md5(),
                    "area_intersection_m2": area(),
                    "percent_intersection_first": pc(),
                    "percent_intersection_second": pc(),
                },
            ],
            faker.md5(): [
                {
                    "intersecting_id": faker.md5(),
                    "area_intersection_m2": area(),
                    "percent_intersection_first": pc(),
                    "percent_intersection_second": pc(),
                },
            ],
        },
    }
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()

    with patch("http_requests.callbacks.tasks.process_feature_intersection_results") as mock:
        # Check that a failed feature-intersection request doesn't call the processing function
        request = await mdl.ProgramOverlapRequest(
            task_id=task.id, program_id=prog.id, previous_program_id=prev_prog.id, status=JobStatus.failed
        )
        job_info = {"tracebacks": [{"traceback": faker.text()}]}
        with patch("http_requests.callbacks.tasks.get_job", return_value=job_info):
            get_and_process_feature_intersections_results(
                request_id=request.id,
                job_id=str(uuid.uuid4()),
                fs_user_id=app_request.state.fs_user_id,
                request=app_request,
                fs_impersonator_user_id=app_request.state.fs_impersonator_user_id,
            )
        mock.assert_not_called()

        # Check that a successful feature-intersection request calls the processing function
        request = await mdl.ProgramOverlapRequest(
            task_id=task.id, program_id=prog.id, previous_program_id=prev_prog.id, status=JobStatus.successful
        )
        with patch("http_requests.callbacks.tasks.get_job_results", return_value=feature_intersections_result):
            get_and_process_feature_intersections_results(
                request_id=request.id,
                job_id=str(uuid.uuid4()),
                fs_user_id=app_request.state.fs_user_id,
                request=app_request,
                fs_impersonator_user_id=app_request.state.fs_impersonator_user_id,
            )
            mock.assert_called_with(
                request=app_request,
                intersections=FeatureIntersections.parse_obj(feature_intersections_result),
                program_id=prog.id,
                previous_program_id=prev_prog.id,
                task_id=task.id,
            )


def test_get_job_result_for_db(faker):
    assert _get_job_result_for_db(None) is None
    small_result = {"a": 1, "b": 2}
    assert _get_job_result_for_db(small_result) == small_result
    large_result = {str(uuid.uuid4()): [faker.random_number() for _ in range(100)] for _ in range(100)}
    assert "Result too large" in _get_job_result_for_db(large_result)


@patch("http_requests.callbacks.tasks.get_and_process_union_area_results")
async def test_get_and_process_program_union_results_task(mock, app_request):
    job_id = str(uuid.uuid4())
    request_id = random.randint(1, 100)
    get_and_process_program_union_results_task(request_id=request_id, job_id=job_id, fs_user_id=1, request=app_request)
    mock.assert_called_with(
        request_id=request_id,
        job_id=job_id,
        request_orm_type=ProgramOverlapRequest,
        get_result_func=get_program_result_from_union_area,
        request=app_request,
    )


@patch("http_requests.callbacks.tasks.get_and_process_union_area_results")
async def test_get_and_process_project_union_results_task(mock, app_request):
    job_id = str(uuid.uuid4())
    request_id = random.randint(1, 100)
    get_and_process_project_union_results_task(request_id=request_id, job_id=job_id, fs_user_id=1, request=app_request)
    mock.assert_called_with(
        request_id=request_id,
        job_id=job_id,
        request_orm_type=ProjectOverlapRequest,
        get_result_func=get_project_result_from_union_area,
        request=app_request,
    )


@patch("http_requests.callbacks.tasks.get_and_process_union_area_results")
async def test_get_and_process_region_union_results_task(mock, app_request):
    job_id = str(uuid.uuid4())
    request_id = random.randint(1, 100)
    get_and_process_region_union_results_task(request_id=request_id, job_id=job_id, fs_user_id=1, request=app_request)
    mock.assert_called_with(
        request_id=request_id,
        job_id=job_id,
        request_orm_type=RegionOverlapRequest,
        get_result_func=get_region_result_from_union_area,
        request=app_request,
    )
