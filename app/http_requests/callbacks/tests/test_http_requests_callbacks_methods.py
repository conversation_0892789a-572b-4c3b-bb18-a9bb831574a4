import uuid
from unittest.mock import AsyncMock

from sqlalchemy import select

from boundaries_service.enums import Job<PERSON>tatus
from boundaries_service.schema import BoundariesServiceJob
from helper.helper import assert_dict_contains_subset, run_query
from http_requests.callbacks.constants import PROCESS_CALLBACKS_DELAY_SECONDS
from http_requests.callbacks.methods import handle_boundaries_service_results
from http_requests.crons.model import ProgramOverlapRequest


async def test_handle_boundaries_service_results(mdl, app_request, db_session_maker):
    task = await mdl.DetermineOverlapTask()
    prev_prog = await mdl.Programs()
    prog = await mdl.Programs(previous_program_id=prev_prog.id)
    async_request = await mdl.ProgramOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, status=JobStatus.accepted
    )

    job = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.successful)
    mock_process_task = AsyncMock()
    await handle_boundaries_service_results(
        request=app_request,
        orm_type=ProgramOverlapRequest,
        request_id=async_request.id,
        job=job,
        process_results_task=mock_process_task,
    )
    async with db_session_maker() as s:
        query = select(ProgramOverlapRequest).where(
            ProgramOverlapRequest.id == async_request.id,
            ProgramOverlapRequest.job_id == job.job_id,
            ProgramOverlapRequest.status == job.status,
        )
        (await run_query(s, query)).one()
    assert mock_process_task.apply_async.call_args.kwargs["countdown"] == PROCESS_CALLBACKS_DELAY_SECONDS
    assert_dict_contains_subset(
        mock_process_task.apply_async.call_args.args[1], {"request_id": async_request.id, "job_id": job.job_id}
    )
