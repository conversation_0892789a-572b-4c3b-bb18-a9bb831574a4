import random
import uuid
from unittest.mock import patch

from boundaries_service.enums import JobStatus
from boundaries_service.schema import BoundariesServiceJob
from helper.helper import assert_dict_contains_subset
from http_requests.callbacks import paths
from http_requests.callbacks.tasks import (
    get_and_process_feature_intersections_results,
    get_and_process_program_union_results_task,
    get_and_process_project_union_results_task,
    get_and_process_region_union_results_task,
)
from http_requests.crons.model import (
    ProgramOverlapRequest,
    ProjectOverlapRequest,
    RegionOverlapRequest,
)


@patch("http_requests.callbacks.router.handle_boundaries_service_results")
async def test_program_union_results(mock_handle, mdl, async_client, app_request):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    request = await mdl.ProgramOverlapRequest(program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id)
    url = paths.base + paths.program_union_results.format(request_id=request.id)
    job = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.successful)
    response = await async_client.post(url, json=job.dict(by_alias=True))
    assert response.status_code == 204
    mock_handle.assert_called_once()
    assert_dict_contains_subset(
        mock_handle.mock_calls[0].kwargs,
        {
            "orm_type": ProgramOverlapRequest,
            "request_id": request.id,
            "job": job,
            "process_results_task": get_and_process_program_union_results_task,
        },
    )


@patch("http_requests.callbacks.router.handle_boundaries_service_results")
async def test_project_union_results(mock_handle, mdl, async_client, app_request):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prog.id)
    request = await mdl.ProjectOverlapRequest(project_id=proj.id, previous_project_id=prev_proj.id, task_id=task.id)
    url = paths.base + paths.project_union_results.format(request_id=request.id)
    job = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.successful)
    response = await async_client.post(url, json=job.dict(by_alias=True))
    assert response.status_code == 204
    mock_handle.assert_called_once()
    assert_dict_contains_subset(
        mock_handle.mock_calls[0].kwargs,
        {
            "orm_type": ProjectOverlapRequest,
            "request_id": request.id,
            "job": job,
            "process_results_task": get_and_process_project_union_results_task,
        },
    )


@patch("http_requests.callbacks.router.handle_boundaries_service_results")
async def test_feature_overlap_results(mock_handle, mdl, async_client, app_request):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    request = await mdl.ProgramOverlapRequest(program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id)
    url = paths.base + paths.feature_overlap_results.format(request_id=request.id)
    job = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.successful)
    response = await async_client.post(url, json=job.dict(by_alias=True))
    assert response.status_code == 204
    mock_handle.assert_called_once()
    assert_dict_contains_subset(
        mock_handle.mock_calls[0].kwargs,
        {
            "orm_type": ProgramOverlapRequest,
            "request_id": request.id,
            "job": job,
            "process_results_task": get_and_process_feature_intersections_results,
        },
    )


@patch("http_requests.callbacks.router.handle_boundaries_service_results")
async def test_region_union_results(mock_handle, mdl, async_client, app_request):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    region_id = random.randint(100, 200)
    request = await mdl.RegionOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, core_region_id=region_id
    )
    url = paths.base + paths.region_union_results.format(request_id=request.id)
    job = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.successful)
    response = await async_client.post(url, json=job.dict(by_alias=True))
    assert response.status_code == 204
    mock_handle.assert_called_once()
    assert_dict_contains_subset(
        mock_handle.mock_calls[0].kwargs,
        {
            "orm_type": RegionOverlapRequest,
            "request_id": request.id,
            "job": job,
            "process_results_task": get_and_process_region_union_results_task,
        },
    )


async def test_options_endpoints(async_client):
    fragments = [
        paths.program_union_results,
        paths.project_union_results,
        paths.feature_overlap_results,
        paths.region_union_results,
    ]
    for fragment in fragments:
        url = paths.base + fragment.format(request_id=1)
        response = await async_client.options(url)
        assert response.status_code == 204
        assert response.headers["Allow"] == "POST"
