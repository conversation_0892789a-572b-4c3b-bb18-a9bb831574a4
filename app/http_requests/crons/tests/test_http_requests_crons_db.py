import random
from datetime import datetime, timedelta
from unittest.mock import patch

from override_settings import async_override_settings

from boundaries_service.dataclasses import KMLFileUpdateData
from boundaries_service.enums import JobStatus
from config import get_settings
from core.model import KMLFiles
from fields.model import Fields
from http_requests.crons.db import (
    create_overlap_task,
    delete_old_cached_results,
    delete_old_overlap_tasks,
    get_kml_files,
    get_or_create_cached_result,
    get_unfinished_overlap_request_count,
    update_kml_files,
)
from http_requests.crons.model import (
    BoundariesServiceResult,
    DetermineOverlapTask,
    FieldOverlapResult,
    ProgramOverlapRequest,
    ProgramOverlapResult,
    ProjectOverlapRequest,
    ProjectOverlapResult,
    RegionOverlapRequest,
    RegionOverlapResult,
)
from root_crud import get

settings = get_settings()


@async_override_settings(settings=settings, NUM_OVERLAP_TASK_RESULTS_TO_KEEP=5)
async def test_delete_old_overlap_tasks(mdl, app_request):
    previous_program = await mdl.Programs()
    program = await mdl.Programs()
    previous_project = await mdl.Projects(program_id=previous_program.id)
    project = await mdl.Projects(program_id=program.id)
    previous_field = await mdl.Fields(parent_project_id=previous_project.id)
    field = await mdl.Fields(parent_project_id=project.id)

    # Create 10 DetermineOverlapTask instances
    created_at = datetime.now() - timedelta(days=20)
    for index in range(1, 11):
        await mdl.DetermineOverlapTask(id=index, created_at=created_at)
        created_at += timedelta(days=1)
    for task_id in range(1, 11):
        await mdl.ProgramOverlapRequest(task_id=task_id, program_id=program.id, previous_program_id=previous_program.id)
        await mdl.ProgramOverlapResult(task_id=task_id, program_id=program.id, previous_program_id=previous_program.id)
        await mdl.ProjectOverlapRequest(task_id=task_id, project_id=project.id, previous_project_id=previous_project.id)
        await mdl.ProjectOverlapResult(task_id=task_id, project_id=project.id, previous_project_id=previous_project.id)
        await mdl.FieldOverlapResult(
            task_id=task_id, field_id=field.id, previous_field_id=previous_field.id, percentage_overlap=10
        )
        await mdl.RegionOverlapRequest(task_id=task_id, program_id=program.id, previous_program_id=previous_program.id)
        await mdl.RegionOverlapResult(task_id=task_id, program_id=program.id, previous_program_id=previous_program.id)

    await delete_old_overlap_tasks(app_request)

    # Only the 5 most recent instances should remain
    tasks = await get.get(request=app_request, orm_type=DetermineOverlapTask)
    assert {task.id for task in tasks} == set(range(6, 11))

    program_overlap_requests = await get.get(request=app_request, orm_type=ProgramOverlapRequest)
    assert {request.task_id for request in program_overlap_requests} == set(range(6, 11))

    program_overlap_results = await get.get(request=app_request, orm_type=ProgramOverlapResult)
    assert {result.task_id for result in program_overlap_results} == set(range(6, 11))

    project_overlap_requests = await get.get(request=app_request, orm_type=ProjectOverlapRequest)
    assert {request.task_id for request in project_overlap_requests} == set(range(6, 11))

    project_overlap_results = await get.get(request=app_request, orm_type=ProjectOverlapResult)
    assert {result.task_id for result in project_overlap_results} == set(range(6, 11))

    field_overlap_results = await get.get(request=app_request, orm_type=FieldOverlapResult)
    assert {result.task_id for result in field_overlap_results} == set(range(6, 11))

    region_overlap_requests = await get.get(request=app_request, orm_type=RegionOverlapRequest)
    assert {request.task_id for request in region_overlap_requests} == set(range(6, 11))

    region_overlap_results = await get.get(request=app_request, orm_type=RegionOverlapResult)
    assert {result.task_id for result in region_overlap_results} == set(range(6, 11))


@async_override_settings(settings=settings, BOUNDARIES_SERVICE_CACHE_AGE_DAYS=5)
async def test_delete_old_cached_results(mdl, app_request, faker):
    # Create 20 BoundariesServiceResult instances
    cutoff = datetime.now() - timedelta(days=5)
    for index in range(1, 21):
        if index % 3 == 0:
            last_used_at = datetime.now() - timedelta(days=random.randint(1, 4))
        elif index % 3 == 1:
            last_used_at = datetime.now() - timedelta(days=random.randint(10, 20))
        else:
            last_used_at = None
        await mdl.BoundariesServiceResult(id=index, last_used_at=last_used_at, params_sha256=faker.sha256())
    await delete_old_cached_results(app_request)
    # All instances should either have last_used_at set to None or a value less than 5 days old
    results = await get.get(request=app_request, orm_type=BoundariesServiceResult)
    assert all(result.last_used_at is None or result.last_used_at > cutoff for result in results)


@patch("http_requests.crons.db.delete_old_cached_results")
@patch("http_requests.crons.db.delete_old_overlap_tasks")
async def test_create_overlap_task(mock_delete_tasks, mock_delete_results, app_request):
    task = await create_overlap_task(app_request)
    mock_delete_tasks.assert_called()
    mock_delete_results.assert_called()
    result = await get.get(request=app_request, orm_type=DetermineOverlapTask)
    assert len(result) == 1
    assert task.id == result[0].id


async def test_get_or_create_cached_result(app_request, faker):
    params_sha256 = faker.sha256()
    results = await get.get(request=app_request, orm_type=BoundariesServiceResult, empty_return=True)
    assert len(results) == 0
    result1 = await get_or_create_cached_result(app_request, params_sha256)
    assert result1.params_sha256 == params_sha256
    result2 = await get_or_create_cached_result(app_request, params_sha256)
    assert result2.params_sha256 == params_sha256
    assert result2.id == result1.id


async def test_get_unfinished_overlap_request_count(mdl, app_request):
    num_program_overlaps, num_project_overlaps, num_region_overlaps = await get_unfinished_overlap_request_count(
        app_request, 1
    )
    assert num_program_overlaps == 0
    assert num_project_overlaps == 0
    assert num_region_overlaps == 0

    task1 = await mdl.DetermineOverlapTask()
    prog1 = await mdl.Programs()
    prog2 = await mdl.Programs()
    proj1 = await mdl.Projects(program_id=prog1.id)
    proj2 = await mdl.Projects(program_id=prog2.id)

    # Create 18 ProjectOverlapRequest instances with different statuses, 9 of which are not completed.
    for _ in range(3):
        for status in list(JobStatus) + [None]:
            await mdl.ProjectOverlapRequest(
                task_id=task1.id, project_id=proj1.id, previous_project_id=proj2.id, status=status
            )

    # Create 12 ProgramOverlapRequest instances with different statuses, 6 of which are not completed.
    for _ in range(2):
        for status in list(JobStatus) + [None]:
            await mdl.ProgramOverlapRequest(
                task_id=task1.id, program_id=prog1.id, previous_program_id=prog2.id, status=status
            )

    # Create 6 ProgramOverlapRequest instances with different statuses, 3 of which are not completed.
    for status in list(JobStatus) + [None]:
        await mdl.RegionOverlapRequest(
            task_id=task1.id, program_id=prog1.id, previous_program_id=prog2.id, core_region_id=1, status=status
        )

    num_program_overlaps, num_project_overlaps, num_region_overlaps = await get_unfinished_overlap_request_count(
        app_request, task1.id
    )
    assert num_program_overlaps == 6
    assert num_project_overlaps == 9
    assert num_region_overlaps == 3

    # Getting the count for another task should return zeros
    task2 = await mdl.DetermineOverlapTask()
    num_program_overlaps, num_project_overlaps, num_region_overlaps = await get_unfinished_overlap_request_count(
        app_request, task2.id
    )
    assert num_program_overlaps == 0
    assert num_project_overlaps == 0
    assert num_region_overlaps == 0


async def test_get_kml_files(mdl, app_request):
    """
    This tests that we can get KML file information by MD5
    """
    test_md5 = "test_md5"

    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    region = await mdl.CoreRegions(name="test_region")
    group = await mdl.Groups(name="test_farm")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100.0, md5=test_md5)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    await mdl.Fields(parent_project_id=project.id, md5=test_md5, fs_field_id=kml_group.id, area=100, farm_id=group.id)

    results = await get_kml_files(app_request, [test_md5])

    kml_file_data = results[0]
    assert kml_file_data.kml_id == kml_file.id


async def test_update_kml_files(mdl, app_request):
    """
    This tests that we update both the relevant KMLFiles and Fields given KMLFileUpdateData inputs
    """
    test_md5 = "before_md5"
    updated_md5 = "updated_md5"
    updated_area = 200.0

    program = await mdl.Programs(name="test_program")
    project = await mdl.Projects(program_id=program.id)
    region = await mdl.CoreRegions(name="test_region")
    group = await mdl.Groups(name="test_farm")
    kml_file = await mdl.KMLFiles(region_id=region.id, area=100.0, md5=test_md5)
    kml_group = await mdl.KMLGroups(name="test_field_name", kml_id=kml_file.id)
    await mdl.Fields(parent_project_id=project.id, md5=test_md5, fs_field_id=kml_group.id, area=100, farm_id=group.id)

    kml_file_update_data = KMLFileUpdateData(kml_id=kml_file.id, md5=updated_md5, area=updated_area)

    await update_kml_files(app_request, [kml_file_update_data])

    found_kml_files = await get.get(request=app_request, orm_type=KMLFiles)
    assert len(found_kml_files) == 1
    found_kml_file = found_kml_files[0]
    assert found_kml_file.realName == f"{updated_md5}.kml"
    assert found_kml_file.md5 == updated_md5
    assert found_kml_file.area == updated_area

    found_fields = await get.get(request=app_request, orm_type=Fields)
    assert len(found_fields) == 1
    found_field = found_fields[0]
    assert found_field.md5 == updated_md5
    assert found_field.area == updated_area
