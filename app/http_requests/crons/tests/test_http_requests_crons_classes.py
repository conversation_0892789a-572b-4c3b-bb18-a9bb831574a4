import random
from random import sample

from sqlalchemy import select

from helper.helper import run_query
from http_requests.callbacks.utils import sha256_param
from http_requests.crons.classes import (
    ProcessFieldOverlapResults,
    ProcessProgramOverlapResult,
    ProcessProjectOverlapResult,
    ProcessRegionOverlapResult,
)
from http_requests.crons.enums import BoundariesServiceAPICall
from http_requests.crons.model import (
    FieldOverlapResult,
    ProgramOverlapResult,
    ProjectOverlapResult,
    RegionOverlapResult,
)
from root_crud import get


async def test_process_program_overlap_result(mdl, app_request, db_session_maker, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    cached_result = ProcessProgramOverlapResult(program_id=prog.id, previous_program_id=prev_prog.id)
    md5s = [faker.md5() for _ in range(20)]
    prev_md5s = [faker.md5() for _ in range(15)]

    # There's no cached result in the DB, so processed should be false...
    shasum, processed = await cached_result.process(
        request=app_request,
        task_id=1,
        md5s=sample(md5s, len(md5s)),
        previous_md5s=sample(prev_md5s, len(prev_md5s)),
        use_cache=True,
    )
    expected_sum = sha256_param(
        BoundariesServiceAPICall.union_areas, sample(md5s, len(md5s)), sample(prev_md5s, len(prev_md5s))
    )
    assert shasum == expected_sum
    assert processed is False
    results = await get.get(request=app_request, orm_type=ProgramOverlapResult, empty_return=True)
    # ...and no ProgramOverlapResult rows will be in the DB
    assert len(results) == 0

    # Create a cached result in the DB
    await mdl.BoundariesServiceResult(
        params_sha256=expected_sum,
        result={
            "list_one_union_area_m2": 100_000,
            "list_two_union_area_m2": 150_000,
            "area_intersection_union_m2_both": 50_000,
        },
    )
    shasum, processed = await cached_result.process(
        request=app_request,
        task_id=task.id,
        md5s=sample(md5s, len(md5s)),
        previous_md5s=sample(prev_md5s, len(prev_md5s)),
        use_cache=True,
    )
    assert shasum == expected_sum
    assert processed is True
    async with db_session_maker() as s:
        query = select(ProgramOverlapResult).where(
            ProgramOverlapResult.program_id == prog.id,
            ProgramOverlapResult.previous_program_id == prev_prog.id,
            ProgramOverlapResult.program_area_ha == 10,
            ProgramOverlapResult.previous_program_area_ha == 15,
            ProgramOverlapResult.area_intersection_ha == 5,
            ProgramOverlapResult.task_id == task.id,
        )
        result = (await run_query(s, query)).scalars().all()
        # We should now have a ProgramOverlapResult row in the DB as a result of calling process()
        assert len(result) == 1
        await s.delete(result[0])
        await s.commit()

    # Call process with use_cache=False, which should return the same shasum but processed should be False, and no
    # results should be created in the DB
    shasum, processed = await cached_result.process(
        request=app_request,
        task_id=task.id,
        md5s=sample(md5s, len(md5s)),
        previous_md5s=sample(prev_md5s, len(prev_md5s)),
        use_cache=False,
    )
    assert shasum == expected_sum
    assert processed is False
    results = await get.get(request=app_request, orm_type=ProgramOverlapResult, empty_return=True)
    assert len(results) == 0


async def test_process_project_overlap_result(mdl, app_request, db_session_maker, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    # Previous project should be in a different program, but for this test it doesn't matter, so easier just to make it
    # part of the same program
    prev_proj = await mdl.Projects(program_id=prog.id)
    md5s = [faker.md5() for _ in range(5)]
    prev_md5s = [faker.md5() for _ in range(4)]
    expected_sum = sha256_param(BoundariesServiceAPICall.union_areas, md5s, prev_md5s)

    cached_result = ProcessProjectOverlapResult(proj.id, prev_proj.id)
    await mdl.BoundariesServiceResult(
        params_sha256=expected_sum,
        result={
            "list_one_union_area_m2": 20_000,
            "list_two_union_area_m2": 10_000,
            "area_intersection_union_m2_both": 30_000,
        },
    )
    shasum, processed = await cached_result.process(
        request=app_request, task_id=task.id, md5s=md5s, previous_md5s=prev_md5s, use_cache=True
    )
    assert shasum == expected_sum
    assert processed is True
    async with db_session_maker() as s:
        query = select(ProjectOverlapResult).where(
            ProjectOverlapResult.project_id == proj.id,
            ProjectOverlapResult.previous_project_id == prev_proj.id,
            ProjectOverlapResult.project_area_ha == 2,
            ProjectOverlapResult.previous_project_area_ha == 1,
            ProjectOverlapResult.area_intersection_ha == 3,
        )
        result = (await run_query(s, query)).scalars().all()
        # We should now have a ProjectOverlapResult row in the DB as a result of calling process()
        assert len(result) == 1


async def test_process_field_overlap_results(mdl, app_request, db_session_maker, faker):
    task = await mdl.DetermineOverlapTask()

    # Create programs with just 2 fields each, for simplicity. One of those fields in each program shares the same
    # md5, so we can test that a 100% overlap result is created correctly.
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prev_prog.id)
    m1, m2, m3 = (faker.md5() for _ in range(3))
    md5s = [m1, m2]
    prev_md5s = [m1, m3]
    area = random.randint(10_000, 50_000) / 10_000
    field1 = await mdl.Fields(parent_project_id=proj.id, md5=m1, area=area)
    field2 = await mdl.Fields(parent_project_id=proj.id, md5=m2)
    prev_field1 = await mdl.Fields(parent_project_id=prev_proj.id, md5=m1, area=area)
    prev_field2 = await mdl.Fields(parent_project_id=prev_proj.id, md5=m3)

    expected_sum = sha256_param(BoundariesServiceAPICall.feature_intersections, md5s, prev_md5s)
    cached_result = ProcessFieldOverlapResults(prog.id, prev_prog.id)
    await mdl.BoundariesServiceResult(
        params_sha256=expected_sum,
        result={
            "feature_intersections": {
                m2: [
                    {
                        "intersecting_id": m3,
                        "percent_intersection_second": 30,
                        "percent_intersection_first": 60,
                        "area_intersection_m2": 40_000,
                    }
                ]
            }
        },
    )
    shasum, processed = await cached_result.process(
        request=app_request, task_id=task.id, md5s=md5s, previous_md5s=prev_md5s, use_cache=True
    )
    assert shasum == expected_sum
    assert processed is True

    # Check that the FieldOverlapResult rows were created correctly
    async with db_session_maker() as s:
        query = select(FieldOverlapResult).where(
            FieldOverlapResult.task_id == task.id,
            FieldOverlapResult.field_id == field2.id,
            FieldOverlapResult.field_md5 == m2,
            FieldOverlapResult.previous_field_id == prev_field2.id,
            FieldOverlapResult.previous_field_md5 == m3,
            FieldOverlapResult.percentage_overlap == 30,
            FieldOverlapResult.area_intersection_ha == 4,
        )
        result = (await run_query(s, query)).scalars().all()
        # We should now have a ProjectOverlapResult row in the DB as a result of calling process()
        assert len(result) == 1
        # 100% overlap result
        query = select(FieldOverlapResult).where(
            FieldOverlapResult.task_id == task.id,
            FieldOverlapResult.field_id == field1.id,
            FieldOverlapResult.field_md5 == m1,
            FieldOverlapResult.previous_field_id == prev_field1.id,
            FieldOverlapResult.previous_field_md5 == m1,
            FieldOverlapResult.percentage_overlap == 100,
            FieldOverlapResult.area_intersection_ha == area,
        )
        result = (await run_query(s, query)).scalars().all()
        # We should now have a ProjectOverlapResult row in the DB as a result of calling process()
        assert len(result) == 1


async def test_process_region_overlap_result(mdl, app_request, db_session_maker, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    md5s = [faker.md5() for _ in range(10)]
    prev_md5s = [faker.md5() for _ in range(10)]
    expected_sum = sha256_param(BoundariesServiceAPICall.union_areas, md5s, prev_md5s)

    cached_result = ProcessRegionOverlapResult(prog.id, prev_prog.id, 1)
    await mdl.BoundariesServiceResult(
        params_sha256=expected_sum,
        result={
            "list_one_union_area_m2": 100_000,
            "list_two_union_area_m2": 150_000,
            "area_intersection_union_m2_both": 50_000,
        },
    )
    shasum, processed = await cached_result.process(
        request=app_request, task_id=task.id, md5s=md5s, previous_md5s=prev_md5s, use_cache=True
    )
    assert shasum == expected_sum
    assert processed is True

    async with db_session_maker() as s:
        query = select(RegionOverlapResult).where(
            RegionOverlapResult.task_id == task.id,
            RegionOverlapResult.program_id == prog.id,
            RegionOverlapResult.previous_program_id == prev_prog.id,
            RegionOverlapResult.core_region_id == 1,
            RegionOverlapResult.program_region_area_ha == 10,
            RegionOverlapResult.previous_program_region_area_ha == 15,
            RegionOverlapResult.area_intersection_ha == 5,
        )
        result = (await run_query(s, query)).scalars().all()
        # We should now have a ProgramOverlapResult row in the DB as a result of calling process()
        assert len(result) == 1
