from unittest.mock import patch

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy import select

from helper.helper import run_query
from http_requests.crons.model import DetermineOverlapTask
from http_requests.crons.tasks import (
    _process_task_exception,
    generate_overlaps_for_all_programs_task,
    generate_program_overlaps_task,
    logger,
)


@patch.object(logger, "error")
def test_process_task_exception(mock_log_error):
    exc = HTTPException(status_code=404, detail="some error")
    overlap_task = None
    error_msg = "generate_program_overlaps_task FAILED"
    result = _process_task_exception(exc, overlap_task, error_msg)
    assert result is None

    task = DetermineOverlapTask()
    result = _process_task_exception(exc, task, error_msg)
    assert "status_code=404" in result.error
    assert "some error" in result.error
    assert "HTTPException" in result.error
    assert "generate_program_overlaps_task FAILED" in mock_log_error.call_args.args[0]


@patch("http_requests.crons.tasks.generate_overlaps_for_programs")
async def test_generate_program_overlaps_task(mock_generate_overlaps, app_request, db_session_maker):
    generate_program_overlaps_task(
        program_id=1, previous_program_id=2, use_cache=True, fs_user_id=3, request=app_request
    )
    async with db_session_maker() as s:
        task = (await run_query(s, select(DetermineOverlapTask))).scalars().one()
    mock_generate_overlaps.assert_called_with(
        request=app_request, overlap_task_id=task.id, program_id=1, previous_program_id=2, use_cache=True
    )
    assert task.finished_at
    assert not task.error


@patch("http_requests.crons.tasks.generate_overlaps_for_programs", side_effect=Exception)
async def test_generate_program_overlaps_task_exception(mock_generate_overlaps, app_request, db_session_maker):
    generate_program_overlaps_task(
        program_id=1, previous_program_id=2, use_cache=False, fs_user_id=3, request=app_request
    )
    async with db_session_maker() as s:
        task = (await run_query(s, select(DetermineOverlapTask))).scalars().one()
    mock_generate_overlaps.assert_called_with(
        request=app_request, overlap_task_id=task.id, program_id=1, previous_program_id=2, use_cache=False
    )
    assert task.finished_at
    assert task.error


@patch("http_requests.crons.tasks.generate_overlaps_for_programs")
async def test_generate_overlaps_for_all_programs_task(mock_generate_overlaps, mdl, app_request, db_session_maker):
    for i in range(1, 10):
        # Give even numbered programs a previous program
        await mdl.Programs(id=i, previous_program_id=None if (i % 2) else (i - 1))
    generate_overlaps_for_all_programs_task(use_cache=True, fs_user_id=3, request=app_request)
    assert mock_generate_overlaps.call_count == 4
    async with db_session_maker() as s:
        task = (await run_query(s, select(DetermineOverlapTask))).scalars().one()
    for i in range(2, 10, 2):
        mock_generate_overlaps.assert_any_call(
            request=app_request, overlap_task_id=task.id, program_id=i, previous_program_id=i - 1, use_cache=True
        )
    assert task.finished_at
    assert not task.error


@patch("http_requests.crons.tasks.generate_overlaps_for_programs", side_effect=Exception)
async def test_generate_overlaps_for_all_programs_task_error(
    mock_generate_overlaps, mdl, app_request, db_session_maker
):
    prev_prog = await mdl.Programs()
    prog = await mdl.Programs(previous_program_id=prev_prog.id)
    generate_overlaps_for_all_programs_task(use_cache=False, fs_user_id=3, request=app_request)
    assert mock_generate_overlaps.call_count == 1
    async with db_session_maker() as s:
        task = (await run_query(s, select(DetermineOverlapTask))).scalars().one()
    mock_generate_overlaps.assert_called_with(
        request=app_request,
        overlap_task_id=task.id,
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        use_cache=False,
    )
    assert task.finished_at
    assert task.error
