import random
import uuid
from unittest.mock import AsyncMock, patch

import httpx
import pytest
from fastapi import HTTPException
from override_settings import async_override_settings
from sqlalchemy import select

from boundaries_service.enums import JobStatus
from boundaries_service.exceptions import construct_missing_md5_error
from boundaries_service.schema import BoundariesServiceJob, UnionArea
from config import get_settings
from fields.enums import FieldStatus
from helper.helper import assert_dict_contains_subset
from http_requests.callbacks import paths as callback_paths
from http_requests.callbacks.utils import sha256_param
from http_requests.crons.enums import BoundariesServiceAPICall
from http_requests.crons.methods import (
    _call_boundaries_service_for_programs,
    _call_feature_intersections,
    _call_union_areas,
    generate_field_overlaps_for_programs,
    generate_overlaps_for_programs,
    generate_program_overlaps_for_programs,
    generate_project_overlaps_for_programs,
    generate_region_overlaps_for_programs,
    lock_api_request_for_update,
    wait_for_task_completion,
)
from http_requests.crons.model import (
    BoundariesServiceResult,
    DetermineOverlapTask,
    FieldOverlapResult,
    ProgramOverlapRequest,
    ProgramOverlapResult,
    ProjectOverlapRequest,
    ProjectOverlapResult,
    RegionOverlapRequest,
    RegionOverlapResult,
)
from projects.enums import ContractStatus, ProjectStatus
from root_crud import get, update

settings = get_settings()


async def test_lock_api_request_for_update(mdl, app_request):
    prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prog.id)
    task = await mdl.DetermineOverlapTask()
    async_request = await mdl.ProjectOverlapRequest(
        task_id=task.id, project_id=proj.id, previous_project_id=prev_proj.id, status=JobStatus.accepted
    )
    # I don't know how to test the SELECT FOR UPDATE aspect of lock_api_request_for_update, so I'm just going to test
    # that it returns the correct object and that it automatically saves any changes to the object.
    async with lock_api_request_for_update(
        app_request, async_request.id, orm_type=ProjectOverlapRequest
    ) as locked_request:
        assert locked_request.id == async_request.id
        assert locked_request.task_id == task.id
        assert locked_request.project_id == proj.id
        assert locked_request.previous_project_id == prev_proj.id
        # The following should be saved automatically
        locked_request.status = JobStatus.failed
    updated_request = (
        await get.get(
            request=app_request,
            orm_type=ProjectOverlapRequest,
            id_field=ProjectOverlapRequest.id,
            ids=[async_request.id],
        )
    )[0]
    # Check that change was saved
    assert updated_request.status == JobStatus.failed


async def test_call_union_areas(mdl, app_request, faker, db_session_maker):
    prev_prog = await mdl.Programs()
    prog = await mdl.Programs()
    proj = await mdl.Projects(program_id=prog.id)
    prev_proj = await mdl.Projects(program_id=prev_prog.id)
    task = await mdl.DetermineOverlapTask()
    md5s = [faker.md5() for _ in range(5)]
    previous_md5s = [faker.md5() for _ in range(5)]
    params_sha256 = sha256_param(BoundariesServiceAPICall.union_areas, md5s, previous_md5s)
    callback_url = faker.url()

    # Check that the function creates a BoundariesServiceResult and a ProjectOverlapRequest and correctly generates
    # params_sha256
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    async_request = await mdl.ProjectOverlapRequest(
        project_id=proj.id, previous_project_id=prev_proj.id, task_id=task.id, status=None
    )
    with patch("http_requests.crons.methods.union_areas", return_value=job_result) as mock_union_areas:
        await _call_union_areas(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProjectOverlapRequest,
        )
        mock_union_areas.assert_called_once_with(md5s, previous_md5s, callback_url)
        async with db_session_maker() as s:
            # Get cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            cached_result = (await s.execute(query)).scalars().one()
            first_cache_result_id = cached_result.id
            # Get updated async request
            query = select(ProjectOverlapRequest).where(ProjectOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.status == JobStatus.accepted
            assert updated_request.job_id == job_result.job_id
            assert updated_request.cache_id == cached_result.id

    # Another request with the same params should return the same cached result. Also try passing params_sha256
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    async_request = await mdl.ProjectOverlapRequest(
        project_id=proj.id, previous_project_id=prev_proj.id, task_id=task.id, status=None
    )
    with patch("http_requests.crons.methods.union_areas", return_value=job_result):
        await _call_union_areas(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProjectOverlapRequest,
            params_sha256=params_sha256,
        )
        async with db_session_maker() as s:
            # Get cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            cached_result = (await s.execute(query)).scalars().one()
            assert cached_result.id == first_cache_result_id
            # Get updated async request
            query = select(ProjectOverlapRequest).where(ProjectOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.status == JobStatus.accepted
            assert updated_request.job_id == job_result.job_id
            assert updated_request.cache_id == cached_result.id

    # Check that the correct values are saved if union_areas raises an exception
    error_message = faker.sentence()
    async_request = await mdl.ProjectOverlapRequest(
        project_id=proj.id, previous_project_id=prev_proj.id, task_id=task.id, status=None
    )
    with patch(
        "http_requests.crons.methods.union_areas", side_effect=HTTPException(status_code=400, detail=error_message)
    ):
        await _call_union_areas(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProjectOverlapRequest,
        )
        async with db_session_maker() as s:
            # Get updated async request
            query = select(ProjectOverlapRequest).where(ProjectOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.result == {"status": 400, "detail": error_message}

    # check for correct handling of retries
    error_message = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['c4f49dc312e28251ac5b9f99bbf2cad5']. Missing list_two: []."
    async_request = await mdl.ProjectOverlapRequest(
        project_id=proj.id, previous_project_id=prev_proj.id, task_id=task.id, status=None
    )
    side_effect_exception = construct_missing_md5_error(error_message, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    with patch("http_requests.crons.methods.union_areas", side_effect=side_effect_exception), patch(
        "boundaries_service.client._get_searched_boundaries",
        return_value=httpx.Response(status_code=200, json={"features": []}),
    ):
        await _call_union_areas(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProjectOverlapRequest,
        )
        async with db_session_maker() as s:
            # Get updated async request
            query = select(ProjectOverlapRequest).where(ProjectOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.result == {
                "status": 500,
                "detail": {
                    "message": "endpoint processes/union-areas/execution returned 400",
                    "response_text": error_message,
                },
            }


@patch("http_requests.crons.methods.create_fully_overlapped_field_results")
async def test_call_feature_intersections(mock_overlapped_results, mdl, app_request, faker, db_session_maker):
    prog = await mdl.Programs()
    prev_prog = await mdl.Programs()
    task = await mdl.DetermineOverlapTask()
    md5s = [faker.md5() for _ in range(30)]
    previous_md5s = [faker.md5() for _ in range(20)]
    params_sha256 = sha256_param(BoundariesServiceAPICall.feature_intersections, md5s, previous_md5s)
    callback_url = faker.url()
    async_request = await mdl.ProgramOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, status=None
    )

    # Check exception is raised if params_sha256 is None
    with pytest.raises(ValueError):
        await _call_feature_intersections(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProgramOverlapRequest,
        )

    # Check that the function creates a BoundariesServiceResult and a ProgramOverlapRequest wtih the correct values.
    # Also check that create_fully_overlapped_field_results and feature_intersections are called with the correct
    # parameters.
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    with patch(
        "http_requests.crons.methods.feature_intersections", return_value=job_result
    ) as mock_feature_intersections:
        await _call_feature_intersections(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProgramOverlapRequest,
            params_sha256=params_sha256,
        )
        mock_feature_intersections.assert_called_once_with(md5s, previous_md5s, callback_url)
        mock_overlapped_results.assert_called_once_with(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            program_id=prog.id,
            previous_program_id=prev_prog.id,
            task_id=task.id,
        )
        async with db_session_maker() as s:
            # Get cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            cached_result = (await s.execute(query)).scalars().one()
            first_cache_result_id = cached_result.id
            # Get updated async request
            query = select(ProgramOverlapRequest).where(ProgramOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.status == JobStatus.accepted
            assert updated_request.job_id == job_result.job_id
            assert updated_request.cache_id == cached_result.id

    # Another request with the same params should return the same cached result.
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    async_request = await mdl.ProgramOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, status=None
    )
    with patch("http_requests.crons.methods.feature_intersections", return_value=job_result):
        await _call_feature_intersections(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProgramOverlapRequest,
            params_sha256=params_sha256,
        )
        async with db_session_maker() as s:
            # Get cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            cached_result = (await s.execute(query)).scalars().one()
            assert first_cache_result_id == cached_result.id
            # Get updated async request
            query = select(ProgramOverlapRequest).where(ProgramOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.status == JobStatus.accepted
            assert updated_request.job_id == job_result.job_id
            assert updated_request.cache_id == cached_result.id

    # Check that the correct values are saved if feature_intersections raises an exception
    async_request = await mdl.ProgramOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, status=None
    )
    error_message = faker.sentence()
    with patch(
        "http_requests.crons.methods.feature_intersections",
        side_effect=HTTPException(status_code=500, detail=error_message),
    ):
        await _call_feature_intersections(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProgramOverlapRequest,
            params_sha256=params_sha256,
        )
        async with db_session_maker() as s:
            # Get updated async request
            query = select(ProgramOverlapRequest).where(ProgramOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.result == {"status": 500, "detail": error_message}

    # check for correct handling of retries
    error_message = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['c4f49dc312e28251ac5b9f99bbf2cad5']. Missing list_two: []."
    async_request = await mdl.ProgramOverlapRequest(
        program_id=prog.id, previous_program_id=prev_prog.id, task_id=task.id, status=None
    )
    side_effect_exception = construct_missing_md5_error(
        error_message, settings.BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH
    )
    with patch("http_requests.crons.methods.feature_intersections", side_effect=side_effect_exception), patch(
        "boundaries_service.client._get_searched_boundaries",
        return_value=httpx.Response(status_code=200, json={"features": []}),
    ):
        await _call_feature_intersections(
            request=app_request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            url=callback_url,
            api_request_id=async_request.id,
            orm_type=ProgramOverlapRequest,
            params_sha256=params_sha256,
        )
        async with db_session_maker() as s:
            # Get updated async request
            query = select(ProgramOverlapRequest).where(ProgramOverlapRequest.id == async_request.id)
            updated_request = (await s.execute(query)).scalars().one()
            assert updated_request.result == {
                "status": 500,
                "detail": {
                    "message": "endpoint processes/feature-intersections/execution returned 400",
                    "response_text": error_message,
                },
            }


async def test_call_boundaries_service_for_programs(mdl, app_request, db_session_maker, faker):
    task = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    empty_prog = await mdl.Programs()
    proj = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    fields = [
        await mdl.Fields(parent_project_id=proj.id, md5=faker.md5(), status=FieldStatus.enrolled) for _ in range(5)
    ]
    endpoint_suffix = "/result"
    api_call = AsyncMock()
    process_cached_result = AsyncMock()

    # Check that request is dismissed if one or more of the programs has no enrolled fields
    await _call_boundaries_service_for_programs(
        request=app_request,
        task_id=task.id,
        program_id=prog.id,
        previous_program_id=empty_prog.id,
        endpoint_part=endpoint_suffix,
        api_call=api_call,
        process_cached_result=process_cached_result,
        use_cache=True,
    )
    api_call.assert_not_called()
    process_cached_result.assert_not_called()
    async with db_session_maker() as s:
        query = select(ProgramOverlapRequest).where(
            ProgramOverlapRequest.program_id == prog.id, ProgramOverlapRequest.previous_program_id == empty_prog.id
        )
        request = (await s.execute(query)).scalars().one()
        assert request.status == JobStatus.dismissed
        assert request.result == {
            "message": (
                "One or both of the following programs has no md5s to measure for overlap "
                f"program_id={prog.id} previous_program_id={empty_prog.id}"
            )
        }

    # Same again with programs switched around
    await _call_boundaries_service_for_programs(
        request=app_request,
        task_id=task.id,
        program_id=empty_prog.id,
        previous_program_id=prog.id,
        endpoint_part=endpoint_suffix,
        api_call=api_call,
        process_cached_result=process_cached_result,
        use_cache=True,
    )
    api_call.assert_not_called()
    process_cached_result.assert_not_called()
    async with db_session_maker() as s:
        query = select(ProgramOverlapRequest).where(
            ProgramOverlapRequest.program_id == empty_prog.id, ProgramOverlapRequest.previous_program_id == prog.id
        )
        request = (await s.execute(query)).scalars().one()
        assert request.status == JobStatus.dismissed
        assert request.result == {
            "message": (
                "One or both of the following programs has no md5s to measure for overlap "
                f"program_id={empty_prog.id} previous_program_id={prog.id}"
            )
        }

    # The normal case - programs with fields
    prev_prog = await mdl.Programs()
    prev_proj = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_fields = [
        await mdl.Fields(parent_project_id=prev_proj.id, md5=faker.md5(), status=FieldStatus.enrolled) for _ in range(5)
    ]

    # Check that if cache exists, request object isn't created and the cached result is processed
    process_cached_result.process = AsyncMock(return_value=(faker.sha256(), True))
    await _call_boundaries_service_for_programs(
        request=app_request,
        task_id=task.id,
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        endpoint_part=endpoint_suffix,
        api_call=api_call,
        process_cached_result=process_cached_result,
        use_cache=True,
    )
    md5s = [field.md5 for field in fields]
    prev_md5s = [field.md5 for field in prev_fields]
    assert_dict_contains_subset(
        process_cached_result.process.call_args.kwargs, {"request": app_request, "task_id": task.id, "use_cache": True}
    )
    assert set(process_cached_result.process.call_args.kwargs["md5s"]) == set(md5s)
    assert set(process_cached_result.process.call_args.kwargs["previous_md5s"]) == set(prev_md5s)
    async with db_session_maker() as s:
        query = select(ProgramOverlapRequest).where(
            ProgramOverlapRequest.program_id == prog.id, ProgramOverlapRequest.previous_program_id == prev_prog.id
        )
        requests = (await s.execute(query)).scalars().all()
        assert not requests

    # Check that if cache doesn't exist, request object is created and the API is called
    params_sha256 = faker.sha256()
    process_cached_result.process = AsyncMock(return_value=(params_sha256, False))
    await _call_boundaries_service_for_programs(
        request=app_request,
        task_id=task.id,
        program_id=prog.id,
        previous_program_id=prev_prog.id,
        endpoint_part=endpoint_suffix,
        api_call=api_call,
        process_cached_result=process_cached_result,
        use_cache=True,
    )
    async with db_session_maker() as s:
        query = select(ProgramOverlapRequest).where(
            ProgramOverlapRequest.program_id == prog.id, ProgramOverlapRequest.previous_program_id == prev_prog.id
        )
        # The following call should not raise an exception if we have created a request object
        request = (await s.execute(query)).scalars().one()
        url = f"{settings.MRV_INTERNAL_URL}{callback_paths.base}{endpoint_suffix}"
        assert_dict_contains_subset(
            api_call.call_args.kwargs,
            {"request": app_request, "url": url, "api_request_id": request.id, "orm_type": ProgramOverlapRequest},
        )


async def test_generate_program_overlaps_for_programs(mdl, app_request, db_session_maker, faker):
    task1 = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    proj = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    fields = [
        await mdl.Fields(parent_project_id=proj.id, md5=faker.md5(), status=FieldStatus.enrolled) for _ in range(5)
    ]
    prev_prog = await mdl.Programs()
    prev_proj = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_fields = [
        await mdl.Fields(parent_project_id=prev_proj.id, md5=faker.md5(), status=FieldStatus.enrolled) for _ in range(5)
    ]
    params_sha256 = sha256_param(
        BoundariesServiceAPICall.union_areas, [field.md5 for field in fields], [field.md5 for field in prev_fields]
    )

    # First run: no cache exists
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    with patch("http_requests.crons.methods.union_areas", return_value=job_result) as mock_union_areas:
        await generate_program_overlaps_for_programs(
            request=app_request, task_id=task1.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
        )
        mock_union_areas.assert_called()
        async with db_session_maker() as s:
            # Check that empty result should have been created
            query = select(BoundariesServiceResult).where(
                BoundariesServiceResult.params_sha256 == params_sha256, BoundariesServiceResult.result.is_(None)
            )
            cached_result = (await s.execute(query)).scalars().one()
            # Check that request has been created
            query = select(ProgramOverlapRequest).where(
                ProgramOverlapRequest.program_id == prog.id, ProgramOverlapRequest.previous_program_id == prev_prog.id
            )
            request = (await s.execute(query)).scalars().one()
            assert request.status == JobStatus.accepted
            assert request.job_id == job_result.job_id
            assert request.cache_id == cached_result.id
            # No results should be created yet - boundaries service will call us back for these (not mocked here)
            query = select(ProgramOverlapResult)
            results = (await s.execute(query)).all()
            assert len(results) == 0

    # Second run: cache now exists, so use cache to create result (cache will be created when boundaries service calls
    # us back)
    cached_result.result = UnionArea(
        list_one_union_area_m2=40_000, list_two_union_area_m2=60_000, area_intersection_union_m2_both=10_000
    ).dict()
    await update.update(request=app_request, instances=[cached_result], type=BoundariesServiceResult)
    task2 = await mdl.DetermineOverlapTask()
    await generate_program_overlaps_for_programs(
        request=app_request, task_id=task2.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    async with db_session_maker() as s:
        query = select(ProgramOverlapResult).where(
            ProgramOverlapResult.program_id == prog.id,
            ProgramOverlapResult.previous_program_id == prev_prog.id,
            ProgramOverlapResult.task_id == task2.id,
            ProgramOverlapResult.program_area_ha == 4,
            ProgramOverlapResult.previous_program_area_ha == 6,
            ProgramOverlapResult.area_intersection_ha == 1,
        )
        # We should have one result created from the cached object
        (await s.execute(query)).scalars().one()
        query = select(BoundariesServiceResult).where(
            BoundariesServiceResult.params_sha256 == params_sha256, BoundariesServiceResult.last_used_at.isnot(None)
        )
        # Check that the cached object was used (last_used_at is not None)
        (await s.execute(query)).scalars().one()
        # Check that no request object was created, because we used cache
        query = select(ProgramOverlapRequest).where(ProgramOverlapRequest.task_id == task2.id)
        assert len((await s.execute(query)).all()) == 0

    # Third run: cache exists, but we don't use it, so it's like the first run - boundaries service is called
    task3 = await mdl.DetermineOverlapTask()
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    with patch("http_requests.crons.methods.union_areas", return_value=job_result) as mock_union_areas:
        await generate_program_overlaps_for_programs(
            request=app_request, task_id=task3.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
        )
        mock_union_areas.assert_called()
        async with db_session_maker() as s:
            # No result should have been created
            query = select(ProgramOverlapResult).where(ProgramOverlapResult.task_id == task3.id)
            assert len((await s.execute(query)).all()) == 0
            # We should still have only one cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            (await s.execute(query)).scalars().one()
            # Check that a request object was created
            query = select(ProgramOverlapRequest).where(
                ProgramOverlapRequest.task_id == task3.id,
                ProgramOverlapRequest.program_id == prog.id,
                ProgramOverlapRequest.previous_program_id == prev_prog.id,
            )
            (await s.execute(query)).scalars().one()


async def test_generate_field_overlaps_for_programs(mdl, app_request, db_session_maker, faker):
    m0, m1 = faker.md5(), faker.md5()
    # Make the first 2 md5s are the same in both programs, so we test 100% overlap is dealt with correctly
    md5s = [m0, m1] + [faker.md5() for _ in range(3)]
    prev_md5s = [m0, m1] + [faker.md5() for _ in range(3)]
    params_sha256 = sha256_param(BoundariesServiceAPICall.feature_intersections, md5s, prev_md5s)
    task1 = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    proj = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    fields = [
        await mdl.Fields(parent_project_id=proj.id, md5=md5, status=FieldStatus.enrolled, area=i + 1)
        for i, md5 in enumerate(md5s)
    ]
    prev_prog = await mdl.Programs()
    prev_proj = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_fields = [
        await mdl.Fields(parent_project_id=prev_proj.id, md5=md5, status=FieldStatus.enrolled, area=i + 1)
        for i, md5 in enumerate(prev_md5s)
    ]

    percent_intersections_by_field_id = {field.id: random.randint(1, 99) for field in fields + prev_fields}
    area_intersections_by_field_id = {field.id: random.randint(5, 200) * 10_000 for field in fields[2:]}

    # First run: no cache exists
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    with patch(
        "http_requests.crons.methods.feature_intersections", return_value=job_result
    ) as mock_feature_intersections:
        await generate_field_overlaps_for_programs(
            request=app_request, task_id=task1.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
        )
        mock_feature_intersections.assert_called()
        async with db_session_maker() as s:
            # Check that empty cache result should be created
            query = select(BoundariesServiceResult).where(
                BoundariesServiceResult.params_sha256 == params_sha256, BoundariesServiceResult.result.is_(None)
            )
            cached_result = (await s.execute(query)).scalars().one()
            # Check that request has been created
            query = select(ProgramOverlapRequest).where(
                ProgramOverlapRequest.program_id == prog.id, ProgramOverlapRequest.previous_program_id == prev_prog.id
            )
            request = (await s.execute(query)).scalars().one()
            assert request.status == JobStatus.accepted
            assert request.job_id == job_result.job_id
            assert request.cache_id == cached_result.id
            # Check that 2 field overlap results have been created - these are for the 100% overlaps
            query = select(FieldOverlapResult)
            assert len((await s.execute(query)).all()) == 2
            for index, (field, prev_field) in enumerate(list(zip(fields, prev_fields))[:2]):
                query = select(FieldOverlapResult).where(
                    FieldOverlapResult.field_id == field.id,
                    FieldOverlapResult.previous_field_id == prev_field.id,
                    FieldOverlapResult.field_md5 == field.md5,
                    FieldOverlapResult.previous_field_md5 == prev_field.md5,
                    FieldOverlapResult.percentage_overlap == 100,
                    FieldOverlapResult.area_intersection_ha == index + 1,
                    FieldOverlapResult.task_id == task1.id,
                )
                (await s.execute(query)).scalars().one()

    # Second run: create cached result (simulates boundaries service having called us back with result)
    feature_intersections = {
        "feature_intersections": {
            field.md5: [
                {
                    "intersecting_id": prev_field.md5,
                    "percent_intersection_second": percent_intersections_by_field_id[prev_field.id],
                    "percent_intersection_first": percent_intersections_by_field_id[field.id],
                    "area_intersection_m2": area_intersections_by_field_id[field.id],
                }
            ]
            for field, prev_field in list(zip(fields, prev_fields))[2:]
        }
    }
    cached_result.result = feature_intersections
    await update.update(request=app_request, instances=[cached_result], type=BoundariesServiceResult)
    task2 = await mdl.DetermineOverlapTask()
    await generate_field_overlaps_for_programs(
        request=app_request, task_id=task2.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    async with db_session_maker() as s:
        query = select(FieldOverlapResult).where(FieldOverlapResult.task_id == task2.id)
        assert len((await s.execute(query)).all()) == len(fields)
        # Check that 2 field overlap results have been created for the 100% overlaps
        for index, (field, prev_field) in enumerate(list(zip(fields, prev_fields))[:2]):
            query = select(FieldOverlapResult).where(
                FieldOverlapResult.field_id == field.id,
                FieldOverlapResult.previous_field_id == prev_field.id,
                FieldOverlapResult.field_md5 == field.md5,
                FieldOverlapResult.previous_field_md5 == prev_field.md5,
                FieldOverlapResult.percentage_overlap == 100,
                FieldOverlapResult.area_intersection_ha == index + 1,
                FieldOverlapResult.task_id == task2.id,
            )
            (await s.execute(query)).scalars().one()

        # Check that 3 further field overlap results have been created with the correct values:
        for field, prev_field in list(zip(fields, prev_fields))[2:]:
            query = select(FieldOverlapResult).where(
                FieldOverlapResult.field_id == field.id,
                FieldOverlapResult.previous_field_id == prev_field.id,
                FieldOverlapResult.field_md5 == field.md5,
                FieldOverlapResult.previous_field_md5 == prev_field.md5,
                FieldOverlapResult.percentage_overlap == percent_intersections_by_field_id[prev_field.id],
                FieldOverlapResult.area_intersection_ha == area_intersections_by_field_id[field.id] / 10_000,
                FieldOverlapResult.task_id == task2.id,
            )
            (await s.execute(query)).scalars().one()

    # Third run: cache exists, but we don't use it, so it's like the first run - boundaries service is called
    task3 = await mdl.DetermineOverlapTask()
    job_result = BoundariesServiceJob(jobID=str(uuid.uuid4()), status=JobStatus.accepted)
    with patch(
        "http_requests.crons.methods.feature_intersections", return_value=job_result
    ) as mock_feature_intersections:
        await generate_field_overlaps_for_programs(
            request=app_request, task_id=task3.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
        )
        mock_feature_intersections.assert_called()
        async with db_session_maker() as s:
            # We should still have only one cached result
            query = select(BoundariesServiceResult).where(BoundariesServiceResult.params_sha256 == params_sha256)
            (await s.execute(query)).scalars().one()
            # Check that a request object was created
            query = select(ProgramOverlapRequest).where(
                ProgramOverlapRequest.task_id == task3.id,
                ProgramOverlapRequest.program_id == prog.id,
                ProgramOverlapRequest.previous_program_id == prev_prog.id,
            )
            (await s.execute(query)).scalars().one()
            # Check that 2 field overlap results have been created - these are for the 100% overlaps
            query = select(FieldOverlapResult).where(FieldOverlapResult.task_id == task3.id)
            assert len((await s.execute(query)).all()) == 2
            for index, (field, prev_field) in enumerate(list(zip(fields, prev_fields))[:2]):
                query = select(FieldOverlapResult).where(
                    FieldOverlapResult.field_id == field.id,
                    FieldOverlapResult.previous_field_id == prev_field.id,
                    FieldOverlapResult.field_md5 == field.md5,
                    FieldOverlapResult.previous_field_md5 == prev_field.md5,
                    FieldOverlapResult.percentage_overlap == 100,
                    FieldOverlapResult.area_intersection_ha == index + 1,
                    FieldOverlapResult.task_id == task3.id,
                )
                (await s.execute(query)).scalars().one()


async def test_generate_project_overlaps_for_programs(mdl, app_request, db_session_maker, faker):
    task1 = await mdl.DetermineOverlapTask()
    prog = await mdl.Programs()
    proj1 = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    field1_1 = await mdl.Fields(parent_project_id=proj1.id, md5=faker.md5(), status=FieldStatus.enrolled)
    field1_2 = await mdl.Fields(parent_project_id=proj1.id, md5=faker.md5(), status=FieldStatus.enrolled)
    proj2 = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    field2_1 = await mdl.Fields(parent_project_id=proj2.id, md5=faker.md5(), status=FieldStatus.enrolled)
    field2_2 = await mdl.Fields(parent_project_id=proj2.id, md5=faker.md5(), status=FieldStatus.enrolled)
    await mdl.ProjectPermissions(project=proj1.id, user=1)
    await mdl.ProjectPermissions(project=proj2.id, user=2)
    prev_prog = await mdl.Programs()
    prev_proj1 = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_field1_1 = await mdl.Fields(parent_project_id=prev_proj1.id, md5=faker.md5(), status=FieldStatus.enrolled)
    prev_field1_2 = await mdl.Fields(parent_project_id=prev_proj1.id, md5=faker.md5(), status=FieldStatus.enrolled)
    prev_proj2 = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_field2_1 = await mdl.Fields(parent_project_id=prev_proj2.id, md5=faker.md5(), status=FieldStatus.enrolled)
    prev_field2_2 = await mdl.Fields(parent_project_id=prev_proj2.id, md5=faker.md5(), status=FieldStatus.enrolled)
    await mdl.ProjectPermissions(project=prev_proj1.id, user=1)
    await mdl.ProjectPermissions(project=prev_proj2.id, user=2)

    proj1_md5s = tuple(sorted([field1_1.md5, field1_2.md5]))
    proj2_md5s = tuple(sorted([field2_1.md5, field2_2.md5]))

    job_ids_by_md5s = {
        proj1_md5s: str(uuid.uuid4()),
        proj2_md5s: str(uuid.uuid4()),
    }

    params_sha256s = [
        sha256_param(
            BoundariesServiceAPICall.union_areas,
            [field1_1.md5, field1_2.md5],
            [prev_field1_1.md5, prev_field1_2.md5],
        ),
        sha256_param(
            BoundariesServiceAPICall.union_areas,
            [field2_1.md5, field2_2.md5],
            [prev_field2_1.md5, prev_field2_2.md5],
        ),
    ]

    async def mock_union_areas(md5s, previous_md5s, url):
        job_id = job_ids_by_md5s[tuple(sorted(md5s))]
        return BoundariesServiceJob(jobID=job_id, status=JobStatus.accepted)

    # First run: no cache exists
    with patch("http_requests.crons.methods.union_areas", mock_union_areas):
        await generate_project_overlaps_for_programs(
            request=app_request, task_id=task1.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
        )
        async with db_session_maker() as s:
            cached_results = []
            # Is empty result created correctly for each project?
            for sha in params_sha256s:
                query = select(BoundariesServiceResult).where(
                    BoundariesServiceResult.params_sha256 == sha, BoundariesServiceResult.result.is_(None)
                )
                cached_results.append((await s.execute(query)).scalars().one())
            # Has request been created for each project?
            query = select(ProjectOverlapRequest).where(
                ProjectOverlapRequest.project_id == proj1.id,
                ProjectOverlapRequest.previous_project_id == prev_proj1.id,
                ProjectOverlapRequest.task_id == task1.id,
                ProjectOverlapRequest.cache_id == cached_results[0].id,
                ProjectOverlapRequest.job_id == job_ids_by_md5s[proj1_md5s],
                ProjectOverlapRequest.status == JobStatus.accepted,
                ProjectOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()
            query = select(ProjectOverlapRequest).where(
                ProjectOverlapRequest.project_id == proj2.id,
                ProjectOverlapRequest.previous_project_id == prev_proj2.id,
                ProjectOverlapRequest.task_id == task1.id,
                ProjectOverlapRequest.cache_id == cached_results[1].id,
                ProjectOverlapRequest.job_id == job_ids_by_md5s[proj2_md5s],
                ProjectOverlapRequest.status == JobStatus.accepted,
                ProjectOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()

    # Second run: cache now exists, so use cache to create results (cache will be created when boundaries service calls
    # us back)
    cached_results[0].result = UnionArea(
        list_one_union_area_m2=60_000, list_two_union_area_m2=40_000, area_intersection_union_m2_both=10_000
    ).dict()
    cached_results[1].result = UnionArea(
        list_one_union_area_m2=100_000, list_two_union_area_m2=150_000, area_intersection_union_m2_both=30_000
    ).dict()
    await update.update(request=app_request, instances=cached_results, type=BoundariesServiceResult)
    task2 = await mdl.DetermineOverlapTask()
    await generate_project_overlaps_for_programs(
        request=app_request, task_id=task2.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    async with db_session_maker() as s:
        # Check that 2 project overlap results have been created from the cached objects
        query = select(ProjectOverlapResult).where(
            ProjectOverlapResult.project_id == proj1.id,
            ProjectOverlapResult.previous_project_id == prev_proj1.id,
            ProjectOverlapResult.task_id == task2.id,
            ProjectOverlapResult.project_area_ha == 6,
            ProjectOverlapResult.previous_project_area_ha == 4,
            ProjectOverlapResult.area_intersection_ha == 1,
        )
        (await s.execute(query)).scalars().one()
        query = select(ProjectOverlapResult).where(
            ProjectOverlapResult.project_id == proj2.id,
            ProjectOverlapResult.previous_project_id == prev_proj2.id,
            ProjectOverlapResult.task_id == task2.id,
            ProjectOverlapResult.project_area_ha == 10,
            ProjectOverlapResult.previous_project_area_ha == 15,
            ProjectOverlapResult.area_intersection_ha == 3,
        )
        (await s.execute(query)).scalars().one()
        # Check that 2 cached results have been used
        query = select(BoundariesServiceResult).where(
            BoundariesServiceResult.params_sha256.in_(params_sha256s), BoundariesServiceResult.last_used_at.isnot(None)
        )
        assert len((await s.execute(query)).all()) == 2
        # Check that no request objects were created, because we used cache
        query = select(ProjectOverlapRequest).where(ProjectOverlapRequest.task_id == task2.id)
        assert len((await s.execute(query)).all()) == 0

    # Third run: cache exists, but we don't use it, so it's like the first run - boundaries service is called
    task3 = await mdl.DetermineOverlapTask()
    job_ids_by_md5s = {
        proj1_md5s: str(uuid.uuid4()),
        proj2_md5s: str(uuid.uuid4()),
    }
    with patch("http_requests.crons.methods.union_areas", mock_union_areas):
        await generate_project_overlaps_for_programs(
            request=app_request, task_id=task3.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
        )
        async with db_session_maker() as s:
            # No result should have been created
            query = select(ProjectOverlapResult).where(ProjectOverlapResult.task_id == task3.id)
            assert len((await s.execute(query)).all()) == 0
            # Check that we still have only 2 cached results
            query = select(BoundariesServiceResult).where(
                BoundariesServiceResult.params_sha256.in_(params_sha256s),
                BoundariesServiceResult.last_used_at.isnot(None),
            )
            assert len((await s.execute(query)).all()) == 2
            # Check that request objects have been created
            query = select(ProjectOverlapRequest).where(
                ProjectOverlapRequest.project_id == proj1.id,
                ProjectOverlapRequest.previous_project_id == prev_proj1.id,
                ProjectOverlapRequest.task_id == task3.id,
                ProjectOverlapRequest.cache_id == cached_results[0].id,
                ProjectOverlapRequest.job_id == job_ids_by_md5s[proj1_md5s],
                ProjectOverlapRequest.status == JobStatus.accepted,
                ProjectOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()
            query = select(ProjectOverlapRequest).where(
                ProjectOverlapRequest.project_id == proj2.id,
                ProjectOverlapRequest.previous_project_id == prev_proj2.id,
                ProjectOverlapRequest.task_id == task3.id,
                ProjectOverlapRequest.cache_id == cached_results[1].id,
                ProjectOverlapRequest.job_id == job_ids_by_md5s[proj2_md5s],
                ProjectOverlapRequest.status == JobStatus.accepted,
                ProjectOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()


async def test_generate_region_overlaps_for_programs(mdl, app_request, db_session_maker, faker):
    prog = await mdl.Programs()
    proj1 = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    # Only regions 2 and 3 are shared between programs, the rest are either in one program or the other, so there won't
    # be any overlaps between the programs for those regions.
    region2_md5s = tuple(sorted([faker.md5() for _ in range(2)]))
    region3_md5s = tuple(sorted([faker.md5() for _ in range(2)]))
    await mdl.Fields(parent_project_id=proj1.id, md5=faker.md5(), status=FieldStatus.enrolled, core_region_id=1)
    await mdl.Fields(parent_project_id=proj1.id, md5=faker.md5(), status=FieldStatus.enrolled, core_region_id=1)
    proj2 = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    await mdl.Fields(parent_project_id=proj2.id, md5=faker.md5(), status=FieldStatus.enrolled, core_region_id=1)
    await mdl.Fields(parent_project_id=proj2.id, md5=region2_md5s[0], status=FieldStatus.enrolled, core_region_id=2)
    proj3 = await mdl.Projects(
        program_id=prog.id, status=ProjectStatus.enrolled, reporting_enabled=True, contract_status=ContractStatus.signed
    )
    await mdl.Fields(parent_project_id=proj3.id, md5=region2_md5s[1], status=FieldStatus.enrolled, core_region_id=2)
    await mdl.Fields(parent_project_id=proj3.id, md5=region3_md5s[0], status=FieldStatus.enrolled, core_region_id=3)
    await mdl.Fields(parent_project_id=proj3.id, md5=region3_md5s[1], status=FieldStatus.enrolled, core_region_id=3)
    await mdl.ProjectPermissions(project=proj1.id, user=1)
    await mdl.ProjectPermissions(project=proj2.id, user=2)
    await mdl.ProjectPermissions(project=proj2.id, user=3)

    prev_prog = await mdl.Programs()
    prev_proj1 = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    prev_region2_md5s = [faker.md5()]
    prev_region3_md5s = [faker.md5() for _ in range(2)]
    await mdl.Fields(
        parent_project_id=prev_proj1.id, md5=prev_region2_md5s[0], status=FieldStatus.enrolled, core_region_id=2
    )
    await mdl.Fields(
        parent_project_id=prev_proj1.id, md5=prev_region3_md5s[0], status=FieldStatus.enrolled, core_region_id=3
    )
    prev_proj2 = await mdl.Projects(
        program_id=prev_prog.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=True,
        contract_status=ContractStatus.signed,
    )
    await mdl.Fields(
        parent_project_id=prev_proj2.id, md5=prev_region3_md5s[1], status=FieldStatus.enrolled, core_region_id=3
    )
    await mdl.Fields(parent_project_id=prev_proj2.id, md5=faker.md5(), status=FieldStatus.enrolled, core_region_id=4)
    await mdl.ProjectPermissions(project=prev_proj1.id, user=1)
    await mdl.ProjectPermissions(project=prev_proj2.id, user=2)

    params_sha256s = [
        sha256_param(
            BoundariesServiceAPICall.union_areas,
            region2_md5s,
            prev_region2_md5s,
        ),
        sha256_param(
            BoundariesServiceAPICall.union_areas,
            region3_md5s,
            prev_region3_md5s,
        ),
    ]

    job_ids_by_md5s = {
        region2_md5s: str(uuid.uuid4()),
        region3_md5s: str(uuid.uuid4()),
    }

    async def mock_union_areas(md5s, previous_md5s, url):
        job_id = job_ids_by_md5s[tuple(sorted(md5s))]
        return BoundariesServiceJob(jobID=job_id, status=JobStatus.accepted)

    # First run: no cache exists
    with patch("http_requests.crons.methods.union_areas", mock_union_areas):
        task1 = await mdl.DetermineOverlapTask()
        await generate_region_overlaps_for_programs(
            request=app_request, task_id=task1.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
        )
        async with db_session_maker() as s:
            cached_results = []
            # Is empty result created correctly for each project?
            for sha in params_sha256s:
                query = select(BoundariesServiceResult).where(
                    BoundariesServiceResult.params_sha256 == sha, BoundariesServiceResult.result.is_(None)
                )
                cached_results.append((await s.execute(query)).scalars().one())
            # Has request been created for each project?
            query = select(RegionOverlapRequest).where(
                RegionOverlapRequest.program_id == prog.id,
                RegionOverlapRequest.previous_program_id == prev_prog.id,
                RegionOverlapRequest.task_id == task1.id,
                RegionOverlapRequest.cache_id == cached_results[0].id,
                RegionOverlapRequest.job_id == job_ids_by_md5s[region2_md5s],
                RegionOverlapRequest.status == JobStatus.accepted,
                RegionOverlapRequest.core_region_id == 2,
                RegionOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()
            query = select(RegionOverlapRequest).where(
                RegionOverlapRequest.program_id == prog.id,
                RegionOverlapRequest.previous_program_id == prev_prog.id,
                RegionOverlapRequest.task_id == task1.id,
                RegionOverlapRequest.cache_id == cached_results[1].id,
                RegionOverlapRequest.job_id == job_ids_by_md5s[region3_md5s],
                RegionOverlapRequest.status == JobStatus.accepted,
                RegionOverlapRequest.core_region_id == 3,
                RegionOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()

    # Second run: cache now exists, so use cache to create results (cache will be created when boundaries service calls
    # us back)
    cached_results[0].result = UnionArea(
        list_one_union_area_m2=120_000, list_two_union_area_m2=50_000, area_intersection_union_m2_both=40_000
    ).dict()
    cached_results[1].result = UnionArea(
        list_one_union_area_m2=90_000, list_two_union_area_m2=110_000, area_intersection_union_m2_both=50_000
    ).dict()
    await update.update(request=app_request, instances=cached_results, type=BoundariesServiceResult)
    task2 = await mdl.DetermineOverlapTask()
    await generate_region_overlaps_for_programs(
        request=app_request, task_id=task2.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    async with db_session_maker() as s:
        # Check that 2 region overlap results have been created from the cached objects
        query = select(RegionOverlapResult).where(
            RegionOverlapResult.program_id == prog.id,
            RegionOverlapResult.previous_program_id == prev_prog.id,
            RegionOverlapResult.task_id == task2.id,
            RegionOverlapResult.core_region_id == 2,
            RegionOverlapResult.program_region_area_ha == 12,
            RegionOverlapResult.previous_program_region_area_ha == 5,
            RegionOverlapResult.area_intersection_ha == 4,
        )
        (await s.execute(query)).scalars().one()
        query = select(RegionOverlapResult).where(
            RegionOverlapResult.program_id == prog.id,
            RegionOverlapResult.previous_program_id == prev_prog.id,
            RegionOverlapResult.task_id == task2.id,
            RegionOverlapResult.core_region_id == 3,
            RegionOverlapResult.program_region_area_ha == 9,
            RegionOverlapResult.previous_program_region_area_ha == 11,
            RegionOverlapResult.area_intersection_ha == 5,
        )
        (await s.execute(query)).scalars().one()
        # Check that 2 cached results have been used
        query = select(BoundariesServiceResult).where(
            BoundariesServiceResult.params_sha256.in_(params_sha256s), BoundariesServiceResult.last_used_at.isnot(None)
        )
        assert len((await s.execute(query)).all()) == 2
        # Check that no request objects were created, because we used cache
        query = select(RegionOverlapRequest).where(RegionOverlapRequest.task_id == task2.id)
        assert len((await s.execute(query)).all()) == 0

    # Third run: cache exists, but we don't use it, so it's like the first run - boundaries service is called
    task3 = await mdl.DetermineOverlapTask()
    job_ids_by_md5s = {
        region2_md5s: str(uuid.uuid4()),
        region3_md5s: str(uuid.uuid4()),
    }
    with patch("http_requests.crons.methods.union_areas", mock_union_areas):
        await generate_region_overlaps_for_programs(
            request=app_request, task_id=task3.id, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
        )
        async with db_session_maker() as s:
            # No result should have been created
            query = select(RegionOverlapResult).where(RegionOverlapResult.task_id == task3.id)
            assert len((await s.execute(query)).all()) == 0
            # Check that we still have only 2 cached results
            query = select(BoundariesServiceResult).where(
                BoundariesServiceResult.params_sha256.in_(params_sha256s),
                BoundariesServiceResult.last_used_at.isnot(None),
            )
            assert len((await s.execute(query)).all()) == 2
            # Check that request objects have been created
            query = select(RegionOverlapRequest).where(
                RegionOverlapRequest.program_id == prog.id,
                RegionOverlapRequest.previous_program_id == prev_prog.id,
                RegionOverlapRequest.task_id == task3.id,
                RegionOverlapRequest.cache_id == cached_results[0].id,
                RegionOverlapRequest.job_id == job_ids_by_md5s[region2_md5s],
                RegionOverlapRequest.status == JobStatus.accepted,
                RegionOverlapRequest.core_region_id == 2,
                RegionOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()
            query = select(RegionOverlapRequest).where(
                RegionOverlapRequest.program_id == prog.id,
                RegionOverlapRequest.previous_program_id == prev_prog.id,
                RegionOverlapRequest.task_id == task3.id,
                RegionOverlapRequest.cache_id == cached_results[1].id,
                RegionOverlapRequest.job_id == job_ids_by_md5s[region3_md5s],
                RegionOverlapRequest.status == JobStatus.accepted,
                RegionOverlapRequest.core_region_id == 3,
                RegionOverlapRequest.result.is_(None),
            )
            (await s.execute(query)).scalars().one()


@patch("http_requests.crons.methods.generate_program_overlaps_for_programs")
@patch("http_requests.crons.methods.generate_project_overlaps_for_programs")
@patch("http_requests.crons.methods.generate_field_overlaps_for_programs")
@patch("http_requests.crons.methods.generate_region_overlaps_for_programs")
async def test_generate_overlaps_for_programs(mock_region, mock_field, mock_project, mock_program, mdl, app_request):
    prev_prog = await mdl.Programs()
    prog = await mdl.Programs(previous_program_id=prev_prog.id)
    another_prog = await mdl.Programs()

    with pytest.raises(ValueError):
        await generate_overlaps_for_programs(
            request=app_request,
            overlap_task_id=1,
            program_id=prog.id,
            previous_program_id=another_prog.id,
            use_cache=True,
        )

    await generate_overlaps_for_programs(
        request=app_request, overlap_task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    mock_region.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    mock_field.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    mock_project.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )
    mock_program.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=True
    )

    await generate_overlaps_for_programs(
        request=app_request, overlap_task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
    )
    mock_region.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
    )
    mock_field.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
    )
    mock_project.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
    )
    mock_program.assert_called_with(
        request=app_request, task_id=1, program_id=prog.id, previous_program_id=prev_prog.id, use_cache=False
    )


@async_override_settings(settings=settings, OVERLAP_TASK_TIMEOUT_SECONDS=0)
async def test_wait_for_task_completion(app_request, mdl):
    task1 = await mdl.DetermineOverlapTask()
    assert task1.finished_at is None
    assert not task1.error
    await wait_for_task_completion(request=app_request, overlap_task=task1)
    task = (
        await get.get(
            request=app_request, orm_type=DetermineOverlapTask, id_field=DetermineOverlapTask.id, ids=[task1.id]
        )
    )[0]
    assert task.finished_at is not None
    assert not task.error

    # If requests are outstanding, we set an error and not set finished_at
    task2 = await mdl.DetermineOverlapTask()
    prog1 = await mdl.Programs()
    prog2 = await mdl.Programs()
    await mdl.ProgramOverlapRequest(
        task_id=task2.id, program_id=prog1.id, previous_program_id=prog2.id, status=JobStatus.accepted
    )
    await wait_for_task_completion(request=app_request, overlap_task=task2)
    task = (
        await get.get(
            request=app_request, orm_type=DetermineOverlapTask, id_field=DetermineOverlapTask.id, ids=[task2.id]
        )
    )[0]
    assert task.finished_at is None
    assert task.error
