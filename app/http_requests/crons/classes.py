from abc import ABC, abstractmethod

from fastapi import Request
from sqlalchemy import func

from boundaries_service.schema import FeatureIntersections, UnionArea
from http_requests.callbacks.utils import (
    create_fully_overlapped_field_results,
    m2_to_hectare,
    process_feature_intersection_results,
    sha256_param,
)
from http_requests.crons.enums import BoundariesServiceAPICall
from http_requests.crons.model import (
    BoundariesServiceResult,
    ProgramOverlapResult,
    ProjectOverlapResult,
    RegionOverlapResult,
)
from root_crud import create, get, update


class ProcessCachedResult(ABC):
    """
    Abstract class for processing cached results from the Boundaries Service API calls. These results are stored in
    table mrv_boundaries_service_result.

    You construct an instance of this class with some of the parameters that you would call the API with. Then you call
    the process method with the other parameters you want to call the API with.

    The process method will check if the result is already cached, and if it is, it will process the cached result and
    store the relevant result rows in the DB.

    If the result is not cached, it will return the sha256 of the parameters, and it would then be up to the
    caller to call the API with those parameters and store the result in the DB, including the cached result in
    mrv_boundaries_service_result.

    For example, here's how you might use ProcessProgramOverlapResult:

    ```python
    cached_result = Process<PERSON>rogramOverlapResult(program_id, previous_program_id)
    sha256, is_processed = await cached_result.process(
        request=request, task_id=task_id, md5s=md5s, previous_md5s=previous_md5s, use_cache=True
    )
    if not is_processed:
        # Call the API with the md5s and previous_md5s and store the result in the DB
    ```

    Derived classes implement abstract method process_cached_result, which processes the cached result (the JSON
    which we got from the Boundaries Service API call and stored in the DB) and generates and stores the relevant
    results rows in the DB.
    """

    @property
    @abstractmethod
    def api_type(self) -> BoundariesServiceAPICall: ...

    async def process(
        self, *, request: Request, task_id: int, md5s: list[str], previous_md5s: list[str], use_cache: bool
    ) -> tuple[str, bool]:
        """
        See class docstring for details and usage.

        Args:
            request: The FastAPI request object
            task_id: The ID of the task that initiated the API call
            md5s: The list of md5s that you would call the API with
            previous_md5s: The list of previous md5s that you would call the API with
            use_cache: Whether to use the cache or not. If False, we ignore the cache in mrv_boundaries_service_result
                even if it exists

        Returns:
            A tuple of sha256 of the parameters and a boolean indicating whether the we found a cached result and
            processed it.
        """
        params_sha256 = sha256_param(self.api_type, md5s, previous_md5s)
        if not use_cache:
            return params_sha256, False
        rows = await get.generic_get(
            request=request,
            orm_type=BoundariesServiceResult,
            filters=[get.Filter(id_field=BoundariesServiceResult.params_sha256, ids=[params_sha256])],
            empty_return=True,
        )
        if rows:
            cached_result = rows[0]
            if cached_result.result is not None:
                await self.process_cached_result(
                    request=request,
                    cached_result=cached_result.result,
                    task_id=task_id,
                    md5s=md5s,
                    previous_md5s=previous_md5s,
                )
                cached_result.last_used_at = func.now()
                await update.update(request=request, instances=[cached_result], type=BoundariesServiceResult)
                return params_sha256, True

        return params_sha256, False

    @abstractmethod
    async def process_cached_result(
        self, *, request: Request, cached_result: dict, task_id: int, md5s: list[str], previous_md5s: list[str]
    ) -> None: ...


class ProcessUnionAreasResult(ProcessCachedResult, ABC):
    """
    Abstract class for processing cached results from the Boundaries Service API call to get union areas
    """

    @property
    def api_type(self) -> BoundariesServiceAPICall:
        return BoundariesServiceAPICall.union_areas


class ProcessProgramOverlapResult(ProcessUnionAreasResult):
    """
    Process the results of a Boundaries Service union areas API call for two programs, and create ProgramOverlapResult
    """

    def __init__(self, program_id: int, previous_program_id: int):
        self.program_id = program_id
        self.previous_program_id = previous_program_id

    async def process_cached_result(
        self, *, request: Request, cached_result: dict, task_id: int, md5s: list[str], previous_md5s: list[str]
    ) -> None:
        union_area = UnionArea.parse_obj(cached_result)
        result_instance = ProgramOverlapResult(
            task_id=task_id,
            program_id=self.program_id,
            previous_program_id=self.previous_program_id,
            program_area_ha=m2_to_hectare(union_area.list_one_union_area_m2),
            previous_program_area_ha=m2_to_hectare(union_area.list_two_union_area_m2),
            # area_intersection_union_m2_both might be None
            area_intersection_ha=m2_to_hectare(union_area.area_intersection_union_m2_both or 0),
        )
        await create.create(
            request=request,
            instances=[result_instance],
            orm_type=ProgramOverlapResult,
            translate=False,
            no_return=False,
        )


class ProcessProjectOverlapResult(ProcessUnionAreasResult):
    """
    Process the results of a Boundaries Service union areas API call for two projects, and create ProjectOverlapResult
    """

    def __init__(self, project_id: int, previous_project_id: int):
        self.project_id = project_id
        self.previous_project_id = previous_project_id

    async def process_cached_result(
        self, *, request: Request, cached_result: dict, task_id: int, md5s: list[str], previous_md5s: list[str]
    ) -> None:
        union_area = UnionArea.parse_obj(cached_result)
        result_instance = ProjectOverlapResult(
            task_id=task_id,
            project_id=self.project_id,
            previous_project_id=self.previous_project_id,
            project_area_ha=m2_to_hectare(union_area.list_one_union_area_m2),
            previous_project_area_ha=m2_to_hectare(union_area.list_two_union_area_m2),
            # area_intersection_union_m2_both might be None
            area_intersection_ha=m2_to_hectare(union_area.area_intersection_union_m2_both or 0),
        )
        await create.create(
            request=request,
            instances=[result_instance],
            orm_type=ProjectOverlapResult,
            translate=False,
            no_return=False,
        )


class ProcessFieldOverlapResults(ProcessCachedResult):
    """
    Process the results of a Boundaries Service feature intersection API call for multiple fields, and create
    FieldOverlapResult instances.
    """

    def __init__(self, program_id: int, previous_program_id: int):
        self.program_id = program_id
        self.previous_program_id = previous_program_id

    @property
    def api_type(self) -> BoundariesServiceAPICall:
        return BoundariesServiceAPICall.feature_intersections

    async def process_cached_result(
        self, *, request: Request, cached_result: dict, task_id: int, md5s: list[str], previous_md5s: list[str]
    ) -> None:
        await create_fully_overlapped_field_results(
            request=request,
            md5s=md5s,
            previous_md5s=previous_md5s,
            program_id=self.program_id,
            previous_program_id=self.previous_program_id,
            task_id=task_id,
        )
        intersections = FeatureIntersections.parse_obj(cached_result)
        await process_feature_intersection_results(
            request=request,
            intersections=intersections,
            program_id=self.program_id,
            previous_program_id=self.previous_program_id,
            task_id=task_id,
        )


class ProcessRegionOverlapResult(ProcessUnionAreasResult):
    """
    Process the results of a Boundaries Service union areas API call for two programs, and create RegionOverlapResult
    """

    def __init__(self, program_id: int, previous_program_id: int, core_region_id: int):
        self.program_id = program_id
        self.previous_program_id = previous_program_id
        self.core_region_id = core_region_id

    async def process_cached_result(
        self, *, request: Request, cached_result: dict, task_id: int, md5s: list[str], previous_md5s: list[str]
    ) -> None:
        union_area = UnionArea.parse_obj(cached_result)
        result_instance = RegionOverlapResult(
            task_id=task_id,
            program_id=self.program_id,
            previous_program_id=self.previous_program_id,
            core_region_id=self.core_region_id,
            program_region_area_ha=m2_to_hectare(union_area.list_one_union_area_m2),
            previous_program_region_area_ha=m2_to_hectare(union_area.list_two_union_area_m2),
            # area_intersection_union_m2_both might be None
            area_intersection_ha=m2_to_hectare(union_area.area_intersection_union_m2_both or 0),
        )
        await create.create(
            request=request,
            instances=[result_instance],
            orm_type=RegionOverlapResult,
            translate=False,
            no_return=False,
        )
