from json.decoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r
from math import floor

from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, status

from config import Settings
from data_service.enums import TrendTypes
from data_service.request import get_ds_imagery_dates, get_trend_data
from data_service.response_schema import AvailableImagesResponse, TrendResponse
from data_service.schema import AvailableImagery
from logger import get_logger

logger = get_logger(__name__)


def transform_imagery_dates_response(
    response: AvailableImagesResponse,
) -> list[AvailableImagery]:
    """
    Transforms response format to a more useful internal format
    """
    return [
        AvailableImagery(
            image_ts=response.available_images[i],
            source=response.sources[i],
            cloud_percentage=floor(float(response.cloud_percentage[i])),
        )
        for i in range(response.number_of_images)
    ]


def pick_imagery_dates(
    imagery_response: list[AvailableImagery],
    cloudcover_percentage: int,
    num_images: int = 4,
) -> list[AvailableImagery]:
    """
    Returns a list of imagery evenly spaced, with cloud covered images removed
    """
    ok_images: list[AvailableImagery] = []
    for image in imagery_response:
        if image.cloud_percentage < cloudcover_percentage:
            ok_images.append(image)
    total_images = len(ok_images)
    if total_images == 0:
        return []
    interval = floor(total_images / num_images)
    output: list[AvailableImagery] = []
    for i in range(num_images):
        output.append(ok_images[i * interval])
    return output


async def get_imagery_dates(
    start_date: str,
    end_date: str,
    md5: str,
    cloud_cover_percentage: int,
    num_images: int,
    settings: Settings,
) -> list[AvailableImagery]:
    raw = await get_ds_imagery_dates(start_date=start_date, end_date=end_date, md5=md5, settings=settings)
    transformed_imagery_dates = transform_imagery_dates_response(AvailableImagesResponse(**raw.json()))
    logger.debug(transformed_imagery_dates)
    if len(transformed_imagery_dates) == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "No imagery available for that md5"},
        )
    return pick_imagery_dates(
        imagery_response=transformed_imagery_dates,
        cloudcover_percentage=cloud_cover_percentage,
        num_images=num_images,
    )


async def get_trends(
    start_date: str,
    end_date: str,
    md5: str,
    metric: TrendTypes,
    settings: Settings,
    cloud_cover_perc: int,
) -> list[TrendResponse]:
    response = await get_trend_data(
        start_date=start_date,
        end_date=end_date,
        md5=md5,
        metric=metric,
        settings=settings,
    )
    try:
        r_json = response.json()
    except JSONDecodeError as e:
        logger.exception(e)
        return []
    resp: list[TrendResponse] = []
    for row in r_json:
        result = TrendResponse(**row)
        if result.cloud_percentage < cloud_cover_perc:
            resp.append(result)
    return resp
