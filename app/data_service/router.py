from __future__ import annotations

from typing import Any

import elasticapm
from fastapi import APIRouter, Depends, Request, status

from config import get_settings, Settings
from data_service import methods, paths
from data_service.enums import TrendTypes
from data_service.request import get_ds_imagery_dates
from data_service.response_schema import TrendResponse
from logger import get_logger
from programs.db import get_program_imagery_dates_num
from projects.conflicts.db import get_program_conflict_md5s

logger = get_logger(__name__)
router = APIRouter(prefix=paths.base)
tags = ["data_service", "internal"]


@elasticapm.async_capture_span()
@router.get(
    "/available_dates",  # /data-service/available_dates
    tags=tags,
    status_code=status.HTTP_200_OK,
)
async def get_dates(start_date: str, end_date: str, md5: str, settings: Settings = Depends(get_settings)) -> Any:
    return (await get_ds_imagery_dates(start_date, end_date, md5, settings)).json()


@elasticapm.async_capture_span()
@router.get(
    "/program/{program_id}/available_dates",  # /programs/{program_id}/phases
    tags=tags,
    status_code=status.HTTP_200_OK,
)
async def get_program_dates(
    request: Request, program_id: int, settings: Settings = Depends(get_settings)
) -> list[dict]:
    start_date, end_date, _, _ = await get_program_imagery_dates_num(request=request, program_id=program_id)
    conflict_md5s = await get_program_conflict_md5s(request=request, program_id=program_id)
    retval = []
    for conflict in conflict_md5s:
        tmp = {
            "md5": conflict[0],
            "project_id": conflict[2],
            "field_id": conflict[1],
            "data": (await get_ds_imagery_dates(start_date.date(), end_date.date(), conflict[0], settings)).json(),
        }
        if len(tmp.get("data", {"available_images": []}).get("available_images")) < 5:
            logger.debug(tmp)
            retval.append(tmp)
    return retval


@elasticapm.async_capture_span()
@router.get(
    "/program/{program_id}/trend",  # /programs/{program_id}/phases
    tags=tags,
    status_code=status.HTTP_200_OK,
)
async def get_program_trends(
    request: Request,
    program_id: int,
    metric: TrendTypes,
    settings: Settings = Depends(get_settings),
) -> list[dict]:
    start_date, end_date, _, cloud_cover_perc = await get_program_imagery_dates_num(
        request=request, program_id=program_id
    )
    conflict_md5s = await get_program_conflict_md5s(request=request, program_id=program_id)
    retval = []
    for conflict in conflict_md5s:
        tmp = {
            "md5": conflict[0],
            "project_id": conflict[2],
            "field_id": conflict[1],
            "data": (
                await methods.get_trends(
                    str(start_date.date()),
                    str(end_date.date()),
                    conflict[0],
                    metric,
                    settings,
                    cloud_cover_perc,
                )
            ),
        }
        logger.debug(tmp)
        retval.append(tmp)
    return retval


@elasticapm.async_capture_span()
@router.get(
    "/trend",  # /programs/{program_id}/phases
    tags=tags,
    status_code=status.HTTP_200_OK,
    response_model=list[TrendResponse],
)
async def get_trends(
    start_date: str,
    end_date: str,
    md5: str,
    cloud_cover_perc: int,
    metric: TrendTypes,
    settings: Settings = Depends(get_settings),
) -> list[TrendResponse]:
    return await methods.get_trends(start_date, end_date, md5, metric, settings, cloud_cover_perc)
