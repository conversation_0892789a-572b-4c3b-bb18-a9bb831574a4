import json

import httpx
from fastapi import status

from config import Settings
from data_service.enums import TrendTypes
from helper.external_api import handle_response
from logger import get_logger

logger = get_logger(__name__)


@handle_response(expected_response_code=status.HTTP_200_OK)
async def get_ds_imagery_dates(start_date: str, end_date: str, md5: str, settings: Settings) -> httpx.Response:
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        return await client.get(
            f"{settings.DATASERVICE_BASE_URL}{settings.DATASERVICE_AVAILABLE_IMAGES.format(md5=md5, start_date=start_date, end_date=end_date)}",
        )


async def get_ds_imagery(
    md5: str,
    timestamp: str,
    settings: Settings,
    r: str = "B04",
    b: str = "B02",
    g: str = "B03",
) -> httpx.Response:
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        return await client.get(
            f"{settings.DATASERVICE_BASE_URL}{settings.DATASERVICE_FALSE_COLOUR.format(md5=md5, timestamp=timestamp)}",
            params={"layers": [r, g, b]},
        )


# @handle_response(expected_response_code=status.HTTP_200_OK)
async def get_trend_data(
    start_date: str,
    end_date: str,
    md5: str,
    metric: TrendTypes,
    settings: Settings,
) -> httpx.Response:
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        body = json.dumps(
            {
                "md5": [md5],
                "start_date": start_date,
                "end_date": end_date,
                "metrics": [metric.value, "cloud.percentage"],
            }
        )
        return await client.post(
            f"{settings.DATASERVICE_BASE_URL}{settings.DATASERVICE_METRICS}",
            content=body,
            headers={"Content-Type": "application/json"},
        )
