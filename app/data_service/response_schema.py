from datetime import datetime
from typing import Any

from pydantic import BaseModel, root_validator


class AvailableImagesResponse(BaseModel):
    available_images: list[str]
    sources: list[str]
    cloud_percentage: list[str]
    number_of_images: int
    max_cloud_percentage: int


class TrendResponse(BaseModel):
    metric: float | None
    sensing_time: datetime
    cloud_percentage: float

    @root_validator(pre=True)
    def change_field_name(cls, values: dict[str, Any]) -> dict[str, Any]:  # noqa: N805
        res = values.get("ndti.masked_clouds.mean")
        if res:
            values["metric"] = res
        else:
            values["metric"] = values.get("ndvi.masked_clouds.mean")
        values["cloud_percentage"] = values.get("cloud.percentage")
        return values
