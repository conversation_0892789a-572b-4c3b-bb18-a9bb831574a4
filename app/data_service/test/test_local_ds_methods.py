import pytest

from config import get_settings
from data_service.methods import (
    get_imagery_dates,
    pick_imagery_dates,
    transform_imagery_dates_response,
)
from data_service.response_schema import AvailableImagesResponse
from data_service.schema import AvailableImagery

settings = get_settings()


@pytest.mark.parametrize(
    "input_,expected",
    [
        (
            AvailableImagesResponse(
                available_images=[],
                sources=[],
                cloud_percentage=[],
                number_of_images=0,
                max_cloud_percentage=0,
            ),
            [],
        ),
        (
            AvailableImagesResponse(
                available_images=["1", "2", "3"],
                sources=["foo", "foo", "foo"],
                cloud_percentage=["10.0002", "100", "1.0002"],
                number_of_images=3,
                max_cloud_percentage=100,
            ),
            [
                AvailableImagery(image_ts="1", source="foo", cloud_percentage=10),
                AvailableImagery(image_ts="2", source="foo", cloud_percentage=100),
                AvailableImagery(image_ts="3", source="foo", cloud_percentage=1),
            ],
        ),
    ],
)
def test_transform_imagery_dates_response(input_, expected):
    assert transform_imagery_dates_response(input_) == expected


@pytest.mark.parametrize(
    "ccp, num ,expected",
    [
        (
            0,
            1,
            [],
        ),
        (
            2,
            1,
            [AvailableImagery(image_ts="0", source="foo", cloud_percentage=0)],
        ),
        (
            5,
            1,
            [AvailableImagery(image_ts="0", source="foo", cloud_percentage=0)],
        ),
        (
            11,
            2,
            [
                AvailableImagery(image_ts="0", source="foo", cloud_percentage=0),
                AvailableImagery(image_ts="5", source="foo", cloud_percentage=5),
            ],
        ),
        (
            200,
            5,
            [
                AvailableImagery(image_ts="0", source="foo", cloud_percentage=0),
                AvailableImagery(image_ts="40", source="foo", cloud_percentage=40),
                AvailableImagery(image_ts="80", source="foo", cloud_percentage=80),
                AvailableImagery(image_ts="120", source="foo", cloud_percentage=120),
                AvailableImagery(image_ts="160", source="foo", cloud_percentage=160),
            ],
        ),
    ],
)
def test_pick_imagery_dates(ccp, num, expected):
    input_ = [AvailableImagery(image_ts=str(i), source="foo", cloud_percentage=i) for i in range(1000)]
    assert pick_imagery_dates(input_, ccp, num) == expected


async def test_get_imagery_dates():
    assert await get_imagery_dates(
        start_date="1999-01-01",
        end_date="2000-01-01",
        md5="acbd18db4cc2f85cedef654fccc4a4d8",
        cloud_cover_percentage=100,
        num_images=2,
        settings=settings,
    ) == [
        AvailableImagery(image_ts="20210102T001109", source="sentinel2", cloud_percentage=91),
        AvailableImagery(image_ts="20210107T001111", source="sentinel2", cloud_percentage=97),
    ]
