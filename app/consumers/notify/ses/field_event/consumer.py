import asyncio
from uuid import UUID

from fastapi import Request
from google.protobuf.json_format import MessageToDict
from regrow_proto.ses.fieldevents.v1.fieldevent_notification_pb2 import (
    FieldEventAction,
    FieldEventNotification,
)

from config import get_settings
from consumers.kafka_consumer import consume_from_kafka
from logger import get_logger
from ses_integration.event_associations import (
    associate_events_with_fields_of_md5,
    delete_event_associations,
)
from ses_integration.schema import EventRevision

logger = get_logger(__name__)


async def process_message(
    request: Request,
    message: FieldEventNotification,
) -> None:
    """
    Process a FieldEventNotification message, taking appropriate actions based on the message's action type.

    Created or updated events will be associated with fields based on their boundary id (MD5 hash) and user id.
    Archived and deleted events will be both soft deleted, with the distinction that the association to closed phases
    will not be deleted for archived events.

    Each of these actions are considered idempotent, meaning that if the same message is received multiple times,
    the outcome will be the same as if it were processed only once. In future, we may want to implement a more robust
    idempotency check to ensure that duplicate messages do not cause unintended side effects or load.
    """

    logger.info(
        f"FieldEventNotification message with action {message.action} received for event_id: {message.data.event_id}",
        extra=MessageToDict(message),
    )

    # create/update
    match message.action:
        case FieldEventAction.FIELD_EVENT_ACTION_CREATED | FieldEventAction.FIELD_EVENT_ACTION_UPDATED:
            await associate_events_with_fields_of_md5(
                request=request,
                event_revisions=[EventRevision(event_id=UUID(message.data.event_id), revision=message.data.revision)],
                field_md5=message.data.boundary_id,
                owner_user_id=message.data.owner_id,
            )
        case FieldEventAction.FIELD_EVENT_ACTION_ARCHIVED | FieldEventAction.FIELD_EVENT_ACTION_DELETED:
            force_delete_in_completed_phases = message.action == FieldEventAction.FIELD_EVENT_ACTION_DELETED
            await delete_event_associations(
                request=request,
                owner_user_id=message.data.owner_id,
                field_md5=message.data.boundary_id,
                event_ids=[UUID(message.data.event_id)],
                force_delete_in_completed_phases=force_delete_in_completed_phases,
            )
        case _:
            logger.warning(
                f"Received FieldEventNotification with unhandled action {message.action}, skipping processing."
            )
            return


if __name__ == "__main__":
    settings = get_settings()
    asyncio.run(
        consume_from_kafka(
            topic=settings.FIELD_EVENT_NOTIFICATION_TOPIC,
            proto_value_class=FieldEventNotification,
            message_handler=process_message,
        )
    )
