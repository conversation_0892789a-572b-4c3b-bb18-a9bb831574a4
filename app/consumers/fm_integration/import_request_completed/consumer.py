import asyncio
from datetime import timezone

from fastapi import Request
from regrow_proto.fm_integration.import_request.v1.import_request_completed_pb2 import (
    ImportRequestCompleted,
)

from config import get_settings
from consumers.kafka_consumer import consume_from_kafka
from fields.db import get_field_by_md5_and_project_id
from fields.model import Fields
from logger import get_logger
from projects.db import get_owner_user_id_for_project
from projects.monitor.model import MonitorEvents  # noqa: F401
from ses_integration.reconcile_event_associations import (
    reconcile_event_associations_for_field_with_updated_md5,
)

# there is definitely a case for abstracting completion updates with some kind of
# BulkFieldEventsChanged events, but that might also be arbitrated by a Kafka consumer
# so might as well just handle it directly for now
from ui.projects.completion.tasks import (
    run_completion_updates_on_project_fields,
)

logger = get_logger(__name__)


async def process_message(
    request: Request,
    message: ImportRequestCompleted,
) -> None:
    try:
        logger.info(f"Import Request Completion received: {message.data.id}")

        project_id: str = message.context.project_id
        if not project_id:
            logger.warning("No project_id found in ImportRequestCompleted message, skipping processing.")
            return
        user_id: str = message.context.user_id
        if not user_id:
            user_id = await get_owner_user_id_for_project(request=request, project_id=project_id)

        field_ids = []

        for (
            field_md5
        ) in message.data.field_ids:  # field_ids are boundary ids provided by the FM integration for imports
            logger.info(f"Processing field with md5: {field_md5}")
            field: Fields = await get_field_by_md5_and_project_id(request=request, md5=field_md5, project_id=project_id)

            if not field:
                logger.warning(f"Field with md5 {field_md5} not found in project {project_id}, skipping processing.")
                continue

            if message.data.start_date:
                from_time = message.data.start_date.ToDatetime(
                    tzinfo=timezone.utc
                )  # start_date is an optional timestamp but only the date is respected in the original service
            else:
                from_time = (
                    field.created_at
                )  # if we don't have a start_time, start from the beginning of the field's creation

            logger.info(f"Processing field with md5: {field_md5} using from_time {from_time}")

            reconcile_event_associations_for_field_with_updated_md5.delay(
                request=None,
                field_id=field.id,
                from_time=from_time,
                fs_user_id=user_id,
                fs_impersonator_user_id=user_id,
            )
            field_ids.append(field.id)

        run_completion_updates_on_project_fields.delay(
            from_time=from_time,
            project_id=int(project_id),
            field_ids=field_ids,
            fs_user_id=user_id,
            fs_impersonator_user_id=user_id,
        )

    except Exception as exc:
        logger.exception(exc)


if __name__ == "__main__":
    settings = get_settings()
    asyncio.run(
        consume_from_kafka(
            topic=settings.IMPORT_REQUEST_COMPLETED_TOPIC,
            proto_value_class=ImportRequestCompleted,
            message_handler=process_message,
        )
    )
