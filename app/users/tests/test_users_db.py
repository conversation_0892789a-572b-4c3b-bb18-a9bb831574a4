from datetime import datetime
from time import sleep

from fastapi import Response, status

from programs.enums import ProgramType
from users.constants import MAX_RECENT_PROGRAMS
from users.db import create_user_recent_program, get_user_recent_programs
from users.schema import CreateUserRecentProgramRequest


async def test_get_user_recent_programs(mdl, app_request):
    user_1_id = (await mdl.Users()).id
    user_2_id = (await mdl.Users()).id
    app_request.state.fs_user_id = user_1_id

    program_1 = await mdl.Programs(name="Program 1", program_type=ProgramType.live)
    program_2 = await mdl.Programs(name="Program 2", program_type=ProgramType.demo)
    program_3 = await mdl.Programs(name="Program 3", program_type=ProgramType.live)
    program_4 = await mdl.Programs(name="Program 4", program_type=ProgramType.automation)
    program_5 = await mdl.Programs(name="Program 5", program_type=ProgramType.internal)

    project_1 = await mdl.Projects(program_id=program_1.id)
    project_2 = await mdl.Projects(program_id=program_2.id)

    recent_program_1 = await mdl.UserRecentPrograms(
        user_id=user_2_id, program_id=program_1.id, project_id=project_1.id, created_at=datetime(2021, 1, 1)
    )
    recent_program_2 = await mdl.UserRecentPrograms(
        user_id=user_1_id, program_id=program_2.id, project_id=project_2.id, created_at=datetime(2021, 2, 1)
    )
    recent_program_3 = await mdl.UserRecentPrograms(
        user_id=user_1_id, program_id=program_3.id, project_id=None, created_at=datetime(2021, 3, 1)
    )

    # Add invalid recent program types to ensure they are filtered out
    await mdl.UserRecentPrograms(
        user_id=user_1_id, program_id=program_4.id, project_id=None, created_at=datetime(2021, 4, 1)
    )
    await mdl.UserRecentPrograms(
        user_id=user_1_id, program_id=program_5.id, project_id=None, created_at=datetime(2021, 5, 1)
    )

    # Only returns the programs that are associated with the user
    result = await get_user_recent_programs(request=app_request)
    assert len(result) == 2
    assert {item.program_id for item in result} == {recent_program_2.program_id, recent_program_3.program_id}
    assert recent_program_1.id not in result

    # Returns the programs in descending order of created_at
    assert result[0].program_name == program_3.name
    assert result[0].program_id == program_3.id
    assert result[0].project_id is None
    assert result[1].program_name == program_2.name
    assert result[1].program_id == program_2.id
    assert result[1].project_id == project_2.id


async def test_create_user_recent_program(mdl, app_request):
    user_1_id = (await mdl.Users()).id
    user_2_id = (await mdl.Users()).id
    app_request.state.fs_user_id = user_1_id

    program_1 = await mdl.Programs(name="Program 1", program_type=ProgramType.live)
    project_1 = await mdl.Projects(program_id=program_1.id)
    program_2 = await mdl.Programs(name="Program 2", program_type=ProgramType.demo)
    project_2 = await mdl.Projects(program_id=program_2.id)

    # Create two recent programs
    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_1.id, project_id=project_1.id)
    )
    # Wait for 1 second to ensure the created_at time for the next creation is different
    sleep(1)

    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_2.id, project_id=project_2.id)
    )
    # Wait for 1 second to ensure the created_at time for the next creation is different
    sleep(1)

    assert len(results) == 2
    assert results[0].program_name == program_2.name
    assert results[0].program_id == program_2.id
    assert results[0].project_id == project_2.id
    assert results[1].program_name == program_1.name
    assert results[1].program_id == program_1.id
    assert results[1].project_id == project_1.id

    # Creates a recent program without a project id
    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_1.id)
    )
    # Wait for 1 second to ensure the created_at time for the next creation is different
    sleep(1)

    assert len(results) == 3
    assert results[0].program_name == program_1.name
    assert results[0].program_id == program_1.id
    assert results[0].project_id is None
    assert results[1].program_name == program_2.name
    assert results[1].program_id == program_2.id
    assert results[1].project_id == project_2.id
    assert results[2].program_name == program_1.name
    assert results[2].program_id == program_1.id
    assert results[2].project_id == project_1.id

    # Does not create a duplicate "recent program" entry from the same user with a project id
    # Instead, updates the created_at time if the same entry is created again
    # The sort order changes so that the most recent entry is at the top
    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_1.id, project_id=project_1.id)
    )
    # Wait for 1 second to ensure the created_at time for the next creation is different
    sleep(1)

    assert len(results) == 3
    assert results[0].program_name == program_1.name
    assert results[0].program_id == program_1.id
    assert results[0].project_id == project_1.id
    assert results[1].program_name == program_1.name
    assert results[1].program_id == program_1.id
    assert results[1].project_id is None
    assert results[2].program_name == program_2.name
    assert results[2].program_id == program_2.id
    assert results[2].project_id == project_2.id

    # Does not create a duplicate "recent program" entry from the same user without a project id
    # Instead, updates the created_at time if the same entry is created again
    # The sort order changes so that the most recent entry is at the top
    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_1.id)
    )

    assert len(results) == 3
    assert results[0].program_name == program_1.name
    assert results[0].program_id == program_1.id
    assert results[0].project_id is None
    assert results[1].program_name == program_1.name
    assert results[1].program_id == program_1.id
    assert results[1].project_id == project_1.id
    assert results[2].program_name == program_2.name
    assert results[2].program_id == program_2.id
    assert results[2].project_id == project_2.id

    # Allows the same entry to be created from a different user
    app_request.state.fs_user_id = user_2_id  # change the user_id to 2

    results = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=program_1.id, project_id=project_1.id)
    )
    assert isinstance(results, list)
    assert len(results) == 1
    assert results[0].program_name == program_1.name
    assert results[0].program_id == program_1.id
    assert results[0].project_id == project_1.id

    app_request.state.fs_user_id = user_1_id  # reset the user_id back to 1

    # Removes the oldest "recent programs" if the list exceeds the MAX_RECENT_PROGRAMS limit
    # Program 10 already exists in the recent programs so we can just add MAX_RECENT_PROGRAMS more
    for _ in range(MAX_RECENT_PROGRAMS + 1):
        program = await mdl.Programs(program_type=ProgramType.live)
        results = await create_user_recent_program(
            request=app_request,
            data=CreateUserRecentProgramRequest(program_id=program.id),
        )

    assert len(results) == MAX_RECENT_PROGRAMS


async def test_create_user_recent_program_invalid_program_types(mdl, app_request):
    """Test that invalid program types (internal, automation) cannot be added to recent programs."""
    user_id = (await mdl.Users()).id
    app_request.state.fs_user_id = user_id

    # Create programs with invalid types for recent programs
    internal_program = await mdl.Programs(name="Internal Program", program_type=ProgramType.internal)
    automation_program = await mdl.Programs(name="Automation Program", program_type=ProgramType.automation)

    # Create projects for these programs
    internal_project = await mdl.Projects(program_id=internal_program.id)
    automation_project = await mdl.Projects(program_id=automation_program.id)

    # Try to create recent program with internal program type - should return 400 error
    result = await create_user_recent_program(
        request=app_request,
        data=CreateUserRecentProgramRequest(program_id=internal_program.id, project_id=internal_project.id),
    )

    assert isinstance(result, Response)
    assert result.status_code == status.HTTP_400_BAD_REQUEST
    assert "Invalid program type 'internal'" in result.body.decode()
    assert "Only live, demo programs can be added to recent programs" in result.body.decode()

    # Try to create recent program with automation program type - should return 400 error
    result = await create_user_recent_program(
        request=app_request,
        data=CreateUserRecentProgramRequest(program_id=automation_program.id, project_id=automation_project.id),
    )

    assert isinstance(result, Response)
    assert result.status_code == status.HTTP_400_BAD_REQUEST
    assert "Invalid program type 'automation'" in result.body.decode()
    assert "Only live, demo programs can be added to recent programs" in result.body.decode()

    # Try to create recent program with internal program type without project - should return 400 error
    result = await create_user_recent_program(
        request=app_request, data=CreateUserRecentProgramRequest(program_id=internal_program.id)
    )

    assert isinstance(result, Response)
    assert result.status_code == status.HTTP_400_BAD_REQUEST
    assert "Invalid program type 'internal'" in result.body.decode()
    assert "Only live, demo programs can be added to recent programs" in result.body.decode()

    # Verify that no recent programs were actually created
    recent_programs = await get_user_recent_programs(request=app_request)
    assert len(recent_programs) == 0
