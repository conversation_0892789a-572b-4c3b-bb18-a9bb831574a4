from pydantic import BaseModel


class UserRecentProgram(BaseModel):
    id: int
    user_id: int
    program_id: int
    project_id: int | None = None

    class Config:
        orm_mode = True


class CreateUserRecentProgramRequest(BaseModel):
    program_id: int
    project_id: int | None = None


class UserRecentProgramItem(BaseModel):
    program_name: str
    program_id: int
    project_id: int | None = None
