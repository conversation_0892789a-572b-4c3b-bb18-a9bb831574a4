from typing import Union

import elasticapm
from fastapi import APIRouter, Request, Response, status
from fastapi.params import Depends

from permissions.enums import Permission
from permissions.resolver import Permissions
from users import db, paths
from users.schema import CreateUserRecentProgramRequest, UserRecentProgramItem

tags = ["user"]
router = APIRouter(prefix=paths.base)


@elasticapm.async_capture_span()
@router.get(
    paths.recent_programs,
    response_model=list[UserRecentProgramItem],
    tags=tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_PROGRAM]))],
)
async def get_user_recent_programs(request: Request) -> list[UserRecentProgramItem]:
    return await db.get_user_recent_programs(request=request)


@elasticapm.async_capture_span()
@router.post(
    paths.recent_programs,
    response_model=list[UserRecentProgramItem],
    tags=tags,
    status_code=status.HTTP_200_OK,
    responses={
        status.HTTP_204_NO_CONTENT: {"description": "Duplicate Entry - No need to update the recent programs list."}
    },
    dependencies=[Depends(Permissions([Permission.GET_PROGRAM]))],
)
async def create_user_recent_program(
    request: Request, data: CreateUserRecentProgramRequest
) -> Union[list[UserRecentProgramItem], Response]:
    return await db.create_user_recent_program(request=request, data=data)
