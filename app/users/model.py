from sqlalchemy import Column, DateTime, Grouping, Index, Integer, UniqueConstraint
from sqlalchemy.sql import func

from db.mysql import Base


class UserRecentPrograms(Base):
    __tablename__ = "mrv_user_recent_programs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    program_id = Column(Integer, nullable=False, index=True)
    project_id = Column(Integer, nullable=True, index=True)
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())

    __table_args__ = (
        # Conditional uniqueness constraint.
        # If there's no project_id, then the user_id and program_id must be unique.
        # Note: alembic is not smart enough to understand this, so we skip the auto-generation unless we're adding a migration.
        Index(
            "unique_user_id_program_id",
            user_id,
            program_id,
            Grouping(func.IF(project_id.is_(None), 1, None)),
            unique=True,
            info={"skip_autogenerate": True},
        ),
        UniqueConstraint(user_id, project_id, name="unique_user_id_project_id"),
    )
