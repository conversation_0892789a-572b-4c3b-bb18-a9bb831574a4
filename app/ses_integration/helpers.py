from regrow.ses.pbtype import area_pb2, depth_pb2, line_pb2, mass_pb2, volume_pb2


def get_ses_depth_measure(
    depth_value: float | None, depth_unit: line_pb2.LineUnit | None = None
) -> depth_pb2.DepthMeasure:
    return depth_pb2.DepthMeasure(value=depth_value, unit=depth_unit)


def get_ses_area_measure(
    area_unit: area_pb2.AreaUnit | None = None,
    area_value: float = 1.0,
) -> area_pb2.AreaMeasure:
    return area_pb2.AreaMeasure(value=area_value, unit=area_unit)


def get_ses_volume_measure(volume_value: float | None, volume_unit: str | None = None) -> volume_pb2.VolumeMeasure:
    return volume_pb2.VolumeMeasure(value=volume_value, unit=volume_unit)


def get_ses_volume_area_measure(
    volume_value: float | None,
    volume_unit: str | None = None,
    area_unit: area_pb2.AreaUnit | None = None,
    area_value: float = 1.0,
) -> volume_pb2.VolumeAreaMeasure:
    area = get_ses_area_measure(area_value=area_value, area_unit=area_unit)
    volume = get_ses_volume_measure(volume_value=volume_value, volume_unit=volume_unit)
    return volume_pb2.VolumeAreaMeasure(volume=volume, area=area)


def get_ses_mass_measure(mass_value: float | None, mass_unit: str | None = None) -> mass_pb2.MassMeasure:
    return mass_pb2.MassMeasure(value=mass_value, unit=mass_unit)


def get_ses_mass_area_measure(
    mass_value: float | None,
    mass_unit: str | None = None,
    area_unit: area_pb2.AreaUnit | None = None,
    area_value: float = 1.0,
) -> mass_pb2.MassAreaMeasure:
    area = get_ses_area_measure(area_value=area_value, area_unit=area_unit)
    mass = get_ses_mass_measure(mass_value=mass_value, mass_unit=mass_unit)
    return mass_pb2.MassAreaMeasure(mass=mass, area=area)
