import asyncio
from datetime import datetime, UTC
from typing import Dict, List, Optional, Tuple, Type

import elasticapm
from fastapi import HTTPException
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from ses_client.client import Client
from ses_client.crop import crop_purpose_commodity_harvest
from ses_client.event import event_type, EventWithContext, StructuredEvent
from ses_client.search import Filter
from starlette import status

from config import get_settings
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from fields.schema import Field
from logger import get_logger
from phases.enums import StageTypes
from phases.model import Phases
from phases.schema import StageRequestNoParentBase
from ses_integration.constants import (
    MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
    SES_EVENT_TO_ENTITY_EVENT,
    STAGE_TYPE_TO_SES_EVENT,
)

logger = get_logger(__name__)

settings = get_settings()


def get_ses_event_types_to_query(
    stages: list[StageRequestNoParentBase], stage_type: StageTypes | None = None
) -> list[str]:
    ses_event_types_to_query = []
    if stage_type:
        event_types = STAGE_TYPE_TO_SES_EVENT.get(stage_type)
        if event_types:
            ses_event_types_to_query.extend(event_types)
    else:
        for stage in stages:
            event_types = STAGE_TYPE_TO_SES_EVENT.get(stage.type_)
            if event_types:
                ses_event_types_to_query.extend(event_types)
    return ses_event_types_to_query


def parse_ses_events_to_entity_events(
    field: Field,
    cropping_events: List[Tuple[EventWithContext | None, EventWithContext | None, EventWithContext | None]] | None,
    field_practice_events: List[EventWithContext] | None,
) -> List[EntityEvent]:
    entity_events = []
    if cropping_events is not None:
        for field_crop_events in cropping_events:
            entity_event = CroppingEvent.from_ses_events(events=field_crop_events, entity_id=field.id)
            entity_events.append(entity_event)

    if field_practice_events is not None:
        for ses_event in field_practice_events:
            ses_type = event_type(ses_event.event)
            entity_event_type: Type[EntityEvent] = SES_EVENT_TO_ENTITY_EVENT[ses_type]
            if ses_type is not None:
                entity_event = entity_event_type.from_ses_events(events=[ses_event], entity_id=field.id)
                entity_events.append(entity_event)
            else:
                logger.error(f"Unsupported SES event type: {ses_type}")

    return entity_events


@elasticapm.async_capture_span()
async def upsert_events_to_ses(
    field: Field, event: Optional[EntityEvent], new_events: list[StructuredEvent], ses_client: Client
) -> list[UpsertEventResponse]:

    logger.debug(f"upsert_events_to_ses {field.md5=}, {new_events=}")

    created_events: list[StructuredEvent] = []

    for event_to_upsert in new_events:

        try:
            if event is None or event.id is None:
                assert event_to_upsert.pb_context.creation.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID) is not None
                assert event_to_upsert.pb_context.association.get(CONTEXT_KEY_REGROW_OWNING_USER) is not None
                assert event_to_upsert.pb_context.association.get(CONTEXT_KEY_REGROW_ACTING_USER) is not None
            created_event = await ses_client.upsert_field_event(field_id=field.md5, structured_event=event_to_upsert)
        except Exception as e:
            logger.error(f"Error upserting event for field: {field.md5} - {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Upserting SES event for field {field.md5} returned error: {e}",
            )
        if created_event is not None:
            created_events.append(created_event)

    return created_events


async def get_cropping_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    stage_type: StageTypes | None = None,
) -> Dict[str, List[Tuple[EventWithContext, EventWithContext]]] | None:
    crop_purpose_filter = None
    # Only query for all crop types if stage type is not CROP_EVENTS, otherwise only fetch Commodity crop events
    if stage_type is not None and stage_type is not StageTypes.CROP_EVENTS:
        crop_purpose_filter = [crop_purpose_commodity_harvest()]
    return await ses_client.fetch_cropping_sequences_for_fields_with_context(
        field_ids=field_md5s,
        to_date=to_date,
        from_date=from_date,
        user_ids=[owner_user_id],
        min_overlap_percentage=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
        crop_purpose=crop_purpose_filter,
    )


async def get_field_practice_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    phase: Phases,
    stage_type: StageTypes | None = None,
) -> Dict[str, List[EventWithContext]] | None:
    ses_event_types_to_query: list[str] = [StructuredEvent.TYPE_FALLOW_PERIOD]
    event_types_for_stage = get_ses_event_types_to_query(stages=phase.stages, stage_type=stage_type)
    ses_event_types_to_query.extend(event_types_for_stage)

    if len(ses_event_types_to_query) == 0:
        return None

    return await ses_client.fetch_events_for_fields_with_context(
        field_ids=field_md5s,
        search_filter=Filter(
            event_types=ses_event_types_to_query,
            interval_from=from_date,
            interval_to=to_date,
            user_ids=[owner_user_id],
            overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
        ),
    )


async def get_ses_field_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    phase: Phases,
    stage_type: StageTypes | None = None,
) -> Tuple[Dict[str, List[Tuple[EventWithContext, EventWithContext]]] | None, Dict[str, List[EventWithContext]] | None]:
    ses_crop_events = await get_cropping_events(
        ses_client=ses_client,
        field_md5s=field_md5s,
        from_date=from_date,
        to_date=to_date,
        owner_user_id=owner_user_id,
        stage_type=stage_type,
    )
    ses_events = await get_field_practice_events(
        ses_client=ses_client,
        field_md5s=field_md5s,
        from_date=from_date,
        to_date=to_date,
        owner_user_id=owner_user_id,
        phase=phase,
        stage_type=stage_type,
    )

    return ses_crop_events, ses_events


async def poll_for_field_change(from_time: datetime, field_md5s: list[str]) -> None:
    ses_client = Client(
        grpc_host_addr=settings.SES_INTERNAL_URL_BASE, search_host_addr=settings.SES_SEARCH_INTERNAL_URL_BASE
    )
    updated_md5: set[str] = set()
    waiting_for_updates = True
    while waiting_for_updates:
        waiting_for_updates = False
        for field_md5 in field_md5s:
            if field_md5 in updated_md5:
                continue
            md5_updated_at = await ses_client.get_last_update_for_field(field_md5)
            if md5_updated_at > from_time:
                updated_md5.add(field_md5)
            else:
                waiting_for_updates = True
                break

        if waiting_for_updates:
            if (
                datetime.now(tz=UTC) - from_time
            ).seconds >= settings.SES_RECONCILIATION_POLLING_INTERVAL_TIMEOUT_SECONDS:
                waiting_for_updates = False
                break

        await asyncio.sleep(settings.SES_RECONCILIATION_POLLING_INTERVAL_SECONDS)
