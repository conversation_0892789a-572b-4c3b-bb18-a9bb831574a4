from typing import Optional

import elasticapm
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel
from starlette import status
from starlette.requests import Request

from config import get_settings
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from ses_integration.migration import migrate_mrv_values_to_ses

router = APIRouter(prefix="")

settings = get_settings()
logger = get_logger(__name__)


class SESMigrationRequest(BaseModel):
    project_ids: Optional[list[int]] = Query(None)
    field_ids: Optional[list[int]] = Query(None)
    generate_fallow_dates: bool = Query(True)


@elasticapm.async_capture_span
@router.post(
    "/programs/{program_id}/ses-migration",
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))],
)
async def migrate_program_to_ses(request: Request, program_id: int, body: SESMigrationRequest) -> None:
    return await migrate_mrv_values_to_ses.migrate_program_data_to_ses(
        request=request,
        program_id=program_id,
        project_ids=body.project_ids,
        field_ids=body.field_ids,
        generate_fallow_dates=body.generate_fallow_dates,
    )
