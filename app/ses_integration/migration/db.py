import uuid

from fastapi import Request
from sqlalchemy import update

from root_crud import create
from ses_integration.migration.model import SESFieldMigrationStatus, SESMigrationResult
from ses_integration.migration.schema import SESMigrationSpecification


async def _set_migration_result_status(
    request: Request, task_id: uuid.UUID, field_id: int, status: SESFieldMigrationStatus
) -> None:
    async with request.state.sql_session.begin() as session:
        await session.execute(
            update(SESMigrationResult)
            .values(status=status)
            .where(SESMigrationResult.task_id == str(task_id))
            .where(SESMigrationResult.field_id == field_id)
        )


async def _initialize_migration_field_status_as_enqueued(
    request: Request, field_and_project_ids: list[tuple[int, int]], spec: SESMigrationSpecification
) -> None:
    # Create.create sends inserts in serial, so bulk insert is a potential optimization
    await create.create(
        request=request,
        orm_type=SESMigrationResult,
        instances=[
            SESMigrationResult(
                task_id=str(spec.task_id),
                field_id=field_id,
                project_id=project_id,
                program_id=spec.program_id,
                status=SESFieldMigrationStatus.enqueued,
            )
            for field_id, project_id in field_and_project_ids
        ],
        translate=False,
        no_return=True,
    )
