import uuid
from typing import Optional

from pydantic import BaseModel
from ses_client.event import StructuredEvent

from programs.enums import UnitsTypes


class SESMigrationSpecification(BaseModel):
    owner_user_id: str | None  # This ID preserves the original id of the user associated with the program
    acting_user_id: str
    program_id: int
    program_measurement_system: UnitsTypes
    task_id: uuid.UUID
    e_phase_id: Optional[int]
    m_phase_id: Optional[int]
    generate_fallow_dates: bool


class MigratoryEvent(BaseModel):
    event: StructuredEvent
    phase_id: int
    field_id: int
    project_id: int
    field_md5: str

    class Config:
        arbitrary_types_allowed = True
