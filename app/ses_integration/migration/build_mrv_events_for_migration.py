import logging
from datetime import datetime, timedelta, timezone
from typing import Any

import pytz
from pytz import BaseTzInfo
from shapely.geometry import shape
from starlette.requests import Request
from timezonefinder import TimezoneFinder

from defaults import defaults_translator
from defaults.attribute_options import CropUsage, NO_ADDITIVES_OPTION
from defaults.consts import COVER_CROP_USAGES
from entity_events import methods as entity_events_methods
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.constants import (
    AUTO_ASSIGN_FALLOW_END_DAY,
    AUTO_ASSIGN_FALLOW_END_MONTH,
    AUTO_ASSIGN_FALLOW_START_DAY,
    AUTO_ASSIGN_FALLOW_START_MONTH,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.tillage_event import Tillage<PERSON>vent
from helper import datetime_helper, list_helper
from phases.enums import PhaseTypes
from ses_integration.migration.constants import (
    EARLIEST_ADJUSTED_FALLOW_END_DAY,
    EARLIEST_ADJUSTED_FALLOW_END_MONTH,
)
from ses_integration.migration.schema import SESMigrationSpecification
from values.enums import EntityTypeChoices

logger = logging.getLogger(__name__)


# entrypoint
async def get_mrv_events(
    request: Request,
    project_id: int,
    field_ids: list[int],
    specification: SESMigrationSpecification,
    field_boundaries: dict[int, dict[str, Any]],
    generate_fallow_dates: bool,
) -> dict[int, dict[PhaseTypes, list[EntityEvent]]]:
    """
    Use the Structured Events Facade to generate EntityEvents from MRV Values.
    * Uses the auto_assign_fallow_dates option to set empty FallowPeriod dates to 1/1/record_year - 12/31/record_year
    * Adjusts FallowPeriod dates based on other management events
    * Converts crop and product names to regrow_name
    """
    phase_types = []
    if specification.e_phase_id:
        phase_types.append(PhaseTypes.ENROLMENT)
    if specification.m_phase_id:
        phase_types.append(PhaseTypes.MONITORING)
    events = await entity_events_methods.get_entity_events_for_project_phases(
        request=request,
        project_id=project_id,
        phase_types=phase_types,
        entity_type=EntityTypeChoices.field,
        event_creation_specification=EventCreationSpecification(auto_assign_fallow_dates=generate_fallow_dates),
    )
    # Intended events (from Intended Commodity Crops) should not be migrated to SES
    e_events = [ev for ev in events.get(PhaseTypes.ENROLMENT, []) if not ev.is_intended and ev.entity_id in field_ids]
    m_events = [ev for ev in events.get(PhaseTypes.MONITORING, []) if ev.entity_id in field_ids]
    await _convert_core_crop_names_to_regrow_names([ev for ev in e_events + m_events if isinstance(ev, CroppingEvent)])
    await _convert_core_product_names_to_regrow_names(
        [ev for ev in e_events + m_events if isinstance(ev, ApplicationEvent)]
    )
    await _repair_dates_using_field_boundary_timezones(field_boundaries, e_events + m_events)
    if generate_fallow_dates:
        e_events, m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    ev_by_field_phase: dict[int, dict[PhaseTypes, list[EntityEvent]]] = {}
    for ev in e_events:
        ev_by_field_phase.setdefault(ev.entity_id, {}).setdefault(PhaseTypes.ENROLMENT, []).append(ev)
    for ev in m_events:
        ev_by_field_phase.setdefault(ev.entity_id, {}).setdefault(PhaseTypes.MONITORING, []).append(ev)
    return ev_by_field_phase


# internal functions
def _adjust_generated_fallow_overlaps_with_events(
    e_events: list[EntityEvent], m_events: list[EntityEvent]
) -> tuple[list[EntityEvent], list[EntityEvent]]:
    """Adjust FallowPeriods to remove any overlaps with CroppingEvents, TillageEvents, or ApplicationEvents. We're a bit
    more eager to eliminate overlaps with commodity crops, since those will cause difficulty deriving cultivation
    cycles. For cover crops and tillage events we're just concerned about the fallow cycle swallowing up events that
    should really belong to the subsequent cycle, so we adjust those more precisely.

    We only adjust fallow periods if they appear to have the dates the event creator auto-assigns.
    """
    e_events_by_field = list_helper.group_by(e_events, key=lambda ev: ev.entity_id)
    m_events_by_field = list_helper.group_by(m_events, key=lambda ev: ev.entity_id)

    phase_events_to_return: list[list[EntityEvent]] = []
    for events_by_field in (e_events_by_field, m_events_by_field):
        phase_events = []
        for field_id, events in events_by_field.items():
            all_field_events = e_events_by_field.get(field_id, []) + m_events_by_field.get(field_id, [])
            field_com_crop_events = [
                ev
                for ev in all_field_events
                if isinstance(ev, CroppingEvent) and ev.interval.end and ev.crop_usage == CropUsage.COMMODITY
            ]
            field_cover_crop_events = [
                ev
                for ev in all_field_events
                if isinstance(ev, CroppingEvent) and ev.interval.end and ev.crop_usage in COVER_CROP_USAGES
            ]
            field_till_application_events = [
                ev for ev in all_field_events if isinstance(ev, (TillageEvent, ApplicationEvent))
            ]
            for ev in events:
                if not isinstance(ev, FallowPeriod):
                    phase_events.append(ev)
                else:
                    fallow = ev
                    remove_fallow = False
                    if _fallow_appears_autogenerated(ev):  # If the event is user-defined, we'd rather leave it as-is
                        fallow, remove_fallow = _adjust_fallow_dates_for_commodity_crop_overlaps(
                            field_com_crop_events, fallow
                        )
                        fallow = adjust_fallow_end_dates_for_cover_crop_overlaps(field_cover_crop_events, fallow)
                        fallow = adjust_fallow_end_dates_for_till_application_overlaps(
                            field_till_application_events, fallow
                        )

                    if not remove_fallow:
                        phase_events.append(fallow)
        phase_events_to_return.append(phase_events)
    return phase_events_to_return[0], phase_events_to_return[1]


def _fallow_appears_autogenerated(fallow: FallowPeriod) -> bool:
    # base_cropping_event_creator uses these dates to create fallows when requested and no dates are defined
    return (
        fallow.interval
        and fallow.interval.start
        and fallow.interval.end
        and fallow.interval.start.month == AUTO_ASSIGN_FALLOW_START_MONTH
        and fallow.interval.start.day == AUTO_ASSIGN_FALLOW_START_DAY
        and fallow.interval.end.month == AUTO_ASSIGN_FALLOW_END_MONTH
        and fallow.interval.end.day == AUTO_ASSIGN_FALLOW_END_DAY
    )


def _adjust_fallow_dates_for_commodity_crop_overlaps(
    com_crop_events: list[CroppingEvent], fallow: FallowPeriod
) -> tuple[FallowPeriod, bool]:
    remove_fallow = False
    for com_crop in com_crop_events:
        # Fallow wholly contained in crop, or crop wholly contained in fallow
        if (
            com_crop.interval.start < fallow.interval.start
            and com_crop.interval.end > fallow.interval.end
            or fallow.interval.start < com_crop.interval.start
            and fallow.interval.end > com_crop.interval.end
        ):
            remove_fallow = True
        # Fallow start overlap
        day_after_crop_end = com_crop.interval.end + timedelta(days=1)
        if com_crop.interval.start < fallow.interval.start < day_after_crop_end:
            # If they're on the same day
            fallow.interval.start = max(fallow.interval.start, day_after_crop_end)
        # Fallow end overlap
        day_before_crop_start = com_crop.interval.start - timedelta(days=1)
        if day_before_crop_start < fallow.interval.end < com_crop.interval.end:
            fallow.interval.end = min(fallow.interval.end, day_before_crop_start)
    return fallow, remove_fallow


def adjust_fallow_end_dates_for_cover_crop_overlaps(
    cover_crop_events: list[CroppingEvent], fallow: FallowPeriod
) -> FallowPeriod:
    """
    In the case of cover crop overlaps, we are primarily concerned with the cover crop after the fallow inadvertently
    being included in the fallow cultivation cycle. We avoid this by eliminating any overlap with a cover crop
    planted in the second half of the year. The preference here is to alter the fallow conservatively rather than
    handle all cases.

    We assume the fallow is autogenerated by the base_create_cropping_event, though it may have been overlap-adjusted
    before this point.
    """
    for cov_crop in cover_crop_events:
        cov_start = cov_crop.interval.start
        fallow_start = fallow.interval.start
        fallow_end = fallow.interval.end
        earliest_crop_start_considered = datetime(
            year=fallow.interval.end.year,
            month=EARLIEST_ADJUSTED_FALLOW_END_MONTH,
            day=EARLIEST_ADJUSTED_FALLOW_END_DAY,
            tzinfo=timezone.utc,
        )
        if fallow_start < cov_start <= fallow_end and cov_start >= earliest_crop_start_considered:
            fallow.interval.end = cov_crop.interval.start - timedelta(days=1)
    return fallow


def adjust_fallow_end_dates_for_till_application_overlaps(
    till_application_events: list[TillageEvent | ApplicationEvent], fallow: FallowPeriod
) -> FallowPeriod:
    """For till and application overlaps, like for cover crop overlaps, we're only concerned with the second half of
    the year to avoid the next crop's events being included in the fallow cycle. We avoid overlapping events in the
    second half of the year. The preference here is to alter the fallow conservatively rather than handle all cases.

    We assume the fallow is autogenerated by the base_create_cropping_event, though it may have been overlap-adjusted
    before this point.
    """
    for event in till_application_events:
        event_date = event.occurred_at
        fallow_start = fallow.interval.start
        fallow_end = fallow.interval.end
        earliest_till_date_considered = datetime(
            year=fallow.interval.end.year,
            month=EARLIEST_ADJUSTED_FALLOW_END_MONTH,
            day=EARLIEST_ADJUSTED_FALLOW_END_DAY,
            tzinfo=timezone.utc,
        )
        if fallow_start < event_date < fallow_end and event_date >= earliest_till_date_considered:
            fallow.interval.end = event_date - timedelta(days=1)
    return fallow


async def _convert_core_crop_names_to_regrow_names(crop_events: list[CroppingEvent]) -> None:
    for crop_ev in crop_events:
        if crop_ev.crop_type:
            # These transformations have been used to quantify unsupported crops. Now is a good time to apply the crop
            # mappings to the crop events themselves.
            if crop_ev.crop_type == "beans":
                crop_ev.crop_type = "dry_bean"
            elif crop_ev.crop_type == "full_cover":
                crop_ev.crop_type = "basic_cover_crop"
            elif crop_ev.crop_type == "grass_pasture":
                crop_ev.crop_type = "pasture"

            else:
                crop_ev.crop_type = await defaults_translator.translate_core_crop_name_to_regrow_name(crop_ev.crop_type)


async def _convert_core_product_names_to_regrow_names(app_events: list[ApplicationEvent]) -> None:
    for app_ev in app_events:
        for prod in app_ev.products or []:
            prod.product_name = (
                await defaults_translator.translate_core_product_type_to_regrow_name(prod.product_name)
                if prod.product_name != NO_ADDITIVES_OPTION
                else NO_ADDITIVES_OPTION
            )
        if app_ev.additives:
            # We need to consolidate additives into one source of truth, currently in .products and .additives
            app_ev.additives = ",".join(
                [
                    (
                        await defaults_translator.translate_core_product_type_to_regrow_name(add)
                        if add != NO_ADDITIVES_OPTION
                        else NO_ADDITIVES_OPTION
                    )
                    for add in app_ev.additives.split(",")
                ]
            )


async def _localize_and_repair_event_timestamps(event_datetime: datetime, field_timezone: BaseTzInfo) -> datetime:
    """
    Interprets midnight and noon timestamps as local dates, even if the stored date string was
    UTC ("Z"). This is the best we can do since setting (or unsetting) tzinfo based on mrv values date string could
    break other workflows. Alternatively, storing separate local dates on EntityEvent could work but only if we want to
    fully adopt that schema change.

    We round to local midnights because that's what the legacy UI attempted to save, though it used the browser timezone
    instead of the field location timezone, which will in some cases have been wrong. This is not guaranteed to fix all
    dates, but should produce mostly correct, fully consistent data.
    """
    # Noons and midnights to be taken at face value. The noons appear to be defaults from prior migrations.
    if event_datetime.hour in {12, 0} and event_datetime.minute == 0 and event_datetime.second == 0:
        event_date = event_datetime.replace(tzinfo=None)
        noon = datetime.min.replace(hour=12).time()
        return datetime.combine(event_date, noon).replace(tzinfo=timezone.utc)
    else:
        event_datetime = event_datetime.replace(tzinfo=None)
        # First shift the stored datetime to local time
        event_local_datetime = datetime_helper.convert_to_local_datetime(event_datetime, to_tz=field_timezone)
        event_rounded = datetime_helper.round_datetime_to_midnight(event_local_datetime)
        # Set to noon of that day since that's what SES wants
        return event_rounded.replace(hour=12).replace(tzinfo=timezone.utc)


async def _repair_dates_using_field_boundary_timezones(
    field_boundaries: dict[int, dict[str, Any]], events: list[EntityEvent]
) -> None:
    """
    field_boundaries: Dict of the GeoJSON field boundary string to get the timezone for, by field_id
    events: List of MRV events whose dates will be normalized to their field's timezone

    Returns events with dates aligned to noons (12:00) in the location's timezone.
    """
    events_by_field: dict[int, list[EntityEvent]] = list_helper.group_by(events, key=lambda evn: evn.entity_id)
    for field_id, events in events_by_field.items():
        boundary_tz = await _get_timezone_for_field_boundary_centroid(field_boundaries[field_id])
        # Set noon times for all local dates
        for ev in events:
            if hasattr(ev, "interval") and ev.interval and ev.interval.start:
                ev.interval.start = await _localize_and_repair_event_timestamps(
                    event_datetime=ev.interval.start, field_timezone=boundary_tz
                )
            if hasattr(ev, "interval") and ev.interval and ev.interval.end:
                ev.interval.end = await _localize_and_repair_event_timestamps(
                    event_datetime=ev.interval.end, field_timezone=boundary_tz
                )
            if hasattr(ev, "occurred_at") and ev.occurred_at:
                ev.occurred_at = await _localize_and_repair_event_timestamps(
                    event_datetime=ev.occurred_at, field_timezone=boundary_tz
                )


async def _get_timezone_for_field_boundary_centroid(field_boundary: dict) -> BaseTzInfo:
    geometry = shape(field_boundary)

    centroid = geometry.centroid
    lat, lon = centroid.y, centroid.x

    tf = TimezoneFinder()
    centroid_timezone = tf.certain_timezone_at(lng=lon, lat=lat)
    return pytz.timezone(centroid_timezone)
