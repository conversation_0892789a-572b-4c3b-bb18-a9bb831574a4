import json
import uuid
from collections import defaultdict
from datetime import datetime
from typing import Any, Generator

import backoff
import elasticapm
from fastapi import Request
from google.protobuf.json_format import MessageToJson
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from ses_client.client import Client
from ses_client.event import event_type, StructuredEvent
from ses_client.fallow import FallowPeriod as SESFallowPeriod
from ses_client.harvest import HarvestActivity

from boundaries_service import client as boundaries_client
from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from cultivation_cycles import methods as cultivation_cycles_methods
from cultivation_cycles.schema import CultivationCycleGenerationConfig
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.units import LengthUnit
from entity_events.validation.constants import STAGE_TYPE_TO_ENTITY_EVENT_CLASS_MAPPING
from fields import db as fields_db
from fields.enums import Field<PERSON>tatus
from fields.model import Fields
from fields.schema import Field
from helper import async_tools, list_helper
from logger import get_logger
from phases import methods as phases_methods
from phases.enums import PhaseTypes
from pint_configuration import ureg
from programs.db import get_program
from programs.enums import UnitsTypes
from programs.model import Programs
from projects.db import get_owner_user_id_for_project
from root_crud import get
from ses_integration import event_associations
from ses_integration.constants import (
    NO_PRACTICE_OBSERVATION_EVENT_TYPES,
    NO_PRACTICE_OBSERVATION_STAGE_TYPES,
)
from ses_integration.migration.build_mrv_events_for_migration import (
    get_mrv_events,
)
from ses_integration.migration.constants import NPO_STAGES_BY_PROGRAM
from ses_integration.migration.db import (
    _initialize_migration_field_status_as_enqueued,
    _set_migration_result_status,
)
from ses_integration.migration.model import SESFieldMigrationStatus
from ses_integration.migration.schema import MigratoryEvent, SESMigrationSpecification
from ses_integration.schema import EventRevision

Quantity = ureg.Quantity
settings = get_settings()
ses_client = Client(
    grpc_host_addr=settings.SES_INTERNAL_URL_BASE, search_host_addr=settings.SES_SEARCH_INTERNAL_URL_BASE
)
logger = get_logger(__name__)


# Module entrypoint
async def migrate_program_data_to_ses(
    request: Request, program_id: int, project_ids: list[int], field_ids: list[int] | None, generate_fallow_dates: bool
) -> None:
    """
    Migrate MRV Values data to SES for a given program and sets of project_ids and field_ids.

    Params:
        program_id: The program to migrate.
        project_ids: Project IDs to migrate. If None, all projects in program will be migrated.
        field_ids: Field IDs to migrate within the specified project_ids. If None, all project fields will be migrated.
        generate_fallow_dates: Whether to generate fallow dates when not present in MRV Values.
    """
    task_id: uuid.UUID = uuid.uuid4()
    spec = await _derive_migration_specification(
        request=request, program_id=program_id, task_id=task_id, generate_fallow_dates=generate_fallow_dates
    )
    for field_and_project_id_batch in await _fetch_field_and_project_id_batches(
        request=request,
        program_id=program_id,
        project_ids=project_ids,
        field_ids=field_ids,
    ):
        await _initialize_migration_field_status_as_enqueued(
            request=request, field_and_project_ids=field_and_project_id_batch, spec=spec
        )
        _migrate_field_batch_events_to_ses.delay(
            field_ids=[tup[0] for tup in field_and_project_id_batch],
            specification=spec,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )


# Internal functions


async def _fetch_field_and_project_id_batches(
    request: Request, program_id: int, project_ids: list[int], field_ids: list[int] | None
) -> list[list[tuple[int, int]]]:
    fields = await fields_db.get_fields_by_program(request=request, program_id=program_id)
    fields = [
        fld
        for fld in fields
        if fld.status == FieldStatus.enrolled
        and (project_ids is None or fld.parent_project_id in project_ids)
        and (field_ids is None or fld.id in field_ids)
    ]
    # Sort by project_id to reduce distributing projects' fields across different batches/tasks
    fields = sorted(fields, key=lambda fld: fld.parent_project_id)
    return list(
        async_tools.batch_list(
            [(fld.id, fld.parent_project_id) for fld in fields], batch_size=settings.SES_MIGRATION_FIELD_BATCH_SIZE
        )
    )


async def _derive_migration_specification(
    request: Request, program_id: int, task_id: uuid.UUID, generate_fallow_dates: bool
) -> SESMigrationSpecification:
    e_phase, m_phase = await phases_methods.get_e_and_m_phase_by_program_id(request=request, program_id=program_id)
    program: Programs = await get_program(request=request, program_id=program_id)
    return SESMigrationSpecification(
        task_id=task_id,
        program_id=program_id,
        acting_user_id=f"migration-{program_id}",
        owner_user_id=None,
        e_phase_id=e_phase.id if e_phase else None,
        m_phase_id=m_phase.id if m_phase else None,
        program_measurement_system=program.units,
        generate_fallow_dates=generate_fallow_dates,
    )


def _derive_no_practice_observations(
    mrv_events_lookup: dict[int, dict[PhaseTypes, list[EntityEvent]]], program_id: int
) -> dict[int, dict[datetime, dict[str, bool]]]:
    """
    Derive Cultivation Cycles and use them to determine the No Practice Observations to attach to their defining
    FallowPeriod or HarvestActivity. Because we're not generating "empty" Cultivation Cycles, we can always assume
    a cycle will have exactly one fallow or harvest. This event will always get an NPO context attached (unless it
    doesn't have an end date as in 1119's M1, which is handled later).

    Unfortunately EntityEvents don't have unique IDs at this point in the process, so key the NPOs on the field_id and
    datetime when the cultivation cycle ends.

    Returns a dict of field_id -> cycle-ending datetime -> event type -> no practice boolean
    """
    if program_id not in NPO_STAGES_BY_PROGRAM:
        raise ValueError("Program must be added to NPO_STAGES_BY_PROGRAM.")
    npo_lookup: dict[int, dict[datetime, dict[str, bool]]] = defaultdict(lambda: defaultdict(lambda: {}))
    for field_id, phase_events in mrv_events_lookup.items():
        try:
            mrv_events = list_helper.flatten_list(list(phase_events.values()))
            ccs, _ = cultivation_cycles_methods.get_cultivation_cycles(
                mrv_events, CultivationCycleGenerationConfig(autogenerate_gap_cycles=False)
            )
            for cc in ccs:
                for npo_stage_type in NO_PRACTICE_OBSERVATION_STAGE_TYPES:
                    if npo_stage_type in NPO_STAGES_BY_PROGRAM.get(program_id):
                        entity_event_type = STAGE_TYPE_TO_ENTITY_EVENT_CLASS_MAPPING[npo_stage_type]
                        npo_lookup[field_id][cc.end.date()][entity_event_type.__name__] = not any(
                            ev for ev in cc.events if isinstance(ev, entity_event_type)
                        )
        except Exception as exc:
            logger.error(
                f"SES Migration: Field {field_id} failed to generate cultivation cycles for No Practice "
                f"Observations.",
                exc_info=exc,
                extra={"phase_events": [ev.json() for ev_list in phase_events.values() for ev in ev_list]},
            )
            raise exc
    return npo_lookup


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def _migrate_field_batch_events_to_ses(
    self: DBTask,
    *,
    field_ids: list[int],
    specification: SESMigrationSpecification,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    fields = await get.get(request=request, orm_type=Fields, id_field=Fields.id, ids=field_ids)
    fields_by_project = list_helper.group_by(fields, lambda fld: fld.parent_project_id)
    for project_id, project_fields in fields_by_project.items():
        specification.owner_user_id = await get_owner_user_id_for_project(request=request, project_id=project_id)
        assert specification.owner_user_id != "None"
        try:
            field_boundaries: dict[int, dict[str, Any]] = await boundaries_client.get_geometries_for_fields(
                fields=[Field.from_orm(fld) for fld in project_fields]
            )
            mrv_events: dict[int, dict[PhaseTypes, list[EntityEvent]]] = await get_mrv_events(
                request,
                project_id,
                field_ids=[fld.id for fld in project_fields],
                specification=specification,
                field_boundaries=field_boundaries,
                generate_fallow_dates=specification.generate_fallow_dates,
            )
            migratory_events = await _prepare_migratory_ses_events(
                request=request,
                fields=project_fields,
                mrv_events_lookup=mrv_events,
                specification=specification,
                field_boundaries=field_boundaries,
            )
            await _upsert_field_events_to_ses(
                request=request,
                migratory_events=migratory_events,
                specification=specification,
            )
        except Exception as exc:
            # Because we first prepare all the events for a project, then send them. We choose not to try to migrate any
            # of the fields for a given project if any of the fields failed during event preparation.
            logger.error(f"SES Migration: Failure while preparing project {project_id} for migration.", exc_info=exc)
            for field in project_fields:
                await _set_migration_result_status(
                    request=request,
                    task_id=specification.task_id,
                    field_id=field.id,
                    status=SESFieldMigrationStatus.failed,
                )


def _apply_measurement_system_to_default_depth_units(
    measurement_system: UnitsTypes, mrv_events: list[EntityEvent]
) -> None:
    # We only care about correcting Imperial programs because Metric is the default MRV Values storage unit
    if measurement_system == UnitsTypes.US_IMPERIAL:
        for ev in mrv_events:
            if isinstance(ev, (TillageEvent, ApplicationEvent)):
                if ev.depth and ev.depth.value and ev.depth.unit and ev.depth.unit == LengthUnit.CENTIMETRE:
                    converted_value = Quantity(ev.depth.value, "centimeter").to("inch").magnitude
                    # MRV Values would convert depths to centimeters but only store one digit right of the decimal,
                    # so converting back to inches results in some error. We round to tenths and apply a bit of
                    # additional pressure towards a whole number (the common case) to mitigate this.
                    if abs(converted_value - round(converted_value, 0)) < 0.1:
                        ev.depth.value = round(converted_value, 0)
                    else:
                        ev.depth.value = round(converted_value, 1)
                    ev.depth.unit = LengthUnit.INCH


async def _prepare_migratory_ses_events(
    request: Request,
    fields: list[Field],
    mrv_events_lookup: dict[int, dict[PhaseTypes, list[EntityEvent]]],
    specification: SESMigrationSpecification,
    field_boundaries: dict[int, dict[str, Any]],
) -> list[MigratoryEvent]:
    migratory_events: list[MigratoryEvent] = []
    no_practice_obs: dict[int, dict[datetime, dict[str, bool]]] = _derive_no_practice_observations(
        mrv_events_lookup, specification.program_id
    )
    mrv_events_being_converted = []
    preparation_failed = False
    for field in fields:
        try:

            if field.id not in mrv_events_lookup or (
                not mrv_events_lookup.get(field.id, {}).get(PhaseTypes.ENROLMENT)
                and not mrv_events_lookup.get(field.id, {}).get(PhaseTypes.MONITORING)
            ):
                # Allow this temporarily to facilitate 253 migration without most fields gapfilled
                # raise ValueError(f"No events to migrate for field {field.id} despite enrolled status.")
                await _set_migration_result_status(
                    request=request,
                    task_id=specification.task_id,
                    field_id=field.id,
                    status=SESFieldMigrationStatus.succeeded,
                )
                continue
            await _set_migration_result_status(
                request=request,
                task_id=specification.task_id,
                field_id=field.id,
                status=SESFieldMigrationStatus.preparing_events,
            )
            for phase_type, mrv_events in mrv_events_lookup.get(field.id, {}).items():
                mrv_events_being_converted = mrv_events
                phase_id = specification.e_phase_id if phase_type == PhaseTypes.ENROLMENT else specification.m_phase_id
                _apply_measurement_system_to_default_depth_units(
                    measurement_system=specification.program_measurement_system, mrv_events=mrv_events
                )
                for mrv_ev in mrv_events:
                    event_row_id = mrv_ev.id
                    mrv_ev.id = None  # int IDs will confuse the to_ses_events functions
                    # Skip event deletion logic in the upsert event endpoint, since it's only for event updates
                    assert specification.owner_user_id
                    ses_events, _ = await mrv_ev.to_ses_events(
                        specification.owner_user_id,
                        specification.acting_user_id,
                        json.dumps(field_boundaries[field.id]),
                    )
                    for ses_ev in ses_events:
                        _set_ses_event_id_to_hash(
                            event=ses_ev, field_id=field.id, row_id=event_row_id, phase_type=phase_type
                        )
                        if isinstance(ses_ev, (SESFallowPeriod, HarvestActivity)):
                            _attach_no_practice_observations(mrv_ev, ses_ev, no_practice_obs)
                        migratory_events.append(
                            MigratoryEvent(
                                event=ses_ev,
                                field_id=field.id,
                                project_id=field.parent_project_id,
                                field_md5=field.md5,
                                phase_id=phase_id,
                            )
                        )
        except Exception as exc:
            logger.error(
                f"SES Migration: Field {field.id} failed to prepare events for migration.",
                exc_info=exc,
                extra={"mrv_events_being_converted": [ev.json() for ev in mrv_events_being_converted]},
            )
            preparation_failed = True
    if preparation_failed:
        raise RuntimeError("Preparing events for migration failed.")
    return migratory_events


async def _upsert_field_events_to_ses(
    request: Request, migratory_events: list[MigratoryEvent], specification: SESMigrationSpecification
) -> None:
    md5_by_field_id = {me.field_id: me.field_md5 for me in migratory_events}
    for field_id, mes in list_helper.group_by(migratory_events, lambda ev: ev.field_id).items():
        successfully_created_events: list[UpsertEventResponse] = []
        event_being_upserted: StructuredEvent | None = None
        try:
            await _set_migration_result_status(
                request=request,
                task_id=specification.task_id,
                field_id=field_id,
                status=SESFieldMigrationStatus.sending_events,
            )
            for me in mes:
                event_being_upserted = me.event
                created_event: UpsertEventResponse = await _upsert_event_with_retries(event_being_upserted)
                successfully_created_events.append(created_event)
                await event_associations.manually_associate_event_with_phase(
                    request=request,
                    event_id=uuid.UUID(created_event.event.id),
                    revision=created_event.event.revision,
                    field_id=me.field_id,
                    phase_id=me.phase_id,
                )
            # Ideally we'll remove this call after duplicate data has been removed.
            event_revisions = _dedupe_event_revisions(
                [
                    EventRevision(event_id=uuid.UUID(ev.event.id), revision=ev.event.revision)
                    for ev in successfully_created_events
                ]
            )
            # Establish associations for all fields with the md5, considering permissions and phase completion
            await event_associations.associate_events_with_fields_of_md5(
                request=request,
                event_revisions=event_revisions,
                field_md5=md5_by_field_id[field_id],
                owner_user_id=specification.owner_user_id,
            )
            await _set_migration_result_status(
                request=request,
                task_id=specification.task_id,
                field_id=field_id,
                status=SESFieldMigrationStatus.succeeded,
            )
        except Exception as exc:
            await _set_migration_result_status(
                request=request, task_id=specification.task_id, field_id=field_id, status=SESFieldMigrationStatus.failed
            )
            logger.error(
                f"SES Migration: Upserting events failed for field {field_id}, task {specification.task_id}",
                exc_info=exc,
                extra={
                    "successfully_created_events": [MessageToJson(ev) for ev in successfully_created_events],
                    "event_being_upserted": (
                        {
                            "event": MessageToJson(event_being_upserted.pb_event),
                            "context": MessageToJson(event_being_upserted.pb_context),
                        }
                        if event_being_upserted
                        else None
                    ),
                },
            )


def _backoff_expo() -> Generator[float, Any, None]:
    return backoff.expo(factor=6, base=3)


@backoff.on_exception(wait_gen=_backoff_expo, exception=ConnectionError, max_tries=4)
async def _upsert_event_with_retries(event_being_upserted: StructuredEvent) -> UpsertEventResponse:
    return await ses_client.upsert_event(structured_event=event_being_upserted)


def _dedupe_event_revisions(event_revisions: list[EventRevision]) -> list[EventRevision]:
    """
    Deduplicate event revisions based on event_id, and raise if multiple event_revision.revision appear.
    """
    revisions_by_event_id: dict[uuid.UUID, EventRevision] = {}
    for ev in event_revisions:
        if ev.event_id in revisions_by_event_id:
            if revisions_by_event_id[ev.event_id].revision != ev.revision:
                raise ValueError(
                    f"Multiple revisions for event {ev.event_id} found: "
                    f"{revisions_by_event_id[ev.event_id].revision} and {ev.revision}"
                )
        revisions_by_event_id[ev.event_id] = ev
    return list(revisions_by_event_id.values())


def _attach_no_practice_observations(
    mrv_ev: EntityEvent,
    struc_ev: StructuredEvent,
    no_practice_observations_lookup: dict[int, dict[datetime, dict[str, bool]]],
) -> None:
    if event_type(struc_ev.pb_event) in NO_PRACTICE_OBSERVATION_EVENT_TYPES:
        # M1 CroppingEvents may not have an end date
        if event_end := mrv_ev.get_interval_end_or_occurred_at():
            npos = no_practice_observations_lookup[mrv_ev.entity_id][event_end.date()]
            struc_ev.association_context(StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION, json.dumps(npos))


def _set_ses_event_id_to_hash(event: StructuredEvent, field_id: int, row_id: int, phase_type: PhaseTypes) -> None:
    """
    Set an EntityEvent's ID based on a deterministic hash of the field_id, event type, row_id, and phase_type. This
    will allow us to re-migrate programs and handle any updates to the events' data as in-place updates rather than
    inserts. This works because events of a certain type coming from MRV Values are uniquely addressable by
    field_id, row_id, phase_type.
    """
    if row_id is None:
        msg = "EntityEvent.id is None, can't generate unique hash for SES event id."
        logger.error(
            msg, exc_info=ValueError(msg), extra={"event": event, "phase_type": phase_type, "field_id": field_id}
        )
    # Circumvent setting ID on construction
    event.pb_event.id = _calculate_ses_event_id_hash(field_id, row_id, event.__class__.__name__, phase_type)


def _calculate_ses_event_id_hash(field_id: int, row_id: int, struc_event_type: str, phase_type: PhaseTypes) -> str:
    migrated_event_id_namespace = uuid.UUID("16efb39d-9f91-4741-90d1-f6a07b304442")
    hash_input = f"{field_id}-{struc_event_type}-{row_id}-{phase_type.value}"
    return str(uuid.uuid5(migrated_event_id_namespace, hash_input))
