from enum import auto, StrEnum

from sqlalchemy import (
    Column,
    Enum,
    Foreign<PERSON>ey,
    func,
    Integer,
    String,
    TIMESTAMP,
    UniqueConstraint,
)

from db.mysql import Base


class SESFieldMigrationStatus(StrEnum):
    enqueued = auto()
    preparing_events = auto()
    sending_events = auto()
    failed = auto()
    succeeded = auto()


class SESMigrationResult(Base):
    __tablename__ = "mrv_ses_migration_results"
    __table_args__ = (
        UniqueConstraint(
            "task_id",
            "field_id",
            name="task_id_field_id_uc",
        ),
    )
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    task_id = Column(String(36), nullable=False, index=True)
    field_id = Column(Integer, ForeignKey("mrv_fields.id"), nullable=False)
    project_id = Column(Integer, ForeignKey("mrv_projects.id"), nullable=False)
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=False)
    status = Column(Enum(SESFieldMigrationStatus), nullable=False)
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(
        TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp()
    )
