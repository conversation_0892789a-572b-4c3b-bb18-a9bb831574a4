import uuid
from itertools import chain
from uuid import UUID

import elasticapm
from regrow.ses.event.v1.event_pb2 import Event
from starlette.requests import Request

from fields.db import get_program_ids_by_field_ids
from fields.model import Fields
from fields.schema import Field
from helper.list_helper import group_by
from logger import get_logger
from phases.db import get_completed_phases_for_field, get_field_phases_for_md5_and_user
from phases.enums import PhaseTypes
from phases.model import Phases, PhasesWithoutRelationships
from programs.enums import ProgramTemplate
from programs.model import ProgramsWithoutRelationships
from projects.model import ProjectPhaseCompletion
from root_crud import create, delete, get
from ses_integration import db
from ses_integration.constants import EVENT_ASSOCIABLE_PHASE_TYPES
from ses_integration.db import (
    get_field_event_associations,
    get_locked_event_ids_for_user,
    get_user_owned_field_event_associations,
    RawPhaseEventAssociation,
)
from ses_integration.model import FieldEventAssociation, PhaseEventAssociation
from ses_integration.schema import AssociationField, EventRevision

logger = get_logger(__name__)


# Public functions
@elasticapm.async_capture_span()
async def update_event_associations(
    request: Request, field: Field, phase_id: int, upserted_events: list[Event], owner_user_id: str
) -> None:
    await associate_events_with_fields_of_md5(
        request=request,
        event_revisions=[EventRevision(event_id=UUID(ev.id), revision=ev.revision) for ev in upserted_events],
        field_md5=field.md5,
        owner_user_id=owner_user_id,
    )


async def associate_events_with_fields_of_md5(
    request: Request,
    event_revisions: list[EventRevision],
    field_md5: str,
    owner_user_id: str,
    project_id: int | None = None,
) -> None:
    # Determine the associable phases for the field, across programs, where we will establish associations
    associable_phases_per_field = await _get_associable_phases_per_field(
        request=request, field_md5=field_md5, owner_user_id=owner_user_id, project_id=project_id
    )
    if not associable_phases_per_field:
        return
    # Replace each field with an AssociationField
    fields_by_id = {
        a_fld.field_id: a_fld
        for a_fld in await _get_association_fields(
            request=request, fields=[fld for fld, phases in associable_phases_per_field]
        )
    }
    await _associate_events_with_fields_for_phases(
        request=request,
        event_revisions=event_revisions,
        associable_phases_per_field=[(fields_by_id[fld.id], phases) for fld, phases in associable_phases_per_field],
    )


async def delete_event_associations(
    request: Request,
    owner_user_id: str,
    event_ids: list[uuid.UUID] | None = None,
    field_md5: str | None = None,
    field_id: int | None = None,
    force_delete_in_completed_phases: bool = False,
) -> None:
    if not event_ids and not field_md5:
        return
    field_event_associations = await get_user_owned_field_event_associations(
        request=request,
        owner_user_id=owner_user_id,
        event_ids=event_ids,
        field_md5=field_md5,
        field_id=field_id,
    )
    phase_event_associations = await get.generic_get(
        request=request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id,
                ids=[fea.id for fea in field_event_associations],
            )
        ],
        empty_return=True,
    )

    await _soft_delete_phase_associations_and_childless_field_associations(
        request=request,
        field_event_associations=field_event_associations,
        phase_event_associations=phase_event_associations,
        force_delete_in_completed_phases=force_delete_in_completed_phases,
    )


async def manually_associate_event_with_phase(
    request: Request, event_id: UUID, revision: int, field_id: int, phase_id: int
) -> None:
    """
    Manually associate an event with a phase, bypassing verification that a Phase has not been completed by the Field's
    Project. Upserts the FieldEventAssociation as well as the PhaseEventAssociation.

    Caller must ensure that the primary user for field_id has permission to use the event_id.
    """
    field = (
        await get.get(
            request=request,
            orm_type=Fields,
            id_field=Fields.id,
            ids=[field_id],
        )
    )[0]
    assoc_field = (await _get_association_fields(request=request, fields=[field]))[0]
    phase: Phases = (await get.get(request=request, orm_type=Phases, id_field=Phases.id, ids=[phase_id]))[0]
    if assoc_field.program_id != phase.program_id:
        raise ValueError(f"Field {field_id} is not in the same program as phase {phase_id}.")
    if phase.type_ not in EVENT_ASSOCIABLE_PHASE_TYPES:
        raise ValueError(f"Phase {phase_id} is not an event-associable type.")
    await _associate_events_with_fields_for_phases(
        request=request,
        event_revisions=[EventRevision(event_id=event_id, revision=revision)],
        associable_phases_per_field=[(assoc_field, [phase])],
    )


async def get_event_revisions_for_field_phase(
    request: Request, field_id: int, phase_type: PhaseTypes
) -> list[EventRevision]:
    field_event_associations: list[FieldEventAssociation] = await db.get_field_event_associations(
        request=request, field_ids=[field_id]
    )
    if not field_event_associations:
        return []
    fea_by_id = {fea.id: fea for fea in field_event_associations}
    phase_event_associations = await db.get_phase_event_associations(
        request=request,
        field_event_association_ids=[fea.id for fea in field_event_associations],
        phase_type=phase_type,
    )
    return [
        EventRevision(event_id=UUID(fea_by_id[pea.field_event_association_id].ses_event_id), revision=pea.revision)
        for pea in phase_event_associations
    ]


async def are_any_events_locked_by_phase_association(
    request: Request, user_id: int, acting_phase_id: int, event_ids: list[uuid.UUID]
) -> bool:
    """
    Determine whether an event is locked based on its phase associations and the acting phase's type.

    Args:
        request: FastAPI request object
        user_id (int): User ID to check for locks
        acting_phase_id (int): ID of the acting phase
        event_ids (uuid.UUID): SES event IDs to check for locks
    """
    if not event_ids:
        return False

    # TODO Add event_ids parameter to function instead of checking membership afterwards
    locked_event_ids = await get_locked_event_ids_for_user(
        request=request,
        owner_user_id=user_id,
        acting_phase_id=acting_phase_id,
    )
    return any(event_id in locked_event_ids for event_id in event_ids)


# Private functions


# Private because it allows associating events with phases that the field has already completed
async def _associate_events_with_fields_for_phases(
    request: Request,
    event_revisions: list[EventRevision],
    associable_phases_per_field: list[tuple[AssociationField, list[PhasesWithoutRelationships]]],
) -> None:
    """
    Establish field event associations and their child phase event associations. All provided event revisions will be
    associated.
    """
    field_event_associations: list[FieldEventAssociation] = await _upsert_associations_between_events_and_fields(
        request=request,
        event_ids=[ev.event_id for ev in event_revisions],
        fields=[fld for fld, phases in associable_phases_per_field if phases],
    )
    await _update_phase_event_associations(
        request, event_revisions, field_event_associations, associable_phases_per_field
    )


async def _get_association_fields(request: Request, fields: list[Fields]) -> list[AssociationField]:
    """
    Given a list of fields, return the association fields for those fields.
    """
    assoc_fields = []
    program_id_by_field_id = await get_program_ids_by_field_ids(request=request, field_ids=[fld.id for fld in fields])
    for field in fields:
        try:
            assoc_fields.append(
                AssociationField(
                    field_md5=field.md5,
                    field_id=field.id,
                    project_id=field.parent_project_id,
                    program_id=program_id_by_field_id[field.id],
                )
            )
        except KeyError:
            logger.error(f"Couldn't get program_id for field {field.id}. Don't associate events with deleted fields.")
    return assoc_fields


async def _upsert_associations_between_events_and_fields(
    request: Request, event_ids: list[uuid.UUID], fields: list[AssociationField]
) -> list[FieldEventAssociation]:
    """
    Given a list of existing events and fields (in AssociationField format), create or update FieldEventAssociations.

    Caller must guarantee that the field is owned by the owning user of the given events in all the given programs.
    Caller will usually want to ensure that the field has an open phase in the program it's in.

    Returns all FieldAssociations for the provided event_ids and fields, whether an update was necessary or not.
    """
    if not fields:
        return []
    assert len({fld.field_md5 for fld in fields}) == 1, "Field md5s must be the same for all association fields"
    field_md5 = fields[0].field_md5
    existing_associations = await get_field_event_associations(
        request=request,
        event_ids=event_ids,
        field_ids=[fld.field_id for fld in fields],
        filter_deleted=False,
    )
    logger.debug(
        f"Found {len(existing_associations)} existing field event associations: {[e.ses_event_id for e in existing_associations]}"
    )
    existing_associations_by_field_id = group_by(existing_associations, lambda assoc: assoc.field_id)
    to_create = []
    to_update = []
    for ses_event_id in event_ids:
        for field in fields:
            existing_associations_for_field = existing_associations_by_field_id.get(field.field_id, [])
            try:
                existing_association = next(
                    assoc for assoc in existing_associations_for_field if assoc.ses_event_id == str(ses_event_id)
                )
                if existing_association.field_md5 != field_md5 or existing_association.deleted_at is not None:
                    to_update.append(existing_association.id)
            except StopIteration:
                to_create.append(
                    FieldEventAssociation(
                        ses_event_id=str(ses_event_id),
                        field_md5=field.field_md5,
                        field_id=field.field_id,
                        project_id=field.project_id,
                        program_id=field.program_id,
                    )
                )
    logger.debug(f"Creating {len(to_create)} new field event associations: {[a.to_dict() for a in to_create]}")
    created_associations = await create.create(
        request=request, instances=to_create, orm_type=FieldEventAssociation, translate=False, return_orm=True
    )
    logger.debug(f"Updating/undeleting {len(to_update)} field event associations: {to_update}")
    updated_associations = await db.bulk_update_and_undelete_field_event_associations(
        request=request, field_event_association_ids=to_update, field_md5=field_md5
    )

    # Filter the updated associations out of the existing associations
    existing_associations = [
        assoc for assoc in existing_associations if assoc.id not in {und_assoc.id for und_assoc in updated_associations}
    ]
    result = created_associations + updated_associations + existing_associations
    logger.info(f"Upserted {len(result)} field event associations")
    return result


async def _update_phase_event_associations(
    request: Request,
    event_revisions: list[EventRevision],
    field_event_associations: list[FieldEventAssociation],
    associable_phases_per_field: list[tuple[AssociationField, list[PhasesWithoutRelationships]]],
) -> None:

    revisions_by_event_id = {ev.event_id: ev.revision for ev in event_revisions}
    field_event_associations_by_field_id = group_by(field_event_associations, lambda assoc: assoc.field_id)
    phase_event_associations = []
    for field, phases in associable_phases_per_field:
        for phase in phases:
            for field_event_assoc in field_event_associations_by_field_id.get(field.field_id, []):
                phase_event_associations.append(
                    RawPhaseEventAssociation(
                        field_event_association_id=field_event_assoc.id,
                        phase_id=phase.id,
                        revision=revisions_by_event_id[uuid.UUID(field_event_assoc.ses_event_id)],
                    )
                )
    await db.bulk_associate_phases_with_events(request=request, phase_event_associations=phase_event_associations)


async def _get_associable_phases_per_field(
    request: Request,
    field_md5: str,
    owner_user_id: str,
    project_id: int | None = None,
) -> list[tuple[Fields, list[PhasesWithoutRelationships]]]:
    """
    A phase can be associated with an event if:
        * the field participates in the program the phase belongs to
        * the phase is one of Enrollment or Monitoring
        * the Phase either hasn't been completed, or is a completed M Phase where the E Phase hasn't been completed
    Returns such phases
    """
    field_phases = await get_field_phases_for_md5_and_user(
        request=request, field_md5=field_md5, owner_user_id=owner_user_id, project_id=project_id
    )
    # Note that we choose not to scope the next call to a user, because we're just removing phases at this point
    return await _select_event_associable_phases(request=request, field_phases=field_phases, field_md5=field_md5)


async def _select_event_associable_phases(
    request: Request,
    field_phases: list[tuple[Fields, list[PhasesWithoutRelationships]]],
    field_md5: str,
) -> list[tuple[Fields, list[PhasesWithoutRelationships]]]:
    """
    Filter out phases that are already completed for the field and that are not an event-associable type. Result does
    not include fields that have no event-associable phases.
    """
    # Note that this call is not permissions-scoped, which is acceptable since we're only excluding phases and an md5
    # can only be used by one user in a particular phase.
    completed_phases = await get_completed_phases_for_field(request=request, field_md5=field_md5)
    completed_phase_ids = [phs.id for phs in completed_phases]

    # Get the program IDs so we can check if they're event-based
    all_phases = list(chain.from_iterable([phases for _fld, phases in field_phases]))
    programs_by_id = {
        prg.id: prg
        for prg in await get.get(
            request=request,
            orm_type=ProgramsWithoutRelationships,
            id_field=ProgramsWithoutRelationships.id,
            ids=list({phs.program_id for phs in all_phases}),
        )
    }
    # Filter out non-event-based programs and non-event-associable phase types
    field_phases = [
        (
            field,
            [
                phs
                for phs in phases
                if programs_by_id[phs.program_id].program_template == ProgramTemplate.event_based
                and phs.type_ in EVENT_ASSOCIABLE_PHASE_TYPES
            ],
        )
        for field, phases in field_phases
    ]

    fields_with_associable_phases = []
    for field, single_field_phases in field_phases:
        field_associable_phases = []
        phases_by_program = group_by(single_field_phases, lambda phs: phs.program_id)
        for _program_id, prg_phases in phases_by_program.items():
            # If the E Phase is associable, then always update other associable phases
            # https://regrow.atlassian.net/browse/MRV-5861
            if any(phs.type_ == PhaseTypes.ENROLMENT and phs.id not in completed_phase_ids for phs in prg_phases):
                assert all(
                    phs.type_ in {PhaseTypes.ENROLMENT, PhaseTypes.MONITORING} for phs in prg_phases
                ), "If we've added a different kind of event associable phase, decide how to adapt this logic."
                field_associable_phases.extend(prg_phases)
            else:
                # Otherwise only uncompleted phases are event-associable
                field_associable_phases.extend([phs for phs in prg_phases if phs.id not in completed_phase_ids])
        if field_associable_phases:
            fields_with_associable_phases.append((field, field_associable_phases))
    return fields_with_associable_phases


async def _soft_delete_phase_associations_and_childless_field_associations(
    request: Request,
    field_event_associations: list[FieldEventAssociation],
    phase_event_associations: list[PhaseEventAssociation],
    force_delete_in_completed_phases: bool = False,
) -> None:
    """
    Soft delete phase associations as long as the linked phase hasn't been completed by the linked project. Also soft-
    delete the parent field event association if all of its child phase event associations are deleted.

    This function is sort of a functional domain gray area since it encodes some associations logic and some data layer.
    It's placed here to avoid exposing it as a public function since a caller could make a mess passing in arbitrary
    associations. Anyone with a better idea how to structure this can feel free to refactor.

    Accepts:
        FieldEventAssociations scoped to a single owner_user_id
        PhaseEventAssociations representing all children of the provided field associations
    """
    logger.info(
        f"Deleting {len(field_event_associations)} field event associations and {len(phase_event_associations)} phase "
        f"event associations. force_delete_in_completed_phases={force_delete_in_completed_phases}"
    )
    for fld_assoc in field_event_associations:
        project_complete_phase_ids = []
        if not force_delete_in_completed_phases:
            project_phase_completions = await get.generic_get(
                request=request,
                orm_type=ProjectPhaseCompletion,
                filters=[
                    get.Filter(id_field=ProjectPhaseCompletion.project_id, ids=[fld_assoc.project_id]),
                ],
                empty_return=True,
            )
            project_complete_phase_ids = [phs.phase_id for phs in project_phase_completions if phs.is_completed is True]
        child_phase_associations = [
            assoc
            for assoc in phase_event_associations
            if assoc.field_event_association_id == fld_assoc.id and assoc.deleted_at is None
        ]
        to_delete = [
            assoc.id
            for assoc in child_phase_associations
            if force_delete_in_completed_phases or assoc.phase_id not in project_complete_phase_ids
        ]

        if to_delete:
            await delete.soft(
                request=request,
                ids=to_delete,
                id_field=PhaseEventAssociation.id,
                orm_type=PhaseEventAssociation,
                return_deleted=False,
            )

        # Delete field association if all child phase associations are deleted
        if to_delete and len(to_delete) == len(child_phase_associations):
            await delete.soft(
                request=request,
                ids=[fld_assoc.id],
                id_field=FieldEventAssociation.id,
                orm_type=FieldEventAssociation,
                return_deleted=False,
            )
