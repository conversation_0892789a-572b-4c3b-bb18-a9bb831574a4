from unittest.mock import ANY

from sentry_sdk.integrations import httpx
from starlette import status


async def test_migrate_program_to_ses(async_client: httpx.AsyncClient, mocker):
    migrate_mock = mocker.patch(
        "ses_integration.migration.router.migrate_mrv_values_to_ses.migrate_program_data_to_ses", return_value=None
    )
    response = await async_client.post(
        "/programs/1/ses-migration", json={"project_ids": [2], "generate_fallow_dates": False}
    )

    assert response.status_code == status.HTTP_200_OK
    migrate_mock.assert_called_once_with(
        request=ANY, program_id=1, project_ids=[2], field_ids=None, generate_fallow_dates=False
    )


async def test_migrate_program_to_ses_specific_field_ids(async_client: httpx.AsyncClient, mocker):
    migrate_mock = mocker.patch(
        "ses_integration.migration.router.migrate_mrv_values_to_ses.migrate_program_data_to_ses", return_value=None
    )
    response = await async_client.post(
        "/programs/1/ses-migration", json={"project_ids": [2], "field_ids": [3], "generate_fallow_dates": False}
    )

    assert response.status_code == status.HTTP_200_OK
    migrate_mock.assert_called_once_with(
        request=ANY, program_id=1, project_ids=[2], field_ids=[3], generate_fallow_dates=False
    )


async def test_migrate_program_to_ses_default_fallow_behavior(async_client: httpx.AsyncClient, mocker):
    migrate_mock = mocker.patch(
        "ses_integration.migration.router.migrate_mrv_values_to_ses.migrate_program_data_to_ses", return_value=None
    )
    response = await async_client.post("/programs/1/ses-migration", json={"project_ids": [2]})

    assert response.status_code == status.HTTP_200_OK
    migrate_mock.assert_called_once_with(
        request=ANY, program_id=1, project_ids=[2], field_ids=None, generate_fallow_dates=True
    )
