from datetime import datetime
from uuid import uuid4

import pytest

from phases.enums import PhaseTypes
from projects.model import ProjectPermissions, ProjectPhaseCompletion
from root_crud import delete, get, update
from ses_integration.db import (
    bulk_associate_phases_with_events,
    get_field_event_associations,
    get_locked_event_ids_for_user,
    get_phase_event_associations,
    get_user_owned_field_event_associations,
    RawPhaseEventAssociation,
)
from ses_integration.model import FieldEventAssociation, PhaseEventAssociation


async def test_get_field_event_associations_by_event_ids(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id2),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=[event_id1], field_md5=None, field_ids=None
    )

    assert len(result) == 1
    assert result[0].ses_event_id == str(event_id1)


async def test_get_field_event_associations_by_field_md5(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_md5 = "test_md5"
    field_md5_2 = "test_md52"
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id)
    field2 = await mdl.Fields(md5=field_md5_2, parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id2),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=field_md5, field_ids=None
    )

    assert len(result) == 1
    assert result[0].field_md5 == field_md5


async def test_get_field_event_associations_by_field_ids(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id2),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id]
    )

    assert len(result) == 1
    assert result[0].field_id == field.id


async def test_get_field_event_associations_filter_deleted(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id2),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
        deleted_at=datetime.now(),
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id]
    )

    assert len(result) == 1
    assert result[0].ses_event_id == str(event_id1)

    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id], filter_deleted=False
    )

    assert len(result) == 2


async def test_get_field_event_associations_no_results(app_request, mdl):
    result = await get_field_event_associations(
        request=app_request, event_ids=[uuid4()], field_md5="non_existent_md5", field_ids=[999]
    )

    assert len(result) == 0


async def test_get_user_owned_field_event_associations(app_request, mdl) -> None:
    owner_user_id = "1"
    other_user_id = "2"
    field_md5 = "test_md5"
    event_id = uuid4()

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, md5=field_md5)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    other_program = await mdl.Programs()
    other_project = await mdl.Projects(program_id=other_program.id)
    other_field = await mdl.Fields(parent_project_id=other_project.id, md5=field_md5)
    await mdl.ProjectPermissions(project=other_project.id, user=int(other_user_id))

    # FieldEventAssociation for owner_user_id
    field_event_association_owner = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(event_id), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    # FieldEventAssociation for other_user_id
    field_event_association_other = await mdl.FieldEventAssociation(
        field_id=other_field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=other_project.id,
        program_id=other_program.id,
    )

    # Test fetching associations for owner_user_id
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=owner_user_id, event_ids=[event_id]
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_owner.id

    # Test fetching associations for other_user_id
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=other_user_id, event_ids=[event_id]
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_other.id


async def test_get_user_owned_field_event_associations_no_event_ids(app_request, mdl) -> None:
    owner_user_id = "1"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    # FieldEventAssociation for owner_user_id
    field_event_association_owner = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(uuid4()), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    # Test fetching associations without specifying event_ids
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=owner_user_id, event_ids=None
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_owner.id


async def test_bulk_associate_phases_with_events(app_request, mdl):
    program = await mdl.Programs()
    phase_id_1 = (await mdl.Phases(program_id=program.id)).id
    phase_id_2 = (await mdl.Phases(program_id=program.id)).id
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    event_id_1 = uuid4()
    event_id_2 = uuid4()

    # Create FieldEventAssociations
    field_event_association_1 = await mdl.FieldEventAssociation(
        field_id=field_1.id,
        ses_event_id=str(event_id_1),
        field_md5=field_1.md5,
        project_id=project.id,
        program_id=program.id,
    )
    field_event_association_2 = await mdl.FieldEventAssociation(
        field_id=field_2.id,
        ses_event_id=str(event_id_2),
        field_md5=field_2.md5,
        project_id=project.id,
        program_id=program.id,
    )

    phase_event_associations = [
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_1.id,
            phase_id=phase_id_1,
            revision=1,
        ),
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_1.id,
            phase_id=phase_id_2,
            revision=2,
        ),
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_2.id,
            phase_id=phase_id_1,
            revision=1,
        ),
    ]
    await bulk_associate_phases_with_events(request=app_request, phase_event_associations=phase_event_associations)

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id,
                ids=[field_event_association_1.id, field_event_association_2.id],
            ),
            get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id_1, phase_id_2]),
        ],
        order_by_cols=[PhaseEventAssociation.field_event_association_id, PhaseEventAssociation.phase_id],
    )

    assert associations[0].field_event_association_id == field_event_association_1.id
    assert associations[0].phase_id == phase_id_1
    assert associations[0].revision == 1

    assert associations[1].field_event_association_id == field_event_association_1.id
    assert associations[1].phase_id == phase_id_2
    assert associations[1].revision == 2

    assert associations[2].field_event_association_id == field_event_association_2.id
    assert associations[2].phase_id == phase_id_1
    assert associations[2].revision == 1


async def test_bulk_associate_phases_with_events_duplicate(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    phase_id_1 = (await mdl.Phases(program_id=program.id)).id
    phase_id_2 = (await mdl.Phases(program_id=program.id)).id
    revision = 1

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_1,
                revision=revision,
            ),
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_2,
                revision=revision,
            ),
        ],
    )
    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_1,
                revision=revision + 1,  # Update revision
            ),
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_2,
                revision=revision + 1,  # Update revision
            ),
        ],
    )

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id]),
            get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id_1, phase_id_2]),
        ],
        order_by_cols=[PhaseEventAssociation.field_event_association_id, PhaseEventAssociation.phase_id],
    )

    assert len(associations) == 2
    assert associations[0].field_event_association_id == field_event_association.id
    assert associations[0].phase_id == phase_id_1
    assert associations[0].revision == revision + 1

    assert associations[1].field_event_association_id == field_event_association.id
    assert associations[1].phase_id == phase_id_2
    assert associations[1].revision == revision + 1


async def test_bulk_associate_phases_with_events_deleted_phase_assoc(app_request, mdl, faker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    revision = 1
    phase_id = (await mdl.Phases(program_id=program.id)).id

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase_id,
        revision=revision,
        deleted_at=faker.date_time(),
    )

    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id,
                revision=revision,
            )
        ],
    )

    # Make sure the association was undeleted
    association = (
        await get.generic_get(
            request=app_request,
            orm_type=PhaseEventAssociation,
            filters=[
                get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id]),
                get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id]),
            ],
        )
    )[0]

    assert association.deleted_at is None


@pytest.mark.parametrize(
    "past_phase_type, past_phase_completed, acting_phase_type, expected_locked",
    [
        (PhaseTypes.MONITORING, False, PhaseTypes.ENROLMENT, False),
        (PhaseTypes.ENROLMENT, True, PhaseTypes.ENROLMENT, True),
        (PhaseTypes.ENROLMENT, True, PhaseTypes.MONITORING, False),
        (PhaseTypes.MONITORING, True, PhaseTypes.ENROLMENT, True),
        (PhaseTypes.MONITORING, True, PhaseTypes.MONITORING, True),
        (PhaseTypes.ENROLMENT, False, PhaseTypes.ENROLMENT, False),
        (PhaseTypes.MONITORING, False, PhaseTypes.MONITORING, False),
    ],
)
async def test_get_locked_event_ids_for_user_phase_logic(
    past_phase_type, past_phase_completed, acting_phase_type, expected_locked, app_request, mdl
):
    """
    Acting E Phase respects locks from E Phases and M Phases.
    Acting M Phase respects locks from M Phases only.
    """
    user_id = 1
    event_id = uuid4()

    past_program = await mdl.Programs()
    past_phase_id = (await mdl.Phases(program_id=past_program.id, type_=past_phase_type)).id

    acting_program = await mdl.Programs(previous_program_id=past_program.id)
    acting_phase_id = (await mdl.Phases(program_id=acting_program.id, type_=acting_phase_type)).id

    past_project = await mdl.Projects(program_id=past_program.id)
    await mdl.ProjectPermissions(project=past_project.id, user=user_id)
    past_field = await mdl.Fields(parent_project_id=past_project.id, md5="test-md5")

    await mdl.ProjectPhaseCompletion(
        project_id=past_project.id,
        phase_id=past_phase_id,
        is_completed=past_phase_completed,
        allow_post_close_edit=False,
    )

    field_event_association = await mdl.FieldEventAssociation(
        field_id=past_field.id,
        ses_event_id=str(event_id),
        field_md5=past_field.md5,
        project_id=past_project.id,
        program_id=past_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=past_phase_id, revision=1
    )

    result = await get_locked_event_ids_for_user(
        request=app_request, owner_user_id=user_id, acting_phase_id=acting_phase_id
    )
    if expected_locked:
        assert event_id in result
    else:
        assert event_id not in result


async def test_get_locked_event_ids_for_user_different_users(app_request, mdl):
    """
    The same event can be locked for one user and not locked for another if they both have access to it.
    User 1 should see the event as locked because of its completed phase, while User 2 should not see the event at all.
    """
    user1_id = 1
    user2_id = 2
    # Users have the same field md5 and SES event id
    field_md5 = "test_md5"
    event_id = uuid4()

    # User 1 setup
    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    field1 = await mdl.Fields(parent_project_id=project1.id, md5=field_md5)
    phase1_id = (await mdl.Phases(program_id=program1.id, type_=PhaseTypes.MONITORING)).id
    await mdl.ProjectPermissions(project=project1.id, user=user1_id)
    field_event_association1 = await mdl.FieldEventAssociation(
        field_id=field1.id,
        ses_event_id=str(event_id),
        field_md5=field1.md5,
        project_id=project1.id,
        program_id=program1.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association1.id, phase_id=phase1_id, revision=1
    )
    await mdl.ProjectPhaseCompletion(
        project_id=project1.id, phase_id=phase1_id, is_completed=True, allow_post_close_edit=False
    )

    # User 2 setup
    program2 = await mdl.Programs()
    project2 = await mdl.Projects(program_id=program2.id)
    field2 = await mdl.Fields(parent_project_id=project2.id, md5=field_md5)
    phase2_id = (await mdl.Phases(program_id=program2.id, type_=PhaseTypes.MONITORING)).id
    await mdl.ProjectPermissions(project=project2.id, user=user2_id)
    field_event_association2 = await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id),
        field_md5=field2.md5,
        project_id=project2.id,
        program_id=program2.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association2.id, phase_id=phase2_id, revision=1
    )
    await mdl.ProjectPhaseCompletion(project_id=project2.id, phase_id=phase2_id, is_completed=False)

    result_user1 = await get_locked_event_ids_for_user(
        request=app_request, owner_user_id=user1_id, acting_phase_id=phase1_id
    )
    result_user2 = await get_locked_event_ids_for_user(
        request=app_request, owner_user_id=user2_id, acting_phase_id=phase2_id
    )

    assert event_id in result_user1
    assert event_id not in result_user2


async def test_get_locked_event_ids_for_user_with_deleted_entities(app_request, mdl):
    """Deleting any of the entities that connect events to phase completion should unlock the event."""
    user_id = 1
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    phase_id = (await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)).id

    # Create associations
    project_permission = await mdl.ProjectPermissions(project=project.id, user=user_id, deleted_at=None)
    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
        deleted_at=None,
    )
    phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase_id,
        revision=1,
        deleted_at=None,
    )
    project_phase_completion = await mdl.ProjectPhaseCompletion(
        project_id=project.id, phase_id=phase_id, is_completed=True, deleted_at=None
    )

    # Event is locked initially
    result = await get_locked_event_ids_for_user(request=app_request, owner_user_id=user_id, acting_phase_id=phase_id)
    assert event_id in result

    # Delete ProjectPhaseCompletion, verify event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=ProjectPhaseCompletion,
        ids=[project_phase_completion.id],
        id_field=ProjectPhaseCompletion.id,
    )
    result = await get_locked_event_ids_for_user(request=app_request, owner_user_id=user_id, acting_phase_id=phase_id)
    assert event_id not in result

    # Recreate ProjectPhaseCompletion
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase_id, is_completed=True, deleted_at=None)

    # Delete PhaseEventAssociation and verify the event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=PhaseEventAssociation,
        ids=[phase_event_association.id],
        id_field=PhaseEventAssociation.id,
    )
    result = await get_locked_event_ids_for_user(request=app_request, owner_user_id=user_id, acting_phase_id=phase_id)
    assert event_id not in result

    phase_event_association.deleted_at = None
    await update.update(request=app_request, type=PhaseEventAssociation, instances=[phase_event_association])

    # Delete FieldEventAssociation, verify the event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=FieldEventAssociation,
        ids=[field_event_association.id],
        id_field=FieldEventAssociation.id,
    )
    result = await get_locked_event_ids_for_user(request=app_request, owner_user_id=user_id, acting_phase_id=phase_id)
    assert event_id not in result

    # Recreate FieldEventAssociation
    field_event_association.deleted_at = None
    await update.update(request=app_request, type=FieldEventAssociation, instances=[field_event_association])

    # Delete ProjectPermissions and verify the event is no longer locked
    await delete.soft(
        request=app_request, orm_type=ProjectPermissions, ids=[project_permission.id], id_field=ProjectPermissions.id
    )
    result = await get_locked_event_ids_for_user(request=app_request, owner_user_id=user_id, acting_phase_id=phase_id)
    assert event_id not in result


async def test_get_phase_event_associations_by_field_event_association_id(app_request, mdl):
    program = await mdl.Programs()
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )

    e_phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=e_phase.id,
        revision=1,
    )
    m_phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=m_phase.id,
        revision=2,
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 1
    assert result[0].id == e_phase_event_association.id
    assert result[0].phase_id == e_phase.id
    assert result[0].revision == 1

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.MONITORING
    )

    assert len(result) == 1
    assert result[0].id == m_phase_event_association.id
    assert result[0].phase_id == m_phase.id
    assert result[0].revision == 2


async def test_get_phase_event_associations_ignores_deleted(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    event_id = uuid4()

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase.id,
        revision=1,
        deleted_at=datetime.now(),
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 0

    phase_event_association.deleted_at = None
    await update.update(
        request=app_request,
        type=PhaseEventAssociation,
        instances=[phase_event_association],
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 1

    phase.deleted_at = datetime.now()
    await update.update(
        request=app_request,
        type=mdl.Phases,
        instances=[phase],
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 0
