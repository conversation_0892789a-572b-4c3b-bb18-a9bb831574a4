import uuid
from unittest.mock import ANY

import pytest
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextRequest
from ses_client.event import EventWithContext

from phases.enums import PhaseTypes, StageTypes
from ses_integration.fetch_entity_events import fetch_events_for_field_phase
from ses_integration.schema import EventRevision


@pytest.mark.parametrize(
    "enrollment_phase_only,is_single_phase_program,expected_phase_type",
    [(False, False, PhaseTypes.MONITORING), (True, False, PhaseTypes.ENROLMENT), (False, True, PhaseTypes.ENROLMENT)],
)
async def test_fetch_events_for_field_phase(
    enrollment_phase_only,
    is_single_phase_program,
    expected_phase_type,
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=expected_phase_type, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mock_get_event_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_field_phase",
        return_value=event_revisions,
    )
    mock_fetch_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=enrollment_phase_only,
        is_single_phase_program=is_single_phase_program,
    )
    assert res == [cropping_event, tillage_event]

    mock_get_event_revisions.assert_called_with(request=app_request, field_id=field.id, phase_type=expected_phase_type)
    mock_fetch_events.assert_called_with(
        fetch_event_requests=[FetchEventWithContextRequest(id=str(event_id), revision="1", merge_context=False)]
    )
    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert {ev.event.id for ev in mock_parse_ses_events.call_args.kwargs["cropping_events"][0]} == {
        sowing_activity_and_context[0].id,
        harvest_activity_and_context[0].id,
        termination_activity_and_context[0].id,
    }
    assert (
        mock_parse_ses_events.call_args.kwargs["field_practice_events"][0].event.id
        == tillage_activity_and_context[0].id
    )


async def test_fetch_events_for_field_phase_with_needed_reconciliation(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mock_get_event_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_field_phase",
        # empty list to simulate no revisions found, triggering reconciliation
        side_effect=[[], event_revisions],
    )
    mock_search_events = mocker.patch(
        "ses_client.client.Client.fetch_events_for_fields",
        # doesn't matter what we return here, we are mocking fetch_event_revisions_with_context
        return_value={},
    )
    mock_fetch_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=False,
        is_single_phase_program=False,
    )
    assert res == [cropping_event, tillage_event]

    assert mock_get_event_revisions.call_count == 2
    mock_search_events.assert_called_with(field_ids=[field.md5], search_filter=ANY)
    mock_get_event_revisions.assert_called_with(
        request=app_request, field_id=field.id, phase_type=PhaseTypes.MONITORING
    )
    mock_fetch_events.assert_called_with(
        fetch_event_requests=[FetchEventWithContextRequest(id=str(event_id), revision="1", merge_context=False)]
    )
    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id


async def test_fetch_events_for_field_phase_with_disabled_cropping_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=False)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_field_phase",
        return_value=event_revisions,
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [tillage_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert mock_parse_ses_events.call_args.kwargs["cropping_events"] == []
    assert len(mock_parse_ses_events.call_args.kwargs["field_practice_events"]) == 1


async def test_fetch_events_for_field_phase_with_disabled_tillage_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=False)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_field_phase",
        return_value=event_revisions,
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [cropping_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert len(mock_parse_ses_events.call_args.kwargs["cropping_events"][0]) == 3
    assert mock_parse_ses_events.call_args.kwargs["field_practice_events"] == []


async def test_fetch_events_for_field_phase_with_disabled_monitoring_tillage_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    enrollment_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=enrollment_phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=enrollment_phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    monitoring = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    await mdl.Stage(phase_id=monitoring.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=monitoring.id, type_=StageTypes.TILLAGE_EVENTS, enabled=False)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_field_phase",
        return_value=event_revisions,
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [cropping_event, tillage_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert len(mock_parse_ses_events.call_args.kwargs["cropping_events"][0]) == 3
    assert len(mock_parse_ses_events.call_args.kwargs["field_practice_events"]) == 1
