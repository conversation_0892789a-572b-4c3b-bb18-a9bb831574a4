from datetime import datetime, timezone

import pytz

from defaults.attribute_options import CropUsage
from defaults.tests.mock_defaults_translator import (
    mock_translate_core_crop_name_to_regrow_name,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod as MRVFallowPeriod
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from fields.model import Fields
from ses_integration.migration.build_mrv_events_for_migration import (
    _adjust_generated_fallow_overlaps_with_events,
    _convert_core_crop_names_to_regrow_names,
    _get_timezone_for_field_boundary_centroid,
    _localize_and_repair_event_timestamps,
)
from values.enums import EntityTypeChoices


def cov_crop_event(field: Fields, plant: str, harvest: str) -> CroppingEvent:
    return CroppingEvent(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime.strptime(plant, "%Y-%m-%d").replace(tzinfo=timezone.utc),
            end=datetime.strptime(harvest, "%Y-%m-%d").replace(tzinfo=timezone.utc),
        ),
        crop_type="corn",
        crop_usage=CropUsage.COVER,
    )


def com_crop_event(field: Fields, plant: str, harvest: str) -> CroppingEvent:
    return CroppingEvent(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime.strptime(plant, "%Y-%m-%d").replace(tzinfo=timezone.utc),
            end=datetime.strptime(harvest, "%Y-%m-%d").replace(tzinfo=timezone.utc),
        ),
        crop_type="corn",
        crop_usage=CropUsage.COMMODITY,
    )


def fallow_period(field: Fields, start: str, end: str) -> MRVFallowPeriod:
    return MRVFallowPeriod(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime.strptime(start, "%Y-%m-%d").replace(tzinfo=timezone.utc),
            end=datetime.strptime(end, "%Y-%m-%d").replace(tzinfo=timezone.utc),
        ),
    )


def till_event(field: Fields, occurred_at: str) -> TillageEvent:
    return TillageEvent(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime.strptime(occurred_at, "%Y-%m-%d").replace(tzinfo=timezone.utc),
    )


def application_event(field: Fields, occurred_at: str) -> ApplicationEvent:
    return ApplicationEvent(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime.strptime(occurred_at, "%Y-%m-%d").replace(tzinfo=timezone.utc),
        products=[],
    )


def test_adjust_fallow_overlaps_with_events(field) -> None:
    # No overlap
    e_events = [com_crop_event(field, "2021-11-01", "2022-04-30"), fallow_period(field, "2022-05-01", "2022-10-01")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval.end == datetime(2022, 4, 30, tzinfo=timezone.utc)
    assert res_e_events[1].interval.start == datetime(2022, 5, 1, tzinfo=timezone.utc)

    # Previous crop overlaps
    e_events = [com_crop_event(field, "2021-11-01", "2022-01-01")]
    m_events = [fallow_period(field, "2022-01-01", "2022-12-31")]
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval.end == datetime(2022, 1, 1, tzinfo=timezone.utc)
    assert res_m_events[0].interval.start == datetime(2022, 1, 2, tzinfo=timezone.utc)

    # Subsequent crop overlaps
    e_events = [fallow_period(field, "2021-01-01", "2021-12-31")]
    m_events = [com_crop_event(field, "2021-12-31", "2022-06-01")]
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval.end == datetime(2021, 12, 30, tzinfo=timezone.utc)
    assert res_m_events[0].interval.start == datetime(2021, 12, 31, tzinfo=timezone.utc)

    # Multiple overlaps
    e_events = [com_crop_event(field, "2021-11-01", "2022-04-30"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = [com_crop_event(field, "2022-06-01", "2023-04-01")]
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 5, 1, tzinfo=timezone.utc), end=datetime(2022, 5, 31, tzinfo=timezone.utc)
    )

    # Crop wholly contained in fallow
    # It isn't worth splitting up the fallow in this case since that will usually just result in two very short
    # fallows. Fallows generated for migration will almost always be one year in length.
    e_events = [com_crop_event(field, "2022-05-01", "2022-10-30"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert len(res_e_events) == 1
    assert len(res_m_events) == 0
    assert isinstance(res_e_events[0], CroppingEvent)
    assert res_e_events[0].interval == e_events[0].interval

    # Fallow wholly contained in crop
    e_events = [com_crop_event(field, "2021-12-01", "2023-01-31"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert len(res_e_events) == 1
    assert len(res_m_events) == 0
    assert isinstance(res_e_events[0], CroppingEvent)
    assert res_e_events[0].interval == e_events[0].interval


def test_adjust_fallow_overlaps_with_events_till_overlapping(field) -> None:
    # A till event overlaps a fallow period, so the fallow period's end date is adjusted back.
    e_events = [till_event(field, "2022-11-01"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 10, 31, tzinfo=timezone.utc)
    )
    assert res_e_events[0].occurred_at == datetime(2022, 11, 1, tzinfo=timezone.utc)


def test_adjust_fallow_overlaps_with_events_application_overlapping(field) -> None:
    # An application event overlaps a fallow period, so the fallow period's end date is adjusted earlier.
    e_events = [application_event(field, "2022-11-01"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 10, 31, tzinfo=timezone.utc)
    )
    assert res_e_events[0].occurred_at == datetime(2022, 11, 1, tzinfo=timezone.utc)


def test_adjust_fallow_overlaps_with_events_till_ignored_overlap(field) -> None:
    # A till event overlaps a fallow period, but in the first half of the year, so it's ignored.
    e_events = [till_event(field, "2022-06-23"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 12, 31, tzinfo=timezone.utc)
    )
    assert res_e_events[0].occurred_at == datetime(2022, 6, 23, tzinfo=timezone.utc)


def test_adjust_fallow_overlaps_with_events_till_ignored_overlap_2(field) -> None:
    # Till event in the second half of the year
    # Till event doesn't overlap the fallow period, so is ignored.
    e_events = [till_event(field, "2022-07-23"), fallow_period(field, "2022-08-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 8, 1, tzinfo=timezone.utc), end=datetime(2022, 12, 31, tzinfo=timezone.utc)
    )
    assert res_e_events[0].occurred_at == datetime(2022, 7, 23, tzinfo=timezone.utc)


def test_adjust_fallow_overlaps_with_events_cover_crop_overlapping(field) -> None:
    # A cover event overlaps a fallow period, so the fallow period's end date is adjusted back.
    e_events = [fallow_period(field, "2022-01-01", "2022-12-31"), cov_crop_event(field, "2022-11-01", "2023-04-01")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 10, 31, tzinfo=timezone.utc)
    )
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 11, 1, tzinfo=timezone.utc), end=datetime(2023, 4, 1, tzinfo=timezone.utc)
    )


def test_adjust_fallow_overlaps_with_events_cover_crop_overlap_ignored(field) -> None:
    # A cover crop event overlaps a fallow period during the first half of the year, so no adjustment is made
    e_events = [cov_crop_event(field, "2021-11-01", "2022-04-01"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval == Interval(
        start=datetime(2021, 11, 1, tzinfo=timezone.utc), end=datetime(2022, 4, 1, tzinfo=timezone.utc)
    )
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 12, 31, tzinfo=timezone.utc)
    )


def test_adjust_fallow_overlaps_with_events_cover_crop_overlap_ignored_2(field) -> None:
    # A cover event overlaps a fallow period, so the fallow period's end date is adjusted back.
    e_events = [cov_crop_event(field, "2022-02-01", "2022-09-01"), fallow_period(field, "2022-01-01", "2022-12-31")]
    m_events = []
    res_e_events, res_m_events = _adjust_generated_fallow_overlaps_with_events(e_events, m_events)
    assert res_e_events[0].interval == Interval(
        start=datetime(2022, 2, 1, tzinfo=timezone.utc), end=datetime(2022, 9, 1, tzinfo=timezone.utc)
    )
    assert res_e_events[1].interval == Interval(
        start=datetime(2022, 1, 1, tzinfo=timezone.utc), end=datetime(2022, 12, 31, tzinfo=timezone.utc)
    )


async def test_convert_core_crop_names_to_regrow_names(mocker, create_cropping_event_data):
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.defaults_translator.translate_core_crop_name_to_regrow_name",
        mock_translate_core_crop_name_to_regrow_name,
    )
    crop_ev = CroppingEvent(**create_cropping_event_data(crop_type="peas"))
    await _convert_core_crop_names_to_regrow_names([crop_ev])
    assert crop_ev.crop_type == "pea"


async def test_convert_core_crop_names_to_regrow_names_beans(create_cropping_event_data):
    crop_ev = CroppingEvent(**create_cropping_event_data(crop_type="beans"))
    await _convert_core_crop_names_to_regrow_names([crop_ev])
    assert crop_ev.crop_type == "dry_bean"


async def test_convert_core_crop_names_to_regrow_names_full_cover(create_cropping_event_data):
    crop_ev = CroppingEvent(**create_cropping_event_data(crop_type="full_cover"))
    await _convert_core_crop_names_to_regrow_names([crop_ev])
    assert crop_ev.crop_type == "basic_cover_crop"


async def test_localize_and_repair_event_timestamps_midnights_and_noons(mdl, field):
    """Midnights and noons are not adjusted, regardless of timezone attached. Result is UTC noon on the same date."""
    datetime_midnight = datetime(2023, 10, 1, 0, 0, 0, tzinfo=timezone.utc)
    datetime_noon = datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)
    local_timezone = pytz.timezone("America/Chicago")
    datetime_midnight_tz = datetime(2023, 10, 1, 0, 0, 0, tzinfo=local_timezone)
    datetime_noon_tz = datetime(2023, 10, 1, 12, 0, 0, tzinfo=local_timezone)

    assert all(
        [
            await _localize_and_repair_event_timestamps(event_datetime=datetime_in, field_timezone=local_timezone)
            == datetime_noon
            for datetime_in in [datetime_midnight, datetime_midnight_tz, datetime_noon, datetime_noon_tz]
        ]
    )


async def test_localize_and_repair_event_timestamps_local_midnights(mdl, field):
    """If the stored datetime is a UTC representation of a midnight in local time, then that midnight is simply
    converted to a noon UTC on the same date.
    """
    field_timezone = pytz.timezone("America/Chicago")
    event_datetime = datetime(2023, 10, 1, 5, 0, 0, tzinfo=timezone.utc)

    result = await _localize_and_repair_event_timestamps(event_datetime=event_datetime, field_timezone=field_timezone)

    assert result == datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)


async def test_localize_and_repair_event_timestamps_rounded_to_local_midnight(mdl, field):
    """If the stored datetime is a UTC representation of a non-midnight local time, then the datetime is first converted
    to local time, then rounded to the nearest midnight, then becomes a noon UTC for the same date.
    """
    field_timezone = pytz.timezone("America/Chicago")
    # 00:30 local time, gets rounded to previous midnight
    event_datetime = datetime(2023, 10, 1, 5, 30, 0, tzinfo=timezone.utc)
    # 11:30 local time, gets rounded to previous midnight
    event_datetime2 = datetime(2023, 10, 1, 16, 30, 0, tzinfo=timezone.utc)

    result = await _localize_and_repair_event_timestamps(event_datetime=event_datetime, field_timezone=field_timezone)

    assert result == datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)
    result2 = await _localize_and_repair_event_timestamps(event_datetime=event_datetime2, field_timezone=field_timezone)
    assert result2 == datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)


async def test_localize_and_repair_event_timestamps_rounded_to_local_midnight2(mdl, field):
    """Testing rounding to midnight for datetimes before local midnight for Sydney timezone."""
    field_timezone = pytz.timezone("Australia/Sydney")
    # 00:30 local time, gets rounded to previous midnight
    event_datetime = datetime(2023, 10, 1, 12, 30, 0, tzinfo=timezone.utc)
    # 11:30 local time, gets rounded to previous midnight
    event_datetime2 = datetime(2023, 10, 1, 1, 30, 0, tzinfo=timezone.utc)

    result = await _localize_and_repair_event_timestamps(event_datetime=event_datetime, field_timezone=field_timezone)

    assert result == datetime(2023, 10, 2, 12, 0, 0, tzinfo=timezone.utc)
    result2 = await _localize_and_repair_event_timestamps(event_datetime=event_datetime2, field_timezone=field_timezone)
    assert result2 == datetime(2023, 10, 2, 12, 0, 0, tzinfo=timezone.utc)


async def test_localize_and_repair_event_timestamps_noon(mdl, field):
    field_timezone = pytz.timezone("Europe/London")
    event_datetime = datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)

    result = await _localize_and_repair_event_timestamps(event_datetime=event_datetime, field_timezone=field_timezone)

    assert result == datetime(2023, 10, 1, 12, 0, 0, tzinfo=timezone.utc)


async def test_localize_and_repair_event_timestamps_non_standard_time(mdl, field):
    field_timezone = pytz.timezone("Australia/Sydney")
    event_datetime = datetime(2023, 10, 1, 15, 30, 0, tzinfo=timezone.utc)

    result = await _localize_and_repair_event_timestamps(event_datetime=event_datetime, field_timezone=field_timezone)

    assert result == datetime(2023, 10, 2, 12, 0, 0, tzinfo=timezone.utc)


async def test_get_timezone_for_field_boundary_centroid():
    # Taken from field 111430 in program 275
    sydney_geometry = {
        "type": "Polygon",
        "coordinates": [
            [
                [147.1454186684867, -34.38259099043741],
                [147.143517014776, -34.39159629071637],
                [147.1591576931895, -34.39354997021284],
                [147.1599659568464, -34.38995978299557],
            ]
        ],
    }
    sydney_tz = await _get_timezone_for_field_boundary_centroid(sydney_geometry)
    assert sydney_tz == pytz.timezone("Australia/Sydney")

    # Taken from field 131002 in program 1119
    indiana_geometry = {
        "type": "Polygon",
        "coordinates": [
            [
                [-86.84884826968469, 39.8500559525563],
                [-86.8465208815702, 39.8500146491585],
                [-86.84647209978669, 39.8507917808095],
                [-86.8464275620879, 39.************],
            ]
        ],
    }
    indiana_tz = await _get_timezone_for_field_boundary_centroid(indiana_geometry)
    assert indiana_tz == pytz.timezone("America/Indiana/Indianapolis")

    # Taken from field 126053 in program 1126
    paris_geometry = {
        "type": "Polygon",
        "coordinates": [[[3.318084, 49.421095], [3.31911, 49.420422], [3.319529, 49.420899], [3.319003, 49.421149]]],
    }
    paris_tz = await _get_timezone_for_field_boundary_centroid(paris_geometry)
    assert paris_tz == pytz.timezone("Europe/Paris")
