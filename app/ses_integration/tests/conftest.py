import uuid
from copy import deepcopy
from datetime import datetime, timedelta, timezone
from typing import Any

import pytest
from regrow.ses.application.v1.application_pb2 import ApplicationActivity
from regrow.ses.context.v1.context_pb2 import EventContext
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from regrow.ses.pbtype.depth_pb2 import DepthMeasure
from regrow.ses.pbtype.line_pb2 import LineUnit
from ses_client.application import ApplicationSubsurface
from ses_client.crop import (
    crop_id_from_label,
    crop_purpose_commodity_harvest,
    HarvestedCrop,
    SownCrop,
    TerminatedCrop,
)
from ses_client.event import new_event_id
from ses_client.fallow import FallowPeriod as SESFallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.irrigation import IrrigationActivity
from ses_client.pb_value import kilograms_per_hectare
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    IrrigationMethods,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import ApplicationInput, ApplicationRate
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Depth, Interval
from entity_events.units import AreaUnit, LengthUnit, MassUnit
from fields.enums import FieldStatus
from fields.model import Fields
from programs.enums import UnitsTypes
from programs.model import Programs
from projects.enums import ProjectStatus
from values.enums import EntityTypeChoices


@pytest.fixture(scope="function")
async def field(mdl, program) -> Fields:
    project = await mdl.Projects(program_id=program.id, status=ProjectStatus.enrolled)
    return await mdl.Fields(status=FieldStatus.enrolled, parent_project_id=project.id)


@pytest.fixture(scope="function")
async def program(mdl) -> Programs:
    return await mdl.Programs(units=UnitsTypes.US_IMPERIAL)


@pytest.fixture(scope="function")
async def field2(mdl, program2) -> Fields:
    project = await mdl.Projects(program_id=program2.id, status=ProjectStatus.enrolled)
    return await mdl.Fields(status=FieldStatus.enrolled, parent_project_id=project.id)


@pytest.fixture(scope="function")
async def program2(mdl) -> Programs:
    return await mdl.Programs()


@pytest.fixture(scope="function")
def sowing_activity() -> SowingActivity:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    sown_crop = SownCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        SowingActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(sown_crop)
        .start(datetime(year=2020, month=1, day=1))
    )


@pytest.fixture(scope="function")
def sowing_activity_and_context() -> tuple[Event, EventContext]:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    sown_crop = SownCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        SowingActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(sown_crop)
        .start(datetime(year=2020, month=1, day=1))
        .event_and_context_pb()
    )


@pytest.fixture(scope="function")
def sowing_activity_upsert_response(sowing_activity_and_context: tuple[Event, EventContext]) -> UpsertEventResponse:
    return UpsertEventResponse(event=sowing_activity_and_context[0], context=sowing_activity_and_context[1])


@pytest.fixture(scope="function")
def upsert_sowing_activity_response(sowing_activity_and_context) -> UpsertEventResponse:
    return UpsertEventResponse(event=sowing_activity_and_context[0], context=sowing_activity_and_context[1])


@pytest.fixture(scope="function")
def harvest_activity_and_context() -> tuple[Event, EventContext]:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    harvest_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvest_crop)
        .start(datetime(year=2021, month=12, day=1))
        .event_and_context_pb()
    )


@pytest.fixture(scope="function")
def harvest_activity() -> HarvestActivity:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    harvest_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvest_crop)
        .start(datetime(year=2021, month=12, day=1))
    )


@pytest.fixture(scope="function")
def harvest_activity_upsert_response(harvest_activity_and_context: tuple[Event, EventContext]) -> UpsertEventResponse:
    return UpsertEventResponse(event=harvest_activity_and_context[0], context=harvest_activity_and_context[1])


@pytest.fixture(scope="function")
def termination_activity_and_context() -> tuple[Event, EventContext]:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    harvest_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        TerminationActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvest_crop)
        .start(datetime(year=2021, month=12, day=1))
        .event_and_context_pb()
    )


@pytest.fixture(scope="function")
def termination_activity() -> TerminationActivity:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    harvest_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    return (
        TerminationActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvest_crop)
        .end(datetime(year=2021, month=12, day=1))
    )


@pytest.fixture(scope="function")
def termination_activity_upsert_response(
    termination_activity_and_context: tuple[Event, EventContext],
) -> UpsertEventResponse:
    return UpsertEventResponse(event=termination_activity_and_context[0], context=termination_activity_and_context[1])


@pytest.fixture
def ses_fallow_period() -> SESFallowPeriod:
    return (
        SESFallowPeriod(event_id=str(uuid.uuid4()), user_id="1").start(datetime(2021, 5, 1)).end(datetime(2021, 10, 1))
    )


@pytest.fixture(scope="function")
def fallow_period_and_context() -> tuple[Event, EventContext]:
    return (
        SESFallowPeriod(event_id=str(uuid.uuid4()), user_id="1")
        .start(datetime(2021, 5, 1))
        .end(datetime(2021, 10, 1))
        .event_and_context_pb()
    )


@pytest.fixture(scope="function")
def fallow_period_upsert_response(
    fallow_period_and_context: tuple[Event, EventContext],
) -> UpsertEventResponse:
    return UpsertEventResponse(event=fallow_period_and_context[0], context=fallow_period_and_context[1])


@pytest.fixture(scope="function")
def application_activity() -> ApplicationActivity:
    uan = BasicFertiliser(input_name="urea_ammonium_nitrate_28").mass_rate(kilograms_per_hectare(150)).pb()
    return (
        ApplicationSubsurface(event_id=new_event_id(), user_id="test-user-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .start(datetime(2021, 2, 1, 0, 0, tzinfo=timezone.utc))
        .input(uan)
    )


@pytest.fixture(scope="function")
def application_activity_upsert_response(
    application_activity_and_context: tuple[Event, EventContext],
) -> UpsertEventResponse:
    return UpsertEventResponse(event=application_activity_and_context[0], context=application_activity_and_context[1])


@pytest.fixture(scope="function")
def application_activity_and_context() -> tuple[Event, EventContext]:
    uan = BasicFertiliser(input_name="urea_ammonium_nitrate_28").mass_rate(kilograms_per_hectare(150)).pb()
    return (
        ApplicationSubsurface(event_id=new_event_id(), user_id="migration-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .soil_depth(DepthMeasure(value=5.0, unit=LineUnit.LINE_UNIT_CENTIMETRE))
        .start(datetime(2022, 2, 1, 0, 0, tzinfo=timezone.utc))
        .input(uan)
        .event_and_context_pb()
    )


@pytest.fixture
def tillage_activity() -> TillageActivity:
    return (
        TillageActivity(event_id=new_event_id(), user_id="test-user-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .start(datetime(2021, 4, 1, 0, 0, tzinfo=timezone.utc))
    )


@pytest.fixture(scope="function")
def tillage_activity_upsert_response(
    tillage_activity_and_context: tuple[Event, EventContext],
) -> UpsertEventResponse:
    return UpsertEventResponse(event=tillage_activity_and_context[0], context=tillage_activity_and_context[1])


@pytest.fixture(scope="function")
def tillage_activity_and_context() -> tuple[Event, EventContext]:
    return (
        TillageActivity(event_id=new_event_id(), user_id="migration-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .start(datetime(2022, 4, 1, 0, 0, tzinfo=timezone.utc))
        .event_and_context_pb()
    )


@pytest.fixture
def irrigation_activity() -> IrrigationActivity:
    return (
        IrrigationActivity(event_id=new_event_id(), user_id="test-user-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .start(datetime(2021, 6, 1, 0, 0, tzinfo=timezone.utc))
        .end(datetime(2021, 9, 1, 0, 0, tzinfo=timezone.utc))
    )


@pytest.fixture(scope="function")
def irrigation_activity_upsert_response(
    irrigation_activity_and_context: tuple[Event, EventContext],
) -> UpsertEventResponse:
    return UpsertEventResponse(event=irrigation_activity_and_context[0], context=irrigation_activity_and_context[1])


@pytest.fixture(scope="function")
def irrigation_activity_and_context() -> tuple[Event, EventContext]:
    return (
        IrrigationActivity(event_id=new_event_id(), user_id="migration-123")
        .geom('{"type": "Polygon", "coordinates": [[[1, 1], [1, 2]]]}')
        .start(datetime(2021, 6, 1, 0, 0, tzinfo=timezone.utc))
        .end(datetime(2021, 9, 1, 0, 0, tzinfo=timezone.utc))
        .event_and_context_pb()
    )


@pytest.fixture(scope="function")
def application_event(field) -> ApplicationEvent:
    return ApplicationEvent(
        id=5,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2021, 2, 1, tzinfo=timezone.utc),
        method=ApplicationMethod.SUBSURFACE,
        depth=Depth(value=5.0, unit=LengthUnit.CENTIMETRE),
        products=[
            ApplicationInput(
                product_name="aqamm",
                application_rate=ApplicationRate(
                    value=5,
                    numerator_unit=MassUnit.POUND,
                    denominator_unit=AreaUnit.HECTARE,
                    rate_type=ApplicationRateType.NITROGEN_RATE,
                ),
            ),
            ApplicationInput(
                product_name="agrotain",
            ),
        ],
    )


@pytest.fixture()
def cropping_event(field) -> CroppingEvent:
    return CroppingEvent(
        id=4,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2020, 1, 1, tzinfo=timezone.utc), end=datetime(2021, 12, 1, tzinfo=timezone.utc)
        ),
        crop_type="dry_beans",
        crop_usage=CropUsage.COMMODITY,
    )


@pytest.fixture()
def intended_cropping_event(cropping_event) -> CroppingEvent:
    intended_cropping_event = deepcopy(cropping_event)
    intended_cropping_event.interval.start += timedelta(days=365)
    intended_cropping_event.interval.end += timedelta(days=365)
    intended_cropping_event.is_intended = True
    return intended_cropping_event


@pytest.fixture()
def fallow_period(field) -> FallowPeriod:
    return FallowPeriod(
        id=2,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2021, 5, 1, tzinfo=timezone.utc), end=datetime(2021, 10, 1, tzinfo=timezone.utc)
        ),
    )


@pytest.fixture()
def tillage_event(field) -> TillageEvent:
    return TillageEvent(
        id=2,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2021, 4, 1, tzinfo=timezone.utc),
        depth=Depth(value=5, unit=LengthUnit.CENTIMETRE),
    )


@pytest.fixture()
def irrigation_event(field) -> IrrigationEvent:
    return IrrigationEvent(
        id=2,  # This simulates an mrv_values.row_id being used as a row_id
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2021, 6, 1, tzinfo=timezone.utc), end=datetime(2021, 9, 1, tzinfo=timezone.utc)
        ),
        method=IrrigationMethods.sprinkler,
    )


@pytest.fixture()
async def geojson_boundary() -> dict[str, Any]:
    return {
        "type": "MultiPolygon",
        "coordinates": [
            [
                [
                    [-91.29233641703286, 41.30334338630458],
                    [-91.28745699999997, 41.305083999999745],
                    [-91.28756061103775, 41.299742821734874],
                    [-91.29232964157615, 41.29971351579836],
                    [-91.29233641703286, 41.30334338630458],
                ]
            ]
        ],
    }
