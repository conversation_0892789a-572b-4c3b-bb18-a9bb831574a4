import uuid

from ses_integration.migration.db import _set_migration_result_status
from ses_integration.migration.model import SESFieldMigrationStatus, SESMigrationResult


async def test_set_migration_result_status(app_request, mdl, orm_select, field, program) -> None:
    task_id = uuid.uuid4()
    await mdl.SESMigrationResult(
        field_id=field.id,
        program_id=program.id,
        project_id=field.parent_project_id,
        status=SESFieldMigrationStatus.enqueued,
        task_id=str(task_id),
    )
    await _set_migration_result_status(
        request=app_request, task_id=task_id, field_id=field.id, status=SESFieldMigrationStatus.failed
    )

    migration_results = await orm_select(SESMigrationResult)
    assert len(migration_results) == 1
    assert migration_results[0].status == SESFieldMigrationStatus.failed
