import json
import uuid
from datetime import datetime, timezone
from math import isclose
from uuid import UUID

from google.protobuf.json_format import MessageToJson
from regrow.ses.irrigation.v1.irrigation_pb2 import IrrigationMethod
from regrow.ses.pbtype.line_pb2 import LineUnit
from ses_client.application import ApplicationActivity, ApplicationSubsurface
from ses_client.event import StructuredEvent
from ses_client.fallow import FallowPeriod as SESFallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.irrigation import IrrigationActivity
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from boundaries_service import client as boundaries_client
from defaults.attribute_options import ApplicationMethod
from defaults.tests import mock_defaults_translator
from entity_events.units import LengthUnit
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from programs.enums import UnitsTypes
from ses_integration.migration.migrate_mrv_values_to_ses import (
    _apply_measurement_system_to_default_depth_units,
    _calculate_ses_event_id_hash,
    _migrate_field_batch_events_to_ses,
    _set_ses_event_id_to_hash,
    migrate_program_data_to_ses,
)
from ses_integration.migration.model import SESFieldMigrationStatus, SESMigrationResult
from ses_integration.migration.schema import SESMigrationSpecification


def one_matches(inputs, predicate) -> bool:
    return len([inp for inp in inputs if predicate(inp)]) == 1


def mock_regrow_name_translations(mocker):
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
    )
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.defaults_translator.translate_core_product_type_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_product_type_to_regrow_name,
    )


async def test_migrate_program_data_to_ses(mocker, mdl, app_request, orm_select, program, field):
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    mock_migrate_field_batch = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses._migrate_field_batch_events_to_ses.delay"
    )
    await migrate_program_data_to_ses(
        request=app_request,
        program_id=program.id,
        project_ids=[field.parent_project_id],
        field_ids=None,
        generate_fallow_dates=True,
    )
    fields = mock_migrate_field_batch.call_args_list[0].kwargs["field_ids"]
    assert fields[0] == field.id
    spec = mock_migrate_field_batch.call_args_list[0].kwargs["specification"]
    assert UUID(str(spec.task_id)) == spec.task_id
    assert spec.program_id == program.id
    assert spec.e_phase_id == e_phase.id
    assert spec.m_phase_id == m_phase.id
    assert spec.program_measurement_system == program.units

    migration_results = await orm_select(SESMigrationResult)
    assert len(migration_results) == 1
    assert migration_results[0].status == SESFieldMigrationStatus.enqueued
    task_id = migration_results[0].task_id
    assert str(UUID(task_id)) == task_id
    assert migration_results[0].field_id == field.id


async def test_migrate_program_data_to_ses_specific_field(mocker, mdl, app_request, orm_select, program, field):
    another_project = await mdl.Projects(program_id=program.id)
    # This field will be ignored since not specified in field_ids
    await mdl.Fields(parent_project_id=another_project.id, status=FieldStatus.enrolled)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    mock_migrate_field_batch = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses._migrate_field_batch_events_to_ses.delay"
    )
    await migrate_program_data_to_ses(
        request=app_request,
        program_id=program.id,
        project_ids=[field.parent_project_id, another_project.id],
        field_ids=[field.id],
        generate_fallow_dates=True,
    )
    fields = mock_migrate_field_batch.call_args_list[0].kwargs["field_ids"]
    assert fields[0] == field.id
    spec = mock_migrate_field_batch.call_args_list[0].kwargs["specification"]
    assert UUID(str(spec.task_id)) == spec.task_id
    assert spec.program_id == program.id
    assert spec.e_phase_id == e_phase.id
    assert spec.m_phase_id == m_phase.id
    assert spec.program_measurement_system == program.units

    migration_results = await orm_select(SESMigrationResult)
    assert len(migration_results) == 1
    assert migration_results[0].status == SESFieldMigrationStatus.enqueued
    task_id = migration_results[0].task_id
    assert str(UUID(task_id)) == task_id
    assert migration_results[0].field_id == field.id


async def test_migrate_field_batch_events_to_ses(
    mdl,
    field,
    program,
    mocker,
    geojson_boundary,
    sowing_activity_upsert_response,
    harvest_activity_upsert_response,
    termination_activity_upsert_response,
    application_activity_upsert_response,
    cropping_event,
    intended_cropping_event,
    application_event,
    orm_select,
    app_request,
) -> None:
    crop_type = cropping_event.crop_type  # Set this aside since it will be mutated
    product_name = application_event.products[0].product_name  # Set this aside since it will be mutated
    additive_name = application_event.products[1].product_name  # Set this aside since it will be mutated
    # Unenrolled fields should not be migrated
    await mdl.Fields(parent_project_id=field.parent_project_id, status=FieldStatus.registered)
    mock_get_entity_events = mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.entity_events_methods.get_entity_events_for_project_phases",
        return_value={
            PhaseTypes.ENROLMENT: [cropping_event, intended_cropping_event],
            PhaseTypes.MONITORING: [application_event],
        },
    )
    mock_regrow_name_translations(mocker)
    mock_get_geometries_for_fields(field.id, geojson_boundary, mocker)
    mock_set_event_date_timezones(mocker)
    mock_npos_map(program.id, mocker)

    mock_upsert_event = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.ses_client.upsert_event",
        side_effect=[
            sowing_activity_upsert_response,
            harvest_activity_upsert_response,
            termination_activity_upsert_response,
            application_activity_upsert_response,
        ],
    )

    mock_update_associations = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.associate_events_with_fields_of_md5"
    )
    mock_manually_update_associations = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.manually_associate_event_with_phase"
    )

    e_phase_id = 42
    m_phase_id = 43
    task_id = uuid.uuid4()
    await mdl.ProjectPermissions(user=1, project=field.parent_project_id)
    await mdl.SESMigrationResult(
        field_id=field.id,
        program_id=program.id,
        project_id=field.parent_project_id,
        status=SESFieldMigrationStatus.enqueued,
        task_id=str(task_id),
    )

    _migrate_field_batch_events_to_ses(
        field_ids=[field.id],
        specification=SESMigrationSpecification(
            acting_user_id=f"migration-{program.id}",
            owner_user_id="1",
            program_id=program.id,
            program_measurement_system=UnitsTypes.US_IMPERIAL,
            task_id=task_id,
            e_phase_id=e_phase_id,
            m_phase_id=m_phase_id,
            generate_fallow_dates=True,
        ),
        fs_user_id=1,
        request=app_request,
    )

    assert (
        mock_get_entity_events.call_args_list[0].kwargs["event_creation_specification"].auto_assign_fallow_dates is True
    )

    provided_new_events = [call.kwargs["structured_event"] for call in mock_upsert_event.call_args_list]
    assert len(provided_new_events) == 4  # The intended cropping event has been filtered out
    for ev in provided_new_events:
        assert str(uuid.UUID(ev.pb_event.id)) == ev.pb_event.id  # Confirm the ID is a valid UUID
        assert "1" == ev.pb_context.source.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID)
        assert "1" == ev.pb_context.creation.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID)
    expected_crop_type = (
        (await mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name(crop_type))
        .title()
        .replace("_", " ")
    )
    expected_product_name = await mock_defaults_translator.mock_translate_core_product_type_to_regrow_name(product_name)
    expected_additive_name = await mock_defaults_translator.mock_translate_core_product_type_to_regrow_name(
        additive_name
    )
    sowing_activity = next(ev for ev in provided_new_events if isinstance(ev, SowingActivity))
    assert sowing_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 4, SowingActivity.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    assert sowing_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2020, 1, 1, tzinfo=timezone.utc
    )
    assert sowing_activity.pb_event.sowing_activity.crops[0].crop.name == expected_crop_type
    harvest_activity = next(ev for ev in provided_new_events if isinstance(ev, HarvestActivity))
    assert harvest_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 12, 1, tzinfo=timezone.utc
    )
    assert harvest_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 4, HarvestActivity.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    assert harvest_activity.pb_event.harvest_activity.crops[0].crop.name == expected_crop_type
    assert json.loads(
        harvest_activity.pb_context.association.get(StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION)
    ) == {
        "TillageEvent": True,
        "IrrigationEvent": True,
        "ApplicationEvent": False,
    }

    termination_activity = next(ev for ev in provided_new_events if isinstance(ev, TerminationActivity))
    assert termination_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 12, 1, tzinfo=timezone.utc
    )
    assert termination_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 4, TerminationActivity.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    assert termination_activity.pb_event.termination_activity.crops[0].crop.name == expected_crop_type
    application_activity = next(ev for ev in provided_new_events if isinstance(ev, ApplicationActivity))
    assert application_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 2, 1, tzinfo=timezone.utc
    )
    assert application_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 5, ApplicationSubsurface.__name__, phase_type=PhaseTypes.MONITORING
    )
    # Application depth should have been converted to inches
    assert application_activity.pb_event.application_activity.subsurface.depth.value == 2.0
    assert application_activity.pb_event.application_activity.subsurface.depth.unit == LineUnit.LINE_UNIT_INCH
    assert one_matches(
        application_activity.pb_event.application_activity.inputs,
        lambda input: input.basic_fertiliser.name == expected_product_name,
    )
    assert one_matches(
        application_activity.pb_event.application_activity.inputs,
        lambda input: input.fertiliser_additive.name == expected_additive_name,
    )

    assert mock_manually_update_associations.call_count == 4
    for exp_event_id, exp_phase_id in [
        (uuid.UUID(sowing_activity_upsert_response.event.id), e_phase_id),
        (uuid.UUID(harvest_activity_upsert_response.event.id), e_phase_id),
        (uuid.UUID(termination_activity_upsert_response.event.id), e_phase_id),
        (uuid.UUID(application_activity_upsert_response.event.id), m_phase_id),
    ]:
        assert any(
            [
                call_args.kwargs["event_id"] == exp_event_id and call_args.kwargs["phase_id"] == exp_phase_id
                for call_args in mock_manually_update_associations.call_args_list
            ]
        )

    assert mock_update_associations.call_count == 1
    for exp_event_id in [
        uuid.UUID(sowing_activity_upsert_response.event.id),
        uuid.UUID(harvest_activity_upsert_response.event.id),
        uuid.UUID(termination_activity_upsert_response.event.id),
        uuid.UUID(application_activity_upsert_response.event.id),
    ]:
        assert any(
            [ev.event_id == exp_event_id for ev in mock_update_associations.call_args_list[0].kwargs["event_revisions"]]
        )

    migration_results = await orm_select(SESMigrationResult)
    assert len(migration_results) == 1
    assert migration_results[0].status == SESFieldMigrationStatus.succeeded
    assert migration_results[0].task_id == str(task_id)
    assert migration_results[0].field_id == field.id


async def test_migrate_field_batch_events_to_ses_fallow(
    mdl,
    field,
    program,
    mocker,
    geojson_boundary,
    fallow_period_upsert_response,
    tillage_activity_upsert_response,
    irrigation_activity_upsert_response,
    fallow_period,
    tillage_event,
    irrigation_event,
    orm_select,
    app_request,
) -> None:
    # Unenrolled fields should not be migrated
    await mdl.Fields(parent_project_id=field.parent_project_id, status=FieldStatus.registered)
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.entity_events_methods.get_entity_events_for_project_phases",
        return_value={
            PhaseTypes.ENROLMENT: [fallow_period, tillage_event, irrigation_event],
            PhaseTypes.MONITORING: [],
        },
    )
    mock_regrow_name_translations(mocker)
    mock_get_geometries_for_fields(field.id, geojson_boundary, mocker)
    mock_set_event_date_timezones(mocker)
    mock_npos_map(program.id, mocker)

    mock_upsert_event = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.ses_client.upsert_event",
        side_effect=[
            fallow_period_upsert_response,
            tillage_activity_upsert_response,
            irrigation_activity_upsert_response,
        ],
    )

    mock_update_associations = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.associate_events_with_fields_of_md5"
    )
    mock_manually_update_associations = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.manually_associate_event_with_phase"
    )

    e_phase_id = 42
    m_phase_id = 43
    task_id = uuid.uuid4()
    await mdl.ProjectPermissions(user=1, project=field.parent_project_id)
    await mdl.SESMigrationResult(
        field_id=field.id,
        program_id=program.id,
        project_id=field.parent_project_id,
        status=SESFieldMigrationStatus.enqueued,
        task_id=str(task_id),
    )
    _migrate_field_batch_events_to_ses(
        field_ids=[field.id],
        specification=SESMigrationSpecification(
            acting_user_id=f"migration-{program.id}",
            owner_user_id=None,
            program_id=program.id,
            program_measurement_system=UnitsTypes.US_IMPERIAL,
            task_id=task_id,
            e_phase_id=e_phase_id,
            m_phase_id=m_phase_id,
            generate_fallow_dates=True,
        ),
        fs_user_id=1,
        request=app_request,
    )
    provided_new_events = [call.kwargs["structured_event"] for call in mock_upsert_event.call_args_list]
    for ev in provided_new_events:
        assert str(uuid.UUID(ev.pb_event.id)) == ev.pb_event.id  # Confirm the ID is a valid UUID
        assert "1" == ev.pb_context.source.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID)
        assert "1" == ev.pb_context.creation.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID)
    fallow_period = next(ev for ev in provided_new_events if isinstance(ev, SESFallowPeriod))
    assert fallow_period.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 2, SESFallowPeriod.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    assert fallow_period.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 5, 1, tzinfo=timezone.utc
    )
    assert fallow_period.pb_event.interval.end_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 10, 1, tzinfo=timezone.utc
    )
    assert json.loads(
        fallow_period.pb_context.association.get(StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION)
    ) == {
        "TillageEvent": False,
        "IrrigationEvent": False,
        "ApplicationEvent": True,
    }
    tillage_activity = next(ev for ev in provided_new_events if isinstance(ev, TillageActivity))
    assert tillage_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 4, 1, tzinfo=timezone.utc
    )
    assert tillage_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 2, TillageActivity.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    # Tillage depth should have been converted from cm to inch
    assert tillage_activity.pb_event.tillage_activity.depth.value == 2.0
    assert tillage_activity.pb_event.tillage_activity.depth.unit == LineUnit.LINE_UNIT_INCH

    irrigation_activity = next(ev for ev in provided_new_events if isinstance(ev, IrrigationActivity))
    assert irrigation_activity.pb_event.interval.start_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 6, 1, tzinfo=timezone.utc
    )
    assert irrigation_activity.pb_event.interval.end_time.ToDatetime(tzinfo=timezone.utc) == datetime(
        2021, 9, 1, tzinfo=timezone.utc
    )
    assert irrigation_activity.pb_event.id == _calculate_ses_event_id_hash(
        field.id, 2, IrrigationActivity.__name__, phase_type=PhaseTypes.ENROLMENT
    )
    assert irrigation_activity.pb_event.irrigation_activity.method == IrrigationMethod.IRRIGATION_METHOD_SPRINKLER

    assert mock_manually_update_associations.call_count == 3
    for exp_event_id, exp_phase_id in [
        (uuid.UUID(fallow_period_upsert_response.event.id), e_phase_id),
        (uuid.UUID(tillage_activity_upsert_response.event.id), e_phase_id),
        (uuid.UUID(irrigation_activity_upsert_response.event.id), e_phase_id),
    ]:
        assert any(
            [
                call_args.kwargs["event_id"] == exp_event_id and call_args.kwargs["phase_id"] == exp_phase_id
                for call_args in mock_manually_update_associations.call_args_list
            ]
        )

    assert mock_update_associations.call_count == 1
    for exp_event_id in [
        uuid.UUID(fallow_period_upsert_response.event.id),
        uuid.UUID(tillage_activity_upsert_response.event.id),
        uuid.UUID(irrigation_activity_upsert_response.event.id),
    ]:
        assert any(
            [ev.event_id == exp_event_id for ev in mock_update_associations.call_args_list[0].kwargs["event_revisions"]]
        )


async def test_apply_measurement_system_to_tillage_depth_no_conversion_from_in(tillage_event) -> None:
    """
    Case: tillage depth is already inches
    Behavior: no conversion is applied
    """
    tillage_event.depth.value = 5.0
    tillage_event.depth.unit = LengthUnit.INCH
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert tillage_event.depth.value == 5.0
    assert tillage_event.depth.unit == LengthUnit.INCH

    # We only expect to see inches if the user explicitly set this, so do not convert these.
    _apply_measurement_system_to_default_depth_units(UnitsTypes.METRIC, [tillage_event])
    assert tillage_event.depth.value == 5.0
    assert tillage_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_tillage_depth_conversion_from_cm(tillage_event) -> None:
    """
    Case: program has its default Measurement System set to Imperial
    Behavior: converts centimeter till depths to inches
    Rounds to tenths because that's the precision of the existing data."""
    tillage_event.depth.value = 5.0
    tillage_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.METRIC, [tillage_event])
    assert tillage_event.depth.value == 5.0
    assert tillage_event.depth.unit == LengthUnit.CENTIMETRE

    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert isclose(tillage_event.depth.value, 2.0, abs_tol=0.0001)
    assert tillage_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_application_depth_conversion_from_cm(application_event) -> None:
    """
    Case: program has its default Measurement System set to Imperial
    Behavior: converts centimeter application depths to inches
    Rounds to tenths because that's the precision of the existing data.
    """
    application_event.method = ApplicationMethod.SUBSURFACE
    application_event.depth.value = 5.0
    application_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.METRIC, [application_event])
    assert application_event.depth.value == 5.0
    assert application_event.depth.unit == LengthUnit.CENTIMETRE

    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [application_event])
    assert application_event.depth.value == 2.0  # Rounded from 1.9685...
    assert application_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_application_depth_no_conversion_from_in(application_event) -> None:
    """
    Case: application depth is already inches
    Behavior: no unit conversion applied
    """
    application_event.method = ApplicationMethod.SUBSURFACE
    application_event.depth.value = 5.0
    application_event.depth.unit = LengthUnit.INCH
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [application_event])
    assert application_event.depth.value == 5.0
    assert application_event.depth.unit == LengthUnit.INCH

    # We only expect to see inches if the user explicitly set this, so do not convert these.
    _apply_measurement_system_to_default_depth_units(UnitsTypes.METRIC, [application_event])
    assert application_event.depth.value == 5.0
    assert application_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_depth_rounds_tenths(tillage_event) -> None:
    """
    Case: converted value is not within 0.1 of a round number
    Behavior: rounds to tenths

    This is the default rounding because .1 was the precision of the MRV Values storage.
    Because the behavior for application depth is shared, we don't test them separately.
    """
    tillage_event.depth.value = 12.9794  # 5.11 IN -> CM
    tillage_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert isclose(tillage_event.depth.value, 5.1, abs_tol=0.001)
    assert tillage_event.depth.unit == LengthUnit.INCH

    tillage_event.depth.value = 12.4206  # 4.89 IN -> CM
    tillage_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert isclose(tillage_event.depth.value, 4.9, abs_tol=0.001)
    assert tillage_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_depth_rounds_to_whole(tillage_event) -> None:
    """
    Case: converted value is within 0.1 of a whole number
    Behavior: value rounded to whole number
    """
    tillage_event.depth.value = 12.9286  # 5.09 IN -> CM
    tillage_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert tillage_event.depth.value == 5
    assert tillage_event.depth.unit == LengthUnit.INCH

    tillage_event.depth.value = 12.4714  # 4.91 IN -> CM
    tillage_event.depth.unit = LengthUnit.CENTIMETRE
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    # No rounding applied
    assert tillage_event.depth.value == 5
    assert tillage_event.depth.unit == LengthUnit.INCH


async def test_apply_measurement_system_to_tillage_depth_no_conversion_no_rounding(tillage_event) -> None:
    """
    Case: No depth unit conversion is necessary
    Behavior: No rounding is applied
    """
    tillage_event.depth.value = 5.0001
    tillage_event.depth.unit = LengthUnit.INCH
    _apply_measurement_system_to_default_depth_units(UnitsTypes.US_IMPERIAL, [tillage_event])
    assert tillage_event.depth.value == 5.0001
    assert tillage_event.depth.unit == LengthUnit.INCH

    # We only expect to see inches if the user explicitly set this, so do not convert these.
    _apply_measurement_system_to_default_depth_units(UnitsTypes.METRIC, [tillage_event])
    assert tillage_event.depth.value == 5.0001
    assert tillage_event.depth.unit == LengthUnit.INCH


async def test_migrate_field_batch_events_to_ses_failed_upsert(
    mdl,
    field,
    program,
    mocker,
    geojson_boundary,
    fallow_period_upsert_response,
    irrigation_activity_upsert_response,
    fallow_period,
    irrigation_event,
    orm_select,
    app_request,
) -> None:
    value_error = ValueError("Failed upsert")
    mock_upsert_event = mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.ses_client.upsert_event",
        side_effect=[fallow_period_upsert_response, value_error],
    )
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration.entity_events_methods.get_entity_events_for_project_phases",
        return_value={PhaseTypes.ENROLMENT: [fallow_period], PhaseTypes.MONITORING: [irrigation_event]},
    )
    mock_get_geometries_for_fields(field.id, geojson_boundary, mocker)
    mock_set_event_date_timezones(mocker)
    mock_npos_map(program.id, mocker)
    mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.associate_events_with_fields_of_md5"
    )
    mocker.patch(
        "ses_integration.migration.migrate_mrv_values_to_ses.event_associations.manually_associate_event_with_phase"
    )
    mock_log = mocker.patch("ses_integration.migration.migrate_mrv_values_to_ses.logger.error")

    await mdl.ProjectPermissions(user=1, project=field.parent_project_id)

    e_phase_id = 42
    m_phase_id = 43
    task_id = uuid.uuid4()
    await mdl.SESMigrationResult(
        field_id=field.id,
        program_id=program.id,
        project_id=field.parent_project_id,
        status=SESFieldMigrationStatus.enqueued,
        task_id=str(task_id),
    )
    _migrate_field_batch_events_to_ses(
        field_ids=[field.id],
        specification=SESMigrationSpecification(
            acting_user_id=f"migration-{program.id}",
            owner_user_id=None,
            program_id=program.id,
            program_measurement_system=UnitsTypes.US_IMPERIAL,
            task_id=task_id,
            e_phase_id=e_phase_id,
            m_phase_id=m_phase_id,
            generate_fallow_dates=True,
        ),
        fs_user_id=1,
        request=app_request,
    )
    migration_results = await orm_select(SESMigrationResult)
    assert len(migration_results) == 1
    assert migration_results[0].status == SESFieldMigrationStatus.failed
    provided_new_events = [call.kwargs["structured_event"] for call in mock_upsert_event.call_args_list]
    mock_log.assert_called_with(
        f"SES Migration: Upserting events failed for field {field.id}, task {task_id}",
        exc_info=value_error,
        extra={
            "successfully_created_events": [MessageToJson(fallow_period_upsert_response)],
            "event_being_upserted": {
                "event": MessageToJson(provided_new_events[1].pb_event),
                "context": MessageToJson(provided_new_events[1].pb_context),
            },
        },
    )


def mock_get_geometries_for_fields(field_id, geojson_boundary, mocker):
    mocker.patch.object(
        boundaries_client,
        "get_geometries_for_fields",
        return_value={field_id: geojson_boundary},
    )


def mock_set_event_date_timezones(mocker):
    mocker.patch(
        "ses_integration.migration.build_mrv_events_for_migration._repair_dates_using_field_boundary_timezones"
    )


def mock_npos_map(expected_program_id, mocker):
    class MockNPODict(dict):
        def get(self, key, default=None):
            if key == expected_program_id:
                return {
                    StageTypes.IRRIGATION_EVENTS,
                    StageTypes.TILLAGE_EVENTS,
                    StageTypes.NUTRIENT_EVENTS,
                }
            return {}

        def __contains__(self, key):
            return key == expected_program_id

    mocker.patch("ses_integration.migration.migrate_mrv_values_to_ses.NPO_STAGES_BY_PROGRAM", new=MockNPODict())


async def test_set_ses_event_id_to_hash(mdl, sowing_activity, harvest_activity) -> None:
    _set_ses_event_id_to_hash(sowing_activity, field_id=1, row_id=1, phase_type=PhaseTypes.ENROLMENT)
    initial_hash = sowing_activity.pb_event.id
    # Confirm ID is a UUID
    assert str(UUID(initial_hash)) == initial_hash

    # Ensure the generated hash is deterministic
    _set_ses_event_id_to_hash(sowing_activity, field_id=1, row_id=1, phase_type=PhaseTypes.ENROLMENT)
    assert sowing_activity.pb_event.id == initial_hash

    _set_ses_event_id_to_hash(sowing_activity, field_id=2, row_id=1, phase_type=PhaseTypes.ENROLMENT)
    assert initial_hash != sowing_activity.pb_event.id

    # Ensure different event types generate different IDs
    _set_ses_event_id_to_hash(harvest_activity, field_id=1, row_id=1, phase_type=PhaseTypes.ENROLMENT)
    assert initial_hash != harvest_activity.pb_event.id

    # Different row_ids generate different IDs
    prev_harvest_hash = harvest_activity.pb_event.id
    _set_ses_event_id_to_hash(harvest_activity, field_id=1, row_id=2, phase_type=PhaseTypes.ENROLMENT)
    assert prev_harvest_hash != harvest_activity.pb_event.id

    # Different phase_type generates different IDs
    prev_harvest_hash = harvest_activity.pb_event.id
    _set_ses_event_id_to_hash(harvest_activity, field_id=1, row_id=2, phase_type=PhaseTypes.MONITORING)
    assert prev_harvest_hash != harvest_activity.pb_event.id
