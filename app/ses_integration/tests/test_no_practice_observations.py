import json

import pytest
from fastapi import HTT<PERSON>Exception
from regrow.ses.context.v1.context_pb2 import Event<PERSON>ontext
from ses_client.event import StructuredEvent

from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.enums import EntityEventType
from phases.enums import StageTypes
from ses_integration.no_practice_observations import (
    get_and_validate_no_practice_observation_stage_type_by_stage_id,
    update_no_practice_observations_in_ses_structured_event_context,
)


async def test_get_and_validate_no_practice_observation_stage_type_by_stage_id(app_request, mdl):
    crop_stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    tillage_stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    nutrient_stage = await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS)
    irrigation_stage = await mdl.Stage(type_=StageTypes.IRRIGATION_EVENTS)

    with pytest.raises(HTTPException):
        await get_and_validate_no_practice_observation_stage_type_by_stage_id(
            request=app_request, stage_id=crop_stage.id
        )

    assert (
        await get_and_validate_no_practice_observation_stage_type_by_stage_id(
            request=app_request, stage_id=tillage_stage.id
        )
        == StageTypes.TILLAGE_EVENTS
    )

    assert (
        await get_and_validate_no_practice_observation_stage_type_by_stage_id(
            request=app_request, stage_id=nutrient_stage.id
        )
        == StageTypes.NUTRIENT_EVENTS
    )

    assert (
        await get_and_validate_no_practice_observation_stage_type_by_stage_id(
            request=app_request, stage_id=irrigation_stage.id
        )
        == StageTypes.IRRIGATION_EVENTS
    )


async def test_update_no_practice_observations_in_ses_structured_event_context():
    res = await update_no_practice_observations_in_ses_structured_event_context(
        no_practice_observations={EntityEventType.TILLAGE_EVENT: False},
        context=EventContext(
            association={
                StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                    {
                        EntityEventType.TILLAGE_EVENT: True,
                        EntityEventType.APPLICATION_EVENT: True,
                        EntityEventType.IRRIGATION_EVENT: True,
                    }
                ),
                CONTEXT_KEY_REGROW_OWNING_USER: "1",
                CONTEXT_KEY_REGROW_ACTING_USER: "1",
            }
        ),
        acting_user_id="1",
        owner_user_id="1",
    )
    assert res == EventContext(
        association={
            StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                {
                    EntityEventType.TILLAGE_EVENT: False,
                    EntityEventType.APPLICATION_EVENT: True,
                    EntityEventType.IRRIGATION_EVENT: True,
                }
            ),
            CONTEXT_KEY_REGROW_OWNING_USER: "1",
            CONTEXT_KEY_REGROW_ACTING_USER: "1",
        }
    )
