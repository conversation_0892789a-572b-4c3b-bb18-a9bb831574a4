from mocks import mock_cropping_sequences, mock_field_practice_events
from ses_client.event import StructuredEvent

from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from fields.enums import FieldStatus
from phases.enums import StageTypes
from ses_integration.methods import (
    get_ses_event_types_to_query,
    parse_ses_events_to_entity_events,
)


async def test_parse_ses_events_to_entity_events(mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    entity_events = parse_ses_events_to_entity_events(field, mock_cropping_sequences, mock_field_practice_events)
    assert len(entity_events) == len(mock_field_practice_events) + len(mock_cropping_sequences)
    assert all(entity_event.entity_id == field.id for entity_event in entity_events)
    assert all(isinstance(entity_event, EntityEvent) for entity_event in entity_events)


async def test_parse_ses_events_to_entity_events_cropping_events(mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    entity_events = parse_ses_events_to_entity_events(field, mock_cropping_sequences, [])

    assert len(entity_events) == len(mock_cropping_sequences)
    assert all(isinstance(entity_event, CroppingEvent) for entity_event in entity_events)


async def test_get_ses_event_types_to_query_for_stages(mdl):
    stages = [
        await mdl.Stage(type_=StageTypes.CROP_EVENTS),
        await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS),
        await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS),
    ]
    result = get_ses_event_types_to_query(stages=stages)
    assert result == [StructuredEvent.TYPE_TILLAGE_ACTIVITY, StructuredEvent.TYPE_APPLICATION_ACTIVITY]


async def test_get_ses_event_types_to_query_for_stage_type(mdl):
    result = get_ses_event_types_to_query(stages=[], stage_type=StageTypes.TILLAGE_EVENTS)
    assert result == [StructuredEvent.TYPE_TILLAGE_ACTIVITY]


async def test_get_ses_event_types_to_query_for_stage_type_empty():
    result = get_ses_event_types_to_query(stages=[], stage_type=StageTypes.CONFIRM_HISTORY)
    assert result == []
