from typing import Type

from ses_client.event import StructuredEvent

from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from phases.enums import PhaseTypes, StageTypes

MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE = 95

SES_EVENT_TO_ENTITY_EVENT: dict[str, Type[EntityEvent]] = {
    StructuredEvent.TYPE_APPLICATION_ACTIVITY: ApplicationEvent,
    StructuredEvent.TYPE_FALLOW_PERIOD: FallowPeriod,
    StructuredEvent.TYPE_HARVEST_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_IRRIGATION_ACTIVITY: IrrigationEvent,
    StructuredEvent.TYPE_PLANTING_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_SOWING_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_TILLAGE_ACTIVITY: TillageEvent,
}

STAGE_TYPE_TO_SES_EVENT: dict[StageTypes, list[Type[StructuredEvent]]] = {
    StageTypes.IRRIGATION_EVENTS: [StructuredEvent.TYPE_IRRIGATION_ACTIVITY],
    StageTypes.NUTRIENT_EVENTS: [StructuredEvent.TYPE_APPLICATION_ACTIVITY],
    StageTypes.TILLAGE_EVENTS: [StructuredEvent.TYPE_TILLAGE_ACTIVITY],
}

STAGE_TYPE_TO_ENTITY_EVENT: dict[StageTypes, list[Type[StructuredEvent]]] = {
    StageTypes.IRRIGATION_EVENTS: [IrrigationEvent],
    StageTypes.NUTRIENT_EVENTS: [ApplicationEvent],
    StageTypes.TILLAGE_EVENTS: [TillageEvent],
}

EVENT_ASSOCIABLE_PHASE_TYPES = {PhaseTypes.MONITORING, PhaseTypes.ENROLMENT}

NO_PRACTICE_OBSERVATION_STAGE_TYPES = [
    StageTypes.IRRIGATION_EVENTS,
    StageTypes.NUTRIENT_EVENTS,
    StageTypes.TILLAGE_EVENTS,
]

NO_PRACTICE_OBSERVATION_EVENT_TYPES = [
    StructuredEvent.TYPE_HARVEST_ACTIVITY,
    StructuredEvent.TYPE_FALLOW_PERIOD,
]
