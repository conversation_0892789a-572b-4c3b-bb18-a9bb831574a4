from sqlalchemy import (
    Column,
    ForeignKey,
    func,
    Integer,
    String,
    TIMESTAMP,
    UniqueConstraint,
)

from db.mysql import Base


class FieldEventAssociation(Base):
    __tablename__ = "mrv_field_event_associations"
    __table_args__ = (
        UniqueConstraint(
            "field_id",
            "ses_event_id",
            name="_field_event_uc",
        ),
    )
    id = Column(Integer, nullable=False, primary_key=True)
    ses_event_id = Column(String(36), nullable=False)
    field_md5 = Column(String(32), nullable=False, index=True)
    # Avoiding foreign keys to in-demand tables since they can cause excessive locking
    field_id = Column(Integer, nullable=False, index=True)
    project_id = Column(Integer, ForeignKey("mrv_projects.id"), nullable=False, index=True)
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=False)
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)


class PhaseEventAssociation(Base):
    __tablename__ = "mrv_phase_event_associations"
    __table_args__ = (
        UniqueConstraint(
            "field_event_association_id",
            "phase_id",
            name="_event_phase_uc",
        ),
    )
    id = Column(Integer, nullable=False, primary_key=True)
    field_event_association_id = Column(Integer, ForeignKey("mrv_field_event_associations.id"), nullable=False)
    phase_id = Column(Integer, ForeignKey("mrv_phases.id"), nullable=False)
    revision = Column(Integer, nullable=False)
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(
        TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp()
    )
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)
