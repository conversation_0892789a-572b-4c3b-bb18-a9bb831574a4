from fastapi import Request
from regrow.ses.event.v1 import event_service_pb2
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextRequest
from ses_client.client import Client
from ses_client.event import event_type, EventWithContext, StructuredEvent
from ses_client.sequence import cropping_sequence

import config
from entity_events.events.entity_event import EntityEvent
from fields.model import Fields
from fields.schema import Field
from phases.db import get_stages_by_program_id_and_phase_type
from phases.enums import PhaseTypes, StageTypes
from programs.db import get_program_from_field_id
from root_crud import get
from ses_integration import event_associations
from ses_integration.methods import parse_ses_events_to_entity_events
from ses_integration.schema import EventRevision

settings = config.get_settings()
ses_client = Client(settings.SES_INTERNAL_URL_BASE, settings.SES_SEARCH_INTERNAL_URL_BASE)


async def fetch_events_for_field_phase(
    request: Request, field_id: int, enrollment_phase_only: bool, is_single_phase_program: bool
) -> list[EntityEvent]:
    """
    Fetch events based on ids and revisions stored in event associations, for a particular field_id.

    Note it will filter out any events corresponding to a stage that is not enabled for the phase type. E.g. if
    the Irrigation stage is not enabled for the enrollment phase, then no irrigation events will be returned.

    Warning: Phase associations don't apply any date filtering, so Phases will be associated with any events that were
    edited before their completion. There will be many duplicate events between E and M Phases as long as this is the
    case.
    """
    phase_type = PhaseTypes.ENROLMENT if enrollment_phase_only or is_single_phase_program else PhaseTypes.MONITORING

    revisions = await _get_revisions(request=request, field_id=field_id, phase_type=phase_type)
    if not revisions:
        return []

    fetch_event_requests = [
        FetchEventWithContextRequest(id=str(revision.event_id), revision=str(revision.revision), merge_context=False)
        for revision in revisions
    ]
    all_events = await ses_client.fetch_event_revisions_with_context(fetch_event_requests=fetch_event_requests)

    cropping_events_with_context, noncropping_events = await _group_and_filter_events(
        request, field_id, phase_type, all_events
    )

    field = (await get.get(request=request, orm_type=Fields, type_=Field, id_field=Fields.id, ids=[field_id]))[0]
    return parse_ses_events_to_entity_events(
        field=field,
        cropping_events=cropping_events_with_context,
        field_practice_events=[EventWithContext(event=ev.event, context=ev.context) for ev in noncropping_events],
    )


async def _get_revisions(request: Request, field_id: int, phase_type: PhaseTypes) -> list[EventRevision]:
    revisions = await event_associations.get_event_revisions_for_field_phase(
        request=request, field_id=field_id, phase_type=phase_type
    )
    if not revisions:
        # import here to avoid circular import
        from ses_integration.reconcile_event_associations import (
            reconcile_event_associations_for_field,
        )

        # attempt to reconcile events from ses if no revisions are found
        await reconcile_event_associations_for_field(request=request, field_id=field_id)
        revisions = await event_associations.get_event_revisions_for_field_phase(
            request=request, field_id=field_id, phase_type=phase_type
        )
    return revisions


async def _group_and_filter_events(
    request: Request,
    field_id: int,
    phase_type: PhaseTypes,
    all_events: list[event_service_pb2.FetchEventWithContextResponse],
) -> tuple[
    list[tuple[EventWithContext | None, EventWithContext | None, EventWithContext | None]],
    list[event_service_pb2.FetchEventWithContextResponse],
]:
    program = await get_program_from_field_id(request=request, field_id=field_id)
    enabled_stages = {
        stage.type_
        for stage in await get_stages_by_program_id_and_phase_type(
            request=request, program_id=program.id, phase_type=phase_type
        )
    }
    cropping_stage_disabled = StageTypes.CROP_EVENTS not in enabled_stages
    tillage_stage_disabled = StageTypes.TILLAGE_EVENTS not in enabled_stages
    nutrient_stage_disabled = StageTypes.NUTRIENT_EVENTS not in enabled_stages
    irrigation_stage_disabled = StageTypes.IRRIGATION_EVENTS not in enabled_stages
    event_with_context_by_id = {ev.event.id: ev for ev in all_events}
    cropping_components = [
        ev.event
        for ev in all_events
        if event_type(ev.event)
        in {
            StructuredEvent.TYPE_SOWING_ACTIVITY,
            StructuredEvent.TYPE_PLANTING_ACTIVITY,
            StructuredEvent.TYPE_HARVEST_ACTIVITY,
            StructuredEvent.TYPE_TERMINATION_ACTIVITY,
        }
    ]
    cropping_events: list[tuple[Event | None, Event | None, Event | None]] = (
        [] if cropping_stage_disabled else cropping_sequence(cropping_components)
    )
    cropping_events_with_context = [
        (
            event_with_context_by_id.get(composite_crop_event[0].id) if composite_crop_event[0] else None,
            event_with_context_by_id.get(composite_crop_event[1].id) if composite_crop_event[1] else None,
            event_with_context_by_id.get(composite_crop_event[2].id) if composite_crop_event[2] else None,
        )
        for composite_crop_event in cropping_events
    ]
    noncropping_events = [
        ev
        for ev in all_events
        if ev.event.id not in {cr.id for cr in cropping_components}
        and not ("tillage_activity" in ev.event and tillage_stage_disabled)
        and not ("irrigation_activity" in ev.event and irrigation_stage_disabled)
        and not ("application_activity" in ev.event and nutrient_stage_disabled)
    ]
    return cropping_events_with_context, noncropping_events
