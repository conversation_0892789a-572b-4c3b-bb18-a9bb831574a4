from datetime import datetime

import elasticapm
from fastapi import APIRouter, HTTPException
from fastapi.params import Depends, Query
from pydantic.main import BaseModel
from starlette import status
from starlette.requests import Request

import ses_integration.reconcile_event_associations
from config import get_settings
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from phases.db import (
    get_phase_by_id,
    get_phase_date_window_by_type,
    get_stage_by_program_id_and_stage_type,
)
from phases.enums import PhaseTypes, StageTypes
from ui.projects.field_events.schema import BackfillNoPracticeObservationRequest
from ui.projects.field_events.tasks import (
    backfill_no_practice_observations_task,
    prefill_monitoring_phase_commodity_crops_task,
)

logger = get_logger(__name__)

super_admin_tags = ["super_admin"]
event_association_tags = ["event-associations"]
router = APIRouter(prefix="")

settings = get_settings()


class EventAssociationRefreshRequest(BaseModel):
    project_ids: list[int] | None = Query(None)
    field_ids: list[int] | None = Query(None)


@elasticapm.async_capture_span()
@router.post(
    "/programs/{program_id}/event-associations/reconcile",
    tags=super_admin_tags + event_association_tags,
    response_model=None,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.RECONCILE_PROJECT_FIELD_EVENTS]))],
    description="Refresh event associations for the given field IDs from SES. It will add any missing associations, "
    "update existing ones, and remove any that are no longer present in SES. May optionally be filtered by "
    "specific projects and/or fields. Note that for very large programs, this can take an exceedingly long time, so it "
    "is recommended to run for specific projects or fields in those cases.",
)
async def reconcile_event_associations(request: Request, program_id: int, body: EventAssociationRefreshRequest) -> None:
    await ses_integration.reconcile_event_associations.reconcile_event_associations_for_program(
        request, program_id, body.project_ids, body.field_ids
    )


@elasticapm.async_capture_span()
@router.post(
    "/programs/{program_id}/phases/{phase_id}/prefill-monitoring-phase-commodity-crops",
    response_model=None,
    tags=super_admin_tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.ADMIN_BULK_UPDATE_FIELD_EVENTS]))],
)
async def prefill_monitoring_phase_commodity_crops(request: Request, program_id: int, phase_id: int) -> None:
    e_phase_date_range = await get_phase_date_window_by_type(
        request=request, program_id=program_id, phase_type=PhaseTypes.ENROLMENT
    )
    if not e_phase_date_range or e_phase_date_range.end_date > datetime.now().date():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": f"Cannot prefill commodity crops for program {program_id} because E phase has not closed."
            },
        )
    phase = await get_phase_by_id(request=request, phase_id=phase_id)
    if not phase:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": f"Phase {phase_id} does not exist."},
        )
    if phase.type_ != PhaseTypes.MONITORING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": f"Cannot prefill commodity crops for non-M phase type {phase.type_}."},
        )
    intended_commodity_crops_stage = await get_stage_by_program_id_and_stage_type(
        request=request,
        program_id=program_id,
        stage_type=StageTypes.INTENDED_COMMODITY_CROPS,
    )
    if not intended_commodity_crops_stage:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": f"Program {program_id} does not have an intended commodity crops stage."},
        )
    prefill_monitoring_phase_commodity_crops_task.delay(
        program_id=program_id,
        intended_commodity_crops_stage_id=intended_commodity_crops_stage.id,
        m_phase_id=phase.id,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@elasticapm.async_capture_span()
@router.post(
    "/programs/{program_id}/backfill-no-practice-observations",
    response_model=None,
    tags=super_admin_tags,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.ADMIN_BULK_UPDATE_FIELD_EVENTS]))],
)
async def backfill_no_practice_observations(
    request: Request, program_id: int, body: BackfillNoPracticeObservationRequest
) -> None:
    backfill_no_practice_observations_task.delay(
        program_id=program_id,
        project_ids=body.project_ids,
        no_practice_observation_type=body.no_practice_observation_type,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )
