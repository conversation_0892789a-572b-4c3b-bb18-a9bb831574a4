from __future__ import annotations

import json

from fastapi import HTTPException, Request, status
from regrow.ses.context.v1.context_pb2 import EventContext
from ses_client.client import Client
from ses_client.event import event_type, StructuredEvent

from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.enums import EntityEventType
from fields.schema import Field
from phases.db import get_stage_by_id
from phases.enums import StageTypes
from ses_integration.constants import (
    NO_PRACTICE_OBSERVATION_EVENT_TYPES,
    NO_PRACTICE_OBSERVATION_STAGE_TYPES,
)
from ui.projects.field_events.schema import (
    NoPracticeObservations,
)


async def get_and_validate_no_practice_observation_stage_type_by_stage_id(
    request: Request, stage_id: int
) -> StageTypes:
    stage = await get_stage_by_id(request=request, stage_id=stage_id)
    if not stage:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Stage {stage_id} does not exist",
        )
    if stage.type_ not in NO_PRACTICE_OBSERVATION_STAGE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"NoPracticeObservation cannot be set on stage {stage_id} of type {stage.type_}",
        )
    return stage.type_


def get_no_practice_observations_from_ses_structured_event_context(
    event_context: EventContext,
) -> NoPracticeObservations | None:
    no_practice_observations = event_context.association.get(StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION)
    if not no_practice_observations:
        return None
    no_practice_observations_dict = json.loads(no_practice_observations)
    return NoPracticeObservations(
        tillage_event=no_practice_observations_dict.get(EntityEventType.TILLAGE_EVENT, False),
        application_event=no_practice_observations_dict.get(EntityEventType.APPLICATION_EVENT, False),
        irrigation_event=no_practice_observations_dict.get(EntityEventType.IRRIGATION_EVENT, False),
    )


async def update_no_practice_observations_in_ses_structured_event_context(
    no_practice_observations: dict[EntityEventType, bool],
    context: EventContext,
    acting_user_id: str,
    owner_user_id: str,
) -> EventContext:
    existing_no_practice_observations = context.association.get(StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION)
    if existing_no_practice_observations:
        no_practice_observations = json.loads(existing_no_practice_observations) | no_practice_observations
    context.association.update(
        {
            StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(no_practice_observations),
            CONTEXT_KEY_REGROW_ACTING_USER: acting_user_id,
            CONTEXT_KEY_REGROW_OWNING_USER: owner_user_id,
        }
    )
    return context


async def update_no_practice_observations_for_event(
    event_id: str,
    no_practice_observations: dict[EntityEventType, bool],
    field: Field,
    acting_user_id: str,
    owner_user_id: str,
    ses_client: Client,
) -> NoPracticeObservations:
    event_with_context = await ses_client.fetch_event_with_context(event_id=event_id)
    event = event_with_context.event
    context = event_with_context.context
    if event_type(event) not in NO_PRACTICE_OBSERVATION_EVENT_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"NoPracticeObservation cannot be set on event {event_id} of type {event_type(event)}",
        )
    updated_context = await update_no_practice_observations_in_ses_structured_event_context(
        no_practice_observations=no_practice_observations,
        context=context,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
    )
    res = await ses_client.add_field_event_context(field_id=field.md5, event_id=event_id, context=updated_context)
    if not res:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update event {event_id} with no practice observation",
        )
    return get_no_practice_observations_from_ses_structured_event_context(event_context=updated_context)
