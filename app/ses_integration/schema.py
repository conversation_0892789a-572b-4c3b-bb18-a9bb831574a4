import uuid

from pydantic import BaseModel


class SESEventsResponse(BaseModel):
    ses_event_id: str
    mrv_phase_id: int
    revision: int

    class Config:
        orm_mode = True


class SESEventsUpdate(BaseModel):
    revision: int

    class Config:
        orm_mode = True


class EventRevision(BaseModel):
    event_id: uuid.UUID
    revision: int


class AssociationField(BaseModel):
    field_md5: str
    field_id: int
    project_id: int
    program_id: int
