from fastapi import APIRouter, Depends, Request, Response, status

from config import get_settings, Settings
from logger import get_logger
from notifications import methods, paths, schema
from notifications.connection_manager import ConnectionManager
from permissions.enums import Permission
from permissions.resolver import Permissions

manager = ConnectionManager()
router = APIRouter(prefix=paths.base)


logger = get_logger(__name__)


@router.post(
    paths.dismiss,  # /programs/{program_id}/notifications/dismiss
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(Permissions([Permission.DISMISS_NOTIFICATIONS]))],
)
async def dismiss_program_notifications_post(
    request: Request, program_id: int, body: schema.NotificationDismissRequest
) -> Response:
    await methods.dismiss_program_notification(
        session=request.state.sql_session,
        notification_ids=body.data.notification_ids,
        program_id=program_id,
        user_id=request.state.fs_user_id,
    )
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.get(
    "",  # /programs/{program_id}/notifications
    dependencies=[Depends(Permissions([Permission.GET_NOTIFICATIONS]))],
)
async def get_program_notifications(
    request: Request,
    program_id: int,
    settings: Settings = Depends(get_settings),
) -> list[schema.ResponseNotification]:
    notifications = await methods.get_notifications(
        session=request.state.sql_session,
        settings=settings,
        program_id=program_id,
        user_id=request.state.fs_user_id,
        use_number_sent_limit=False,
    )
    await methods.mark_sent(
        session=request.state.sql_session,
        notification_ids=[i.id for i in notifications],
    )
    return notifications
