from config import get_settings
from notifications.methods import (
    dismiss_program_notification,
    get_notifications,
    mark_sent,
)

settings = get_settings()


async def create_notifications(notifications_factory, program_id: int):
    for _ in range(3):
        await notifications_factory(program_id=program_id, user=1)


async def test_notifications(app_request, mdl):
    program = await mdl.Programs()
    program_id = program.id
    await create_notifications(mdl.Notifications, program_id=program_id)
    settings.NUMBER_SENT_MAX = 10
    notifications = await get_notifications(
        session=app_request.state.sql_session, settings=settings, program_id=program_id, user_id=1
    )
    assert len(notifications) == 3
    assert len([i for i in notifications if i.number_sent == 0]) == 3

    await mark_sent(session=app_request.state.sql_session, notification_ids=[i.id for i in notifications])
    notifications = await get_notifications(
        session=app_request.state.sql_session, settings=settings, program_id=program_id, user_id=1
    )
    assert len([i for i in notifications if i.number_sent == 1]) == 3


async def test_delete_notifications(app_request, mdl):
    program = await mdl.Programs()
    program_id = program.id
    await create_notifications(mdl.Notifications, program_id=program_id)
    settings.NUMBER_SENT_MAX = 10
    notifications = await get_notifications(
        session=app_request.state.sql_session, settings=settings, program_id=program_id, user_id=1
    )
    assert len(notifications) == 3
    await dismiss_program_notification(
        session=app_request.state.sql_session,
        notification_ids=[i.id for i in notifications],
        program_id=program_id,
        user_id=1,
    )
    notifications = await get_notifications(
        session=app_request.state.sql_session, settings=settings, program_id=program_id, user_id=1
    )
    assert len(notifications) == 0
