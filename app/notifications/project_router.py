from fastapi import APIRouter, Depends, Request, Response, status

from config import get_settings, Settings
from logger import get_logger
from notifications import methods, paths, schema
from notifications.connection_manager import ConnectionManager
from permissions.enums import Permission
from permissions.resolver import Permissions

manager = ConnectionManager()
router = APIRouter(prefix=paths.base)


logger = get_logger(__name__)


@router.post(
    paths.dismiss,  # /projects/{project_id}/notifications/dismiss
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(Permissions([Permission.DISMISS_NOTIFICATIONS]))],
)
async def dismiss_notifications_post(
    request: Request, project_id: int, body: schema.NotificationDismissRequest
) -> Response:
    await methods.mark_dismissed(
        session=request.state.sql_session,
        notification_ids=body.data.notification_ids,
        project_id=project_id,
    )
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.get(
    "",  # /projects/{project_id}/notifications
    dependencies=[Depends(Permissions([Permission.GET_NOTIFICATIONS]))],
)
async def get_notifications(
    request: Request,
    project_id: int,
    settings: Settings = Depends(get_settings),
) -> list[schema.ResponseNotification]:
    notifications = await methods.get_notifications(
        session=request.state.sql_session, settings=settings, project_id=project_id
    )
    await methods.mark_sent(
        session=request.state.sql_session,
        notification_ids=[i.id for i in notifications],
    )
    return notifications
