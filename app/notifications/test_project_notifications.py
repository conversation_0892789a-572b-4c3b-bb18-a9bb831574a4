import httpx
from fastapi import status

from logger import get_logger
from notifications import paths
from projects import paths as projects_paths

logger = get_logger(__name__)


async def create_notifications(notifications_factory, project_id: int):
    for _ in range(3):
        await notifications_factory(project_id=project_id)


async def mark_recv_notification(async_client: httpx.AsyncClient, notification_ids: list[int], project_id: int):
    return await async_client.delete(
        f"{projects_paths.base}/{project_id}{paths.base}",
        params={"notification_ids": notification_ids},
    )


async def get_notifications(async_client: httpx.AsyncClient, project_id: int):
    return await async_client.get(
        f"{projects_paths.base}/{project_id}{paths.base}",
    )


async def dismiss_notifications(async_client: httpx.AsyncClient, project_id: int, notification_ids: list[int]):
    return await async_client.post(
        f"{projects_paths.base}/{project_id}{paths.base}/dismiss",
        json={"data": {"notification_ids": notification_ids}},
    )


async def test_notifications(async_client: httpx.AsyncClient, mdl):
    # client = TestClient(app)
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    project_id = project.id
    logger.debug(project_id)
    await create_notifications(mdl.Notifications, project_id=project_id)
    notifications = await get_notifications(async_client, project_id=project_id)
    notifications = notifications.json()
    logger.debug(notifications)
    assert len(notifications) == 3
    assert len([i for i in notifications if i["number_sent"] != 0]) == 0
    ids = [i["id"] for i in notifications]
    smaller_ids = ids[:2]
    await mark_recv_notification(async_client, notification_ids=smaller_ids, project_id=project_id)
    notifications = await get_notifications(async_client, project_id=project_id)
    notifications = notifications.json()
    logger.debug(notifications)
    # with client.websocket_connect(websocket_path) as websocket:
    #    notifications = websocket.receive_json()
    assert len(notifications) == 3
    assert notifications[0]["number_sent"] == 1


async def test_delete_notifications(async_client: httpx.AsyncClient, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    project_id = project.id
    await create_notifications(mdl.Notifications, project_id=project_id)
    notifications = await get_notifications(async_client, project_id=project_id)
    notifications = notifications.json()
    notification_ids = [n["id"] for n in notifications]
    response = await dismiss_notifications(async_client, project_id, notification_ids)
    assert response.status_code == status.HTTP_204_NO_CONTENT, response.content
    notifications = await get_notifications(async_client, project_id=project_id)
    notifications = notifications.json()
    assert len(notifications) == 0
