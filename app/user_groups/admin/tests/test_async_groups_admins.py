from permissions.enums import RoleTypes
from permissions.permissions_to_roles_mapping import group_admin_permissions


async def test_update_groups_for_admin_unrestricted_access_check(
    async_client, app_request, mdl, faker, grant_regular_role_permissions_factory
):
    program = await mdl.Programs(group_management=True)
    user_id1 = faker.random_number(3)
    user_id2 = faker.random_number(3)
    user_id3 = faker.random_number(3)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    await mdl.UserGroupsAdmins(group_id=group1.id, user_id=user_id1, program_id=program.id)
    await mdl.UserGroupsAdmins(group_id=group1.id, user_id=user_id2, program_id=program.id)
    await mdl.UserGroupsAdmins(group_id=None, user_id=user_id3, program_id=program.id, unrestricted_access=True)
    await grant_regular_role_permissions_factory(
        "Group Admin",
        RoleTypes.ADMIN,
        program.id,
        {user_id1},
        group_admin_permissions,
    )
    # user2 (Restricted) updated by user1 (Restricted) remain restricted is allowed
    request_params = {
        "url": f"/programs/{program.id}/admin_groups/{user_id2}",
        "json": {"groups": [group1.id], "unrestricted_access": False},
        "headers": {"fs-user-id": str(user_id1)},
    }
    response = await async_client.post(**request_params)
    assert response.status_code == 200

    # user2 (Restricted) updated by user1 (Restricted) to Unrestricted is not allowed
    request_params = {
        "url": f"/programs/{program.id}/admin_groups/{user_id2}",
        "json": {"groups": [], "unrestricted_access": True},
        "headers": {"fs-user-id": str(user_id1)},
    }
    response = await async_client.post(**request_params)
    assert response.status_code == 403

    # user3 (Unrestricted) updated by user1 (Restricted) to restricted is not allowed
    request_params = {
        "url": f"/programs/{program.id}/admin_groups/{user_id3}",
        "json": {"groups": [group1.id], "unrestricted_access": False},
        "headers": {"fs-user-id": str(user_id1)},
    }
    response = await async_client.post(**request_params)
    assert response.status_code == 403
