from user_groups.admin.controller import GroupsAdminsController
from user_groups.schema import UpdateGroupsAdminsRequest


async def test_get_groups_for_admin(app_request, mdl, faker):
    program = await mdl.Programs()
    user_id = faker.random_number(3)
    unrestricted_user_id = faker.random_number(3)

    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")

    await mdl.UserGroupsAdmins(group_id=group1.id, user_id=user_id, program_id=program.id)
    await mdl.UserGroupsAdmins(group_id=group2.id, user_id=user_id, program_id=program.id)
    await mdl.UserGroupsAdmins(
        group_id=None, user_id=unrestricted_user_id, program_id=program.id, unrestricted_access=True
    )

    controller = GroupsAdminsController(request=app_request)

    # normal user should return only the groups they are admin of
    results = await controller.get_groups_for_admin(program_id=program.id, user_id=user_id)
    assert results.unrestricted_access is False
    assert len(results.groups) == 2
    assert {group.id for group in results.groups} == {group1.id, group2.id}

    # unrestricted access user should return all groups in the program
    results = await controller.get_groups_for_admin(program_id=program.id, user_id=unrestricted_user_id)
    assert results.unrestricted_access is True
    assert len(results.groups) == 3
    assert {group.id for group in results.groups} == {group1.id, group2.id, group3.id}


async def test_update_groups_for_admin(app_request, mdl, faker):
    program = await mdl.Programs()
    user_id = faker.random_number(3)

    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")
    group4 = await mdl.UserGroups(program_id=program.id, name="group4")

    await mdl.UserGroupsAdmins(group_id=group1.id, user_id=user_id, program_id=program.id)
    await mdl.UserGroupsAdmins(group_id=group2.id, user_id=user_id, program_id=program.id)
    await mdl.UserGroupsAdmins(group_id=group4.id, user_id=user_id, program_id=program.id)

    controller = GroupsAdminsController(request=app_request)

    # add group3
    payload = UpdateGroupsAdminsRequest(unrestricted_access=False, groups=[group1.id, group2.id, group3.id, group4.id])
    results = await controller.update_groups_for_admin(program_id=program.id, user_id=user_id, payload=payload)
    assert len(results.groups) == 4
    assert {group.id for group in results.groups} == {group1.id, group2.id, group3.id, group4.id}

    # remove group2
    payload = UpdateGroupsAdminsRequest(unrestricted_access=False, groups=[group1.id, group3.id])
    results = await controller.update_groups_for_admin(program_id=program.id, user_id=user_id, payload=payload)
    assert len(results.groups) == 2
    assert {group.id for group in results.groups} == {group1.id, group3.id}

    # remove group1
    payload = UpdateGroupsAdminsRequest(unrestricted_access=False, groups=[group3.id])
    results = await controller.update_groups_for_admin(program_id=program.id, user_id=user_id, payload=payload)
    assert len(results.groups) == 1
    assert {group.id for group in results.groups} == {group3.id}


async def test_update_groups_for_admin_unrestricted_access(app_request, mdl, faker):
    program = await mdl.Programs()
    user_id = faker.random_number(3)

    group1 = await mdl.UserGroups(program_id=program.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2")
    group3 = await mdl.UserGroups(program_id=program.id, name="group3")

    await mdl.UserGroupsAdmins(group_id=group1.id, user_id=user_id, program_id=program.id)

    controller = GroupsAdminsController(request=app_request)

    # change to unrestricted access = true
    payload = UpdateGroupsAdminsRequest(unrestricted_access=True)
    results = await controller.update_groups_for_admin(program_id=program.id, user_id=user_id, payload=payload)

    assert len(results.groups) == 3
    assert {group.id for group in results.groups} == {group1.id, group2.id, group3.id}
    assert results.unrestricted_access is True

    # change to unrestricted access = false
    payload = UpdateGroupsAdminsRequest(unrestricted_access=False, groups=[group2.id])
    results = await controller.update_groups_for_admin(program_id=program.id, user_id=user_id, payload=payload)

    assert len(results.groups) == 1
    assert {group.id for group in results.groups} == {group2.id}
