from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Request, status

from db.fastapi_dependencies import transacted
from permissions.enums import Permission
from permissions.resolver import Permissions
from user_groups import schema
from user_groups.admin.controller import (  # your controller
    GroupsAdminsController as TheController,
)
from user_groups.admin.utils import check_group_admin_editing_allowed

router = APIRouter(prefix=TheController.prefix(), tags=TheController.tags())


@router.get(
    TheController.Routes.get,  # /programs/{program_id}/admin_groups/{user_id}
    response_model=schema.AdminGroupsResponse,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_GROUPS_FOR_ADMIN]))],
)
async def get_groups_for_admin(request: Request, program_id: int, user_id: int) -> schema.AdminGroupsResponse:
    controller = TheController(request=request)
    result = await controller.get_groups_for_admin(program_id=program_id, user_id=user_id)
    handle_error(result)
    return result


@router.post(
    TheController.Routes.post,  # /programs/{program_id}/admin_groups/{user_id}
    response_model=schema.AdminGroupsResponse,
    tags=TheController.tags(),
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(transacted), Depends(Permissions([Permission.UPDATE_GROUPS_FOR_ADMIN]))],
)
async def update_groups_for_admin(
    request: Request,
    program_id: int,
    user_id: int,
    payload: schema.UpdateGroupsAdminsRequest,
) -> schema.AdminGroupsResponse:
    controller = TheController(request=request)
    await check_group_admin_editing_allowed(
        request=request, program_id=program_id, target_user_ids=[user_id], payload=payload
    )
    result = await controller.update_groups_for_admin(
        program_id=program_id,
        user_id=user_id,
        payload=payload,
    )
    handle_error(result, status.HTTP_400_BAD_REQUEST, controller.error_string)
    return result


def handle_error(
    result: Any | None = None, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR, msg: str = ""
) -> None:
    if result is not None:
        return None

    raise HTTPException(status_code=status_code, detail={"message": str(msg)})
