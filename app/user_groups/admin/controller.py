import enum

from fastapi import Request

from base_controller import BaseController
from permissions.db import get_role_users_by_user_id
from permissions.methods import is_super_admin
from user_groups import db, schema


class GroupsAdminsController(BaseController):
    def __init__(self, request: Request | None = None):
        super().__init__(request=request)

    @staticmethod
    def tags() -> list[str]:
        return ["admin_groups"]

    @staticmethod
    def prefix() -> str | None:
        return "/programs/{program_id}/admin_groups"

    async def get_groups_for_admin(self, program_id: int, user_id: int) -> schema.AdminGroupsResponse:
        """
        List all the groups for an admin user
        """
        # Fetch groups data
        rows = await db.get_groups_for_admin(request=self.request, program_id=program_id, user_id=user_id)
        groups = [schema.UserGroup.from_orm(row) for row in rows]

        # Fetch unrestricted_access value
        group_admin = await db.get_group_admins(request=self.request, user_id=user_id, program_id=program_id)
        if not group_admin:
            # super admin by default has unrestricted access
            role_users = await get_role_users_by_user_id(request=self.request, user_id=user_id)
            unrestricted_access = is_super_admin(role_users)
        else:
            unrestricted_access = group_admin[0].unrestricted_access

        # return all groups for unrestricted_access user
        if unrestricted_access:
            rows = await db.get_user_groups_by_program_id(request=self.request, program_id=program_id)
            groups = [schema.UserGroup.from_orm(row) for row in rows]

        return schema.AdminGroupsResponse(unrestricted_access=unrestricted_access, groups=groups)

    async def update_groups_for_admin(
        self, program_id: int, user_id: int, payload: schema.UpdateGroupsAdminsRequest
    ) -> schema.AdminGroupsResponse | None:
        """
        Update the groups for an admin user, overwrites the existing groups with the new group_ids
        """
        # check all groups exists and belong to the program
        if not await db.all_groups_exists(request=self.request, program_id=program_id, group_ids=payload.groups):
            self.add_error(f"not all group exists in program: {program_id}, group_ids: {payload.groups}")
            return None

        existing_groups = await db.get_groups_for_admin(request=self.request, program_id=program_id, user_id=user_id)

        existing_group_ids = {group.id for group in existing_groups}

        group_ids_set = set(payload.groups)

        groups_to_remove = [group_id for group_id in existing_group_ids if group_id not in group_ids_set]

        await db.delete_unrestricted_group_admin(request=self.request, user_id=user_id, program_id=program_id)

        if payload.unrestricted_access:
            await db.delete_groups_admins(request=self.request, group_ids=list(existing_group_ids), user_ids=[user_id])

            await db.create_groups_admins(
                request=self.request,
                user_id=user_id,
                group_ids=[],  # unrestricted access means no groups
                program_id=program_id,
                unrestricted_access=True,
            )
        else:
            if groups_to_remove:
                await db.delete_groups_admins(request=self.request, group_ids=groups_to_remove, user_ids=[user_id])

            # create diff
            groups_to_add = [group_id for group_id in group_ids_set if group_id not in existing_group_ids]
            if groups_to_add:
                await db.create_groups_admins(
                    request=self.request,
                    user_id=user_id,
                    group_ids=groups_to_add,
                    program_id=program_id,
                    unrestricted_access=False,
                )
        return await self.get_groups_for_admin(program_id=program_id, user_id=user_id)

    class Routes(enum.StrEnum):
        get = "/{user_id}"
        post = "/{user_id}"
