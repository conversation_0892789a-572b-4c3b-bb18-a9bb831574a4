from __future__ import annotations

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status

from permissions.methods import get_original_user_id, is_original_user_super_admin
from programs.db import get_program_by_id
from user_groups import db as groups_db, schema


async def check_group_admin_editing_allowed(
    request: Request,
    program_id: int,
    target_user_ids: list[int],
    payload: schema.UpdateGroupsAdminsRequest | None = None,
) -> None:
    """
    Check if the user can edit the particular admin user when group management enabled
    This only take effect when group management is enabled for the program.
    Rules:
    - Admins with Unrestricted access can edit all admin users in the program.
    - Admins WITHOUT Unrestricted access can not edit Unrestricted access admins.
    """
    program = await get_program_by_id(request=request, program_id=program_id)
    if program.group_management is False:
        return

    if await is_original_user_super_admin(request=request):
        return

    original_user_groups = await groups_db.get_group_admins(
        request=request, user_id=get_original_user_id(request=request), program_id=program_id
    )
    original_user_unrestricted_access = any(ga.unrestricted_access for ga in original_user_groups)
    if original_user_unrestricted_access:
        return

    if payload is not None and payload.unrestricted_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": f"Cannot assign unrestricted access to user with id {target_user_ids} by users without unrestricted access."
            },
        )

    # when original user is not unrestricted access, check if we are trying to edit unrestricted access admin
    for user_id in target_user_ids:
        target_admin_groups = await groups_db.get_group_admins(request=request, user_id=user_id, program_id=program_id)
        target_admin_unrestricted_access = any(ga.unrestricted_access for ga in target_admin_groups)
        if target_admin_unrestricted_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "message": f"User with id {user_id} has unrestricted access and cannot be edited by users without unrestricted access."
                },
            )
