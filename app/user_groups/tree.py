from anytree import Node, PreOrderIter

from user_groups import schema


class UserGroupTree:
    def __init__(self) -> None:
        self.node_map: dict[int, Node] = {}

    def get_node(self, group_id: int) -> Node | None:
        return self.node_map.get(group_id, None)

    def update_group_properties(
        self, group_id: int, updated_name: str, updated_parent_group_id: int | None
    ) -> list[schema.UserGroup] | None:
        """
        Update the display name and path based on the group and its children, and return all the updated groups
        """

        # find the node to update
        node = self.get_node(group_id=group_id)
        if node is None:
            return None

        # find the new parent node
        new_parent = None
        if updated_parent_group_id is not None:
            parent_node = self.get_node(group_id=updated_parent_group_id)
            if parent_node is None:
                return None
            new_parent = parent_node

        # update group attributes
        node.group.name = updated_name
        node.group.parent_group_id = updated_parent_group_id
        node.parent = new_parent

        # build display name and path for the group and its children
        updated_groups: list[schema.UserGroup] = []
        for node in PreOrderIter(node):
            # update display name
            node.group.display_name = (
                node.parent.group.display_name + " | " + node.group.name if node.parent else node.group.name
            )
            # update path
            node.group.path = node.parent.group.path + "/" + str(node.group.id) if node.parent else str(node.group.id)
            updated_groups.append(node.group)

        return updated_groups

    @staticmethod
    def build(groups: list[schema.UserGroup]) -> "UserGroupTree":
        tree = UserGroupTree()

        # build node map
        for group in groups:
            node = Node(name=group.id, group=group)
            tree.node_map[group.id] = node

        # build tree
        for group in groups:
            node = tree.node_map[group.id]
            node.parent = tree.node_map.get(group.parent_group_id, None)

        return tree
