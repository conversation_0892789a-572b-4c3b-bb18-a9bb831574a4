from __future__ import annotations

from typing import TYPE_CHECKING

import elasticapm
from fastapi import HTTPException, status
from sqlalchemy import exc, func, or_, update as sqlalchemy_update
from sqlalchemy.future import select

from helper.helper import run_query
from logger import get_logger
from programs.enums import CustomRegInputsTypes, CustomRegInputsValidationRules
from programs.model import ProgramCustomRegInputs
from projects.model import Projects, ProjectsWithoutRelationships
from root_crud import create, delete, get
from user_groups import model, schema

if TYPE_CHECKING:

    from fastapi import Request

logger = get_logger(__name__)


UserGroups = model.UserGroups


async def get_user_groups_by_name(request: Request, program_id: int, group_name: str) -> list[model.UserGroups]:
    """
    This differs from search_user_groups because:
        1. It checks the `name` column, not the `display_name` column
        2. It looks for an exact match in the name column
    """
    query = (
        select(UserGroups)
        .where(UserGroups.program_id == program_id)
        .where(UserGroups.name == group_name)
        .where(UserGroups.deleted_at.is_(None))
    )

    async with request.state.sql_session() as s:
        result = await run_query(s, query)
        return result.scalars().all()


async def search_user_groups(request: Request, query_string: str, program_id: int) -> list[model.UserGroups]:
    if query_string:
        like_query_string = f"%{query_string}%"
        try:
            query_string_int = int(query_string)
        except ValueError:
            query_string_int = None

        query = (
            select(UserGroups)
            .where(UserGroups.program_id == program_id)
            .where(UserGroups.deleted_at.is_(None))
            .where(
                or_(
                    UserGroups.display_name.like(like_query_string),
                    UserGroups.id == query_string_int if query_string_int is not None else False,
                )
            )
        )
    else:
        query = select(UserGroups).where(UserGroups.program_id == program_id).where(UserGroups.deleted_at.is_(None))

    query = query.order_by(UserGroups.created_at.desc())
    async with request.state.sql_session() as s:
        result = await run_query(s, query)
        return result.scalars().all()


@elasticapm.async_capture_span()
async def partial_update_groups(*, request: Request, payloads: list[schema.PatchUserGroupRequest]) -> None:
    try:
        async with request.state.sql_session.begin() as s:
            updated_data = [payload.dict(exclude_unset=True) for payload in payloads]
            await s.execute(sqlalchemy_update(model.UserGroups), updated_data)
    except exc.IntegrityError as e:
        logger.debug(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": ("Cannot update object with probably invalid parent(integrity error)")},
        )


async def projects_in_group(request: Request, group_id: int) -> int:
    """
    Query the number of projects associated with a group
    """
    count_query = (
        select(func.count(Projects.id))
        .select_from(Projects)
        .join(model.UserGroupProjects, model.UserGroupProjects.project_id == Projects.id)
        .join(model.UserGroups, model.UserGroups.id == model.UserGroupProjects.group_id)
        .where(model.UserGroupProjects.group_id == group_id)
        .where(model.UserGroups.deleted_at.is_(None))
        .where(model.UserGroupProjects.deleted_at.is_(None))
        .where(Projects.deleted_at.is_(None))
    )

    async with request.state.sql_session() as s:
        result = await s.execute(count_query)
        return result.scalar()


@elasticapm.async_capture_span()
async def admins_in_group(request: Request, group_id: int) -> int:
    """
    Query the number of admins associated with a group
    """
    count_query = (
        select(func.count(model.UserGroupsAdmins.id))
        .select_from(model.UserGroupsAdmins)
        .join(model.UserGroups, model.UserGroups.id == model.UserGroupsAdmins.group_id)
        .where(model.UserGroupsAdmins.group_id == group_id)
        .where(model.UserGroupsAdmins.deleted_at.is_(None))
        .where(model.UserGroups.deleted_at.is_(None))
    )

    async with request.state.sql_session() as s:
        result = await s.execute(count_query)
        return result.scalar()


@elasticapm.async_capture_span()
async def get_children_groups(
    request: Request, parent_group_ids: list[int], exclude_parent: bool = False
) -> list[model.UserGroups]:
    """
    Get the children of the group, including the group being passed in
    optionally with exclude_parent set to True, the parent group will be excluded in the result
    """
    if not parent_group_ids:
        return []

    parent_groups = await get.get(
        request=request,
        orm_type=model.UserGroups,
        id_field=model.UserGroups.id,
        ids=parent_group_ids,
        empty_return=True,
    )
    missing_groups = set(parent_group_ids) - {group.id for group in parent_groups}
    if len(missing_groups) > 0:
        raise ValueError(f"Groups not found: {missing_groups}")

    filter_sql_expr = [model.UserGroups.path.like(f"{group.path}/%") for group in parent_groups]

    children = await get.generic_get(
        request=request,
        orm_type=model.UserGroups,
        filter_sql_expr=or_(*filter_sql_expr),
        empty_return=True,
    )

    if not exclude_parent:
        children.extend(parent_groups)
    return children


async def get_user_group_projects_by_program_id(request: Request, program_id: int) -> list[model.UserGroupProjects]:
    query = (
        select(model.UserGroupProjects)
        .join(Projects, model.UserGroupProjects.project_id == Projects.id)
        .where(Projects.program_id == program_id)
        .where(model.UserGroupProjects.deleted_at.is_(None))
    )
    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).scalars().all()


async def get_user_group_admins_by_program_id(request: Request, program_id: int) -> list[model.UserGroupsAdmins]:
    query = (
        select(model.UserGroupsAdmins)
        .where(model.UserGroupsAdmins.program_id == program_id)
        .where(model.UserGroupsAdmins.deleted_at.is_(None))
    )
    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).scalars().all()


@elasticapm.async_capture_span()
async def get_group_projects_by_group_id(
    request: Request, program_id: int, group_ids: list[int], include_project_without_groups: bool = False
) -> list[ProjectsWithoutRelationships]:
    async with request.state.sql_session() as s:
        query = (
            select(ProjectsWithoutRelationships)
            .join(
                model.UserGroupProjects,
                model.UserGroupProjects.project_id == ProjectsWithoutRelationships.id,
                isouter=True,
            )
            .where(ProjectsWithoutRelationships.program_id == program_id)
            .where(ProjectsWithoutRelationships.deleted_at.is_(None))
            .where(model.UserGroupProjects.deleted_at.is_(None))
        )
        if include_project_without_groups:
            query = query.where(
                or_(model.UserGroupProjects.group_id.in_(group_ids), model.UserGroupProjects.group_id.is_(None))
            )
        else:
            query = query.where(model.UserGroupProjects.group_id.in_(group_ids))
        return (await run_query(s, query)).scalars().all()


@elasticapm.async_capture_span()
async def get_group_admins_by_group_id(
    request: Request, group_ids: list[int], include_unrestricted: bool = False
) -> list[model.UserGroupsAdmins]:
    filter_sql_expr = [model.UserGroupsAdmins.group_id.in_(group_ids)]
    if include_unrestricted:
        filter_sql_expr.append(model.UserGroupsAdmins.unrestricted_access.is_(True))
    return await get.generic_get(
        request=request,
        orm_type=model.UserGroupsAdmins,
        filter_sql_expr=or_(*filter_sql_expr),
        empty_return=True,
    )


@elasticapm.async_capture_span()
async def upsert_custom_reg_input_for_group(request: Request, program_id: int, display_name: str) -> bool:
    """
    Update or create a custom reg input for a group
    """
    try:
        custom_reg_input = None
        query = select(ProgramCustomRegInputs).where(
            ProgramCustomRegInputs.program_id == program_id,
            ProgramCustomRegInputs.type_ == CustomRegInputsTypes.user_group,
            ProgramCustomRegInputs.deleted_at.is_(None),
        )

        async with request.state.sql_session() as s:
            result = await run_query(s, query)
            # Ideally, there should be only one custom reg input of type UserGroup for a program. However, add a check to ensure that only one is returned.
            custom_reg_input = result.scalars().first()

            if custom_reg_input:
                config = custom_reg_input.config
                config = f"{config}, {display_name}"
                custom_reg_input.config = config
                await s.commit()

            else:
                new_custom_reg_input = ProgramCustomRegInputs(
                    program_id=program_id,
                    # Todo: Confirm if this is the correct order.
                    order=0,
                    name="User Group",
                    type_=CustomRegInputsTypes.user_group,
                    validation_rule=CustomRegInputsValidationRules.none,
                    config=f"{display_name}",
                )
                s.add(new_custom_reg_input)
                await s.commit()

            return True
    except Exception as e:
        logger.error("Could not upsert custom reg input for group: %s, %s", display_name, e)
        return False


@elasticapm.async_capture_span()
async def delete_custom_reg_input_for_group(request: Request, program_id: int, group_id: int) -> bool:
    """
    Delete a custom reg input for a group
    """
    query = select(UserGroups).where(UserGroups.id == group_id)
    display_name = None
    async with request.state.sql_session() as s:
        result = await run_query(s, query)
        group = result.scalars().first()
        display_name = group.display_name

    query = select(ProgramCustomRegInputs).where(
        ProgramCustomRegInputs.program_id == program_id,
        ProgramCustomRegInputs.type_ == CustomRegInputsTypes.user_group,
        ProgramCustomRegInputs.deleted_at.is_(None),
    )
    try:
        async with request.state.sql_session() as s:
            result = await run_query(s, query)
            custom_reg_input = result.scalars().first()

            if not custom_reg_input:
                return True

            config = custom_reg_input.config
            config_list = config.split(", ")
            config_list.remove(display_name)
            if len(config_list) == 0:
                # If there are no more display names in the config field, delete the custom reg input.
                # Todo: confirm is this is the right behaviour.
                await s.delete(custom_reg_input)
            else:
                custom_reg_input.config = ", ".join(config_list)
            await s.commit()
            return True
    except Exception as e:
        logger.error("Could not delete custom reg input for group: %s, %s", display_name, e)
        return False


@elasticapm.async_capture_span()
async def refresh_custom_reg_input_for_program(request: Request, program_id: int) -> bool:
    """
    Refresh the custom reg input for a program
    """
    query = select(ProgramCustomRegInputs).where(
        ProgramCustomRegInputs.program_id == program_id,
        ProgramCustomRegInputs.type_ == CustomRegInputsTypes.user_group,
        ProgramCustomRegInputs.deleted_at.is_(None),
    )
    try:
        async with request.state.sql_session() as s:
            result = await run_query(s, query)
            custom_reg_input = result.scalars().first()

            query = (
                select(UserGroups.display_name)
                .where(UserGroups.program_id == program_id)
                .where(UserGroups.allow_self_assignment.is_(True))
                .where(UserGroups.deleted_at.is_(None))
            )
            result = await run_query(s, query)
            display_names = result.scalars().all()

            if custom_reg_input:
                custom_reg_input.config = ", ".join(display_names)
            else:
                new_custom_reg_input = ProgramCustomRegInputs(
                    program_id=program_id,
                    # Todo: Confirm if this is the correct order.
                    order=0,
                    name="User Group",
                    type_=CustomRegInputsTypes.user_group,
                    validation_rule=CustomRegInputsValidationRules.none,
                    config=", ".join(display_names),
                )
                s.add(new_custom_reg_input)
            await s.commit()
            return True
    except Exception as e:
        logger.error("Could not refresh custom reg input for program: %s, %s", program_id, e)
        return False


@elasticapm.async_capture_span()
async def rename_custom_reg_input_for_group(
    request: Request, program_id: int, older_display_name: str, display_name: str
) -> bool:
    """
    Rename a custom reg input for a group
    """

    query = select(ProgramCustomRegInputs).where(
        ProgramCustomRegInputs.program_id == program_id,
        ProgramCustomRegInputs.type_ == CustomRegInputsTypes.user_group,
        ProgramCustomRegInputs.deleted_at.is_(None),
    )
    try:
        async with request.state.sql_session() as s:
            result = await run_query(s, query)
            custom_reg_input = result.scalars().first()

            if not custom_reg_input:
                logger.error("Custom reg input for group does not exist: %s", older_display_name)
                return False

            config = custom_reg_input.config
            config_list = config.split(", ")
            config_list.remove(older_display_name)
            config_list.append(display_name)
            custom_reg_input.config = ", ".join(config_list)
            await s.commit()
            return True
    except Exception as e:
        logger.error("Could not rename custom reg input for group: %s, %s", display_name, e)
        return False


@elasticapm.async_capture_span()
async def all_groups_exists(request: Request, program_id: int, group_ids: list[int]) -> bool:
    unique_group_ids = list(set(group_ids))
    groups = await get.get(
        request=request,
        orm_type=model.UserGroups,
        id_field=model.UserGroups.id,
        ids=unique_group_ids,
        empty_return=True,
    )
    return groups is not None and len(groups) == len(unique_group_ids)


@elasticapm.async_capture_span()
async def get_groups_for_admin(request: Request, program_id: int, user_id: int) -> list[model.UserGroups]:
    query = (
        select(model.UserGroups)
        .join(model.UserGroupsAdmins, model.UserGroupsAdmins.group_id == model.UserGroups.id)
        .where(model.UserGroups.program_id == program_id)
        .where(model.UserGroupsAdmins.user_id == user_id)
        .where(model.UserGroups.deleted_at.is_(None))
        .where(model.UserGroupsAdmins.deleted_at.is_(None))
    )

    async with request.state.sql_session() as s:
        result = await run_query(s, query)
        return result.scalars().all()


@elasticapm.async_capture_span()
async def get_group_admins(request: Request, program_id: int, user_id: int) -> list[model.UserGroupsAdmins]:
    return await get.generic_get(
        request=request,
        orm_type=model.UserGroupsAdmins,
        filters=[
            get.Filter(id_field=model.UserGroupsAdmins.program_id, ids=[program_id]),
            get.Filter(id_field=model.UserGroupsAdmins.user_id, ids=[user_id]),
        ],
        empty_return=True,
    )


@elasticapm.async_capture_span()
async def create_groups_admins(
    request: Request, user_id: int, group_ids: list[int], program_id: int, unrestricted_access: bool
) -> None:
    if unrestricted_access:
        instances = [
            model.UserGroupsAdmins(user_id=user_id, group_id=None, unrestricted_access=True, program_id=program_id)
        ]
    else:
        instances = [
            model.UserGroupsAdmins(user_id=user_id, group_id=group_id, unrestricted_access=False, program_id=program_id)
            for group_id in group_ids
        ]

    await create.create(
        request=request,
        orm_type=model.UserGroupsAdmins,
        instances=instances,
        translate=False,
    )


@elasticapm.async_capture_span()
async def delete_groups_admins(
    request: Request, group_ids: list[int], user_ids: list[int] | None = None
) -> list[model.UserGroupsAdmins]:
    filters = [get.Filter(id_field=model.UserGroupsAdmins.deleted_at, ids=[None])]
    if user_ids:
        filters.append(get.Filter(id_field=model.UserGroupsAdmins.user_id, ids=user_ids))

    return await delete.soft(
        request=request,
        orm_type=model.UserGroupsAdmins,
        id_field=model.UserGroupsAdmins.group_id,
        ids=group_ids,
        filters=filters,
    )


async def get_user_group_projects_by_user_group(request: Request, user_group_id: int) -> list[model.UserGroupProjects]:
    return await get.get(
        request=request,
        orm_type=model.UserGroupProjects,
        id_field=model.UserGroupProjects.group_id,
        ids=[user_group_id],
        empty_return=True,
    )


async def delete_user_group_projects(request: Request, user_group_id: int, project_id: int) -> None:
    await delete.soft(
        request=request,
        orm_type=model.UserGroupProjects,
        id_field=model.UserGroupProjects.group_id,
        ids=[user_group_id],
        filters=[get.Filter(id_field=model.UserGroupProjects.project_id, ids=[project_id])],
    )


@elasticapm.async_capture_span()
async def delete_unrestricted_group_admin(
    request: Request, user_id: int, program_id: int
) -> list[model.UserGroupsAdmins]:
    return await delete.soft(
        request=request,
        orm_type=model.UserGroupsAdmins,
        id_field=model.UserGroupsAdmins.user_id,
        ids=[user_id],
        filters=[
            get.Filter(id_field=model.UserGroupsAdmins.program_id, ids=[program_id]),
            get.Filter(id_field=model.UserGroupsAdmins.unrestricted_access, ids=[True]),
        ],
    )


@elasticapm.async_capture_span()
async def get_user_group_id_by_name(request: Request, program_id: int, group_name: str) -> int | None:
    query = (
        select(UserGroups.id)
        .where(UserGroups.program_id == program_id)
        .where(UserGroups.display_name == group_name)
        .where(UserGroups.deleted_at.is_(None))
    )

    async with request.state.sql_session() as s:
        result = await run_query(s=s, query=query)
        return result.scalar_one_or_none()


@elasticapm.async_capture_span()
async def get_user_groups_by_ids(request: Request, group_ids: list[int]) -> list[model.UserGroups]:
    return await get.get(
        request=request,
        orm_type=model.UserGroups,
        id_field=model.UserGroups.id,
        ids=group_ids,
        empty_return=True,
    )


@elasticapm.async_capture_span()
async def get_user_groups_by_program_id(request: Request, program_id: int) -> list[model.UserGroups]:
    return await get.get(
        request=request,
        orm_type=model.UserGroups,
        id_field=model.UserGroups.program_id,
        ids=[program_id],
        empty_return=True,
    )
