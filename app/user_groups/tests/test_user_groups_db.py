from sqlalchemy.future import select

from programs.model import ProgramCustomRegInputs
from user_groups.db import (
    delete_custom_reg_input_for_group,
    refresh_custom_reg_input_for_program,
    rename_custom_reg_input_for_group,
    upsert_custom_reg_input_for_group,
)


async def test_upsert_custom_reg_input_for_group(app_request, mdl):
    program = await mdl.Programs()
    await mdl.UserGroups(program_id=program.id, name="group1")
    await mdl.UserGroups(program_id=program.id, name="group2")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group1")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group2")

    async with app_request.state.sql_session() as s:
        query = select(ProgramCustomRegInputs).where(
            ProgramCustomRegInputs.program_id == program.id,
            ProgramCustomRegInputs.type_ == "user_group",
        )
        result = await s.execute(query)
        custom_reg_inputs = result.scalars().all()
        assert len(custom_reg_inputs) == 1
        custom_reg_input = custom_reg_inputs[0]
        assert custom_reg_input.config == "group1, group2"


async def test_delete_custom_reg_input_for_group(app_request, mdl):
    program = await mdl.Programs()
    group1 = await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1")
    group2 = await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group1")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group2")
    await delete_custom_reg_input_for_group(app_request, program.id, group1.id)

    query = select(ProgramCustomRegInputs).where(
        ProgramCustomRegInputs.program_id == program.id,
        ProgramCustomRegInputs.type_ == "user_group",
    )
    async with app_request.state.sql_session() as s:
        result = await s.execute(query)
        custom_reg_inputs = result.scalars().all()
        assert len(custom_reg_inputs) == 1
        custom_reg_input = custom_reg_inputs[0]
        assert custom_reg_input.config == "group2"

    await delete_custom_reg_input_for_group(app_request, program.id, group2.id)

    async with app_request.state.sql_session() as s:
        result = await s.execute(query)
        custom_reg_inputs = result.scalars().all()
        assert len(custom_reg_inputs) == 0


async def test_rename_custom_reg_input_for_group(app_request, mdl):
    program = await mdl.Programs()
    await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1")
    await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group1")
    await upsert_custom_reg_input_for_group(app_request, program.id, "group2")
    await rename_custom_reg_input_for_group(app_request, program.id, "group1", "group3")

    async with app_request.state.sql_session() as s:
        query = select(ProgramCustomRegInputs).where(
            ProgramCustomRegInputs.program_id == program.id,
            ProgramCustomRegInputs.type_ == "user_group",
        )
        result = await s.execute(query)
        custom_reg_inputs = result.scalars().all()
        assert len(custom_reg_inputs) == 1
        custom_reg_input = custom_reg_inputs[0]
        config_list = custom_reg_input.config.split(", ")
        assert "group1" not in config_list
        assert "group3" in config_list
        assert "group2" in config_list
        assert len(config_list) == 2


async def test_refresh_custom_reg_input_for_group(app_request, mdl):
    program = await mdl.Programs()
    await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1")
    await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2", allow_self_assignment=True)
    await mdl.UserGroups(program_id=program.id, name="group3", display_name="group3", allow_self_assignment=True)
    refresh_result = await refresh_custom_reg_input_for_program(app_request, program.id)
    assert refresh_result

    async with app_request.state.sql_session() as s:
        query = select(ProgramCustomRegInputs).where(
            ProgramCustomRegInputs.program_id == program.id,
            ProgramCustomRegInputs.type_ == "user_group",
        )
        result = await s.execute(query)
        custom_reg_inputs = result.scalars().all()
        assert len(custom_reg_inputs) == 1
        custom_reg_input = custom_reg_inputs[0]
        config_list = custom_reg_input.config.split(", ")
        assert "group1" not in config_list
        assert "group2" in config_list
        assert "group3" in config_list
        assert len(config_list) == 2
