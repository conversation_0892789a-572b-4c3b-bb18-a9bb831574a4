from datetime import datetime

from sqlalchemy.future import select

from programs.enums import CustomRegInputsValidationRules
from programs.model import ProgramCustomRegInputs
from user_groups import schema
from user_groups.user_groups_controller import UserGroupsController


def test_row_reference_value(app_request):
    controller = UserGroupsController(request=app_request)
    data_row = schema.UserGroup(id=1, program_id=2, name="name", display_name="display_name", path="1")
    result = controller.row_reference_value(data_row=data_row)
    assert result.dict() == {"id": 1, "program_id": 2}


async def test_read_by_reference(app_request, mdl):
    controller = UserGroupsController(request=app_request)
    program1 = await mdl.Programs()
    program2 = await mdl.Programs()
    group1 = await mdl.UserGroups(program_id=program1.id, name="group1")
    group2 = await mdl.UserGroups(program_id=program2.id, name="group2")

    result = await controller.read_by_reference(
        reference=schema.UserGroupReference(id=group1.id, program_id=program1.id)
    )
    assert result.id == group1.id
    assert result.program_id == program1.id
    assert result.name == group1.name

    result = await controller.read_by_reference(
        reference=schema.UserGroupReference(id=group2.id, program_id=program2.id)
    )
    assert result.id == group2.id
    assert result.program_id == program2.id
    assert result.name == group2.name


async def test_generate_update_payloads(app_request, mdl):
    controller = UserGroupsController(request=app_request)
    program = await mdl.Programs()
    group1 = await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1", color_category_index=1)
    group2 = await mdl.UserGroups(
        program_id=program.id, name="group2", parent_group_id=group1.id, display_name="group1 | group2"
    )
    group3 = await mdl.UserGroups(
        program_id=program.id, name="group3", parent_group_id=group2.id, display_name="group1 | group2 | group3"
    )

    # update group1 attrs without name
    payload = schema.PatchUserGroupRequest(id=group1.id, program_id=program.id, color_category_index=2)
    results = await controller.generate_update_payloads(payload=payload)
    assert len(results) == 1
    assert [result.dict(exclude_unset=True) for result in results] == [{"id": group1.id, "color_category_index": 2}]

    # update group attrs including name
    payload = schema.PatchUserGroupRequest(id=group1.id, program_id=program.id, name="new_name", color_category_index=2)
    results = await controller.generate_update_payloads(payload=payload)
    assert len(results) == 4
    assert [result.dict(exclude_unset=True) for result in results] == [
        {"id": group1.id, "name": "new_name", "color_category_index": 2},
        {"id": group1.id, "display_name": "new_name"},
        {"id": group2.id, "display_name": "new_name | group2"},
        {"id": group3.id, "display_name": "new_name | group2 | group3"},
    ]

    # update group2 attrs including name
    payload = schema.PatchUserGroupRequest(id=group2.id, program_id=program.id, name="new_name", color_category_index=2)
    results = await controller.generate_update_payloads(payload=payload)
    assert len(results) == 3
    assert [result.dict(exclude_unset=True) for result in results] == [
        {"id": group2.id, "name": "new_name", "color_category_index": 2},
        {"id": group2.id, "display_name": "group1 | new_name"},
        {"id": group3.id, "display_name": "group1 | new_name | group3"},
    ]


async def test_do_partial_update(app_request, mdl, faker):
    controller = UserGroupsController(request=app_request)
    program = await mdl.Programs()
    group1_id = faker.unique.random_number(3)
    group2_id = faker.unique.random_number(3)
    group3_id = faker.unique.random_number(3)

    group1 = await mdl.UserGroups(
        id=group1_id,
        program_id=program.id,
        name="group1",
        display_name="group1",
        color_category_index=1,
        path=f"{group1_id}",
    )
    group2 = await mdl.UserGroups(
        id=group2_id,
        program_id=program.id,
        name="group2",
        parent_group_id=group1.id,
        display_name="group1 | group2",
        path=f"{group1_id}/{group2_id}",
    )
    group3 = await mdl.UserGroups(
        id=group3_id,
        program_id=program.id,
        name="group3",
        parent_group_id=group2.id,
        display_name="group1 | group2 | group3",
        path=f"{group1_id}/{group2_id}/{group3_id}",
    )

    # update group1 attrs without name
    payload = schema.PatchUserGroupRequest(id=group1.id, program_id=program.id, color_category_index=2)
    result = await controller.do_partial_update(payload=payload)
    assert result.id == group1.id
    assert result.color_category_index == 2

    # display_name unchanged
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {
        group1.id: "group1",
        group2.id: "group1 | group2",
        group3.id: "group1 | group2 | group3",
    }

    # path not changed
    assert {group.id: group.path for group in all_groups} == {
        group1.id: f"{group1.id}",
        group2.id: f"{group1.id}/{group2.id}",
        group3.id: f"{group1.id}/{group2.id}/{group3.id}",
    }

    # update group1 attrs including name
    payload = schema.PatchUserGroupRequest(id=group1.id, program_id=program.id, name="new_name")
    result = await controller.do_partial_update(payload=payload)
    assert result.id == group1.id
    assert result.name == "new_name"
    assert result.display_name == "new_name"
    assert result.color_category_index == 2

    # display_name updated
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {
        group1.id: "new_name",
        group2.id: "new_name | group2",
        group3.id: "new_name | group2 | group3",
    }

    # path not changed
    assert {group.id: group.path for group in all_groups} == {
        group1.id: f"{group1.id}",
        group2.id: f"{group1.id}/{group2.id}",
        group3.id: f"{group1.id}/{group2.id}/{group3.id}",
    }


async def test_do_partial_update_parent(app_request, mdl, faker):
    controller = UserGroupsController(request=app_request)
    program = await mdl.Programs()
    group1_id = faker.unique.random_number(3)
    group2_id = faker.unique.random_number(3)
    group3_id = faker.unique.random_number(3)

    group1 = await mdl.UserGroups(
        id=group1_id,
        program_id=program.id,
        name="group1",
        display_name="group1",
        color_category_index=1,
        path=f"{group1_id}",
    )
    group2 = await mdl.UserGroups(
        id=group2_id,
        program_id=program.id,
        name="group2",
        parent_group_id=group1.id,
        display_name="group1 | group2",
        path=f"{group1_id}/{group2_id}",
    )
    group3 = await mdl.UserGroups(
        id=group3_id,
        program_id=program.id,
        name="group3",
        parent_group_id=group2.id,
        display_name="group1 | group2 | group3",
        path=f"{group1_id}/{group2_id}/{group3_id}",
    )

    # update group2 parent to None
    payload = schema.PatchUserGroupRequest(id=group2.id, program_id=program.id, parent_group_id=None)
    result = await controller.do_partial_update(payload=payload)
    assert result.id == group2.id
    assert result.parent_group_id is None
    assert result.display_name == "group2"
    assert result.path == f"{group2.id}"

    # children updated
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {
        group1.id: "group1",
        group2.id: "group2",
        group3.id: "group2 | group3",
    }

    # path updated
    assert {group.id: group.path for group in all_groups} == {
        group1.id: f"{group1.id}",
        group2.id: f"{group2.id}",
        group3.id: f"{group2.id}/{group3.id}",
    }

    # update group2 parent to group1
    payload = schema.PatchUserGroupRequest(id=group2.id, program_id=program.id, parent_group_id=group1.id)
    result = await controller.do_partial_update(payload=payload)
    assert result.id == group2.id
    assert result.parent_group_id == group1.id
    assert result.display_name == "group1 | group2"
    assert result.path == f"{group1.id}/{group2.id}"

    # children updated
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {
        group1.id: "group1",
        group2.id: "group1 | group2",
        group3.id: "group1 | group2 | group3",
    }

    # path updated
    assert {group.id: group.path for group in all_groups} == {
        group1.id: f"{group1.id}",
        group2.id: f"{group1.id}/{group2.id}",
        group3.id: f"{group1.id}/{group2.id}/{group3.id}",
    }


async def test_do_read(app_request, mdl):
    program = await mdl.Programs()
    group1 = await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1", color_category_index=1)
    group2 = await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2", color_category_index=2)
    controller = UserGroupsController(request=app_request)

    # can read specific group
    result = await controller.do_read(id=group1.id, program_id=program.id)
    assert result.id == group1.id
    result = await controller.do_read(id=group2.id, program_id=program.id)
    assert result.id == group2.id

    # cannot read group if program id is incorrect
    result = await controller.do_read(id=group1.id, program_id=program.id + 1)
    assert result is None


async def test_do_create(app_request, mdl):
    program = await mdl.Programs()
    controller = UserGroupsController(request=app_request)

    # create group without parent
    payload = schema.CreateUserGroupRequest(name="group1", program_id=program.id, color_category_index=1)
    group1 = await controller.do_create(payload=payload)
    assert group1.name == "group1"
    assert group1.display_name == "group1"
    assert group1.color_category_index == 1
    assert group1.path == f"{group1.id}"

    # create group with parent
    payload = schema.CreateUserGroupRequest(
        name="group2",
        program_id=program.id,
        parent_group_id=group1.id,
        color_category_index=2,
        allow_self_assignment=True,
    )
    group2 = await controller.do_create(payload=payload)
    assert group2.name == "group2"
    assert group2.display_name == "group1 | group2"
    assert group2.color_category_index == 2
    assert group2.path == f"{group1.id}/{group2.id}"

    # verify both groups are created
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {
        group1.id: "group1",
        group2.id: "group1 | group2",
    }

    # create group with incorrect parent
    payload = schema.CreateUserGroupRequest(
        name="group3", program_id=program.id, parent_group_id=group2.id + 100, color_category_index=3
    )
    result = await controller.do_create(payload=payload)
    assert result is None

    async with app_request.state.sql_session() as s:
        custom_reg_input = await s.execute(select(ProgramCustomRegInputs).filter_by(program_id=program.id))
        custom_reg_input = custom_reg_input.scalars().first()
        assert custom_reg_input.config == "group1 | group2"


async def test_do_delete(app_request, mdl):
    program = await mdl.Programs()
    controller = UserGroupsController(request=app_request)
    group1 = await mdl.UserGroups(program_id=program.id, name="group1", display_name="group1", color_category_index=1)
    group2 = await mdl.UserGroups(program_id=program.id, name="group2", display_name="group2", color_category_index=2)
    group3 = await mdl.UserGroups(
        program_id=program.id,
        name="group3",
        display_name="group2 | group3",
        parent_group_id=group2.id,
        color_category_index=2,
    )
    await mdl.ProgramCustomRegInputs(
        program_id=program.id,
        type_="user_group",
        config="group1, group2, group2 | group3",
        order=0,
        validation_rule=CustomRegInputsValidationRules.none,
    )

    # delete group1
    result = await controller.do_delete(id=group1.id, program_id=program.id)
    assert result is True
    # verify group1 is deleted
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {group2.id: "group2", group3.id: "group2 | group3"}

    # verify custom reg input is updated
    async with app_request.state.sql_session() as s:
        custom_reg_input = await s.execute(select(ProgramCustomRegInputs).filter_by(program_id=program.id))
        custom_reg_input = custom_reg_input.scalars().first()
        assert custom_reg_input.config == "group2, group2 | group3"

    # delete group3 with incorrect program id
    result = await controller.do_delete(id=group3.id, program_id=program.id + 1)
    assert result is None
    # verify group3 is not deleted
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {group2.id: "group2", group3.id: "group2 | group3"}

    # delete group2 with children should fail
    result = await controller.do_delete(id=group2.id, program_id=program.id)
    assert result is None
    # verify group3 is not deleted
    all_groups = await controller.do_list()
    assert {group.id: group.display_name for group in all_groups} == {group2.id: "group2", group3.id: "group2 | group3"}


async def test_search_user_groups_by_name(app_request, mdl):

    program = await mdl.Programs()
    controller = UserGroupsController(request=app_request)

    user_group1 = await mdl.UserGroups(
        program_id=program.id,
        name="Test Group 1",
        display_name="Test Group 1",
        created_at=datetime(2024, 1, 1, 0, 0, 1),
    )
    user_group2 = await mdl.UserGroups(
        program_id=program.id,
        name="Test Group 2",
        display_name="Test Group 2",
        created_at=datetime(2024, 1, 2, 0, 0, 1),
    )
    user_group3 = await mdl.UserGroups(
        program_id=program.id, name="Test Group 1", display_name="Test Group 1", deleted_at="2024-06-05 05:00:16"
    )

    result = await controller.do_search("Test", program.id)
    assert len(result) == 2

    # order by created_at desc
    assert result[0].id == user_group2.id
    assert result[0].display_name == "Test Group 2"
    assert result[1].id == user_group1.id
    assert result[1].display_name == "Test Group 1"

    user_group_ids = [user_group.id for user_group in result]
    assert user_group3.id not in user_group_ids


async def test_search_user_groups_by_id(app_request, mdl):
    program = await mdl.Programs()
    controller = UserGroupsController(request=app_request)

    user_group1 = await mdl.UserGroups(program_id=program.id, name="Test Group 1", display_name="Test Group 1")

    result = await controller.do_search(user_group1.id, program.id)
    assert len(result) == 1
    assert result[0].id == user_group1.id
    assert result[0].display_name == "Test Group 1"


async def test_search_user_groups_no_query(app_request, mdl):
    program = await mdl.Programs()
    controller = UserGroupsController(request=app_request)

    user_group1 = await mdl.UserGroups(program_id=program.id, name="Test Group 1", display_name="Test Group 1")

    result = await controller.do_search("Test", program.id)
    assert len(result) == 1
    assert result[0].id == user_group1.id


async def test_get_children_groups(app_request, mdl, faker):
    program = await mdl.Programs()
    group1_id = faker.unique.random_number(3)
    group2_id = faker.unique.random_number(3)
    group3_id = faker.unique.random_number(3)
    group4_id = faker.unique.random_number(3)

    group1 = await mdl.UserGroups(
        id=group1_id,
        program_id=program.id,
        name="group1",
        display_name="group1",
        color_category_index=1,
        path=f"{group1_id}",
    )
    group2 = await mdl.UserGroups(
        id=group2_id,
        program_id=program.id,
        name="group2",
        parent_group_id=group1.id,
        display_name="group1 | group2",
        path=f"{group1_id}/{group2_id}",
    )
    group3 = await mdl.UserGroups(
        id=group3_id,
        program_id=program.id,
        name="group3",
        parent_group_id=group2.id,
        display_name="group1 | group2 | group3",
        path=f"{group1_id}/{group2_id}/{group3_id}",
    )
    group4 = await mdl.UserGroups(
        id=group4_id,
        program_id=program.id,
        name="group4",
        parent_group_id=group2.id,
        display_name="group1 | group2 | group4",
        path=f"{group1_id}/{group2_id}/{group4_id}",
    )

    controller = UserGroupsController(request=app_request)
    # include parent
    result = await controller.get_children_groups(parent_group_id=group1.id)
    assert len(result) == 4
    assert {group.id for group in result} == {group1.id, group2.id, group3.id, group4.id}

    result = await controller.get_children_groups(parent_group_id=group2.id)
    assert len(result) == 3
    assert {group.id for group in result} == {group2.id, group3.id, group4.id}

    result = await controller.get_children_groups(parent_group_id=group3.id)
    assert len(result) == 1

    # exclude parent
    result = await controller.get_children_groups(parent_group_id=group1.id, exclude_parent=True)
    assert len(result) == 3
    assert {group.id for group in result} == {group2.id, group3.id, group4.id}

    result = await controller.get_children_groups(parent_group_id=group2.id, exclude_parent=True)
    assert len(result) == 2
    assert {group.id for group in result} == {group3.id, group4.id}

    result = await controller.get_children_groups(parent_group_id=group3.id, exclude_parent=True)
    assert len(result) == 0


async def test_list_self_assignable_groups(app_request, mdl, faker):
    program = await mdl.Programs()
    group1_id = faker.unique.random_number(3)
    group2_id = faker.unique.random_number(3)

    # group1 is not self-assignable
    await mdl.UserGroups(
        id=group1_id,
        program_id=program.id,
        name="group1",
        display_name="group1",
        color_category_index=1,
        path=f"{group1_id}",
        allow_self_assignment=False,
    )
    group2 = await mdl.UserGroups(
        id=group2_id,
        program_id=program.id,
        name="group2",
        display_name="group2",
        color_category_index=2,
        path=f"{group2_id}",
        allow_self_assignment=True,
    )

    controller = UserGroupsController(request=app_request)
    result = await controller.list_self_assignable_groups(program_id=program.id)
    assert len(result) == 1
    assert {group.id for group in result} == {group2.id}
