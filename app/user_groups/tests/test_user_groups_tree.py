from user_groups import schema
from user_groups.tree import UserGroupTree


def test_user_group_tree():
    # create a tree with 3 groups
    group1 = schema.UserGroup(id=1, name="group1", parent_group_id=None, program_id=1, display_name="group1", path="1")
    group2 = schema.UserGroup(
        id=2, name="group2", parent_group_id=1, program_id=1, display_name="group1 | group2", path="1/2"
    )
    group3 = schema.UserGroup(
        id=3, name="group3", parent_group_id=2, program_id=1, display_name="group1 | group2 | group3", path="1/2/3"
    )
    groups = [group1, group2, group3]
    tree = UserGroupTree.build(groups=groups)

    # test get_node
    assert tree.get_node(group_id=1).group == group1
    assert tree.get_node(group_id=2).group == group2
    assert tree.get_node(group_id=3).group == group3
    assert tree.get_node(group_id=4) is None

    # test children
    root = tree.get_node(group_id=group1.id)
    assert root.group == group1
    assert root.children[0].group == group2
    assert root.children[0].children[0].group == group3
    assert root.children[0].children[0].children == ()

    # test parent
    assert tree.get_node(group_id=1).parent is None
    assert tree.get_node(group_id=2).parent.group == group1
    assert tree.get_node(group_id=3).parent.group == group2


def test_tree_update_group_properties():
    # create a tree with 3 groups
    group1 = schema.UserGroup(id=1, name="group1", parent_group_id=None, program_id=1, display_name="group1", path="1")
    group2 = schema.UserGroup(
        id=2, name="group2", parent_group_id=1, program_id=1, display_name="group1 | group2", path="1/2"
    )
    group3 = schema.UserGroup(
        id=3, name="group3", parent_group_id=2, program_id=1, display_name="group1 | group2 | group3", path="1/2/3"
    )
    groups = [group1, group2, group3]
    tree = UserGroupTree.build(groups=groups)

    # update group name
    updated_groups = tree.update_group_properties(group_id=2, updated_name="new_group2", updated_parent_group_id=1)
    assert updated_groups == [group2, group3]
    assert tree.get_node(group_id=2).group.name == "new_group2"
    assert tree.get_node(group_id=2).group.display_name == "group1 | new_group2"
    assert tree.get_node(group_id=3).group.display_name == "group1 | new_group2 | group3"

    # update group name with non-existent group
    updated_groups = tree.update_group_properties(group_id=4, updated_name="new_group4", updated_parent_group_id=2)
    assert updated_groups is None

    # update group parent to None
    updated_groups = tree.update_group_properties(group_id=2, updated_name="new_group2", updated_parent_group_id=None)
    assert updated_groups == [group2, group3]
    assert tree.get_node(group_id=2).group.name == "new_group2"
    assert tree.get_node(group_id=2).group.display_name == "new_group2"
    assert tree.get_node(group_id=2).group.path == "2"
    assert tree.get_node(group_id=3).group.display_name == "new_group2 | group3"
    assert tree.get_node(group_id=3).group.path == "2/3"

    # update group parent to another group
    updated_groups = tree.update_group_properties(group_id=2, updated_name="new_group2", updated_parent_group_id=1)
    assert updated_groups == [group2, group3]
    assert tree.get_node(group_id=2).group.name == "new_group2"
    assert tree.get_node(group_id=2).group.display_name == "group1 | new_group2"
    assert tree.get_node(group_id=2).group.path == "1/2"
    assert tree.get_node(group_id=3).group.display_name == "group1 | new_group2 | group3"
    assert tree.get_node(group_id=3).group.path == "1/2/3"

    # update group parent with non-existent group
    updated_groups = tree.update_group_properties(group_id=2, updated_name="new_group2", updated_parent_group_id=4)
    assert updated_groups is None
