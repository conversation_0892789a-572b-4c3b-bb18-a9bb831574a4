from pydantic import BaseModel, Field as PydanticField

from boundaries_service.enums import JobStatus


class BoundariesServiceJob(BaseModel):
    """
    Job ID and status of a boundaries service API async call
    """

    job_id: str = PydanticField(..., alias="jobID")
    status: JobStatus


class UnionArea(BaseModel):
    list_one_union_area_m2: float
    list_two_union_area_m2: float
    area_intersection_union_m2_both: float | None


class Intersection(BaseModel):
    intersecting_id: str
    # Intersection area divided by the area of intersecting_id boundary, ranged 0 to 100.
    percent_intersection_second: float
    # Intersection area divided by the area of boundary that is the key to feature_intersections, ranged 0 to 100.
    # See class FeatureIntersections for the field id whose area we divide by.
    percent_intersection_first: float
    area_intersection_m2: float

    @staticmethod
    def _get_rounded_percent(percent: float) -> float:
        if percent < 0 or percent > 100:
            raise ValueError(f"Percentage {percent} out of range")
        return round(percent, 2)

    @property
    def percent_second_rounded(self) -> float:
        """
        Return percent_intersection_second in the range 0..100 and rounded to 2 decimal places.
        Raises exception if result is outside range 0..100
        """
        return Intersection._get_rounded_percent(self.percent_intersection_second)

    @property
    def percent_first_rounded(self) -> float:
        """
        Return percent_intersection_first in the range 0..100 and rounded to 2 decimal places
        Raises exception if result is outside range 0..100
        """
        return Intersection._get_rounded_percent(self.percent_intersection_first)

    @property
    def intersection_m2_rounded(self) -> float:
        """
        Return intersection area rounded to 2 decimal places
        """
        return round(self.area_intersection_m2, 2)


class FeatureIntersections(BaseModel):
    # Maps md5 of boundary to other fields it intersects with. `feature_intersections` is a dict, and the key of this
    # dict is a boundary ID. This boundary ID refers to the "first" field of `percent_intersection_first` in class
    # Intersection - see class definition above.
    feature_intersections: dict[str, list[Intersection]]
