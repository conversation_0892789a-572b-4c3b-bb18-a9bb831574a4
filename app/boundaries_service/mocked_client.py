import random

from faker import Faker

from boundaries_service.schema import FeatureIntersections


async def mocked_feature_intersections_multiple(
    boundary_ids: list[str], comparison_boundary_ids: list[str]
) -> FeatureIntersections:
    src = boundary_ids[0]
    num_unique_md5s = len(set(boundary_ids) | set(comparison_boundary_ids))
    result = {
        src: [
            {
                "intersecting_id": against,
                "percent_intersection_first": 30 if num_unique_md5s <= 2 else 10,
                "percent_intersection_second": 5 if num_unique_md5s <= 2 else 50,
                # Area is ignored, so set it to a random number
                "area_intersection_m2": random.randint(10**4, 10**5),  # NOSONAR
            }
            for against in comparison_boundary_ids
            if against != src
        ]
    }
    return FeatureIntersections.parse_obj({"feature_intersections": result})


async def mocked_feature_intersections_multiple_in_return(
    boundary_ids: list[str], comparison_boundary_ids: list[str]
) -> FeatureIntersections:
    result = {
        src: [
            {
                "intersecting_id": against,
                "percent_intersection_first": 10,
                "percent_intersection_second": 20,
                # Area is ignored, so set it to a random number
                "area_intersection_m2": random.randint(10**4, 10**5),  # NOSONAR
            }
            for against in comparison_boundary_ids
            if against != src
        ]
        for src in boundary_ids
    }
    return FeatureIntersections.parse_obj({"feature_intersections": result})


async def mocked_feature_intersections_double(
    boundary_ids: list[str], comparison_boundary_ids: list[str]
) -> FeatureIntersections:
    faker_object = Faker()
    result = {
        # By omission, boundary_ids[0] implicitly has 0% overlap
        boundary_ids[1]: [
            {
                "intersecting_id": faker_object.md5(),
                "percent_intersection_first": 9,
                "percent_intersection_second": 2,
                # Area is ignored, so set it to a random number
                "area_intersection_m2": random.randint(10**4, 10**5),  # NOSONAR
            },
        ],
    }
    return FeatureIntersections.parse_obj({"feature_intersections": result})
