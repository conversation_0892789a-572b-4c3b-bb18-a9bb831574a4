from boundaries_service.enums import FeatureCollection

# ** chunk sizes **
# the chunk sizes are kept very small as to not risk failing unnecessarily if boundaries-service is under load

# this is how many MD5s we search boundaries-service for at once
MD5_SEARCH_CHUNK_SIZE = 5

# this is how many boundaries we try to create in the boundaries-service at once
BOUNDARY_CREATION_CHUNK_SIZE = 2

# If we encounter this missing string in an exception,
# its worth stripping it for boundary ids and seeing if we can get them defined
MISSING_IDS_SNIPPET = "Missing the following ids from collection"


FEATURE_COLLECTION_TO_RANK = {
    FeatureCollection.ADMIN_2_GEOBOUNDARIES_CGAZ: 0,
    FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ: 1,
    FeatureCollection.ADMIN_0_GEOBOUNDARIES_CGAZ: 2,
}
