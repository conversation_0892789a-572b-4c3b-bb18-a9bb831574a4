from datetime import datetime

import pytest

from boundaries_service import client
from boundaries_service.client import (
    _add_boundaries_service_subscriber,
    _get_prefer_header,
    is_right_situation_to_build_missing_boundaries,
    NoFertilizerDataError,
)
from boundaries_service.enums import FeatureCollection
from boundaries_service.responses import BoundariesServiceApplicationMethod
from config import get_settings

settings = get_settings()


def test_add_boundaries_service_subscriber(faker):
    payload = {"x": 1}
    response = _add_boundaries_service_subscriber(payload, None)
    assert response == {"x": 1}

    url = faker.uri()
    response = _add_boundaries_service_subscriber(payload, url)
    assert response == {
        "x": 1,
        "subscriber": {
            "successUri": url,
            "successHeaders": {
                "fs-super-admin": "true",
                "fs-user-id": settings.CORE_USER_ID,
            },
            "failedUri": url,
            "failedHeaders": {
                "fs-super-admin": "true",
                "fs-user-id": settings.CORE_USER_ID,
            },
        },
    }


def test_get_prefer_header(faker):
    assert _get_prefer_header(None) == {"prefer": "respond-sync"}
    assert _get_prefer_header(faker.uri()) == {"prefer": "respond-async"}


def test_is_right_situation_to_build_missing_boundaries():
    assert is_right_situation_to_build_missing_boundaries()


async def test_get_fertilizer_quantity_by_md5_and_crop(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH}",
        method="POST",
        json={
            "data": [
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2020,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                }
            ]
        },
        status_code=200,
    )
    quantity = await client.get_fertilizer_quantity_by_md5_and_crop_name(
        md5=md5, crop_name="corn", year=2020, region="Illinois"
    )
    assert quantity.crop_name == "corn"
    assert quantity.year == 2020
    assert quantity.n_quantity == 100.0
    assert quantity.p_quantity == 1.0
    assert quantity.k_quantity == 2.0
    assert quantity.s_quantity == 3.0


async def test_get_fertilizer_quantity_by_md5_and_crop_multiple_responses_one_collection(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH}",
        method="POST",
        json={
            "data": [
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2018,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2019,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2021,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2022,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
            ]
        },
        status_code=200,
    )
    quantity = await client.get_fertilizer_quantity_by_md5_and_crop_name(
        md5=md5, crop_name="corn", year=2020, region="Illinois"
    )
    assert quantity.crop_name == "corn"
    assert quantity.year == 2019
    assert quantity.n_quantity == 100.0
    assert quantity.p_quantity == 1.0
    assert quantity.k_quantity == 2.0
    assert quantity.s_quantity == 3.0


async def test_get_fertilizer_quantity_by_md5_and_crop_multiple_responses_multiple_collections(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH}",
        method="POST",
        json={
            "data": [
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2015,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2016,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_0_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2020,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_0_GEOBOUNDARIES_CGAZ,
                    "fertilizer_quantity.crop_name": "corn",
                    "fertilizer_quantity.year": 2021,
                    "fertilizer_quantity.n_quantity": 100.0,
                    "fertilizer_quantity.p_quantity": 1.0,
                    "fertilizer_quantity.k_quantity": 2.0,
                    "fertilizer_quantity.s_quantity": 3.0,
                },
            ]
        },
        status_code=200,
    )
    quantity = await client.get_fertilizer_quantity_by_md5_and_crop_name(
        md5=md5, crop_name="corn", year=2020, region="Illinois"
    )
    assert quantity.crop_name == "corn"
    assert quantity.year == 2016
    assert quantity.n_quantity == 100.0
    assert quantity.p_quantity == 1.0
    assert quantity.k_quantity == 2.0
    assert quantity.s_quantity == 3.0


async def test_get_fertilizer_quantity_by_md5_and_crop_empty_response(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH}",
        method="POST",
        json={"data": []},
        status_code=200,
    )
    with pytest.raises(NoFertilizerDataError) as exc:
        await client.get_fertilizer_quantity_by_md5_and_crop_name(
            md5=md5, crop_name="corn", year=2020, region="Illinois"
        )
    assert exc.value.args[0] == (
        "No fertilizer quantity information available for md5='3cf36028569cecb17c222a0099749192', "
        "crop_name='corn', year=2020, region='Illinois'."
    )


async def test_get_fertilizer_applications_by_md5_and_crop(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_APPLICATION_PATH}",
        method="POST",
        json={
            "data": [
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_application.crop_name": "corn",
                    "fertilizer_application.days_after_planting_1": 1,
                    "fertilizer_application.days_before_harvest_1": None,
                    "fertilizer_application.type_1": "urea",
                    "fertilizer_application.depth_1": 0.0,
                    "fertilizer_application.method_1": "broadcast",
                    "fertilizer_application.percentage_1": 60.0,
                    "fertilizer_application.days_after_planting_2": None,
                    "fertilizer_application.days_before_harvest_2": 1,
                    "fertilizer_application.type_2": "ammonium_nitrate",
                    "fertilizer_application.depth_2": 4.0,
                    "fertilizer_application.method_2": "subsurface",
                    "fertilizer_application.percentage_2": 40.0,
                }
            ]
        },
        status_code=200,
    )
    applications = await client.get_fertilizer_applications_by_md5_and_crop(
        md5=md5, crop_name="corn", planting_date=datetime(2020, 1, 1), region="Illinois"
    )
    assert applications.crop_name == "corn"
    assert applications.applications[0].product_name == "urea"
    assert applications.applications[0].depth == 0.0
    assert applications.applications[0].method == BoundariesServiceApplicationMethod.BROADCAST
    assert applications.applications[0].percentage == 60.0
    assert applications.applications[0].days_after_planting == 1
    assert applications.applications[0].days_before_harvest is None
    assert applications.applications[1].product_name == "ammonium_nitrate"
    assert applications.applications[1].depth == 4.0
    assert applications.applications[1].method == BoundariesServiceApplicationMethod.SUBSURFACE
    assert applications.applications[1].percentage == 40.0
    assert applications.applications[1].days_after_planting is None
    assert applications.applications[1].days_before_harvest == 1


async def test_get_fertilizer_applications_by_md5_and_crop_multiple_collections(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_APPLICATION_PATH}",
        method="POST",
        json={
            "data": [
                {
                    "feature.collection": FeatureCollection.ADMIN_0_GEOBOUNDARIES_CGAZ,
                    "fertilizer_application.crop_name": "corn",
                    "fertilizer_application.days_after_planting_1": None,
                    "fertilizer_application.days_before_harvest_1": 1,
                    "fertilizer_application.type_1": "ammonium_nitrate",
                    "fertilizer_application.depth_1": 4.0,
                    "fertilizer_application.method_1": "subsurface",
                    "fertilizer_application.percentage_1": 40.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_2_GEOBOUNDARIES_CGAZ,
                    "fertilizer_application.crop_name": "corn",
                    "fertilizer_application.days_after_planting_1": 1,
                    "fertilizer_application.days_before_harvest_1": None,
                    "fertilizer_application.type_1": "urea",
                    "fertilizer_application.depth_1": 0.0,
                    "fertilizer_application.method_1": "broadcast",
                    "fertilizer_application.percentage_1": 60.0,
                },
                {
                    "feature.collection": FeatureCollection.ADMIN_1_GEOBOUNDARIES_CGAZ,
                    "fertilizer_application.crop_name": "corn",
                    "fertilizer_application.days_after_planting_1": 3,
                    "fertilizer_application.days_before_harvest_1": 2,
                    "fertilizer_application.type_1": "potassium_chloride",
                    "fertilizer_application.depth_1": 2.0,
                    "fertilizer_application.method_1": "incorporate",
                    "fertilizer_application.percentage_1": 30.0,
                },
            ]
        },
        status_code=200,
    )
    applications = await client.get_fertilizer_applications_by_md5_and_crop(
        md5=md5, crop_name="corn", planting_date=datetime(2020, 1, 1), region="Illinois"
    )
    assert applications.crop_name == "corn"
    assert applications.applications[0].product_name == "urea"
    assert applications.applications[0].depth == 0.0
    assert applications.applications[0].method == BoundariesServiceApplicationMethod.BROADCAST
    assert applications.applications[0].percentage == 60.0
    assert applications.applications[0].days_after_planting == 1
    assert applications.applications[0].days_before_harvest is None


async def test_get_fertilizer_applications_by_md5_and_crop_empty_response(httpx_mock):
    md5 = "3cf36028569cecb17c222a0099749192"
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}",
        method="GET",
        json={"features": [{"collection": "collection_name"}]},
        status_code=200,
    )
    httpx_mock.add_response(
        url=f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_APPLICATION_PATH}",
        method="POST",
        json={"data": []},
        status_code=200,
    )
    with pytest.raises(NoFertilizerDataError):
        await client.get_fertilizer_applications_by_md5_and_crop(
            md5=md5, crop_name="corn", planting_date=datetime(2020, 1, 1), region="Illinois"
        )
