from boundaries_service.exceptions import construct_missing_md5_error
from config import get_settings

settings = get_settings()


def test_construct_missing_md5_error():
    text1 = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: [\"c4f49dc312e28251ac5b9f99bbf2cad5\"]. Missing list_two: [].\")\n"
    err1 = construct_missing_md5_error(text1, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    assert err1.get_md5s_reported_missing() == ["c4f49dc312e28251ac5b9f99bbf2cad5"]

    text2 = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: []. Missing list_two: ['c4f49dc312e28251ac5b9f99bbf2cad5'].\")\n"
    err2 = construct_missing_md5_error(text2, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    assert err2.get_md5s_reported_missing() == ["c4f49dc312e28251ac5b9f99bbf2cad5"]

    text3 = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['b4f49dc312e28251ac5b9f99bbf2cad5']. Missing list_two: ['c4f49dc312e28251ac5b9f99bbf2cad5'].\")\n"
    err3 = construct_missing_md5_error(text3, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    assert err3.get_md5s_reported_missing() == ["b4f49dc312e28251ac5b9f99bbf2cad5", "c4f49dc312e28251ac5b9f99bbf2cad5"]

    text4 = 'Missing the following ids from collection(s) [\'fields_flurosense\', \'ofs_fields\']. Missing list_one: ["20c8aaa8f4e1acd01c6839021aaeedae", "4ee611ecd82f6e6950216b872f619b01", "45c3281437798a6e672bf848b89dd28d", "05218650f9a9721d8253c0713b719102", "b3525ecc953b9baf8bbb6f94db58107f"]. Missing list_two: [].")\n'
    err4 = construct_missing_md5_error(text4, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    assert err4.get_md5s_reported_missing() == [
        "05218650f9a9721d8253c0713b719102",
        "20c8aaa8f4e1acd01c6839021aaeedae",
        "45c3281437798a6e672bf848b89dd28d",
        "4ee611ecd82f6e6950216b872f619b01",
        "b3525ecc953b9baf8bbb6f94db58107f",
    ]

    text5 = "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['20c8aaa8f4e1acd01c6839021aaeedae', '4ee611ecd82f6e6950216b872f619b01']. Missing list_two: ['b4f49dc312e28251ac5b9f99bbf2cad5', 'c4f49dc312e28251ac5b9f99bbf2cad5'].\")\n"
    err5 = construct_missing_md5_error(text5, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)
    assert err5.get_md5s_reported_missing() == [
        "20c8aaa8f4e1acd01c6839021aaeedae",
        "4ee611ecd82f6e6950216b872f619b01",
        "b4f49dc312e28251ac5b9f99bbf2cad5",
        "c4f49dc312e28251ac5b9f99bbf2cad5",
    ]
