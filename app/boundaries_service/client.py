"""
A client to fetch information from boundaries service
"""

import json
import time
from collections.abc import Generator
from datetime import datetime
from typing import Any, Literal, Optional, overload

import backoff
import httpx
from async_lru import alru_cache
from curlify2 import Curlify
from fastapi import HTTPException
from httpcore import ReadError
from httpx import ConnectError, ConnectTimeout, ReadError as HttpxReadError, Response
from starlette import status

from boundaries_service.constants import (
    BOUNDARY_CREATION_CHUNK_SIZE,
    FEATURE_COLLECTION_TO_RANK,
    MD5_SEARCH_CHUNK_SIZE,
    MISSING_IDS_SNIPPET,
)
from boundaries_service.dataclasses import KMLFileData, KMLFileUpdateData
from boundaries_service.enums import FeatureCollection
from boundaries_service.exceptions import construct_missing_md5_error
from boundaries_service.responses import (
    BoundariesServiceCollectionFeatures,
    BoundariesServiceFertilizerApplications,
    BoundariesServiceFertilizerQuantity,
)
from boundaries_service.schema import (
    BoundariesServiceJob,
    FeatureIntersections,
    UnionArea,
)
from config import get_settings
from fields.custom_dataclasses import BoundingBox
from fields.schema import Field
from http_requests.crons.constants import DEFAULT_COLLECTIONS
from logger import get_logger

settings = get_settings()
logger = get_logger(__name__)


def is_502_503(response: Response) -> bool:
    return response.status_code in {502, 503}


#  We use exponential backoff with jitter to mitigate contention for a shared resource
def backoff_expo() -> Generator[float, Any, None]:
    return backoff.expo(factor=5)


@alru_cache
async def _get_collection_name_for_md5(md5: str) -> str:
    """
    To be able to query information about fertilizers by a field's MD5, we have to additionally pass information
    about what boundaries service collection that boundary is in.
    As of 11/27, you don't NEED to pass this collection name, but the subsequent queries WITHOUT collection name will
    be significantly slower (several seconds vs tens of milliseconds) without it.
    """
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}"
    query_params = {"ids": md5}
    if settings.BOUNDARIES_SERVICE_SEARCH_BYPASS_CACHE:
        query_params["time"] = str(int(time.time()))

    @backoff.on_exception(
        wait_gen=backoff_expo, exception=(ReadError, HttpxReadError, ConnectError, ConnectTimeout), max_tries=4
    )
    @backoff.on_predicate(wait_gen=backoff_expo, predicate=is_502_503, max_tries=4)
    async def make_get_with_retries(get_url: str, params: dict) -> Response:
        return await client.get(get_url, params=params)

    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await make_get_with_retries(url, query_params)
        if response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": f"Unable to fetch field boundary collection name for md5 {md5}. Boundaries Service returned status code {response.status_code}."
                },
            )

        try:
            # Pluck out the feature and return its collection name
            return response.json()["features"][0]["collection"]
        except IndexError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Field does not belong to any boundary collection"},
            )


@backoff.on_exception(
    wait_gen=backoff_expo, exception=(ReadError, HttpxReadError, ConnectError, ConnectTimeout), max_tries=4
)
@backoff.on_predicate(wait_gen=backoff_expo, predicate=is_502_503, max_tries=4)
async def make_post_with_retries(
    client: httpx.AsyncClient, post_url: str, json: dict, headers: dict[str, str] | None = None
) -> Response:
    return await client.post(post_url, json=json, headers=headers, timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT)


class NoFertilizerDataError(Exception):

    def __init__(
        self,
        message: str,
        md5: str,
        crop_name: str,
        season: str | None = None,
        year: int | None = None,
        region: str | None = None,
        extra: dict[str, Any] | None = None,
    ) -> None:
        self.message = message
        self.md5 = md5
        self.crop_name = crop_name
        self.season = season
        self.year = year
        self.region = region
        self.extra = extra
        super().__init__(message)


async def get_fertilizer_applications_by_md5_and_crop(
    md5: str, crop_name: str, planting_date: datetime, region: str | None = None
) -> BoundariesServiceFertilizerApplications:
    """
    Given a field's md5 + a crop name, fetch info on Fertilizer number, timing, and product from boundaries service.
    This information is year-agnostic, unlike quantity information.

    Params:
        md5: the Boundaries Service md5 to query by
        crop_name
        planting_date: used to specify a planting season for the dataset query
        region: optionally provided for generating more usable error messages when fertilizer quantity isn't found
    """
    season = "spring" if planting_date.month <= 6 else "winter"
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_APPLICATION_PATH}"
    # Get the collection that the field md5 belongs to
    collection_name = await _get_collection_name_for_md5(md5)
    data = {
        "filter-lang": "intersecting-feature",
        "filter": {
            "boundary_id": md5,
            "collection_id": collection_name,
        },
        "data-filter": f"crop_name = '{crop_name}' AND (season = '{season}' OR season = 'season_agnostic')",
    }

    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await make_post_with_retries(client, url, data)

        # Handle non-200 status codes
        if response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Boundaries Service fertilizer application dataset returned a non-200 status code."},
            )

        if len(response.json()["data"]) == 0:
            raise NoFertilizerDataError(
                md5=md5,
                crop_name=crop_name,
                season=season,
                region=region,
                message=f"No fertilizer quantity information available for {md5=}, {crop_name=}, {season=}, {region=}.",
            )
        most_specific_response_data = min(
            response.json()["data"],
            key=lambda x: FEATURE_COLLECTION_TO_RANK[FeatureCollection(x.get("feature.collection"))],
        )

        # Marshall to BoundariesServiceFertilizerApplications
        return BoundariesServiceFertilizerApplications.parse_from_api_response(most_specific_response_data)


async def get_fertilizer_quantity_by_md5_and_crop_name(
    md5: str, crop_name: str, year: int, region: str | None = None
) -> BoundariesServiceFertilizerQuantity:
    """
    Given a field's md5, crop name, and year, fetch fertilizer quantity info from Boundaries Service fertilizer dataset.

    Params:
        md5: the Boundaries Service md5 to query by
        crop_name
        year: data from the provided year +/- 5 years is fetched, and the data closest to the provided year is selected
        region: optionally provided for generating more usable error messages when fertilizer quantity isn't found
    """
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH}"
    collection_name = await _get_collection_name_for_md5(md5)
    query_years = ",".join(map(str, range(year - 5, year + 6)))
    data = {
        "filter-lang": "intersecting-feature",
        "filter": {
            "boundary_id": md5,
            "collection_id": collection_name,
        },
        "data-filter": f"crop_name = '{crop_name}' AND year IN ({query_years})",
    }
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await make_post_with_retries(client, url, data)
        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail={
                    "message": f"Boundaries service fertilizer quantity query returned status code {response.status_code}."
                },
            )
        if len(response.json()["data"]) == 0:
            raise NoFertilizerDataError(
                md5=md5,
                crop_name=crop_name,
                year=year,
                region=region,
                message=f"No fertilizer quantity information available for {md5=}, {crop_name=}, {year=}, {region=}.",
            )
        most_specific_response_data = min(
            response.json()["data"],
            key=lambda x: (
                FEATURE_COLLECTION_TO_RANK[FeatureCollection(x.get("feature.collection"))],
                abs(x.get("fertilizer_quantity.year", datetime.max.year) + 0.25 - year),
            ),
        )
        # Marshall to BoundariesServiceFertilizerQuantity
        return BoundariesServiceFertilizerQuantity.parse_from_api_response(most_specific_response_data)


async def get_collection_features_by_coordinates(
    collection_name: str, bbox: BoundingBox, exclude_ids: list[str], limit: int = 1000
) -> list[BoundariesServiceCollectionFeatures]:
    path = (
        f"{settings.BOUNDARIES_SERVICE_COLLECTION_FEATURES_PATH.format(collection_id=collection_name)}"
        f"?limit={limit}"
        f"&bbox={bbox.southwest_point.long},{bbox.southwest_point.lat},{bbox.northeast_point.long},{bbox.northeast_point.lat}"
    )
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{path}"

    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await client.get(url)

        if response.status_code == 422:
            logger.error(f"Status {response.status_code} received from Boundaries Service GET {url} for bbox {bbox}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail={"message": "Invalid coordinates provided"}
            )

        if response.status_code != 200:
            logger.error(f"Status {response.status_code} received from Boundaries service GET {url} for bbox {bbox}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Unable to fetch collection features information"},
            )

        try:
            response_data = response.json()["features"]
        except Exception as e:
            logger.error("Boundaries service is missing features list for the given coordinates: %s", e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "No features list available for given coordinates"},
            )

        geometries = []
        for feature in response_data:
            if feature["properties"]["id"] not in exclude_ids:
                geometries.append(BoundariesServiceCollectionFeatures.parse_from_api_response(feature))

        # Marshall to BoundariesServiceCollectionFeatures
        return geometries


def _add_boundaries_service_subscriber(payload: dict[str, Any], url: str | None) -> dict[str, Any]:
    """
    If we have a callback URL, we need to add subscriber information to the payload. The same URL is used for both
    success and failure.
    """
    if url:
        # These headers are needed to authenticate the callback request
        headers = {
            "fs-super-admin": "true",
            "fs-user-id": settings.CORE_USER_ID,
        }
        payload["subscriber"] = {
            "successUri": url,
            "successHeaders": headers,
            "failedUri": url,
            "failedHeaders": headers,
        }
    return payload


def _get_prefer_header(callback_url: str | None) -> dict[str, str]:
    """
    Boundaries `prefer` header is `respond-async` if a callback URL is provided, otherwise it's `respond-sync`.
    """
    return {"prefer": "respond-async" if callback_url else "respond-sync"}


async def union_areas(
    list_one_ids: list[str], list_two_ids: list[str], callback_url: str | None
) -> BoundariesServiceJob | UnionArea:
    """
    Calls the boundaries service endpoint `union-areas`. For more information, see the API documentation:
    http://boundaries-service-qa.int.dev.regrow.cloud/docs#tag/OGC-API-Processes/operation/Union_Areas_processes_union_areas_execution_post

    At the time of writing, this endpoint only works in async mode, so a callback URL is required.

    For async calls, use get_job() and get_job_results() to get the status and results of the job once the job is
    complete.
    """
    url = settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE + settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH
    data = _add_boundaries_service_subscriber(
        {
            "inputs": {
                "list_one": list_one_ids,
                "list_two": list_two_ids,
                "collections": DEFAULT_COLLECTIONS,
            },
            "outputs": {},
            "response": "value",
        },
        callback_url,
    )
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await make_post_with_retries(client, url, data, headers=_get_prefer_header(callback_url))
        if response.status_code >= 400:
            _raise_on_missing_boundary_response(response, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)

            logger.error(f"Boundaries service error {response.status_code} Curl: {Curlify(response.request).to_curl()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": f"endpoint {settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH} returned {response.status_code}",
                    "response_text": response.text,
                },
            )
        data = response.json()
        return BoundariesServiceJob.parse_obj(data) if callback_url else UnionArea.parse_obj(data)


def _raise_on_missing_boundary_response(resp: Response, endpoint: str) -> None:
    """
    On missing MD5 responses to union_areas, such as
    HTTPException(status_code=400, detail=\"Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['c4f49dc312e28251ac5b9f99bbf2cad5']. Missing list_two: [].\")\n"}]}
    we instead raise a MissingMD5Error, from which we have the opportunity to recover and retry
    """
    if resp.status_code > 400:
        return
    detail = resp.json().get("detail")
    if detail is None:
        return
    if detail.find(MISSING_IDS_SNIPPET) == -1:
        return
    exc = construct_missing_md5_error(detail, endpoint)
    logger.error(f"Attempting to handle missing boundary error: {exc}")
    raise exc


@overload
async def feature_intersections(
    boundary_ids: list[str], comparison_boundary_ids: list[str], callback_url: str
) -> BoundariesServiceJob: ...


@overload
async def feature_intersections(
    boundary_ids: list[str], comparison_boundary_ids: list[str], callback_url: Literal[None] = None
) -> FeatureIntersections: ...


async def feature_intersections(
    boundary_ids: list[str], comparison_boundary_ids: list[str], callback_url: str | None = None
) -> BoundariesServiceJob | FeatureIntersections:
    """
    Calls the boundaries service endpoint `feature-intersections`. For more information, see the API documentation:
    http://boundaries-service-qa.int.dev.regrow.cloud/docs#tag/OGC-API-Processes/operation/Feature_Intersections_processes_feature_intersections_execution_post

    Unlike `union-areas`, this endpoint works in both sync and async modes. If a callback URL is provided, the response
    will be a `BoundariesServiceJob` object, otherwise it will be a `FeatureIntersections` object.

    For async calls, use get_job() and get_job_results() to get the status and results of the job once the job is
    complete.
    """
    url = settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE + settings.BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH
    data = _add_boundaries_service_subscriber(
        {
            "inputs": {
                "boundary_ids": boundary_ids,
                "comparison_boundary_ids": comparison_boundary_ids,
                "collections": DEFAULT_COLLECTIONS,
            },
            "outputs": {},
            "response": "value",
        },
        callback_url,
    )
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await make_post_with_retries(client, url, data, headers=_get_prefer_header(callback_url))
        if response.status_code >= 400:
            _raise_on_missing_boundary_response(response, settings.BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH)
            logger.error(f"Boundaries service error {response.status_code} Curl: {Curlify(response.request).to_curl()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": f"endpoint {settings.BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH} returned {response.status_code}",
                    "response_text": response.text,
                },
            )
        data = response.json()
        return BoundariesServiceJob.parse_obj(data) if callback_url else FeatureIntersections.parse_obj(data)


async def get_job(job_uuid: str) -> dict:
    """
    When you make an async call, you get back a UUID job id. This call lets you fetch the status of that job.

    See the API documentation for more information:
    http://boundaries-service-qa.int.dev.regrow.cloud/docs#tag/OGC-API-Processes/operation/get_job_status_jobs__job_id__get
    """
    path = settings.BOUNDARIES_SERVICE_JOB_PATH.format(job_uuid=job_uuid)
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{path}"
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await client.get(url)
        return response.json()


async def get_job_results(job_uuid: str) -> dict:
    """
    When you make an async call, you get back a UUID job id. This call lets you fetch the final result of that job.

    See the API documentation for more information:
    http://boundaries-service-qa.int.dev.regrow.cloud/docs#tag/OGC-API-Processes/operation/get_job_results_jobs__job_id__results_get
    """
    path = settings.BOUNDARIES_SERVICE_JOB_RESULTS_PATH.format(job_uuid=job_uuid)
    url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{path}"
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        response = await client.get(url)
        return response.json()


async def determine_which_md5s_are_missing(md5s: list[str]) -> list[str]:
    """
    This function determines which md5s are missing from a given list
    """
    # the time query param is a hack to prevent the query from being cached
    search_boundary_base_url = (
        settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE
        + settings.BOUNDARIES_SERVICE_SEARCH_PATH
        + f"?time={int(time.time())}"
    )
    existing_md5s = []
    for chunk in _lst_to_chunks(md5s, MD5_SEARCH_CHUNK_SIZE):
        url = search_boundary_base_url
        for md5 in chunk:
            url = url + "&ids=" + md5
        res = await _get_searched_boundaries(url)
        if res.status_code > 299:
            logger.error(
                f"unable to determine missing md5s; received status code {res.status_code}: {res.json()} for MD5s: {chunk}"
            )
            continue
        features = res.json()["features"]
        for feature in features:
            existing_md5s.append(feature["id"])
    missing_md5s = set(md5s) - set(existing_md5s)
    return list(missing_md5s)


async def _get_searched_boundaries(url: str) -> Response:
    """
    This is separated out of determine_which_md5s_are_missing to allow mocking;
    time and random md5s making mocking hard otherwise.  It's probably best to consider it
    internal to determine_which_md5s_are_missing and not use it separately.
    """
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        res = await client.get(url)
    return res


async def create_boundaries(kml_files: list[KMLFileData]) -> list[KMLFileUpdateData]:
    """
    This function is for declaring a batches of boundaries in boundares service and
    obtaining properties from the results
    """
    kml_file_updates = []
    create_boundary_base_url = settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE + "collections/fields_flurosense/items"

    for chunk in _lst_to_chunks(kml_files, BOUNDARY_CREATION_CHUNK_SIZE):
        features = [{"type": "Feature", "geometry": kml_file.geojson, "properties": None} for kml_file in chunk]
        body = {"type": "FeatureCollection", "features": features}
        async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
            res = await make_post_with_retries(client, create_boundary_base_url, body)
        if res.status_code > 299:
            logger.error(
                f"got status code {res.status_code}: {res.json()} for KML IDs: {[kml_file.kml_id for kml_file in chunk]}"
            )
            continue
        features = res.json()["features"]
        for feature in features:
            kml_id = _get_kml_id_by_geojson(feature["geometry"], chunk)
            if kml_id is None:
                continue
            kml_file_update = KMLFileUpdateData(
                kml_id=kml_id,
                md5=feature["id"],
                area=feature["properties"]["area_m2"] / 10000,
            )
            kml_file_updates.append(kml_file_update)
    return kml_file_updates


def _lst_to_chunks(lst: list[Any], chunk_size: int) -> Generator[list[Any], None, None]:
    for i in range(0, len(lst), chunk_size):
        yield lst[i : i + chunk_size]


# the boundary service search response may contain an original ID field.
# however, this represents the ID of the FIRST record that was sent for that boundary.
# if a subsequent record is sent for the same boundary, but that record has a
# different ID, then we do NOT get the different ID back as the original ID.
# therefore, in a bulk search, instead of matching the responses to the records
# on ID, we match on geoJSON (not the best).
def _get_kml_id_by_geojson(geojson: dict, kml_files: list[KMLFileData]) -> Optional[int]:
    geojson_str = json.dumps(geojson)
    for kml_file in kml_files:
        if geojson_str == json.dumps(kml_file.geojson):
            return kml_file.kml_id
    return None


def is_right_situation_to_build_missing_boundaries() -> bool:
    """
    we have to be in an environment where either we are using one of
    * both the production MRV database and the production boundary service (prod)
    * both the dev MRV database and the QA boundary service (dev, dev1, dev7, dev8, dev9, dev10)
    * test environments for both (local)

    """
    return settings.env in ["dev", "dev1", "dev7", "dev8", "dev9", "dev10", "prod", "local"]


async def get_geometry_for_md5(md5: str) -> str:
    """Fetch geometry from boundaries service"""
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        url = f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?ids={md5}"
        if settings.BOUNDARIES_SERVICE_SEARCH_BYPASS_CACHE:
            url += f"&time={int(time.time())}"
        res = await client.get(url)
        if res.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Boundaries service did not return a geometry for field md5: {md5}. Reason: {res.text}",
            )
        # Get features[0].geometry, if exists
        features = res.json().get("features", [])
        if not features:
            raise ValueError(f"No features found for md5 {md5}")

        return json.dumps(features[0].get("geometry"))


async def get_geometries_for_fields(fields: list[Field]) -> dict[int, dict[str, Any]]:
    """Fetch field boundary geometries from Boundaries Service"""
    async with httpx.AsyncClient(timeout=settings.EXTERNAL_HTTP_REQUEST_TIMEOUT) as client:
        md5s = [fld.md5 for fld in fields]
        url = (
            f"{settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE}{settings.BOUNDARIES_SERVICE_SEARCH_PATH}?&ids="
            + "&ids=".join(md5s)
        )
        if settings.BOUNDARIES_SERVICE_SEARCH_BYPASS_CACHE:
            url += f"&time={int(time.time())}"
        res = await client.get(url)
        if res.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Boundaries service returned status code {res.status_code} while looking up geometries for "
                f"md5s: {md5s}. Reason: {res.text}",
            )
        features = res.json().get("features", [])
        geoms_by_md5 = {feat["id"]: feat["geometry"] for feat in features}
        missing_md5s = md5s - geoms_by_md5.keys()
        if len(missing_md5s) > 0:
            raise ValueError(f"No features found for md5s {missing_md5s}")
        return {fld.id: geoms_by_md5[fld.md5] for fld in fields}
