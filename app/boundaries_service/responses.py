import enum
from typing import Optional, Type

from geojson_pydantic import MultiPolygon, Polygon
from pydantic import BaseModel, Field, root_validator


class BoundariesServiceApplicationMethod(enum.StrEnum):
    """
    Describes the various methods that can be returned from Boundaries service. The value side of the enum needs to
    be the value that is returned by boundaries service.

    These methods are supposed to match-by-value the list of SS-accepted ApplicationMethods, but for now (12/06/2023)
    the only methods that will be returned are (broadcast,subsurface).
    """

    BROADCAST = "broadcast"
    SUBSURFACE = "subsurface"
    INCORPORATE = "incorporate"
    INJECT = "inject"


class BoundariesServiceFertilizerApplication(BaseModel):
    """
    Describes an individual application of a specific fertilizer, termed as a percentage of the season's aggregate
    amount of fertilizer (not described here). The timing of the fertilizer is described relative to planting or harvest

    This information can be interpreted together with additional Fertilizer Quantity information to infer/gapfill a
    collection of likely ApplicationEvents for a CroppingEvent, assuming that nutrient management info was not
    collected directly from the grower.

    It seems that sometimes days_after_planting and days_before_harvest will be specified as negative numbers (eg to
    indicate that fertilization happened before planting)
    """

    days_after_planting: Optional[int]
    days_before_harvest: Optional[int]
    product_name: str
    depth: int = Field(ge=0)
    method: BoundariesServiceApplicationMethod
    percentage: float = Field(gt=0, le=100)

    @root_validator
    @classmethod
    def exactly_one_of_days_attributes_specified(cls, values: dict) -> dict:
        none_specified = values.get("days_after_planting") is None and values.get("days_before_harvest") is None
        both_specified = values.get("days_after_planting") is not None and values.get("days_before_harvest") is not None

        if none_specified or both_specified:
            raise ValueError("Only one of 'days_after_planting' and 'days_before_harvest' can be specified")

        return values


class BoundariesServiceFertilizerApplications(BaseModel):
    """
    A collection of BoundariesServiceFertilizerApplications as interpreted from responses re: BoundariesService's
    Fertilizer Applications dataset.
    """

    crop_name: str
    applications: list[BoundariesServiceFertilizerApplication]

    @classmethod
    def parse_from_api_response(cls, response: dict) -> Type["BoundariesServiceFertilizerApplications"]:
        """
        Parse a FertilizerApplicationsResponse from the response from
        POST boundaries-service//datasets/fertilizer_application/1.0.0/data
        """

        def _is_application_populated(number: int) -> bool:
            """
            This just checks that a certain application number `number` within the BoundariesService response actually
            represents an application.
            There are 5 applications returned in each response, even if only two of them are populated with data.
            """
            application_timing_attributes = [
                f"fertilizer_application.days_after_planting_{number}",
                f"fertilizer_application.days_before_harvest_{number}",
            ]
            # If there's no timing information, application N is assumed not to have occurred.
            return any(response.get(attr) is not None for attr in application_timing_attributes)

        crop_name = response["fertilizer_application.crop_name"]
        applications = [
            {
                "days_after_planting": response.get(f"fertilizer_application.days_after_planting_{application_number}"),
                "days_before_harvest": response.get(f"fertilizer_application.days_before_harvest_{application_number}"),
                "product_name": response.get(f"fertilizer_application.type_{application_number}"),
                "depth": response.get(f"fertilizer_application.depth_{application_number}"),
                "method": response.get(f"fertilizer_application.method_{application_number}"),
                "percentage": response.get(f"fertilizer_application.percentage_{application_number}"),
            }
            for application_number in [1, 2, 3, 4, 5]
            if _is_application_populated(application_number)
        ]

        return cls(crop_name=crop_name, applications=applications)


class BoundariesServiceFertilizerQuantity(BaseModel):
    """
    As per the Fertilizer Quantity dataset from BoundariesService, describes an aggregate amount of fertilizer applied
    over the entire growing season of a crop in a specific year (relevant to a specific geometry).

    This information can be interpreted together with additional application timing data from Boundaries Service to
    infer/gapfill a collection of likely ApplicationEvents for a CroppingEvent, assuming that nutrient management info
    was not collected directly from the grower.
    """

    crop_name: str
    year: int = Field(gt=0)
    n_quantity: float = Field(ge=0)
    p_quantity: Optional[float] = Field(ge=0)
    k_quantity: Optional[float] = Field(ge=0)
    s_quantity: Optional[float] = Field(ge=0)

    @classmethod
    def parse_from_api_response(cls, response: dict) -> Type["BoundariesServiceFertilizerQuantity"] | None:
        """
        Parse a FertilizerQuantityResponse from the response from
        POST boundaries-service//datasets/fertilizer_quantity/1.0.0/data
        """
        return cls(
            crop_name=response.get("fertilizer_quantity.crop_name"),
            year=response.get("fertilizer_quantity.year"),
            n_quantity=response.get("fertilizer_quantity.n_quantity"),
            p_quantity=response.get("fertilizer_quantity.p_quantity"),
            k_quantity=response.get("fertilizer_quantity.k_quantity"),
            s_quantity=response.get("fertilizer_quantity.s_quantity"),
        )


class BoundariesServiceCollectionFeaturesProperties(BaseModel):
    id: str  # noqa: A003, VNE003
    area_m2: float


class BoundariesServiceCollectionFeatures(BaseModel):
    type: str = "Feature"  # noqa: A003, VNE003
    geometry: MultiPolygon | Polygon
    properties: BoundariesServiceCollectionFeaturesProperties

    @classmethod
    def parse_from_api_response(cls, response: dict) -> "BoundariesServiceCollectionFeatures":
        """
        Parse a CollectionFeaturesResponse from the response from
        GET collections/{collection_id}/items
        """
        return cls(
            geometry=response.get("geometry"),
            properties=BoundariesServiceCollectionFeaturesProperties(**response.get("properties", {})),
        )
