from enum import auto, StrEnum


class JobStatus(StrEnum):
    """
    Job status of boundaries service API call
    """

    accepted = auto()
    running = auto()
    successful = auto()
    failed = auto()
    # We set `dismissed` for instances where no boundaries service call was needed
    dismissed = auto()


class FeatureCollection(StrEnum):
    ADMIN_0_GEOBOUNDARIES_CGAZ = "admin_0_geoboundaries_cgaz"
    ADMIN_1_GEOBOUNDARIES_CGAZ = "admin_1_geoboundaries_cgaz"
    ADMIN_2_GEOBOUNDARIES_CGAZ = "admin_2_geoboundaries_cgaz"
