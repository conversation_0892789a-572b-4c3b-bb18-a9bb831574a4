import re

from fastapi import HTTPException
from starlette import status

from config import get_settings

settings = get_settings()

# extract lower-case hexidecimal strings in quotes and exactly 32 characters long
full_pattern = "(?:'|\")" + "((?:[a-f]|[0-9]){32})" + "(?:'|\")"
MISSING_UUID_PATTERN = re.compile(full_pattern)


class MissingMD5Error(Exception):
    """
    This error is thrown if we find missing UUIDs in calls to boundaries service
    """

    def __init__(self, message: str, missing_uuids: list[str], endpoint: str):
        # Call the base class constructor with the parameters it needs
        super().__init__(message)
        self.message = message
        self.missing_uuids = sorted(missing_uuids)
        self.endpoint = endpoint

    def get_md5s_reported_missing(self) -> list[str]:
        return self.missing_uuids

    def get_underlying_http_exception(self) -> HTTPException:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"endpoint {self.endpoint} returned 400",
                "response_text": self.message,
            },
        )


def construct_missing_md5_error(message: str, endpoint: str) -> MissingMD5Error:
    """
    Extract the missing UUIDs out of the message and construct the resulting error
    """
    # "Missing the following ids from collection(s) ['fields_flurosense', 'ofs_fields']. Missing list_one: ['c4f49dc312e28251ac5b9f99bbf2cad5']. Missing list_two: []."
    missing_uuids = set(MISSING_UUID_PATTERN.findall(message))
    return MissingMD5Error(message, list(missing_uuids), endpoint)


def construct_missing_md5_error_for_unions(message: str) -> MissingMD5Error:
    return construct_missing_md5_error(message, settings.BOUNDARIES_SERVICE_UNION_AREAS_PATH)


def construct_missing_md5_error_for_feature_intersections(message: str) -> MissingMD5Error:
    return construct_missing_md5_error(message, settings.BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH)
