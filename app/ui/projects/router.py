from __future__ import annotations

from typing import Dict, List, Optional

import elasticapm
from fastapi import Depends, Request, status
from fastapi.exceptions import HTTPException

from entity_events.events.entity_event import EntityEvent
from entity_events.methods import (
    get_entity_events_for_field,
    get_entity_events_for_project_phase,
)
from fields.model import Fields
from fields.schema import Field
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from phases.db import get_phase_by_id, get_phase_by_stage_id
from phases.enums import PhaseTypes
from phases.model import Phases, Stage
from phases.schema import StageRequestNoParent
from projects.db import get_program_id_by_project_id
from projects.model import Projects
from root_crud import get
from ui.projects.completion.annotations import (
    CompletionForFieldsResponse,
    StagePercentageCompletionForPhaseResponse,
)
from ui.projects.completion.handle_bulk_field_completion_updates import (
    attempt_updating_project_completion_for_program,
)
from ui.projects.completion.provide_completion_results import (
    obtain_stage_completion_for_fields,
    obtain_stage_completion_percentages_for_phase,
)
from ui.projects.completion.schema import ProjectCompletionPreparationRequest
from ui.projects.completion.tasks import (
    run_completion_preparation_as_necessary,
    run_completion_update_for_projects_in_shared_program,
)
from ui.projects.field_events.schema import (
    CultivationCycleFromMRVValues,
    FieldEventFromMRVValues,
)
from ui.projects.methods import (
    get_project_field_events_from_mrv_values,
    mrv_values_cultivation_cycles_to_ui_events,
)
from ui.projects.paths import (
    project_completion_for_programs_path,
    project_events,
    stage_completion_of_fields_path,
    stage_completion_percentages_for_phase_path,
    stage_cultivation_cycles,
)
from ui.router import router, tags
from values.enums import EntityTypeChoices

logger = get_logger(__name__)


@elasticapm.async_capture_span()
@router.get(
    path=project_events,  # /ui/projects/{project_id}/events
    status_code=status.HTTP_200_OK,
    tags=tags,
    response_model=dict[str, list[FieldEventFromMRVValues]],
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_project_mrv_values_events(
    request: Request,
    project_id: int,
    phase_id: Optional[int] = None,
    stage_id: Optional[int] = None,
    event_type: Optional[str] = None,
    field_id: Optional[int] = None,
) -> dict[int, list[FieldEventFromMRVValues]]:
    # Validate the project ID
    try:
        await get.check_or_404_by_id(request, project_id, Projects, Projects.id)
    except HTTPException as http_exc:
        http_exc.detail = {"message": "Project for project ID %d not found" % project_id}

    return await get_project_field_events_from_mrv_values(
        request=request,
        project_id=project_id,
        phase_id=phase_id,
        stage_id=stage_id,
        event_type=event_type,
        field_id=field_id,
    )


@router.get(
    path=stage_cultivation_cycles,
    response_model=dict[str, list[CultivationCycleFromMRVValues]],
    status_code=status.HTTP_200_OK,
    tags=tags,
    description="Retrieve all cultivation cycles generated from the mrv-values derrived events in a given stage and optional field",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_stage_cultivation_cycles(
    request: Request, project_id: int, stage_id: int, field_id: Optional[int] = None
) -> dict[int, list[CultivationCycleFromMRVValues]]:
    # If the project is invalid, short circuit the API request and fail immediately
    try:
        await get.check_or_404_by_id(request, project_id, Projects, Projects.id)
    except HTTPException as http_exc:
        http_exc.detail = {"message": "Project for project ID %d not found" % project_id}

    stages: list[StageRequestNoParent] = await get.get(
        request=request,
        orm_type=Stage,
        type_=StageRequestNoParent,
        ids=[stage_id],
        id_field=Stage.id,
        empty_return=True,
    )
    if not stages:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail={"message": "Stage ID %d not found" % stage_id}
        )
    stage: StageRequestNoParent = stages[0]

    # Retrieve phase associated with the given stage ID
    phase: Phases = await get_phase_by_stage_id(request, stage_id)
    if phase is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail={"message": "Phase for stage ID %d not found" % stage_id}
        )

    # Construct a map from field IDs to associated entity events
    field_id_to_events: dict[int, list[EntityEvent]] = {}
    if field_id is None:
        try:
            for event in await get_entity_events_for_project_phase(
                request, project_id, phase.type_, EntityTypeChoices.field
            ):
                if event.entity_id not in field_id_to_events:
                    field_id_to_events[event.entity_id] = []
                field_id_to_events[event.entity_id].append(event)
        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Error retrieving entity events for project %d with error - %s" % (project_id, str(ve))
                },
            )
    else:
        try:
            fields: List[Field] = await get.get(
                request=request, orm_type=Fields, type_=Field, ids=[field_id], id_field=Fields.id
            )
        except HTTPException as http_exc:
            http_exc.detail = {"message": f"Field for field ID {field_id} not found"}
            raise http_exc

        try:
            entity_events_for_field: Dict[PhaseTypes, List[EntityEvent]] = await get_entity_events_for_field(
                request, fields[0]
            )
            entity_events: list[EntityEvent] = []
            for event in entity_events_for_field.get(phase.type_, []):
                entity_events.append(event)
        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": f"Error retrieving entity events for field {field_id} with error - {str(ve)}"},
            )

        if entity_events:
            field_id_to_events = {field_id: entity_events}

    return mrv_values_cultivation_cycles_to_ui_events(field_id_to_events, stage.type_, phase.type_, stage_id)


# /ui/projects/{project_id}/stages/{stage_id}/fields/completion?field_id
@router.get(
    path=stage_completion_of_fields_path,
    response_model=CompletionForFieldsResponse,
    status_code=status.HTTP_200_OK,
    tags=tags,
    description="Retreive whether each field (or a single field) in a project is complete for the given phase",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_binary_completion_of_project_fields_for_stage(
    request: Request, project_id: int, stage_id: int, field_id: Optional[int] = None
) -> CompletionForFieldsResponse:
    return await obtain_stage_completion_for_fields(request, project_id, stage_id, field_id)


# /ui/projects/{project_id}/phases/{phase_id}/completion
@router.get(
    path=stage_completion_percentages_for_phase_path,
    response_model=StagePercentageCompletionForPhaseResponse,
    status_code=status.HTTP_200_OK,
    tags=tags,
    description="Retreive the completion percentage for each stage in a phase",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_completion_percentages_for_stages_in_phase(
    request: Request, project_id: int, phase_id: int
) -> StagePercentageCompletionForPhaseResponse:
    phase = await get_phase_by_id(request, phase_id)
    program_id = await get_program_id_by_project_id(request, project_id)
    if (phase is None) or (phase.program_id != program_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "The given phase is not a phase of the project's program",
            },
        )
    return await obtain_stage_completion_percentages_for_phase(request, project_id, phase, program_id)


@router.post(
    path="/prepare-completion-totals-for-projects",
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[
        Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))
    ],  # this could be called by the migration infrastructure
)
async def post_projects_for_eager_completion_total_updates(
    request: Request, body: ProjectCompletionPreparationRequest
) -> None:
    run_completion_update_for_projects_in_shared_program.delay(
        project_ids=body.project_ids,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@router.get(
    path="/prepare-completion-totals",
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[
        Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))
    ],  # this is called by the migration infrastructure
)
async def invoke_completion_totals(request: Request) -> None:
    run_completion_preparation_as_necessary.delay(
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@router.get(
    path=project_completion_for_programs_path,
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[
        Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))
    ],  # this is called by the migration infrastructure
)
async def assure_project_completion_updated(request: Request, program_id: int) -> None:
    await attempt_updating_project_completion_for_program(request, program_id)
