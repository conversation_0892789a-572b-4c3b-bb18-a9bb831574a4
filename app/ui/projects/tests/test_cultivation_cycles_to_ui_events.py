from cultivation_cycles.methods import get_cultivation_cycles
from cultivation_cycles.schema import CultivationCycleGenerationConfig
from phases.enums import PhaseTypes, StageTypes
from ui.projects.methods import mrv_values_cultivation_cycles_to_ui_events


def test_cultivation_cycles_to_ui_events(events):
    cult_cycles, _ = get_cultivation_cycles(
        events=events, generation_config=CultivationCycleGenerationConfig(should_split_irrigation_events=True)
    )

    stage_id = 42
    field_id = 1234
    field_id_to_events = {field_id: events}

    ui_cult_cycles = mrv_values_cultivation_cycles_to_ui_events(
        field_id_to_events, StageTypes.HISTORICAL_CROP_ROTATION, PhaseTypes.MONITORING, stage_id
    )
    assert f"{cult_cycles[0].id}_{stage_id}" == ui_cult_cycles[1234][0].id
