from datetime import datetime, timezone
from unittest.mock import patch

import pytest
from sentry_sdk.integrations import httpx
from ses_client.client import Client as SesClient

from boundaries_service import client as boundaries_client
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ses_integration.tests.mocks import (
    get_mock_crop_sequences,
    get_mock_field_practice_events_with_fallow_period,
)
from ui.projects.completion.annotations import (
    CompletionForFieldsResponse,
    CompletionForFieldsValidation,
)
from ui.projects.field_events.tests.mocks import get_mock_geom

field_md5 = "field-md5"


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
@pytest.mark.asyncio
async def test_get_binary_completion_of_project_fields_for_stage(async_client: httpx.AsyncClient, mdl):

    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)

    url = f"/ui/projects/{project.id}/stages/{stage.id}/fields/completion?field_id={field.id}"
    response = await async_client.get(url)
    assert response.status_code == 200
    data: CompletionForFieldsResponse = response.json()
    assert str(field.id) in data

    validations: CompletionForFieldsValidation = data[str(field.id)]

    assert validations.get("valid") is False
    assert validations.get("validation_errors") is None
