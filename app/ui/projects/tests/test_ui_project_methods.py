from fields.enums import FieldStatus
from permissions.enums import Permission, RoleTypes
from ui.projects.methods import can_bypass_event_lock, get_field_with_parent_project


async def test_get_field_with_parent_project(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    result = await get_field_with_parent_project(request=app_request, project_id=project.id, field_id=field.id)

    assert result.id == field.id


async def test_get_field_without_parent_project(app_request, mdl):
    program = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program.id)
    project_2 = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project_1.id, deleted_at=None, status=FieldStatus.enrolled)
    result = await get_field_with_parent_project(request=app_request, project_id=project_2.id, field_id=field.id)

    assert result is None


async def test_can_bypass_event_lock(app_request, faker, mdl):
    program = await mdl.Programs()

    # Create a user with the bypass permission
    user_with_permission_id = faker.unique.random_number(3)
    admin_role = await mdl.Roles(name="admin_with_bypass", role_type=RoleTypes.ADMIN)
    await mdl.RolesUsers(role_id=admin_role.id, fs_user_id=user_with_permission_id, program_id=program.id)
    await mdl.RolesPermissions(role_id=admin_role.id, permission=Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK)

    # Create a user without the bypass permission
    user_without_permission_id = faker.unique.random_number(3)
    regular_role = await mdl.Roles(name="admin_without_bypass", role_type=RoleTypes.ADMIN)
    await mdl.RolesUsers(role_id=regular_role.id, fs_user_id=user_without_permission_id, program_id=program.id)

    # Test user with permission
    app_request.state.fs_impersonator_user_id = user_with_permission_id
    result = await can_bypass_event_lock(request=app_request, program_id=program.id)
    assert result is True

    # Test user without permission
    app_request.state.fs_impersonator_user_id = user_without_permission_id
    result = await can_bypass_event_lock(request=app_request, program_id=program.id)
    assert result is False
