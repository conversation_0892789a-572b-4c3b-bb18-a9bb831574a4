from fastapi import Request

from logger import get_logger
from phases.db import get_stage_by_id, get_stages_from_program_and_phase_types
from phases.model import Phases, Stage
from projects.db import get_completion_percentage_from_project_stage_summary
from projects.tasks import run_stage_completion
from ui.projects.completion.annotations import (
    CompletionForFieldsResponse,
    CompletionForFieldsValidation,
    StagePercentageCompletionForPhaseResponse,
)
from ui.projects.completion.calculation.calculate_project_completion_percentages import (
    calculate_project_stage_completion,
)
from ui.projects.completion.constants import SUPPORTED_FIELD_EVENT_STAGE_TYPES
from ui.projects.completion.dataclasses import CalculationContext
from ui.projects.completion.db import (
    get_per_field_stage_completions_for_project,
    get_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.handle_field_completion_change import (
    handle_updating_stage_completions_for_field,
)

logger = get_logger(__name__)


# interface
async def obtain_stage_completion_for_fields(
    request: Request, project_id: int, stage_id: int, field_id: int | None = None
) -> CompletionForFieldsResponse:
    stage = await get_stage_by_id(request, stage_id)
    if stage.type_ not in SUPPORTED_FIELD_EVENT_STAGE_TYPES:
        # we likely will want to handle more
        raise ValueError(f"Unhandled stage type: {stage.type_}")
    if field_id is None:
        return await _obtain_stage_completion_for_project_fields(request, project_id, stage)
    else:
        return await _obtain_stage_completion_for_one_project_field(request, field_id, stage)


async def obtain_stage_completion_percentages_for_phase(
    request: Request, project_id: int, phase: Phases, program_id: int
) -> StagePercentageCompletionForPhaseResponse:
    """
    Do not call this from tasks
    """
    stages = await get_stages_from_program_and_phase_types(request, program_id, [phase.type_])
    result: StagePercentageCompletionForPhaseResponse = {}
    for stage in stages:
        if stage.type_ in SUPPORTED_FIELD_EVENT_STAGE_TYPES:
            completion_percentage = await _determine_completion_percentage_for_stage(request, project_id, stage)
        else:
            # use the existing value and recalculate async for a quicker return
            completion_percentage = await get_completion_percentage_from_project_stage_summary(
                request, project_id, stage.id
            )
            if completion_percentage is None:
                completion_percentage = 0

            run_stage_completion.delay(
                project_id=project_id,
                stage_id=stage.id,
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            )
        result[stage.id] = completion_percentage
    return result


# internals
async def _determine_completion_percentage_for_stage(request: Request, project_id: int, stage: Stage) -> int:
    # with update should be safe since this is directly from the UI only and not part of recalculation
    psc_record = await calculate_project_stage_completion(
        request,
        stage,
        project_id,
        CalculationContext(should_update_field_level=True, is_inside_task=False, should_evaluate_field_level=True),
    )
    return psc_record.percentage_complete


async def _obtain_stage_completion_for_project_fields(
    request: Request, project_id: int, stage: Stage
) -> CompletionForFieldsResponse:
    per_field_completions = await get_per_field_stage_completions_for_project(request, stage.id, project_id)
    completion_results: CompletionForFieldsResponse = {}
    for fc in per_field_completions:
        completion_result = await _resolve_field_completion_to_bool(
            request, stage, fc.field_id, fc.completion_result.completion_status
        )
        completion_results[fc.field_id] = CompletionForFieldsValidation(
            valid=completion_result,
            validation_errors=fc.completion_result.validation_errors,
        )

    return completion_results


async def _obtain_stage_completion_for_one_project_field(
    request: Request, field_id: int, stage: Stage
) -> CompletionForFieldsResponse:
    completion_result = await get_stage_completion_for_field(request, stage.id, field_id)
    result = await _resolve_field_completion_to_bool(request, stage, field_id, completion_result.completion_status)
    return {
        field_id: CompletionForFieldsValidation(valid=result, validation_errors=completion_result.validation_errors)
    }


async def _resolve_field_completion_to_bool(
    request: Request,
    stage: Stage,
    field_id: int,
    completion_status: NarrowestCompletionStatus,
) -> bool:
    match completion_status:
        case NarrowestCompletionStatus.complete:
            return True
        case NarrowestCompletionStatus.incomplete:
            return False
        case NarrowestCompletionStatus.undetermined:
            # we think this is safe since it's only coming in
            # from UI requests, but be careful about where this
            # is called so as not to create a task loop
            stage_completions = await handle_updating_stage_completions_for_field(
                request, stage.phase_id, field_id, None
            )
            if stage.id in stage_completions:
                completion_result = stage_completions[stage.id]
                completion_status = completion_result.completion_status
            else:
                completion_status = NarrowestCompletionStatus.undetermined
            if completion_status == NarrowestCompletionStatus.undetermined:
                logger.error(
                    "failed to resolve completion status of field for stage",
                    extra={"field_id": field_id, "stage.id": stage.id},
                )
            return completion_status == NarrowestCompletionStatus.complete
        case _:
            raise ValueError(f"Unexpected completion status: {completion_status}")
