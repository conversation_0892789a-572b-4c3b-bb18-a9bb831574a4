from fastapi import HTT<PERSON><PERSON>xception, Request, status

from logger import get_logger
from phases.model import Stage
from projects.schema import ProjectStageCompletion
from ui.projects.completion.constants import MAX_FIELDS_UPDATED_INLINE
from ui.projects.completion.dataclasses import CalculationContext, StageCompletionResult
from ui.projects.completion.db import (
    get_per_field_stage_completions_for_project,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.handle_field_completion_change import (
    determine_stage_completions_for_field_with_ids,
    handle_updating_stage_completions_for_field,
)

logger = get_logger(__name__)


# internals
async def calculate_project_stage_completion(
    request: Request, stage: Stage, project_id: int, calculation_context: CalculationContext
) -> ProjectStageCompletion:
    per_field_stage_completions = await get_per_field_stage_completions_for_project(request, stage.id, project_id)
    total = 0
    completed = 0
    missing_updates = 0
    started_async_update = False
    for fc in per_field_stage_completions:
        completion_result = fc.completion_result
        # logger.info(
        #    f"field: {fc.field_id}, completion_status: {completion_result.completion_status}, total cycles: {completion_result.total_cycle_count}, completed cycles: {completion_result.completed_cycle_count}"
        # )
        # we need to recalculate
        if (
            completion_result.completion_status == NarrowestCompletionStatus.undetermined
            or completion_result.total_cycle_count is None
        ):
            stage_completions = {}
            if calculation_context.should_update_field_level:
                over_max_updates = missing_updates > MAX_FIELDS_UPDATED_INLINE
                if over_max_updates:
                    if not calculation_context.is_inside_task and (not started_async_update):
                        # break circularity by bringing task in here
                        from ui.projects.completion.tasks import (
                            run_field_completion_change_handler,
                        )

                        run_field_completion_change_handler.delay(
                            phase_id=stage.phase_id,
                            field_id=fc.field_id,
                            stage_completion_identifier_lookup={},
                            fs_user_id=request.state.fs_user_id,
                            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
                        )
                        started_async_update = True

                else:
                    stage_completions = await handle_updating_stage_completions_for_field(
                        request, stage.phase_id, fc.field_id, {}
                    )
            elif calculation_context.should_evaluate_field_level:
                stage_completions = await determine_stage_completions_for_field_with_ids(
                    request, stage.phase_id, fc.field_id
                )
            if stage.id in stage_completions:
                completion_result = stage_completions[stage.id]
            else:
                logger.warning("Failed to update completions for stage", extra={"stage_id": {stage.id}})
                completion_result = StageCompletionResult(
                    completion_status=NarrowestCompletionStatus.undetermined,
                    total_cycle_count=5,  # arbitrary but large enough to be pretty realistic
                    completed_cycle_count=0,
                )
            missing_updates = missing_updates + 1
        total = total + completion_result.total_cycle_count
        completed = completed + completion_result.completed_cycle_count

    over_max_updates = missing_updates > MAX_FIELDS_UPDATED_INLINE
    if over_max_updates and not calculation_context.is_inside_task:
        raise HTTPException(
            status_code=status.HTTP_202_ACCEPTED, detail={"message": "Field completion status is being updated"}
        )

    if total > 0:
        percentage_complete = int(round((100.0 * completed) / total))
    else:
        percentage_complete = 0

    # logger.info(f"calculated {percentage_complete}% complete for project {project_id} and stage {stage.id}")

    return ProjectStageCompletion(
        completed=completed,
        to_complete=total - completed,
        total=total,
        percentage_complete=percentage_complete,
        is_completed=(total == completed),
    )
