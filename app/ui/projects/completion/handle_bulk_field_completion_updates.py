from datetime import datetime

from starlette.requests import Request

from fields.db import get_fields_by_program
from fields.model import Fields
from logger import get_logger
from phases.db import (
    get_phase_from_project_id,
    get_stages_from_program_and_phase_types,
)
from phases.model import Phases
from programs.db import get_event_based_programs
from projects.db import get_field_ids_for_project_id, get_projects_by_program_id
from ui.projects.completion.constants import (
    MAX_FIELDS_UPDATED_CRON,
    SUPPORTED_FIELD_EVENT_STAGE_TYPES,
    SUPPORTED_PHASE_TYPES,
)
from ui.projects.completion.dataclasses import CalculationContext
from ui.projects.completion.db import get_field_ids_with_any_stage_completions
from ui.projects.completion.handle_field_completion_change import (
    handle_updating_stage_completions_for_field_skipping_project,
)
from ui.projects.completion.handle_field_completion_invalidation import (
    handle_field_phase_invalidation,
)
from ui.projects.completion.handle_project_stage_updates import (
    attempt_updating_stage_completions_for_project,
    handle_updating_stage_completions_for_project,
)
from ui.projects.completion.phase_stage_helpers import (
    get_candidate_phases_for_program,
    get_relevant_stages_for_phase,
)

logger = get_logger(__name__)


async def update_completion_for_projects_in_shared_program(request: Request, project_ids: list[int]) -> None:
    """
    handle updating completions for all fields in the given projects for all applicable phases

    Note: this function assumes the project_ids all pertain to the same program
    """
    first_project_id = project_ids[0]

    relevant_phases = []

    for phase_type in SUPPORTED_PHASE_TYPES:
        phase = await get_phase_from_project_id(request, first_project_id, phase_type)
        if phase is not None:
            relevant_phases.append(phase)

    for project_id in project_ids:
        try:
            logger.info(f"updating completion for fields in project {project_id}")
            field_ids = await get_field_ids_for_project_id(request, project_id)
            for field_id in field_ids:
                for phase in relevant_phases:
                    await handle_updating_stage_completions_for_field_skipping_project(request, phase.id, field_id, {})

            logger.info(f"updating project-level stage percentages {project_id}")
            calculation_context = CalculationContext(
                should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=False
            )
            for phase in relevant_phases:
                stages = await get_stages_from_program_and_phase_types(request, phase.program_id, [phase.type_])
                for stage in stages:
                    if stage.type_ in SUPPORTED_FIELD_EVENT_STAGE_TYPES:
                        await handle_updating_stage_completions_for_project(
                            request, stage, project_id, calculation_context
                        )
            logger.info(f"finished project-level completion update for {project_id}")
        except ValueError as e:
            logger.warn(f"found exception while updating completion for {project_id}: {e}")
    logger.info("finished updating completion in bulk update")


async def prepare_completion_as_necessary(request: Request) -> None:
    programs = await get_event_based_programs(request)
    project_calculation_context = CalculationContext(
        should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=False
    )
    for program in programs:
        candidate_phases = await get_candidate_phases_for_program(request, program.id)
        relevant_phases = []
        relevant_stages = []
        for phase in candidate_phases:
            if phase.end_date <= datetime.now():
                continue
            relevant_phases.append(phase)
            relevant_stages_for_phase = await get_relevant_stages_for_phase(request, phase)
            for stage in relevant_stages_for_phase:
                relevant_stages.append(stage)
        if not relevant_phases:
            continue

        fields = await get_fields_by_program(request, program.id)
        fields = await _filter_to_fields_needing_completion(request, fields)
        field_groups = _bundle_fields_by_project(fields)
        update_count = 0
        for f_group in field_groups:
            for field in f_group:
                logger.info(f"updating completion for fields  {field.id}")
                try:
                    for phase in relevant_phases:
                        await _handle_field_phase_update(request, phase, field.id)
                except Exception as e:
                    logger.warn(f"issue while evaluating completion for {field.id}: {e}, continuing")

                update_count = update_count + 1
            proj_id = f_group[0].parent_project_id
            for stage in relevant_stages:
                try:
                    await attempt_updating_stage_completions_for_project(
                        request, stage, proj_id, project_calculation_context
                    )
                except Exception as e:
                    logger.warn(
                        f"Found an issue while attempting to update project {proj_id}/stage {stage.id} completion: {e}"
                    )
            if update_count > MAX_FIELDS_UPDATED_CRON:
                break


async def handle_multiple_fields_changing_completion(request: Request, project_id: int, field_ids: list[int]) -> None:
    project_calculation_context = CalculationContext(
        should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=False
    )

    supported_phases = []
    for supported_pt in SUPPORTED_PHASE_TYPES:
        candidate_phase = await get_phase_from_project_id(request, project_id, supported_pt)
        if candidate_phase:
            supported_phases.append(candidate_phase)

    for field_id in field_ids:
        logger.info(f"updating completion for field {field_id} in project {project_id}")
        for phase in supported_phases:
            try:
                await _handle_field_phase_update(request, phase, field_id)
            except Exception as e:
                logger.warn(f"issue while evaluating completion for {field_id}: {e}, continuing")

    logger.info(f"updating project-level stage percentages {project_id}")
    for phase in supported_phases:
        relevant_stages = await get_relevant_stages_for_phase(request, phase)
        for stage in relevant_stages:
            try:
                await attempt_updating_stage_completions_for_project(
                    request, stage, project_id, project_calculation_context
                )
            except Exception as e:
                logger.warn(
                    f"Found an issue while attempting to update project {project_id}/stage {stage.id} completion: {e}"
                )
    logger.info(f"done with multi-field completion change for {project_id}")


async def attempt_updating_project_completion_for_program(request: Request, program_id: int) -> dict[int, bool]:
    projects = await get_projects_by_program_id(request=request, program_id=program_id)

    calculation_context = CalculationContext(
        should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=False
    )

    relevant_phases = await get_candidate_phases_for_program(request, program_id)
    relevant_stages = []
    for phase in relevant_phases:
        stages = await get_stages_from_program_and_phase_types(request, phase.program_id, [phase.type_])
        for stage in stages:
            if stage.type_ in SUPPORTED_FIELD_EVENT_STAGE_TYPES:
                relevant_stages.append(stage)

    project_updates: dict[int, bool] = {}
    total_count = 0
    update_count = 0
    issue_count = 0
    for proj in projects:
        if proj.deleted_at is not None:
            continue
        total_count = total_count + 1
        did_any_project_update = False
        for stage in relevant_stages:
            try:
                did_update = await attempt_updating_stage_completions_for_project(
                    request, stage, proj.id, calculation_context
                )
                logger.info(
                    f"Did we update project completion for project {proj.id} and stage {stage.id}: {did_update}"
                )
                if did_update:
                    did_any_project_update = True
            except Exception as e:
                logger.warn(
                    f"Found an issue while attempting to update project {proj.id}/stage {stage.id} completion for program {program_id}: {e}"
                )
                issue_count = issue_count + 1
        project_updates[proj.id] = did_any_project_update
        if did_any_project_update:
            update_count = update_count + 1
    logger.info(f"total projects: {total_count}, updates: {update_count}, issues: {issue_count}")
    return project_updates


# internals
async def _filter_to_fields_needing_completion(request: Request, fields: list[Fields]) -> list[Fields]:
    f_ids_with_comp = set(await get_field_ids_with_any_stage_completions(request, [f.id for f in fields]))
    return [f for f in fields if f.id not in f_ids_with_comp]


def _bundle_fields_by_project(fields: list[Fields]) -> list[list[Fields]]:
    parent_lookup: dict[int, list[int]] = {}
    for field in fields:
        f_group = parent_lookup.get(field.parent_project_id, [])
        f_group.append(field)
        parent_lookup[field.parent_project_id] = f_group
    field_bundles = list(parent_lookup.values())
    # return largest projects groups first
    field_bundles.sort(key=lambda x: -len(x))
    return field_bundles


async def _handle_field_phase_update(request: Request, phase: Phases, field_id: int) -> None:
    await handle_field_phase_invalidation(request, phase.id, field_id)
    await handle_updating_stage_completions_for_field_skipping_project(request, phase.id, field_id, {})
