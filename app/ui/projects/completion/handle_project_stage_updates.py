from fastapi import Request

from logger import get_logger
from phases.db import get_stage_by_id
from phases.model import Stage
from projects.db import (
    get_completion_percentage_from_project_stage_summary,
    get_project_stage_completion_from_project_stage_summary,
)
from projects.schema import ProjectStageCompletion
from ui.projects.completion.calculation.calculate_project_completion_percentages import (
    calculate_project_stage_completion,
)
from ui.projects.completion.dataclasses import CalculationContext
from ui.projects.completion.db import (
    update_project_stage_completion,
)
from ui.projects.completion.phase_stage_helpers import (
    get_relevant_phases_for_project_id,
    get_relevant_stages_for_phase,
)

logger = get_logger(__name__)


async def obtain_stage_completion_for_project(
    request: Request, stage_id: int, project_id: int
) -> ProjectStageCompletion:
    """
    This will objtain the project stage completion, triggering an update if necessary;
    this should not be called from a task
    """
    stage = await get_stage_by_id(request, stage_id)
    existing_completion_result = await get_project_stage_completion_from_project_stage_summary(
        request, stage.id, project_id
    )
    if existing_completion_result is None:
        calculation_context = CalculationContext(
            should_update_field_level=True, is_inside_task=False, should_evaluate_field_level=True
        )
        return await handle_updating_stage_completions_for_project(request, stage, project_id, calculation_context)
    return existing_completion_result


# read inside the ui.projects.completion universe but not outside
async def handle_updating_stage_completions_for_project(
    request: Request, stage: Stage, project_id: int, calculation_context: CalculationContext
) -> ProjectStageCompletion:
    """
    Here we know a change has been made, so we want to determine if the completion has changed
    and update the stage completions for the project if so
    """
    current_completion_record, _ = await _undertake_updating_stage_completions_for_project(
        request, stage, project_id, calculation_context
    )
    return current_completion_record


async def attempt_updating_all_relevant_stage_completions_for_project(request: Request, project_id: int) -> None:
    """
    Here, we're interested in updating all of the relevant stage completions;
    this method could return a dictionary from stages to bool
    (whether or not the update was made) but we don't need it yet
    """
    project_calculation_context = CalculationContext(
        should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=False
    )
    relevant_phases = await get_relevant_phases_for_project_id(request, project_id)
    for phase in relevant_phases:
        stages = await get_relevant_stages_for_phase(request, phase)
        for stage in stages:
            await attempt_updating_stage_completions_for_project(
                request, stage, project_id, project_calculation_context
            )


async def attempt_updating_stage_completions_for_project(
    request: Request, stage: Stage, project_id: int, calculation_context: CalculationContext
) -> bool:
    """
    Here we're concerned whether field-level and project-level completions are matching,
    so we recalculate and check, and return whether or not there has been an update
    """
    _, did_update = await _undertake_updating_stage_completions_for_project(
        request, stage, project_id, calculation_context
    )
    return did_update


# Internals
async def _undertake_updating_stage_completions_for_project(
    request: Request, stage: Stage, project_id: int, calculation_context: CalculationContext
) -> tuple[ProjectStageCompletion, bool]:
    existing_completion_percentage = await get_completion_percentage_from_project_stage_summary(
        request, project_id, stage.id
    )
    current_completion_record = await calculate_project_stage_completion(
        request, stage, project_id, calculation_context
    )
    did_update = False
    if existing_completion_percentage is None:
        await update_project_stage_completion(request, stage.id, project_id, current_completion_record)
        did_update = True
    elif existing_completion_percentage != current_completion_record.percentage_complete:
        await update_project_stage_completion(request, stage.id, project_id, current_completion_record)
        did_update = True
    # otherwise, we're up to date, and don't need to update the completion record
    return current_completion_record, did_update
