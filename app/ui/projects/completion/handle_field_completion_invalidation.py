from starlette.requests import Request

from projects.db import get_field_ids_by_project_ids
from ui.projects.completion.annotations import StageCompletionIdentifierLookup
from ui.projects.completion.dataclasses import StageCompletionResult
from ui.projects.completion.db import (
    bulk_update_stage_completion_for_fields,
    get_stage_completion_id_lookup,
    store_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus

# interface, used outside of completion package


async def bulk_handle_project_invalidation(request: Request, project_ids: list[int]) -> None:
    field_ids = await get_field_ids_by_project_ids(request=request, project_ids=project_ids)
    await bulk_update_stage_completion_for_fields(
        request=request,
        field_ids=field_ids,
        field_completion_status=NarrowestCompletionStatus.undetermined,
        completed_cycles=None,
        total_cycles=None,
    )


async def handle_field_phase_invalidation(
    request: Request, phase_id: int, field_id: int
) -> StageCompletionIdentifierLookup:
    await _invalidate_completion_status_for_field(request, phase_id, field_id)
    return await get_stage_completion_id_lookup(request, phase_id, field_id)


# internals to this module


async def _invalidate_completion_status_for_field(request: Request, phase_id: int, field_id: int) -> None:
    """
    Get the existing stages for completion and instead store entries indicating the
    result is undetermined
    """
    existing_stage_ids_lookup = await get_stage_completion_id_lookup(request, phase_id, field_id)
    invalidation_coroutines = [
        store_stage_completion_for_field(
            request,
            stage_id,
            field_id,
            StageCompletionResult(
                completion_status=NarrowestCompletionStatus.undetermined,
                completed_cycle_count=None,
                total_cycle_count=None,
            ),
        )
        for stage_id in existing_stage_ids_lookup.keys()
    ]
    for ic in invalidation_coroutines:
        await ic
