from typing import Tuple

from ui.projects.completion.dataclasses import (
    CompletionForFieldsValidation,
    ValidationResults,
)

FieldId = int
CompletionForFieldsResponse = dict[FieldId, CompletionForFieldsValidation]


StageId = int
StagePercentageCompletionForPhaseResponse = dict[StageId, int]

StageCompletionIdentifierLookup = dict[StageId, int]

FieldCompletionCountResponseWithErrors = Tuple[int, list[ValidationResults]]
