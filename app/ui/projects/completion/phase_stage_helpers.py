from starlette.requests import Request

from phases.db import (
    get_phase_from_program_id,
    get_phase_from_project_id,
    get_stages_from_program_and_phase_types,
)
from phases.model import Phases, Stage
from ui.projects.completion.constants import (
    SUPPORTED_FIELD_EVENT_STAGE_TYPES,
    SUPPORTED_PHASE_TYPES,
)

# these functions are used internally to the completions module


async def get_relevant_phases_for_project_id(request: Request, project_id: int) -> list[Phases]:
    relevant_phases = []
    for phase_type in SUPPORTED_PHASE_TYPES:
        phase = await get_phase_from_project_id(request, project_id, phase_type)
        if phase is None:
            continue
        if phase.deleted_at is not None:
            continue
        if phase.enabled is False:
            continue
        relevant_phases.append(phase)
    return relevant_phases


async def get_candidate_phases_for_program(request: Request, program_id: int) -> list[Phases]:
    candidate_phases: list[Phases] = []
    for phase_type in SUPPORTED_PHASE_TYPES:
        phase = await get_phase_from_program_id(request, program_id, phase_type)
        if phase is None:
            continue
        if phase.deleted_at is not None:
            continue
        if phase.enabled is False:
            continue
        candidate_phases.append(phase)
    return candidate_phases


async def get_relevant_stages_for_phase(request: Request, phase: Phases) -> list[Stage]:
    relevant_stages = []
    stages = await get_stages_from_program_and_phase_types(request, phase.program_id, [phase.type_])
    for stage in stages:
        if stage.type_ in SUPPORTED_FIELD_EVENT_STAGE_TYPES:
            relevant_stages.append(stage)
    return relevant_stages
