from sqlalchemy import delete, or_, select, update
from sqlalchemy.dialects.mysql import insert
from starlette.requests import Request

from fields.enums import FieldStatus
from fields.model import Fields
from helper.helper import run_query
from logger import get_logger
from phases.model import Stage
from projects.db import upsert_project_completion_summary
from projects.model import ProjectCompletion
from projects.schema import ProjectStageCompletion
from root_crud import create
from ui.projects.completion.annotations import StageCompletionIdentifierLookup
from ui.projects.completion.dataclasses import (
    StageCompletionResult,
    StageFieldCompletion,
    to_validation_results,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.model import StageFieldCompletions

logger = get_logger(__name__)

# Interface:  this function is used outside the completion package

# Package internal functions


async def get_stage_completion_id_lookup(
    request: Request, phase_id: int, field_id: int
) -> StageCompletionIdentifierLookup:
    stage_completion_id_query = (
        select(StageFieldCompletions.stage_id, StageFieldCompletions.id)
        .join(Stage, StageFieldCompletions.stage_id == Stage.id)
        .where(Stage.phase_id == phase_id, StageFieldCompletions.field_id == field_id)
    )
    async with request.state.sql_session.begin() as s:
        query_results = (await run_query(query=stage_completion_id_query, s=s)).all()
    return {qr[0]: qr[1] for qr in query_results}


async def get_field_ids_with_any_stage_completions(request: Request, field_ids: list[int]) -> list[int]:
    field_id_query = (
        select(StageFieldCompletions.field_id).where(StageFieldCompletions.field_id.in_(field_ids)).distinct()
    )
    async with request.state.sql_session.begin() as s:
        query_results = (await run_query(query=field_id_query, s=s)).scalars()
    return query_results


async def store_stage_completion_for_field(
    request: Request, stage_id: int, field_id: int, completion_result: StageCompletionResult
) -> None:
    """
    store the stage completion, deleting the previous

    Internals note:  external behavior is expecting the delete before the insert,
    which also updates the id associated with the record
    """
    delete_stmt = delete(StageFieldCompletions).where(
        StageFieldCompletions.stage_id == stage_id, StageFieldCompletions.field_id == field_id
    )

    insert_stmt = (
        insert(StageFieldCompletions)
        .values(
            stage_id=stage_id,
            field_id=field_id,
            field_completion_status=completion_result.completion_status,
            completed_cycles=completion_result.completed_cycle_count,
            total_cycles=completion_result.total_cycle_count,
            validation_errors=completion_result.validation_errors or [],
        )
        .on_duplicate_key_update(
            stage_id=stage_id,
            field_id=field_id,
            field_completion_status=completion_result.completion_status,
            completed_cycles=completion_result.completed_cycle_count,
            total_cycles=completion_result.total_cycle_count,
            validation_errors=completion_result.validation_errors or [],
        )
    )

    async with request.state.sql_session.begin() as s:
        await s.execute(delete_stmt)
    async with request.state.sql_session.begin() as s:
        await s.execute(insert_stmt)


async def bulk_update_stage_completion_for_fields(
    request: Request,
    field_ids: list[int],
    field_completion_status: NarrowestCompletionStatus,
    completed_cycles: int | None,
    total_cycles: int | None,
) -> None:
    update_stmt = (
        update(StageFieldCompletions)
        .where(StageFieldCompletions.field_id.in_(field_ids))
        .values(
            field_completion_status=field_completion_status,
            completed_cycles=completed_cycles,
            total_cycles=total_cycles,
        )
    )
    async with request.state.sql_session.begin() as s:
        await s.execute(update_stmt)


async def get_per_field_stage_completions_for_project(
    request: Request, stage_id: int, project_id: int
) -> list[StageFieldCompletion]:
    """
    Obtain all the completions for the stage for the complete-able fields in the project;
    if there are no stage completions for a field, it is NarrowestCompletionStatus.undetermined
    """
    fields_query = (
        select(Fields.id)
        .where(Fields.parent_project_id == project_id)
        .where(Fields.deleted_at.is_(None))
        .where(
            or_(
                Fields.status.in_([FieldStatus.enrolled, FieldStatus.registered, FieldStatus.contract_voided]),
                Fields.status.is_(None),
            )
        )
    )

    async with request.state.sql_session.begin() as s:
        fields = list((await run_query(query=fields_query, s=s)).scalars())
        if len(fields) == 0:
            return []
        completions_query = (
            select(
                StageFieldCompletions.field_id,
                StageFieldCompletions.field_completion_status,
                StageFieldCompletions.completed_cycles,
                StageFieldCompletions.total_cycles,
                StageFieldCompletions.validation_errors,
            )
            .where(StageFieldCompletions.stage_id == stage_id)
            .where(StageFieldCompletions.field_id.in_(fields))
        )
        completions_query_results = (await run_query(query=completions_query, s=s)).all()

        completions_result_lookup = {}
        for qr in completions_query_results:
            if qr[4] is None:
                ve_list = []
            else:
                ve_list = [to_validation_results(ve) for ve in qr[4]]

            completions_result_lookup[qr[0]] = StageCompletionResult(
                completion_status=qr[1],
                completed_cycle_count=qr[2],
                total_cycle_count=qr[3],
                validation_errors=ve_list,
            )

    return [
        StageFieldCompletion(
            stage_id=stage_id,
            field_id=fid,
            completion_result=completions_result_lookup.get(
                fid,
                StageCompletionResult(
                    completion_status=NarrowestCompletionStatus.undetermined,
                    completed_cycle_count=None,
                    total_cycle_count=None,
                    validation_errors=[],
                ),
            ),
        )
        for fid in fields
    ]


async def get_stage_completion_for_field(request: Request, stage_id: int, field_id: int) -> StageCompletionResult:
    field_completions_query = (
        select(
            StageFieldCompletions.field_completion_status,
            StageFieldCompletions.completed_cycles,
            StageFieldCompletions.total_cycles,
            StageFieldCompletions.validation_errors,
        )
        .where(StageFieldCompletions.field_id == field_id)
        .where(StageFieldCompletions.stage_id == stage_id)
    )
    async with request.state.sql_session.begin() as s:
        query_results = (await run_query(query=field_completions_query, s=s)).all()
    if len(query_results) > 0:
        if query_results[0][3]:
            validation_errors = [to_validation_results(ve) for ve in query_results[0][3]]
        else:
            validation_errors = []
        stage_completion_status = StageCompletionResult(
            completion_status=query_results[0][0],
            completed_cycle_count=query_results[0][1],
            total_cycle_count=query_results[0][2],
            validation_errors=validation_errors,
        )
        # if we don't find a count, the results were from before we
        # were computing counts and needs reevaluation
        if stage_completion_status.completed_cycle_count is None:
            stage_completion_status.completion_status = NarrowestCompletionStatus.undetermined
        return stage_completion_status
    else:
        return StageCompletionResult(
            completion_status=NarrowestCompletionStatus.undetermined,
            completed_cycle_count=None,
            total_cycle_count=None,
            validation_errors=None,
        )


async def update_project_stage_completion(
    request: Request, stage_id: int, project_id: int, project_stage_completion: ProjectStageCompletion
) -> None:
    # insert a completion so we have the completion change history
    completion = (
        await create.create(
            request=request,
            instances=[
                ProjectCompletion(
                    project_id=project_id,
                    stage_id=stage_id,
                    percentage_complete=project_stage_completion.percentage_complete,
                )
            ],
            orm_type=ProjectCompletion,
            translate=False,
            no_return=False,
        )
    )[0]
    upsert_update_date = completion["created_at"]
    # upsert a summary so the record is up-to-date
    await upsert_project_completion_summary(
        request,
        project_id,
        stage_id,
        project_stage_completion.percentage_complete,
        project_stage_completion.to_complete,
        project_stage_completion.completed,
        upsert_update_date,
    )
