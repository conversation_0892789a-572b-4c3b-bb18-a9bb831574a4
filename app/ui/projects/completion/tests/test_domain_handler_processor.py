from unittest.mock import ANY

from phases.enums import PhaseTypes
from ui.projects.completion.domain_handler_processor import (
    process_field_completion_domain_event,
)


async def test_process_field_completion_domain_event(mdl, mocker, app_request):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    # dashboards shouldn't be processed by completion
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.DASHBOARDS, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=1)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None)
    mock_update_stage_completions_for_field = mocker.patch(
        "ui.projects.completion.domain_handler_processor.completion_tasks.run_field_completion_change_handler"
    )
    await process_field_completion_domain_event(app_request, field.id)
    mock_update_stage_completions_for_field.delay.assert_called_once_with(
        fs_impersonator_user_id=ANY,
        fs_user_id=ANY,
        phase_id=phase.id,
        field_id=field.id,
        stage_completion_identifier_lookup=ANY,
    )
