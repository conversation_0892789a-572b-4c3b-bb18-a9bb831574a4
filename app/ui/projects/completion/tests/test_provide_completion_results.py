from datetime import datetime, timezone
from math import isclose
from unittest.mock import patch

import pytest
from fastapi import HTTPException
from ses_client.client import Client as SesClient

from boundaries_service import client as boundaries_client
from fields.enums import FieldStatus
from logger import get_logger
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ses_integration.tests.mocks import (
    get_mock_crop_sequences,
    get_mock_field_practice_events_with_fallow_period,
)
from ui.projects.completion.constants import MAX_FIELDS_UPDATED_INLINE
from ui.projects.completion.dataclasses import StageCompletionResult
from ui.projects.completion.db import store_stage_completion_for_field
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.provide_completion_results import (
    obtain_stage_completion_for_fields,
    obtain_stage_completion_percentages_for_phase,
)
from ui.projects.field_events.tests.mocks import get_mock_geom

logger = get_logger(__name__)

field_md5 = "field-md5"

field_2_md5 = "field-md5-2"


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_obtain_stage_completion_for_fields_with_single_field(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    initial_completion = await obtain_stage_completion_for_fields(app_request, project.id, stage.id, field.id)
    assert initial_completion[field.id].valid is False

    await store_stage_completion_for_field(
        app_request,
        stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=4, total_cycle_count=4
        ),
    )
    updated_completion = await obtain_stage_completion_for_fields(app_request, project.id, stage.id, field.id)
    assert updated_completion[field.id].valid is True


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_obtain_stage_completion_for_fields_with_multiple_fields(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(
        md5="not-" + field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    initial_completion = await obtain_stage_completion_for_fields(app_request, project.id, stage.id)

    assert initial_completion[field.id].valid is False
    assert initial_completion[field_2.id].valid is False

    await store_stage_completion_for_field(
        app_request,
        stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete,
            completed_cycle_count=4,
            total_cycle_count=4,
            validation_errors=None,
        ),
    )
    updated_completion = await obtain_stage_completion_for_fields(app_request, project.id, stage.id)
    assert updated_completion[field.id].valid is True
    assert updated_completion[field_2.id].valid is False


async def test_obtain_stage_completion_percentages_for_phase(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(
        md5=field_2_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    # we're doing all these to make sure we can run them and not fall over
    e_stage_types = [
        StageTypes.ASSIGN_PRACTICES,
        StageTypes.FIELD_BOUNDARIES,
        StageTypes.VIEW_OUTCOMES,
        StageTypes.CONTRACT,
        StageTypes.ELIGIBILITY,
        StageTypes.SURVEY,
        StageTypes.CROP_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
    ]
    stages_by_type = {}
    for e_stage_type in e_stage_types:
        stages_by_type[e_stage_type] = await mdl.Stage(
            type_=e_stage_type, phase_id=e_phase.id, deleted_at=None, enabled=True
        )

    # note, we have to set values for all of the SUPPORTED_FIELD_EVENT_STAGE_TYPES in the Stage
    # otherwise we will retrigger updating them all any time we get a NarrowestCompletionStatus.unspecified
    crop_events_stage = stages_by_type[StageTypes.CROP_EVENTS]
    await store_stage_completion_for_field(
        app_request,
        crop_events_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=4, total_cycle_count=4
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        crop_events_stage.id,
        field_2.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=4, total_cycle_count=4
        ),
    )

    nutrient_events_stage = stages_by_type[StageTypes.NUTRIENT_EVENTS]
    await store_stage_completion_for_field(
        app_request,
        nutrient_events_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=1, total_cycle_count=4
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        nutrient_events_stage.id,
        field_2.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=3, total_cycle_count=4
        ),
    )

    tillage_events_stage = stages_by_type[StageTypes.TILLAGE_EVENTS]
    await store_stage_completion_for_field(
        app_request,
        tillage_events_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=0, total_cycle_count=4
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        tillage_events_stage.id,
        field_2.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=2, total_cycle_count=4
        ),
    )

    irrgation_events_stage = stages_by_type[StageTypes.IRRIGATION_EVENTS]
    await store_stage_completion_for_field(
        app_request,
        irrgation_events_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=2, total_cycle_count=4
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        irrgation_events_stage.id,
        field_2.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=4, total_cycle_count=4
        ),
    )

    test_e_phase_percentages = await obtain_stage_completion_percentages_for_phase(
        app_request, project.id, e_phase, program.id
    )

    # this is to test we can run legacy stages and not fall-over, at least in this setting
    for e_stage_type in e_stage_types:
        assert stages_by_type[e_stage_type].id in test_e_phase_percentages
    # assert we're getting what we expect
    assert isclose(test_e_phase_percentages[crop_events_stage.id], 100.0)
    assert isclose(test_e_phase_percentages[nutrient_events_stage.id], 50.0)
    assert isclose(test_e_phase_percentages[tillage_events_stage.id], 25.0)
    assert isclose(test_e_phase_percentages[irrgation_events_stage.id], 75.0)


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_obtain_stage_completion_percentages_for_phase_from_nothing(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    # we're doing all these to make sure we can run them and not fall over
    e_stage_types = [
        StageTypes.CROP_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
    ]
    stages_by_type = {}
    for e_stage_type in e_stage_types:
        stages_by_type[e_stage_type] = await mdl.Stage(
            type_=e_stage_type, phase_id=e_phase.id, deleted_at=None, enabled=True
        )
    test_e_phase_percentages = await obtain_stage_completion_percentages_for_phase(
        app_request, project.id, e_phase, program.id
    )
    for e_stage_type in e_stage_types:
        assert stages_by_type[e_stage_type].id in test_e_phase_percentages


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_obtain_stage_completion_percentages_for_phase_with_too_many_non_updated_fields(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    too_many_fields = MAX_FIELDS_UPDATED_INLINE + 1
    for _ in range(too_many_fields):
        await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    # we're doing all these to make sure we can run them and not fall over
    e_stage_types = [
        StageTypes.CROP_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
    ]
    stages_by_type = {}
    for e_stage_type in e_stage_types:
        stages_by_type[e_stage_type] = await mdl.Stage(
            type_=e_stage_type, phase_id=e_phase.id, deleted_at=None, enabled=True
        )
    with pytest.raises(HTTPException):
        await obtain_stage_completion_percentages_for_phase(app_request, project.id, e_phase, program.id)


async def test_obtain_stage_completion_percentages_for_phase_no_fields(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    e_stage_types = [
        StageTypes.ASSIGN_PRACTICES,
        StageTypes.FIELD_BOUNDARIES,
        StageTypes.VIEW_OUTCOMES,
        StageTypes.CONTRACT,
        StageTypes.ELIGIBILITY,
        StageTypes.SURVEY,
        StageTypes.CROP_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
    ]
    stages_by_type = {}
    for e_stage_type in e_stage_types:
        stages_by_type[e_stage_type] = await mdl.Stage(
            type_=e_stage_type, phase_id=e_phase.id, deleted_at=None, enabled=True
        )

    test_e_phase_percentages = await obtain_stage_completion_percentages_for_phase(
        app_request, project.id, e_phase, program.id
    )

    for e_stage_type in e_stage_types:
        assert stages_by_type[e_stage_type].id in test_e_phase_percentages
    for percentage in test_e_phase_percentages.values():
        assert isclose(percentage, 0.0)
