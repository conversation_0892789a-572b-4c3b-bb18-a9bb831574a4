from datetime import datetime, timedelta, timezone

from fields.db import undelete_fields
from fields.enums import FieldStatus
from fields.methods import back_fill_field_status
from permissions.enums import RoleTypes
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from projects.db import get_project_stage_completion_from_project_stage_summary
from ui.projects.completion.dataclasses import StageCompletionResult
from ui.projects.completion.db import store_stage_completion_for_field
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.handle_project_stage_updates import (
    attempt_updating_all_relevant_stage_completions_for_project,
    handle_updating_stage_completions_for_project,
    obtain_stage_completion_for_project,
)
from ui.projects.completion.provide_completion_results import (
    obtain_stage_completion_percentages_for_phase,
)


async def test_obtain_stage_completion_for_project(app_request, mdl):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
        deleted_at=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.contract_voided)
    field_3 = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    test_stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=e_phase.id, deleted_at=None, enabled=True)
    await store_stage_completion_for_field(
        app_request,
        test_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=2, total_cycle_count=3
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        test_stage.id,
        field_2.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=3, total_cycle_count=3
        ),
    )
    await store_stage_completion_for_field(
        app_request,
        test_stage.id,
        field_3.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=0, total_cycle_count=3
        ),
    )

    # a completion result will be generated and added to the db if it does not otherwise exist
    completion_result = await obtain_stage_completion_for_project(app_request, test_stage.id, project.id)
    assert completion_result.percentage_complete == 56
    assert completion_result.total == 9
    assert completion_result.is_completed is False
    assert completion_result.to_complete == 4
    assert completion_result.completed == 5
    db_completion_result = await get_project_stage_completion_from_project_stage_summary(
        request=app_request,
        stage_id=test_stage.id,
        project_id=project.id,
    )
    assert db_completion_result.percentage_complete == 56
    assert db_completion_result.total == 9
    assert db_completion_result.is_completed is False
    assert db_completion_result.to_complete == 4
    assert db_completion_result.completed == 5

    # changes are registered appropriately if the change is handled
    await store_stage_completion_for_field(
        app_request,
        test_stage.id,
        field.id,
        StageCompletionResult(
            completion_status=NarrowestCompletionStatus.complete, completed_cycle_count=3, total_cycle_count=3
        ),
    )
    await handle_updating_stage_completions_for_project(app_request, test_stage, project.id, False)
    completion_result = await obtain_stage_completion_for_project(app_request, test_stage.id, project.id)
    assert completion_result.percentage_complete == 67
    assert completion_result.total == 9
    assert completion_result.is_completed is False
    assert completion_result.to_complete == 3
    assert completion_result.completed == 6
    db_completion_result = await get_project_stage_completion_from_project_stage_summary(
        request=app_request,
        stage_id=test_stage.id,
        project_id=project.id,
    )
    assert db_completion_result.percentage_complete == 67
    assert db_completion_result.total == 9
    assert db_completion_result.is_completed is False
    assert db_completion_result.to_complete == 3
    assert db_completion_result.completed == 6


async def test_attempt_updating_all_relevant_stage_completions_for_project(app_request, mdl):
    producer_role = await mdl.Roles(name="Producer", role_type=RoleTypes.PRODUCER)

    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    phase_end_date = (datetime.now() + timedelta(days=90)).date()
    e_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True, end_date=phase_end_date
    )
    m_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True, end_date=phase_end_date
    )
    stages = []
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            stage = await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)
            stages.append(stage)

    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=datetime.now(), status=FieldStatus.deleted)
    await mdl.RolesUsers(role_id=producer_role.id, program_id=program.id, fs_user_id=user.id)

    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=1, total_cycle_count=2
    )
    for stage in stages:
        await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)

    await attempt_updating_all_relevant_stage_completions_for_project(app_request, project.id)

    stage_percentages = await obtain_stage_completion_percentages_for_phase(
        app_request, project.id, e_phase, program.id
    )
    assert stage_percentages[stages[0].id] == 0

    # simulate undeleting a field
    await undelete_fields(app_request, [field.id])
    await back_fill_field_status(app_request, project_ids=[project.id])
    await attempt_updating_all_relevant_stage_completions_for_project(app_request, project.id)

    stage_percentages = await obtain_stage_completion_percentages_for_phase(
        app_request, project.id, e_phase, program.id
    )
    assert stage_percentages[stages[0].id] == 50
