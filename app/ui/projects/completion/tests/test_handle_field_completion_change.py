from datetime import date, datetime, timezone
from unittest.mock import patch

from ses_client.client import Client as SesClient
from sqlalchemy import select

from boundaries_service import client as boundaries_client
from defaults.attribute_options import CropUsage
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from fields.enums import FieldStatus
from helper.helper import run_query
from logger import get_logger
from phases.dataclasses import DateRange
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ses_integration.tests.mocks import (
    get_mock_crop_sequences,
    get_mock_field_practice_events_with_fallow_period,
)
from ui.projects.completion.dataclasses import (
    EvaluationContext,
    StageCompletionResult,
)
from ui.projects.completion.db import (
    get_per_field_stage_completions_for_project,
    get_stage_completion_id_lookup,
    store_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.handle_field_completion_change import (
    _are_there_independently_changed_completion_results,
    _determine_reporting_completions_for_field,
    _determine_stage_completions_for_field_cultivation_cycles,
    update_stage_completions_for_field,
)
from ui.projects.completion.handle_field_completion_invalidation import (
    bulk_handle_project_invalidation,
    handle_field_phase_invalidation,
)
from ui.projects.completion.model import StageFieldCompletions
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
)
from ui.projects.completion.tests.scenarios import (
    generate_test_crop_sequences,
    generate_test_field_practice_events,
    is_scenario_for_single_phase_programs,
    ScenarioForTesting,
    should_testing_scenario_pass_completion,
)
from ui.projects.field_events.methods import (
    _build_field_cultivation_cycles_result_lookup,
)
from ui.projects.field_events.schema import (
    CultivationCycleResponseId,
    CultivationCycleResult,
    NoPracticeObservations,
)
from ui.projects.field_events.tests.mocks import get_mock_geom
from ui.projects.methods import get_project_program_phase_and_stage

field_md5 = "field-md5"


logger = get_logger(__name__)


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_handle_updating_stage_completions_for_field(app_request, mdl) -> None:
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    # used to test retreiving statuses of non-updated completions
    unassessed_field = await mdl.Fields(
        md5="not-" + field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True)
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)

    program, session_e_phase, _ = await get_project_program_phase_and_stage(app_request, project.id, e_phase.id, None)
    completions = await update_stage_completions_for_field(app_request, program, session_e_phase, field, None, True)
    assert len(completions) == 4
    for stage_id, completion_result in completions.items():
        assert completion_result.completion_status == NarrowestCompletionStatus.incomplete
        stored_completions = await get_per_field_stage_completions_for_project(app_request, stage_id, project.id)
        assert len(stored_completions) == 2
        for sc in stored_completions:
            assert sc.stage_id == stage_id
            if sc.field_id == field.id:
                assert sc.completion_result.completion_status == completion_result.completion_status
            else:
                assert sc.field_id == unassessed_field.id
                assert sc.completion_result.completion_status == NarrowestCompletionStatus.undetermined

    program, session_m_phase, _ = await get_project_program_phase_and_stage(app_request, project.id, m_phase.id, None)
    completions = await update_stage_completions_for_field(app_request, program, session_m_phase, field, None, True)
    assert len(completions) == 4
    for stage_id, completion_result in completions.items():
        assert completion_result.completion_status != NarrowestCompletionStatus.undetermined
        stored_completions = await get_per_field_stage_completions_for_project(app_request, stage_id, project.id)
        assert len(stored_completions) == 2
        for sc in stored_completions:
            assert sc.stage_id == stage_id
            if sc.field_id == field.id:
                assert sc.completion_result.completion_status == completion_result.completion_status
            else:
                # the field with no completion updates has undetermined completion status
                assert sc.field_id == unassessed_field.id
                assert sc.completion_result.completion_status == NarrowestCompletionStatus.undetermined

    await handle_field_phase_invalidation(app_request, session_m_phase.id, field.id)
    stored_completions = await get_per_field_stage_completions_for_project(app_request, stage_id, project.id)
    assert len(stored_completions) == 2
    for sc in stored_completions:
        assert sc.completion_result.completion_status == NarrowestCompletionStatus.undetermined

    dashboard_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.DASHBOARDS, deleted_at=None, enabled=True
    )
    completions = await update_stage_completions_for_field(app_request, program, dashboard_phase, field, None, True)
    assert len(completions) == 0


async def test_bulk_handle_project_invalidation(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)

    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=field_1.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=field_2.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )

    other_project = await mdl.Projects(program_id=program.id)
    other_field = await mdl.Fields(parent_project_id=other_project.id)
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=other_field.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )

    await bulk_handle_project_invalidation(request=app_request, project_ids=[project.id])

    async with db_session_maker() as s:
        query = (
            select(StageFieldCompletions)
            .where(StageFieldCompletions.field_completion_status == NarrowestCompletionStatus.undetermined)
            .order_by(StageFieldCompletions.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
    assert len(res) == 2
    assert res[0].field_id == field_1.id
    assert res[1].field_id == field_2.id


async def test_extended_scenario_validation(app_request, mdl):
    for scenario in list(ScenarioForTesting):
        logger.info(f"testing scenario: {scenario}")
        start = datetime(year=2023, month=1, day=1, hour=1, minute=1, second=1, tzinfo=timezone.utc)
        end = datetime(year=2023, month=12, day=31, hour=1, minute=1, second=1, tzinfo=timezone.utc)
        is_single_phase_data_collection = is_scenario_for_single_phase_programs(scenario)

        program = await mdl.Programs(
            reporting_period_start_date=start,
            reporting_period_end_date=end,
            program_template=ProgramTemplate.event_based,
            required_years_of_history=3,
            is_single_phase_data_collection=is_single_phase_data_collection,
        )
        user = await mdl.Users()
        project = await mdl.Projects(program_id=program.id)
        await mdl.ProjectPermissions(project=project.id, user=user.id)
        field = await mdl.Fields(
            md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
        )
        e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            await mdl.Stage(type_=stage_type, phase_id=e_phase.id, deleted_at=None, enabled=True)

        ses_crop_events = generate_test_crop_sequences(field_md5, scenario)
        ses_events = generate_test_field_practice_events(field_md5, scenario)
        cultivation_cycles = (
            await _build_field_cultivation_cycles_result_lookup(
                request=app_request,
                fields=[field],
                ses_crop_events=ses_crop_events,
                ses_events=ses_events,
                program=program,
                acting_user_id=None,
                owner_user_id=user.id,
                phase=e_phase,
                stage=None,
            )
        )[field.id]
        completions = await _determine_stage_completions_for_field_cultivation_cycles(
            app_request, program, e_phase, field, cultivation_cycles
        )
        all_complete = all([NarrowestCompletionStatus.complete == v.completion_status for v in completions.values()])
        assert all_complete == should_testing_scenario_pass_completion(scenario)


async def test_are_there_independently_changed_completion_results(mdl, app_request):
    # setup
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stages = [
        (await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True))
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]
    ]
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    completion_result = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=2, total_cycle_count=3
    )

    # test no change case
    for stage in stages:
        await store_stage_completion_for_field(app_request, stage.id, field.id, completion_result)

    existing_stage_completion_id_lookup = await get_stage_completion_id_lookup(app_request, phase.id, field.id)

    false_for_no_changes = await _are_there_independently_changed_completion_results(
        app_request, phase.id, field.id, existing_stage_completion_id_lookup
    )
    assert false_for_no_changes is False

    # test change case
    # even if the values are the same, the unique identifiers should be different
    for stage in stages:
        await store_stage_completion_for_field(app_request, stage.id, field.id, completion_result)

    true_for_changes = await _are_there_independently_changed_completion_results(
        app_request, phase.id, field.id, existing_stage_completion_id_lookup
    )
    assert true_for_changes is True


async def test_cropping_event_type_in_determine_reporting_completions_for_field(
    mdl,
    app_request,
    create_fallow_period_data,
    create_cropping_event_data,
    create_interval_data,
    create_reduction_event_data,
):
    # test setup
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    no_practice_observations = NoPracticeObservations(
        tillage_event=True,
        application_event=True,
        irrigation_event=True,
    )
    cc_start = datetime(2025, 1, 1, 0, 0, tzinfo=timezone.utc)
    cc_end = datetime(2025, 12, 1, 0, 0, tzinfo=timezone.utc)
    event_start = datetime(2021, 4, 1, tzinfo=timezone.utc)
    event_end = datetime(2021, 9, 1, tzinfo=timezone.utc)
    interval = create_interval_data(start=event_start, end=event_end)

    response_id = CultivationCycleResponseId(
        crop_event_id=None,
        crop_type=None,
        start_date=cc_start,
        end_date=cc_end,
        phase_id=phase.id,
        stage_id=None,
    )

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    reporting_period = DateRange(start_date=date(2025, 1, 1), end_date=date(2025, 12, 31))

    # fallow should satisfy measuremnt
    fallow_period_data = create_fallow_period_data(interval=interval)
    fallow_period = FallowPeriod.parse_obj(fallow_period_data)

    cultivation_cycle = CultivationCycleResult(
        id=response_id,
        events=[fallow_period],
        no_practice_observations=no_practice_observations,
        crop_event_is_locked=False,
        participates_in_phase_completion=True,
        contains_prefilled_monitoring_phase_events=False,
        start=cc_start,
        end=cc_end,
    )

    completion_results = await _determine_reporting_completions_for_field(
        app_request,
        [stage],
        [cultivation_cycle],
        ec,
        vs,
        reporting_period,
    )

    result = completion_results[stage.id]
    assert result.completion_status == NarrowestCompletionStatus.complete

    cropping_event_data = create_cropping_event_data(
        interval=interval,
        reductions=[create_reduction_event_data(occurred_at=event_end)],
        crop_usage=CropUsage.COMMODITY,
    )
    cropping_event = CroppingEvent.parse_obj(cropping_event_data)
    cultivation_cycle.events = [cropping_event]

    completion_results = await _determine_reporting_completions_for_field(
        app_request,
        [stage],
        [cultivation_cycle],
        ec,
        vs,
        reporting_period,
    )

    result = completion_results[stage.id]
    assert result.completion_status == NarrowestCompletionStatus.complete

    cropping_event_data = create_cropping_event_data(
        interval=interval,
        reductions=[create_reduction_event_data(occurred_at=event_end)],
        crop_usage=CropUsage.COVER,
    )
    cropping_event = CroppingEvent.parse_obj(cropping_event_data)
    cultivation_cycle.events = [cropping_event]

    completion_results = await _determine_reporting_completions_for_field(
        app_request,
        [stage],
        [cultivation_cycle],
        ec,
        vs,
        reporting_period,
    )

    result = completion_results[stage.id]
    assert result.completion_status == NarrowestCompletionStatus.incomplete
