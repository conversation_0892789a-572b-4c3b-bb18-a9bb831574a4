from sqlalchemy import select

from fields.enums import FieldStatus
from helper.helper import run_query
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ui.projects.completion.dataclasses import (
    StageCompletionResult,
    to_validation_results,
)
from ui.projects.completion.db import (
    bulk_update_stage_completion_for_fields,
    get_field_ids_with_any_stage_completions,
    get_per_field_stage_completions_for_project,
    get_stage_completion_id_lookup,
    store_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.model import StageFieldCompletions

# note that invalidate_completion_status_for_field and get_per_field_stage_completions_for_project
# are used in test_handle_field_completion_change in ways that test them


async def test_store_stage_completion_for_field(app_request, mdl):
    """
    store_stage_completion_for_field relies on a unique key and INSERT ... ON DUPLICATE KEY UPDATE ...
    syntax; this function makes sure that's used apporpriately
    """
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    validation_errors = [
        to_validation_results(
            {
                "event_id": "1",
                "error_messages": ["turnip gravy is actually good for you"],
                "stage_type": StageTypes.CROP_EVENTS,
                "error_key": ["key"],
            }
        )
    ]
    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycle_count=1,
        total_cycle_count=2,
        validation_errors=validation_errors,
    )
    await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)

    fs_comps = await get_per_field_stage_completions_for_project(app_request, stage.id, project.id)
    assert len(fs_comps) == 1
    assert fs_comps[0].completion_result.completion_status == NarrowestCompletionStatus.incomplete
    assert fs_comps[0].completion_result.completed_cycle_count == 1
    assert fs_comps[0].completion_result.total_cycle_count == 2
    assert fs_comps[0].completion_result.validation_errors == validation_errors

    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.complete,
        completed_cycle_count=2,
        total_cycle_count=2,
        validation_errors=[],
    )
    await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)

    fs_comps = await get_per_field_stage_completions_for_project(app_request, stage.id, project.id)
    assert len(fs_comps) == 1
    assert fs_comps[0].completion_result.completion_status == NarrowestCompletionStatus.complete
    assert fs_comps[0].completion_result.completed_cycle_count == 2
    assert fs_comps[0].completion_result.total_cycle_count == 2
    assert fs_comps[0].completion_result.validation_errors == []


async def test_bulk_update_stage_completion_for_fields(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(phase_id=phase.id)

    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=field_1.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=field_2.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )

    other_project = await mdl.Projects(program_id=program.id)
    other_field = await mdl.Fields(parent_project_id=other_project.id)
    await mdl.StageFieldCompletions(
        stage_id=stage.id,
        field_id=other_field.id,
        field_completion_status=NarrowestCompletionStatus.incomplete,
        completed_cycles=1,
        total_cycles=5,
    )

    await bulk_update_stage_completion_for_fields(
        request=app_request,
        field_ids=[field_1.id, field_2.id],
        field_completion_status=NarrowestCompletionStatus.undetermined,
        completed_cycles=None,
        total_cycles=None,
    )

    async with db_session_maker() as s:
        query = (
            select(StageFieldCompletions)
            .where(StageFieldCompletions.field_completion_status == NarrowestCompletionStatus.undetermined)
            .order_by(StageFieldCompletions.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
    assert len(res) == 2
    assert res[0].field_id == field_1.id
    assert res[1].field_id == field_2.id


async def test_get_field_ids_with_any_stage_completions(mdl, app_request):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=1, total_cycle_count=2
    )
    await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)

    f_ids_with_completions = await get_field_ids_with_any_stage_completions(app_request, [field.id, field_2.id])
    assert field.id in f_ids_with_completions
    assert field_2.id not in f_ids_with_completions


async def test_get_stage_completion_id_lookup(mdl, app_request):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=1, total_cycle_count=5
    )
    await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)
    stage_completion_status_2 = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=1, total_cycle_count=5
    )
    await store_stage_completion_for_field(app_request, stage.id, field_2.id, stage_completion_status_2)
    f1_lookup = await get_stage_completion_id_lookup(app_request, phase.id, field.id)
    f2_lookup = await get_stage_completion_id_lookup(app_request, phase.id, field_2.id)
    assert f1_lookup[stage.id] != f2_lookup[stage.id]
