from fastapi import Request

from phases.db import get_field_phases_by_matching_md5_and_user
from ui.projects.completion import tasks as completion_tasks
from ui.projects.completion.constants import SUPPORTED_PHASE_TYPES
from ui.projects.completion.handle_field_completion_invalidation import (
    handle_field_phase_invalidation,
)


async def process_field_completion_domain_event(request: Request, field_id: int) -> None:
    """
    internals for domain_event_bus.domain_event_handlers.FieldCompletionEventHandler
    """
    field_phases = await get_field_phases_by_matching_md5_and_user(request, field_id)
    for field_with_phases in field_phases:
        field_id = field_with_phases[0].id
        for phase in field_with_phases[1]:
            if phase.type_ in SUPPORTED_PHASE_TYPES:
                await _handle_field_phase_change(request, field_id, phase.id)


# internals
async def _handle_field_phase_change(
    request: Request,
    field_id: int,
    phase_id: int,
) -> None:
    stage_completion_identifier_lookup = await handle_field_phase_invalidation(
        request=request, field_id=field_id, phase_id=phase_id
    )
    completion_tasks.run_field_completion_change_handler.delay(
        phase_id=phase_id,
        field_id=field_id,
        stage_completion_identifier_lookup=stage_completion_identifier_lookup,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )
