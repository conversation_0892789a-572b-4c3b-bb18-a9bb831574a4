from phases.enums import PhaseTypes, StageTypes

MAX_FIELDS_UPDATED_INLINE = 2

MAX_FIELDS_UPDATED_CRON = 500

FALLOW_OVERLAPS_CROPPING_KEY = "fallow_overlaps_cropping"
FALLOW_OVERLAPS_CROPPING_MSG = "Fallow period cannot overlap cropping period."
RESIDUE_HARVESTED_MISSING_KEY = "residue_harvested_missing"
RESIDUE_HARVESTED_MISSING_MSG = "residue_harvested is a required attribute type for commodity and forage crops."
TERMINATION_METHOD_MISSING_KEY = "termination_method_missing"
TERMINATION_METHOD_MISSING_MSG = (
    "termination_method is a required attribute type for cover, catch, and companion crops."
)
TOTAL_CROP_DATE_RANGE_OVERLAP_MSG_KEY = "total_crop_date_range_overlap"
TOTAL_CROP_DATE_RANGE_OVERLAP_MSG = (
    "Two commodity crops or two cover crops cannot have the same planting and harvest dates."
)
ALL_STAGES_FALLOW_KEY = "all_stages_fallow"
ALL_STAGES_FALLOW_MSG = "Field cannot be fallow for 100% of the field's history"

SUPPORTED_PHASE_TYPES = [PhaseTypes.ENROLMENT, PhaseTypes.MONITORING]


SUPPORTED_FIELD_EVENT_STAGE_TYPES = [
    # StageTypes.FIELD_BOUNDARIES (still used, different shared event/legacy implementation)
    # StageTypes.ASSIGN_PRACTICES (still used, different shared event/legacy implementation)
    # StageTypes.CONFIRM_HISTORY (legacy, not supported)
    # StageTypes.SUMMER_CROPS,   legacy phase, not expected but interpreted as StageTypes.CROP_EVENTS
    # StageTypes.WINTER_CROPS,   legacy phase, not expected but interpreted as StageTypes.CROP_EVENTS
    # StageTypes.TILLAGE,   legacy phase, not expected but interpreted as StageTypes.TILLAGE_EVENTS
    # StageTypes.NUTRIENT_MGMT,   legacy phase, not expected but interpreted as StageTypes.NUTRIENT_EVENTS
    # StageTypes.VIEW_OUTCOMES (still used, different shared event/legacy implementation)
    # StageTypes.SURVEY  (still used, different shared event/legacy implementation)
    # StageTypes.CONTRACT  (still used, different shared event/legacy implementation)
    # StageTypes.ELIGIBILITY  (still used, different shared event/legacy implementation)
    # StageTypes.FIELD_INFORMATION  (legacy, not supported)
    # StageTypes.HISTORICAL_CROP_ROTATION (legacy, not supported)
    # StageTypes.HISTORICAL_TILLAGE,   legacy phase, not expected but interpreted as StageTypes.TILLAGE_EVENTS
    # StageTypes.INTENDED_COMMODITY_CROPS (legacy, not supported)
    # StageTypes.IRRIGATION,   legacy phase, not expected but interpreted as StageTypes.IRRIGATION_EVENTS
    # StageTypes.RICE_CROP_HISTORY  (not currently supported)
    # StageTypes.MOB_HISTORY (legacy, not supported)
    # StageTypes.MOB_MOVEMENT  (legacy, not supported)
    # StageTypes.FARM_LEVEL_MANAGEMENT  (legacy, not supported)
    # StageTypes.NUTRIENT_MGMT_INTENDED  (legacy, not supported)
    StageTypes.CROP_EVENTS,  # supported event stage, not legacy
    StageTypes.IRRIGATION_EVENTS,  # supported event stage, not legacy
    StageTypes.NUTRIENT_EVENTS,  # supported event stage, not legacy
    StageTypes.TILLAGE_EVENTS,  # supported event stage, not legacy
    # StageTypes.CHEMICAL_MANAGEMENT = "CHEMICAL_MANAGEMENT" (legacy, not supported)
]

ROOT_VEGGIES_EXCLUSION_LIST = ["sugar beets", "potatoes"]
FLOOD_EVENT_OVERLAP_ERROR_MSG = "Flood event and other event date intervals overlap"
FLOOD_EVENT_OVERLAP_ERROR_KEY = "flood_event_overlap_error"
TILLAGE_CROPPING_OVERLAP_ERROR_MSG = "Tillage events cannot overlap with cropping events in the same cycle."
TILLAGE_CROPPING_OVERLAP_ERROR_KEY = "tillage_cropping_overlap_error"
VALIDATIONS_FAIL_COMPLETION = False
