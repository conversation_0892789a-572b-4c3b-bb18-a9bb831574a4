from typing import Optional

from fastapi import HTT<PERSON>Exception, status
from starlette.requests import Request

from fields.db import get_field_by_id
from fields.schema import Field
from logger import get_logger
from phases.dataclasses import DateRange
from phases.db import (
    get_phase_by_id,
    get_stages_from_program_and_phase_types,
)
from phases.enums import PhaseTypes
from phases.model import Phases, Stage
from programs.db import get_program_by_id
from programs.enums import ProgramTemplate
from programs.model import Programs
from projects.db import get_owner_user_id_for_project
from ui.projects.completion.annotations import StageCompletionIdentifierLookup, StageId
from ui.projects.completion.constants import (
    SUPPORTED_FIELD_EVENT_STAGE_TYPES,
    SUPPORTED_PHASE_TYPES,
)
from ui.projects.completion.dataclasses import EvaluationContext, StageCompletionResult
from ui.projects.completion.db import (
    get_stage_completion_id_lookup,
    store_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.evaluation.cycle_completion_checks import (
    is_commodity_crop_or_fallow_cycle,
    is_cycle_with_crop_in_progress,
)
from ui.projects.completion.evaluation.stage_completion_checks import (
    get_stage_completion_evaluation_function,
)
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
    ValidationSpecification,
)
from ui.projects.field_events.methods import (
    build_field_cultivation_cycles_for_calculations,
    is_cultivation_cycle_in_filter_period,
)
from ui.projects.field_events.schema import CultivationCycleResult

logger = get_logger(__name__)


StageCompletionResults = dict[StageId, StageCompletionResult]

# *** Interface ***
# You should be able to calculate and store stage completions for fields
# with a variety of arguments as convenient from here


async def handle_updating_stage_completions_for_field(
    request: Request,
    phase_id: int,
    field_id: int,
    stage_completion_id_lookup: Optional[StageCompletionIdentifierLookup],
) -> StageCompletionResults:
    """
    This function will see that field-level stage completions are calculated and stored,
    and also dispatch jobs to see that project-level stage completions are also updated
    """
    field = await get_field_by_id(request, field_id)
    phase = await get_phase_by_id(request, phase_id)

    if field is None or phase is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field or phase not found",
        )
    program = await get_program_by_id(request, phase.program_id)
    return await update_stage_completions_for_field(request, program, phase, field, stage_completion_id_lookup, False)


async def handle_updating_stage_completions_for_field_skipping_project(
    request: Request,
    phase_id: int,
    field_id: int,
    stage_completion_id_lookup: Optional[StageCompletionIdentifierLookup],
) -> StageCompletionResults:
    """
    This function will see that field-level stage completions are calculated and stored,
    and also update project-level stage completions
    """
    field = await get_field_by_id(request, field_id)
    phase = await get_phase_by_id(request, phase_id)

    if field is None or phase is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field or phase not found",
        )
    program = await get_program_by_id(request, phase.program_id)
    return await update_stage_completions_for_field(request, program, phase, field, stage_completion_id_lookup, True)


async def update_stage_completions_for_field(
    request: Request,
    program: Programs,
    phase: Phases,
    field: Field,
    stage_completion_id_lookup: Optional[StageCompletionIdentifierLookup],
    skip_recalculate_project: bool,
) -> StageCompletionResults:
    """
    This function will see that field-level stage completions are calculated and stored,
    and also dispatch jobs to see that project-level stage completions are also updated
    """
    if program.program_template != ProgramTemplate.event_based:
        # no completion results to calculate
        logger.error(
            "Attempting to use event-based completion for non-event program; no results available",
            extra={"program.id": program.id},
        )
        return {}
    if phase.type_ not in SUPPORTED_PHASE_TYPES:
        logger.warning(f"EBDC completion called for unexpected phase type: {phase.type_}; skipping update")
        return {}
    completion_results = await _determine_stage_completions_for_field(request, program, phase, field)
    # if we don't have a lookup, we're calculating inline with an API call
    # and don't want to recalculate
    if stage_completion_id_lookup is not None:
        # if there are new results since we invalidated, recalculate
        changed_results = await _are_there_independently_changed_completion_results(
            request, phase.id, field.id, stage_completion_id_lookup
        )
        if changed_results:
            completion_results = await _determine_stage_completions_for_field(request, program, phase, field)

    await _store_stage_completions_for_field(request, field, completion_results)

    if skip_recalculate_project:
        pass
    else:
        # break circular imports
        from ui.projects.completion.tasks import run_stage_completion_change_handler

        for stage_id in completion_results:
            run_stage_completion_change_handler.delay(
                stage_id=stage_id,
                project_id=field.parent_project_id,
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            )

    return completion_results


async def determine_stage_completions_for_field_with_ids(
    request: Request,
    phase_id: int,
    field_id: int,
) -> StageCompletionResults:
    """
    This function will only calculate field-level completions and not store them,
    nor dispatch further jobs
    """
    field = await get_field_by_id(request, field_id)
    phase = await get_phase_by_id(request, phase_id)

    if field is None or phase is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field or phase not found",
        )
    program = await get_program_by_id(request, phase.program_id)
    return await _determine_stage_completions_for_field(request, program, phase, field)


# *** Internals ***


async def _determine_stage_completions_for_field(
    request: Request, program: Programs, phase: Phases, field: Field
) -> StageCompletionResults:
    """
    This is a very short method to allow using
    _determine_stage_completions_for_field_cultivation_cycles
    in tests
    """
    owner_user_id = await get_owner_user_id_for_project(request, field.parent_project_id)

    cultivation_cycles = sorted(
        (
            await build_field_cultivation_cycles_for_calculations(
                request=request,
                program=program,
                acting_user_id=None,
                owner_user_id=owner_user_id,
                phase=phase,
                stage=None,
                fields=[field],
            )
        )[field.id],
        key=lambda cc: (cc.start, cc.end),
    )
    return await _determine_stage_completions_for_field_cultivation_cycles(
        request, program, phase, field, cultivation_cycles
    )


async def _determine_stage_completions_for_field_cultivation_cycles(
    request: Request,
    program: Programs,
    phase: Phases,
    field: Field,
    cultivation_cycles: list[CultivationCycleResult],
) -> StageCompletionResults:
    stages = await get_stages_from_program_and_phase_types(request, program.id, [phase.type_])
    if not stages:
        logger.warning(
            "Attempting to assess completion with no enabled stages for program",
            extra={"program_id": program.id, "phase_id": phase.id},
        )
    if phase.type_ == PhaseTypes.ENROLMENT:
        if program.is_single_phase_data_collection:
            ec = EvaluationContext(
                program=program, phase=phase, field=field, completion_end_date=program.reporting_period_end_date.date()
            )
            vs = await generate_validation_specification_for_program(request, ec)
            completion_results = await _determine_reporting_completions_for_field(
                request, stages, cultivation_cycles, ec, vs, program.get_reporting_period()
            )
        else:
            ec = EvaluationContext(
                program=program,
                phase=phase,
                field=field,
                completion_end_date=program.reporting_period_start_date.date(),
            )
            vs = await generate_validation_specification_for_program(request, ec)
            completion_results = await _determine_historical_period_completions_for_field(
                request, stages, cultivation_cycles, ec, vs
            )
    else:
        if phase.type_ != PhaseTypes.MONITORING:
            raise ValueError(f"Unexpected phase type for determining stage completions: {phase.type_}")
        ec = EvaluationContext(
            program=program, phase=phase, field=field, completion_end_date=program.reporting_period_end_date.date()
        )
        vs = await generate_validation_specification_for_program(request, ec)
        completion_results = await _determine_reporting_completions_for_field(
            request, stages, cultivation_cycles, ec, vs, program.get_reporting_period()
        )
    return completion_results


async def _determine_historical_period_completions_for_field(
    request: Request,
    stages: list[Stage],
    all_field_cultivation_cycles: list[CultivationCycleResult],
    ec: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResults:
    """
    Each stage will pass if all of the cycles for the historical period pass evaluation
    """
    historical_cycles = _filter_to_participating_cultivation_cycles(all_field_cultivation_cycles)
    return await _check_all_completion_cycles_for_all_stages(request, stages, historical_cycles, ec, vs)


def _filter_to_participating_cultivation_cycles(
    cultivation_cycles: list[CultivationCycleResult],
) -> list[CultivationCycleResult]:
    return [cc for cc in cultivation_cycles if cc.participates_in_phase_completion]


async def _check_all_completion_cycles_for_all_stages(
    request: Request,
    stages: list[Stage],
    cultivation_cycles: list[CultivationCycleResult],
    ec: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResults:
    completion_results: StageCompletionResults = {}
    for stage in stages:
        evaluation_func = get_stage_completion_evaluation_function(stage.type_)
        if evaluation_func is None:
            continue
        completion_results[stage.id] = await evaluation_func(request, cultivation_cycles, ec, vs)
    return completion_results


async def _determine_reporting_completions_for_field(
    request: Request,
    stages: list[Stage],
    all_field_cultivation_cycles: list[CultivationCycleResult],
    ec: EvaluationContext,
    vs: ValidationSpecification,
    reporting_period: DateRange,
) -> StageCompletionResults:
    """
    We have to have everything complete up to one cropping cycle with a commodity crop.
    Another commodity or cover cropping can be started and not completed (i.e. not yet harvested or terminated)
    but we have to have at least one complete commodity crop.
    """
    relevant_cultivation_cycles = _remove_incomplete_end_cycle(
        _filter_to_participating_cultivation_cycles(all_field_cultivation_cycles)
    )
    has_one_commodity_cycle = any(
        map(is_commodity_crop_or_fallow_cycle, _filter_cycles_to_period(relevant_cultivation_cycles, reporting_period))
    )

    if has_one_commodity_cycle:
        completion_results = await _check_all_completion_cycles_for_all_stages(
            request, stages, relevant_cultivation_cycles, ec, vs
        )
    else:
        completion_results = {
            stage.id: StageCompletionResult(
                completion_status=NarrowestCompletionStatus.incomplete,
                completed_cycle_count=0,
                total_cycle_count=1,
            )
            for stage in stages
            if (stage.type_ in SUPPORTED_FIELD_EVENT_STAGE_TYPES)
        }
    return completion_results


def _filter_cycles_to_period(
    cultivation_cycles: list[CultivationCycleResult], filter_period: DateRange
) -> list[CultivationCycleResult]:
    return [cc for cc in cultivation_cycles if is_cultivation_cycle_in_filter_period(cc, filter_period)]


def _remove_incomplete_end_cycle(
    cultivation_cycles: list[CultivationCycleResult],
) -> list[CultivationCycleResult]:
    cycle_count = len(cultivation_cycles)
    if cycle_count == 0:
        return cultivation_cycles
    elif is_cycle_with_crop_in_progress(cultivation_cycles[-1]):
        return cultivation_cycles[:-1]
    else:
        return cultivation_cycles


async def _store_stage_completions_for_field(request: Request, field: Field, results: StageCompletionResults) -> None:
    storage_coroutines = [
        store_stage_completion_for_field(request, stage_id, field.id, completion_status)
        for stage_id, completion_status in results.items()
    ]
    for st_co in storage_coroutines:
        await st_co


async def _are_there_independently_changed_completion_results(
    request: Request, phase_id: int, field_id: int, existing_stage_completion_id_lookup: StageCompletionIdentifierLookup
) -> bool:
    current_stage_completion_id_lookup = await get_stage_completion_id_lookup(request, phase_id, field_id)
    for current_stage_id, completion_id in current_stage_completion_id_lookup.items():
        if current_stage_id not in existing_stage_completion_id_lookup:
            return True
        if completion_id != existing_stage_completion_id_lookup[current_stage_id]:
            return True
    return False
