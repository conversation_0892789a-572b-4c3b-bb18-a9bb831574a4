"""
This module handles any cross-event completion checks that are appropriate for the stage
but delegates all checks that can be made about a specific event; it also provides other
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Callable

from starlette.requests import Request

from defaults.attribute_options import CropUsage, IrrigationMethods
from entity_events.events.entity_event import Entity<PERSON>vent
from entity_events.events.enums import EntityEventType
from entity_events.events.irrigation_event import IrrigationEvent
from logger import get_logger
from phases.enums import StageTypes
from ui.projects.completion.constants import (
    FLOOD_EVENT_OVERLAP_ERROR_KEY,
    FLOOD_EVENT_OVERLAP_ERROR_MSG,
    ROOT_VEGGIES_EXCLUSION_LIST,
    TILLAGE_CROPPING_OVERLAP_ERROR_KEY,
    TILLAGE_CROPPING_OVERLAP_ERROR_MSG,
    VALIDATIONS_FAIL_COMPLETION,
)
from ui.projects.completion.dataclasses import (
    CompletionEvaluationResult,
    CycleCompletionValidationResult,
    EvaluationContext,
    ValidationResults,
    ValidationResultsError,
)
from ui.projects.completion.evaluation import event_completion_checks as ev_checks
from ui.projects.completion.specification.validation_specification import (
    ValidationSpecification,
)
from ui.projects.field_events.schema import CultivationCycleResult

logger = get_logger(__name__)


# Interface, and used out of package


def is_commodity_crop_cycle(cc: CultivationCycleResult) -> bool:
    return any(
        [
            ev.crop_usage == CropUsage.COMMODITY
            for ev in cc.events
            if _event_type_name(ev) == EntityEventType.CROPPING_EVENT
        ]
    )


def is_fallow_cycle(cc: CultivationCycleResult) -> bool:
    for ev in cc.events:
        if _event_type_name(ev) == EntityEventType.FALLOW_PERIOD:
            return True
    return False


def is_commodity_crop_or_fallow_cycle(cc: CultivationCycleResult) -> bool:
    return is_commodity_crop_cycle(cc) or is_fallow_cycle(cc)


def is_cycle_with_crop_in_progress(cc: CultivationCycleResult) -> bool:
    cropping_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.CROPPING_EVENT]
    if not cropping_events:
        return False
    return cropping_events[-1].has_crop_in_progress()


# interface, but only used within evaluation package by stage completion checks


async def evaluate_cycle_completion_for_crop_stage(
    request: Request, cc: CultivationCycleResult, ec: EvaluationContext, vs: ValidationSpecification
) -> CycleCompletionValidationResult:
    result: CycleCompletionValidationResult = CycleCompletionValidationResult(
        valid=True, validation_errors=list[ValidationResults]()
    )
    cropping_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.CROPPING_EVENT]
    fallow_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.FALLOW_PERIOD]

    found_commodity_cropping_event = any(ev.crop_usage == CropUsage.COMMODITY for ev in cropping_events)
    if not (found_commodity_cropping_event or fallow_events):
        result.valid = _is_period_ending_cycle(cc, ec)
    else:
        # we can have both cover-crop cultivation cycles and fallows, so checking both is fine

        # not sure if we need to validate that commodity crops and fallows don't end up in the
        # same cycle; seems like something the cultivation cycle should generate if needed

        # we could combine these but separating them in case we want to introduce event-type
        # specific processing in the future
        cropping_event_evaluation_result = await _check_until_completion_failure(
            lambda ev: ev_checks.check_cropping_event_completion(request, ev, ec, vs), cropping_events
        )
        # logger.info(f"cropping event check result: {cropping_event_evaluation_result.get_debugging_string()}")
        cropping_events_complete = cropping_event_evaluation_result.passed_evaluation
        fallow_period_evaluation_result = await _check_until_completion_failure(
            lambda ev: ev_checks.check_event_completion(request, ev, ec, vs), fallow_events
        )
        # logger.info(f"fallow period check result: {fallow_period_evaluation_result.get_debugging_string()}")
        fallow_period_complete = fallow_period_evaluation_result.passed_evaluation
        result.valid = cropping_events_complete and fallow_period_complete
    # logger.info(f"cropping complete in cycle {_summarize_cycle_result(cc)}\n: {result}")
    return result


# the later three methods end up sharing a lot of implementation;
# if we have to change them all, could be worth refactoring to one method


async def evaluate_cycle_completion_for_irrigation_stage(
    request: Request, cc: CultivationCycleResult, ec: EvaluationContext, vs: ValidationSpecification
) -> CycleCompletionValidationResult:
    result: CycleCompletionValidationResult = CycleCompletionValidationResult(
        valid=True, validation_errors=list[ValidationResults]()
    )
    stage_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.IRRIGATION_EVENT]
    have_no_stage_events = len(stage_events) == 0
    if cc.no_practice_observations.irrigation_event:
        # NPO is not a valid completion unless there really are no events
        result.valid = have_no_stage_events
    elif have_no_stage_events:
        result.valid = _is_period_ending_cycle_without_cropping_or_fallow(cc, ec)
    else:

        event_check_result = await _check_until_completion_failure(
            lambda se: ev_checks.check_event_completion(request, se, ec, vs), stage_events
        )

        # gather flood events
        flood_events = [
            ev for ev in stage_events if isinstance(ev, IrrigationEvent) and ev.method == IrrigationMethods.flood
        ]
        # Ensure there is no overlap between date intervals of flood_events and other_events
        result.valid = event_check_result.passed_evaluation
        # Check for overlapping events with a flooding event on the same field.
        # For each flood event, compare its interval with all other irrigation events in the stage.
        for flood_event in flood_events:
            for other_event in stage_events:
                if other_event == flood_event:
                    continue  # Skip comparing the event with itself

                # Determine the latest start and earliest end between the two intervals
                latest_start = max(flood_event.interval.start, other_event.interval.start)
                earliest_end = min(flood_event.interval.end, other_event.interval.end)

                # If the intervals overlap, mark the result as invalid and add a validation error
                if latest_start <= earliest_end:
                    result.validation_errors.append(
                        ValidationResults(
                            event_id=str(other_event.id),
                            errors=[
                                ValidationResultsError(
                                    error_key=FLOOD_EVENT_OVERLAP_ERROR_KEY, error_message=FLOOD_EVENT_OVERLAP_ERROR_MSG
                                )
                            ],
                            error_keys=None,
                            error_messages=None,
                            stage_type=StageTypes.IRRIGATION_EVENTS,
                        )
                    )
                    if VALIDATIONS_FAIL_COMPLETION:
                        result.valid = False

    # logger.info(f"irrigation complete in cycle {_summarize_cycle_result(cc)}\n: {result}")
    return result


async def evaluate_cycle_completion_for_nutrient_stage(
    request: Request, cc: CultivationCycleResult, ec: EvaluationContext, vs: ValidationSpecification
) -> CycleCompletionValidationResult:
    result: CycleCompletionValidationResult = CycleCompletionValidationResult(
        valid=True, validation_errors=list[ValidationResults]()
    )
    stage_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.APPLICATION_EVENT]
    have_no_stage_events = len(stage_events) == 0
    if cc.no_practice_observations.application_event:
        # NPO is not a valid completion unless there really are no events
        result.valid = have_no_stage_events
    elif have_no_stage_events:
        result.valid = _is_period_ending_cycle_without_cropping_or_fallow(cc, ec)
    else:
        event_check_result = await _check_until_completion_failure(
            lambda se: ev_checks.check_event_completion(request, se, ec, vs), stage_events
        )
        # logger.info(f"nutrient event check result: {event_check_result.get_debugging_string()}")
        result.valid = event_check_result.passed_evaluation
    # logger.info(f"nutrients complete in cycle {_summarize_cycle_result(cc)}\n: {result}")
    return result


def _events_overlap_beyond_meeting_date(event1: EntityEvent, event2: EntityEvent) -> bool:
    """
    Check if two events overlap in time beyond meeting at a particular date.
    Assumes that both events have a start and end date.
    """

    start1 = event1.get_interval_start_or_occurred_at()
    end1 = event1.get_interval_end_or_occurred_at()
    start2 = event2.get_interval_start_or_occurred_at()
    end2 = event2.get_interval_end_or_occurred_at()

    if None in (start1, end1, start2, end2):
        return False

    start1_date = start1.date()
    end1_date = end1.date()
    start2_date = start2.date()
    end2_date = end2.date()

    return not (end1_date <= start2_date or end2_date <= start1_date)


def _tillage_and_cropping_conflict(
    request: Request, cc: CultivationCycleResult, ec: EvaluationContext, vs: ValidationSpecification
) -> list[ValidationResults]:
    """
    Check if there are any cropping events in the cycle that conflict with tillage events.
    If there are, the cycle cannot be considered complete for tillage.
    """

    cropping_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.CROPPING_EVENT]
    tillage_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.TILLAGE_EVENT]
    results: list[ValidationResults] = []
    if not cropping_events or not tillage_events:
        return results  # No conflict if one of them is missing
    # Check if any cropping event overlaps with tillage events
    for crop_event in cropping_events:
        crop_type: str = crop_event.crop_type
        if crop_type in ROOT_VEGGIES_EXCLUSION_LIST:
            continue
        for tillage_event in tillage_events:
            if _events_overlap_beyond_meeting_date(crop_event, tillage_event):
                results.append(
                    ValidationResults(
                        event_id=str(tillage_event.id),
                        errors=[
                            ValidationResultsError(
                                error_key=TILLAGE_CROPPING_OVERLAP_ERROR_KEY,
                                error_message=TILLAGE_CROPPING_OVERLAP_ERROR_MSG,
                            )
                        ],
                        error_keys=None,
                        error_messages=None,
                        stage_type=StageTypes.TILLAGE_EVENTS,
                    )
                )
    return results


async def evaluate_cycle_completion_for_tillage_stage(
    request: Request, cc: CultivationCycleResult, ec: EvaluationContext, vs: ValidationSpecification
) -> CycleCompletionValidationResult:
    result: CycleCompletionValidationResult = CycleCompletionValidationResult(
        valid=True, validation_errors=list[ValidationResults]()
    )
    stage_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.TILLAGE_EVENT]

    have_no_stage_events = len(stage_events) == 0
    if cc.no_practice_observations.tillage_event:
        # NPO is not a valid completion unless there really are no events
        result.valid = have_no_stage_events
    elif have_no_stage_events:
        result.valid = _is_period_ending_cycle_without_cropping_or_fallow(cc, ec)
    else:
        # Check for conflicts with cropping events
        validation: list[ValidationResults] = _tillage_and_cropping_conflict(request, cc, ec, vs)

        if validation and len(validation) > 0:
            if VALIDATIONS_FAIL_COMPLETION:
                result.valid = False
            result.validation_errors = validation
            if VALIDATIONS_FAIL_COMPLETION:
                return result

        event_check_result = await _check_until_completion_failure(
            lambda se: ev_checks.check_event_completion(request, se, ec, vs), stage_events
        )
        # logger.info(f"tillage event check result: {event_check_result.get_debugging_string()}")
        result.valid = event_check_result.passed_evaluation
    # logger.info(f"tillage complete in cycle {_summarize_cycle_result(cc)}\n: {result}")
    return result


# internal
async def _check_until_completion_failure(
    completion_check_function: Callable, stage_events: list
) -> CompletionEvaluationResult:
    for se in stage_events:
        evaluation_result = await completion_check_function(se)
        if evaluation_result.passed_evaluation is False:
            # TO-DO: set date range
            return evaluation_result
    return CompletionEvaluationResult(
        passed_evaluation=True,
        failed_rules=set(),
        unevaluated_rules=set(),
        # TO-DO: set date range
        cultivation_cycle_date_range=None,
    )


def _is_period_ending_cycle_without_cropping_or_fallow(cc: CultivationCycleResult, ec: EvaluationContext) -> bool:
    period_ending_cycle = _is_period_ending_cycle(cc, ec)
    if not period_ending_cycle:
        return False
    for ev in cc.events:
        if _event_type_name(ev) in [EntityEventType.CROPPING_EVENT, EntityEventType.FALLOW_PERIOD]:
            return False
    return True


def _is_period_ending_cycle(cc: CultivationCycleResult, ec: EvaluationContext) -> bool:
    """
    Check to see if we're within 10 months of the end date
    """
    start_plus_min_window = (cc.start + timedelta(days=300)).date()
    return start_plus_min_window > ec.completion_end_date


def _summarize_cycle_result(cc: CultivationCycleResult) -> str:
    """
    Used for diagnostic logging in testing
    """
    types_clause = ", ".join([_event_type_name(e) for e in cc.events])
    npos_clause = f"npos: tillage={cc.no_practice_observations.tillage_event}  application={cc.no_practice_observations.application_event}  irrigation={cc.no_practice_observations.irrigation_event}"
    return f"cycle (from {cc.start} to {cc.end})\nevents: {types_clause}\n{npos_clause}\nused in completion: {cc.participates_in_phase_completion}"


# maybe this should be moved to the entity events
def _event_type_name(ev: EntityEvent) -> str:
    return ev.__class__.__name__
