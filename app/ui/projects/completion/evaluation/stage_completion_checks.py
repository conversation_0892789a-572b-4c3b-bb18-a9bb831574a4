"""
This module handles any cross-cycle completion checks that are appropriate for the stage
but delegates all checks that can be made within a single cycle
"""

from datetime import date
from typing import Any, Callable, Optional

from starlette.requests import Request

from defaults.attribute_options import CropUsage
from defaults.consts import COVER_CROP_USAGES
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.enums import EntityEventType
from logger import get_logger
from phases.enums import PhaseTypes, StageTypes
from ui.projects.completion.annotations import (
    FieldCompletionCountResponseWithErrors,
)
from ui.projects.completion.constants import (
    ALL_STAGES_FALLOW_KEY,
    ALL_STAGES_FALLOW_MSG,
    FALLOW_OVERLAPS_CROPPING_KEY,
    FALLOW_OVERLAPS_CROPPING_MSG,
    TOTAL_CROP_DATE_RANGE_OVERLAP_MSG,
    TOTAL_CROP_DATE_RANGE_OVERLAP_MSG_KEY,
    VALIDATIONS_FAIL_COMPLETION,
)
from ui.projects.completion.dataclasses import (
    EvaluationContext,
    StageCompletionResult,
    ValidationResults,
    ValidationResultsError,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.evaluation.cycle_completion_checks import (
    evaluate_cycle_completion_for_crop_stage,
    evaluate_cycle_completion_for_irrigation_stage,
    evaluate_cycle_completion_for_nutrient_stage,
    evaluate_cycle_completion_for_tillage_stage,
    is_commodity_crop_cycle,
)
from ui.projects.completion.specification.validation_specification import (
    ValidationSpecification,
)
from ui.projects.field_events.schema import CultivationCycleResult

logger = get_logger(__name__)


# Interface


def get_stage_completion_evaluation_function(stage_type: StageTypes) -> Optional[Callable]:
    """
    returns a function capable of evaluating a CultivationCycleResult for the stage;
    this function should be async and accept this argument signature:
    (request: Request, cultivation_cycles: list[CultivationCycleResult],
    es: EvaluationContext, vs: ValidationSpecification)
    """
    match stage_type:
        case StageTypes.CROP_EVENTS:
            return _evaluate_crop_stage_completion
        case StageTypes.SUMMER_CROPS:
            return _evaluate_crop_stage_completion
        case StageTypes.WINTER_CROPS:
            return _evaluate_crop_stage_completion
        case StageTypes.IRRIGATION:
            return _evaluate_irrigation_stage_completion
        case StageTypes.IRRIGATION_EVENTS:
            return _evaluate_irrigation_stage_completion
        case StageTypes.NUTRIENT_MGMT:
            return _evaluate_nutrient_stage_completion
        case StageTypes.NUTRIENT_EVENTS:
            return _evaluate_nutrient_stage_completion
        case StageTypes.TILLAGE_EVENTS:
            return _evaluate_tillage_stage_completion
        case StageTypes.TILLAGE:
            return _evaluate_tillage_stage_completion
        case StageTypes.HISTORICAL_TILLAGE:
            return _evaluate_tillage_stage_completion
        case _:
            # GRAZING_EVENT and FIRE_EVENT do not currently have coverage
            return None


# Internals


async def _evaluate_crop_stage_completion(
    request: Request,
    cultivation_cycles: list[CultivationCycleResult],
    es: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResult:
    cycle_count = len(cultivation_cycles)
    (completed_cycle_count, errors) = await _count_true_with_errors(
        lambda cc: evaluate_cycle_completion_for_crop_stage(request, cc, es, vs), cultivation_cycles
    )
    is_currently_complete = cycle_count == completed_cycle_count

    # we don't want to check the history unless we're checking the completion of the history,
    # so don't run this check unless it's enrollment
    all_fallow_errors = []
    if es.phase.type_ == PhaseTypes.ENROLMENT:
        all_fallow_errors = _find_all_historical_cycles_fallow(cultivation_cycles, es)

    total_overlaps = _find_total_overlap_in_same_usage_cropping_events(cultivation_cycles)

    fallow_overlaps = _find_fallows_overlapping_a_commodity_cropping(cultivation_cycles)

    if all_fallow_errors or total_overlaps or fallow_overlaps:
        if VALIDATIONS_FAIL_COMPLETION:
            is_currently_complete = False
        # need to change at least one event to fix
        if VALIDATIONS_FAIL_COMPLETION:
            if cycle_count == completed_cycle_count:
                completed_cycle_count = completed_cycle_count - 1
        if errors is None:
            errors = list[ValidationResults]
        for all_fallow_error in all_fallow_errors:
            errors.append(all_fallow_error)
        for total_overlap in total_overlaps:
            errors.append(total_overlap)
        for fallow_overlap in fallow_overlaps:
            errors.append(fallow_overlap)

    completion_status = _convert_bool_to_completion_status(is_currently_complete)

    if errors:
        errors = _consolidate_validation_errors(errors)

    return StageCompletionResult(
        completion_status=completion_status,
        completed_cycle_count=completed_cycle_count,
        total_cycle_count=cycle_count,
        validation_errors=errors,
    )


def _find_total_overlap_in_same_usage_cropping_events(
    cultivation_cycles: list[CultivationCycleResult],
) -> list[ValidationResults]:
    validation_errors: list[ValidationResults] = []
    cropping_events_by_planting_date: dict[date, CroppingEvent] = {}
    for cc in cultivation_cycles:
        cropping_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.CROPPING_EVENT]
        for ce in cropping_events:
            planting_date = ce.get_planting_date()
            harvest_date = ce.get_harvest_date()
            if planting_date and harvest_date and ce.crop_usage:
                ce_is_commodity = _is_commodity_usage(ce.crop_usage)
                candidate_conflicts = cropping_events_by_planting_date.get(planting_date, [])
                for conflict_candidate in candidate_conflicts:
                    if conflict_candidate.get_harvest_date() == harvest_date:
                        if _is_commodity_usage(conflict_candidate.crop_usage) == ce_is_commodity:
                            # we will consolidate the validation results per event later
                            validation_errors.append(
                                ValidationResults(
                                    event_id=ce.id,
                                    errors=[
                                        ValidationResultsError(
                                            error_key=TOTAL_CROP_DATE_RANGE_OVERLAP_MSG_KEY,
                                            error_message=TOTAL_CROP_DATE_RANGE_OVERLAP_MSG,
                                        )
                                    ],
                                    error_keys=None,
                                    error_messages=None,
                                    stage_type=StageTypes.CROP_EVENTS,
                                )
                            )
                            validation_errors.append(
                                ValidationResults(
                                    event_id=conflict_candidate.id,
                                    errors=[
                                        ValidationResultsError(
                                            error_key=TOTAL_CROP_DATE_RANGE_OVERLAP_MSG_KEY,
                                            error_message=TOTAL_CROP_DATE_RANGE_OVERLAP_MSG,
                                        )
                                    ],
                                    error_keys=None,
                                    error_messages=None,
                                    stage_type=StageTypes.CROP_EVENTS,
                                )
                            )
                candidate_conflicts.append(ce)
                cropping_events_by_planting_date[planting_date] = candidate_conflicts
    return validation_errors


def _find_fallows_overlapping_a_commodity_cropping(
    cultivation_cycles: list[CultivationCycleResult],
) -> list[ValidationResults]:
    validation_errors: list[ValidationResults] = []
    fps_with_intervals = []
    # filter to FPs with complete intervals
    for cc in cultivation_cycles:
        fallow_periods = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.FALLOW_PERIOD]
        for fp in fallow_periods:
            start = fp.get_interval_start_or_occurred_at()
            end = fp.get_interval_end_or_occurred_at()
            if start and end:
                fps_with_intervals.append(fp)

    for cc in cultivation_cycles:
        cropping_events = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.CROPPING_EVENT]
        for ce in cropping_events:
            planting_date = ce.get_planting_date()
            harvest_date = ce.get_harvest_date()
            if not ce.crop_usage:
                continue
            if not _is_commodity_usage(ce.crop_usage):
                continue
            if planting_date and harvest_date:
                for fp in fps_with_intervals:
                    # fallow ends before planting
                    fp_end = fp.get_interval_end_or_occurred_at().date()
                    if fp_end < planting_date:
                        continue
                    # fallow starts after harvest
                    fp_start = fp.get_interval_start_or_occurred_at().date()
                    if fp_start > harvest_date:
                        continue
                    # otherwise, we must overlap
                    # we will consolidate the validation results per event later
                    validation_errors.append(
                        ValidationResults(
                            event_id=ce.id,
                            errors=[
                                ValidationResultsError(
                                    error_key=FALLOW_OVERLAPS_CROPPING_KEY, error_message=FALLOW_OVERLAPS_CROPPING_MSG
                                )
                            ],
                            error_keys=None,
                            error_messages=None,
                            stage_type=StageTypes.CROP_EVENTS,
                        )
                    )
                    validation_errors.append(
                        ValidationResults(
                            event_id=fp.id,
                            errors=[
                                ValidationResultsError(
                                    error_key=FALLOW_OVERLAPS_CROPPING_KEY, error_message=FALLOW_OVERLAPS_CROPPING_MSG
                                )
                            ],
                            error_keys=None,
                            error_messages=None,
                            stage_type=StageTypes.CROP_EVENTS,
                        )
                    )
    return validation_errors


def _is_commodity_usage(crop_usage: CropUsage) -> bool:
    return crop_usage not in COVER_CROP_USAGES


async def _evaluate_irrigation_stage_completion(
    request: Request,
    cultivation_cycles: list[CultivationCycleResult],
    es: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResult:
    cycle_count = len(cultivation_cycles)
    (completed_cycle_count, errors) = await _count_true_with_errors(
        lambda cc: evaluate_cycle_completion_for_irrigation_stage(request, cc, es, vs), cultivation_cycles
    )
    completion_status = _convert_bool_to_completion_status(cycle_count == completed_cycle_count)
    return StageCompletionResult(
        completion_status=completion_status,
        completed_cycle_count=completed_cycle_count,
        total_cycle_count=cycle_count,
        validation_errors=errors,
    )


async def _evaluate_nutrient_stage_completion(
    request: Request,
    cultivation_cycles: list[CultivationCycleResult],
    es: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResult:
    cycle_count = len(cultivation_cycles)
    (completed_cycle_count, errors) = await _count_true_with_errors(
        lambda cc: evaluate_cycle_completion_for_nutrient_stage(request, cc, es, vs), cultivation_cycles
    )
    completion_status = _convert_bool_to_completion_status(cycle_count == completed_cycle_count)
    return StageCompletionResult(
        completion_status=completion_status,
        completed_cycle_count=completed_cycle_count,
        total_cycle_count=cycle_count,
        validation_errors=errors,
    )


async def _evaluate_tillage_stage_completion(
    request: Request,
    cultivation_cycles: list[CultivationCycleResult],
    es: EvaluationContext,
    vs: ValidationSpecification,
) -> StageCompletionResult:
    cycle_count = len(cultivation_cycles)
    (completed_cycle_count, errors) = await _count_true_with_errors(
        lambda cc: evaluate_cycle_completion_for_tillage_stage(request, cc, es, vs), cultivation_cycles
    )
    completion_status = _convert_bool_to_completion_status(cycle_count == completed_cycle_count)
    return StageCompletionResult(
        completion_status=completion_status,
        completed_cycle_count=completed_cycle_count,
        total_cycle_count=cycle_count,
        validation_errors=errors,
    )


# internals
async def _count_true_with_errors(
    async_eval_func: Callable, cultivation_cycles: list[CultivationCycleResult]
) -> FieldCompletionCountResponseWithErrors:
    errors: list[ValidationResults] = []
    count = 0
    for cc in cultivation_cycles:
        result = await async_eval_func(cc)
        if result.valid:
            count += 1
        errors.extend(result.validation_errors)
    return count, errors


def _convert_bool_to_completion_status(status_bool: bool) -> NarrowestCompletionStatus:
    return NarrowestCompletionStatus.complete if status_bool else NarrowestCompletionStatus.incomplete


def _find_all_historical_cycles_fallow(
    cultivation_cycles: list[CultivationCycleResult], ec: EvaluationContext
) -> list[ValidationResults]:
    validation_errors: list[ValidationResults] = []

    have_any_commodity_cycle_in_history_period = any(
        [
            is_commodity_crop_cycle(cc)
            for cc in cultivation_cycles
            if cc.end.date() <= ec.program.reporting_period_start_date.date()
        ]
    )

    if not have_any_commodity_cycle_in_history_period:
        for cc in cultivation_cycles:
            fallow_periods = [ev for ev in cc.events if _event_type_name(ev) == EntityEventType.FALLOW_PERIOD]
            for fp in fallow_periods:
                validation_errors.append(
                    ValidationResults(
                        event_id=fp.id,
                        errors=[
                            ValidationResultsError(error_key=ALL_STAGES_FALLOW_KEY, error_message=ALL_STAGES_FALLOW_MSG)
                        ],
                        error_keys=None,
                        error_messages=None,
                        stage_type=StageTypes.CROP_EVENTS,
                    )
                )
    return validation_errors


# maybe this should be moved to the entity events
def _event_type_name(ev: EntityEvent) -> str:
    return ev.__class__.__name__


def _consolidate_validation_errors(unconsolidated_errors: list[ValidationResults]) -> list[ValidationResults]:
    errors_per_key: dict[str, set[ValidationResultsError]] = {}
    stage_per_key: dict[str, StageTypes] = {}
    event_id_per_key: dict[str, Any] = {}
    for vr in unconsolidated_errors:
        event_key = str(vr.event_id)
        event_id_per_key[event_key] = vr.event_id
        error_set = errors_per_key.get(event_key, set())
        for em in vr.errors:
            error_set.add(em)
        stage_per_key[event_key] = vr.stage_type
        errors_per_key[event_key] = error_set
    return [
        ValidationResults(
            event_id=event_id_per_key[event_key],
            errors=list(errors_per_key[event_key]),
            stage_type=stage_per_key[event_key],
            error_keys=None,
            error_messages=None,
        )
        for event_key in errors_per_key.keys()
    ]
