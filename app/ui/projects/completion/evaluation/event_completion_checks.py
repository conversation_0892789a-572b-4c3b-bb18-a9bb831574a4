from starlette.requests import Request

from entity_events.events.constants import USAGE_INVOLVES_HARVEST
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.validation.annotations import ValidationResultsResponse
from entity_events.validation.validator import Attribute<PERSON><PERSON>uesValidator
from logger import get_logger
from ui.projects.completion.constants import (
    RESIDUE_HARVESTED_MISSING_MSG,
    TERMINATION_METHOD_MISSING_MSG,
)
from ui.projects.completion.dataclasses import (
    CompletionEvaluationResult,
    EvaluationContext,
    ValidationAllowances,
)
from ui.projects.completion.specification.validation_specification import (
    ValidationSpecification,
)

logger = get_logger(__name__)

# interface


async def check_cropping_event_completion(
    request: Request,
    cropping_event: CroppingEvent,
    evaluation_context: EvaluationContext,
    validation_specification: ValidationSpecification,
) -> CompletionEvaluationResult:
    eval_result = await check_event_completion(request, cropping_event, evaluation_context, validation_specification)
    # check cropping-event specific completion
    specific_completion_failures = _find_completion_failures_from_usage_specific_attributes(cropping_event)
    if specific_completion_failures:
        eval_result.passed_evaluation = False
        for specific_failure in specific_completion_failures:
            eval_result.failed_rules.add(specific_failure)
    return eval_result


async def check_event_completion(
    request: Request,
    event: EntityEvent,
    evaluation_context: EvaluationContext,
    validation_specification: ValidationSpecification,
) -> CompletionEvaluationResult:
    validation_results = await _build_validation_results(request, event, evaluation_context)
    validation_allowances = _build_event_specific_validation_allowances(event)
    return validation_specification.do_event_validation_results_meet_specification(
        event.__class__.__name__, validation_results, validation_allowances
    )


# internals
def _find_completion_failures_from_usage_specific_attributes(cropping_event: CroppingEvent) -> list[str]:
    missing_attribute_messages = []
    if cropping_event.crop_usage in USAGE_INVOLVES_HARVEST:
        if cropping_event.residue_harvested is None:
            missing_attribute_messages.append(RESIDUE_HARVESTED_MISSING_MSG)
    else:
        if cropping_event.termination_method is None:
            missing_attribute_messages.append(TERMINATION_METHOD_MISSING_MSG)
    return missing_attribute_messages


async def _build_validation_results(
    request: Request, event: EntityEvent, evaluation_context: EvaluationContext
) -> ValidationResultsResponse:
    validator_results_and_errors = await AttributeValuesValidator.validate_event(
        event=event.to_validation_dict(),
        event_id=event.id,
        entity_event_name=event.__class__.__name__,
        run_required_attrs_check=True,
        run_required_values_check=True,
        program_id=evaluation_context.program.id,
        request=request,
        field_id=evaluation_context.field.id,
        phase_type=evaluation_context.phase.type_,
    )
    return validator_results_and_errors["all_results"]


def _build_event_specific_validation_allowances(event: EntityEvent) -> ValidationAllowances:
    validation_allowances = ValidationAllowances(allowed_failed_rules=set(), allowed_unevaluated_rules=set())
    if isinstance(event, CroppingEvent):
        if event.crop_usage not in USAGE_INVOLVES_HARVEST:
            validation_allowances.allowed_failed_rules.add("Crop yield value should be positive.")
            validation_allowances.allowed_unevaluated_rules.add("Crop yield value should be positive.")
    return validation_allowances
