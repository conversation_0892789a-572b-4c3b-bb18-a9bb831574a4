import datetime

from defaults.attribute_options import CropUsage
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ui.projects.completion.constants import (
    ALL_STAGES_FALLOW_KEY,
    ALL_STAGES_FALLOW_MSG,
    FALLOW_OVERLAPS_CROPPING_MSG,
    TOTAL_CROP_DATE_RANGE_OVERLAP_MSG,
    VALIDATIONS_FAIL_COMPLETION,
)
from ui.projects.completion.dataclasses import EvaluationContext
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.evaluation.stage_completion_checks import (
    _evaluate_crop_stage_completion,
    _evaluate_tillage_stage_completion,
)
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
)
from ui.projects.field_events.schema import (
    CultivationCycleResponseId,
    CultivationCycleResult,
    NoPracticeObservations,
)


async def test_evaluate_tillage_stage_completion_on_no_tillage_observation(app_request, mdl):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2021, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    no_tillage_cycle = CultivationCycleResult(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
            end_date=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            harvest_year=2020,
            phase_id=6,
            stage_id=None,
        ),
        start=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
        end=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
        events=[],
        no_practice_observations=NoPracticeObservations(
            tillage_event=True, application_event=False, irrigation_event=False
        ),
        participates_in_phase_completion=True,
    )
    completion_result = await _evaluate_tillage_stage_completion(app_request, [no_tillage_cycle], ec, vs)
    assert completion_result.completion_status == NarrowestCompletionStatus.complete
    assert completion_result.completed_cycle_count == 1
    assert completion_result.total_cycle_count == 1


async def test_evaluate_tillage_stage_completion_on_no_tillage(app_request, mdl):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2021, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    no_tillage_cycle = CultivationCycleResult(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
            end_date=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            harvest_year=2020,
            phase_id=6,
            stage_id=None,
        ),
        start=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
        end=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
        events=[],
        no_practice_observations=NoPracticeObservations(
            tillage_event=False, application_event=False, irrigation_event=False
        ),
        participates_in_phase_completion=True,
    )
    completion_result = await _evaluate_tillage_stage_completion(app_request, [no_tillage_cycle], ec, vs)
    assert completion_result.completion_status == NarrowestCompletionStatus.incomplete
    assert completion_result.completed_cycle_count == 0
    assert completion_result.total_cycle_count == 1


async def test_evaluate_crop_stage_completion_on_overlapped_crops(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        reporting_period_start_date=datetime.datetime(
            year=2022, month=1, day=1, hour=1, minute=1, second=1, tzinfo=datetime.timezone.utc
        ),
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2021, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    cropping_event_data = create_cropping_event_data()
    cropping_event_one = CroppingEvent.parse_obj(cropping_event_data)
    cropping_event_two = CroppingEvent.parse_obj(cropping_event_data)
    cropping_event_one.id = "1"
    cropping_event_two.id = "2"

    indentical_cropping_cycle = CultivationCycleResult(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
            end_date=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            harvest_year=2020,
            phase_id=6,
            stage_id=None,
        ),
        start=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
        end=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
        events=[cropping_event_one, cropping_event_two],
        no_practice_observations=NoPracticeObservations(
            tillage_event=False, application_event=False, irrigation_event=False
        ),
        participates_in_phase_completion=True,
    )
    crop_stage_completion = await _evaluate_crop_stage_completion(app_request, [indentical_cropping_cycle], ec, vs)
    if VALIDATIONS_FAIL_COMPLETION:
        assert crop_stage_completion.completion_status == NarrowestCompletionStatus.incomplete
    else:
        assert crop_stage_completion.completion_status == NarrowestCompletionStatus.complete

    assert len(crop_stage_completion.validation_errors) == 2

    expected_ids = {cropping_event_one.id, cropping_event_two.id}
    found_ids = set()
    for ve in crop_stage_completion.validation_errors:
        found_ids.add(ve.event_id)
        assert TOTAL_CROP_DATE_RANGE_OVERLAP_MSG in [e.error_message for e in ve.errors]
    assert expected_ids == found_ids


async def test_evaluate_crop_stage_completion_on_fallow_history(app_request, mdl, create_fallow_period_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        reporting_period_start_date=datetime.datetime(
            year=2022, month=1, day=1, hour=1, minute=1, second=1, tzinfo=datetime.timezone.utc
        ),
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2021, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    fallow_period_data = create_fallow_period_data()
    fallow_period = FallowPeriod.parse_obj(fallow_period_data)

    fallow_only_cycle = CultivationCycleResult(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
            end_date=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            harvest_year=2020,
            phase_id=6,
            stage_id=None,
        ),
        start=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
        end=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
        events=[fallow_period],
        no_practice_observations=NoPracticeObservations(
            tillage_event=False, application_event=False, irrigation_event=False
        ),
        participates_in_phase_completion=True,
    )
    crop_stage_completion = await _evaluate_crop_stage_completion(app_request, [fallow_only_cycle], ec, vs)

    expected_ids = {str(fallow_period.id)}
    found_ids = set()
    for ve in crop_stage_completion.validation_errors:
        found_ids.add(str(ve.event_id))
        assert ALL_STAGES_FALLOW_MSG in [e.error_message for e in ve.errors]
        assert ALL_STAGES_FALLOW_KEY in [e.error_key for e in ve.errors]
    assert expected_ids == found_ids


async def test_evaluate_crop_stage_completion_on_overlapped_crop_fallow(
    app_request,
    mdl,
    create_cropping_event_data,
    create_fallow_period_data,
    create_interval_data,
    create_reduction_event_data,
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        reporting_period_start_date=datetime.datetime(
            year=2022, month=1, day=1, hour=1, minute=1, second=1, tzinfo=datetime.timezone.utc
        ),
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2021, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    sowing_date_2021 = datetime.datetime(2021, 4, 1, tzinfo=datetime.timezone.utc)
    fallow_period_start = datetime.datetime(2021, 6, 1, tzinfo=datetime.timezone.utc)
    harvest_date_2021 = datetime.datetime(2021, 10, 1, tzinfo=datetime.timezone.utc)
    fallow_period_end = datetime.datetime(2021, 11, 1, tzinfo=datetime.timezone.utc)

    cropping_event_data = create_cropping_event_data(
        interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
        reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
        crop_usage=CropUsage.COMMODITY,
    )
    cropping_event = CroppingEvent.parse_obj(cropping_event_data)
    fallow_period_data = create_fallow_period_data(
        interval=create_interval_data(start=fallow_period_start, end=fallow_period_end)
    )
    fallow_period = FallowPeriod.parse_obj(fallow_period_data)

    overlapping_cropping_fallow_cycle = CultivationCycleResult(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
            end_date=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            harvest_year=2020,
            phase_id=6,
            stage_id=None,
        ),
        start=datetime.datetime(2019, 3, 2, 0, 0, tzinfo=datetime.timezone.utc),
        end=datetime.datetime(2020, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
        events=[cropping_event, fallow_period],
        no_practice_observations=NoPracticeObservations(
            tillage_event=False, application_event=False, irrigation_event=False
        ),
        participates_in_phase_completion=True,
    )
    crop_stage_completion = await _evaluate_crop_stage_completion(
        app_request, [overlapping_cropping_fallow_cycle], ec, vs
    )
    if VALIDATIONS_FAIL_COMPLETION:
        assert crop_stage_completion.completion_status == NarrowestCompletionStatus.incomplete
    else:
        assert crop_stage_completion.completion_status == NarrowestCompletionStatus.complete

    expected_ids = {str(cropping_event.id), str(fallow_period.id)}
    found_ids = set()
    for ve in crop_stage_completion.validation_errors:
        found_ids.add(str(ve.event_id))
        assert FALLOW_OVERLAPS_CROPPING_MSG in [e.error_message for e in ve.errors]
    assert expected_ids == found_ids
