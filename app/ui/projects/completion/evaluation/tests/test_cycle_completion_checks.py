from datetime import date, datetime, timezone

from defaults.attribute_options import CropUsage, IrrigationMethods
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ui.projects.completion.constants import (
    TILLAGE_CROPPING_OVERLAP_ERROR_MSG,
    VALIDATIONS_FAIL_COMPLETION,
)
from ui.projects.completion.dataclasses import EvaluationContext
from ui.projects.completion.evaluation.cycle_completion_checks import (
    evaluate_cycle_completion_for_crop_stage,
    evaluate_cycle_completion_for_irrigation_stage,
    evaluate_cycle_completion_for_nutrient_stage,
    evaluate_cycle_completion_for_tillage_stage,
    FLOOD_EVENT_OVERLAP_ERROR_MSG,
    is_commodity_crop_cycle,
    is_cycle_with_crop_in_progress,
)
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
)
from ui.projects.field_events.schema import (
    CultivationCycleResponseId,
    CultivationCycleResult,
    NoPracticeObservations,
)


def test_is_commodity_crop_cycle(create_interval_data, create_cropping_event_data, create_reduction_event_data):
    sowing_date_2021 = datetime(2021, 4, 1, 1, 1, 1, tzinfo=timezone.utc)
    harvest_date_2021 = datetime(2021, 10, 1, 1, 1, 1, tzinfo=timezone.utc)

    commodity_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
            crop_usage="Commodity",
        )
    )

    cover_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
            crop_usage="Cover",
        )
    )

    base_cultivation_cycle = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,  # I don't think we will need to have any dependency on this
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[],
        no_practice_observations=NoPracticeObservations(),
    )
    base_cultivation_cycle.events = [commodity_cropping_event]
    assert is_commodity_crop_cycle(base_cultivation_cycle) is True

    base_cultivation_cycle.events = [cover_cropping_event]
    assert is_commodity_crop_cycle(base_cultivation_cycle) is False


async def test_non_commodity_is_incomplete(
    app_request, mdl, create_interval_data, create_cropping_event_data, create_reduction_event_data
):
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)

    commodity_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
            crop_usage="Commodity",
        )
    )

    cover_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
            crop_usage="Cover",
        )
    )

    base_cultivation_cycle = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,  # I don't think we will need to have any dependency on this
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)

    base_cultivation_cycle.events = [commodity_cropping_event]
    result = await evaluate_cycle_completion_for_crop_stage(app_request, base_cultivation_cycle, ec, vs)
    assert result.valid is True

    base_cultivation_cycle.events = [cover_cropping_event]
    result = await evaluate_cycle_completion_for_crop_stage(app_request, base_cultivation_cycle, ec, vs)
    assert result.valid is False


def test_is_cycle_with_crop_in_progress(create_interval_data, create_cropping_event_data, create_reduction_event_data):

    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)
    completed_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
        )
    )

    uncompleted_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=1,
            interval=create_interval_data(start=sowing_date_2021, end=None),
            crop_type="barley",
            reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
        )
    )

    base_cultivation_cycle = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,  # I don't think we will need to have any dependency on this
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[],
        no_practice_observations=NoPracticeObservations(),
    )

    base_cultivation_cycle.events = [completed_cropping_event]
    assert is_cycle_with_crop_in_progress(base_cultivation_cycle) is False

    base_cultivation_cycle.events = [uncompleted_cropping_event]
    assert is_cycle_with_crop_in_progress(base_cultivation_cycle) is True


async def test_evaluate_cycle_completion_for_tillage_stage(mdl, app_request, create_tillage_event_data):

    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event_data = create_tillage_event_data()
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    # the cultivation cycle results parameters aren't checked now
    # so shouldn't be relevant to this test
    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)

    cc = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[tillage_event],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await evaluate_cycle_completion_for_tillage_stage(app_request, cc, ec, vs)
    assert result


async def test_evaluate_cycle_completion_for_nutrient_stage(mdl, app_request, create_application_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    application_event_data = create_application_event_data()
    application_event = ApplicationEvent.parse_obj(application_event_data)
    # the cultivation cycle results parameters aren't checked now
    # so shouldn't be relevant to this test
    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)
    cc = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[application_event],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await evaluate_cycle_completion_for_nutrient_stage(app_request, cc, ec, vs)
    assert result


async def test_evaluate_cycle_completion_for_irrigation_stage(mdl, app_request, create_irrigation_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.IRRIGATION_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    irrigation_event_data = create_irrigation_event_data()
    irrigation_event_1 = IrrigationEvent.parse_obj(irrigation_event_data)
    irrigation_event_2 = IrrigationEvent.parse_obj(irrigation_event_data)
    irrigation_event_1.interval = Interval(start=datetime(2021, 4, 1), end=datetime(2021, 6, 1))
    irrigation_event_1.method = IrrigationMethods.flood
    irrigation_event_2.interval = Interval(start=datetime(2021, 4, 2), end=datetime(2021, 5, 1))
    irrigation_event_2.method = IrrigationMethods.flood
    # the cultivation cycle results parameters aren't checked now
    # so shouldn't be relevant to this test
    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)
    cc = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[irrigation_event_1, irrigation_event_2],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await evaluate_cycle_completion_for_irrigation_stage(app_request, cc, ec, vs)
    assert result.valid != VALIDATIONS_FAIL_COMPLETION

    assert result.validation_errors is not None and any(
        error.event_id == str(irrigation_event_2.id)
        and FLOOD_EVENT_OVERLAP_ERROR_MSG in [e.error_message for e in error.errors]
        for error in result.validation_errors
    )


async def test_evaluate_cycle_completion_for_cropping_with_overlapping_tillage(
    mdl,
    app_request,
    create_cropping_event_data,
    create_interval_data,
    create_reduction_event_data,
    create_tillage_event_data,
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)

    completed_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )

    # Create a tillage event that overlaps with the cultivation cycle
    tillage_event_data = create_tillage_event_data()
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    tillage_event.occurred_at = datetime(2021, 6, 15)

    cc = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[completed_cropping_event, tillage_event],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await evaluate_cycle_completion_for_tillage_stage(app_request, cc, ec, vs)
    assert result.valid != VALIDATIONS_FAIL_COMPLETION
    assert result.validation_errors is not None
    for err in result.validation_errors:
        assert TILLAGE_CROPPING_OVERLAP_ERROR_MSG in [e.error_message for e in err.errors]


async def test_evaluate_cycle_completion_for_cropping_with_meeting_tillage(
    mdl,
    app_request,
    create_cropping_event_data,
    create_interval_data,
    create_reduction_event_data,
    create_tillage_event_data,
):
    """
    A tillage is not considered to overlap with a cropping period if it happens
    on either day
    """
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)

    completed_cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )

    # Create a tillage event that overlaps with the cultivation cycle
    # exactly on the harvest date
    tillage_event_data = create_tillage_event_data()
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    tillage_event.occurred_at = datetime(2021, 10, 1)

    cc = CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=7,
            crop_event_id=None,
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[completed_cropping_event, tillage_event],
        no_practice_observations=NoPracticeObservations(),
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=date(2022, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await evaluate_cycle_completion_for_tillage_stage(app_request, cc, ec, vs)
    assert result.valid
    assert result.validation_errors is not None
    for err in result.validation_errors:
        assert TILLAGE_CROPPING_OVERLAP_ERROR_MSG not in [e.error_message for e in err.errors]
