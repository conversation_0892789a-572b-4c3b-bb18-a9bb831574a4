import sqlalchemy as sa

from db.mysql import Base
from ui.projects.completion.enums import NarrowestCompletionStatus


class StageFieldCompletions(Base):
    """
    This table lists whether a field is completed (if this status is known),
    and additionally the numbers of cycles, both completed and in total.
    This table is currently used for caching and shouldn't be used in reporting
    """

    __tablename__ = "mrv_field_stage_completion"
    __table_args__ = (
        sa.UniqueConstraint(
            "stage_id",
            "field_id",
            name="_field_stage_completion_unique_field_stage",
        ),
    )
    id = sa.Column(sa.Integer, nullable=False, primary_key=True, index=True)
    # ideally this should be a foreign key to mrv_stages.id but we're not doing that
    # to avoid migrations failing in deployments due to excessive locking
    stage_id = sa.Column(sa.Integer, nullable=False, index=True)
    # ditto for "mrv_fields.id"
    field_id = sa.Column(sa.Integer, nullable=False, index=True)
    field_completion_status = sa.Column(sa.Enum(NarrowestCompletionStatus), nullable=False, index=False)
    completed_cycles = sa.Column(sa.Integer, nullable=True, index=False)
    total_cycles = sa.Column(sa.Integer, nullable=True, index=False)
    validation_errors = sa.Column(
        sa.JSON, nullable=True, index=False
    )  # this is a JSON object with rule names as keys and error messages as values
    # this is used to store the validation errors for the stage completion
