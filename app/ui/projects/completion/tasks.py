from datetime import datetime

import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from fields.db import get_fields_id_to_md5_mapping
from logger import get_logger
from phases.db import get_stage_by_id
from ses_integration.methods import poll_for_field_change
from ui.projects.completion.annotations import StageCompletionIdentifierLookup
from ui.projects.completion.dataclasses import CalculationContext
from ui.projects.completion.handle_bulk_field_completion_updates import (
    handle_multiple_fields_changing_completion,
    prepare_completion_as_necessary,
    update_completion_for_projects_in_shared_program,
)
from ui.projects.completion.handle_field_completion_change import (
    handle_updating_stage_completions_for_field,
)
from ui.projects.completion.handle_project_stage_updates import (
    handle_updating_stage_completions_for_project,
)

logger = get_logger(__name__)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_completion_update_for_projects_in_shared_program(
    self: DBTask,
    *,
    project_ids: list[int],
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    """
    The method employed by this task assumes all of the given project ids are in the same program
    """
    if not project_ids:
        return
    await update_completion_for_projects_in_shared_program(request, project_ids)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_field_completion_change_handler(
    self: DBTask,
    *,
    phase_id: int,
    field_id: int,
    stage_completion_identifier_lookup: StageCompletionIdentifierLookup,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await handle_updating_stage_completions_for_field(request, phase_id, field_id, stage_completion_identifier_lookup)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_stage_completion_change_handler(
    self: DBTask,
    *,
    stage_id: int,
    project_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    stage = await get_stage_by_id(request, stage_id)
    if stage is None:
        logger.error("Failed to find stage when running stage completion change handler", extra={"stage_id": stage_id})
    else:
        calculation_context = CalculationContext(
            should_update_field_level=False, is_inside_task=True, should_evaluate_field_level=True
        )
        await handle_updating_stage_completions_for_project(request, stage, project_id, calculation_context)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_completion_preparation_as_necessary(
    self: DBTask,
    *,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await prepare_completion_as_necessary(request)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def run_completion_updates_on_project_fields(
    self: DBTask,
    *,
    project_id: int,
    field_ids: list[int],
    from_time: datetime,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    field_id_to_md5_mapping = await get_fields_id_to_md5_mapping(request, field_ids)
    field_md5s = list(field_id_to_md5_mapping.values())
    await poll_for_field_change(from_time, field_md5s)
    await handle_multiple_fields_changing_completion(request, project_id, field_ids)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def update_completion_for_projects_in_shared_program_task(
    self: DBTask,
    *,
    project_ids: list[int],
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await update_completion_for_projects_in_shared_program(request=request, project_ids=project_ids)
