from dataclasses import dataclass
from datetime import date
from typing import Optional

from entity_events.events.entity_event import EventIdType
from fields.schema import Field
from phases.dataclasses import DateRange
from phases.enums import StageTypes
from phases.model import Phases
from programs.model import Programs
from ui.projects.completion.enums import NarrowestCompletionStatus


@dataclass
class EvaluationContext:
    """
    This contains information we need to send along with validation rule evaluation
    """

    program: Programs
    field: Field
    phase: Phases
    completion_end_date: date


@dataclass
class CalculationContext:
    """
    This contains information we need to send along with validation rule evaluation
    """

    # update the field and trigger-project level follow-ups
    should_update_field_level: bool
    # avoid spawning new tasks, but instead handle work in-line
    is_inside_task: bool
    # reevaluate field-level completion if determined but don't follow-up
    should_evaluate_field_level: bool


@dataclass(eq=False)
class ValidationResultsError:
    error_key: str
    error_message: str


@dataclass
class ValidationResults:
    event_id: EventIdType
    errors: list[ValidationResultsError] | None
    error_messages: list[str] | None
    stage_type: StageTypes | str | None
    error_keys: list[str] | None

    def unpack_to_dict(self) -> dict:
        if not self.errors:
            self.errors = [
                ValidationResultsError(error_key=error_key, error_message=error_message)
                for error_key, error_message in zip(self.error_keys or [], self.error_messages or [])
            ]
        # Remove duplicates if any, order not preserved
        filtered_errors = list[ValidationResultsError]()
        keys: set[str] = set[str]()
        for er in self.errors:
            if er.error_key and er.error_key not in keys:
                filtered_errors.append(er)
                keys.add(er.error_key)

        return {
            "event_id": self.event_id,
            "errors": filtered_errors,
            "stage_type": self.stage_type if self.stage_type else "UNKNOWN",
        }


def to_validation_results(inputs: dict) -> ValidationResults | None:
    if not inputs:
        return None

    if inputs.get("errors"):
        inputs["errors"] = [
            ValidationResultsError(error_key=er.get("error_key"), error_message=er.get("error_message"))
            for er in inputs.get("errors", [])
        ]
    elif not inputs.get("errors") and inputs.get("error_keys") and inputs.get("error_messages"):
        inputs["errors"] = [
            ValidationResultsError(error_key=error_key, error_message=error_message)
            for error_key, error_message in zip(inputs.get("error_keys", []), inputs.get("error_messages", []))
        ]
    return ValidationResults(
        event_id=inputs["event_id"],
        errors=inputs["errors"] if inputs.get("errors") else None,
        error_messages=None,
        error_keys=None,
        stage_type=StageTypes(inputs["stage_type"]) if inputs.get("stage_type") else "UNKNOWN",
    )


@dataclass
class CompletionForFieldsValidation:
    valid: bool
    validation_errors: list[ValidationResults] | None = None

    def unpack_to_dict(self) -> dict:
        if self.validation_errors:
            error_list = [ve.unpack_to_dict() for ve in self.validation_errors]
        else:
            error_list = []
        return {"valid": self.valid, "validation_errors": error_list}


@dataclass
class CycleCompletionValidationResult:
    valid: bool
    validation_errors: list[ValidationResults] | None = None

    def unpack_to_dict(self) -> dict:
        if self.validation_errors:
            error_list = [ve.unpack_to_dict() for ve in self.validation_errors]
        else:
            error_list = []
        return {"valid": self.valid, "validation_errors": error_list}


@dataclass
class StageCompletionResult:
    completion_status: NarrowestCompletionStatus
    completed_cycle_count: Optional[int]
    total_cycle_count: Optional[int]
    validation_errors: list[ValidationResults] | None = None


@dataclass
class StageFieldCompletion:
    stage_id: int
    field_id: int
    completion_result: StageCompletionResult


@dataclass
class CompletionEvaluationResult:
    """
    These are the results of evaluating completion in a cycle
    """

    passed_evaluation: bool
    failed_rules: set[str]
    unevaluated_rules: set[str]
    cultivation_cycle_date_range: Optional[DateRange]

    def get_debugging_string(self) -> str:
        return f"passed_evaluation: {self.passed_evaluation}, failed_rules: {self.failed_rules}, unevaluated_rules: {self.unevaluated_rules}"


@dataclass
class ValidationAllowances:
    """
    Sometimes, we want validation to succeed despite a given validation rule not passing;
    for example, cover crops are allowed to have an unevaluated check for yield being a positive value
    """

    allowed_failed_rules: set[str]
    allowed_unevaluated_rules: set[str]
