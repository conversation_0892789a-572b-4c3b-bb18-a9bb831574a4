import datetime
from unittest.mock import patch

from starlette.requests import Request

import entity_events
from fields.enums import FieldStatus
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from ui.projects.completion.dataclasses import EvaluationContext
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
)


async def test_generate_validation_specification_for_program(app_request, mdl):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))

    # this program deals with crops that take a long time to grow
    async def mock_disallowed_checks(request: Request, program_id: int):
        return {"A cropping event cannot be longer than 12 months."}

    with patch.object(
        entity_events.validation.validator.AttributeValuesValidator,
        "get_disabled_validation_rules_by_program_id",
        mock_disallowed_checks,
    ):
        vs = await generate_validation_specification_for_program(app_request, ec)
    assert (
        "Planting date must be before the harvest/termination date." in vs.required_event_level_checks["CroppingEvent"]
    )
    assert "A cropping event cannot be longer than 12 months." not in vs.required_event_level_checks["CroppingEvent"]
