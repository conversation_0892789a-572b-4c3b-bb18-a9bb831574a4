from starlette.requests import Request

from entity_events.events.enums import EntityEventType
from entity_events.validation.annotations import ValidationResultsResponse
from entity_events.validation.rules_scraper import validation_rules_scraper
from entity_events.validation.validator import Attribute<PERSON><PERSON>uesValidator
from logger import get_logger
from phases.enums import PhaseTypes
from ui.projects.completion.dataclasses import (
    CompletionEvaluationResult,
    EvaluationContext,
    ValidationAllowances,
)
from ui.projects.completion.specification.constants import (
    CROSS_EVENT_CHECKS_BY_EVENT_TYPE,
    NON_REQUIRED_CHECKS_BY_EVENT_TYPE,
)

logger = get_logger(__name__)


# interfaces


class ValidationSpecification:
    def __init__(self) -> None:
        self.required_event_level_checks: dict[str, set[str]] = {}

    def do_event_validation_results_meet_specification(
        self, event_type: str, validation_results: ValidationResultsResponse, specific_allowances: ValidationAllowances
    ) -> CompletionEvaluationResult:
        satisfied_tests = set()
        for attrib, vr_for_attribs in validation_results.items():
            for test_result in vr_for_attribs:
                if isinstance(test_result, str):
                    missing_attribute_fail = f"{attrib} {test_result}"
                    if missing_attribute_fail not in specific_allowances.allowed_failed_rules:
                        return CompletionEvaluationResult(
                            passed_evaluation=False,
                            failed_rules={missing_attribute_fail},
                            unevaluated_rules=set(),
                            cultivation_cycle_date_range=None,
                        )
                for test, result in test_result.items():
                    match result:
                        case "passed":
                            satisfied_tests.add(test)
                        case "failed":
                            if test not in specific_allowances.allowed_failed_rules:
                                return CompletionEvaluationResult(
                                    passed_evaluation=False,
                                    failed_rules={test},
                                    unevaluated_rules=set(),
                                    cultivation_cycle_date_range=None,
                                )
                        case _:
                            raise ValueError(f"Unexpected validation result: {result}")
        unsatisfied_tests = (
            self.required_event_level_checks[event_type]
            - satisfied_tests
            - specific_allowances.allowed_unevaluated_rules
        )
        if unsatisfied_tests:
            # logger.info(f"event_type: {event_type}")
            # logger.info(f"unsatisfied_tests: {unsatisfied_tests}")
            return CompletionEvaluationResult(
                passed_evaluation=False,
                failed_rules=set(),
                unevaluated_rules=unsatisfied_tests,
                cultivation_cycle_date_range=None,
            )
        return CompletionEvaluationResult(
            passed_evaluation=True, failed_rules=set(), unevaluated_rules=set(), cultivation_cycle_date_range=None
        )


async def generate_validation_specification_for_program(
    request: Request, ec: EvaluationContext
) -> ValidationSpecification:
    validation_specification = ValidationSpecification()
    rules_by_event_type_and_attribute = await validation_rules_scraper.get_rules()
    if not rules_by_event_type_and_attribute:
        await validation_rules_scraper.run()
        rules_by_event_type_and_attribute = await validation_rules_scraper.get_rules()
    disabled_rules = await AttributeValuesValidator.get_disabled_validation_rules_by_program_id(
        request, ec.program.id
    ) | {
        "Harvest date year must match Year.",
        "Tillage date must match Year.",
    }

    event_types = list(rules_by_event_type_and_attribute.keys())
    for event_type in event_types:
        rules_by_attribute = rules_by_event_type_and_attribute[event_type]
        non_required_rules = (
            disabled_rules
            | CROSS_EVENT_CHECKS_BY_EVENT_TYPE.get(event_type, set())
            | NON_REQUIRED_CHECKS_BY_EVENT_TYPE.get(event_type, set())
            | _compute_context_specific_rule_exclusions(event_type, ec)
        )

        event_rules = set()
        for attribute in rules_by_attribute.keys():
            attribute_rules = rules_by_attribute[attribute]
            for rule in attribute_rules:
                msg = rule["msg"]
                if msg not in non_required_rules:
                    event_rules.add(msg)
        validation_specification.required_event_level_checks[event_type] = set(event_rules)

    return validation_specification


# internals


def _compute_context_specific_rule_exclusions(event_type: str, ec: EvaluationContext) -> set[str]:
    context_specific_exclusions: set[str] = set()
    if event_type == EntityEventType.CROPPING_EVENT:
        if ec.program.is_single_phase_data_collection is False and ec.phase.type_ == PhaseTypes.ENROLMENT:
            context_specific_exclusions.add("Crop yield value should be positive.")
    return context_specific_exclusions
