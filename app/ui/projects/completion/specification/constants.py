CROSS_EVENT_CHECKS_BY_EVENT_TYPE = {
    "CroppingEvent": {
        "Cropping events should not overlap other cropping events on this field. Intercropping is not supported at this time."
    },
    "TillageEvent": {
        "Cannot have No Till within the same year as a tillage event. Remove this row or the conflicting tillage event."
    },
}

NON_REQUIRED_CHECKS_BY_EVENT_TYPE = {
    "IrrigationEvent": {
        "Flood percentage should be in range between 0 and 100.",
        "Subsurface drip depth value should be positive.",
    },
    "ApplicationEvent": {
        "Additives must include at most one Nitrification Inhibitor and one Urease Inhibitor.",
        "Application depth value should be positive.",
        "Subsurface drip depth value should be positive.",
        "Must be Measure approved additives.",
        "Water amount value should be positive when application method 'Fertigation' is selected.",
        "Cannot have more than 2 additives.",
        "Additives must be unique. Can't select the same additive twice for the same event.",
        "Mass/weight units are required for dry product and nitrogen rates, and volume units are required for liquid product rates.",
        "Flood percentage should be in range between 0 and 100.",
        "Nitrogen rate cannot be used with liquid measurements",
        "Irrigation end date cannot be before start date.",
        "Irrigation intervals require a start datetime.",
        "Irrigation intervals require an end datetime.",
        "Irrigation cannot exceed 365 days.",
    },
}
