from enum import StrEnum


class NarrowestCompletionStatus(StrEnum):
    """
    This completion status is for items assessed at the narrowest scope over which a completion can apply,
    and thus is binary or undetermined,
    as compared to the completion of a project, which is a percentage of complete.
    """

    complete = "complete"
    incomplete = "incomplete"
    undetermined = "undetermined"


def find_weakest_narrow_completion_status(statuses: list[NarrowestCompletionStatus]) -> NarrowestCompletionStatus:
    """
    If there are multiple narrow completion status, return the one that indicates the least
    about the state of completion (undetermined over incomplete, and incomplete over complete)
    """
    if not statuses:
        raise AssertionError("Need at least one status to assess weakest")
    found_incomplete = False
    for status in statuses:
        if status == NarrowestCompletionStatus.undetermined:
            return NarrowestCompletionStatus.undetermined
        elif status == NarrowestCompletionStatus.incomplete:
            found_incomplete = True
    if found_incomplete:
        return NarrowestCompletionStatus.incomplete
    else:
        return NarrowestCompletionStatus.complete
