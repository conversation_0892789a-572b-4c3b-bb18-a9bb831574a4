from __future__ import annotations

import re

from fastapi import HTT<PERSON>Exception, Request, status

from cultivation_cycles.methods import get_cultivation_cycles
from cultivation_cycles.schema import CultivationCycleGenerationConfig
from entity_events.db import get_event_stages_for_project
from entity_events.events.entity_event import EntityEvent
from entity_events.helpers import get_event_type_by_phase_and_stage_type
from entity_events.methods import (
    get_entity_events_for_field,
    get_entity_events_for_project_phase,
)
from fields.db import get_field_by_id, get_fields_by_project_id
from fields.enums import FieldStatus
from fields.schema import Field
from logger import get_logger
from permissions.enums import Permission
from permissions.methods import get_original_user_id, user_has_permission_in_program
from phases.db import get_phase_by_id, get_phase_by_stage_id
from phases.enums import PhaseTypes, StageTypes
from phases.model import Phases, Stage
from phases.schema import StageRequestNoParentBase
from programs.db import get_program_by_project_id
from programs.model import Programs
from projects.utils import process_field_events
from ui.projects.field_events.schema import (
    CultivationCycleFromMRVValues,
    FieldEventFromMRVValues,
)
from values.enums import EntityTypeChoices

logger = get_logger(__name__)


def mrv_values_cultivation_cycles_to_ui_events(
    field_id_to_events: dict[int, list[EntityEvent]], stage_type: StageTypes, phase_type: PhaseTypes, stage_id: int
) -> dict[int, list[CultivationCycleFromMRVValues]]:
    """
    Use the associated field ID -> entity event map to construct cultivation cycles
    """
    response_dict: dict[int, list[CultivationCycleFromMRVValues]] = {}
    for field_id in field_id_to_events:
        response_dict[field_id] = []
        cult_cycles, _ = get_cultivation_cycles(
            events=field_id_to_events[field_id],
            generation_config=CultivationCycleGenerationConfig(stage_type=stage_type),
        )
        for cycle in cult_cycles:
            response_dict[field_id].append(
                CultivationCycleFromMRVValues(
                    id=f"{cycle.id}_{stage_id}",
                    start_date=cycle.start,
                    end_date=cycle.end,
                    events=[
                        FieldEventFromMRVValues.from_event(
                            event, get_event_type_by_phase_and_stage_type(stage_type, phase_type)
                        )
                        for event in cycle.events
                    ],
                )
            )

    return response_dict


async def get_project_field_events_from_mrv_values(
    request: Request,
    project_id: int,
    stage_id: int | None,
    field_id: int | None,
    event_type: str | None,
    phase_id: int | None,
) -> dict[int, list[FieldEventFromMRVValues]]:
    field_id_to_events: dict[int, list[FieldEventFromMRVValues]] = {}

    try:
        # retrieve all events for a phase
        if phase_id:
            phase = await get_phase_by_id(request, phase_id)
            events = await get_entity_events_for_project_phase(
                request, project_id, phase.type_, EntityTypeChoices.field
            )
            field_id_to_events = await process_field_events(events, None, field_id, event_type)
        # retrieve all events for a stage
        elif stage_id:
            stages: list[Stage] = await get_event_stages_for_project(request, project_id, stage_id)

            if not stages:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail={"message": "Stage ID %s not found" % stage_id}
                )

            stage = stages[0]
            phase = await get_phase_by_stage_id(request, stage.id)
            events = await get_entity_events_for_project_phase(
                request, project_id, phase.type_, EntityTypeChoices.field
            )
            field_id_to_events = await process_field_events(events, stage, field_id, event_type)
        # retrieve all field events
        elif stage_id is None and phase_id is None and field_id is not None:
            field = await get_field_by_id(request, field_id)
            if field is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={"message": f"Field ID {field_id} not found"},
                )
            events = [
                event
                for phase_events in (await get_entity_events_for_field(request, field)).values()
                for event in phase_events
            ]

            field_id_to_events = await process_field_events(events, None, field_id, event_type)

    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving entity events for project {project_id}: {str(ve)}"},
        )

    return field_id_to_events


async def get_project_program_phase_and_stage(
    request: Request, project_id: int, phase_id: int, stage_id: int | None = None
) -> tuple[Programs, Phases, Stage | None]:
    program: Programs = await get_program_by_project_id(request=request, project_id=project_id)
    if program is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"No program found for project_id {project_id}"
        )

    phase: Phases | None = next((phase for phase in program.phases if phase.id == phase_id), None)
    if phase is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid phase_id {phase_id} for project_id {project_id}"
        )

    # Verify and set stage_type if passed
    if stage_id is not None:
        stage: StageRequestNoParentBase | None = next((stage for stage in phase.stages if stage.id == stage_id), None)
        if stage is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid stage_id {stage_id} for phase_id {phase_id}"
            )
        return program, phase, stage

    return program, phase, None


async def get_field_with_parent_project(
    request: Request,
    project_id: int,
    field_id: int,
) -> Field | None:
    field = await get_field_by_id(request, field_id)
    if field is None or (field.parent_project_id != project_id):
        logger.warning(f"Field id {field_id} is not a member of project id {project_id}")
        return None

    return field


async def get_project_fields_by_optional_id(request: Request, project_id: int, field_id: int | None) -> list[Field]:
    if field_id is None:
        fields = await get_fields_by_project_id(
            request=request, project_id=project_id, has_statuses={FieldStatus.enrolled, FieldStatus.registered}
        )
    else:
        field = await get_field_with_parent_project(request=request, project_id=project_id, field_id=field_id)
        if field is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Field_id {field_id} not found for project_id {project_id}",
            )
        fields = [field]
    return fields


async def can_bypass_event_lock(request: Request, program_id: int) -> bool:
    """
    Check if the original user has permission to bypass the lock on project field events
    """
    user_id = get_original_user_id(request)
    return await user_has_permission_in_program(
        request=request,
        user_id=user_id,
        program_id=program_id,
        permission=Permission.BYPASS_PROJECT_FIELD_EVENTS_LOCK,
    )


def extract_acting_user_id(request: Request) -> str:
    acting_user_id = request.headers.get("fs-user-id")
    if not acting_user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No user ID in request.")
    # the acting_user_id should contain an integer that corresponds to a user ID,
    # or a program ID if the request originates from a program migration
    if not re.search(r"\d+", acting_user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid user ID {acting_user_id} in request."
        )
    return str(acting_user_id)
