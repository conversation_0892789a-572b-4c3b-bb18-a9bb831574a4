from datetime import datetime
from typing import Callable, Final

from entity_events.event_creators.base_event_creator import base_create_cropping_event
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent

# TODO: deprecate depdenency on facade event creators in favour of from_ui_dict class methods
FROM_UI_DICT_EVENT_CREATOR_TYPE_LOOKUP: dict[EntityEventType, Callable[..., EntityEvent]] = {
    CroppingEvent.__name__: base_create_cropping_event,
    FallowPeriod.__name__: base_create_cropping_event,
    TillageEvent.__name__: TillageEvent.from_ui_dict,
    ApplicationEvent.__name__: ApplicationEvent.from_ui_dict,
    IrrigationEvent.__name__: IrrigationEvent.from_ui_dict,
}

DEFAULT_REQUIRED_YEARS_OF_HISTORY = 3

DEFAULT_FIELD_EVENT_QUERY_FROM_DATE: Final[datetime] = datetime(2015, 1, 1)
