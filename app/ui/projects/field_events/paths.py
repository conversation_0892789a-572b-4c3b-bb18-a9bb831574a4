from phases.paths import base as phases_base, phase_id, stage_id
from projects.paths import field_id, fields, single_project

phase = phases_base + phase_id
ui_project_phase_base = single_project + phase
ui_project_phase_stage_base = single_project + phase + stage_id
ui_field_events_base = "/field_events"
field = fields + field_id
field_events = field + "/events"
field_event = field_events + "/{event_id}"
fields_cultivation_cycles = fields + "/cultivation_cycles"
field_cultivation_cycle = field + "/cultivation_cycles/{cultivation_cycle_id}"
field_events_meta = "/events/meta"
bulk_update_field_events = field + "/bulk_update"
