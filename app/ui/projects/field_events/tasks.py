import math
from typing import Any

import elasticapm
from celery import chain, group
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from entity_events.events.enums import EntityEventType
from helper.async_tools import batch_list
from phases.db import get_phase_from_program_id
from phases.enums import PhaseTypes
from programs.db import get_program
from projects.db import get_projects_by_program_id
from slack_integration.integration import post_message
from ui.projects.field_events.monitoring_phase_prefill import (
    prefill_monitoring_phase_commodity_crops_for_project,
)
from ui.projects.field_events.no_practice_observation_backfill import (
    backfill_no_practice_observations,
)

settings = get_settings()

BACKFILL_NO_PRACTICE_OBSERVATIONS_MAX_CONCURRENT_TASKS = 5


# PREFILL MONITORING PHASE COMMODITY CROPS
@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def prefill_monitoring_phase_commodity_crops_for_project_task(
    self: DBTask,
    *,
    program_id: int,
    project_id: int,
    intended_commodity_crops_stage_id: int,
    m_phase_id: int,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await prefill_monitoring_phase_commodity_crops_for_project(
            request=request,
            program_id=program_id,
            project_id=project_id,
            intended_commodity_crops_stage_id=intended_commodity_crops_stage_id,
            m_phase_id=m_phase_id,
            acting_user_id=str(fs_user_id),
        )
    except Exception as e:
        await post_message(
            slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
            message=f":x: M phase commodity crops and tillage no practice observations could not be prefilled for program {program_id}, project {project_id}: {e}",
        )
        raise e


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def complete_prefill_monitoring_phase_commodity_crops_task(
    self: DBTask,
    previous_task_outputs: Any,
    *,
    program_id: int,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
        message=f":white_check_mark: M phase commodity crops and tillage no practice observations prefilled for program {program_id}.",
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def prefill_monitoring_phase_commodity_crops_task(
    self: DBTask,
    *,
    program_id: int,
    intended_commodity_crops_stage_id: int,
    m_phase_id: int,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    projects = await get_projects_by_program_id(request=request, program_id=program_id)
    project_ids = [project.id for project in projects]
    chain(
        group(
            prefill_monitoring_phase_commodity_crops_for_project_task.s(
                program_id=program_id,
                project_id=project_id,
                intended_commodity_crops_stage_id=intended_commodity_crops_stage_id,
                m_phase_id=m_phase_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            )
            for project_id in project_ids
        ),
        complete_prefill_monitoring_phase_commodity_crops_task.s(
            program_id=program_id,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ).delay()


# BACKFILL NO PRACTICE OBSERVATIONS
@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def backfill_no_practice_observations_for_projects_task(
    self: DBTask,
    *,
    project_ids: list[int],
    no_practice_observation_type: EntityEventType,
    enrollment_phase_only: bool,
    is_single_phase_data_collection: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await backfill_no_practice_observations(
            request=request,
            project_ids=project_ids,
            no_practice_observation_type=no_practice_observation_type,
            enrollment_phase_only=enrollment_phase_only,
            is_single_phase_data_collection=is_single_phase_data_collection,
            acting_user_id=str(fs_user_id),
        )
    except Exception as e:
        await post_message(
            slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
            message=f":x: {no_practice_observation_type} no practice observations could not be backfilled for projects {project_ids}: {e}",
        )
        raise e


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def complete_backfill_no_practice_observations_task(
    self: DBTask,
    previous_task_outputs: Any,
    *,
    program_id: int,
    no_practice_observation_type: EntityEventType,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
        message=f":white_check_mark: {no_practice_observation_type} no practice observations backfilled for program {program_id}.",
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def backfill_no_practice_observations_task(
    self: DBTask,
    *,
    program_id: int,
    project_ids: list[int] | None,
    no_practice_observation_type: EntityEventType,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    program = await get_program(request=request, program_id=program_id)
    m_phase = await get_phase_from_program_id(request=request, program_id=program_id, type=PhaseTypes.MONITORING)
    if not m_phase or not m_phase.enabled:
        enrollment_phase_only = True
    else:
        enrollment_phase_only = False

    if not project_ids:
        projects = await get_projects_by_program_id(request=request, program_id=program_id)
        project_ids = [project.id for project in projects]

    batch_size = math.ceil(len(project_ids) / BACKFILL_NO_PRACTICE_OBSERVATIONS_MAX_CONCURRENT_TASKS)
    chain(
        group(
            backfill_no_practice_observations_for_projects_task.s(
                project_ids=project_id_batch,
                no_practice_observation_type=no_practice_observation_type,
                enrollment_phase_only=enrollment_phase_only,
                is_single_phase_data_collection=program.is_single_phase_data_collection,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            )
            for project_id_batch in batch_list(project_ids, batch_size)
        ),
        complete_backfill_no_practice_observations_task.s(
            program_id=program_id,
            no_practice_observation_type=no_practice_observation_type,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ).delay()
