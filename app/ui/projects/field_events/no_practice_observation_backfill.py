from fastapi import Request
from ses_client.client import Client

from config import get_settings
from domain_event_bus.domain_event_bus import event_bus
from domain_event_bus.domain_events import BulkProjectEventsChanged
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from fields.db import get_fields_orm_by_project_ids
from fields.schema import Field
from logger import get_logger
from projects.db import get_owner_user_ids_for_projects
from ses_integration.fetch_entity_events import fetch_events_for_field_phase
from ses_integration.no_practice_observations import (
    update_no_practice_observations_for_event,
)

logger = get_logger(__name__)
settings = get_settings()
ses_client = Client(settings.SES_INTERNAL_URL_BASE, settings.SES_SEARCH_INTERNAL_URL_BASE)


NO_PRACTICE_OBSERVATION_TYPE_TO_EVENT_TYPE = {
    EntityEventType.TILLAGE_EVENT: TillageEvent,
    EntityEventType.APPLICATION_EVENT: ApplicationEvent,
    EntityEventType.IRRIGATION_EVENT: IrrigationEvent,
}


async def backfill_no_practice_observations(
    request: Request,
    project_ids: list[int],
    no_practice_observation_type: EntityEventType,
    enrollment_phase_only: bool,
    is_single_phase_data_collection: bool,
    acting_user_id: str,
) -> None:
    no_practice_obs_event_type = NO_PRACTICE_OBSERVATION_TYPE_TO_EVENT_TYPE[no_practice_observation_type]
    project_to_owner_user_id = await get_owner_user_ids_for_projects(request=request, project_ids=project_ids)
    fields = await get_fields_orm_by_project_ids(request=request, project_ids=project_ids)
    for field in fields:
        owner_user_id = project_to_owner_user_id.get(field.parent_project_id)
        if not owner_user_id:
            logger.warning(f"Field belongs to project {field.parent_project_id} that no longer exists.")
            continue
        try:
            events = await fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=enrollment_phase_only,
                is_single_phase_program=is_single_phase_data_collection,
            )
        except Exception as e:
            logger.warning(f"Could not fetch events for field {field.id}: {e}")
            continue
        has_no_practice_obs_event = False
        crop_event_ids = []
        for event in events:
            if isinstance(event, no_practice_obs_event_type):
                has_no_practice_obs_event = True
                break
            if isinstance(event.id, CroppingIDs):
                crop_event_id = (
                    event.id.harvesting_id if event.id.is_non_fallow_cropping_event() else event.id.fallow_id
                )
                if crop_event_id:
                    crop_event_ids.append(crop_event_id)
        if not has_no_practice_obs_event:
            for crop_event_id in crop_event_ids:
                await update_no_practice_observations_for_event(
                    event_id=str(crop_event_id),
                    no_practice_observations={no_practice_observation_type: True},
                    field=Field.from_orm(field),
                    acting_user_id=acting_user_id,
                    owner_user_id=owner_user_id,
                    ses_client=ses_client,
                )
    await event_bus.publish(
        event=BulkProjectEventsChanged(project_ids=project_ids),
        request=request,
    )
