from datetime import datetime, timedelta, timezone

from entity_events.events.entity_event import Entity<PERSON>vent
from entity_events.events.enums import EntityEventType
from logger import get_logger

logger = get_logger(__name__)

# Configuration map for hypothetical date generation by event type (string-based)
EVENT_TYPE_DAY_OFFSETS: dict[EntityEventType, int] = {
    EntityEventType.CROPPING_EVENT: 180,
    EntityEventType.FALLOW_PERIOD: 180,
    EntityEventType.IRRIGATION_EVENT: 30,
}


def _event_type_name(event: EntityEvent) -> str:
    """Get the event type name as string."""
    return event.__class__.__name__


def get_hypothetical_interval(event: EntityEvent) -> tuple[datetime, datetime]:
    """
    Generate hypothetical start/end dates for events with missing intervals.

    Rules:
    - For cropping/fallow events: +/-180 days within same year
    - For irrigation events: +/-30 days within same year
    - If both start and end exist → return as-is
    - If start only → set hypothetical end date
    - If end only → set hypothetical start date

    Args:
        event: EntityEvent with potentially partial interval

    Returns:
        Tuple of (start_date, end_date) with hypothetical dates filled in

    Raises:
        ValueError: If event has no interval, unsupported event type, or both start and end are None
    """
    # Check if event type is supported
    event_type_name = _event_type_name(event)
    if event_type_name not in EVENT_TYPE_DAY_OFFSETS:
        raise ValueError(f"Unsupported event type for hypothetical interval generation: {event_type_name}")

    # Check if event has an interval
    if not hasattr(event, "interval") or not event.interval:
        raise ValueError("Event must have an interval")

    # If both start and end exist, return as-is
    if event.interval.start and event.interval.end:
        return event.interval.start, event.interval.end

    # Get the day offset for this event type
    day_offset = EVENT_TYPE_DAY_OFFSETS[event_type_name]

    if event.interval.start:
        # Start only → hypothetical end +N days
        start = event.interval.start
        end = start + timedelta(days=day_offset)
        # Ensure end is within same year
        if end.year != start.year:
            end = datetime(start.year, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        return start, end

    if event.interval.end:
        # End only → hypothetical start -N days
        end = event.interval.end
        start = end - timedelta(days=day_offset)
        # Ensure start is within same year
        if start.year != end.year:
            start = datetime(end.year, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
        return start, end

    # Both start and end are None
    raise ValueError("Event must have at least start or end date")


def do_events_overlap(source_event: EntityEvent, target_event: EntityEvent) -> bool:
    """
    Check if two events overlap based on their types and intervals.

    Rules:
    - Same event types can overlap (except for mixed cropping/fallow)
    - Cropping and fallow events can overlap with each other (agricultural cycles)
    - Interval events (cropping, fallow, irrigation): Compare periods with hypothetical dates
    - Practice events (tillage, application, fire): Compare occurred_at dates for exact match
    - Different incompatible event types: No overlap

    Args:
        source_event: Event being copied
        target_event: Existing event in target field

    Returns:
        True if events overlap and target should be overwritten, False otherwise
    """
    source_type_name = _event_type_name(source_event)
    target_type_name = _event_type_name(target_event)

    # Check if events can overlap based on their types
    if source_type_name != target_type_name and not (
        source_type_name in (EntityEventType.CROPPING_EVENT, EntityEventType.FALLOW_PERIOD)
        and target_type_name in (EntityEventType.CROPPING_EVENT, EntityEventType.FALLOW_PERIOD)
    ):
        # Different incompatible event types cannot overlap
        return False

    # interval-based events (cropping, fallow, irrigation)
    if source_type_name in (
        EntityEventType.CROPPING_EVENT,
        EntityEventType.FALLOW_PERIOD,
        EntityEventType.IRRIGATION_EVENT,
    ):
        try:
            source_start, source_end = get_hypothetical_interval(source_event)
            target_start, target_end = get_hypothetical_interval(target_event)
            return not (source_end <= target_start or target_end <= source_start)
        except ValueError as e:
            logger.warning("Error calculating interval event overlap: %s", e)
            return False

    # other practice events (same date comparison)
    else:
        source_date = source_event.occurred_at.date()
        target_date = target_event.occurred_at.date()
        return source_date == target_date
