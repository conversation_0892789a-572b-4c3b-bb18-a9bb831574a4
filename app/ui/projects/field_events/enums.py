import enum


class CopyFieldEventsMode(enum.StrEnum):
    COPY = "COPY"  # Copy events, existing events in destination field will be kept, adding new events to the field. Skipping locked field
    OVERWRITE = "OVERWRITE"  # Copy events, existing events in destination field will be deleted, then new events will be copied over. Skipping locked field
    OVERWRITE_LOCKED = "OVERWRITE_LOCKED"  # Copy events, existing events in destination field will be overwritten, even if they are locked
    OVERWRITE_OVERLAPPING = "OVERWRITE_OVERLAPPING"  # Copy events, overwrite only overlapping events in target field
