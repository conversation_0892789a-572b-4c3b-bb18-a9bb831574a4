import json
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest
import pytz
from fastapi import HTTPException
from regrow.ses.context.v1.context_pb2 import EventContext
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextResponse
from regrow.ses.fallow.v1.fallow_pb2 import FallowPeriod
from regrow.ses.harvest.v1.harvest_pb2 import HarvestActivity
from regrow.ses.tillage.v1.tillage_pb2 import TillageActivity
from ses_client.client import Client as SesClient
from ses_client.event import StructuredEvent

from cultivation_cycles.schema import CULTIVATION_CYCLE_DATETIME_FORMAT
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.enums import EntityEventType
from phases.enums import StageTypes
from ui.projects.field_events.router import create_update_no_practice_observation
from ui.projects.field_events.schema import (
    CultivationCycleResponse,
    CultivationCycleResponseId,
    NoPracticeObservationRequest,
    NoPracticeObservations,
)


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_create_no_practice_observation(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    test_event_id = uuid.uuid4()
    test_event = Event(id=str(test_event_id), harvest_activity=HarvestActivity())
    test_context = EventContext()
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=test_event, context=test_context)
    mock_fetch_events_for_fields.return_value = {}

    cultivation_cycle_id = {
        "crop_event_id": {
            "planting_id": str(uuid.uuid4()),
            "harvesting_id": str(test_event_id),
        },
        "crop_type": "corn",
        "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "harvest_year": 2024,
        "phase_id": phase.id,
        "stage_id": stage.id,
    }

    cultivation_cycles_response = await create_update_no_practice_observation(
        request=app_request,
        project_id=project.id,
        phase_id=phase.id,
        stage_id=stage.id,
        field_id=field.id,
        cultivation_cycle_id=json.dumps(cultivation_cycle_id),
        body=NoPracticeObservationRequest(
            no_practice_observation_value=True,
        ),
    )
    expected_cultivation_cycles_response = CultivationCycleResponse(
        id=CultivationCycleResponseId.parse_obj(cultivation_cycle_id),
        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
        end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC),
        events=[],
        no_practice_observations=NoPracticeObservations(
            tillage_event=True, application_event=False, irrigation_event=False
        ),
    )
    assert cultivation_cycles_response == expected_cultivation_cycles_response

    mock_add_field_event_context.assert_called_with(
        field_id=field.md5,
        event_id=str(test_event_id),
        context=EventContext(
            association={
                StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps({EntityEventType.TILLAGE_EVENT: True}),
                CONTEXT_KEY_REGROW_OWNING_USER: "7",
                CONTEXT_KEY_REGROW_ACTING_USER: "1",
            }
        ),
    )


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_update_no_practice_observation(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    test_event_id = uuid.uuid4()
    test_event = Event(id=str(test_event_id), harvest_activity=HarvestActivity())
    test_context = EventContext(
        association={
            StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                {
                    EntityEventType.TILLAGE_EVENT: False,
                    EntityEventType.APPLICATION_EVENT: True,
                    EntityEventType.IRRIGATION_EVENT: True,
                }
            ),
            CONTEXT_KEY_REGROW_OWNING_USER: "7",
            CONTEXT_KEY_REGROW_ACTING_USER: "1",
        }
    )
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=test_event, context=test_context)
    mock_fetch_events_for_fields.return_value = {}

    cultivation_cycle_id = {
        "crop_event_id": {
            "planting_id": str(uuid.uuid4()),
            "harvesting_id": str(test_event_id),
        },
        "crop_type": "corn",
        "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "harvest_year": 2024,
        "phase_id": phase.id,
        "stage_id": stage.id,
    }
    cultivation_cycles_response = await create_update_no_practice_observation(
        request=app_request,
        project_id=project.id,
        phase_id=phase.id,
        stage_id=stage.id,
        field_id=field.id,
        cultivation_cycle_id=json.dumps(cultivation_cycle_id),
        body=NoPracticeObservationRequest(
            no_practice_observation_value=True,
        ),
    )
    expected_cultivation_cycles_response = CultivationCycleResponse(
        id=CultivationCycleResponseId.parse_obj(cultivation_cycle_id),
        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.utc),
        end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.utc),
        events=[],
        no_practice_observations=NoPracticeObservations(
            tillage_event=True, application_event=True, irrigation_event=True
        ),
    )
    assert cultivation_cycles_response == expected_cultivation_cycles_response

    mock_add_field_event_context.assert_called_with(
        field_id=field.md5,
        event_id=str(test_event_id),
        context=EventContext(
            association={
                StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                    {
                        EntityEventType.TILLAGE_EVENT: True,
                        EntityEventType.APPLICATION_EVENT: True,
                        EntityEventType.IRRIGATION_EVENT: True,
                    }
                ),
                CONTEXT_KEY_REGROW_OWNING_USER: "7",
                CONTEXT_KEY_REGROW_ACTING_USER: "1",
            }
        ),
    )


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_create_no_practice_observation_fallow_period(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    test_event_id = uuid.uuid4()
    test_event = Event(id=str(test_event_id), fallow_period=FallowPeriod())
    test_context = EventContext()
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=test_event, context=test_context)
    mock_fetch_events_for_fields.return_value = {}

    cultivation_cycle_id = {
        "crop_event_id": {
            "fallow_id": str(test_event_id),
        },
        "crop_type": "corn",
        "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
        "harvest_year": 2024,
        "phase_id": phase.id,
        "stage_id": stage.id,
    }
    cultivation_cycles_response = await create_update_no_practice_observation(
        request=app_request,
        project_id=project.id,
        phase_id=phase.id,
        stage_id=stage.id,
        field_id=field.id,
        cultivation_cycle_id=json.dumps(cultivation_cycle_id),
        body=NoPracticeObservationRequest(
            no_practice_observation_value=True,
        ),
    )
    expected_cultivation_cycles_response = CultivationCycleResponse(
        id=CultivationCycleResponseId.parse_obj(cultivation_cycle_id),
        start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
        end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC),
        events=[],
        no_practice_observations=NoPracticeObservations(
            tillage_event=True, application_event=False, irrigation_event=False
        ),
    )
    assert cultivation_cycles_response == expected_cultivation_cycles_response

    mock_add_field_event_context.assert_called_with(
        field_id=field.md5,
        event_id=str(test_event_id),
        context=EventContext(
            association={
                StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps({EntityEventType.TILLAGE_EVENT: True}),
                CONTEXT_KEY_REGROW_OWNING_USER: "7",
                CONTEXT_KEY_REGROW_ACTING_USER: "1",
            },
        ),
    )


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_create_update_no_practice_observation_empty_cultivation_cycle(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    with pytest.raises(HTTPException) as exc_info:
        await create_update_no_practice_observation(
            request=app_request,
            project_id=project.id,
            phase_id=phase.id,
            stage_id=stage.id,
            field_id=field.id,
            cultivation_cycle_id=json.dumps(
                {
                    "crop_event_id": None,
                    "crop_type": None,
                    "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "phase_id": phase.id,
                    "stage_id": stage.id,
                }
            ),
            body=NoPracticeObservationRequest(
                no_practice_observation_value=True,
            ),
        )
    assert exc_info.value.status_code == 400
    assert (
        exc_info.value.detail
        == "NoPracticeObservation cannot be set on a cultivation cycle not associated with a commodity crop or fallow period"
    )

    mock_add_field_event_context.assert_not_called()


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_create_update_no_practice_observation_bad_event_type(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    test_event_id = uuid.uuid4()
    # non-harvest event
    test_event = Event(id=str(test_event_id), tillage_activity=TillageActivity())
    test_context = EventContext()
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=test_event, context=test_context)
    mock_fetch_events_for_fields.return_value = {}

    with pytest.raises(HTTPException) as exc_info:
        await create_update_no_practice_observation(
            request=app_request,
            project_id=project.id,
            phase_id=phase.id,
            stage_id=stage.id,
            field_id=field.id,
            cultivation_cycle_id=json.dumps(
                {
                    "crop_event_id": {
                        "planting_id": str(uuid.uuid4()),
                        "harvesting_id": str(test_event_id),
                    },
                    "crop_type": "corn",
                    "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "phase_id": phase.id,
                    "stage_id": stage.id,
                }
            ),
            body=NoPracticeObservationRequest(
                no_practice_observation_value=True,
            ),
        )
    assert exc_info.value.status_code == 400
    assert (
        exc_info.value.detail
        == f"NoPracticeObservation cannot be set on event {test_event_id} of type {StructuredEvent.TYPE_TILLAGE_ACTIVITY}"
    )

    mock_add_field_event_context.assert_not_called()


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_events_for_fields")
@patch.object(SesClient, "fetch_event_with_context")
async def test_create_update_no_practice_observation_conflicting_events(
    mock_fetch_event_with_context, mock_fetch_events_for_fields, mock_add_field_event_context, app_request, mdl
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id)
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    field = await mdl.Fields(parent_project_id=project.id)

    test_event_id = uuid.uuid4()
    test_event = Event(id=str(test_event_id), harvest_activity=HarvestActivity())
    test_context = EventContext()
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=test_event, context=test_context)
    # conflicting event
    mock_fetch_events_for_fields.return_value = {
        str(field.md5): [Event(id=str(uuid.uuid4()), tillage_activity=TillageActivity())]
    }

    with pytest.raises(HTTPException) as exc_info:
        await create_update_no_practice_observation(
            request=app_request,
            project_id=project.id,
            phase_id=phase.id,
            stage_id=stage.id,
            field_id=field.id,
            cultivation_cycle_id=json.dumps(
                {
                    "crop_event_id": {
                        "planting_id": str(uuid.uuid4()),
                        "harvesting_id": str(test_event_id),
                    },
                    "crop_type": "corn",
                    "start_date": datetime(2024, 1, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "end_date": datetime(2024, 6, 1, 0, 0, 0).strftime(CULTIVATION_CYCLE_DATETIME_FORMAT),
                    "phase_id": phase.id,
                    "stage_id": stage.id,
                }
            ),
            body=NoPracticeObservationRequest(
                no_practice_observation_value=True,
            ),
        )
    assert exc_info.value.status_code == 400
    assert (
        exc_info.value.detail
        == f"NoPracticeObservation cannot be set because there are {EntityEventType.TILLAGE_EVENT} events in the cultivation cycle"
    )

    mock_add_field_event_context.assert_not_called()
