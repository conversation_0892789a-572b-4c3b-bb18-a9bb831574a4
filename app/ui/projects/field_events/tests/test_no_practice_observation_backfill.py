import json
import uuid
from datetime import datetime
from unittest.mock import call, patch

from regrow.ses.context.v1.context_pb2 import EventContext
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextResponse
from regrow.ses.harvest.v1.harvest_pb2 import HarvestActivity
from ses_client.client import Client
from ses_client.event import StructuredEvent

from defaults.attribute_options import CropUsage
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from ui.projects.field_events.no_practice_observation_backfill import (
    backfill_no_practice_observations,
)


@patch("ui.projects.field_events.no_practice_observation_backfill.fetch_events_for_field_phase")
@patch.object(Client, "add_field_event_context")
@patch.object(Client, "fetch_event_with_context")
async def test_backfill_no_practice_observations(
    mock_fetch_event_with_context,
    mock_add_field_event_context,
    mock_fetch_events_for_field_phase,
    mdl,
    app_request,
    create_cropping_event_data,
    create_irrigation_event_data,
    create_tillage_event_data,
    create_interval_data,
):
    program = await mdl.Programs()

    project_1 = await mdl.Projects(program_id=program.id)
    user_1 = await mdl.Users()
    await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)
    field_1 = await mdl.Fields(parent_project_id=project_1.id)

    project_2 = await mdl.Projects(program_id=program.id)
    user_2 = await mdl.Users()
    await mdl.ProjectPermissions(project=project_2.id, user=user_2.id)
    field_2 = await mdl.Fields(parent_project_id=project_2.id)

    harvesting_id_1 = uuid.uuid4()
    harvesting_id_2 = uuid.uuid4()
    harvesting_id_3 = uuid.uuid4()

    mock_fetch_events_for_field_phase.side_effect = [
        # field with irrigation event
        [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    id=CroppingIDs(harvesting_id=harvesting_id_1),
                    interval=create_interval_data(start=datetime(2025, 1, 1), end=datetime(2025, 6, 1)),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    id=CroppingIDs(harvesting_id=harvesting_id_2),
                    interval=create_interval_data(start=datetime(2024, 1, 1), end=datetime(2024, 6, 1)),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
            IrrigationEvent.parse_obj(
                create_irrigation_event_data(
                    interval=create_interval_data(start=datetime(2024, 1, 1), end=datetime(2024, 1, 10)),
                )
            ),
        ],
        # field without irrigation event
        [
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    id=CroppingIDs(harvesting_id=harvesting_id_3),
                    interval=create_interval_data(start=datetime(2023, 1, 1), end=datetime(2023, 6, 1)),
                    crop_usage=CropUsage.COMMODITY,
                    reductions=[],
                )
            ),
            TillageEvent.parse_obj(create_tillage_event_data(occurred_at=datetime(2023, 1, 1))),
        ],
    ]

    def fake_fetch_event_with_context(event_id: str) -> FetchEventWithContextResponse:
        return FetchEventWithContextResponse(
            event=Event(id=str(event_id), harvest_activity=HarvestActivity()),
            context=EventContext(
                association={
                    StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                        {EntityEventType.APPLICATION_EVENT: True}
                    )
                }
            ),
        )

    mock_fetch_event_with_context.side_effect = fake_fetch_event_with_context
    mock_add_field_event_context.return_value = True

    await backfill_no_practice_observations(
        request=app_request,
        project_ids=[project_1.id, project_2.id],
        no_practice_observation_type=EntityEventType.IRRIGATION_EVENT,
        enrollment_phase_only=False,
        is_single_phase_data_collection=False,
        acting_user_id="1",
    )

    assert mock_fetch_events_for_field_phase.mock_calls == [
        call(request=app_request, field_id=field_1.id, enrollment_phase_only=False, is_single_phase_program=False),
        call(request=app_request, field_id=field_2.id, enrollment_phase_only=False, is_single_phase_program=False),
    ]
    assert mock_add_field_event_context.mock_calls == [
        call(
            field_id=field_2.md5,
            event_id=str(harvesting_id_3),
            context=EventContext(
                association={
                    StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                        {EntityEventType.APPLICATION_EVENT: True, EntityEventType.IRRIGATION_EVENT: True}
                    ),
                    CONTEXT_KEY_REGROW_ACTING_USER: "1",
                    CONTEXT_KEY_REGROW_OWNING_USER: str(user_2.id),
                }
            ),
        ),
    ]
