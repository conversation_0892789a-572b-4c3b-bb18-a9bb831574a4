from datetime import datetime, timezone
from unittest.mock import patch

from defaults.attribute_options import NoCropType
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.tillage_event import TillageEvent
from fields.enums import FieldStatus
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from ui.projects.field_events.router import (
    admin_bulk_create_update_field_events,
    bulk_create_update_field_events,
)
from ui.projects.field_events.schema import (
    AdminBulkUpdateFieldEventRequest,
    AdminBulkUpdateFieldEventsRequest,
    AdminBulkUpdateFieldResponse,
    FieldEvent,
)
from ui.projects.field_events.tests.mocks import event_interval
from values.enums import EntityTypeChoices

start = event_interval.start_time.ToDatetime()
end = event_interval.end_time.ToDatetime()


def create_successful_response(project_id, phase_id, field_id, field_event):
    return AdminBulkUpdateFieldResponse(
        project_id=project_id,
        phase_id=phase_id,
        field_id=field_id,
        **field_event.dict(exclude={"project_id", "phase_id", "field_id"}),
        succeeded=True,
        error_detail=None,
        status_code=None,
    )


def create_failed_response(project_id, phase_id, field_id, field_event, error_detail, status_code=None):
    return AdminBulkUpdateFieldResponse(
        project_id=project_id,
        phase_id=phase_id,
        field_id=field_id,
        **field_event.dict(exclude={"project_id", "phase_id", "field_id"}),
        succeeded=False,
        error_detail=error_detail,
        status_code=status_code,
    )


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_success(
    mock_create_update_field_events, app_request, mdl, create_interval_data, faker
):
    user_id = faker.unique.random_int(3)

    program1 = await mdl.Programs(program_template=ProgramTemplate.event_based)
    program2 = await mdl.Programs(program_template=ProgramTemplate.event_based)

    project1 = await mdl.Projects(program_id=program1.id)
    project2 = await mdl.Projects(program_id=program2.id)

    await mdl.ProjectPermissions(user=user_id, project=project1.id)
    await mdl.ProjectPermissions(user=user_id, project=project2.id)

    phase1 = await mdl.Phases(program_id=program1.id, type_=PhaseTypes.ENROLMENT)
    phase2 = await mdl.Phases(program_id=program2.id, type_=PhaseTypes.ENROLMENT)

    field1 = await mdl.Fields(parent_project_id=project1.id, deleted_at=None, status=FieldStatus.enrolled)
    field2 = await mdl.Fields(parent_project_id=project2.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field1.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    fallow_field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    fallow_event2 = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field2.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    fallow_field_event2 = FieldEvent.from_event(event=fallow_event2, event_type=EntityEventType.CROPPING_EVENT)

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [create_successful_response(project_id, phase_id, field_id, event) for event in events]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(
        events=[
            AdminBulkUpdateFieldEventRequest(
                project_id=project1.id,
                phase_id=phase1.id,
                field_id=field1.id,
                **fallow_field_event.dict(),
            ),
            AdminBulkUpdateFieldEventRequest(
                project_id=project2.id,
                phase_id=phase2.id,
                field_id=field2.id,
                **fallow_field_event2.dict(),
            ),
        ]
    )

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 2
    assert result.succeeded == 2
    assert result.failed == 0

    assert len(result.events) == 2
    for event_response in result.events:
        assert event_response.succeeded is True
        assert event_response.error_detail is None

    assert mock_create_update_field_events.call_count == 2


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_partial_failure(
    mock_create_update_field_events, app_request, mdl, create_interval_data, faker
):
    user_id = faker.unique.random_int(3)
    nonexistent_field_id = faker.unique.random_int(min=90000, max=99999)

    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user_id, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    valid_field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    invalid_field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        if field_id == nonexistent_field_id:
            return [
                create_failed_response(
                    project_id,
                    phase_id,
                    field_id,
                    event,
                    f"Field {field_id} does not exist for project {project_id}",
                    faker.random_int(min=400, max=499),
                )
                for event in events
            ]
        else:
            return [create_successful_response(project_id, phase_id, field_id, event) for event in events]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(
        events=[
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **valid_field_event.dict(),
            ),
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=nonexistent_field_id,
                **invalid_field_event.dict(),
            ),
        ]
    )

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 2
    assert result.succeeded == 1
    assert result.failed == 1

    assert len(result.events) == 2

    successful_events = [e for e in result.events if e.succeeded]
    failed_events = [e for e in result.events if not e.succeeded]

    assert len(successful_events) == 1
    assert len(failed_events) == 1

    success_event = successful_events[0]
    assert success_event.succeeded is True
    assert success_event.error_detail is None
    assert success_event.field_id == field.id

    failed_event = failed_events[0]
    assert failed_event.succeeded is False
    assert failed_event.error_detail is not None
    assert "does not exist" in failed_event.error_detail
    assert failed_event.field_id == nonexistent_field_id

    assert mock_create_update_field_events.call_count == 2


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_grouped_processing(
    mock_create_update_field_events, app_request, mdl, create_interval_data, faker
):
    user_id = faker.unique.random_int(3)

    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user_id, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    events = []
    for _ in range(3):
        fallow_event = CroppingEvent.parse_obj(
            {
                "id": None,
                "entity_id": field.id,
                "entity_type": EntityTypeChoices.field,
                "interval": interval,
                "crop_type": NoCropType.FALLOW,
            }
        )
        field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

        events.append(
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **field_event.dict(),
            )
        )

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [create_successful_response(project_id, phase_id, field_id, event) for event in events]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(events=events)

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 3
    assert result.succeeded == 3
    assert result.failed == 0

    for event_response in result.events:
        assert event_response.succeeded is True
        assert event_response.error_detail is None
        assert event_response.project_id == project.id
        assert event_response.phase_id == phase.id
        assert event_response.field_id == field.id

    assert mock_create_update_field_events.call_count == 1


async def test_admin_bulk_create_update_field_events_empty_request(app_request):
    bulk_request = AdminBulkUpdateFieldEventsRequest(events=[])

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 0
    assert result.succeeded == 0
    assert result.failed == 0
    assert len(result.events) == 0


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_validation_error(
    mock_create_update_field_events,
    app_request,
    mdl,
    create_application_event_data,
    create_application_products_data,
    faker,
):
    user_id = faker.unique.random_int(3)

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user_id, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    application_event = create_application_event_data()
    application_event["additives"] = "ni,ui,ni"
    app_event = ApplicationEvent.parse_obj(application_event)
    field_event = FieldEvent.from_event(event=app_event, event_type=EntityEventType.APPLICATION_EVENT)
    field_event.event_values["application_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [
            create_failed_response(
                project_id,
                phase_id,
                field_id,
                event,
                {"validation_errors": {"additives": ["Too many additives specified."]}},
                faker.random_int(min=400, max=499),
            )
            for event in events
        ]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(
        events=[
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **field_event.dict(),
            )
        ]
    )

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 1
    assert result.succeeded == 0
    assert result.failed == 1

    failed_event = result.events[0]
    assert failed_event.succeeded is False
    assert failed_event.error_detail is not None
    assert isinstance(failed_event.error_detail, dict)
    assert "validation_errors" in failed_event.error_detail

    assert mock_create_update_field_events.call_count == 1


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_mixed_event_types(
    mock_create_update_field_events,
    app_request,
    mdl,
    create_interval_data,
    create_tillage_event_data,
    faker,
):
    user_id = faker.unique.random_int(3)

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user_id, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    fallow_field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    tillage_event_data = create_tillage_event_data()
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    tillage_field_event = FieldEvent.from_event(event=tillage_event, event_type=EntityEventType.TILLAGE_EVENT)
    tillage_field_event.event_values["tillage_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [create_successful_response(project_id, phase_id, field_id, event) for event in events]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(
        events=[
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **fallow_field_event.dict(),
            ),
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **tillage_field_event.dict(),
            ),
        ]
    )

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 2
    assert result.succeeded == 2
    assert result.failed == 0

    event_types = [event.type for event in result.events]
    assert EntityEventType.CROPPING_EVENT in event_types
    assert EntityEventType.TILLAGE_EVENT in event_types

    assert mock_create_update_field_events.call_count == 1


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_admin_bulk_create_update_field_events_unsupported_event_type(
    mock_create_update_field_events, app_request, mdl, create_interval_data, faker
):
    user_id = faker.unique.random_int(3)

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user_id, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [
            create_failed_response(
                project_id,
                phase_id,
                field_id,
                event,
                f"Unsupported event creator type: {event.type}",
                faker.random_int(min=400, max=499),
            )
            for event in events
        ]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    bulk_request = AdminBulkUpdateFieldEventsRequest(
        events=[
            AdminBulkUpdateFieldEventRequest(
                project_id=project.id,
                phase_id=phase.id,
                field_id=field.id,
                **field_event.dict(),
            )
        ]
    )

    result = await admin_bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
    )

    assert result.total == 1
    assert result.succeeded == 0
    assert result.failed == 1

    failed_event = result.events[0]
    assert failed_event.succeeded is False
    assert failed_event.error_detail is not None
    assert "Unsupported event creator type" in failed_event.error_detail

    assert mock_create_update_field_events.call_count == 1


@patch("ui.projects.field_events.router.create_update_field_events")
async def test_bulk_create_update_field_events_success(
    mock_create_update_field_events, app_request, mdl, create_interval_data, faker
):
    user_id = faker.unique.random_int(3)
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)

    await mdl.ProjectPermissions(user=user_id, project=project.id)

    phase1 = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    field1 = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field1.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    fallow_field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    fallow_event2 = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field1.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    fallow_field_event2 = FieldEvent.from_event(event=fallow_event2, event_type=EntityEventType.CROPPING_EVENT)
    bulk_request = [fallow_field_event, fallow_field_event2]

    def mock_process_side_effect(request, events, project_id, phase_id, field_id, acting_user_id):
        return [event.dict() for event in events]

    mock_create_update_field_events.side_effect = mock_process_side_effect

    result = await bulk_create_update_field_events(
        request=app_request,
        body=bulk_request,
        project_id=project.id,
        phase_id=phase1.id,
        field_id=field1.id,
    )

    assert len(result) == 2
