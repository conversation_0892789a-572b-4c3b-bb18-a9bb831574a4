from datetime import datetime, timedelta
from unittest.mock import ANY, patch
from uuid import UUID

import pytest
import ses_client
from fastapi import HTT<PERSON>Exception, status
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from ses_client.client import Client as SesClient
from ses_client.event import StructuredEvent
from sqlalchemy import select

from boundaries_service import client as boundaries_client
from domain_event_bus.domain_events import FieldEventsChanged
from entity_events.events.enums import EntityEventType
from entity_events.events.schema import CroppingIDs
from fields.enums import FieldStatus
from logger import get_logger
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from ses_integration.model import (
    FieldEventAssociation,
    PhaseEventAssociation,
)
from ui import paths as ui_paths
from ui.projects.field_events import paths as field_paths
from ui.projects.field_events.router import delete_field_event, event_bus
from ui.projects.field_events.schema import FieldEvent
from ui.projects.field_events.tests.mocks import (
    get_mock_geom,
    mock_delete_or_archive_field_event_for_user,
    mock_delete_or_archive_field_event_for_user_fail,
    mock_event2_id,
    mock_event_id,
)

logger = get_logger(__name__)


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
async def test_delete_field_event_invalid_field(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id)
    field_event = {
        "id": mock_event_id,
        "event_values": {
            "planting_date": "2023-01-01",
            "crop_type": "corn",
        },
        "type": "CroppingEvent",
    }
    with pytest.raises(HTTPException) as exc_info:
        await delete_field_event(
            request=app_request,
            project_id=project.id,
            field_id="bad-id",
            phase_id=phase.id,
            body=field_event,
        )

    assert exc_info.value.detail == f"Field does exist for project id {project.id}"
    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST


@patch.object(event_bus, "publish")
@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "delete_or_archive_field_event_for_user",
    mock_delete_or_archive_field_event_for_user,
)
async def test_delete_field_event(mock_event_bus_publish, app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    phase = await mdl.Phases(program_id=program.id)

    field_event = FieldEvent(
        event_values={"planting_date": "2023-01-01", "crop_type": "corn"},
        id=CroppingIDs(planting_id=None, sowing_id=mock_event_id, harvesting_id=mock_event2_id, termination_id=None),
        type=EntityEventType.CROPPING_EVENT,
    )

    result = await delete_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        phase_id=phase.id,
        body=field_event,
    )

    assert result == [mock_event_id, mock_event2_id]
    mock_event_bus_publish.assert_called_with(
        FieldEventsChanged(program_id=program.id, phase_id=phase.id, project_id=project.id, field_id=field.id),
        app_request,
    )

    field_event = FieldEvent(
        event_values={"planting_date": "2023-01-01", "crop_type": "corn"},
        id=mock_event_id,
        type=EntityEventType.TILLAGE_EVENT,
    )

    result = await delete_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        phase_id=phase.id,
        body=field_event,
    )

    assert result == [mock_event_id]


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "delete_or_archive_field_event_for_user",
    mock_delete_or_archive_field_event_for_user_fail,
)
async def test_delete_field_event_fail(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    phase = await mdl.Phases(program_id=program.id)
    field_event = FieldEvent(
        event_values={"planting_date": "2023-01-01", "crop_type": "corn"},
        id=mock_event_id,
        type=EntityEventType.TILLAGE_EVENT,
    )
    with pytest.raises(HTTPException) as exc_info:
        await delete_field_event(
            request=app_request,
            project_id=project.id,
            field_id=field.id,
            phase_id=phase.id,
            body=field_event,
        )

    assert exc_info.value.detail == f"No SES event to delete for id: {mock_event_id}"
    assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
async def test_delete_field_event_locks_fail(mocker, app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    phase = await mdl.Phases(program_id=program.id)

    field_event = FieldEvent(
        event_values={"planting_date": "2023-01-01", "crop_type": "corn"},
        id=CroppingIDs(planting_id=None, sowing_id=mock_event_id, harvesting_id=mock_event2_id, termination_id=None),
        type=EntityEventType.CROPPING_EVENT,
    )
    locked_event_ids = [UUID(mock_event2_id)]
    mocker.patch("ses_integration.event_associations.get_locked_event_ids_for_user", return_value=locked_event_ids)

    with pytest.raises(HTTPException) as exc_info:
        await delete_field_event(
            request=app_request,
            project_id=project.id,
            field_id=field.id,
            phase_id=phase.id,
            body=field_event,
        )
    assert exc_info.value.detail == f"Event {mock_event2_id} is locked and cannot be edited at this time"
    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST


@patch.object(event_bus, "publish")
@patch.object(boundaries_client, "get_geometry_for_md5", get_mock_geom)
@patch.object(ses_client.client.Client, "delete_or_archive_field_event_for_user")
async def test_create_update_field_event(
    mock_delete_or_archive_field_event_for_user, mock_event_bus_publish, async_client, mdl, app_request
):
    """
    Test the create_update_field_event endpoint by tracking a series of cropping events, starting with
      - creating a fallow event
      - then update it to a cropping event with just a planting date
      - then updating the crop type
      - then adding a harvest date
      - and finally converting it back to a fallow event

    Check that the SES IDs (fallow, planting, harvest etc) are tracked properly
    """

    async def get_phase_event_associations(event_ids, phase_id):
        async with app_request.state.sql_session() as sess:
            query = (
                select(PhaseEventAssociation)
                .join(
                    FieldEventAssociation, FieldEventAssociation.id == PhaseEventAssociation.field_event_association_id
                )
                .filter(FieldEventAssociation.ses_event_id.in_(event_ids))
                .filter(PhaseEventAssociation.phase_id == phase_id)
                .filter(PhaseEventAssociation.deleted_at.is_(None))
            )
            return (await sess.execute(query)).scalars().all()

    prog = await mdl.Programs(program_template=ProgramTemplate.event_based)
    proj = await mdl.Projects(program_id=prog.id)
    await mdl.ProjectPermissions(user=7, project=proj.id)
    field = await mdl.Fields(parent_project_id=proj.id)
    phase = await mdl.Phases(program_id=prog.id, type_=PhaseTypes.MONITORING)
    # Call endpoint create_update_field_event
    url = (ui_paths.ui_base + field_paths.ui_project_phase_base + field_paths.field_events).format(
        project_id=proj.id, phase_id=phase.id, field_id=field.id
    )
    start_time = datetime.now() - timedelta(days=200)
    end_time = datetime.now() - timedelta(days=100)

    # Start with a fallow event
    payload = {
        "event_values": {
            "planting_date": start_time.date().isoformat(),
            "harvest_date": end_time.date().isoformat(),
            "crop_type": "fallow",
        },
        "type": "CroppingEvent",
    }
    fallow_uuid = None

    def mock_upsert_fallow_event(structured_event: StructuredEvent, field_id: str):
        nonlocal fallow_uuid
        fallow_uuid = structured_event.pb_event.id
        return UpsertEventResponse(event=structured_event.pb_event, context=structured_event.pb_context)

    # Create a fallow event with start and end dates
    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_fallow_event):
        response = await async_client.post(url, json=payload)
    assert response.status_code == 200
    field_event_response = response.json()
    assert field_event_response == {
        "id": {
            "fallow_id": fallow_uuid,
            "planting_id": None,
            "sowing_id": None,
            "harvesting_id": None,
            "termination_id": None,
        },
        "event_values": {
            "start_date": start_time.date().isoformat(),
            "end_date": end_time.date().isoformat(),
            "crop_type": "fallow",
        },
        "type": "FallowPeriod",
        "is_prefilled": False,
    }
    mock_delete_or_archive_field_event_for_user.assert_not_called()
    mock_event_bus_publish.assert_called_with(
        FieldEventsChanged(program_id=prog.id, phase_id=phase.id, project_id=proj.id, field_id=field.id), ANY
    )

    # We should have saved a PhaseEventAssociation for the fallow event
    phase_events = await get_phase_event_associations([fallow_uuid], phase.id)
    assert len(phase_events) == 1

    # Convert this FallowPeriod event to a CroppingEvent with just the planting date
    payload = {
        "id": {
            "fallow_id": fallow_uuid,
            "planting_id": None,
            "sowing_id": None,
            "harvesting_id": None,
            "termination_id": None,
        },
        "event_values": {
            "planting_date": start_time.date().isoformat(),
            "crop_type": "rye",
            "crop_usage": "Commodity",
        },
        "type": "CroppingEvent",
    }
    sowing_uuid = None

    def mock_upsert_field_event(structured_event: StructuredEvent, field_id: str):
        nonlocal sowing_uuid
        sowing_uuid = structured_event.pb_event.id
        return UpsertEventResponse(event=structured_event.pb_event, context=structured_event.pb_context)

    # Create a cropping event with just the planting date - no harvest date
    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_field_event):
        response = await async_client.post(url, json=payload)
    assert response.status_code == 200
    field_event_response = response.json()
    # We should get a start UUID but no end UUID
    assert field_event_response["id"] == {
        "fallow_id": None,
        "planting_id": None,
        "sowing_id": sowing_uuid,
        "harvesting_id": None,
        "termination_id": None,
    }
    assert field_event_response["type"] == "CroppingEvent"
    assert field_event_response["event_values"]["planting_date"] == start_time.date().isoformat()
    assert field_event_response["event_values"]["crop_type"] == "rye"

    # Check that SES delete_field_event called for fallow event
    mock_delete_or_archive_field_event_for_user.assert_called_once()
    assert mock_delete_or_archive_field_event_for_user.call_args.kwargs["event_id"] == fallow_uuid
    mock_delete_or_archive_field_event_for_user.reset_mock()

    # We should have saved a PhaseEventAssociation for the sowing activity
    phase_events = await get_phase_event_associations([sowing_uuid], phase.id)
    assert len(phase_events) == 1

    # The fallow event should have been removed from PhaseEventAssociation
    phase_events = await get_phase_event_associations([fallow_uuid], phase.id)
    assert len(phase_events) == 0

    # Change the crop type - still no harvest date
    payload = field_event_response
    payload["event_values"]["crop_type"] = "corn"

    def mock_upsert_event_with_field_check(structured_event: StructuredEvent, field_id: str):
        return UpsertEventResponse(event=structured_event.pb_event, context=structured_event.pb_context)

    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_event_with_field_check):
        response = await async_client.post(url, json=payload)
    assert response.status_code == 200
    field_event_response = response.json()
    # Crop type should change
    assert field_event_response["event_values"]["crop_type"] == "corn"
    # We should get back the same UUID for the start event, as well as the other fields
    assert field_event_response["id"] == {
        "fallow_id": None,
        "planting_id": None,
        "sowing_id": sowing_uuid,
        "harvesting_id": None,
        "termination_id": None,
    }
    assert field_event_response["type"] == "CroppingEvent"
    assert field_event_response["event_values"]["planting_date"] == start_time.date().isoformat()

    # We should still have PhaseEventAssociation for the sowing activity
    phase_events = await get_phase_event_associations([sowing_uuid], phase.id)
    assert len(phase_events) == 1

    # Check that SES delete_field_event not called
    mock_delete_or_archive_field_event_for_user.assert_not_called()

    # Add a harvest date - this should create another 2 UUIDs, for the harvest and termination events
    payload = field_event_response
    payload["event_values"]["harvest_date"] = end_time.date().isoformat()

    termination_uuid = harvest_uuid = None

    def mock_upsert_activities(structured_event: StructuredEvent, field_id: str):
        nonlocal harvest_uuid, termination_uuid
        assert not structured_event.pb_event.HasField("planting_activity")
        if structured_event.pb_event.HasField("sowing_activity"):
            assert not structured_event.pb_event.HasField("termination_activity")
            assert not structured_event.pb_event.HasField("harvest_activity")
            assert structured_event.pb_event.id == sowing_uuid
        elif structured_event.pb_event.HasField("termination_activity"):
            assert not structured_event.pb_event.HasField("sowing_activity")
            assert not structured_event.pb_event.HasField("harvest_activity")
            termination_uuid = structured_event.pb_event.id
        elif structured_event.pb_event.HasField("harvest_activity"):
            assert not structured_event.pb_event.HasField("sowing_activity")
            assert not structured_event.pb_event.HasField("termination_activity")
            harvest_uuid = structured_event.pb_event.id
        return UpsertEventResponse(event=structured_event.pb_event, context=structured_event.pb_context)

    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_activities):
        response = await async_client.post(url, json=payload)
    assert response.status_code == 200
    field_event_response = response.json()
    assert field_event_response["id"] == {
        "fallow_id": None,
        "planting_id": None,
        "sowing_id": sowing_uuid,
        "harvesting_id": harvest_uuid,
        "termination_id": termination_uuid,
    }
    assert field_event_response["type"] == "CroppingEvent"
    assert field_event_response["event_values"]["planting_date"] == start_time.date().isoformat()
    assert field_event_response["event_values"]["harvest_date"] == end_time.date().isoformat()

    # We should still have PhaseEventAssociation for the sowing activity, and we should now have one for the harvest activity
    phase_events = await get_phase_event_associations([sowing_uuid], phase.id)
    assert len(phase_events) == 1
    phase_events = await get_phase_event_associations([harvest_uuid], phase.id)
    assert len(phase_events) == 1

    # Check that SES delete_field_event not called
    mock_delete_or_archive_field_event_for_user.assert_not_called()

    planting_uuid = None

    # Change planting method to PlantingMethod.TRANSPLANT_WET. This will change underlying SES start event type from
    # SowingActivity to PlantingActivity. Also change the crop usage to "Cover", which means we won't have a harvest
    # activity anymore.

    def mock_upsert_activities2(structured_event: StructuredEvent, field_id: str):
        nonlocal planting_uuid
        assert not structured_event.pb_event.HasField("sowing_activity")
        assert not structured_event.pb_event.HasField("harvest_activity")
        if structured_event.pb_event.HasField("planting_activity"):
            assert not structured_event.pb_event.HasField("termination_activity")
            planting_uuid = structured_event.pb_event.id
        elif structured_event.pb_event.HasField("termination_activity"):
            assert not structured_event.pb_event.HasField("planting_activity")
            assert structured_event.pb_event.id == termination_uuid
        return UpsertEventResponse(event=structured_event.pb_event, context=structured_event.pb_context)

    payload = field_event_response
    payload["event_values"]["planting_method"] = "Transplant (wet)"
    payload["event_values"]["crop_usage"] = "Cover"

    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_activities2):
        response = await async_client.post(url, json=payload)

    assert response.status_code == 200
    field_event_response = response.json()
    # Planting ID added and sowing ID removed
    assert field_event_response["id"] == {
        "fallow_id": None,
        "planting_id": planting_uuid,
        "sowing_id": None,
        "harvesting_id": None,
        "termination_id": termination_uuid,
    }
    # Check that SES delete_field_event called for sowing event
    assert mock_delete_or_archive_field_event_for_user.call_count == 2
    deleted_ids = {call.kwargs["event_id"] for call in mock_delete_or_archive_field_event_for_user.call_args_list}
    assert deleted_ids == {sowing_uuid, harvest_uuid}
    mock_delete_or_archive_field_event_for_user.reset_mock()

    # Check that the PhaseEventAssociations row for this sowing event is also deleted
    phase_events = await get_phase_event_associations([sowing_uuid, harvest_uuid], phase.id)
    assert not phase_events

    # Finally, convert the event back to a fallow event
    payload = field_event_response
    payload["event_values"]["crop_type"] = "fallow"

    fallow_uuid = None
    # Create a fallow event with start and end dates
    with patch.object(ses_client.client.Client, "upsert_field_event", side_effect=mock_upsert_fallow_event):
        response = await async_client.post(url, json=payload)
    assert response.status_code == 200
    field_event_response = response.json()

    assert field_event_response == {
        "id": {
            "fallow_id": fallow_uuid,
            "planting_id": None,
            "sowing_id": None,
            "harvesting_id": None,
            "termination_id": None,
        },
        "event_values": {
            "start_date": start_time.date().isoformat(),
            "end_date": end_time.date().isoformat(),
            "crop_type": "fallow",
        },
        "type": "FallowPeriod",
        "is_prefilled": False,
    }
    # We should have saved a PhaseEventAssociation for the fallow event
    phase_events = await get_phase_event_associations([fallow_uuid], phase.id)
    assert len(phase_events) == 1

    # Check that SES delete_field_event called for planting and termination events
    assert mock_delete_or_archive_field_event_for_user.call_count == 2
    deleted_ids = {call.kwargs["event_id"] for call in mock_delete_or_archive_field_event_for_user.call_args_list}
    assert deleted_ids == {planting_uuid, termination_uuid}

    # Check that the PhaseEventAssociations rows planting and termination events are also deleted
    phase_events = await get_phase_event_associations([planting_uuid, termination_uuid], phase.id)
    assert not phase_events
