import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from elasticapm import Client
from regrow.ses.pbtype.interval_pb2 import Interval
from regrow.ses.tillage.v1 import tillage_pb2
from ses_client import pb_value
from ses_client.application import ApplicationActivity
from ses_client.crop import (
    crop_id_from_label,
    crop_purpose_commodity_harvest,
    HarvestedCrop,
    PlantedCrop,
    TerminatedCrop,
)
from ses_client.fallow import FallowPeriod, StructuredEvent
from ses_client.harvest import HarvestActivity
from ses_client.input import FertiliserAdditive, OrganicAmendment
from ses_client.irrigation import IrrigationActivity
from ses_client.planting import PlantingActivity
from ses_client.search import Filter
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from entity_events.events.entity_event import EntityEvent, SESEventWithContext
from fields.schema import Field

mock_event_id = "6daec06d-6c56-4b8b-92fc-1fa2f430ec57"
mock_event2_id = "6daec06d-6c56-4b8b-92fc-1fa2f430ec58"
mock_geom = {
    "type": "MultiPolygon",
    "coordinates": [
        [
            [
                [-91.29233641703286, 41.30334338630458],
                [-91.28745699999997, 41.305083999999745],
                [-91.28756061103775, 41.299742821734874],
                [-91.29232964157615, 41.29971351579836],
                [-91.29233641703286, 41.30334338630458],
            ]
        ]
    ],
}


async def get_mock_geom(md5: str):
    return json.dumps(mock_geom)


def mock_delete_field_event(self, geom: str, event_id: str, user_id: str, field_id: str):
    class Res:
        pass

    res = Res()
    res.id = event_id
    return res


async def mock_delete_or_archive_field_event_for_user(self, event_id: str, user_id: str, field_id: str):
    class Res:
        pass

    res = Res()
    res.id = event_id
    return res


def mock_delete_field_event_fail(self, geom: str, event_id: str, user_id: str, field_id: str):
    return None


async def mock_delete_or_archive_field_event_for_user_fail(self, event_id: str, user_id: str, field_id: str):
    return None


event_interval = Interval(start_time=datetime(2021, 1, 1), end_time=datetime(2021, 5, 1))
fallow_period = (
    FallowPeriod(event_id=mock_event_id, user_id="user-id")
    .start(datetime(2021, 1, 1))
    .end(datetime(2021, 5, 1))
    .geom(json.dumps(mock_geom))
)
upsert_fallow_period_response = SESEventWithContext(event=fallow_period.pb_event, context=fallow_period.pb_context)


def mock_upsert_field_event(self, se: StructuredEvent) -> SESEventWithContext:
    return upsert_fallow_period_response


def mock_upsert_event_with_field_check(self, se: StructuredEvent, field_id: str) -> SESEventWithContext:
    return upsert_fallow_period_response


async def mock_upsert_events_to_ses(
    field: Field, event: EntityEvent, new_events: list[StructuredEvent], ses_client: Client
) -> SESEventWithContext:
    return [upsert_fallow_period_response]


event_interval = Interval(start_time=datetime(2021, 1, 1), end_time=datetime(2021, 5, 1))
pb_application_input = (
    OrganicAmendment(input_id=None, input_name="dairy_liquid").volume_rate(pb_value.litres_per_hectare(97.52)).pb()
)
pb_application_input_additives = (
    FertiliserAdditive(input_id=None, input_name="centuro").volume_rate(pb_value.litres_per_hectare(97.52)).pb()
)

application_event = (
    ApplicationActivity(event_id=mock_event_id, user_id="user-id")
    .start(datetime(2021, 1, 1))
    .end(datetime(2021, 5, 1))
    .geom(json.dumps(mock_geom))
    .input(pb_application_input)
    .input(pb_application_input_additives)
)
upsert_application_event_response = SESEventWithContext(
    event=application_event.pb_event, context=application_event.pb_context
)


def mock_upsert_application_event(self, se: StructuredEvent) -> SESEventWithContext:
    return upsert_application_event_response


async def mock_upsert_application_events_to_ses(
    field: Field, event: EntityEvent, new_events: list[StructuredEvent], ses_client: Client
) -> SESEventWithContext:
    return [upsert_application_event_response]


tillage_event = (
    TillageActivity(event_id=mock_event_id, user_id="user-id")
    .start(datetime(2021, 1, 1))
    .end(datetime(2021, 5, 1))
    .geom(json.dumps(mock_geom))
    .tillage_practice(tillage_pb2.TILLAGE_PRACTICE_CONVENTIONAL)
    .soil_inversion(False)
    .depth(pb_value.depth_centimetres(260))
)
upsert_tillage_event_response = SESEventWithContext(event=tillage_event.pb_event, context=tillage_event.pb_context)


def mock_upsert_tillage_event(self, se: StructuredEvent) -> SESEventWithContext:
    return upsert_tillage_event_response


async def mock_upsert_tillage_events_to_ses(
    field: Field, event: EntityEvent, new_events: list[StructuredEvent], ses_client: Client
) -> SESEventWithContext:
    return [upsert_tillage_event_response]


irrigation_event = (
    IrrigationActivity(event_id=mock_event_id, user_id="user-id")
    .start(datetime(2021, 1, 1))
    .end(datetime(2021, 5, 1))
    .geom(json.dumps(mock_geom))
    .method_subsurface(soil_depth=pb_value.depth_inches(1))
)
upsert_irrigation_event_response = SESEventWithContext(
    event=irrigation_event.pb_event, context=irrigation_event.pb_context
)


async def mock_upsert_irrigation_events_to_ses(
    field: Field, event: EntityEvent, new_events: list[StructuredEvent], ses_client: Client
) -> SESEventWithContext:
    return [upsert_irrigation_event_response]


event_id = str(uuid.uuid4())
crop_type = "corn"
crop_id = crop_id_from_label(crop_type)
sown_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
terminated_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
harvest_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())

planting_activity = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(sown_crop)
    .start(datetime(year=2020, month=1, day=1))
    .event_and_context_pb()
)

termination_activity = (
    TerminationActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(terminated_crop)
    .start(datetime(year=2021, month=12, day=1))
    .event_and_context_pb()
)

harvest_activity = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(harvest_crop)
    .start(datetime(year=2021, month=12, day=1))
    .event_and_context_pb()
)

upsert_cropping_event_response = (
    SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
    SESEventWithContext(event=termination_activity[0], context=termination_activity[1]),
    SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
)


async def mock_upsert_cropping_events_to_ses(
    field: Field, event: EntityEvent, new_events: list[StructuredEvent], ses_client: Client
) -> SESEventWithContext:
    return upsert_cropping_event_response


def mock_events_for_fields(self, field_ids: List[str], search_filter: Filter) -> Dict[str, List[StructuredEvent]]:

    return {
        field_ids[0]: [
            # FallowPeriod(event_id="12356", user_id="1"),
        ]
    }


start_date = datetime.now() - timedelta(days=100)
end_date = datetime.now() - timedelta(days=30)
date = datetime.now()


event_id = str(uuid.uuid4())
crop_type = "corn"
crop_id = crop_id_from_label(crop_type)
sown_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
harvest_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
planting_activity = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(sown_crop)
    .start(datetime(year=2020, month=1, day=1))
    .event_and_context_pb()
)
planting_activity1 = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(sown_crop)
    .start(datetime(year=2021, month=1, day=1))
    .event_and_context_pb()
)
harvest_activity = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(harvest_crop)
    .start(datetime(year=2021, month=12, day=1))
    .event_and_context_pb()
)
harvest_activity1 = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(harvest_crop)
    .start(datetime(year=2022, month=12, day=1))
    .event_and_context_pb()
)
harvest_activity2 = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(harvest_crop)
    .start(datetime(year=2023, month=12, day=1))
    .event_and_context_pb()
)


def get_mock_crop_sequences(field_id: str) -> Dict[str, list[tuple]]:
    def mock_cropping_sequences_for_fields(
        self,
        field_ids: List[str],
        from_date: datetime,
        to_date: datetime,
        user_ids: Optional[List[str]] = None,
        min_overlap_percentage: Optional[int] = 50,
        crop_purpose: Optional[str] = None,
    ) -> Dict[str, list[tuple]]:
        return {
            field_id: [
                (
                    SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
                    SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
                ),
                (
                    SESEventWithContext(event=planting_activity1[0], context=planting_activity1[1]),
                    SESEventWithContext(event=harvest_activity1[0], context=harvest_activity1[1]),
                ),
            ]
        }

    return mock_cropping_sequences_for_fields


def mock_cropping_sequences_for_fields(
    self,
    field_ids: List[str],
    from_date: datetime,
    to_date: datetime,
    user_ids: Optional[List[str]] = None,
    min_overlap_percentage: Optional[int] = 50,
) -> Dict[str, list[tuple]]:
    return {
        field_ids[0]: [
            (
                SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
                SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
            ),
            (
                SESEventWithContext(event=planting_activity1[0], context=planting_activity1[1]),
                SESEventWithContext(event=harvest_activity1[0], context=harvest_activity1[1]),
            ),
        ]
    }
