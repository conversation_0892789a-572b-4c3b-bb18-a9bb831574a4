from datetime import datetime, timezone
from unittest.mock import patch

import pytest
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status

from boundaries_service import client as boundaries_client
from defaults.attribute_options import NoCropType
from defaults.defaults import defaults_retriever
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.units import LengthUnit
from entity_events.validation.rules_scraper import ValidationRulesScraper
from fields.enums import FieldStatus
from logger import get_logger
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from ui.projects.field_events.router import create_update_field_event
from ui.projects.field_events.schema import FieldEvent
from ui.projects.field_events.tests.mocks import (
    event_interval,
    get_mock_geom,
    mock_upsert_application_events_to_ses,
    mock_upsert_cropping_events_to_ses,
    mock_upsert_events_to_ses,
    mock_upsert_irrigation_events_to_ses,
    mock_upsert_tillage_events_to_ses,
)
from values.enums import EntityTypeChoices

start = event_interval.start_time.ToDatetime()
end = event_interval.end_time.ToDatetime()

logger = get_logger(__name__)

# both defaults_retriever and entity_events.validation.rules_scraper.validation_rules_scraper
# globals, and validation_rules_scraper depends on defaults_retriever state,
# so patching in that variable where used controls the state across different test distributions
validation_rules_scraper_1 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_1)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_1)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_fallow_event(mock_update_assoc, upsert_events_to_ses, app_request, mdl, create_interval_data):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_1.get_rules()) == 0:
        await validation_rules_scraper_1.run()
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    interval = create_interval_data(start=start, end=end)
    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    field_event_response = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)

    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.FALLOW_PERIOD
    assert result.event_values["start_date"] == start.date()
    assert result.event_values["end_date"] == end.date()
    assert result.event_values["crop_type"] == NoCropType.FALLOW.value

    assert mock_update_assoc.was_called_once()


# to allow the most aggressive test redistribution,
# we have to be sure each test has a fresh scraper that gets initialized as we control
validation_rules_scraper_2 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_2)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_2)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_update_fallow_event(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_interval_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_2.get_rules()) == 0:
        await validation_rules_scraper_2.run()
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    interval = create_interval_data(start=start, end=end)

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": {"fallow_id": "052e6b89-9ebb-4a0d-a6eb-d64e778fe11f"},
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )
    field_event_response = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)
    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.FALLOW_PERIOD
    assert result.event_values["start_date"] == start.date()
    assert result.event_values["end_date"] == end.date()
    assert result.event_values["crop_type"] == NoCropType.FALLOW.value

    assert mock_update_assoc.was_called_once()


validation_rules_scraper_3 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_3)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_3)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_application_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_application_event(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_application_products_data,
    create_application_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_3.get_rules()) == 0:
        await validation_rules_scraper_3.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    application_event = create_application_event_data()
    application_event["products"] = create_application_products_data(main_product_name="urea")
    application_event["additives"] = "anvol,centuro"

    app_event = ApplicationEvent.parse_obj(application_event)

    field_event_response = FieldEvent.from_event(event=app_event, event_type=EntityEventType.APPLICATION_EVENT)

    field_event_response.event_values["application_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.APPLICATION_EVENT
    assert result.event_values["application_date"] == start.date()
    assert result.event_values["application_product"] == "dairy_liquid"
    assert mock_update_assoc.was_called_once()


validation_rules_scraper_4 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_4)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_4)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_application_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_application_event_validation_error(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_application_products_data,
    create_application_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_4.get_rules()) == 0:
        await validation_rules_scraper_4.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id

    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    application_event = create_application_event_data()
    application_event["additives"] = "ni,ui,ni"

    app_event = ApplicationEvent.parse_obj(application_event)

    field_event_response = FieldEvent.from_event(event=app_event, event_type=EntityEventType.APPLICATION_EVENT)

    field_event_response.event_values["application_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    with pytest.raises(HTTPException) as e:
        await create_update_field_event(
            request=app_request,
            project_id=project.id,
            field_id=field.id,
            body=field_event_response,
            phase_id=phase_id,
        )
    expected_errors = {
        "additives": [
            "Cannot have more than 2 additives.",
            "Additives must include at most one Nitrification Inhibitor and one Urease Inhibitor.",
            "Additives must be unique. Can't select the same additive twice for the same event.",
        ]
    }

    assert e.value.detail["validation_errors"] == expected_errors
    assert e.value.status_code == status.HTTP_400_BAD_REQUEST


validation_rules_scraper_5 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_5)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_5)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_tillage_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_tillage_event(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_tillage_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_5.get_rules()) == 0:
        await validation_rules_scraper_5.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event = create_tillage_event_data()

    till_event = TillageEvent.parse_obj(tillage_event)

    field_event_response = FieldEvent.from_event(event=till_event, event_type=EntityEventType.TILLAGE_EVENT)

    field_event_response.event_values["tillage_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.TILLAGE_EVENT
    assert mock_update_assoc.was_called_once()


validation_rules_scraper_6 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_6)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_6)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_tillage_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_tillage_event_validation_error(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_tillage_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_6.get_rules()) == 0:
        await validation_rules_scraper_6.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event = create_tillage_event_data()
    tillage_event["depth"] = {"value": 101, "unit": LengthUnit.CENTIMETRE}
    till_event = TillageEvent.parse_obj(tillage_event)

    field_event_response = FieldEvent.from_event(event=till_event, event_type=EntityEventType.TILLAGE_EVENT)

    field_event_response.event_values["tillage_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)

    with pytest.raises(HTTPException) as e:
        await create_update_field_event(
            request=app_request,
            project_id=project.id,
            field_id=field.id,
            body=field_event_response,
            phase_id=phase_id,
        )

    expected_errors = {
        "tillage_depth": ["Tillage depth should be between 0 and 100 cm or 39.37 inches."],
    }

    assert e.value.detail["validation_errors"] == expected_errors
    assert e.value.status_code == status.HTTP_400_BAD_REQUEST


validation_rules_scraper_7 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_7)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_7)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_irrigation_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_irrigation_event(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_irrigation_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_7.get_rules()) == 0:
        await validation_rules_scraper_7.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    irrigation_event = create_irrigation_event_data()

    irr_event = IrrigationEvent.parse_obj(irrigation_event)

    field_event_response = FieldEvent.from_event(event=irr_event, event_type=EntityEventType.IRRIGATION_EVENT)

    field_event_response.event_values["start_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)
    field_event_response.event_values["end_date"] = datetime(2020, 10, 29, tzinfo=timezone.utc)

    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.IRRIGATION_EVENT
    assert mock_update_assoc.was_called_once()


validation_rules_scraper_8 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_8)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_8)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_cropping_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_cropping_event(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_cropping_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_8.get_rules()) == 0:
        await validation_rules_scraper_8.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event = create_cropping_event_data()

    crop_event = CroppingEvent.parse_obj(cropping_event)

    field_event_response = FieldEvent.from_event(event=crop_event, event_type=EntityEventType.CROPPING_EVENT)

    field_event_response.event_values["planting_date"] = datetime(2020, 9, 29, tzinfo=timezone.utc)
    field_event_response.event_values["harvest_date"] = datetime(2020, 10, 29, tzinfo=timezone.utc)

    result = await create_update_field_event(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        body=field_event_response,
        phase_id=phase_id,
    )

    assert result.type == EntityEventType.CROPPING_EVENT
    assert result.event_values["crop_usage"] == "Commodity"
    assert mock_update_assoc.was_called_once()


validation_rules_scraper_9 = ValidationRulesScraper()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("entity_events.validation.rules_scraper.validation_rules_scraper", validation_rules_scraper_9)
@patch("entity_events.validation.validator.validation_rules_scraper", validation_rules_scraper_9)
@patch(
    "ui.projects.field_events.methods.upsert_events_to_ses",
    side_effect=mock_upsert_cropping_events_to_ses,
)
@patch("ui.projects.field_events.methods.update_event_associations")
async def test_create_cropping_event_validation_error(
    mock_update_assoc,
    upsert_events_to_ses,
    app_request,
    mdl,
    create_cropping_event_data,
):
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    await defaults_retriever.fetch_all_crops(use_file=True)
    if len(await validation_rules_scraper_9.get_rules()) == 0:
        await validation_rules_scraper_9.run()
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=7, project=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    phase_id = phase.id
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event = create_cropping_event_data()

    crop_event = CroppingEvent.parse_obj(cropping_event)

    field_event_response = FieldEvent.from_event(event=crop_event, event_type=EntityEventType.CROPPING_EVENT)

    field_event_response.event_values["planting_date"] = datetime(2020, 11, 29, tzinfo=timezone.utc)
    field_event_response.event_values["harvest_date"] = datetime(2020, 10, 29, tzinfo=timezone.utc)
    with pytest.raises(HTTPException) as e:
        await create_update_field_event(
            request=app_request,
            project_id=project.id,
            field_id=field.id,
            body=field_event_response,
            phase_id=phase_id,
        )

    expected_errors = {
        "harvest_date": ["Planting date must be before the harvest/termination date."],
        "planting_date": ["Planting date must be before the harvest/termination date."],
    }

    assert e.value.detail["validation_errors"] == expected_errors
    assert e.value.status_code == status.HTTP_400_BAD_REQUEST
