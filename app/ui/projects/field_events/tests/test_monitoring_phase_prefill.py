import uuid
from datetime import datetime, timezone
from unittest.mock import ANY, call, patch

import pytest
from regrow.ses.crop.v1.crop_id_enum_pb2 import CropId
from regrow.ses.crop.v1.crop_pb2 import Crop
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.harvest.v1.harvest_pb2 import HarvestActivity, HarvestedCrop
from ses_client.client import Client

from defaults.attribute_options import CropUsage, TillagePractice
from defaults.consts import RegrowCropName
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.schema import CroppingIDs
from fields.enums import EntityDataType, FieldDataState
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs.enums import PracticeChange
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.field_events.monitoring_phase_prefill import (
    prefill_monitoring_phase_commodity_crops_for_project,
    prefill_monitoring_phase_cover_crops_or_tillage_for_field,
)
from ui.projects.field_events.schema import (
    AdminBulkUpdateFieldResponse,
    CultivationCycleResponse,
    CultivationCycleResponseId,
    FieldEvent,
    FieldEventRequest,
    NoPracticeObservations,
)


@patch("ui.projects.field_events.monitoring_phase_prefill.update_no_practice_observations_for_event")
@patch("ui.projects.field_events.monitoring_phase_prefill.create_update_field_events")
@patch("ui.projects.field_events.monitoring_phase_prefill.get_entity_events_for_project_stages")
@patch.object(Client, "fetch_events_for_fields")
async def test_prefill_monitoring_phase_commodity_crops_for_project(
    mock_fetch_events_for_fields,
    mock_get_entity_events_for_project_stages,
    mock_create_update_field_events,
    mock_update_no_practice_observations_for_event,
    mdl,
    app_request,
    create_cropping_event_data,
    create_interval_data,
):
    program = await mdl.Programs()
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    intended_commodity_crops_stage = await mdl.Stage(
        phase_id=e_phase.id, type_=StageTypes.INTENDED_COMMODITY_CROPS, enabled=True
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user.id, project=project.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldFacts(
        facts=[[PracticeChange.cover_crops, PracticeChange.no_till]],
        field_id=field_1.id,
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )
    await mdl.FieldFacts(
        facts=[[PracticeChange.cover_crops, PracticeChange.reduced_till]],
        field_id=field_2.id,
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )

    crop_event_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field_1.id,
            interval=create_interval_data(
                start=datetime(2025, 1, 1, tzinfo=timezone.utc), end=datetime(2025, 6, 1, tzinfo=timezone.utc)
            ),
            crop_usage=CropUsage.COMMODITY,
            crop_type="beans",
            reductions=[],
        )
    )
    crop_event_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field_2.id,
            interval=create_interval_data(
                start=datetime(2025, 1, 1, tzinfo=timezone.utc), end=datetime(2025, 6, 1, tzinfo=timezone.utc)
            ),
            crop_usage=CropUsage.COMMODITY,
            crop_type="beans",
            reductions=[],
        )
    )
    mock_get_entity_events_for_project_stages.return_value = {
        PhaseTypes.ENROLMENT: {StageTypes.INTENDED_COMMODITY_CROPS: [crop_event_1, crop_event_2]}
    }

    mock_fetch_events_for_fields.return_value = {field_1.md5: [], field_2.md5: []}

    harvesting_id_1 = uuid.uuid4()
    harvesting_id_2 = uuid.uuid4()
    mock_create_update_field_events.side_effect = [
        [
            AdminBulkUpdateFieldResponse(
                id={"harvesting_id": harvesting_id_1},
                event_values={},
                type=EntityEventType.CROPPING_EVENT,
                project_id=project.id,
                phase_id=m_phase.id,
                field_id=field_1.id,
                succeeded=True,
            )
        ],
        [
            AdminBulkUpdateFieldResponse(
                id={"harvesting_id": harvesting_id_2},
                event_values={},
                type=EntityEventType.CROPPING_EVENT,
                project_id=project.id,
                phase_id=m_phase.id,
                field_id=field_2.id,
                succeeded=True,
            )
        ],
    ]

    await prefill_monitoring_phase_commodity_crops_for_project(
        request=app_request,
        program_id=program.id,
        project_id=project.id,
        intended_commodity_crops_stage_id=intended_commodity_crops_stage.id,
        m_phase_id=m_phase.id,
        acting_user_id="1",
    )

    mock_create_update_field_events.assert_has_calls(
        [
            call(
                request=app_request,
                events=[
                    FieldEventRequest(
                        id=None,
                        event_values={
                            AttributeTypes.crop_type: "dry_bean",
                            AttributeTypes.crop_usage: crop_event_1.crop_usage,
                            AttributeTypes.planting_date: crop_event_1.get_interval_start_or_occurred_at(),
                            AttributeTypes.harvest_date: crop_event_1.get_interval_end_or_occurred_at(),
                        },
                        type=EntityEventType.CROPPING_EVENT,
                    )
                ],
                project_id=project.id,
                phase_id=m_phase.id,
                field_id=field_1.id,
                acting_user_id="1",
            ),
            call(
                request=app_request,
                events=[
                    FieldEventRequest(
                        id=None,
                        event_values={
                            AttributeTypes.crop_type: "dry_bean",
                            AttributeTypes.crop_usage: crop_event_2.crop_usage,
                            AttributeTypes.planting_date: crop_event_2.get_interval_start_or_occurred_at(),
                            AttributeTypes.harvest_date: crop_event_2.get_interval_end_or_occurred_at(),
                        },
                        type=EntityEventType.CROPPING_EVENT,
                    )
                ],
                project_id=project.id,
                phase_id=m_phase.id,
                field_id=field_2.id,
                acting_user_id="1",
            ),
        ]
    )
    mock_update_no_practice_observations_for_event.assert_has_calls(
        [
            call(
                event_id=str(harvesting_id_1),
                no_practice_observations={EntityEventType.TILLAGE_EVENT: True},
                field=ANY,
                acting_user_id="1",
                owner_user_id=str(user.id),
                ses_client=ANY,
            ),
        ]
    )


@patch("ui.projects.field_events.monitoring_phase_prefill.update_no_practice_observations_for_event")
@patch("ui.projects.field_events.monitoring_phase_prefill.create_update_field_events")
@patch("ui.projects.field_events.monitoring_phase_prefill.get_entity_events_for_project_stages")
@patch.object(Client, "fetch_events_for_fields")
async def test_prefill_monitoring_phase_commodity_crops_for_project_existing_events(
    mock_fetch_events_for_fields,
    mock_get_entity_events_for_project_stages,
    mock_create_update_field_events,
    mock_update_no_practice_observations_for_event,
    mdl,
    app_request,
    create_cropping_event_data,
    create_interval_data,
):
    program = await mdl.Programs()
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    intended_commodity_crops_stage = await mdl.Stage(
        phase_id=e_phase.id, type_=StageTypes.INTENDED_COMMODITY_CROPS, enabled=True
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(user=user.id, project=project.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    await mdl.FieldFacts(
        facts=[[PracticeChange.cover_crops, PracticeChange.reduced_till]],
        field_id=field_1.id,
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )
    await mdl.FieldFacts(
        facts=[[PracticeChange.cover_crops, PracticeChange.reduced_till]],
        field_id=field_1.id,
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )

    crop_event_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field_1.id,
            interval=create_interval_data(
                start=datetime(2025, 1, 1, tzinfo=timezone.utc), end=datetime(2025, 6, 1, tzinfo=timezone.utc)
            ),
            crop_usage=CropUsage.COMMODITY,
            crop_type="beans",
            reductions=[],
        )
    )
    crop_event_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field_2.id,
            interval=create_interval_data(
                start=datetime(2025, 1, 1, tzinfo=timezone.utc), end=datetime(2025, 6, 1, tzinfo=timezone.utc)
            ),
            crop_usage=CropUsage.COMMODITY,
            crop_type="beans",
            reductions=[],
        )
    )
    mock_get_entity_events_for_project_stages.return_value = {
        PhaseTypes.ENROLMENT: {StageTypes.INTENDED_COMMODITY_CROPS: [crop_event_1, crop_event_2]}
    }

    mock_fetch_events_for_fields.return_value = {
        field_1.md5: [
            # crop matches intended crop
            Event(
                id=str(uuid.uuid4()),
                harvest_activity=HarvestActivity(crops=[HarvestedCrop(crop=Crop(id=CropId.CROP_ID_DRY_BEAN))]),
            )
        ],
        field_2.md5: [
            # crop does not match intended crop
            Event(
                id=str(uuid.uuid4()),
                harvest_activity=HarvestActivity(crops=[HarvestedCrop(crop=Crop(id=CropId.CROP_ID_CORN))]),
            )
        ],
    }

    await prefill_monitoring_phase_commodity_crops_for_project(
        request=app_request,
        program_id=program.id,
        project_id=project.id,
        intended_commodity_crops_stage_id=intended_commodity_crops_stage.id,
        m_phase_id=m_phase.id,
        acting_user_id="1",
    )

    mock_create_update_field_events.assert_has_calls(
        [
            call(
                request=app_request,
                events=[
                    FieldEventRequest(
                        id=None,
                        event_values={
                            AttributeTypes.crop_type: "dry_bean",
                            AttributeTypes.crop_usage: crop_event_2.crop_usage,
                            AttributeTypes.planting_date: crop_event_2.get_interval_start_or_occurred_at(),
                            AttributeTypes.harvest_date: crop_event_2.get_interval_end_or_occurred_at(),
                        },
                        type=EntityEventType.CROPPING_EVENT,
                    )
                ],
                project_id=project.id,
                phase_id=m_phase.id,
                field_id=field_2.id,
                acting_user_id="1",
            ),
        ]
    )
    mock_update_no_practice_observations_for_event.assert_not_called()


@pytest.mark.parametrize(
    "cover_crop_practice_change,expected_event_values",
    [
        (
            PracticeChange.cover_crops,
            {AttributeTypes.crop_usage: CropUsage.COVER},
        ),
        (
            PracticeChange.basic_cover_crops,
            {
                AttributeTypes.crop_type: RegrowCropName.basic_cover_crop,
                AttributeTypes.crop_usage: CropUsage.COVER,
            },
        ),
        (
            PracticeChange.premium_cover_crops,
            {
                AttributeTypes.crop_type: RegrowCropName.premium_cover_crop_mix,
                AttributeTypes.crop_usage: CropUsage.COVER,
            },
        ),
    ],
)
@patch("ui.projects.field_events.monitoring_phase_prefill.uuid")
async def test_prefill_monitoring_phase_cover_crops_for_field(
    mock_uuid, cover_crop_practice_change, expected_event_values, mdl
):
    test_uuid = uuid.uuid4()
    mock_uuid.uuid4.return_value = test_uuid

    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1), reporting_period_end_date=datetime(2025, 12, 31)
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    cultivation_cycle = CultivationCycleResponse(
        id=CultivationCycleResponseId(
            crop_event_id=CroppingIDs(),
            crop_type=None,
            start_date=datetime(2024, 6, 2, tzinfo=timezone.utc),
            end_date=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
            harvest_year=2025,
            phase_id=phase.id,
            stage_id=stage.id,
        ),
        start=datetime(2024, 6, 2, tzinfo=timezone.utc),
        end=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
        events=[
            FieldEvent(
                event_values={AttributeTypes.crop_usage: CropUsage.COMMODITY}, type=EntityEventType.CROPPING_EVENT
            )
        ],
        no_practice_observations=NoPracticeObservations(),
    )
    res = prefill_monitoring_phase_cover_crops_or_tillage_for_field(
        field_id=field.id,
        cultivation_cycles=[cultivation_cycle],
        stage_type=stage.type_,
        program=program,
        field_id_to_stage_completion={field.id: NarrowestCompletionStatus.incomplete},
        field_id_to_assigned_practices={field.id: [cover_crop_practice_change, PracticeChange.reduced_till]},
    )
    assert res == [
        CultivationCycleResponse(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2025,
                index=1,
                phase_id=phase.id,
                stage_id=stage.id,
            ),
            start=datetime(2025, 6, 2, tzinfo=timezone.utc),
            end=datetime(2025, 6, 3, 23, 59, tzinfo=timezone.utc),
            events=[
                FieldEvent(
                    id=CroppingIDs(harvesting_id=test_uuid),
                    event_values=expected_event_values,
                    type=EntityEventType.CROPPING_EVENT,
                    is_prefilled=True,
                )
            ],
            no_practice_observations=NoPracticeObservations(),
            contains_prefilled_monitoring_phase_events=True,
        ),
        cultivation_cycle,
    ]


@pytest.mark.parametrize(
    "tillage_practice_change,expected_event_values",
    [
        (
            PracticeChange.reduced_till,
            {AttributeTypes.tillage_practice: TillagePractice.reduced_till},
        ),
        (
            PracticeChange.conventional_till,
            {AttributeTypes.tillage_practice: TillagePractice.conventional_till},
        ),
    ],
)
@patch("ui.projects.field_events.monitoring_phase_prefill.uuid")
async def test_prefill_monitoring_phase_tillage_for_field(
    mock_uuid, tillage_practice_change, expected_event_values, mdl
):
    test_uuid = uuid.uuid4()
    mock_uuid.uuid4.return_value = test_uuid

    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1), reporting_period_end_date=datetime(2025, 12, 31)
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    cultivation_cycle = CultivationCycleResponse(
        id=CultivationCycleResponseId(
            crop_event_id=CroppingIDs(),
            crop_type=None,
            start_date=datetime(2024, 6, 2, tzinfo=timezone.utc),
            end_date=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
            harvest_year=2025,
            phase_id=phase.id,
            stage_id=stage.id,
        ),
        start=datetime(2024, 6, 2, tzinfo=timezone.utc),
        end=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
        events=[],
        no_practice_observations=NoPracticeObservations(),
    )
    res = prefill_monitoring_phase_cover_crops_or_tillage_for_field(
        field_id=field.id,
        cultivation_cycles=[cultivation_cycle],
        stage_type=stage.type_,
        program=program,
        field_id_to_stage_completion={field.id: NarrowestCompletionStatus.incomplete},
        field_id_to_assigned_practices={field.id: [PracticeChange.cover_crops, tillage_practice_change]},
    )
    assert res == [
        CultivationCycleResponse(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2025,
                index=1,
                phase_id=phase.id,
                stage_id=stage.id,
            ),
            start=datetime(2025, 6, 2, tzinfo=timezone.utc),
            end=datetime(2025, 6, 3, 23, 59, tzinfo=timezone.utc),
            events=[
                FieldEvent(
                    id=test_uuid,
                    event_values=expected_event_values,
                    type=EntityEventType.TILLAGE_EVENT,
                    is_prefilled=True,
                )
            ],
            no_practice_observations=NoPracticeObservations(),
            contains_prefilled_monitoring_phase_events=True,
        ),
        cultivation_cycle,
    ]


@pytest.mark.parametrize(
    "stage_type,existing_event_values,existing_event_type",
    [
        (StageTypes.CROP_EVENTS, {AttributeTypes.crop_usage: CropUsage.COVER}, EntityEventType.CROPPING_EVENT),
        (
            StageTypes.TILLAGE_EVENTS,
            {AttributeTypes.tillage_practice: TillagePractice.conventional_till},
            EntityEventType.TILLAGE_EVENT,
        ),
    ],
)
async def test_prefill_monitoring_phase_cover_crops_or_tillage_for_field_existing_events(
    stage_type, existing_event_values, existing_event_type, mdl
):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1), reporting_period_end_date=datetime(2025, 12, 31)
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(phase_id=phase.id, type_=stage_type, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    cultivation_cycle = CultivationCycleResponse(
        id=CultivationCycleResponseId(
            crop_event_id=CroppingIDs(),
            crop_type=None,
            start_date=datetime(2024, 6, 2, tzinfo=timezone.utc),
            end_date=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
            harvest_year=2025,
            phase_id=phase.id,
            stage_id=stage.id,
        ),
        start=datetime(2024, 6, 2, tzinfo=timezone.utc),
        end=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
        events=[FieldEvent(event_values=existing_event_values, type=existing_event_type)],
        no_practice_observations=NoPracticeObservations(),
    )
    res = prefill_monitoring_phase_cover_crops_or_tillage_for_field(
        field_id=field.id,
        cultivation_cycles=[cultivation_cycle],
        stage_type=stage_type,
        program=program,
        field_id_to_stage_completion={field.id: NarrowestCompletionStatus.incomplete},
        field_id_to_assigned_practices={field.id: [PracticeChange.cover_crops, PracticeChange.reduced_till]},
    )
    assert res == [cultivation_cycle]


@patch("ui.projects.field_events.monitoring_phase_prefill.uuid")
async def test_prefill_monitoring_phase_cover_crops_or_tillage_for_field_existing_events_outside_reporting_period(
    mock_uuid, mdl
):
    test_uuid = uuid.uuid4()
    mock_uuid.uuid4.return_value = test_uuid

    program = await mdl.Programs(
        reporting_period_start_date=datetime(2026, 1, 1), reporting_period_end_date=datetime(2026, 12, 31)
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    cultivation_cycle = CultivationCycleResponse(
        id=CultivationCycleResponseId(
            crop_event_id=CroppingIDs(),
            crop_type=None,
            start_date=datetime(2024, 6, 2, tzinfo=timezone.utc),
            end_date=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
            harvest_year=2025,
            phase_id=phase.id,
            stage_id=stage.id,
        ),
        start=datetime(2024, 6, 2, tzinfo=timezone.utc),
        end=datetime(2025, 6, 1, 23, 59, tzinfo=timezone.utc),
        events=[
            FieldEvent(
                event_values={AttributeTypes.crop_usage: CropUsage.COVER},
                type=EntityEventType.CROPPING_EVENT,
            ),
        ],
        no_practice_observations=NoPracticeObservations(),
    )
    res = prefill_monitoring_phase_cover_crops_or_tillage_for_field(
        field_id=field.id,
        cultivation_cycles=[cultivation_cycle],
        stage_type=StageTypes.CROP_EVENTS,
        program=program,
        field_id_to_stage_completion={field.id: NarrowestCompletionStatus.incomplete},
        field_id_to_assigned_practices={field.id: [PracticeChange.cover_crops, PracticeChange.reduced_till]},
    )
    assert res == [
        CultivationCycleResponse(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2025,
                index=1,
                phase_id=phase.id,
                stage_id=stage.id,
            ),
            start=datetime(2025, 6, 2, tzinfo=timezone.utc),
            end=datetime(2025, 6, 3, 23, 59, tzinfo=timezone.utc),
            events=[
                FieldEvent(
                    id=CroppingIDs(harvesting_id=test_uuid),
                    event_values={AttributeTypes.crop_usage: CropUsage.COVER},
                    type=EntityEventType.CROPPING_EVENT,
                    is_prefilled=True,
                )
            ],
            no_practice_observations=NoPracticeObservations(),
            contains_prefilled_monitoring_phase_events=True,
        ),
        cultivation_cycle,
    ]
