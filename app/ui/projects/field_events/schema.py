import json
from datetime import date
from typing import Any, Optional
from uuid import UUID

from pydantic import BaseModel, Field as Pydantic<PERSON>ield, validator

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from entity_events.events.entity_event import EntityEvent, EventIdType
from entity_events.events.enums import EntityEventType
from entity_events.events.schema import CroppingIDs
from entity_events.validation.annotations import (
    ValidationErrorResponse,
    ValidationResultsResponse,
)
from fields.schema import EventValue
from phases.enums import AttributeTypes
from ui.projects.field_events.enums import CopyFieldEventsMode


# Events derrived from values stored in the Structured Events Service
class FieldEvent(BaseModel):
    id: EventIdType = None
    event_values: dict[AttributeTypes, Any | None]
    type: EntityEventType
    is_prefilled: bool = False

    @classmethod
    def from_event(cls, event: EntityEvent, event_type: str) -> "FieldEvent":

        return cls(
            id=event.id,
            type=event_type,
            event_values=event.to_ui_dict(),
        )


class FieldEventRequest(FieldEvent):
    id: EventIdType = None

    @validator("id", pre=True, always=True)
    def parse_event_id(cls, value: Optional[str] | Optional[UUID]) -> Optional[UUID | CroppingIDs]:
        """Parse and validate the EventIdType."""

        if value is None:
            return None
        if isinstance(value, UUID):
            return value
        if isinstance(value, dict):
            return CroppingIDs(**value)
        if isinstance(value, str):
            try:
                return UUID(value)
            except ValueError:
                try:
                    return CroppingIDs(**json.loads(value))
                except json.JSONDecodeError:
                    raise ValueError("Invalid value for EventIdType")
        raise ValueError("Invalid value for EventIdType")


# BULK UPDATE FIELD EVENTS


class AdminBulkUpdateFieldEventRequest(FieldEventRequest):
    project_id: int
    phase_id: int
    field_id: int


class AdminBulkUpdateFieldEventsRequest(BaseModel):
    events: list[AdminBulkUpdateFieldEventRequest]


class AdminBulkUpdateFieldResponse(AdminBulkUpdateFieldEventRequest):
    succeeded: bool
    error_detail: str | dict[str, ValidationErrorResponse | ValidationResultsResponse] | None = None
    status_code: int | None = None


class AdminBulkUpdateFieldEventsResponse(BaseModel):
    events: list[AdminBulkUpdateFieldResponse]
    total: int
    succeeded: int
    failed: int


# COPY FIELD EVENTS
class BulkCopyEventRequestBase(BaseModel):
    source_event_id: EventIdType
    target_field_id: int

    @validator("source_event_id", pre=True, always=True)
    def parse_source_event_id(
        cls, value: Optional[str] | Optional[UUID] | Optional[dict]
    ) -> Optional[UUID | CroppingIDs]:
        """Parse and validate the source event ID."""
        if value is None:
            return None
        if isinstance(value, UUID):
            return value
        if isinstance(value, CroppingIDs):
            return value
        if isinstance(value, dict):
            return CroppingIDs(**value)
        if isinstance(value, str):
            try:
                return UUID(value)
            except ValueError:
                try:
                    return CroppingIDs(**json.loads(value))
                except json.JSONDecodeError:
                    raise ValueError("Invalid value for source_event_id")
        raise ValueError("Invalid value for source_event_id")


class BulkCopyEventRequest(BulkCopyEventRequestBase):
    target_stage_type: str


class AdminBulkCopyEventRequest(BulkCopyEventRequestBase):
    target_phase_id: int
    target_stage_id: int
    target_project_id: int


class BulkCopyEventsRequest(BaseModel):
    events: list[BulkCopyEventRequest]
    copy_mode: CopyFieldEventsMode = CopyFieldEventsMode.COPY


class AdminBulkCopyEventsRequest(BulkCopyEventsRequest):
    events: list[AdminBulkCopyEventRequest]


class BulkCopyEventResponseBase(BaseModel):
    new_event_id: str | CroppingIDs | None = None
    new_event: FieldEvent | None = None
    succeeded: bool
    error_detail: str | dict[str, ValidationErrorResponse | ValidationResultsResponse] | None = None
    status_code: int | None = None


class BulkCopyEventResponse(BulkCopyEventResponseBase, BulkCopyEventRequestBase):
    pass


class AdminBulkCopyEventResponse(BulkCopyEventResponseBase, AdminBulkCopyEventRequest):
    pass


class BulkCopyEventsResponse(BaseModel):
    events: list[BulkCopyEventResponse]
    total: int
    succeeded: int
    failed: int


class AdminBulkCopyEventsResponse(BulkCopyEventsResponse):
    events: list[AdminBulkCopyEventResponse]


class NoPracticeObservations(BaseModel):
    tillage_event: bool = False
    application_event: bool = False
    irrigation_event: bool = False

    def to_ses_dict(self, exclude_unset: bool = True) -> dict[str, bool]:
        no_practice_observations_dict = self.dict(exclude_unset=exclude_unset)
        ses_dict: dict[str, bool] = {}
        if tillage_event := no_practice_observations_dict.get("tillage_event"):
            ses_dict[EntityEventType.TILLAGE_EVENT] = tillage_event
        if application_event := no_practice_observations_dict.get("application_event"):
            ses_dict[EntityEventType.APPLICATION_EVENT] = application_event
        if irrigation_event := no_practice_observations_dict.get("irrigation_event"):
            ses_dict[EntityEventType.IRRIGATION_EVENT] = irrigation_event
        return ses_dict


class NoPracticeObservationRequest(BaseModel):
    no_practice_observation_value: bool


class BackfillNoPracticeObservationRequest(BaseModel):
    project_ids: list[int] | None = None
    no_practice_observation_type: EntityEventType


class CultivationCycleResponseId(CultivationCycleId):
    # UI Cultivation cycles require a uniqueId per phase/stage to ensure events are cached to the correct phase/stage context
    phase_id: int
    stage_id: int | None = None


class CultivationCycleResponse(CultivationCycle):
    id: CultivationCycleResponseId
    events: list[FieldEvent] = PydanticField(default_factory=list)
    no_practice_observations: NoPracticeObservations
    crop_event_is_locked: bool = False
    participates_in_phase_completion: bool | None = None
    contains_prefilled_monitoring_phase_events: bool = False


class CultivationCycleResult(CultivationCycle):
    id: CultivationCycleResponseId
    events: list[EntityEvent]
    no_practice_observations: NoPracticeObservations
    crop_event_is_locked: bool = False
    participates_in_phase_completion: bool | None = None
    contains_prefilled_monitoring_phase_events: bool = False


# Events derrived from Legacy MRV_VALUES table
class FieldEventFromMRVValues(BaseModel):
    id: EventIdType = None
    event_values: list[EventValue] = PydanticField(min_items=1)
    type: Optional[EntityEventType] = None

    @classmethod
    def from_event(cls, event: EntityEvent, event_type: str) -> "FieldEventFromMRVValues":
        return cls(
            id=event.id,
            type=event_type,
            event_values=[EventValue(attribute_type=key, value=value) for key, value in event.to_ui_dict().items()],
        )


class CultivationCycleFromMRVValues(BaseModel):
    id: str  # noqa: A003, VNE003
    start_date: date
    end_date: date
    events: list[FieldEventFromMRVValues] = PydanticField(default_factory=list)


class FrozenCroppingIDs(CroppingIDs):
    """A CroppingIDs object that is frozen, to be used as a dictionary key"""

    class Config:
        frozen = True

    @classmethod
    def from_cropping_ids(cls, cropping_ids: CroppingIDs) -> "FrozenCroppingIDs":
        return cls(**cropping_ids.dict())
