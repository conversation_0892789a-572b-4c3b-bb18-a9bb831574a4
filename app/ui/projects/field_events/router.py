import json
from collections import defaultdict
from typing import Any, List, Tuple
from uuid import UUID

import elasticapm
from fastapi import APIRouter, Depends, HTTPException, Query
from ses_client.client import Client
from ses_client.search import Filter
from starlette import status
from starlette.requests import Request

from config import get_settings
from domain_event_bus.domain_event_bus import event_bus
from domain_event_bus.domain_events import (
    BulkFieldEventsChanged,
    BulkProjectEventsChanged,
    FieldEventsChanged,
)
from entity_events.events.schema import CroppingIDs
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from phases.dataclasses import DateRange
from phases.db import get_phase_by_id
from phases.enums import StageTypes
from phases.model import Phases
from projects.db import (
    get_owner_user_id_for_project,
    get_program_id_by_project_id,
    get_project_user_id,
)
from root_crud import get
from ses_integration.constants import (
    STAGE_TYPE_TO_ENTITY_EVENT,
    STAGE_TYPE_TO_SES_EVENT,
)
from ses_integration.db import (
    get_locked_event_ids_for_user,
)
from ses_integration.event_associations import (
    are_any_events_locked_by_phase_association,
    delete_event_associations,
)
from ses_integration.no_practice_observations import (
    get_and_validate_no_practice_observation_stage_type_by_stage_id,
    update_no_practice_observations_for_event,
)
from ui.projects.field_events.methods import (
    build_field_cultivation_cycles_for_response,
    copy_field_events,
    create_update_field_events,
    get_event_query_range_for_reporting_period,
)
from ui.projects.field_events.paths import (
    bulk_update_field_events,
    field_cultivation_cycle,
    field_events,
    field_events_meta,
    fields_cultivation_cycles,
    ui_field_events_base,
    ui_project_phase_base,
    ui_project_phase_stage_base,
)
from ui.projects.field_events.schema import (
    AdminBulkCopyEventRequest,
    AdminBulkCopyEventResponse,
    AdminBulkCopyEventsRequest,
    AdminBulkCopyEventsResponse,
    AdminBulkUpdateFieldEventRequest,
    AdminBulkUpdateFieldEventsRequest,
    AdminBulkUpdateFieldEventsResponse,
    AdminBulkUpdateFieldResponse,
    BulkCopyEventResponse,
    BulkCopyEventsRequest,
    BulkCopyEventsResponse,
    CultivationCycleResponse,
    CultivationCycleResponseId,
    FieldEvent,
    FieldEventRequest,
    NoPracticeObservationRequest,
)
from ui.projects.methods import (
    can_bypass_event_lock,
    extract_acting_user_id,
    get_field_with_parent_project,
    get_project_fields_by_optional_id,
    get_project_program_phase_and_stage,
)


class EventNotFoundError(Exception):
    pass


logger = get_logger(__name__)

settings = get_settings()

ui_project_phase_router = APIRouter(prefix=ui_project_phase_base)
ui_project_phase_stage_router = APIRouter(prefix=ui_project_phase_stage_base)
ui_field_events_router = APIRouter(prefix=ui_field_events_base)

ses_client = Client(
    settings.SES_INTERNAL_URL_BASE,
    settings.SES_SEARCH_INTERNAL_URL_BASE,
    settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE.rstrip("/"),
)


@elasticapm.async_capture_span()
@ui_project_phase_router.delete(
    path=field_events,  # ui/projects/{project_id}/phases/{phase_id}/fields/{field_id}/events
    status_code=status.HTTP_200_OK,
    description="Delete field event",
    dependencies=[Depends(Permissions([Permission.DELETE_PROJECT_FIELD_EVENTS]))],
)
async def delete_field_event(
    request: Request,
    project_id: int,
    field_id: int,
    phase_id: int,
    body: FieldEventRequest,
) -> list[str]:
    field_event = body
    field = await get_field_with_parent_project(request, project_id, field_id)
    if field is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Field does exist for project id {project_id}"
        )
    # in a further version we'll use this as the "active user"
    # request.headers.get("fs-user-id")
    owner_user_id = await get_owner_user_id_for_project(request, project_id)

    if isinstance(field_event.id, CroppingIDs):
        event_ids = field_event.id.get_ids()
    else:
        event_ids = [str(field_event.id)]

    program_id = await get_program_id_by_project_id(request, project_id)
    # user with BYPASS_PROJECT_FIELD_EVENT_LOCK permission can bypass the lock
    bypass_lock = await can_bypass_event_lock(request, program_id=program_id)

    if not bypass_lock:
        event_create_update_locked_by_phase = False

        for event_id in event_ids:
            event_create_update_locked_by_phase = await are_any_events_locked_by_phase_association(
                request, event_ids=[UUID(event_id)], acting_phase_id=phase_id, user_id=int(owner_user_id)
            )

            if event_create_update_locked_by_phase:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Event {event_id} is locked and cannot be edited at this time",
                )

    deleted_ids: list[UUID] = []
    try:
        for event_id in event_ids:
            res = await ses_client.delete_or_archive_field_event_for_user(
                field_id=field.md5, event_id=event_id, user_id=owner_user_id
            )
            if res and (deleted_id := getattr(res, "id", None)):
                deleted_ids.append(UUID(deleted_id))
            else:
                raise EventNotFoundError(f"No SES event to delete for id: {event_id}")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    finally:
        # clean-up and handle anything we did manage to delete
        if deleted_ids:
            await delete_event_associations(request=request, event_ids=deleted_ids, owner_user_id=owner_user_id)
            await event_bus.publish(
                FieldEventsChanged(program_id=program_id, phase_id=phase_id, project_id=project_id, field_id=field.id),
                request,
            )
    return [str(del_id) for del_id in deleted_ids]


@elasticapm.async_capture_span()
@ui_project_phase_router.post(
    field_events,  # ui/projects/{project_id}/phases/{phase_id}/fields/{field_id}/events
    status_code=status.HTTP_200_OK,
    description="Create or update a field event",
    dependencies=[Depends(Permissions([Permission.CREATE_UPDATE_EVENT]))],
)
async def create_update_field_event(
    request: Request,
    field_id: int,
    phase_id: int,
    project_id: int,
    body: FieldEventRequest,
) -> FieldEvent:
    acting_user_id = extract_acting_user_id(request=request)

    # Use create_update_field_events to handle the event processing
    results = await create_update_field_events(
        request=request,
        events=[body],
        project_id=project_id,
        phase_id=phase_id,
        field_id=field_id,
        acting_user_id=acting_user_id,
    )

    # Extract the single result
    if not results:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No result returned from event processing",
        )

    result = results[0]

    if not result.succeeded:
        raise HTTPException(status_code=result.status_code, detail=result.error_detail)

    program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
    await event_bus.publish(
        FieldEventsChanged(
            program_id=program_id,
            phase_id=phase_id,
            project_id=project_id,
            field_id=field_id,
        ),
        request,
    )

    return result


@elasticapm.async_capture_span()
@ui_project_phase_stage_router.post(
    field_cultivation_cycle + "/no_practice_observation",
    # ui/projects/{project_id}/phases/{phase_id}/stages/{stage_id}/fields/{field_id}/cultivation_cycles/{cultivation_cycle_id}/no_practice_observation
    status_code=status.HTTP_200_OK,
    description="Create or update no practice observation for harvest event",
    dependencies=[Depends(Permissions([Permission.UPDATE_PROJECT_FIELD_EVENTS]))],
)
async def create_update_no_practice_observation(
    request: Request,
    project_id: int,
    phase_id: int,
    stage_id: int,
    field_id: int,
    cultivation_cycle_id: str,
    body: NoPracticeObservationRequest,
) -> CultivationCycleResponse:
    field = await get_field_with_parent_project(request, project_id, field_id)
    if field is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field does not exist for project id {project_id}",
        )

    cultivation_cycle_id_obj = CultivationCycleResponseId.parse_obj(json.loads(cultivation_cycle_id))
    event_id = (
        cultivation_cycle_id_obj.crop_event_id.harvesting_id or cultivation_cycle_id_obj.crop_event_id.fallow_id
        if isinstance(cultivation_cycle_id_obj.crop_event_id, CroppingIDs)
        else None
    )
    if event_id is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="NoPracticeObservation cannot be set on a cultivation cycle not associated with a commodity crop or fallow period",
        )
    acting_user_id = extract_acting_user_id(request=request)
    owner_user_id = await get_owner_user_id_for_project(request, project_id)

    program_id = await get_program_id_by_project_id(request, project_id)
    # user with BYPASS_PROJECT_FIELD_EVENT_LOCK permission can bypass the lock
    bypass_lock = await can_bypass_event_lock(request, program_id=program_id)

    npo_attached_event_locked = False
    if not bypass_lock:
        npo_attached_event_locked = await are_any_events_locked_by_phase_association(
            request, acting_phase_id=phase_id, event_ids=[event_id], user_id=int(owner_user_id)
        )
    if npo_attached_event_locked:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Event {event_id} is locked and cannot be edited at this time",
        )

    stage_type = await get_and_validate_no_practice_observation_stage_type_by_stage_id(
        request=request, stage_id=stage_id
    )
    no_practice_observation_type = STAGE_TYPE_TO_ENTITY_EVENT[stage_type][0].__name__
    if body.no_practice_observation_value is True:
        conflicting_events = await ses_client.fetch_events_for_fields(
            field_ids=[field.md5],
            search_filter=Filter(
                event_types=STAGE_TYPE_TO_SES_EVENT[stage_type],
                interval_from=cultivation_cycle_id_obj.start_date,
                interval_to=cultivation_cycle_id_obj.end_date,
                user_ids=[owner_user_id],
            ),
        )
        if conflicting_events.get(field.md5):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"NoPracticeObservation cannot be set because there are {no_practice_observation_type} events in the cultivation cycle",
            )

    updated_no_practice_observations = await update_no_practice_observations_for_event(
        event_id=str(event_id),
        no_practice_observations={no_practice_observation_type: body.no_practice_observation_value},
        field=field,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        ses_client=ses_client,
    )

    phase = await get_phase_by_id(request, phase_id)
    await event_bus.publish(
        event=FieldEventsChanged(
            program_id=phase.program_id, phase_id=phase.id, project_id=field.parent_project_id, field_id=field.id
        ),
        request=request,
    )

    return CultivationCycleResponse(
        id=cultivation_cycle_id_obj,
        start=cultivation_cycle_id_obj.start_date,
        end=cultivation_cycle_id_obj.end_date,
        events=[],
        no_practice_observations=updated_no_practice_observations,
    )


# CULTIVATION CYCLES
@elasticapm.async_capture_span()
@ui_project_phase_router.get(
    path=fields_cultivation_cycles,
    # ui/projects/{project_id}/phases/{phase_id}/fields/cultivation_cycles?stage_id?field_id
    status_code=status.HTTP_200_OK,
    description="Get project field cultivation cycles",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_phase_cultivation_cycles(
    request: Request,
    project_id: int,
    phase_id: int,
    stage_id: int | None = None,
    field_id: int | None = None,
    prefill_monitoring_phase: bool = False,
) -> dict[str, list[CultivationCycleResponse]]:
    program, phase, stage = await get_project_program_phase_and_stage(request, project_id, phase_id, stage_id)
    acting_user_id = extract_acting_user_id(request=request)
    owner_user_id = await get_owner_user_id_for_project(request, project_id)
    fields = await get_project_fields_by_optional_id(request=request, project_id=project_id, field_id=field_id)
    return await build_field_cultivation_cycles_for_response(
        request=request,
        program=program,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        phase=phase,
        stage=stage,
        fields=fields,
        prefill_monitoring_phase=prefill_monitoring_phase,
    )


@ui_project_phase_router.get(
    path="/data_collection_date_range",  # ui/projects/{project_id}/phases/{phase_id}/data_collection_date_range
    status_code=status.HTTP_200_OK,
    description="Get query range used to fetch the current phase data",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT]))],
)
async def get_phase_data_collection_date_range(
    request: Request,
    project_id: int,
    phase_id: int,
) -> DateRange:
    program, phase, _ = await get_project_program_phase_and_stage(request, project_id, phase_id, None)
    from_date, to_date = get_event_query_range_for_reporting_period(program=program, phase_type=phase.type_)

    return DateRange(start_date=from_date.date(), end_date=to_date.date())


#  Field Event Meta Data
@ui_project_phase_router.get(
    path=field_events_meta,  # ui/projects/{project_id}/phases/{phase_id}/events/meta
    status_code=status.HTTP_200_OK,
    description="Get field event meta data by phase",
    dependencies=[Depends(Permissions([Permission.GET_PROJECT_FIELD_EVENTS]))],
)
async def get_field_event_meta_data(
    request: Request,
    project_id: int,
    phase_id: int,
) -> dict[str, dict[str, Any]]:
    project_user_id = await get_project_user_id(request, project_id)
    phase: Phases = (await get.get(request=request, orm_type=Phases, id_field=Phases.id, ids=[phase_id]))[0]
    return {
        str(event_id): {"is_locked": True}
        for event_id in await get_locked_event_ids_for_user(
            request=request, owner_user_id=project_user_id, acting_phase_id=phase.id
        )
    }


# BULK UPDATE
@elasticapm.async_capture_span()
@ui_project_phase_router.post(
    path=bulk_update_field_events,  # ui/projects/{project_id}/phases/{phase_id}/fields/{field_id}/bulk_update
    status_code=status.HTTP_200_OK,
    description="Bulk create or update field events",
    dependencies=[Depends(Permissions([Permission.CREATE_UPDATE_EVENT]))],
)
async def bulk_create_update_field_events(
    request: Request,
    body: list[FieldEventRequest],
    project_id: int,
    phase_id: int,
    field_id: int,
) -> list[FieldEvent]:
    """
    Bulk create or update field events across single project phase.
    Always return status 200, with a response body containing the results of each operation.
    """
    acting_user_id = extract_acting_user_id(request=request)

    results: list[FieldEvent] = []
    try:
        group_results = await create_update_field_events(
            request=request,
            events=body,
            project_id=project_id,
            phase_id=phase_id,
            field_id=field_id,
            acting_user_id=acting_user_id,
        )
        results.extend(group_results)

    except Exception as e:
        logger.error(f"Error processing group (project={project_id}, phase={phase_id}, field={field_id}): {str(e)}")
        for field_event in body:
            results.append(
                FieldEvent.from_event(
                    event=field_event,
                    event_type=field_event.type,
                )
            )

    program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
    await event_bus.publish(
        event=FieldEventsChanged(program_id=program_id, phase_id=phase_id, project_id=project_id, field_id=field_id),
        request=request,
    )

    return results


@elasticapm.async_capture_span()
@ui_project_phase_router.post(
    path="/copy_field_events",  # ui/projects/{project_id}/phases/{phase_id}/copy_field_events
    status_code=status.HTTP_200_OK,
    description="Bulk copy field events",
    dependencies=[Depends(Permissions([Permission.CREATE_UPDATE_EVENT]))],
)
async def bulk_copy_field_events(
    request: Request,
    body: BulkCopyEventsRequest,
    project_id: int,
    phase_id: int,
    include_new_field_event: bool = Query(True, description="Include new field event data in response"),
) -> BulkCopyEventsResponse:
    """
    Bulk copy field events across fields within a phase.
    Uses ses_client.bulk_copy_events efficiently to copy multiple events at once.
    Always return status 200, with a response body containing the results of each operation.
    """
    events = body.events
    if len(events) == 0:
        return BulkCopyEventsResponse(events=[], total=0, succeeded=0, failed=0)

    program, phase, _ = await get_project_program_phase_and_stage(request, project_id, phase_id, None)

    # Group events by (stage_type, field_id)
    grouped_events: dict[Tuple[StageTypes, int], List[AdminBulkCopyEventRequest]] = defaultdict(list)
    for event in body.events:
        key = (
            event.target_stage_type,
            event.target_field_id,
        )
        grouped_events[key].append(event)

    results: list[AdminBulkCopyEventResponse] = []

    for (
        target_stage_type,
        target_field_id,
    ), events_group in grouped_events.items():
        try:
            stage = next((stage for stage in phase.stages if stage.type_ == target_stage_type), None)
            field = await get_field_with_parent_project(request, project_id, target_field_id)
            if field is None or stage is None:
                for event in events_group:
                    results.append(
                        BulkCopyEventResponse(
                            source_event_id=event.source_event_id,
                            target_field_id=target_field_id,
                            new_event_id=None,
                            new_event=None,
                            succeeded=False,
                            error_detail=f"Destination field {target_field_id} or stage {target_stage_type} does not exist.",
                            status_code=status.HTTP_404_NOT_FOUND,
                        )
                    )
                continue

            group_results = await copy_field_events(
                request=request,
                target_program=program,
                target_phase=phase,
                target_stage=stage,
                target_field=field,
                event_ids=[
                    (
                        event.source_event_id
                        if isinstance(event.source_event_id, CroppingIDs)
                        else str(event.source_event_id)
                    )
                    for event in events_group
                ],
                copy_mode=body.copy_mode,
                include_new_field_event=include_new_field_event,
            )
            results.extend(group_results)

        except Exception as e:
            logger.error(
                f"Error processing group (project={project_id}, phase={phase_id}, field={target_field_id}): {str(e)}"
            )
            for event in events_group:
                results.append(
                    BulkCopyEventResponse(
                        source_event_id=event.source_event_id,
                        target_field_id=target_field_id,
                        new_event_id=None,
                        new_event=None,
                        succeeded=False,
                        error_detail=f"Group processing error: {str(e)}",
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
                )

    # Calculate summary
    total = len(results)
    succeeded = sum(1 for r in results if r.succeeded)
    failed = total - succeeded

    return BulkCopyEventsResponse(events=results, total=total, succeeded=succeeded, failed=failed)


# ADMIN ROUTES
@elasticapm.async_capture_span()
@ui_field_events_router.post(
    path="",  # /ui/field_events
    status_code=status.HTTP_200_OK,
    description="Bulk create or update field events",
    dependencies=[Depends(Permissions([Permission.ADMIN_BULK_UPDATE_FIELD_EVENTS]))],
)
async def admin_bulk_create_update_field_events(
    request: Request,
    body: AdminBulkUpdateFieldEventsRequest,
) -> AdminBulkUpdateFieldEventsResponse:
    """
    Bulk create or update field events across multiple projects, phases, and fields.
    Always return status 200, with a response body containing the results of each operation.
    """
    acting_user_id = extract_acting_user_id(request=request)

    # Group events by (project_id, phase_id, field_id)
    project_ids = set()
    grouped_events: dict[Tuple[int, int, int], List[AdminBulkUpdateFieldEventRequest]] = defaultdict(list)
    for bulk_event in body.events:
        project_ids.add(bulk_event.project_id)
        key = (bulk_event.project_id, bulk_event.phase_id, bulk_event.field_id)
        grouped_events[key].append(bulk_event)

    results: list[AdminBulkUpdateFieldResponse] = []
    for (project_id, phase_id, field_id), events_group in grouped_events.items():
        try:
            group_results = await create_update_field_events(
                request=request,
                events=events_group,
                project_id=project_id,
                phase_id=phase_id,
                field_id=field_id,
                acting_user_id=acting_user_id,
            )
            results.extend(group_results)

        except Exception as e:
            logger.error(f"Error processing group (project={project_id}, phase={phase_id}, field={field_id}): {str(e)}")
            for field_event in events_group:
                results.append(
                    AdminBulkUpdateFieldResponse(
                        project_id=project_id,
                        phase_id=phase_id,
                        field_id=field_id,
                        **field_event.dict(exclude={"project_id", "phase_id", "field_id"}),
                        succeeded=False,
                        error_detail=f"Group processing error: {str(e)}",
                    )
                )

    project_id_list = list(project_ids)
    if len(project_id_list) == 1:
        field_ids = [event_key[2] for event_key in grouped_events.keys()]
        await event_bus.publish(
            event=BulkFieldEventsChanged(project_id=project_id_list[0], field_ids=field_ids),
            request=request,
        )
    elif len(project_id_list) > 1:
        await event_bus.publish(
            event=BulkProjectEventsChanged(project_ids=project_id_list),
            request=request,
        )

    # Calculate summary
    total = len(results)
    succeeded = sum(1 for r in results if r.succeeded)
    failed = total - succeeded

    return AdminBulkUpdateFieldEventsResponse(events=results, total=total, succeeded=succeeded, failed=failed)


@elasticapm.async_capture_span()
@ui_field_events_router.post(
    path="/copy",  # /ui/field_events/copy
    status_code=status.HTTP_200_OK,
    description="Bulk copy field events",
    dependencies=[Depends(Permissions([Permission.ADMIN_BULK_COPY_FIELD_EVENTS]))],
)
async def admin_bulk_copy_field_events(
    request: Request,
    body: AdminBulkCopyEventsRequest,
    include_new_field_event: bool = Query(True, description="Include new field event data in response"),
) -> AdminBulkCopyEventsResponse:
    """
    Bulk copy field events across multiple projects, phases, and fields.
    Uses ses_client.bulk_copy_events efficiently to copy multiple events at once.
    Always return status 200, with a response body containing the results of each operation.
    """
    # Group events by (project_id, phase_id, stage_id, field_id)
    grouped_events: dict[Tuple[int, int, int, int], List[AdminBulkCopyEventRequest]] = defaultdict(list)
    for event in body.events:
        key = (
            event.target_project_id,
            event.target_phase_id,
            event.target_stage_id,
            event.target_field_id,
        )
        grouped_events[key].append(event)

    results: list[AdminBulkCopyEventResponse] = []

    for (
        target_project_id,
        target_phase_id,
        target_stage_id,
        target_field_id,
    ), events_group in grouped_events.items():
        try:
            program, phase, stage = await get_project_program_phase_and_stage(
                request, target_project_id, target_phase_id, target_stage_id
            )
            field = await get_field_with_parent_project(request, target_project_id, target_field_id)
            if field is None:
                for event in events_group:
                    results.append(
                        AdminBulkCopyEventResponse(
                            source_event_id=event.source_event_id,
                            target_field_id=target_field_id,
                            target_phase_id=target_phase_id,
                            target_stage_id=target_stage_id,
                            target_project_id=target_project_id,
                            new_event_id=None,
                            new_event=None,
                            succeeded=False,
                            error_detail=f"Destination field {target_field_id} does not exist.",
                            status_code=status.HTTP_404_NOT_FOUND,
                        )
                    )
                continue

            group_results = await copy_field_events(
                request=request,
                target_program=program,
                target_phase=phase,
                target_stage=stage,
                target_field=field,
                event_ids=[
                    (
                        event.source_event_id
                        if isinstance(event.source_event_id, CroppingIDs)
                        else str(event.source_event_id)
                    )
                    for event in events_group
                ],
                copy_mode=body.copy_mode,
                include_new_field_event=include_new_field_event,
            )

            formatted_results = [
                AdminBulkCopyEventResponse(
                    source_event_id=result.source_event_id,
                    target_field_id=target_field_id,
                    target_phase_id=target_phase_id,
                    target_stage_id=target_stage_id,
                    target_project_id=target_project_id,
                    new_event_id=result.new_event_id,
                    new_event=result.new_event,
                    succeeded=result.succeeded,
                    error_detail=result.error_detail,
                    status_code=result.status_code,
                )
                for result in group_results
            ]
            results.extend(formatted_results)

        except Exception as e:
            logger.error(
                f"Error processing group (project={target_project_id}, phase={target_phase_id}, field={target_field_id}): {str(e)}"
            )
            for event in events_group:
                results.append(
                    AdminBulkCopyEventResponse(
                        source_event_id=event.source_event_id,
                        target_field_id=target_field_id,
                        target_phase_id=target_phase_id,
                        target_stage_id=target_stage_id,
                        target_project_id=target_project_id,
                        new_event_id=None,
                        new_event=None,
                        succeeded=False,
                        error_detail=f"Group processing error: {str(e)}",
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
                )

    # Calculate summary
    total = len(results)
    succeeded = sum(1 for r in results if r.succeeded)
    failed = total - succeeded

    return AdminBulkCopyEventsResponse(events=results, total=total, succeeded=succeeded, failed=failed)
