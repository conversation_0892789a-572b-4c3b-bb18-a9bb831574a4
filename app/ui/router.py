from __future__ import annotations

from fastapi import APIRouter

from projects.router import producer, tags as project_tags
from ui.paths import ui_base
from ui.projects.field_events.router import (
    ui_field_events_router,
    ui_project_phase_router,
    ui_project_phase_stage_router,
)

tags = ["ui"] + project_tags + producer

router = APIRouter(prefix=ui_base)
router.include_router(router=ui_project_phase_router, tags=tags)
router.include_router(router=ui_project_phase_stage_router, tags=tags)
router.include_router(router=ui_field_events_router, tags=tags)
