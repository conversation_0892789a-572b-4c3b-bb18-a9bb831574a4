[{"biofuels_api_enabled": false, "cdl_id": 61, "core_id": 127, "dndc_id": 0, "dndc_name": "fallow", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Fallow", "barren", "Barren", "fallow_idle_cropland", "Fallow/Idle Cropland", "fallow"], "mrv_enabled": false, "regrow_id": 1, "regrow_name": "fallow", "si_enabled": true}, {"biofuels_api_enabled": true, "cdl_id": 1, "core_id": 105, "dndc_id": 1, "dndc_name": "corn, grain", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Dbl Crop WinWht/Corn", "dbl_crop_winwht_corn", "Dbl Crop Oats/Corn", "grain_corn", "Grain Corn", "Dbl Crop Barley/Corn", "dbl_crop_triticale_corn", "dbl_crop_oats_corn", "corn", "Dbl Crop Triticale/Corn", "Grain corn", "Corn", "dbl_crop_barley_corn"], "mrv_enabled": false, "regrow_id": 2, "regrow_name": "corn", "si_enabled": true}, {"biofuels_api_enabled": true, "cdl_id": 24, "core_id": 357, "dndc_id": 2, "dndc_name": "wheat, winter", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["winter_wheat", "wheat_winter", "Winter Wheat"], "mrv_enabled": false, "regrow_id": 3, "regrow_name": "wheat_winter", "si_enabled": true}, {"biofuels_api_enabled": true, "cdl_id": 5, "core_id": 302, "dndc_id": 3, "dndc_name": "soybean", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["soybean", "soybeans", "Soybeans", "Dbl Crop Corn/Soybeans", "dbl_crop_winwht_soybeans", "Dbl Crop WinWht/Soybeans", "dbl_crop_barley_soybeans", "Dbl Crop Barley/Soybeans", "dbl_crop_corn_soybeans"], "mrv_enabled": false, "regrow_id": 4, "regrow_name": "soybean", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 4, "dndc_name": "hay, legume", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["other_hay"], "mrv_enabled": false, "regrow_id": 5, "regrow_name": "other_hay", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 37, "core_id": 161, "dndc_id": 5, "dndc_name": "hay, non-legume", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["other_hay_non_alfalfa", "Hay", "Other Hay/Non Alfalfa", "hay"], "mrv_enabled": false, "regrow_id": 6, "regrow_name": "hay", "si_enabled": false}, {"biofuels_api_enabled": true, "cdl_id": 23, "core_id": 356, "dndc_id": 6, "dndc_name": "wheat, spring", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Spring Wheat", "wheat_spring", "durum_wheat", "dbl_crop_lettuce_durum_wht", "wheat_durum", "Dbl Crop Lettuce/Durum Wht", "spring_wheat", "Durum Wheat"], "mrv_enabled": false, "regrow_id": 7, "regrow_name": "wheat_spring", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 45, "core_id": 315, "dndc_id": 7, "dndc_name": "sugarcane", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Sugarcane", "sugarcane"], "mrv_enabled": false, "regrow_id": 8, "regrow_name": "sugarcane", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 21, "core_id": 16, "dndc_id": 8, "dndc_name": "barley", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["spring_barley", "<PERSON>ley", "winter_barley", "Spring <PERSON>ley", "Dbl Crop Lettuce/Barley", "barley", "<PERSON><PERSON>", "barley_spring", "barley_winter", "dbl_crop_lettuce_barley"], "mrv_enabled": false, "regrow_id": 9, "regrow_name": "barley", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 28, "core_id": 225, "dndc_id": 9, "dndc_name": "oats", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Spring Oats", "oats_spring", "Winter Oats", "Dbl Crop Soybeans/Oats", "oat", "Oats", "spring_oats", "oats", "winter_oats", "Oat", "dbl_crop_soybeans_oats", "oats_winter"], "mrv_enabled": false, "regrow_id": 10, "regrow_name": "oat", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 36, "core_id": 4, "dndc_id": 10, "dndc_name": "alfalfa", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Alfalfa", "alfalfa"], "mrv_enabled": false, "regrow_id": 11, "regrow_name": "alfalfa", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 176, "core_id": 450, "dndc_id": 11, "dndc_name": "grass, annual", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["grass_annual"], "mrv_enabled": false, "regrow_id": 12, "regrow_name": "grass_annual", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 447, "dndc_id": 144, "dndc_name": "grass_perennial", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Grassland/Fallow", "Grassland/Pasture", "grassland_pasture", "grassland", "Grassland", "grass_perennial"], "mrv_enabled": false, "regrow_id": 13, "regrow_name": "grass_perennial", "si_enabled": false}, {"biofuels_api_enabled": true, "cdl_id": 4, "core_id": 299, "dndc_id": 13, "dndc_name": "sorghum, grain", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["dbl_crop_winwht_sorghum", "Dbl Crop WinWht/Sorghum", "Dbl Crop Barley/Sorghum", "dbl_crop_durum_wht_sorghum", "sorghum", "dbl_crop_barley_sorghum", "Dbl Crop Durum Wht/Sorghum", "Sorghum"], "mrv_enabled": false, "regrow_id": 14, "regrow_name": "sorghum", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 2, "core_id": 106, "dndc_id": 14, "dndc_name": "cotton", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["cotton", "dbl_crop_lettuce_cotton", "Cotton", "Dbl Crop Soybeans/Cotton", "dbl_crop_soybeans_cotton", "Dbl Crop WinWht/Cotton", "Dbl Crop Lettuce/Cotton", "dbl_crop_winwht_cotton"], "mrv_enabled": false, "regrow_id": 15, "regrow_name": "cotton", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 15, "dndc_name": "rye, spring", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["spring_rye", "Spring Rye", "rye_spring"], "mrv_enabled": false, "regrow_id": 16, "regrow_name": "rye_spring", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 427, "dndc_id": 16, "dndc_name": "vegetables", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["misc_vegs___fruits", "vegetable", "Vegetables", "misc_vegs__fruits", "Misc Vegs & Fruits", "vegetables"], "mrv_enabled": false, "regrow_id": 17, "regrow_name": "vegetable", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 235, "dndc_id": 17, "dndc_name": "papaya", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 18, "regrow_name": "papaya", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 43, "core_id": 260, "dndc_id": 18, "dndc_name": "potato", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["potato", "potatoes", "Potatoes"], "mrv_enabled": false, "regrow_id": 19, "regrow_name": "potato", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 20, "dndc_id": 19, "dndc_name": "beet", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["beets", "beet", "Beets"], "mrv_enabled": false, "regrow_id": 20, "regrow_name": "beet", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 3, "core_id": 273, "dndc_id": 20, "dndc_name": "rice, paddy", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["rice", "Rice"], "mrv_enabled": false, "regrow_id": 21, "regrow_name": "rice", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 15, "dndc_id": 21, "dndc_name": "banana", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 22, "regrow_name": "banana", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 245, "core_id": 70, "dndc_id": 22, "dndc_name": "celery", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["celery", "Celery"], "mrv_enabled": false, "regrow_id": 23, "regrow_name": "celery", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 10, "core_id": 241, "dndc_id": 23, "dndc_name": "peanut", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["peanut", "Peanut", "Peanuts", "peanuts"], "mrv_enabled": false, "regrow_id": 24, "regrow_name": "peanut", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 24, "dndc_name": "rice, upland", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 25, "regrow_name": "rice_upland", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 34, "core_id": 62, "dndc_id": 160, "dndc_name": "canola", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Spring Canola", "spring_canola", "rape_seed", "<PERSON><PERSON>d", "winter_canola", "Canola", "canola_winter", "rapeseed", "canola", "Winter Canola", "canola_spring"], "mrv_enabled": false, "regrow_id": 26, "regrow_name": "canola", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 11, "core_id": 336, "dndc_id": 26, "dndc_name": "tobacco", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["tobacco", "Tobacco"], "mrv_enabled": false, "regrow_id": 27, "regrow_name": "tobacco", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 29, "core_id": 215, "dndc_id": 27, "dndc_name": "millet", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Millet", "millet"], "mrv_enabled": false, "regrow_id": 28, "regrow_name": "millet", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 6, "core_id": 319, "dndc_id": 28, "dndc_name": "sunflower", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Sunflower", "Sunflowers", "sunflowers", "sunflower"], "mrv_enabled": false, "regrow_id": 29, "regrow_name": "sunflower", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 42, "core_id": 118, "dndc_id": 29, "dndc_name": "beans", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["dry_bean", "beans", "dry_beans", "Dry Beans", "<PERSON>s"], "mrv_enabled": false, "regrow_id": 30, "regrow_name": "dry_bean", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 30, "dndc_name": "rice, deepwater", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 31, "regrow_name": "rice_deepwater", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 49, "core_id": 229, "dndc_id": 31, "dndc_name": "onion", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["onion", "Onion"], "mrv_enabled": false, "regrow_id": 32, "regrow_name": "onion", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 32, "dndc_name": "palm", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 33, "regrow_name": "palm", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 221, "core_id": 312, "dndc_id": 33, "dndc_name": "strawberry", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON>berry", "strawberries", "strawberry", "Strawberries"], "mrv_enabled": false, "regrow_id": 34, "regrow_name": "strawberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 227, "core_id": 182, "dndc_id": 34, "dndc_name": "lettuce", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["lettuce", "Lettuce"], "mrv_enabled": false, "regrow_id": 35, "regrow_name": "lettuce", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 35, "dndc_name": "artichoke", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 36, "regrow_name": "artichoke", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 36, "dndc_name": "flowers", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["flowers", "flower", "Flowers"], "mrv_enabled": false, "regrow_id": 37, "regrow_name": "flower", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 37, "dndc_name": "sprout", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 38, "regrow_name": "sprout", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 38, "dndc_name": "berries", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 39, "regrow_name": "berry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 39, "dndc_name": "truck crops", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 40, "regrow_name": "truck_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 40, "dndc_name": "fruit trees", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 41, "regrow_name": "fruit_tree", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 72, "core_id": 85, "dndc_id": 41, "dndc_name": "citrus", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Citrus", "citrus"], "mrv_enabled": false, "regrow_id": 42, "regrow_name": "citrus", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 69, "core_id": 148, "dndc_id": 42, "dndc_name": "grape", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Vineyard", "grape", "vineyard", "grapes", "Grapes", "Grape"], "mrv_enabled": false, "regrow_id": 43, "regrow_name": "grape", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 387, "core_id": 387, "dndc_id": 43, "dndc_name": "corn, silage", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["Silage corn", "<PERSON><PERSON><PERSON>rn", "sileage_corn", "corn_silage", "Silage Corn", "silage_corn"], "mrv_enabled": false, "regrow_id": 44, "regrow_name": "corn_silage", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 56, "core_id": 166, "dndc_id": 44, "dndc_name": "hops", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Hops", "hops"], "mrv_enabled": false, "regrow_id": 45, "regrow_name": "hop", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 54, "core_id": 338, "dndc_id": 45, "dndc_name": "tomato", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["tomato", "tomatoes", "Tomatoes"], "mrv_enabled": false, "regrow_id": 46, "regrow_name": "tomato", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 46, "dndc_name": "rice, rainfed", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 47, "regrow_name": "rice_rainfed", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 47, "dndc_name": "cover crop, N-fixing", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 48, "regrow_name": "cover_crop_n_fixing", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 33, "core_id": 281, "dndc_id": 48, "dndc_name": "safflower", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["safflower", "Safflower"], "mrv_enabled": false, "regrow_id": 49, "regrow_name": "safflower", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 32, "core_id": 134, "dndc_id": 49, "dndc_name": "flax", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Flaxseed", "Flax", "flax", "flaxseed"], "mrv_enabled": false, "regrow_id": 50, "regrow_name": "flax", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 50, "dndc_name": "sedge", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 51, "regrow_name": "sedge", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 67, "dndc_id": 51, "dndc_name": "cassava", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 52, "regrow_name": "cassava", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 52, "dndc_name": "cattail", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 53, "regrow_name": "cattail", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 214, "core_id": 41, "dndc_id": 53, "dndc_name": "broccoli", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON><PERSON><PERSON>", "broccoli"], "mrv_enabled": false, "regrow_id": 54, "regrow_name": "broccoli", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 54, "dndc_name": "evergreens", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["tree_christmas", "Christmas Trees", "christmas_trees", "evergreen"], "mrv_enabled": false, "regrow_id": 55, "regrow_name": "evergreen", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 243, "core_id": 51, "dndc_id": 55, "dndc_name": "cabbage", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Cabbage", "cabbage"], "mrv_enabled": false, "regrow_id": 56, "regrow_name": "cabbage", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 56, "dndc_name": "onion, green", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 57, "regrow_name": "green_onion", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 35, "core_id": 219, "dndc_id": 57, "dndc_name": "mustard", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["mustard", "Mustard"], "mrv_enabled": false, "regrow_id": 58, "regrow_name": "mustard", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 58, "dndc_name": "tule", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 59, "regrow_name": "tule", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 59, "dndc_name": "moss", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 60, "regrow_name": "moss", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 246, "core_id": 267, "dndc_id": 60, "dndc_name": "radish", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Radishes", "Radishe", "radishe", "Radish", "radish"], "mrv_enabled": false, "regrow_id": 61, "regrow_name": "radish", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 62, "dndc_name": "boreal sedge", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 63, "regrow_name": "boreal_sedge", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 75, "core_id": 5, "dndc_id": 63, "dndc_name": "almond", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON>", "Almonds", "almond", "almonds"], "mrv_enabled": false, "regrow_id": 64, "regrow_name": "almond", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 64, "dndc_name": "nut tree", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 65, "regrow_name": "nut_tree", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 208, "dndc_id": 65, "dndc_name": "melon", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["honeydew_melons", "honeydew_melon", "melon", "Honeydew Mel<PERSON>", "Honeydew Mel<PERSON>"], "mrv_enabled": false, "regrow_id": 66, "regrow_name": "melon", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 66, "dndc_name": "hay, pasture", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 67, "regrow_name": "pasture_hay", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 67, "dndc_name": "hay, small grain", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 68, "regrow_name": "small_grain_hay", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 46, "core_id": 321, "dndc_id": 73, "dndc_name": "potato, sweet", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["sweet_potatoes", "Sweet Potatoes", "sweet_potato"], "mrv_enabled": false, "regrow_id": 69, "regrow_name": "sweet_potato", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 151, "dndc_id": 74, "dndc_name": "beans, green", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 70, "regrow_name": "green_bean", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 66, "core_id": 73, "dndc_id": 78, "dndc_name": "cherries", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Cherry", "cherry"], "mrv_enabled": false, "regrow_id": 71, "regrow_name": "cherry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 180, "dndc_id": 87, "dndc_name": "lemons", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 72, "regrow_name": "lemon", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 331, "dndc_id": 88, "dndc_name": "tea", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 73, "regrow_name": "tea", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 89, "dndc_name": "pea, cover", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 74, "regrow_name": "cover_crop_pea", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 90, "dndc_name": "cover crop, non-N-fixing", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 75, "regrow_name": "cover_crop_non_n_fixing", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 58, "core_id": 86, "dndc_id": 91, "dndc_name": "clover, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Clover", "clover", "Clover/Wildflowers", "clover_wildflowers"], "mrv_enabled": false, "regrow_id": 76, "regrow_name": "clover", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 92, "dndc_name": "sorghum, silage", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 77, "regrow_name": "sorghum_silage", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 27, "core_id": 276, "dndc_id": 93, "dndc_name": "rye, winter, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["winter_rye", "Winter Rye", "rye_winter", "<PERSON><PERSON>", "rye"], "mrv_enabled": false, "regrow_id": 78, "regrow_name": "rye", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 24, "core_id": 357, "dndc_id": 94, "dndc_name": "wheat, winter, cover", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 79, "regrow_name": "cover_wheat_winter", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 224, "core_id": 347, "dndc_id": 95, "dndc_name": "vetch, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["vetch", "<PERSON><PERSON><PERSON>"], "mrv_enabled": false, "regrow_id": 80, "regrow_name": "vetch", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 96, "dndc_name": "lentil, cover", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 81, "regrow_name": "cover_lentil", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 205, "core_id": 343, "dndc_id": 97, "dndc_name": "triticale, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["triticale_winter", "Spring Triticale", "Tri<PERSON>e", "Winter Triticale", "winter_triticale", "triticale_spring", "spring_triticale", "triticale", "Triatcale"], "mrv_enabled": false, "regrow_id": 82, "regrow_name": "triticale", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 98, "dndc_name": "wheatgrass, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 83, "regrow_name": "wheatgrass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 99, "dndc_name": "grass, perennial, C4, calvin", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 84, "regrow_name": "grass_prennnial_c4_calvin", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 248, "core_id": 120, "dndc_id": 100, "dndc_name": "eggplant", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Eggplant", "eggplant"], "mrv_enabled": false, "regrow_id": 85, "regrow_name": "eggplant", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 53, "core_id": 244, "dndc_id": 101, "dndc_name": "pea, grain", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["pea", "Peas", "peas"], "mrv_enabled": false, "regrow_id": 86, "regrow_name": "pea", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 247, "core_id": 345, "dndc_id": 102, "dndc_name": "turnip, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["turnip", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "mrv_enabled": false, "regrow_id": 87, "regrow_name": "turnip", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 39, "core_id": 48, "dndc_id": 103, "dndc_name": "buckwheat, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["buckwheat", "Buckwheat"], "mrv_enabled": false, "regrow_id": 88, "regrow_name": "buckwheat", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 104, "dndc_name": "sunn hemp, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 89, "regrow_name": "sunn_hemp", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 52, "core_id": 181, "dndc_id": 105, "dndc_name": "lentil", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON><PERSON>", "lentils", "lentil"], "mrv_enabled": false, "regrow_id": 90, "regrow_name": "lentil", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 106, "dndc_name": "<PERSON><PERSON> pallescens", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 91, "regrow_name": "grass_thatching", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 107, "dndc_name": "<PERSON><PERSON>a speciosa", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 92, "regrow_name": "desert_needlegrass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 108, "dndc_name": "<PERSON><PERSON> ligularis", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 93, "regrow_name": "poa_ligularis", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 12, "core_id": 320, "dndc_id": 109, "dndc_name": "sweet corn", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["sweet_corn", "Sweet Corn"], "mrv_enabled": false, "regrow_id": 94, "regrow_name": "sweet_corn", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 13, "core_id": 424, "dndc_id": 110, "dndc_name": "pop or orn corn", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["pop_or_orn_corn", "Pop or Orn Corn"], "mrv_enabled": false, "regrow_id": 95, "regrow_name": "pop_or_orn_corn", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 25, "core_id": 426, "dndc_id": 111, "dndc_name": "other small grains", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["other_small_grains", "Other winter cereals", "other_winter_cereal", "Winter cereals", "other_spring_cereals", "other_winter_cereals", "Spring cereals", "other_small_grain", "Other Small Grains", "Other spring cereals", "other_spring_cereal"], "mrv_enabled": false, "regrow_id": 96, "regrow_name": "other_small_grain", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 44, "core_id": 234, "dndc_id": 112, "dndc_name": "other crops", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["trees", "Permanent crops", "other_tree_crop", "permanent_crop", "Trees", "other", "other_crops", "Other Crops", "Other", "Other crops"], "mrv_enabled": false, "regrow_id": 97, "regrow_name": "other", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 278, "dndc_id": 113, "dndc_name": "annual ryegrass, cover", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["Ryegrass"], "mrv_enabled": false, "regrow_id": 98, "regrow_name": "ryegrass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 48, "core_id": 353, "dndc_id": 114, "dndc_name": "watermelon", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Watermelons", "watermelons", "Watermelon", "watermelon"], "mrv_enabled": false, "regrow_id": 99, "regrow_name": "watermelon", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 333, "dndc_id": 115, "dndc_name": "teff", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 100, "regrow_name": "teff", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 229, "core_id": 265, "dndc_id": 116, "dndc_name": "pumpkin", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON><PERSON>", "Gourds", "pumpkins", "gourds", "<PERSON><PERSON><PERSON>", "pumpkin"], "mrv_enabled": false, "regrow_id": 101, "regrow_name": "pumpkin", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 14, "core_id": 247, "dndc_id": 117, "dndc_name": "mint, herb", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["mint", "Mint"], "mrv_enabled": false, "regrow_id": 102, "regrow_name": "mint", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 118, "dndc_name": "mint, hay", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 103, "regrow_name": "hay_mint", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 67, "core_id": 240, "dndc_id": 119, "dndc_name": "peach", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Peach", "peach"], "mrv_enabled": false, "regrow_id": 104, "regrow_name": "peach", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 76, "core_id": 351, "dndc_id": 120, "dndc_name": "walnut", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Walnut", "walnut"], "mrv_enabled": false, "regrow_id": 105, "regrow_name": "walnut", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 41, "core_id": 314, "dndc_id": 121, "dndc_name": "sugarbeet", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["Sugarbeets", "sugarbeets", "sugar_beet"], "mrv_enabled": false, "regrow_id": 106, "regrow_name": "sugar_beet", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 122, "dndc_name": "chicory_seed", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 107, "regrow_name": "chicory_seed", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 60, "core_id": 324, "dndc_id": 123, "dndc_name": "switchgrass", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["switchgrass", "Switchgrass"], "mrv_enabled": false, "regrow_id": 108, "regrow_name": "switchgrass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 335, "dndc_id": 124, "dndc_name": "timothy_grass", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 109, "regrow_name": "timothy_grass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 125, "dndc_name": "miscanthus", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": ["miscanthus", "Miscant<PERSON>"], "mrv_enabled": false, "regrow_id": 110, "regrow_name": "miscanthus", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 126, "dndc_name": "barley_malt", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 111, "regrow_name": "barley_malt", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 127, "dndc_name": "chicory_root", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 112, "regrow_name": "chicory_root", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 128, "dndc_name": "castor_bean", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 113, "regrow_name": "castor_bean", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 22, "core_id": 119, "dndc_id": 129, "dndc_name": "wheat_durum", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": true, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 114, "regrow_name": "wheat_durum", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": 51, "core_id": 76, "dndc_id": 130, "dndc_name": "chickpea", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["chickpea", "chick_peas", "<PERSON>ck <PERSON>"], "mrv_enabled": false, "regrow_id": 115, "regrow_name": "chickpea", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 231, "dndc_id": 131, "dndc_name": "orchardgrass", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 116, "regrow_name": "orchardgrass", "si_enabled": true}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 126, "dndc_id": 132, "dndc_name": "faba_bean", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 117, "regrow_name": "faba_bean", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 442, "dndc_id": 133, "dndc_name": "premium_cover_crop_mix", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 118, "regrow_name": "premium_cover_crop_mix", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 134, "dndc_name": "phacelia", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 119, "regrow_name": "phacelia", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 135, "dndc_name": "egyptian_clover", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 120, "regrow_name": "egyptian_clover", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 130, "dndc_id": 136, "dndc_name": "tall_fescue", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 121, "regrow_name": "tall_fescue", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 137, "dndc_name": "willow", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 122, "regrow_name": "willow", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 138, "dndc_name": "triticale_grain", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 123, "regrow_name": "triticale_grain", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 163, "dndc_id": 139, "dndc_name": "hemp", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 124, "regrow_name": "hemp", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 59, "dndc_id": 140, "dndc_name": "canary_seed", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 125, "regrow_name": "canary_seed", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 129, "dndc_id": 141, "dndc_name": "red_fescue", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 126, "regrow_name": "red_fescue", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 142, "dndc_name": "ruzigrass", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 127, "regrow_name": "ruzigrass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 68, "core_id": 6, "dndc_id": 40, "dndc_name": "fruit trees", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Apples", "Apple", "apples", "apple"], "mrv_enabled": false, "regrow_id": 128, "regrow_name": "apple", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 223, "core_id": 7, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["apricot", "Apricot"], "mrv_enabled": false, "regrow_id": 129, "regrow_name": "apricot", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 207, "core_id": 10, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["asparagus", "Asparagus"], "mrv_enabled": false, "regrow_id": 130, "regrow_name": "asparagus", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 215, "core_id": 13, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["avocado", "Avocado"], "mrv_enabled": false, "regrow_id": 131, "regrow_name": "avocado", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 186, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 132, "regrow_name": "bean_lima", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 292, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 133, "regrow_name": "bean_snap", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 29, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 134, "regrow_name": "blackberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 242, "core_id": 31, "dndc_id": 38, "dndc_name": "blueberry", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["blueberry", "Blueberries", "blueberries"], "mrv_enabled": false, "regrow_id": 135, "regrow_name": "blueberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 63, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON><PERSON><PERSON><PERSON>", "canteloupe", "Dbl Crop Lettuce/Cantaloupe", "Canteloupe", "dbl_crop_lettuce_cantaloupe", "cantal<PERSON><PERSON>"], "mrv_enabled": false, "regrow_id": 136, "regrow_name": "canteloupe", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 206, "core_id": 65, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Carrots", "carrot", "Carrot"], "mrv_enabled": false, "regrow_id": 137, "regrow_name": "carrot", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 244, "core_id": 68, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "cauliflower"], "mrv_enabled": false, "regrow_id": 138, "regrow_name": "cauliflower", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 50, "core_id": 111, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["cucumber", "<PERSON><PERSON><PERSON>ber"], "mrv_enabled": false, "regrow_id": 139, "regrow_name": "cucumber", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 115, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 140, "regrow_name": "date", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 132, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 141, "regrow_name": "fig", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 208, "core_id": 140, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["garlic", "<PERSON><PERSON><PERSON>"], "mrv_enabled": false, "regrow_id": 142, "regrow_name": "garlic", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 147, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 143, "regrow_name": "grapefruit", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 219, "core_id": 152, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["greens", "Greens"], "mrv_enabled": false, "regrow_id": 144, "regrow_name": "greens", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 162, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 145, "regrow_name": "hazelnut", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 213, "core_id": 165, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 146, "regrow_name": "honeydew", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 175, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 147, "regrow_name": "kiwi", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 187, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 148, "regrow_name": "lime", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 218, "core_id": 223, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["nectarine", "Nectarine"], "mrv_enabled": false, "regrow_id": 149, "regrow_name": "nectarine", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 211, "core_id": 228, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON>", "olive", "olives", "Olives"], "mrv_enabled": false, "regrow_id": 150, "regrow_name": "olive", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 212, "core_id": 230, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Orange", "orange"], "mrv_enabled": false, "regrow_id": 151, "regrow_name": "orange", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 77, "core_id": 243, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["pears", "pear", "<PERSON><PERSON>s", "Pear"], "mrv_enabled": false, "regrow_id": 152, "regrow_name": "pear", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 74, "core_id": 245, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Pecan", "pecan"], "mrv_enabled": false, "regrow_id": 153, "regrow_name": "pecan", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 216, "core_id": 248, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["pepper", "Pepper"], "mrv_enabled": false, "regrow_id": 154, "regrow_name": "pepper", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 204, "core_id": 255, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Pistachios", "pistachio", "Pistachio", "pistachios"], "mrv_enabled": false, "regrow_id": 155, "regrow_name": "pistachio", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 220, "core_id": 257, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["plum", "<PERSON><PERSON>"], "mrv_enabled": false, "regrow_id": 156, "regrow_name": "plum", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 157, "regrow_name": "plum_prune", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 210, "core_id": 263, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON><PERSON><PERSON>", "prune"], "mrv_enabled": false, "regrow_id": 158, "regrow_name": "prune", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 270, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 159, "regrow_name": "raspberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 307, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 160, "regrow_name": "spinach", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 222, "core_id": 308, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Squash", "squash"], "mrv_enabled": false, "regrow_id": 161, "regrow_name": "squash", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 327, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 162, "regrow_name": "tangelo", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 328, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 163, "regrow_name": "tangerine", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 164, "regrow_name": "temple", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 152, "core_id": 425, "dndc_id": 61, "dndc_name": "shrub", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Shrubland", "shrubland", "shrub"], "mrv_enabled": false, "regrow_id": 166, "regrow_name": "shrub", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 250, "core_id": 109, "dndc_id": 38, "dndc_name": "cranberry", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["cranberry", "Cranberries", "cranberries"], "mrv_enabled": false, "regrow_id": 167, "regrow_name": "cranberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 38, "core_id": 54, "dndc_id": 57, "dndc_name": "camelina", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON><PERSON>", "camelina"], "mrv_enabled": false, "regrow_id": 168, "regrow_name": "camelina", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 59, "core_id": 293, "dndc_id": 144, "dndc_name": "sod_grass", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["sod_grass_seed", "Sod/Grass Seed", "sod_grass"], "mrv_enabled": false, "regrow_id": 169, "regrow_name": "sod_grass", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 57, "core_id": 422, "dndc_id": 16, "dndc_name": "herb", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["<PERSON><PERSON>", "herbs", "herb"], "mrv_enabled": false, "regrow_id": 170, "regrow_name": "herb", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 30, "core_id": 306, "dndc_id": 94, "dndc_name": "speltz", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["speltz", "spelt_spring", "Spelt", "spelt_winter", "Speltz", "Winter Spelt", "Spring Spelt", "spring_spelt"], "mrv_enabled": false, "regrow_id": 171, "regrow_name": "speltz", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 141, "core_id": 342, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["deciduous_forest", "Deciduous Forest", "forest_deciduous"], "mrv_enabled": false, "regrow_id": 172, "regrow_name": "forest_deciduous", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": 142, "core_id": null, "dndc_id": 54, "dndc_name": "forest_evergreen", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Evergreen Forest", "forest", "evergreen_forest", "Forest"], "mrv_enabled": false, "regrow_id": 173, "regrow_name": "forest_evergreen", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 25, "dndc_name": "oilseed", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": ["Oilseed", "Oilseeds", "oilseed"], "mrv_enabled": false, "regrow_id": 174, "regrow_name": "oilseed", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 18, "dndc_name": "other_root_crop", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["other_root_crop", "Other root crops", "other_root_crops", "Root crops"], "mrv_enabled": false, "regrow_id": 175, "regrow_name": "other_root_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 3, "dndc_name": "legume", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["legume", "legumes", "Legumes"], "mrv_enabled": false, "regrow_id": 176, "regrow_name": "legume", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["caneberry", "caneberries", "Caneberries"], "mrv_enabled": false, "regrow_id": 177, "regrow_name": "caneberry", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["Pomegranate", "pomegranate"], "mrv_enabled": false, "regrow_id": 178, "regrow_name": "pomegranate", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": null, "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["<PERSON><PERSON>", "poppy"], "mrv_enabled": false, "regrow_id": 179, "regrow_name": "poppy", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 268, "dndc_id": 25, "dndc_name": "rapeseeds", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 180, "regrow_name": "rapeseed", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 443, "dndc_id": 145, "dndc_name": "basic_cover_crop", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 181, "regrow_name": "basic_cover_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 12, "dndc_name": "grass, perennial, C3, calvin", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 182, "regrow_name": "grass_perennial_c3_calvin", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 143, "dndc_name": "pigeonpea", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 183, "regrow_name": "pigeonpea", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": 239, "dndc_id": 156, "dndc_name": "pasture", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": ["pasture", "Pasture"], "mrv_enabled": false, "regrow_id": 184, "regrow_name": "pasture", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 158, "dndc_name": "rice_paddy_short", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 185, "regrow_name": "rice_paddy_short", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 159, "dndc_name": "rice_paddy_long", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 186, "regrow_name": "rice_paddy_long", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 161, "dndc_name": "lupin", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 187, "regrow_name": "lupin", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 162, "dndc_name": "alternative_single_species_cover_crop", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 188, "regrow_name": "alternative_single_species_cover_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 163, "dndc_name": "legume_cover_crop", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 189, "regrow_name": "legume_cover_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 164, "dndc_name": "traditional_blend_cover_crop", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 190, "regrow_name": "traditional_blend_cover_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": 165, "dndc_name": "alternative_blend_cover_crop", "explore_api_enabled": true, "measure_api_enabled": true, "monitor_api_enabled": false, "monitor_api_name": null, "mrv_enabled": false, "regrow_id": 191, "regrow_name": "alternative_blend_cover_crop", "si_enabled": false}, {"biofuels_api_enabled": false, "cdl_id": null, "core_id": null, "dndc_id": null, "dndc_name": "no_commodity", "explore_api_enabled": false, "measure_api_enabled": false, "monitor_api_enabled": true, "monitor_api_name": ["No Commodity"], "mrv_enabled": false, "regrow_id": 192, "regrow_name": "no_commodity", "si_enabled": false}]