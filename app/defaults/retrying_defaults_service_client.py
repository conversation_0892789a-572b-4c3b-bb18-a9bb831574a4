import asyncio

import backoff
import httpx
from defaults_service_client.client import DefaultsServiceClient
from defaults_service_client.schemas import (
    BaseModel,
    Crop,
    CropParametersQueryParams,
    CropTranslation,
    CropTranslationQueryParams,
    DefaultsServiceTable,
    DefaultTypeEnum,
)

from helper.external_api import backoff_fatal_error, RETRYABLE_EXCEPTIONS


class RetryingDefaultsServiceClient:
    """
    A wrapper for the DefaultsServiceClient that adds retries on 502 and 503 status codes.

    It also wraps the synchronous methods in asyncio.to_thread to allow for asynchronous usage.
    """

    def __init__(self, base_url: str, http_client: httpx.Client):
        self._client = DefaultsServiceClient(base_url=base_url, http_client=http_client)

    @backoff.on_exception(
        exception=RETRYABLE_EXCEPTIONS, wait_gen=backoff.expo, giveup=backoff_fatal_error, max_tries=4
    )
    async def get_defaults(self) -> DefaultsServiceTable:
        return await asyncio.to_thread(self._client.get_defaults)

    @backoff.on_exception(
        exception=RETRYABLE_EXCEPTIONS, wait_gen=backoff.expo, giveup=backoff_fatal_error, max_tries=4
    )
    async def get_crop_parameters(
        self,
        query_params: CropParametersQueryParams = CropParametersQueryParams(country_code="USA"),
    ) -> list[Crop]:
        return await asyncio.to_thread(self._client.get_crop_parameters, query_params)

    @backoff.on_exception(
        exception=RETRYABLE_EXCEPTIONS, wait_gen=backoff.expo, giveup=backoff_fatal_error, max_tries=4
    )
    async def list_crop_translations(
        self, query_params: CropTranslationQueryParams = CropTranslationQueryParams()
    ) -> list[CropTranslation]:
        return await asyncio.to_thread(self._client.list_crop_translations, query_params)

    @backoff.on_exception(
        exception=RETRYABLE_EXCEPTIONS, wait_gen=backoff.expo, giveup=backoff_fatal_error, max_tries=4
    )
    async def get_table(self, table_name: DefaultTypeEnum) -> list[BaseModel]:
        return await asyncio.to_thread(self._client.get_table, table_name)
