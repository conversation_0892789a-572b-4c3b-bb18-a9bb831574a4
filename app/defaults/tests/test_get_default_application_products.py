from typing import List

from defaults.consts import EXPLORE_API_ENABLED, MEASURE_API_ENABLED
from defaults.router import get_default_application_products
from defaults.schema import ApplicationProduct, ApplicationProductCategory
from phases.enums import PhaseTypes


async def test_get_default_application_products():
    default_products = await get_default_application_products()
    assert len(default_products) > 0

    fert_type = "ammbic"
    eenf_type = "esn"
    org_amend_type = "poultry_solid"
    fertilizer = next((fertilizer for fertilizer in default_products if fertilizer["type"] == fert_type), None)
    eenf = next((eenf for eenf in default_products if eenf["type"] == eenf_type), None)
    organic_amendment = next((org_amend for org_amend in default_products if org_amend["type"] == org_amend_type), None)
    assert fertilizer["type"] == fert_type
    assert fertilizer["category"] is ApplicationProductCategory.FERTILIZER
    assert eenf["type"] == eenf_type
    assert eenf["category"] is ApplicationProductCategory.EENF
    assert organic_amendment["type"] == org_amend_type
    assert organic_amendment["category"] is ApplicationProductCategory.ORGANIC_AMENDMENT


async def test_get_monitor_enabled_default_application_products():
    default_products: List[ApplicationProduct] = await get_default_application_products(
        phase_type=PhaseTypes.MONITORING
    )
    assert len(default_products) > 0
    monitor_products = [product for product in default_products if bool(product[MEASURE_API_ENABLED])]
    not_monitor_products = [product for product in default_products if not bool(product[MEASURE_API_ENABLED])]

    assert len(default_products) == len(monitor_products)
    assert len(not_monitor_products) == 0


async def test_get_enrolment_enabled_default_application_products():
    default_products: List[ApplicationProduct] = await get_default_application_products(phase_type=PhaseTypes.ENROLMENT)
    assert len(default_products) > 0
    enrolment_products = [product for product in default_products if bool(product[EXPLORE_API_ENABLED])]
    not_enrolment_products = [product for product in default_products if not bool(product[EXPLORE_API_ENABLED])]

    assert len(default_products) == len(enrolment_products)
    assert len(not_enrolment_products) == 0
