import httpx

from defaults.router import get_options_for_attribute_type
from phases.enums import AttributeTypes


async def test_get_options_for_attribute_type(app_request: httpx.Request):
    attribute_options = await get_options_for_attribute_type(
        request=app_request, attribute_type=AttributeTypes.application_rate_unit
    )
    assert len(attribute_options) > 0

    attribute_options = await get_options_for_attribute_type(
        request=app_request, attribute_type=AttributeTypes.crop_type
    )
    assert len(attribute_options) > 0

    attribute_options = await get_options_for_attribute_type(
        request=app_request, attribute_type=AttributeTypes.irrigation_method
    )
    assert len(attribute_options) > 0

    # unsupported attribute
    attribute_options = await get_options_for_attribute_type(request=app_request, attribute_type=AttributeTypes.string)
    assert len(attribute_options) == 0
