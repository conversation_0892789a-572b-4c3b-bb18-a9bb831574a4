import httpx

from defaults.consts import EXPLORE_API_ENABLED, MEASURE_API_ENABLED
from defaults.router import get_default_crops
from entity_events.events.helpers import is_fallow_crop_type
from phases.enums import PhaseTypes


async def test_get_default_crops(app_request: httpx.Request):
    default_crops = await get_default_crops(request=app_request, phase_type=None)

    monitor_crops = [crop for crop in default_crops if bool(crop[MEASURE_API_ENABLED])]
    enrolment_crops = [crop for crop in default_crops if bool(crop[EXPLORE_API_ENABLED])]
    not_enrolment_crops = [crop for crop in default_crops if not bool(crop[EXPLORE_API_ENABLED])]
    not_monitor_crops = [crop for crop in default_crops if not bool(crop[MEASURE_API_ENABLED])]

    assert len(monitor_crops) > 0
    assert len(enrolment_crops) > 0
    assert len(not_enrolment_crops) > 0
    assert len(not_monitor_crops) > 0


async def test_get_monitor_phase_enabled_crops(app_request: httpx.Request):
    default_crops = await get_default_crops(request=app_request, phase_type=PhaseTypes.MONITORING)
    monitor_crops = [crop for crop in default_crops if bool(crop[MEASURE_API_ENABLED])]
    not_monitor_crops = [crop for crop in default_crops if not bool(crop[MEASURE_API_ENABLED])]
    fallow_crops = [crop for crop in default_crops if is_fallow_crop_type(crop["regrow_name"])]

    assert len(default_crops) == len(monitor_crops) + len(fallow_crops)
    assert len(not_monitor_crops) - len(fallow_crops) == 0


async def test_get_enrolment_phase_enabled_crops(app_request: httpx.Request):
    default_crops = await get_default_crops(request=app_request, phase_type=PhaseTypes.ENROLMENT)
    enrolment_crops = [crop for crop in default_crops if bool(crop[EXPLORE_API_ENABLED])]
    not_enrolment_crops = [crop for crop in default_crops if not bool(crop[EXPLORE_API_ENABLED])]
    fallow_crops = [crop for crop in default_crops if is_fallow_crop_type(crop["regrow_name"])]

    assert len(default_crops) == len(enrolment_crops) + len(fallow_crops)
    assert len(not_enrolment_crops) - len(fallow_crops) == 0
