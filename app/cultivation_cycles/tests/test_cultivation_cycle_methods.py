from datetime import datetime, timedelta
from uuid import UUID

import pytest
import pytz

from cultivation_cycles.methods import (
    _get_empty_cultivation_cycles_from_commodity_cropping_events,
    get_cultivation_cycles,
    get_reporting_period_cultivation_cycles,
    get_unbinned_events_time_delta_from_cultivation_cycles,
)
from cultivation_cycles.schema import (
    CultivationCycle,
    CultivationCycleError,
    CultivationCycleGenerationConfig,
    CultivationCycleId,
)
from defaults.attribute_options import CropUsage, NoCropType
from defaults.consts import RegrowCropName
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent


def test_get_cultivation_cycles_happy_path(events):
    cycles, unbinned_events = get_cultivation_cycles(events)

    # Correct number of cycles?
    assert len(cycles) == 7
    assert len(unbinned_events) == 0

    # Each cycle having the appropriate interval?
    expected_cycle_intervals = [
        ("2017-07-23T00:00:00+00:00", "2018-07-22T23:59:00+00:00"),  # "Leading" CC with back-extended start date
        ("2018-07-23T00:00:00+00:00", "2018-08-01T23:59:00+00:00"),
        ("2018-08-02T00:00:00+00:00", "2019-07-15T23:59:00+00:00"),
        ("2019-07-16T00:00:00+00:00", "2020-07-06T23:59:00+00:00"),
        ("2020-07-07T00:00:00+00:00", "2021-07-14T23:59:00+00:00"),
        ("2021-07-15T00:00:00+00:00", "2021-08-01T23:59:00+00:00"),
        ("2021-08-02T00:00:00+00:00", "2022-07-15T23:59:00+00:00"),
    ]
    assert cycles[0].id == CultivationCycleId(
        crop_event_id=CroppingIDs(
            sowing_id=UUID("052e6b89-9ebb-4a0d-a6eb-d64e778fe11f"),
            harvesting_id=UUID("42157f99-9245-4da5-be91-a80dceb38ced"),
        ),
        crop_type=RegrowCropName.peanut.value,
        start_date=expected_cycle_intervals[0][0],
        end_date=expected_cycle_intervals[0][1],
        harvest_year=2018,
    )
    # Confirm FallowPeriod cycle ID was generated correctly
    assert cycles[1].id.crop_type == NoCropType.FALLOW.value
    assert isinstance(cycles[1].id.crop_event_id, CroppingIDs)
    assert cycles[1].id.start_date == datetime.fromisoformat(expected_cycle_intervals[1][0])
    assert cycles[1].id.end_date == datetime.fromisoformat(expected_cycle_intervals[1][1])

    expected_crop_types = [
        RegrowCropName.peanut,
        NoCropType.FALLOW,
        RegrowCropName.barley,
        RegrowCropName.cotton,
        RegrowCropName.potato,
        NoCropType.FALLOW,
        RegrowCropName.sorghum,
    ]
    for ind, expected_crop_type in enumerate(expected_crop_types):
        assert cycles[ind].id.crop_type == expected_crop_type
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )

    # Each cycle having the appropriate number of events?
    # Format of tuples is (#CroppingEvents, #TillageEvents, #ApplicationEvents, #IrrigationEvents)
    desired_event_counts = [
        [2, 1, 1, 1],
        [0, 0, 0, 0],
        [1, 2, 1, 1],
        [1, 1, 1, 3],
        [2, 2, 1, 0],
        [0, 0, 0, 0],
        [1, 0, 1, 1],
    ]
    for desired_event_count, cycle in zip(desired_event_counts, cycles):
        actual_event_counts = [
            len([e for e in cycle.events if isinstance(e, event_class)])
            for event_class in [CroppingEvent, TillageEvent, ApplicationEvent, IrrigationEvent]
        ]
        assert actual_event_counts == desired_event_count


async def test_get_cultivation_cycles_with_split(
    create_cropping_event_data,
    create_interval_data,
    create_irrigation_event_data,
    create_tillage_event_data,
    create_reduction_event_data,
):
    dt_format = "%Y-%m-%dT%H:%M:%S%z"
    a_start, a_end, b_start, b_end = (
        datetime.strptime("2018-02-15T07:00:00+00:00", dt_format),
        datetime.strptime("2018-07-22T07:00:00+00:00", dt_format),
        datetime.strptime("2019-02-01T07:00:00+00:00", dt_format),
        datetime.strptime("2019-07-15T07:00:00+00:00", dt_format),
    )

    # These will define the cultivation cycle bounds
    commodity_cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(start=a_start, end=a_end),
                crop_usage=CropUsage.COMMODITY,
                crop_type="peanuts",
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(start=b_start, end=b_end),
                crop_usage=CropUsage.COMMODITY,
                crop_type="barley",
                reductions=[],
            )
        ),
    ]

    # Interval events that span the Cycles in interesting ways
    """
        Cultivation Cycles         ---------------  --------------
        IrrigationEvents       ---------       --------        -------
        Splits                 --- -----       ---  ---        --- ---
        Binned Splits              -----       ---  ---        ---
    Note that in the above row, there are two events that don't get binned. These outer events are "lost"
    """
    irrigation_events = [
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(
                interval=create_interval_data(start=a_end - timedelta(days=370), end=a_end - timedelta(days=5))
            )
        ),
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(
                interval=create_interval_data(start=a_end - timedelta(days=5), end=b_start + timedelta(days=5))
            )
        ),
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(
                interval=create_interval_data(start=b_end - timedelta(days=5), end=b_end + timedelta(days=5))
            )
        ),
    ]
    i1, i2, i3 = irrigation_events  # For later reference

    # And a non-interval event for good measure, somewhere in the second CC
    tillage_event_occurred_at = b_end - timedelta(days=5)
    tillage_event = TillageEvent.parse_obj(create_tillage_event_data(occurred_at=tillage_event_occurred_at))

    events = [*commodity_cropping_events, *irrigation_events, tillage_event]

    cycles, unbinned_events = get_cultivation_cycles(
        events, CultivationCycleGenerationConfig(should_split_irrigation_events=True)
    )

    # The first cycle should have two irrigation events, with both having been clipped.
    first_cycle_irrigation_events = [ev for ev in cycles[0].events if isinstance(ev, IrrigationEvent)]
    assert len(first_cycle_irrigation_events) == 2
    # The first IE (i1) should have had its start clipped by the beginning of the CC
    assert first_cycle_irrigation_events[0].interval.start == cycles[0].start  # or a_start?
    assert first_cycle_irrigation_events[0].interval.end == i1.interval.end
    # The second IE (i2) should have had its end clipped by the end of the CC
    assert first_cycle_irrigation_events[1].interval.start == i2.interval.start
    assert first_cycle_irrigation_events[1].interval.end == cycles[0].end  # or a_end?

    # The second cycle should also have two irrigation events, with both having been clipped
    second_cycle_irrigation_events = [ev for ev in cycles[1].events if isinstance(ev, IrrigationEvent)]
    assert len(second_cycle_irrigation_events) == 2
    # The first IE (i2) should have had its start clipped by the start of the CC
    assert second_cycle_irrigation_events[0].interval.start == cycles[1].start  # or a_start?
    assert second_cycle_irrigation_events[0].interval.end == i2.interval.end
    # The second IE (i3) should have its end clipped by the end of the CC
    assert second_cycle_irrigation_events[1].interval.start == i3.interval.start
    assert second_cycle_irrigation_events[1].interval.end == cycles[1].end  # or a_end?

    # The tillage event should have made it into the second cycle
    second_cycle_tillage_event: TillageEvent = next(filter(lambda e: isinstance(e, TillageEvent), cycles[1].events))
    assert second_cycle_tillage_event.occurred_at == tillage_event_occurred_at

    assert len(unbinned_events) == 0


def test_get_cultivation_cycles_leading_fallows(events):
    # Filter out any events in or before 2020 so we have to pad the first three years with fallow cycles
    events = [event for event in events if event.get_interval_end_or_occurred_at().year > 2020]
    cycles, unbinned_events = get_cultivation_cycles(events, CultivationCycleGenerationConfig(start_year=2018))

    # Correct number of cycles?
    assert len(cycles) == 6
    assert len(unbinned_events) == 0

    # Each cycle having the appropriate interval?
    expected_cycle_intervals = [
        ("2017-07-15T00:00:00+00:00", "2018-07-14T23:59:00+00:00"),  # Leading fallow cycle
        ("2018-07-15T00:00:00+00:00", "2019-07-14T23:59:00+00:00"),  # Leading fallow cycle
        ("2019-07-15T00:00:00+00:00", "2020-07-14T23:59:00+00:00"),  # Leading fallow cycle
        ("2020-07-15T00:00:00+00:00", "2021-07-14T23:59:00+00:00"),  # One-year initial cropping cultivation cycle
        ("2021-07-15T00:00:00+00:00", "2021-08-01T23:59:00+00:00"),  # Explicit FallowPeriod
        ("2021-08-02T00:00:00+00:00", "2022-07-15T23:59:00+00:00"),  # Normal cultivation cycle
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )

    # Each cycle having the appropriate number of events?
    # Format of tuples is (#CroppingEvents, #TillageEvents, #ApplicationEvents, #IrrigationEvents)
    desired_event_counts = [
        [0, 0, 0, 0],
        [0, 0, 0, 0],
        [0, 0, 0, 0],
        [2, 1, 1, 0],
        [0, 0, 0, 0],
        [1, 0, 1, 1],
    ]
    for desired_event_count, cycle in zip(desired_event_counts, cycles):
        actual_event_counts = [
            len([e for e in cycle.events if isinstance(e, event_class)])
            for event_class in [CroppingEvent, TillageEvent, ApplicationEvent, IrrigationEvent]
        ]
        assert actual_event_counts == desired_event_count


def test_get_cultivation_cycles_crop_events_with_same_end_date(create_cropping_event_data, create_interval_data):
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # same end date
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # same end date
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # same end date, no start date
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(start=None, end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2, cropping_3, cropping_4]
    cycles, unbinned_events = get_cultivation_cycles(events)

    assert len(cycles) == 1
    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2024-12-31T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_1, cropping_2, cropping_3, cropping_4]

    assert unbinned_events == []


def test_get_cultivation_cycles_crop_event_with_no_end_date(create_cropping_event_data, create_interval_data):
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # no end date
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(start=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC), end=None),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2]
    cycles, unbinned_events = get_cultivation_cycles(events)

    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2024-12-31T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_1, cropping_2]

    assert unbinned_events == []


def test_get_cultivation_cycles_use_first_commodity_crop_start_date(create_cropping_event_data, create_interval_data):
    CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    cycles, unbinned_events = get_cultivation_cycles([])

    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2025-01-01T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )

    assert unbinned_events == []


def test_get_cultivation_cycles_multiple_fields(events):
    # Simulate events being sent across multiple fields by setting one of the events to have a different entity ID
    events[0].entity_id = 1234
    events[1].entity_id = 2345
    with pytest.raises(CultivationCycleError) as ve:
        get_cultivation_cycles(events)
        assert "Cultivation cycles can only be generated for 1 field at a time." in str(ve.value)


def test_get_cultivation_cycles_no_events():
    # No events should result in no cultivation cycles
    cycles, unbinned_events = get_cultivation_cycles([])
    assert len(cycles) == 0
    assert len(unbinned_events) == 0


def test_bin_events_into_cultivation_cycles_no_cycles(create_tillage_event_data):
    events = [TillageEvent.parse_obj(create_tillage_event_data(occurred_at=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC)))]
    cycles, unbinned_events = get_cultivation_cycles(events=events)
    assert cycles == []
    assert unbinned_events == events


def test_bin_events_into_cultivation_cycles(events):
    # Test that the events are grouped into cultivation cycles correctly
    events = [event for event in events if not isinstance(event, FallowPeriod)]
    cult_cycles, unbinned_events = get_cultivation_cycles(
        events=events, generation_config=CultivationCycleGenerationConfig(should_split_irrigation_events=True)
    )
    assert len(cult_cycles) == 5
    assert len(unbinned_events) == 0
    assert all(len(cycle.events) > 0 for cycle in cult_cycles)


def test_bin_event_by_start_date(create_cropping_event_data, create_interval_data):
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # no end date
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC),
                end=None,
            ),
            crop_usage=CropUsage.COVER,
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2, cropping_3]
    cycles, unbinned_events = get_cultivation_cycles(events)

    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2024-12-31T23:59:00+00:00"),
        ("2025-01-01T00:00:00+00:00", "2025-12-31T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_1, cropping_2]
    assert cycles[1].events == [cropping_3]

    assert unbinned_events == []


def test_bin_event_outside_cultivation_cycles_by_end_date(create_cropping_event_data, create_interval_data):
    # end date before earliest cycle
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2023, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2023, 12, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COVER,
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # end date after latest cycle
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COVER,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2, cropping_3, cropping_4]
    cycles, unbinned_events = get_cultivation_cycles(events)

    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2024-12-31T23:59:00+00:00"),
        ("2025-01-01T00:00:00+00:00", "2025-12-31T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_2]
    assert cycles[1].events == [cropping_3]

    assert unbinned_events == [cropping_1, cropping_4]


def test_bin_event_outside_cultivation_cycles_by_start_date(create_cropping_event_data, create_interval_data):
    # start date before earliest cycle
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2023, 1, 1, 0, 0, 0, 0, pytz.UTC),
                end=None,
            ),
            crop_usage=CropUsage.COVER,
            reductions=[],
        )
    )
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    cropping_3 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 12, 31, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # end date after latest cycle
    cropping_4 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2026, 1, 1, 0, 0, 0, 0, pytz.UTC),
                end=None,
            ),
            crop_usage=CropUsage.COVER,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2, cropping_3, cropping_4]
    cycles, unbinned_events = get_cultivation_cycles(events)

    expected_cycle_intervals = [
        ("2024-01-01T00:00:00+00:00", "2024-12-31T23:59:00+00:00"),
        ("2025-01-01T00:00:00+00:00", "2025-12-31T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_2]
    assert cycles[1].events == [cropping_3]

    assert unbinned_events == [cropping_1, cropping_4]


def test_bin_event_in_multiple_cultivation_cycles(create_cropping_event_data, create_interval_data):
    cropping_1 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 1, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    # in multiple cultivation cycles
    cropping_2 = CroppingEvent.parse_obj(
        create_cropping_event_data(
            interval=create_interval_data(
                start=datetime(2024, 1, 20, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 2, 10, 0, 0, 0, 0, pytz.UTC)
            ),
            crop_usage=CropUsage.COMMODITY,
            reductions=[],
        )
    )
    events = [cropping_1, cropping_2]
    cycles, unbinned_events = get_cultivation_cycles(events)

    expected_cycle_intervals = [
        ("2023-02-02T00:00:00+00:00", "2024-02-01T23:59:00+00:00"),
        ("2024-02-02T00:00:00+00:00", "2024-02-10T23:59:00+00:00"),
    ]
    assert all(
        (cycle.start.isoformat(), cycle.end.isoformat()) == desired_cycle_interval
        for cycle, desired_cycle_interval in zip(cycles, expected_cycle_intervals)
    )
    assert cycles[0].events == [cropping_1]
    assert cycles[1].events == [cropping_2]

    assert unbinned_events == []


async def test_get_reporting_period_cultivation_cycles(mdl, events):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2021, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2021, 12, 31, 23, 59, 59),
    )
    ccs, _ = get_cultivation_cycles(events)
    reporting_period_ccs = get_reporting_period_cultivation_cycles(ccs, program)

    assert len(reporting_period_ccs) == 2


def test_get_gap_cycles_adjust_no_cycles(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(  # not split
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 7, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 8, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(  # start date only cover crop, ignored
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC),
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(  # end date only cover crop, ignored
            create_cropping_event_data(
                interval=create_interval_data(
                    end=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC),
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 12, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 12, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_gap_cycles_adjust_one_cycle_multiple_times(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # split after 1st adjustment
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 12, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 1, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 3, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2026, 1, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_gap_cycles_adjust_multiple_cycles(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 12, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 4, 30, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 5, 1, 0, 0, 0, 0, pytz.UTC), datetime(2026, 3, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2026, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2026, 12, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_gap_cycles_add_buffer_cycle(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 12, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 5, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC), datetime(2026, 12, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_gap_cycles_overlap_into_next_planting_date(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 6, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_gap_cycles_no_next_planting_date(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(start=None, end=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC)),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2023, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 6, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_leading_cycles_adjust_first_cycle_multiple_times(create_cropping_event_data, create_interval_data):
    cropping_events = [
        # split after 1st adjustment
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2023, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2023, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2023, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2023, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=2022,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2021, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2022, 3, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2022, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2023, 3, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2023, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_leading_cycles_adjust_multiple_cycles(create_cropping_event_data, create_interval_data):
    cropping_events = [
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2022, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2022, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2023, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2023, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2024, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=2022,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2021, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2022, 3, 31, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2022, 4, 1, 0, 0, 0, 0, pytz.UTC), datetime(2023, 4, 30, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2023, 5, 1, 0, 0, 0, 0, pytz.UTC), datetime(2024, 6, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_ending_cycles_adjust_no_cycles(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=2026,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 6, 1, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_ending_cycles_adjust_one_cycle_multiple_times(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # split after 1st adjustment
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=2026,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 3, 31, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_ending_cycles_adjust_multiple_cycles(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2027, 4, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2027, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=2027,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2026, 4, 30, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2026, 5, 1, 0, 0, 0, 0, pytz.UTC), datetime(2027, 3, 31, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_ending_cycles_add_buffer_cycle(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=2026,
        autogenerate_gap_cycles=True,
    )
    cycle_intervals = [(cycle.start, cycle.end) for cycle in cycles]
    expected_cycle_intervals = [
        (datetime(2024, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2025, 6, 1, 23, 59, 0, 0, pytz.UTC)),
        (datetime(2025, 6, 2, 0, 0, 0, 0, pytz.UTC), datetime(2027, 5, 31, 23, 59, 0, 0, pytz.UTC)),
    ]
    assert cycle_intervals == expected_cycle_intervals


def test_get_cultivation_cycles_unique_cultivation_cycle_id_index(create_cropping_event_data, create_interval_data):
    cropping_events = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2025, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 5, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2026, 7, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        # split
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2026, 12, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2027, 6, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(2027, 7, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2027, 12, 1, 0, 0, 0, 0, pytz.UTC)
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]
    cycles = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=cropping_events,
        start_year=None,
        end_year=None,
        autogenerate_gap_cycles=True,
    )
    assert len(cycles) == 4
    assert cycles[0].id.index is None
    assert cycles[1].id.harvest_year == 2026
    assert cycles[1].id.index == 1
    assert cycles[2].id.harvest_year == 2026
    assert cycles[2].id.index == 2
    assert cycles[3].id.index is None


def test_get_unbinned_event_time_deltas_from_cultivation_cycles(
    create_tillage_event_data, create_irrigation_event_data, create_interval_data
):
    cultivation_cycles = [
        CultivationCycle(
            id=CultivationCycleId(
                harvest_year=2024,
            ),
            start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
            end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC),
            events=[],
        ),
        CultivationCycle(
            id=CultivationCycleId(
                harvest_year=2025,
            ),
            start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC),
            end=datetime(2025, 12, 31, 0, 0, 0, 0, pytz.UTC),
            events=[],
        ),
    ]
    unbinned_events = [
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(
                interval=create_interval_data(
                    start=datetime(2022, 1, 1, 0, 0, 0, 0, pytz.UTC), end=datetime(2022, 12, 31, 0, 0, 0, 0, pytz.UTC)
                )
            )
        ),
        TillageEvent.parse_obj(create_tillage_event_data(occurred_at=datetime(2023, 1, 1, 0, 0, 0, 0, pytz.UTC))),
        TillageEvent.parse_obj(create_tillage_event_data(occurred_at=datetime(2026, 1, 1, 0, 0, 0, 0, pytz.UTC))),
    ]
    res = get_unbinned_events_time_delta_from_cultivation_cycles(
        cultivation_cycles=cultivation_cycles, unbinned_events=unbinned_events
    )
    assert res == (timedelta(days=366), timedelta(days=1))


def test_get_unbinned_event_time_deltas_from_cultivation_cycles_none_before(
    create_tillage_event_data, create_irrigation_event_data, create_interval_data
):
    cultivation_cycles = [
        CultivationCycle(
            id=CultivationCycleId(
                harvest_year=2024,
            ),
            start=datetime(2024, 1, 1, 0, 0, 0, 0, pytz.UTC),
            end=datetime(2024, 12, 31, 0, 0, 0, 0, pytz.UTC),
            events=[],
        ),
        CultivationCycle(
            id=CultivationCycleId(
                harvest_year=2025,
            ),
            start=datetime(2025, 1, 1, 0, 0, 0, 0, pytz.UTC),
            end=datetime(2025, 12, 31, 0, 0, 0, 0, pytz.UTC),
            events=[],
        ),
    ]
    unbinned_events = [
        TillageEvent.parse_obj(create_tillage_event_data(occurred_at=datetime(2026, 1, 1, 0, 0, 0, 0, pytz.UTC))),
    ]
    res = get_unbinned_events_time_delta_from_cultivation_cycles(
        cultivation_cycles=cultivation_cycles, unbinned_events=unbinned_events
    )
    assert res == (timedelta(days=0), timedelta(days=1))
