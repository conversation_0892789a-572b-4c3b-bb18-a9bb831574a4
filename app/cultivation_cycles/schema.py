from datetime import datetime, timedelta
from typing import Any

from pydantic import BaseModel, Field, root_validator

from entity_events.events.entity_event import EntityEvent, EventIdType
from phases.dataclasses import DateRange
from phases.enums import StageTypes

CULTIVATION_CYCLE_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%SZ"


class CultivationCycleId(BaseModel):
    """
    A set of data that uniquely identifies a cultivation cycle. The related harvest event is derived from the crop_event_id.

    For a non-empty cultivation cycle, the following attributes are set:
    - crop_event_id
    - crop_type
    - start_date
    - end_date
    - harvest_year
    - index

    For an empty cultivation cycle, the following attributes are set:
    - harvest_year
    - index
    """

    crop_event_id: EventIdType
    crop_type: str | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None
    harvest_year: int | None = None
    index: int | None = None


class CultivationCycle(BaseModel):
    # The date range is defined "inclusive" so anything falling on the start or end date is included in the cycle.
    id: CultivationCycleId  # noqa: A003, VNE003
    start: datetime
    end: datetime
    events: list[EntityEvent] = Field(default_factory=list)

    def add_event(self, event: EntityEvent) -> None:
        self.events.append(event)
        self.validate_events_within_range()

    def validate_events_within_range(self) -> None:
        for event in self.events:
            event_binning_date = event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()
            # Because we don't have confidence in the TZ info on our dates, we give a one-day buffer.
            if not self.start - timedelta(days=1) < event_binning_date < self.end + timedelta(days=1):
                raise ValueError(
                    f"Event date {event_binning_date} is outside of Cultivation Cycle range {self.start} - {self.end}"
                )

    @root_validator
    def validate_start_before_or_coinciding_end(cls, values: dict[str, Any]) -> dict[str, Any]:  # noqa
        # Start and end dates are inclusive, so a single-day cycle could potentially have the same start and end
        if values["start"] > values["end"]:
            raise ValueError(
                f"Cultivation Cycle start date {values['start']} must be before or on end date " f"{values['end']}."
            )
        return values

    def get_date_range(self) -> DateRange:
        return DateRange(start_date=self.start.date(), end_date=self.end.date())


class CultivationCycleGenerationConfig(BaseModel):
    """
    start_year: If specified, then autogenerate leading cycle(s) to the start_year
    end_year: If specified, then autogenerate ending cycle(s) to the end_year
    autogenerate_gap_cycles: If true, then if there is a gap of 1+ year between this
        harvest event and the next, autogenerate gap cycle(s) to fill the gap, until
        the gap becomes <1 year OR an autogenerated gap cycle can no longer fit in the
        gap (overlaps into the next planting event).
    should_split_irrigation_events: If true, then split irrigation events that span
        multiple cultivation cycles to fit neatly into the cultivation cycles
    stage_type: If specified, then filter events by stage_type
    """

    start_year: int | None = None
    end_year: int | None = None
    autogenerate_gap_cycles: bool = True
    should_split_irrigation_events: bool = False
    stage_type: StageTypes | None = None


class CultivationCycleError(Exception):
    def __init__(self, message: str, extra: dict[str, Any] | None = None) -> None:
        super().__init__(message)
        self.extra = extra
