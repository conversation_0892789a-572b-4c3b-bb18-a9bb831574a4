from datetime import datetime, timedelta, timezone
from typing import Optional, Union

import pytz
from dateutil.relativedelta import relativedelta

from cultivation_cycles.schema import (
    CultivationCycle,
    CultivationCycleError,
    CultivationCycleGenerationConfig,
    CultivationCycleId,
)
from defaults.attribute_options import CropUsage, NoCropType
from defaults.consts import COVER_CROP_USAGES
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent, EventIdType
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.measures import Interval
from entity_events.methods import STAGE_TYPE_TO_ENTITY_EVENT
from logger import get_logger
from phases.enums import StageTypes
from programs.model import Programs

"""
Cultivation Cycles:
Time periods delineated as commodity crop harvest-to-harvest at the field-level. Cultivation Cycles are meant to replace
"record years" as they are a more flexible logic for grouping field events together, especially when not
necessarily dealing with a single commodity crop per year. Cultivation Cycles are not stored in the database, but are
derived from a field's CroppingEvents.

Note that where an event is represented by a date-range, the end-date of the event is used to associate it with the
Cultivation Cycle.

Background here:
https://regrow.atlassian.net/wiki/spaces/PROD/pages/2782855218/MRV+cultivation+cycles#Cultivation-cycle-definition
"""

logger = get_logger(__name__)


def get_cultivation_cycles(
    events: list[EntityEvent],
    generation_config: CultivationCycleGenerationConfig = CultivationCycleGenerationConfig(),
) -> tuple[list[CultivationCycle], list[EntityEvent]]:
    """
    Get all EntityEvents associated with a given field, derive cultivation cycles from the CroppingEvents, and group
    all EntityEvents into the CultivationCycles chronologically. The start date of the first CultivationCycle will be
    derived from the earliest CroppingEvent's harvest date.

    If start_year is defined, we will pad the beginning of the cultivation cycles list with fallow cultivation cycles
    of length one year, such that the first cultivation cycle ends in start_year.

    If should_split_irrigation_events is enabled, irrigation events are "clipped"/"split" into N events so that they
    neatly fit inside the confines of cultivation cycle windows. See _group_events_into_cultivation_cycles for more info.

    Args:
        events: The events to group into cultivation cycles.
        generation_config: The configuration used to generate cultivation cycles.

    Returns:
        Sorted list of CultivationCycles with contiguous date ranges, each containing EntityEvents binned by time.
        List of EntityEvents that could not be binned into a CultivationCycle.
    """
    # Cultivation cycles are ~defined by the harvest-harvest time of commodity cropping events
    complete_cropping_events: list[Union[CroppingEvent | FallowPeriod]] = [
        event
        for event in events
        if (
            isinstance(event, CroppingEvent)
            and (event.crop_usage == CropUsage.COMMODITY or event.crop_usage in COVER_CROP_USAGES)
            and event.interval.end is not None
        )
        or (isinstance(event, FallowPeriod) and event.interval.end is not None)
    ]

    # All events should have the same entity ID so we don't generate cultivation cycles across multiple fields.
    if len({cce.entity_id for cce in complete_cropping_events}) > 1:
        raise CultivationCycleError("Cultivation cycles can only be generated for 1 field at a time.")

    # Create the cultivation cycles ("bins") using the CroppingEvents
    cult_cycles: list[CultivationCycle] = _get_empty_cultivation_cycles_from_commodity_cropping_events(
        cropping_events=complete_cropping_events,
        start_year=generation_config.start_year,
        end_year=generation_config.end_year,
        autogenerate_gap_cycles=generation_config.autogenerate_gap_cycles,
    )

    # If no cultivation cycles are found, return all events as unbinned events
    if not cult_cycles:
        return cult_cycles, events

    # Bin all events into their appropriate cultivation cycles
    # Also, "split" events that cross cultivation cycle boundaries if specified.
    cult_cycles, unbinned_events = _bin_events_into_cultivation_cycles(
        cult_cycles, events, generation_config.should_split_irrigation_events
    )

    if generation_config.stage_type:
        return _filter_cultivation_cycles_events(cult_cycles, generation_config.stage_type), unbinned_events

    return cult_cycles, unbinned_events


def _filter_cultivation_cycles_events(
    cultivation_cycles: list[CultivationCycle],
    stage_type: StageTypes,
) -> list[CultivationCycle]:
    """
    Filter out all events from the cultivation cycles that are not of the specified type.
    """
    for cultivation_cycle in cultivation_cycles:
        filtered_events = []
        for event in cultivation_cycle.events:
            if stage_type in STAGE_TYPE_TO_ENTITY_EVENT:
                for event_type in STAGE_TYPE_TO_ENTITY_EVENT[stage_type]:
                    if isinstance(event, event_type):
                        filtered_events.append(event)

        cultivation_cycle.events = filtered_events

    return cultivation_cycles


def _get_empty_cultivation_cycles_from_commodity_cropping_events(
    cropping_events: list[Union[CroppingEvent | FallowPeriod]],
    start_year: Optional[int],
    end_year: Optional[int],
    autogenerate_gap_cycles: bool,
) -> list[CultivationCycle]:
    """
    Returns chronological-order Cultivation Cycles with start_date and end_date populated, but no events. Cultivation
    Cycle end dates are derived from each CroppingEvent's harvest date. The next Cultivation Cycle starts on the
    day following the previous cycle's end date. Events occurring on the same date as harvest are binned in the
    Cultivation Cycle that ends with that harvest, per CAR SEP logic.

    This is a useful public function because many different types of events will need grouping by cultivation cycle.

    Because of the restriction that all CCs lengths must fall within +/- 2 months of eachother, we have to project
    backwards the starting date of the first CC, because it doesn't benefit from the additional length of the harvest ->
    planting from the previous cultivation cycle.
    """
    # Sort the Cropping Events ASC by end date, breaking ties by start date
    harvest_sorted_cropping_events: list[Union[CroppingEvent | FallowPeriod]] = sorted(
        cropping_events, key=lambda event: (event.interval.end, event.interval.start or event.interval.end)
    )

    previous_cycle_end: datetime | None = None
    previous_cover_cropping_events: list[CroppingEvent] = []
    cultivation_cycles: list[CultivationCycle] = []
    for current_cropping_event in harvest_sorted_cropping_events:
        if isinstance(current_cropping_event, CroppingEvent) and current_cropping_event.crop_usage in COVER_CROP_USAGES:
            # Cover cropping events are used solely to adjust autogenerated cycles to NOT split these events
            previous_cover_cropping_events.append(current_cropping_event)
            continue
        if not previous_cycle_end:
            # The first CC derived from the first crop event will be set to one year for consistency
            # with other cycles (adjusted to contain the first crop event if needed)
            cycle_start = min(
                _round_up_datetime(current_cropping_event.interval.end) - relativedelta(years=1) + timedelta(minutes=1),
                _round_down_datetime(
                    current_cropping_event.interval.start or datetime.max.replace(tzinfo=timezone.utc)
                ),
            )
            cycle_start = _adjust_cycle_start_to_contain_split_cover_cropping_events(
                cycle_start=cycle_start, cover_cropping_events=previous_cover_cropping_events
            )
            # Add necessary leading cycles before the first crop event to the beginning of the cycles list
            if start_year is not None:
                cultivation_cycles.extend(
                    _autogenerate_leading_cycles(
                        previous_cover_cropping_events=previous_cover_cropping_events,
                        first_crop_cycle_start=cycle_start,
                        start_year=start_year,
                    )
                )
        else:
            # If this harvest event has the same harvest date as the previous harvest event,
            # then skip it, because a CC has already been generated for this harvest date.
            if _round_up_datetime(current_cropping_event.interval.end) == previous_cycle_end:
                continue
            # CCs should have start dates one day after the previous CC's end date
            cycle_start = previous_cycle_end + timedelta(minutes=1)
            if autogenerate_gap_cycles:
                gap_cycles, cycle_start = _autogenerate_gap_cycles(
                    current_cropping_event=current_cropping_event,
                    previous_cover_cropping_events=previous_cover_cropping_events,
                    cycle_start=cycle_start,
                )
                cultivation_cycles.extend(gap_cycles)
        cycle_end = _round_up_datetime(current_cropping_event.interval.end)
        cultivation_cycles.append(
            CultivationCycle(
                id=_generate_cultivation_cycle_id(
                    crop_event_id=current_cropping_event.id,
                    crop_type=(
                        current_cropping_event.crop_type
                        if isinstance(current_cropping_event, CroppingEvent)
                        else NoCropType.FALLOW.value
                    ),
                    cycle_start=cycle_start,
                    cycle_end=cycle_end,
                ),
                start=cycle_start,
                end=cycle_end,
            )
        )
        previous_cycle_end = cycle_end
        previous_cover_cropping_events = []
    if cultivation_cycles and end_year is not None:
        cultivation_cycles.extend(
            _autogenerate_ending_cycles(
                previous_cover_cropping_events=previous_cover_cropping_events,
                last_crop_cycle_end=cultivation_cycles[-1].end,
                end_year=end_year,
            )
        )
    return cultivation_cycles


def _get_cover_cropping_events_split_by_cycle_start_or_end(
    cover_cropping_events: list[CroppingEvent], cycle_start_or_end: datetime
) -> list[CroppingEvent]:
    return [
        e
        for e in cover_cropping_events
        if e.interval.start is not None
        and e.interval.end is not None
        and e.interval.start < cycle_start_or_end <= e.interval.end
    ]


def _adjust_cycle_start_to_contain_split_cover_cropping_events(
    cycle_start: datetime, cover_cropping_events: list[CroppingEvent]
) -> datetime:
    # If any cover cropping events are split by the cycle start date, then shift
    # the cycle start date backwards, until all split cover cropping events are
    # entirely contained in the cycle.
    split_cover_cropping_events = _get_cover_cropping_events_split_by_cycle_start_or_end(
        cover_cropping_events=cover_cropping_events, cycle_start_or_end=cycle_start
    )
    while split_cover_cropping_events:
        cycle_start = min([e.interval.start for e in split_cover_cropping_events])
        split_cover_cropping_events = _get_cover_cropping_events_split_by_cycle_start_or_end(
            cover_cropping_events=cover_cropping_events, cycle_start_or_end=cycle_start
        )
    return _round_down_datetime(cycle_start)


def _adjust_cycle_end_to_contain_split_cover_cropping_events(
    cycle_end: datetime, cover_cropping_events: list[CroppingEvent]
) -> datetime:
    # If any cover cropping events are split by the cycle end date, then shift
    # the cycle end date backwards, until all split cover cropping events are
    # entirely contained in the next cycle.
    split_cover_cropping_events = _get_cover_cropping_events_split_by_cycle_start_or_end(
        cover_cropping_events=cover_cropping_events, cycle_start_or_end=cycle_end
    )
    while split_cover_cropping_events:
        cycle_end = min([e.interval.start for e in split_cover_cropping_events]) - timedelta(days=1)
        split_cover_cropping_events = _get_cover_cropping_events_split_by_cycle_start_or_end(
            cover_cropping_events=cover_cropping_events, cycle_start_or_end=cycle_end
        )
    return _round_up_datetime(cycle_end)


def _autogenerate_leading_cycles(
    previous_cover_cropping_events: list[CroppingEvent],
    first_crop_cycle_start: datetime,
    start_year: int,
) -> list[CultivationCycle]:
    """
    Because the first cultivation cycles in the crop history could be fallow cycles, we need to rely on the
    Enrollment Cropping stage's configured Start Year to know how many, if any, leading cultivation cycles should be
    added at the beginning of the history.
    """
    leading_cycles: list[CultivationCycle] = []
    cycle_end = first_crop_cycle_start - timedelta(minutes=1)
    while cycle_end.year >= start_year:
        cycle_start = cycle_end - relativedelta(years=1) + timedelta(minutes=1)
        cycle_start = _adjust_cycle_start_to_contain_split_cover_cropping_events(
            cycle_start=cycle_start, cover_cropping_events=previous_cover_cropping_events
        )
        leading_cycle = CultivationCycle(
            id=_generate_cultivation_cycle_id(
                crop_event_id=None,
                crop_type=None,
                cycle_start=cycle_start,
                cycle_end=cycle_end,
                index=1,
            ),
            start=cycle_start,
            end=cycle_end,
        )
        leading_cycles = [leading_cycle] + leading_cycles
        cycle_end = cycle_start - timedelta(minutes=1)
    return leading_cycles


def _autogenerate_gap_cycles(
    current_cropping_event: CroppingEvent | FallowPeriod,
    previous_cover_cropping_events: list[CroppingEvent],
    cycle_start: datetime,
) -> tuple[list[CultivationCycle], datetime]:
    """
    Autogenerates gap cycles if there is a gap of more than 18 months between the
    the previous harvest event and the current harvest event.
    """
    gap_cycles: list[CultivationCycle] = []
    cycle_end = cycle_start + relativedelta(years=1) - timedelta(minutes=1)
    crop_start = current_cropping_event.interval.start
    crop_end = current_cropping_event.interval.end
    while cycle_start + relativedelta(months=18) - timedelta(days=1) < crop_end and cycle_end < (
        crop_start or crop_end
    ):
        original_cycle_end = cycle_end
        cycle_end = _adjust_cycle_end_to_contain_split_cover_cropping_events(
            cycle_end=cycle_end, cover_cropping_events=previous_cover_cropping_events
        )
        # If a cover cropping event, or series of overlapping cover cropping events, spans
        # the entirety of this gap cycle AND overlaps into the next, then this gap cycle
        # will be reduced to nothing. The next gap cycle will need to be longer than this
        # gap cycle, in order to contain these events. For simplicity, add 1 buffer year.
        if cycle_end < cycle_start:
            cycle_end = original_cycle_end + relativedelta(years=1) - timedelta(days=1)
            continue
        gap_cycle = CultivationCycle(
            id=_generate_cultivation_cycle_id(
                crop_event_id=None,
                crop_type=None,
                cycle_start=cycle_start,
                cycle_end=cycle_end,
                index=get_unique_index_for_next_empty_cultivation_cycle(
                    harvest_year=cycle_end.year, previous_cultivation_cycle=gap_cycles[-1] if gap_cycles else None
                ),
            ),
            start=cycle_start,
            end=cycle_end,
        )
        gap_cycles.append(gap_cycle)
        cycle_start = cycle_end + timedelta(minutes=1)
        cycle_end = cycle_start + relativedelta(years=1) - timedelta(minutes=1)
    return gap_cycles, cycle_start


def _autogenerate_ending_cycles(
    previous_cover_cropping_events: list[CroppingEvent],
    last_crop_cycle_end: datetime,
    end_year: int,
) -> list[CultivationCycle]:
    ending_cycles: list[CultivationCycle] = []
    cycle_start = last_crop_cycle_end + timedelta(minutes=1)
    cycle_end = cycle_start + relativedelta(years=1) - timedelta(minutes=1)
    # If cycle ends in end year, OR cycle ends after end year BUT starts before end
    # year, then generate it.
    while cycle_end.year <= end_year or cycle_start.year < end_year:
        original_cycle_end = cycle_end
        cycle_end = _adjust_cycle_end_to_contain_split_cover_cropping_events(
            cycle_end=cycle_end, cover_cropping_events=previous_cover_cropping_events
        )
        if cycle_end < cycle_start:
            cycle_end = original_cycle_end + relativedelta(years=1) - timedelta(days=1)
            continue
        ending_cycle = CultivationCycle(
            id=_generate_cultivation_cycle_id(
                crop_event_id=None,
                crop_type=None,
                cycle_start=cycle_start,
                cycle_end=cycle_end,
                index=get_unique_index_for_next_empty_cultivation_cycle(
                    harvest_year=cycle_end.year, previous_cultivation_cycle=ending_cycles[-1] if ending_cycles else None
                ),
            ),
            start=cycle_start,
            end=cycle_end,
        )
        ending_cycles.append(ending_cycle)
        cycle_start = cycle_end + timedelta(minutes=1)
        cycle_end = cycle_start + relativedelta(years=1) - timedelta(minutes=1)
    return ending_cycles


def _generate_cultivation_cycle_id(
    crop_event_id: EventIdType,
    crop_type: str | None,
    cycle_start: datetime,
    cycle_end: datetime,
    index: int | None = None,
) -> CultivationCycleId:
    if crop_event_id:
        return CultivationCycleId(
            crop_event_id=crop_event_id,
            crop_type=crop_type,
            start_date=cycle_start,
            end_date=cycle_end,
            harvest_year=cycle_end.year,
            index=index,
        )
    else:
        return CultivationCycleId(harvest_year=cycle_end.year, index=index)


def _bin_events_into_cultivation_cycles(
    cultivation_cycles: list[CultivationCycle],
    events: list[EntityEvent],
    should_split_irrigation_events: bool = False,
) -> tuple[list[CultivationCycle], list[EntityEvent]]:
    """
    Takes an arbitrary list of EntityEvent and bins/groups the events into the given CultivationCycles based on their
    defined date ranges.

    Assumes that input CultivationCycles have contiguous, non-overlapping date ranges, are sorted ASC, and that all
    events will fall into a single cycle.

    Edit: We've actually been directed by Karley Skill that we should be _splitting_ events if they fall across multiple
    cultivation cycles, because the current Scenarios Service baseline generation logic assumes that events are wholly
    contained in a cultivation cycle. I view this more as us accommodating their current limitations than anything else.
    I've added this as an optional parameter because it doesn't seem like we'll always want to be doing this.

    Note that while Scenarios Service's validation will fail if events within a CultivationCycle have dates outside of
    the cycle's defined date range. Within MRV, however, events with a start and end date (such as IrrigationEvent) are
    only required to have their end date within the CultivationCycle date range.
    """
    # 1) Split any events that need splitting
    if should_split_irrigation_events:
        split_events = []
        for event in events:
            # If the event isn't an irrigation event, then don't split it.
            if not isinstance(event, IrrigationEvent):
                split_events.append(event)
                continue

            # Note that this iterative splitting/clipping process might result in portions of events overflowing the
            # left/right of the collection of cultivation cycles being lost. EG if the earliest CC is (2,4) and your
            # event is (1,3), the resulting event will be (2,3) and the (1,2) portion of the original event is lost.
            for cycle in cultivation_cycles:
                if (split_event := _generate_split_event(event, cycle)) is not None:
                    split_events.append(split_event)

    events_to_bin = split_events if should_split_irrigation_events else events

    # 2) Bin the events into cultivation cycles
    unbinned_events = []
    earliest_cycle_start_date = min(cycle.start for cycle in cultivation_cycles)
    latest_cycle_end_date = max(cycle.end for cycle in cultivation_cycles)
    for event in events_to_bin:
        # Bin events using the end date, and fall back to using the start date if there is no end date.
        event_binning_date = event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()
        # If the event binning date falls before/after the earliest/latest cycles, then do not bin the event.
        if not earliest_cycle_start_date.date() <= event_binning_date.date() <= latest_cycle_end_date.date():
            unbinned_events.append(event)
            continue
        for cycle in cultivation_cycles:
            if cycle.start.date() <= event_binning_date.date() <= cycle.end.date():
                cycle.add_event(event)
                break
        else:
            unbinned_events.append(event)

    return cultivation_cycles, unbinned_events


def _generate_split_event(event: IrrigationEvent, cycle: CultivationCycle) -> Optional[EntityEvent]:
    """
    Given an event and a candidate cultivation cycle, if the event overlaps with the cultivation cycle,
    return a COPY of the event with its interval modified to be "clipped" to within the cycle.

    EG
        Event Interval:                 ---------------------
        Cycle:                      ----------
        Split Event Interval:           ------

        Event Interval:                 ---------------------
        Cycle:                                  ----
        Split Event Interval:                   ----

        Event Interval:                 ------
        Cycle:                                   -----
        Split Event Interval:                (None)
    """
    # Check for no overlap
    # Adding a day to the cycle.end (because a cycle.end of midnight on (eg) 02-11-22 should capture all events that
    # occur on the date of 02-11-22)
    if event.interval.end < cycle.start or event.interval.start >= cycle.end + timedelta(days=1):
        return None

    # There is an overlap; determine the overlap interval
    overlap_interval = Interval(start=max(event.interval.start, cycle.start), end=min(event.interval.end, cycle.end))

    # Return a deep copy of the event with the new interval
    return event.copy(update={"interval": overlap_interval}, deep=True)


def get_unique_index_for_next_empty_cultivation_cycle(
    harvest_year: int, previous_cultivation_cycle: CultivationCycle | None
) -> int:
    """
    Returns an index for the empty cultivation cycle such that the ID, consisting of
    harvest year + index, remains unique.
    """
    if (
        previous_cultivation_cycle
        and previous_cultivation_cycle.id.crop_event_id is None
        and previous_cultivation_cycle.id.harvest_year == harvest_year
    ):
        return previous_cultivation_cycle.id.index + 1
    else:
        return 1


def get_unique_index_for_previous_empty_cultivation_cycle(
    harvest_year: int, next_cultivation_cycle: CultivationCycle | None
) -> int:
    """
    Returns an index for the empty cultivation cycle such that the ID, consisting of
    harvest year + index, remains unique.
    """
    if (
        next_cultivation_cycle
        and next_cultivation_cycle.id.crop_event_id is None
        and next_cultivation_cycle.id.harvest_year == harvest_year
    ):
        return next_cultivation_cycle.id.index - 1
    else:
        return 1


def get_reporting_period_cultivation_cycles(ccs: list[CultivationCycle], program: Programs) -> list[CultivationCycle]:
    return [
        cc
        for cc in ccs
        if pytz.UTC.localize(program.reporting_period_start_date)
        <= cc.end
        <= pytz.UTC.localize(program.reporting_period_end_date)
    ]


def get_unbinned_events_time_delta_from_cultivation_cycles(
    cultivation_cycles: list[CultivationCycle], unbinned_events: list[EntityEvent]
) -> tuple[timedelta, timedelta]:
    """Returns a tuple containing the time delta between the earliest unbinned event
    and the earliest cultivation cycle, and the time delta between the latest unbinned
    event and the latest cultivation cycle."""
    cultivation_cycles = sorted(cultivation_cycles, key=lambda x: x.start)
    cultivation_cycles_start_date = cultivation_cycles[0].start
    cultivation_cycles_end_date = cultivation_cycles[-1].end
    min_start_date = cultivation_cycles_start_date
    max_end_date = cultivation_cycles_end_date
    for event in unbinned_events:
        event_binning_date = event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()
        if event_binning_date < min_start_date:
            min_start_date = event_binning_date
        elif event_binning_date > max_end_date:
            max_end_date = event_binning_date
    return cultivation_cycles_start_date - min_start_date, max_end_date - cultivation_cycles_end_date


def _round_up_datetime(dt: datetime) -> datetime:
    """Sets the time portion of the datetime to 23:59:00. Cultivation cycles should end at 12:59:00."""
    return dt.replace(hour=23, minute=59)


def _round_down_datetime(dt: datetime) -> datetime:
    """Sets the time portion of the datetime to 00:00:00. Cultivation cycles should start at 00:00:00."""
    return dt.replace(hour=0, minute=0)
