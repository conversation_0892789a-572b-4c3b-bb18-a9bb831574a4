from typing import Optional

from defaults.attribute_options import CropUsage, IrrigationMethods, TerminationMethods
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.helpers import (
    application_products_values_to_products,
    application_water_amount_unit_value_to_enum,
    harvest_yield_rate_unit_value_to_enums,
    irrigation_subsurface_depth_unit_value_to_enum,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.reduction_event import ReductionEvent, StandardReductionTypes
from entity_events.events.tillage_event import TillageEvent
from entity_events.units import LengthUnit
from logger import get_logger
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from values.enums import EntityTypeChoices

"""
Program 154: <PERSON><PERSON><PERSON>'s Ingrained 2023
Last Analysis: 10/11/2023
Question: Does the program have all of the attributes configured to construct each event?

Note: Recall that in the E phase, this program made the weird choice to also collect "partial" cropping events for
the year 2023 -- these have planting dates but no harvest dates. These will just result in no Cropping/Reduction event
being created, which is a nice thing.

E Phase CroppingEvent + ReductionEvent
- Missing CropUsage
    - We'll have to determine CropUsage by the product + perhaps the season
    - It doesn't have winter_crop_termination and residue_harvested; what are the dependencies around that?
        * ResidueHarvested is available when CropType is not no cover, and when Record year is not 2023
        * Termination is available when CropType is not no cover, and when Record year is not 2023
        * So basically we have to ignore one of these pieces of data depending on which crop usage we determine

E Phase TillageEvent
- Missing Soil Inversion
    - We'll have to determine Soil Inversion by the depth of the tillage
- Missing Strip Tillage (optional anyways)

E Phase IrrigationEvent
* Looks good!

E Phase ApplicationEvent
- Doesn't have additives (optional anyways)

M Phase CroppingEvent + ReductionEvent
- Missing CropUsage
    - We'll have to determine CropUsage by the product + perhaps the season
    - It doesn't have winter_crop_termination and residue_harvested; what are the dependencies around that?
        * ResidueHarvested is available when CropType is not no cover, and when Record year is not 2023
        * Termination is available when CropType is not no cover, and when Record year is not 2023
        * So basically we have to ignore one of these pieces of data depending on which crop usage we determine

M Phase TillageEvent
- (For some reason this DOES have soil inversion, unlike the E phase)
- Missing Strip Tillage (optional anyways)

M Phase IrrigationEvent
* Looks good!

M Phase ApplicationEvent
* Looks good!

"""

logger = get_logger(__name__)


def _get_crop_usage_from_crop(crop_type: str) -> CropUsage:
    """
    Determine the crop_usage for every distinct crop being used
    Note: This really shouldn't be used by anyone else. If you have to understand this
    please reach out to Brian or Sam; It's only here because we didn't collect data correctly, and the
    majority of ACTUAL projects aren't using the vast majority of these crops. We don't recommend
    using crop type to infer crop usage.
    This is the INITIAL crop list from the follow thread:
    https://regrowag.slack.com/archives/C04TREM1NLS/p1697052452514969?thread_ts=1697049852.319099&cid=C04TREM1NLS
    """
    crop_usage_lookup = {
        "alfalfa": CropUsage.COVER,  # Not C
        "barley": CropUsage.COVER,  # Not C
        "clover": CropUsage.COVER,  # C
        "corn": CropUsage.COMMODITY,
        "cotton": CropUsage.COMMODITY,
        "dry_beans": CropUsage.COMMODITY,
        "hay": CropUsage.COVER,
        "pasture": CropUsage.COVER,  # Not C
        "potatoes": CropUsage.COMMODITY,
        "radishes": CropUsage.COVER,  # C
        "rice": CropUsage.COMMODITY,
        "sorghum": CropUsage.COMMODITY,
        "soybeans": CropUsage.COMMODITY,
        "sugar_beets": CropUsage.COMMODITY,
        "sunflowers": CropUsage.COVER,  # Not C
        "sweet_corn": CropUsage.COMMODITY,
        "triticale": CropUsage.COVER,  # C
        "turnips": CropUsage.COVER,  # C
        "vetch": CropUsage.COVER,  # C
        "wheat_spring": CropUsage.COMMODITY,
        "wheat_winter": CropUsage.COVER,  # Not C
    }
    return crop_usage_lookup[crop_type]


def _create_reduction_event(
    attribute_type_to_value: dict[AttributeTypes, str], crop_usage: CropUsage
) -> Optional[ReductionEvent]:
    """
    The program doesn't have a notion of CropUsage; So it collects both the termination method and the
    residue_harvested. Depending on the crop_usage that we infer from the crop type, we'll create an appropriate
    reduction event using either Termination(For Cover) or Residue Harvested(For Commodity)

    Since the E/M phase cropping stages collect only differ in dependencies (and this takes place after dependency
    filtering, we should be able to use this for both E and M phase stages.
    """
    reduction_date = attribute_type_to_value.get(AttributeTypes.harvest_date)

    data = {
        "occurred_at": reduction_date,
    }
    if crop_usage == CropUsage.COMMODITY:
        """For commodity crops, reductions should be defined by residue_harvested. We assume the termination method is a
        "grain harvest", though it won't be selected by the user."""
        match attribute_type_to_value.get(AttributeTypes.residue_harvested):
            case "No residue harvested" | "0%":
                data = {**data, **StandardReductionTypes.get_harvest_reduction_fractions(".05", ".95")}
            case "25%":
                data = {**data, **StandardReductionTypes.get_harvest_reduction_fractions(".25", ".75")}
            case "50%":
                data = {**data, **StandardReductionTypes.get_harvest_reduction_fractions(".5", ".5")}
            case "75%":
                data = {**data, **StandardReductionTypes.get_harvest_reduction_fractions(".75", ".25")}
            case "All residue harvested" | "100%":
                data = {**data, **StandardReductionTypes.HAY_100}
    elif crop_usage == CropUsage.COVER:
        """For cover crops, the reduction event should be defined by termination_method. We assume that all residue
        stays on the field for grain harvest and all is removed for a forage harvest."""
        termination_method = attribute_type_to_value.get(AttributeTypes.winter_crop_termination)
        match termination_method:
            case TerminationMethods.forage_harvest:
                data = {**data, **StandardReductionTypes.HAY_100}
            case TerminationMethods.grain_harvest:
                data = {**data, **StandardReductionTypes.GRAIN_HARVEST}
            case TerminationMethods.mechanical:
                data = {**data, **StandardReductionTypes.MOW_100}
            case TerminationMethods.tillage | TerminationMethods.winterkill | TerminationMethods.herbicide:
                return None
    else:
        raise ValueError(f"Unexpected crop_usage attribute: {crop_usage}.")

    return ReductionEvent.parse_obj(data)


def create_cropping_event_154_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    """
    Given a mapping of attribute_Type: value for data in a single UI stage-row, create and return a Cropping event if
    possible, else None

    We have to determine the Crop Usage based on the crop being used

    (11/29/2023): To ask the question of whether we can reuse this for E/M Cropping Stages
    Do the E/M Cropping Stages collect the same attributes?: Yes
    Do the E/M Cropping Stages have the same attribute options?: Yes, but for CropType M is a subset of E
        - The M only has "rice" enabled, whereas the E has many crops, incl rice (This is fine)
    Do the E/M Cropping Stages use the same dependencies?: No. The M phase
    doesn't have ANY dependencies. We should have separate functions for e and m
    """

    def _filter_values_using_e_cropping_dependencies(
        attribute_type_to_value: dict[AttributeTypes, str],
    ) -> dict[AttributeTypes, str]:
        """
        Given an attribute_type_to_value dict representing values for a single UI row (any stage),
        filter the attribute_type_to_value dict based on E-phase Cropping dependencies

        The current (11/28/2023) dependencies for the e phase cropping stage in 154 are:
        * crop_yield is available when record_year is not 2023 x
        * crop_yield is available when winter_crop_termination is not Tillage x
        * crop_yield is available when crop_type is not no cover x
        * crop_yield is available when winter_crop_termination is not Herbicide x
        * crop_yield is available when winter_crop_termination is not Mechanical x
        * crop_yield is available when winter_crop_termination is not Winterkill x
        * harvest_date is available when record_year is not 2023 x
        * harvest_date is available when crop_type is not no cover x
        * planting_date is available when crop_type is not no cover x
        * residue_harvested is available when record_year is not 2023 x
        * residue_harvested is available when crop_type is not no cover x
        * termination_method is available when record_year is not 2023 x
        * termination_method is available when crop_type is not no cover x
        * yield_rate_unit is available when winter_crop_termination is not Tillage x
        * yield_rate_unit is available when crop_type is not no cover x
        * yield_rate_unit is available when winter_crop_termination is not Herbicide x
        * yield_rate_unit is available when winter_crop_termination is not Mechanical x
        * yield_rate_unit is available when winter_crop_termination is not Winterkill x

        Basically, take the inverse of these rules and filter out items (in a pure fashion) from attribute_type_to_value
        """
        copy = attribute_type_to_value.copy()

        if copy.get(AttributeTypes.record_year) == "2023":
            dependency_blocked_attributes = [
                AttributeTypes.crop_yield,
                AttributeTypes.harvest_date,
                AttributeTypes.residue_harvested,
                AttributeTypes.winter_crop_termination,
            ]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        if copy.get(AttributeTypes.winter_crop_termination) in ("Tillage", "Herbicide", "Mechanical", "Winterkill"):
            dependency_blocked_attributes = [AttributeTypes.crop_yield, AttributeTypes.yield_rate_unit]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        if copy.get(AttributeTypes.crop_type) in ("fallow", "no cover"):
            dependency_blocked_attributes = [
                AttributeTypes.crop_yield,
                AttributeTypes.yield_rate_unit,
                AttributeTypes.planting_date,
                AttributeTypes.harvest_date,
                AttributeTypes.winter_crop_termination,
                AttributeTypes.residue_harvested,
            ]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        return copy

    filtered_attribute_type_to_value = _filter_values_using_e_cropping_dependencies(attribute_type_to_value)

    # Fallow "cropping events" don't get any MRV CroppingEvent created for them
    if filtered_attribute_type_to_value.get(AttributeTypes.crop_type) in ("fallow", "no cover"):
        return None

    # ~~ Prepare our data needed to create a CroppingEvent ~~

    yield_rate_numerator_unit, yield_rate_denominator_unit = harvest_yield_rate_unit_value_to_enums(
        filtered_attribute_type_to_value.get(AttributeTypes.yield_rate_unit)
    )

    crop_usage = _get_crop_usage_from_crop(filtered_attribute_type_to_value[AttributeTypes.crop_type])

    reduction_event = _create_reduction_event(filtered_attribute_type_to_value, crop_usage)

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": filtered_attribute_type_to_value.get(AttributeTypes.planting_date),
            "end": filtered_attribute_type_to_value.get(AttributeTypes.harvest_date),
        },
        "crop_type": filtered_attribute_type_to_value.get(AttributeTypes.crop_type),
        "crop_usage": crop_usage,
        "crop_yield": (
            {
                "value": filtered_attribute_type_to_value.get(AttributeTypes.crop_yield),
                "numerator_unit": yield_rate_numerator_unit,
                "denominator_unit": yield_rate_denominator_unit,
            }
            if filtered_attribute_type_to_value.get(AttributeTypes.crop_yield) is not None
            else None
        ),
        "reductions": [reduction_event],
    }

    return CroppingEvent.parse_obj(data)


def create_cropping_event_154_m(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    """
    The one difference with the M phase is that the dependencies around record year aren't present; so we have to
    implement some different dependency filtering from that we do in the create_cropping_event_154_e function
    """
    # There are no M phase cropping stage dependencies, so no filtering is needed.

    # Fallow "cropping events" don't get any MRV CroppingEvent created for them
    if attribute_type_to_value.get(AttributeTypes.crop_type) in ("fallow", "no cover"):
        return None

    # ~~ Prepare our data needed to create a CroppingEvent ~~

    yield_rate_numerator_unit, yield_rate_denominator_unit = harvest_yield_rate_unit_value_to_enums(
        attribute_type_to_value.get(AttributeTypes.yield_rate_unit)
    )

    crop_usage = _get_crop_usage_from_crop(attribute_type_to_value[AttributeTypes.crop_type])

    reduction_event = _create_reduction_event(attribute_type_to_value, crop_usage)

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": attribute_type_to_value.get(AttributeTypes.planting_date),
            "end": attribute_type_to_value.get(AttributeTypes.harvest_date),
        },
        "crop_type": attribute_type_to_value.get(AttributeTypes.crop_type),
        "crop_usage": crop_usage,
        "crop_yield": (
            {
                "value": attribute_type_to_value.get(AttributeTypes.crop_yield),
                "numerator_unit": yield_rate_numerator_unit,
                "denominator_unit": yield_rate_denominator_unit,
            }
            if attribute_type_to_value.get(AttributeTypes.crop_yield) is not None
            else None
        ),
        "reductions": [reduction_event],
    }

    return CroppingEvent.parse_obj(data)


def _filter_values_using_tillage_dependencies(
    attribute_type_to_value: dict[AttributeTypes, str],
) -> dict[AttributeTypes, str]:
    """
    This can be re-used by both the E and M create_tillage_event functions

    It's fine to just implement the dependencies that are in the M phase, because they're the superset of those
    in the E phase (the only addition is the soil inversion, and we're safely popping off items from a dict)

    Given an attribute_type_to_value dict representing values for a single UI row (any stage),
        filter the attribute_type_to_value dict based on M-phase Cropping dependencies

    soil_inversion is available when TillageEvent is 1
    tillage_date is available when TillageEvent is 1
    tillage_depth is available when TillageEvent is 1

    Basically take the INVERSE of these rules and use them to pop off items from the dict
    """
    copy = attribute_type_to_value.copy()

    if copy.get(AttributeTypes.tillage_event) != "1":
        dependency_blocked_attributes = [
            AttributeTypes.soil_inversion,
            AttributeTypes.tillage_date,
            AttributeTypes.tillage_depth,
        ]
        for attribute_type in dependency_blocked_attributes:
            copy.pop(attribute_type, None)

    return copy


def create_tillage_event_154_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[TillageEvent]:
    """
    Given a mapping of attribute_Type: value for data in a single UI stage-row, create and return a Tillage event if
    possible, else None

    This is the normal base_create_tillage_event, except it sets soil_inversion to false by default, since we didn't
    collect it in the E phase, and because this is a Rice program that's unlikely to actually use mold board plows.
    """
    # Filter MRV Values that are hidden by Tillage structured stages dependencies
    filtered_attribute_type_to_value = _filter_values_using_tillage_dependencies(attribute_type_to_value)

    # Check for "No tillage event" toggle
    if filtered_attribute_type_to_value.get(AttributeTypes.tillage_event) == "0":
        return None

    # We don't have to filter out tillage depth=0 dicts, because it's not one of the categorical options

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "occurred_at": filtered_attribute_type_to_value.get(AttributeTypes.tillage_date),
        "depth": {
            "value": filtered_attribute_type_to_value.get(AttributeTypes.tillage_depth),
            "unit": LengthUnit.CENTIMETRE,  # We always send CM values to the backend mrv_values
        },
        "soil_inversion": False,  # Blanket default for E phase data
        "strip_fraction": filtered_attribute_type_to_value.get(AttributeTypes.strip_fraction),  # Going to be None
    }

    return TillageEvent.parse_obj(data)


def create_tillage_event_154_m(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[TillageEvent]:
    """
    The M phase tillage stage differs from the E phase tillage stage in that it:
    - Collects soil inversion
    """
    # Filter MRV Values that are hidden by Tillage structured stages dependencies
    filtered_attribute_type_to_value = _filter_values_using_tillage_dependencies(attribute_type_to_value)

    # Check for "No tillage event" toggle
    if filtered_attribute_type_to_value.get(AttributeTypes.tillage_event) == "0":
        return None

    # We don't have to filter out tillage depth=0 dicts, because it's not one of the categorical options

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "occurred_at": filtered_attribute_type_to_value.get(AttributeTypes.tillage_date),
        "depth": {
            "value": filtered_attribute_type_to_value.get(AttributeTypes.tillage_depth),
            "unit": LengthUnit.CENTIMETRE,  # We always send CM values to the backend mrv_values
        },
        "soil_inversion": filtered_attribute_type_to_value.get(AttributeTypes.soil_inversion),  # Collected in M
        "strip_fraction": filtered_attribute_type_to_value.get(AttributeTypes.strip_fraction),  # Going to be None
    }

    return TillageEvent.parse_obj(data)


def create_application_event_154(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[ApplicationEvent]:
    """
    Given a mapping of attribute_Type: value for data in a single UI stage-row, create and return an Application event
    if possible, else None

    This should work for both the E/M stages because they share attributes,options, and dependencies
    """

    def _filter_values_using_application_dependencies(
        attribute_type_to_value: dict[AttributeTypes, str],
    ) -> dict[AttributeTypes, str]:
        """
        Both E and M share the same dependencies
        As of (11/29/2023):
        * application_depth is available when application_method is not Broadcasted
        * water_amount is available when application_method is Fertigation
        * water_amount_unit is available when application_method is Fertigation

        Basically use the inverse of these to do filtering on the values in the dict
        """
        copy = attribute_type_to_value.copy()

        if copy.get(AttributeTypes.application_method) == "Broadcasted":
            copy.pop(AttributeTypes.application_depth, None)
        if copy.get(AttributeTypes.application_method != "Fertigation"):
            dependency_blocked_attributes = [AttributeTypes.water_amount, AttributeTypes.water_amount_unit]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        return copy

    filtered_attribute_type_to_value = _filter_values_using_application_dependencies(attribute_type_to_value)

    # (There's no "Application Y/N toggle in this program in either phase, so don't have to worry about that)
    # Look out for <= 0 application amounts; that's not an application
    if float(filtered_attribute_type_to_value[AttributeTypes.application_rate]) <= 0:
        return None
    # Water doesn't constitute a legit ApplicationEvent
    if filtered_attribute_type_to_value.get(AttributeTypes.application_product) == "water":
        return None

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "occurred_at": filtered_attribute_type_to_value.get(AttributeTypes.application_date),
        "products": application_products_values_to_products(
            filtered_attribute_type_to_value.get(AttributeTypes.application_product),
            filtered_attribute_type_to_value.get(AttributeTypes.application_rate),
            filtered_attribute_type_to_value.get(AttributeTypes.application_rate_type),
            filtered_attribute_type_to_value.get(AttributeTypes.application_rate_unit),
        ),
        "method": filtered_attribute_type_to_value.get(AttributeTypes.application_method),
        "depth": (
            {
                "value": filtered_attribute_type_to_value.get(AttributeTypes.application_depth),
                "unit": LengthUnit.CENTIMETRE,
            }
            if filtered_attribute_type_to_value.get(AttributeTypes.application_depth) is not None
            else None
        ),
        "water_amount": (
            {
                "value": filtered_attribute_type_to_value.get(AttributeTypes.water_amount),
                "unit": application_water_amount_unit_value_to_enum(
                    filtered_attribute_type_to_value.get(AttributeTypes.water_amount_unit)
                ),
            }
            if filtered_attribute_type_to_value.get(AttributeTypes.water_amount) is not None
            else None
        ),
    }

    return ApplicationEvent.parse_obj(data)


def create_irrigation_event_154(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[IrrigationEvent]:
    """
    Given a mapping of attribute_Type: value for data in a single UI stage-row, create and return an Irrigation event
    if possible, else None

    Can we use the same E and M?
    They have the same attributes
    The options differ only in that the E has [in, cm] and the M has []? Though in the UI they can still select in/cm/mm
    The dependencies on these stages are functionally identical (E's is "longer" but does the same thing; I'll use E)

    Note that this program has AWD, which should just be interpreted as flood
    """

    def _filter_dependencies(attribute_type_to_value: dict[AttributeTypes, str]) -> dict[AttributeTypes, str]:
        """
        Filter values data using the (shared E/M) dependencies for the Irrigation stage

        (11/29/2023 dependencies)
        * end_date is available when irrigation_enabled is 1
        * flood_pct is available when irrigation_enabled is 1
        * flood_pct is available when irrigation_method is Furrow
        * irrigation_method is available when irrigation_enabled is 1
        * start_date is available when irrigation_enabled is 1
        * subsurface_drip_depth is available when irrigation_method is Subsurface drip
        * subsurface_drip_depth_unit is available when irrigation method

        Basically apply the inverse of these rules to do filtering
        """
        copy = attribute_type_to_value.copy()

        if copy.get(AttributeTypes.irrigation_enabled) != "1":
            dependency_blocked_attributes = [
                AttributeTypes.end_date,
                AttributeTypes.flood_pct,
                AttributeTypes.irrigation_method,
                AttributeTypes.start_date,
            ]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        if copy.get(AttributeTypes.irrigation_method) != "Furrow":
            copy.pop(AttributeTypes.flood_pct, None)

        if copy.get(AttributeTypes.irrigation_method) != "Subsurface drip":
            dependency_blocked_attributes = [
                AttributeTypes.subsurface_drip_depth,
                AttributeTypes.subsurface_drip_depth_unit,
            ]
            for attribute_type in dependency_blocked_attributes:
                copy.pop(attribute_type, None)

        return copy

    filtered_attribute_type_to_value = _filter_dependencies(attribute_type_to_value)

    if filtered_attribute_type_to_value.get(AttributeTypes.irrigation_enabled) == "0":
        return None

    # If the irrigation method was selected to be AWD, consider it to be flood
    if (
        irrigation_method := filtered_attribute_type_to_value.get(AttributeTypes.irrigation_method)
    ) == IrrigationMethods.alternating_wet_dry:
        irrigation_method = IrrigationMethods.flood

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": filtered_attribute_type_to_value.get(AttributeTypes.start_date),
            "end": filtered_attribute_type_to_value.get(AttributeTypes.end_date),
        },
        "method": irrigation_method,
        "flood_percentage": filtered_attribute_type_to_value.get(AttributeTypes.flood_pct),
        "subsurface_depth": (
            {
                "value": filtered_attribute_type_to_value.get(AttributeTypes.subsurface_drip_depth),
                "unit": irrigation_subsurface_depth_unit_value_to_enum(
                    filtered_attribute_type_to_value.get(AttributeTypes.subsurface_drip_depth_unit)
                ),
            }
            if filtered_attribute_type_to_value.get(AttributeTypes.subsurface_drip_depth) is not None
            else None
        ),
    }

    return IrrigationEvent.parse_obj(data)


def pre_filter_154_values(
    values_data: dict[PhaseTypes, dict[int, dict[StageTypes, dict[int, dict[AttributeTypes, str]]]]],
) -> dict[PhaseTypes, dict[int, dict[StageTypes, dict[int, dict[AttributeTypes, str]]]]]:
    # We want to remove the E phase 2023 cropping events if the E phase is present
    if PhaseTypes.ENROLMENT in values_data:
        enrolment_data = values_data[PhaseTypes.ENROLMENT]

        # Get the E Phase Historical Crop Rotation data
        cropping_stage_datas = [
            enrolment_data[entity_id][StageTypes.HISTORICAL_CROP_ROTATION] for entity_id in enrolment_data
        ]

        for cropping_stage_data in cropping_stage_datas:
            # Each cropping_stage_data element is a dict of row_id to attribute_type_to_value (ATTV)
            # Delete the items (ie "rows") from the cropping_stage_data_element where the row's ATTV is 2023 record year
            # We have to do it in this weird kinda way to avoid changing the size of the dict WHILE iterating
            # We want the "rows" having data for 2023 crops, since those got "duplicated" into the M phase.
            rows_to_delete = [
                row for row, attv in cropping_stage_data.items() if attv.get(AttributeTypes.record_year) == "2023"
            ]
            for row in rows_to_delete:
                del cropping_stage_data[row]

    return values_data
