from typing import Optional, <PERSON>
from uuid import UUID

from defaults.attribute_options import CropUsage
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.event_creators.helpers import (
    filter_cropping_values_using_dependencies,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.helpers import is_fallow_crop_type
from logger import get_logger
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices

"""
Program 1215: SustainConnect 2025

E Phase CroppingEvent + ReductionEvent
- Intended Commodity Crops stage does not have planting_date and harvest_date attributes.
    * To make it pass event validation, we are setting arbitrary dates based on record year.

E Phase TillageEvent
* Looks good!

E Phase IrrigationEvent
* Looks good!

E Phase ApplicationEvent
* Looks good!

M Phase
* No stages configured so far

"""

logger = get_logger(__name__)


AUTO_ASSIGN_INTENDED_CROPPING_START_MONTH = 5
AUTO_ASSIGN_INTENDED_CROPPING_START_DAY = 1
AUTO_ASSIGN_INTENDED_CROPPING_END_MONTH = 5
AUTO_ASSIGN_INTENDED_CROPPING_END_DAY = 31


def create_intended_cropping_event_1215_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> Optional[Union[CroppingEvent | FallowPeriod]]:
    """
    Given a mapping of attribute_type:value for data in a single UI stage-row, create and return a Cropping Event if
    possible, else None. This version of the function is made to work with the Intended Commodity Crops stage.
    """
    filtered_attribute_type_to_value = filter_cropping_values_using_dependencies(attribute_type_to_value)

    # Check for fallow crop_type
    if is_fallow_crop_type(filtered_attribute_type_to_value.get(AttributeTypes.crop_type)):
        if event_creation_specification.error_handling == EventCreatorErrorHandling.RAISE_ERROR:
            raise ValueError(
                f"Intended Commodity Crop stage should not produce fallow cropping events. field_id: {entity_id}"
            )
        return None

    start = filtered_attribute_type_to_value.get(AttributeTypes.planting_date)
    end = filtered_attribute_type_to_value.get(AttributeTypes.harvest_date)
    record_year = filtered_attribute_type_to_value.get(AttributeTypes.record_year)

    # Since 1215 does not have planting date and harvest date attribute, we are setting an arbitrary date
    if start is None:
        start = f"{record_year}-{AUTO_ASSIGN_INTENDED_CROPPING_START_MONTH}-{AUTO_ASSIGN_INTENDED_CROPPING_START_DAY}T11:00:00Z"
    if end is None:
        end = (
            f"{record_year}-{AUTO_ASSIGN_INTENDED_CROPPING_END_MONTH}-{AUTO_ASSIGN_INTENDED_CROPPING_END_DAY}T11:00:00Z"
        )

    data = {
        "id": event_id,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": start,
            "end": end,
        },
        "is_intended": True,
        "crop_type": filtered_attribute_type_to_value.get(AttributeTypes.crop_type),
        "crop_usage": CropUsage.COMMODITY,
        "reductions": [],
    }

    return CroppingEvent.parse_obj(data)
