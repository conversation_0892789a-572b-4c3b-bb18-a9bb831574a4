"""
Program 1143's Nutrient Management and Irrigation Phases both have issues and need to
be discarded. Irrigation did not collect dates, and Nutrient Management was only collected
for 2024. This Event Creator will discard all events for NM and Irrigation.
"""

from uuid import UUID

from entity_events.data_classes import EventCreationSpecification
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def create_empty_application_event(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> TillageEvent | None:
    return None


def create_empty_irrigation_event(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> IrrigationEvent | None:
    return None
