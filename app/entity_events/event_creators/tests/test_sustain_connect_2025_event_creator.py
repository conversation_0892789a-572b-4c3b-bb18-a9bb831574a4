from datetime import datetime
from uuid import uuid4

import pytest
from dateutil.tz import tzutc

from defaults.attribute_options import CropUsage
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.event_creators.sustain_connect_2025_event_creator import (
    AUTO_ASSIGN_INTENDED_CROPPING_END_DAY,
    AUTO_ASSIGN_INTENDED_CROPPING_END_MONTH,
    AUTO_ASSIGN_INTENDED_CROPPING_START_DAY,
    AUTO_ASSIGN_INTENDED_CROPPING_START_MONTH,
    create_intended_cropping_event_1215_e,
)
from entity_events.measures import Interval
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def test_create_intended_cropping_event_1215_e():
    entity_id = 123
    entity_type = EntityTypeChoices.field
    event_id = uuid4()
    record_year = "2022"
    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.RAISE_ERROR)

    crop_type = "corn"
    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.crop_type: crop_type,
    }

    cropping_event = create_intended_cropping_event_1215_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event.dict() == {
        "id": event_id,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": True,
        "interval": Interval(
            start=datetime(
                int(record_year),
                AUTO_ASSIGN_INTENDED_CROPPING_START_MONTH,
                AUTO_ASSIGN_INTENDED_CROPPING_START_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
            end=datetime(
                int(record_year),
                AUTO_ASSIGN_INTENDED_CROPPING_END_MONTH,
                AUTO_ASSIGN_INTENDED_CROPPING_END_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
        ),
        "crop_type": crop_type,
        "crop_usage": CropUsage.COMMODITY,
        "crop_yield": None,
        "planting_rate": None,
        "reductions": [],
        "residue_harvested": None,
        "termination_method": None,
        "planting_method": None,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.crop_type: crop_type,
        AttributeTypes.planting_date: "2022-04-15T11:00:00Z",
        AttributeTypes.harvest_date: "2022-10-15T11:00:00Z",
    }

    cropping_event = create_intended_cropping_event_1215_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event.dict() == {
        "id": event_id,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": True,
        "interval": Interval(
            start=datetime(2022, 4, 15, 11, 0, tzinfo=tzutc()), end=datetime(2022, 10, 15, 11, 0, tzinfo=tzutc())
        ),
        "crop_type": crop_type,
        "crop_usage": CropUsage.COMMODITY,
        "crop_yield": None,
        "planting_rate": None,
        "reductions": [],
        "residue_harvested": None,
        "termination_method": None,
        "planting_method": None,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.crop_type: "fallow",
    }

    with pytest.raises(ValueError, match="Intended Commodity Crop stage should not produce fallow cropping events"):
        create_intended_cropping_event_1215_e(
            attribute_type_to_value=attribute_type_to_value,
            entity_id=entity_id,
            entity_type=entity_type,
            event_id=event_id,
            event_creation_specification=event_creation_specification,
        )

    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.SKIP_EVENT)
    cropping_event = create_intended_cropping_event_1215_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )
    assert cropping_event is None
