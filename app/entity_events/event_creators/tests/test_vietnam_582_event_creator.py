from defaults.attribute_options import <PERSON><PERSON>Usage, YieldRateUnit
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.vietnam_582_event_creator import (
    create_cropping_event_582,
)
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def test_create_cropping_event_582_aquaculture(create_cropping_event_data):
    data = {AttributeTypes.crop_type: "other"}
    cropping_event = create_cropping_event_582(
        attribute_type_to_value=data,
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )

    assert cropping_event is None  # aquaculture is filtered out in base_create_cropping_event


def test_create_cropping_event_582_long_rice(create_cropping_event_data):
    data = {
        AttributeTypes.crop_type: "rice",
        AttributeTypes.crop_usage: CropUsage.COMMODITY,
        AttributeTypes.planting_date: "2024-05-01",
        AttributeTypes.harvest_date: "2024-12-15",
        AttributeTypes.crop_yield: 2.0,
        AttributeTypes.yield_rate_unit: YieldRateUnit.BU_AC,
    }
    cropping_event = create_cropping_event_582(
        attribute_type_to_value=data,
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )

    assert cropping_event.crop_type == "long_rice"


def test_create_cropping_event_582_short_rice(create_cropping_event_data):
    data = {
        AttributeTypes.crop_type: "rice",
        AttributeTypes.crop_usage: CropUsage.COMMODITY,
        AttributeTypes.planting_date: "2024-08-01T01:00:00.000Z",
        AttributeTypes.harvest_date: "2024-12-15T01:00:00.000Z",
        AttributeTypes.crop_yield: 2.0,
        AttributeTypes.yield_rate_unit: YieldRateUnit.BU_AC,
    }
    cropping_event = create_cropping_event_582(
        attribute_type_to_value=data,
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )

    assert cropping_event.crop_type == "short_rice"
