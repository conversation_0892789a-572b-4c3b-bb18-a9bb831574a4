from datetime import datetime

from dateutil.tz import tzutc

from defaults.attribute_options import CropUsage
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.event_creators.gq_sustain_2023_event_creator import (
    AUTO_ASSIGN_FALL_TILLAGE_DAY,
    AUTO_ASSIGN_FALL_TILLAGE_MONTH,
    AUTO_ASSIGN_SPRING_TILLAGE_DAY,
    AUTO_ASSIGN_SPRING_TILLAGE_MONTH,
    AUTO_ASSIGN_SUMMER_CROPPING_END_DAY,
    AUTO_ASSIGN_SUMMER_CROPPING_END_MONTH,
    AUTO_ASSIGN_SUMMER_CROPPING_START_DAY,
    AUTO_ASSIGN_SUMMER_CROPPING_START_MONTH,
    AUTO_ASSIGN_WINTER_CROPPING_END_DAY,
    AUTO_ASSIGN_WINTER_CROPPING_END_MONTH,
    AUTO_ASSIGN_WINTER_CROPPING_START_DAY,
    AUTO_ASSIGN_WINTER_CROPPING_START_MONTH,
    create_fall_tillage_event_190_e,
    create_spring_tillage_event_190_e,
    create_summer_cropping_event_190_e,
    create_winter_cropping_event_190_e,
)
from entity_events.measures import Interval
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def test_create_spring_tillage_event_190_e():
    entity_id = 123
    entity_type = EntityTypeChoices.field
    row_id = 1
    record_year = "2022"
    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.RAISE_ERROR)

    tillage_practice = "reduced till"
    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.spring_tillage_practice: tillage_practice,
    }

    tillage_event = create_spring_tillage_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert tillage_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "occurred_at": datetime(
            int(record_year), AUTO_ASSIGN_SPRING_TILLAGE_MONTH, AUTO_ASSIGN_SPRING_TILLAGE_DAY, 11, 0, tzinfo=tzutc()
        ),
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": tillage_practice,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.spring_tillage_practice: "no till",
    }

    tillage_event = create_spring_tillage_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert tillage_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "occurred_at": datetime(
            int(record_year), AUTO_ASSIGN_SPRING_TILLAGE_MONTH, AUTO_ASSIGN_SPRING_TILLAGE_DAY, 11, 0, tzinfo=tzutc()
        ),
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": "no till",
    }


def test_create_summer_cropping_event_190_e():
    entity_id = 123
    entity_type = EntityTypeChoices.field
    row_id = 1
    record_year = "2022"
    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.RAISE_ERROR)

    summer_crop_type = "corn"
    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.summer_crop_type: summer_crop_type,
    }

    cropping_event = create_summer_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "interval": Interval(
            start=datetime(
                int(record_year),
                AUTO_ASSIGN_SUMMER_CROPPING_START_MONTH,
                AUTO_ASSIGN_SUMMER_CROPPING_START_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
            end=datetime(
                int(record_year),
                AUTO_ASSIGN_SUMMER_CROPPING_END_MONTH,
                AUTO_ASSIGN_SUMMER_CROPPING_END_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
        ),
        "crop_type": summer_crop_type,
        "crop_usage": CropUsage.COMMODITY,
        "crop_yield": None,
        "planting_rate": None,
        "reductions": [],
        "residue_harvested": None,
        "termination_method": None,
        "planting_method": None,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.summer_crop_type: "fallow",
    }

    cropping_event = create_summer_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event is None

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.summer_crop_type: "no cover",
    }

    cropping_event = create_summer_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event is None


def test_create_fall_tillage_event_190_e():
    entity_id = 123
    entity_type = EntityTypeChoices.field
    row_id = 1
    record_year = "2022"
    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.RAISE_ERROR)

    tillage_practice = "reduced till"
    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.fall_tillage_practice: tillage_practice,
    }

    tillage_event = create_fall_tillage_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert tillage_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "occurred_at": datetime(
            int(record_year), AUTO_ASSIGN_FALL_TILLAGE_MONTH, AUTO_ASSIGN_FALL_TILLAGE_DAY, 11, 0, tzinfo=tzutc()
        ),
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": tillage_practice,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.fall_tillage_practice: "no till",
    }

    tillage_event = create_fall_tillage_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert tillage_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "occurred_at": datetime(
            int(record_year), AUTO_ASSIGN_FALL_TILLAGE_MONTH, AUTO_ASSIGN_FALL_TILLAGE_DAY, 11, 0, tzinfo=tzutc()
        ),
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": "no till",
    }


def test_create_winter_cropping_event_190_e():
    entity_id = 123
    entity_type = EntityTypeChoices.field
    row_id = 1
    record_year = "2022"
    event_creation_specification = EventCreationSpecification(error_handling=EventCreatorErrorHandling.RAISE_ERROR)

    winter_crop_type = "rye"
    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.winter_crop_type: winter_crop_type,
    }

    cropping_event = create_winter_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event.dict() == {
        "id": None,
        "entity_id": entity_id,
        "entity_type": entity_type,
        "is_intended": False,
        "interval": Interval(
            start=datetime(
                int(record_year),
                AUTO_ASSIGN_WINTER_CROPPING_START_MONTH,
                AUTO_ASSIGN_WINTER_CROPPING_START_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
            end=datetime(
                int(record_year),
                AUTO_ASSIGN_WINTER_CROPPING_END_MONTH,
                AUTO_ASSIGN_WINTER_CROPPING_END_DAY,
                11,
                0,
                tzinfo=tzutc(),
            ),
        ),
        "crop_type": winter_crop_type,
        "crop_usage": CropUsage.COVER,
        "crop_yield": None,
        "planting_rate": None,
        "reductions": [],
        "residue_harvested": None,
        "termination_method": None,
        "planting_method": None,
    }

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.winter_crop_type: "fallow",
    }

    cropping_event = create_winter_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event is None

    attribute_type_to_value = {
        AttributeTypes.record_year: record_year,
        AttributeTypes.winter_crop_type: "no cover",
    }

    cropping_event = create_winter_cropping_event_190_e(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        row_id=row_id,
        event_creation_specification=event_creation_specification,
    )

    assert cropping_event is None
