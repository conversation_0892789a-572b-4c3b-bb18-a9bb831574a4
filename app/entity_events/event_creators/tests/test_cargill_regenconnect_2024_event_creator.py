from unittest.mock import patch

from defaults.attribute_options import CropUsage, ResidueHarvested, TerminationMethods
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.event_creators.cargill_regenconnect_2024_event_creator import (
    create_cropping_event_1119_enrollment,
)
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


@patch("entity_events.event_creators.cargill_regenconnect_2024_event_creator.base_create_cropping_event")
def test_create_cropping_event_1119_enrollment_commodity(mock_base_create_cropping_event):
    attribute_type_to_value = {
        AttributeTypes.crop_usage: CropUsage.COMMODITY,
        AttributeTypes.harvest_date: "2023-10-06",
    }
    entity_id = 1
    entity_type = EntityTypeChoices.field
    event_id = None
    event_creation_specification = EventCreationSpecification()
    error_handling = EventCreatorErrorHandling.RAISE_ERROR

    create_cropping_event_1119_enrollment(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )

    # Verify that base_create_cropping_event was called with the correct arguments
    mock_base_create_cropping_event.assert_called_once_with(
        attribute_type_to_value={
            AttributeTypes.crop_usage: CropUsage.COMMODITY,
            AttributeTypes.harvest_date: "2023-10-06",
            AttributeTypes.residue_harvested: ResidueHarvested.percent_00,
        },
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=EventCreationSpecification(error_handling=error_handling),
    )


@patch("entity_events.event_creators.cargill_regenconnect_2024_event_creator.base_create_cropping_event")
def test_create_cropping_event_1119_enrollment_cover(mock_base_create_cropping_event):
    attribute_type_to_value = {
        AttributeTypes.crop_usage: CropUsage.COVER,
        AttributeTypes.harvest_date: "2023-10-06",
    }
    entity_id = 1
    entity_type = EntityTypeChoices.field
    event_id = None
    event_creation_specification = EventCreationSpecification()
    error_handling = EventCreatorErrorHandling.RAISE_ERROR

    create_cropping_event_1119_enrollment(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )

    # Verify that base_create_cropping_event was called with the correct arguments
    mock_base_create_cropping_event.assert_called_once_with(
        attribute_type_to_value={
            AttributeTypes.crop_usage: CropUsage.COVER,
            AttributeTypes.harvest_date: "2023-10-06",
            AttributeTypes.winter_crop_termination: TerminationMethods.winterkill,
        },
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=EventCreationSpecification(error_handling=error_handling),
    )
