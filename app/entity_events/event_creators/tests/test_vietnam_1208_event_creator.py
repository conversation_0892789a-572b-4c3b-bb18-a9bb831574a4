from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.vietnam_1208_event_creator import (
    create_cropping_event_1208_monitoring,
)
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def test_create_cropping_event_1208_monitoring_irrigation_disabled():
    data = {AttributeTypes.irrigation_enabled: "0"}
    assert (
        create_cropping_event_1208_monitoring(
            attribute_type_to_value=data,
            entity_id=1,
            entity_type=EntityTypeChoices.field,
            event_id=None,
            event_creation_specification=EventCreationSpecification(),
        )
        is None
    )
