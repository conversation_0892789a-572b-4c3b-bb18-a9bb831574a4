from datetime import datetime, timezone

import pytest

from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateUnit,
    CropUsage,
    IrrigationMethods,
    NoCropType,
    ResidueHarvested,
    TerminationMethods,
    TillagePractice,
    YieldRateUnit,
)
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import (
    base_create_application_event,
    base_create_cropping_event,
    base_create_irrigation_event,
    base_create_tillage_event,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.reduction_event import StandardReductionTypes
from entity_events.events.tillage_event import TillageEvent
from entity_events.units import AreaUnit, LengthUnit, MassUnit, VolumeUnit
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices

start_date = datetime(2023, 3, 15, 7, 0, 0, tzinfo=timezone.utc)
end_date = datetime(2023, 3, 16, 7, 0, 0, tzinfo=timezone.utc)
entity_id = 123


async def test_base_create_commodity_cropping_event():
    data = {
        AttributeTypes.planting_date: start_date,
        AttributeTypes.harvest_date: end_date,
        AttributeTypes.crop_type: "corn",
        AttributeTypes.crop_usage: CropUsage.COMMODITY.value,
        AttributeTypes.crop_yield: "15",
        AttributeTypes.yield_rate_unit: YieldRateUnit.BU_AC.value,
        AttributeTypes.residue_harvested: ResidueHarvested.percent_100,
        AttributeTypes.termination_method: TerminationMethods.mechanical.value,
    }
    crop_event = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(crop_event, CroppingEvent)
    assert crop_event.termination_method is None

    assert crop_event.crop_type == "corn"
    assert crop_event.crop_usage == CropUsage.COMMODITY
    assert crop_event.crop_yield.value == 15.0
    assert crop_event.crop_yield.denominator_unit == AreaUnit.ACRE
    assert crop_event.crop_yield.numerator_unit == VolumeUnit.BUSHEL
    assert crop_event.residue_harvested == ResidueHarvested.percent_100
    assert crop_event.interval.start == start_date
    assert crop_event.interval.end == end_date


async def test_base_create_cover_cropping_event():
    data = {
        AttributeTypes.planting_date: start_date,
        AttributeTypes.harvest_date: end_date,
        AttributeTypes.crop_type: "clover",
        AttributeTypes.crop_usage: CropUsage.COVER.value,
        AttributeTypes.residue_harvested: ResidueHarvested.percent_100,
        AttributeTypes.termination_method: TerminationMethods.mechanical.value,
    }
    crop_event = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(crop_event, CroppingEvent)
    assert crop_event.crop_yield is None
    assert crop_event.residue_harvested is None

    assert crop_event.crop_type == "clover"
    assert crop_event.crop_usage == CropUsage.COVER
    assert crop_event.termination_method is TerminationMethods.mechanical
    assert crop_event.interval.start == start_date
    assert crop_event.interval.end == end_date

    # Assert that the winter_crop_termination is also mapped to termination_method
    new_data = {
        AttributeTypes.planting_date: start_date,
        AttributeTypes.harvest_date: end_date,
        AttributeTypes.crop_type: "clover",
        AttributeTypes.crop_usage: CropUsage.COVER.value,
        AttributeTypes.residue_harvested: ResidueHarvested.percent_100,
        AttributeTypes.winter_crop_termination: TerminationMethods.herbicide.value,
    }
    crop_event = base_create_cropping_event(
        attribute_type_to_value=new_data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(crop_event, CroppingEvent)
    assert crop_event.termination_method is TerminationMethods.herbicide


@pytest.mark.parametrize(
    "termination_method, expected_reduction",
    [
        (TerminationMethods.grain_harvest, StandardReductionTypes.GRAIN_HARVEST),
        (TerminationMethods.forage_harvest, StandardReductionTypes.SILAGE_HARVEST),
        (TerminationMethods.mechanical, StandardReductionTypes.MOW_100),
        (TerminationMethods.tillage, None),
        (TerminationMethods.winterkill, None),
        (TerminationMethods.herbicide, None),
    ],
)
async def test_base_create_crop_event_cover_crop_termination_method_reductions(
    termination_method: TerminationMethods, expected_reduction: StandardReductionTypes | None
):
    data = {
        AttributeTypes.planting_date: start_date,
        AttributeTypes.harvest_date: end_date,
        AttributeTypes.crop_type: "clover",
        AttributeTypes.crop_usage: CropUsage.COVER.value,
        AttributeTypes.termination_method: termination_method.value,
    }
    crop_event = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(crop_event, CroppingEvent)
    assert crop_event.crop_usage == CropUsage.COVER
    assert crop_event.termination_method == termination_method
    if expected_reduction:
        reduction = crop_event.reductions[0]
        assert float(expected_reduction["grain_removed_fraction"]) == reduction.grain_removed_fraction
        assert float(expected_reduction["grain_residue_fraction"]) == reduction.grain_residue_fraction
        assert float(expected_reduction["leaf_removed_fraction"]) == reduction.leaf_removed_fraction
        assert float(expected_reduction["leaf_residue_fraction"]) == reduction.leaf_residue_fraction
        assert float(expected_reduction["stem_removed_fraction"]) == reduction.stem_removed_fraction
        assert float(expected_reduction["stem_residue_fraction"]) == reduction.stem_residue_fraction
        assert float(expected_reduction["root_removed_fraction"]) == reduction.root_removed_fraction
        assert float(expected_reduction["root_residue_fraction"]) == reduction.root_residue_fraction
    else:
        assert not crop_event.reductions


async def test_base_create_fallow_period():
    data = {
        AttributeTypes.planting_date: start_date,
        AttributeTypes.harvest_date: end_date,
        AttributeTypes.crop_type: NoCropType.FALLOW,
    }
    fallow_period = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(fallow_period, FallowPeriod)
    assert fallow_period.interval.start == start_date
    assert fallow_period.interval.end == end_date


async def test_base_create_fallow_period_with_start_and_end():
    data = {
        AttributeTypes.start_date: start_date,
        AttributeTypes.end_date: end_date,
        AttributeTypes.crop_type: NoCropType.FALLOW,
    }
    fallow_period = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(fallow_period, FallowPeriod)
    assert fallow_period.interval.start == start_date
    assert fallow_period.interval.end == end_date


async def test_base_create_fallow_period_auto_assign_fallow_dates():
    data = {
        AttributeTypes.record_year: 2022,
        AttributeTypes.crop_type: NoCropType.FALLOW,
    }
    fallow_period = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(auto_assign_fallow_dates=True),
    )
    assert isinstance(fallow_period, FallowPeriod)
    assert fallow_period.interval.start == datetime(2022, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    assert fallow_period.interval.end == datetime(2022, 12, 31, 0, 0, 0, tzinfo=timezone.utc)

    # Don't auto-assign dates if fallow has Crop Usage is Cover. This is just to reject bad migration data.
    data = {
        AttributeTypes.record_year: 2022,
        AttributeTypes.crop_type: NoCropType.FALLOW,
        AttributeTypes.crop_usage: CropUsage.COVER,
    }
    fallow_period = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(auto_assign_fallow_dates=True),
    )
    assert fallow_period is None

    fallow_period = base_create_cropping_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(auto_assign_fallow_dates=False),
    )
    assert fallow_period is None


async def test_base_create_subsurface_drip_irrigation_event():
    data = {
        "start_date": start_date,
        "end_date": end_date,
        "subsurface_drip_depth": 14,
        "subsurface_drip_depth_unit": "cm",
        "flood_pct": 35,
        "irrigation_method": IrrigationMethods.subsurface_drip.value,
    }
    irrigation_event = base_create_irrigation_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(irrigation_event, IrrigationEvent)
    assert irrigation_event.flood_percentage is None

    assert irrigation_event.method == IrrigationMethods.subsurface_drip
    assert irrigation_event.subsurface_depth.value == 14
    assert irrigation_event.subsurface_depth.unit == LengthUnit.CENTIMETRE
    assert irrigation_event.interval.start == start_date
    assert irrigation_event.interval.end == end_date


async def test_base_create_furrow_irrigation_event():
    data = {
        "start_date": start_date,
        "end_date": end_date,
        "subsurface_drip_depth": 14,
        "subsurface_drip_depth_unit": "cm",
        "flood_pct": 35,
        "irrigation_method": IrrigationMethods.furrow.value,
    }
    irrigation_event = base_create_irrigation_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(irrigation_event, IrrigationEvent)
    assert irrigation_event.subsurface_depth is None

    assert irrigation_event.flood_percentage == 35
    assert irrigation_event.method == IrrigationMethods.furrow
    assert irrigation_event.interval.start == start_date
    assert irrigation_event.interval.end == end_date


async def test_base_create_tillage_event():
    data = {
        "tillage_practice": TillagePractice.no_till,
        "tillage_date": end_date,
        "tillage_depth": 10,
        "soil_inversion": 0,
        "strip_fraction": 0.5,
    }
    tillage_event = base_create_tillage_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(tillage_event, TillageEvent)
    assert tillage_event.occurred_at == end_date
    assert tillage_event.depth.value == 10
    assert tillage_event.depth.unit == LengthUnit.CENTIMETRE
    assert tillage_event.soil_inversion is False
    assert tillage_event.strip_fraction == 0.5
    assert tillage_event.tillage_practice == TillagePractice.no_till


async def test_base_create_broadcasted_application_event():
    data = {
        "application_product": "ammbic",
        "application_date": end_date,
        "application_rate": 2,
        "application_rate_unit": ApplicationRateUnit.KG_HA,
        "application_method": ApplicationMethod.BROADCASTED,
        "application_depth": 50,
        "water_amount": 5,
        "water_amount_unit": "cm",
        "additives": "agrotain",
    }
    application_event = base_create_application_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(application_event, ApplicationEvent)
    assert application_event.water_amount is None
    assert application_event.depth is None

    assert application_event.occurred_at == end_date
    assert application_event.additives == "agrotain"
    assert application_event.method == ApplicationMethod.BROADCASTED

    event_product = application_event.products[0]
    assert event_product.product_name == "ammbic"
    assert event_product.application_rate.value == 2
    assert event_product.application_rate.denominator_unit == AreaUnit.HECTARE
    assert event_product.application_rate.numerator_unit == MassUnit.KILOGRAM


async def test_base_create_application_event_with_water():
    data = {
        "application_product": "ammbic",
        "application_date": end_date,
        "application_rate": 55,
        "application_rate_unit": ApplicationRateUnit.LBS_AC,
        "application_method": ApplicationMethod.FERTIGATION,
        "application_depth": 50,
        "water_amount": 5,
        "water_amount_unit": "cm",
        "additives": "agrotain",
    }
    application_event = base_create_application_event(
        attribute_type_to_value=data,
        entity_id=entity_id,
        entity_type=EntityTypeChoices.field,
        event_id=None,
        event_creation_specification=EventCreationSpecification(),
    )
    assert isinstance(application_event, ApplicationEvent)
    assert application_event.occurred_at == end_date
    assert application_event.depth.value == 50
    assert application_event.depth.unit == LengthUnit.CENTIMETRE
    assert application_event.water_amount.value == 5
    assert application_event.water_amount.unit == LengthUnit.CENTIMETRE
    assert application_event.additives == "agrotain"
    assert application_event.method == ApplicationMethod.FERTIGATION

    event_product = application_event.products[0]
    assert event_product.product_name == "ammbic"
    assert event_product.application_rate.value == 55
    assert event_product.application_rate.denominator_unit == AreaUnit.ACRE
    assert event_product.application_rate.numerator_unit == MassUnit.POUND
