from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.event_creators.cargill_sustainconnect_275_event_creator import (
    create_cropping_event_275_enrollment,
)
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def test_create_cropping_event_275_irrigation_toggle_off(mocker):
    entity_id = 1
    entity_type = EntityTypeChoices.field
    event_id = None
    error_handling = EventCreatorErrorHandling.RAISE_ERROR
    event = create_cropping_event_275_enrollment(
        attribute_type_to_value={
            AttributeTypes.irrigation_enabled: "0",
        },
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=EventCreationSpecification(error_handling=error_handling),
    )
    assert event is None


def test_create_cropping_event_275_irrigation_toggle_on(mocker):
    mock_base_create_cropping_event = mocker.patch(
        "entity_events.event_creators.cargill_sustainconnect_275_event_creator.base_create_cropping_event"
    )
    entity_id = 1
    entity_type = EntityTypeChoices.field
    event_id = None
    error_handling = EventCreatorErrorHandling.RAISE_ERROR
    create_cropping_event_275_enrollment(
        attribute_type_to_value={
            AttributeTypes.irrigation_enabled: "1",
        },
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=EventCreationSpecification(error_handling=error_handling),
    )
    mock_base_create_cropping_event.assert_called_with(
        attribute_type_to_value={
            AttributeTypes.irrigation_enabled: "1",
        },
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=EventCreationSpecification(error_handling=error_handling),
    )
