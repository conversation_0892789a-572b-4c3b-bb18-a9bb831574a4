from datetime import datetime
from typing import Optional
from uuid import UUID

from entity_events.event_creators import base_event_creator
from entity_events.events.cropping_event import CroppingEvent
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices

"""
Program 582: Vietnam TRVC Rice Season 1 and
Program 1208: Vietnam TRVC Rice Season 2
"""
from entity_events.data_classes import EventCreationSpecification


def create_cropping_event_582(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    """
    Convert crop types of "other" into "aquaculture," and "rice" into "rice_paddy_long" or "_short" depending on
    planting month. Then forward event creation on to default event creator function.
    """
    if attribute_type_to_value.get(AttributeTypes.crop_type) == "other":
        attribute_type_to_value.update([(AttributeTypes.crop_type, "aquaculture")])
    elif (
        attribute_type_to_value.get(AttributeTypes.planting_date) is not None
        and attribute_type_to_value.get(AttributeTypes.crop_type) == "rice"
    ):
        planting_date = datetime.strptime(
            attribute_type_to_value.get(AttributeTypes.planting_date).split("T")[0],  # type: ignore[arg-type, union-attr]
            "%Y-%m-%d",
        )
        if 2 <= planting_date.month <= 7:
            attribute_type_to_value.update([(AttributeTypes.crop_type, "long_rice")])
        else:
            attribute_type_to_value.update([(AttributeTypes.crop_type, "short_rice")])

    return base_event_creator.base_create_cropping_event(
        attribute_type_to_value,
        entity_id,
        entity_type,
        event_id,
        event_creation_specification=event_creation_specification,
    )
