from uuid import UUID

from pydantic import ValidationError

from defaults.attribute_options import CropUsage, ResidueHarvested
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import (
    base_create_application_event,
    base_create_cropping_event,
)
from entity_events.event_creators.cargill_regenconnect_2024_event_creator import (
    default_termination_method_by_harvest_date,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from helper.datetime_helper import parse_datetime_as_utc
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def create_eu_2025_cropping_event_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> CroppingEvent | None:
    crop_usage = attribute_type_to_value.get(AttributeTypes.crop_usage)
    # Map premium and basic cover crop to standard cover crop
    crop_usage = (
        CropUsage.COVER
        if crop_usage
        in {
            CropUsage.PREMIUM_COVER_CROP,
            CropUsage.BASIC_COVER_CROP,
        }
        else crop_usage
    )
    attribute_type_to_value[AttributeTypes.crop_usage] = crop_usage  # type: ignore[assignment]
    # Default residue_harvested and termination_method based on crop usage
    if crop_usage == CropUsage.COMMODITY:
        attribute_type_to_value[AttributeTypes.residue_harvested] = ResidueHarvested.percent_00
    elif crop_usage == CropUsage.COVER:
        if attribute_type_to_value.get(AttributeTypes.harvest_date):
            attribute_type_to_value[AttributeTypes.winter_crop_termination] = (
                default_termination_method_by_harvest_date(
                    parse_datetime_as_utc(attribute_type_to_value.get(AttributeTypes.harvest_date))
                )
            )
    return base_create_cropping_event(
        attribute_type_to_value, entity_id, entity_type, event_id, event_creation_specification
    )


def create_eu_2025_application_event_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> ApplicationEvent | None:
    # This program made the Nutrient Management stage optional so a lot of partial data was entered. This means we'll
    # have to find some way to ignore that stage on a field or project basis. In the meantime, this will allow testing
    # to proceed by ignoring validation errors.
    try:
        return base_create_application_event(
            attribute_type_to_value, entity_id, entity_type, event_id, event_creation_specification
        )
    except (ValidationError, ValueError):
        return None
