from typing import Optional
from uuid import UUID

from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import base_create_fire_event
from entity_events.event_creators.vietnam_582_event_creator import (
    create_cropping_event_582,
)
from entity_events.events.cropping_event import CroppingEvent
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def create_cropping_event_1208_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    """
    Convert crop types of "other" into "aquaculture," and "rice" into "rice_paddy_long" or "_short" depending on
    planting month. Then forward event creation on to default event creator function.
    """
    return create_cropping_event_582(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def create_cropping_event_1208_monitoring(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    """
    Convert crop types of "other" into "aquaculture," and "rice" into "rice_paddy_long" or "_short" depending on
    planting month. Then forward event creation on to default event creator function.
    """
    if attribute_type_to_value.get(AttributeTypes.irrigation_enabled) == "0":
        return None
    return create_cropping_event_582(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def create_fire_event_1208_monitoring(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    if attribute_type_to_value.get(AttributeTypes.irrigation_enabled) == "0":
        return None
    return base_create_fire_event(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )
