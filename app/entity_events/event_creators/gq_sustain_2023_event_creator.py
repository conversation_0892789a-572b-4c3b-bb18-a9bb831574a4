from typing import Optional

from defaults.attribute_options import CropUsage
from entity_events.data_classes import EventCreationSpecification
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.tillage_event import TillageEvent
from logger import get_logger
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices

"""
Program 190: GQ Sustain 2023

E Phase CroppingEvent + TillageEvent
- 4 Events per row, without any dates

"""

logger = get_logger(__name__)

AUTO_ASSIGN_SPRING_TILLAGE_MONTH = 3
AUTO_ASSIGN_SPRING_TILLAGE_DAY = 1

AUTO_ASSIGN_SUMMER_CROPPING_START_MONTH = 5
AUTO_ASSIGN_SUMMER_CROPPING_START_DAY = 1
AUTO_ASSIGN_SUMMER_CROPPING_END_MONTH = 5
AUTO_ASSIGN_SUMMER_CROPPING_END_DAY = 31

AUTO_ASSIGN_FALL_TILLAGE_MONTH = 10
AUTO_ASSIGN_FALL_TILLAGE_DAY = 1

AUTO_ASSIGN_WINTER_CROPPING_START_MONTH = 11
AUTO_ASSIGN_WINTER_CROPPING_START_DAY = 1
AUTO_ASSIGN_WINTER_CROPPING_END_MONTH = 11
AUTO_ASSIGN_WINTER_CROPPING_END_DAY = 30


def create_spring_tillage_event_190_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[TillageEvent]:
    # Filter MRV Values that are hidden by Tillage structured stages dependencies
    filtered_attribute_type_to_value = _filter_values_using_e_dependencies(attribute_type_to_value)

    record_year = filtered_attribute_type_to_value.get(AttributeTypes.record_year)

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "occurred_at": f"{record_year}-{AUTO_ASSIGN_SPRING_TILLAGE_MONTH}-{AUTO_ASSIGN_SPRING_TILLAGE_DAY}T11:00:00Z",
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": filtered_attribute_type_to_value.get(AttributeTypes.spring_tillage_practice),
    }

    return TillageEvent.parse_obj(data)


def create_summer_cropping_event_190_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    filtered_attribute_type_to_value = _filter_values_using_e_dependencies(attribute_type_to_value)

    # Fallow "cropping events" don't get any MRV CroppingEvent created for them
    if filtered_attribute_type_to_value.get(AttributeTypes.summer_crop_type) in ("fallow", "no cover"):
        return None

    # ~~ Prepare our data needed to create a CroppingEvent ~~
    crop_usage = CropUsage.COMMODITY
    record_year = filtered_attribute_type_to_value.get(AttributeTypes.record_year)
    start = f"{record_year}-{AUTO_ASSIGN_SUMMER_CROPPING_START_MONTH}-{AUTO_ASSIGN_SUMMER_CROPPING_START_DAY}T11:00:00Z"
    end = f"{record_year}-{AUTO_ASSIGN_SUMMER_CROPPING_END_MONTH}-{AUTO_ASSIGN_SUMMER_CROPPING_END_DAY}T11:00:00Z"

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": start,
            "end": end,
        },
        "crop_type": filtered_attribute_type_to_value.get(AttributeTypes.summer_crop_type),
        "crop_usage": crop_usage,
        "crop_yield": None,
        "reductions": [],
    }

    return CroppingEvent.parse_obj(data)


def create_fall_tillage_event_190_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[TillageEvent]:
    # Filter MRV Values that are hidden by Tillage structured stages dependencies
    filtered_attribute_type_to_value = _filter_values_using_e_dependencies(attribute_type_to_value)

    record_year = filtered_attribute_type_to_value.get(AttributeTypes.record_year)

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "occurred_at": f"{record_year}-{AUTO_ASSIGN_FALL_TILLAGE_MONTH}-{AUTO_ASSIGN_FALL_TILLAGE_DAY}T11:00:00Z",
        "depth": None,
        "soil_inversion": False,
        "strip_fraction": None,
        "tillage_practice": filtered_attribute_type_to_value.get(AttributeTypes.fall_tillage_practice),
    }

    return TillageEvent.parse_obj(data)


def create_winter_cropping_event_190_e(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    row_id: int,
    event_creation_specification: EventCreationSpecification,
) -> Optional[CroppingEvent]:
    filtered_attribute_type_to_value = _filter_values_using_e_dependencies(attribute_type_to_value)

    # Fallow "cropping events" don't get any MRV CroppingEvent created for them
    if filtered_attribute_type_to_value.get(AttributeTypes.winter_crop_type) in ("fallow", "no cover"):
        return None

    # ~~ Prepare our data needed to create a CroppingEvent ~~
    crop_usage = CropUsage.COVER
    record_year = filtered_attribute_type_to_value.get(AttributeTypes.record_year)
    start = f"{record_year}-{AUTO_ASSIGN_WINTER_CROPPING_START_MONTH}-{AUTO_ASSIGN_WINTER_CROPPING_START_DAY}T11:00:00Z"
    end = f"{record_year}-{AUTO_ASSIGN_WINTER_CROPPING_END_MONTH}-{AUTO_ASSIGN_WINTER_CROPPING_END_DAY}T11:00:00Z"

    data = {
        "entity_id": entity_id,
        "entity_type": entity_type,
        "interval": {
            "start": start,
            "end": end,
        },
        "crop_type": filtered_attribute_type_to_value.get(AttributeTypes.winter_crop_type),
        "crop_usage": crop_usage,
        "crop_yield": None,
        "reductions": [],
    }

    return CroppingEvent.parse_obj(data)


def _filter_values_using_e_dependencies(
    attribute_type_to_value: dict[AttributeTypes, str],
) -> dict[AttributeTypes, str]:
    """
    Given an attribute_type_to_value dict representing values for a single UI row (any stage),
    filter the attribute_type_to_value dict based on E-phase Cropping dependencies

    The current (04/17/2025) dependencies for the e phase cropping stage in 190 are:
    * fall_tillage_practice is available when record_year is not 2023 x
    * winter_crop_type is available when record_year is not 2023 x
    """
    copy = attribute_type_to_value.copy()

    if copy.get(AttributeTypes.record_year) == "2023":
        dependency_blocked_attributes = [
            AttributeTypes.fall_tillage_practice,
            AttributeTypes.winter_crop_type,
        ]
        for attribute_type in dependency_blocked_attributes:
            copy.pop(attribute_type, None)
    return copy
