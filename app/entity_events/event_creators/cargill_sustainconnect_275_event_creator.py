from uuid import UUID

from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import (
    base_create_cropping_event,
)
from entity_events.events.cropping_event import CroppingEvent
from phases.enums import AttributeTypes
from values.enums import EntityTypeChoices


def create_cropping_event_275_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> CroppingEvent | None:
    # This program is unabashedly using irrigation_enabled as a toggle on E Phase crop rows
    crop_sown = attribute_type_to_value.get(AttributeTypes.irrigation_enabled, "0")
    if crop_sown == "1":
        return base_create_cropping_event(
            attribute_type_to_value=attribute_type_to_value,
            entity_id=entity_id,
            entity_type=entity_type,
            event_id=event_id,
            event_creation_specification=event_creation_specification,
        )
    assert crop_sown == "0"
    return None
