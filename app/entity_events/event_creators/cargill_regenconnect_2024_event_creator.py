from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from dateutil import parser

from defaults.attribute_options import Crop<PERSON>sage, ResidueHarvested, TerminationMethods
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import (
    base_create_cropping_event,
    base_create_tillage_event,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.tillage_event import TillageEvent
from helper.datetime_helper import parse_datetime_as_utc
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from values.enums import EntityTypeChoices

"""
Program 1119 has M1 and M2 phases. M1 collected partial tillage and cropping events.

M eligibility does not handle partial events, so event creators with default dates were
added. The crop event creator was removed on 4/17/25 to allow for migration into the Y5
program without migrating defaulted dates.

The custom tillage event creator ignores tillage events without tillage dates. These events
can't be migrated into SES if they lack dates.
"""


def default_termination_method_by_harvest_date(harvest_date: datetime) -> str:
    """
    This function is used to set the default termination method for cover cropping events
    in the 1119 program. Ref https://regrow.atlassian.net/browse/MRV-5235
    """
    if harvest_date.month in (9, 10, 11, 12, 1, 2):
        return TerminationMethods.winterkill
    else:
        return TerminationMethods.herbicide


def create_cropping_event_1119_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> CroppingEvent | FallowPeriod | None:
    crop_usage = attribute_type_to_value.get(AttributeTypes.crop_usage)

    # Ref https://regrow.atlassian.net/browse/MRV-5235
    if crop_usage == CropUsage.COMMODITY:
        attribute_type_to_value[AttributeTypes.residue_harvested] = ResidueHarvested.percent_00

    if crop_usage == CropUsage.COVER:
        if attribute_type_to_value.get(AttributeTypes.harvest_date):
            attribute_type_to_value[AttributeTypes.winter_crop_termination] = (
                default_termination_method_by_harvest_date(
                    parse_datetime_as_utc(attribute_type_to_value.get(AttributeTypes.harvest_date))
                )
            )

    return base_create_cropping_event(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def create_tillage_event_1119_monitoring(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> TillageEvent | None:
    # M2 phase closes on 9/30/2025
    if datetime.now(timezone.utc) < datetime(2025, 9, 30, tzinfo=timezone.utc) and not attribute_type_to_value.get(
        AttributeTypes.tillage_date
    ):
        return None

    return base_create_tillage_event(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def pre_filter_1119_values(
    values_data: dict[PhaseTypes, dict[int, dict[StageTypes, dict[int, dict[AttributeTypes, str]]]]],
) -> dict[PhaseTypes, dict[int, dict[StageTypes, dict[int, dict[AttributeTypes, str]]]]]:
    """
    In 1119, the enrollment phase collected cropping events for 2024 with planting and estimated harvest dates, then
    recollected 2024 cropping events in M1 with actual harvest dates and yields. This function pulls the M Phase 2024
    cropping data into the E Phase, overwriting the E Phase's version of this data. This is for a couple reasons:
    * we only want one copy of the 2024 commodity cropping event to go to SES, and it should be the more correct one
    * we want the 2024 com crop event to be associated with the E Phase for locking purposes, so it's not editable in Y5
    """
    # Remove the E-phase 2024 Cropping events if the E-phase is in the data
    if PhaseTypes.ENROLMENT in values_data and PhaseTypes.MONITORING in values_data:
        e_data = values_data[PhaseTypes.ENROLMENT]
        m_data = values_data[PhaseTypes.MONITORING]

        for entity_id, m_data_by_stage in m_data.items():
            m_crop_data = m_data_by_stage[StageTypes.HISTORICAL_CROP_ROTATION]
            rows_to_move_to_e = []
            for row_id, attv in m_crop_data.items():
                if _is_2024_commodity_crop(attv):
                    rows_to_move_to_e.append((row_id, attv))
            _add_rows_to_e_phase(e_data, entity_id, [row for _, row in rows_to_move_to_e])
            for row_id_to_del, _ in rows_to_move_to_e:
                del m_crop_data[row_id_to_del]
    return values_data


def _is_2024_commodity_crop(attv: dict[AttributeTypes, str], crop_name: Optional[str] = None) -> bool:
    return bool(
        attv.get(AttributeTypes.harvest_date)
        and attv.get(AttributeTypes.crop_type) is not None
        and parser.parse(attv[AttributeTypes.harvest_date]).year == 2024
        and attv.get(AttributeTypes.crop_usage) == CropUsage.COMMODITY
        and (crop_name is None or attv.get(AttributeTypes.crop_type) == crop_name)
    )


def _add_rows_to_e_phase(e_data: dict, entity_id: int, rows_to_move_to_e: list[dict[AttributeTypes, str]]) -> None:
    e_crop_data = e_data[entity_id][StageTypes.HISTORICAL_CROP_ROTATION]
    row_ids_to_update = []
    unmatched_rows = []

    for row_attv in rows_to_move_to_e:
        match_found = False
        for row_id, e_attv in e_crop_data.items():
            if _is_2024_commodity_crop(e_attv, row_attv[AttributeTypes.crop_type]):
                row_ids_to_update.append((row_id, row_attv))
                match_found = True
                break
        if not match_found:
            unmatched_rows.append(row_attv)

    if unmatched_rows:
        row_ids_to_delete = [row_id for row_id, e_attv in e_crop_data.items() if _is_2024_commodity_crop(e_attv)]
        for row_id in row_ids_to_delete:
            del e_crop_data[row_id]
        for i, row_attv in enumerate(unmatched_rows, start=60):
            e_crop_data[i] = row_attv
    else:
        for row_id, row_attv in row_ids_to_update:
            e_crop_data[row_id] = row_attv
