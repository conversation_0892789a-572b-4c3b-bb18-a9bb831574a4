from datetime import datetime, timedelta

import pytest

from entity_events.measures import Interval


def test_interval_validators():
    start = datetime.now() - timedelta(days=10)
    end = datetime.now()
    # Should not raise any exceptions
    interval = Interval(start=start, end=end)
    assert interval.start == start
    assert interval.end == end
    # You can set just the start
    interval = Interval(start=start)
    assert interval.start == start
    assert interval.end is None
    # You can set just the end
    interval = Interval(end=end)
    assert interval.start is None
    assert interval.end == end
    # At least start or end must be set - the following checks this
    with pytest.raises(ValueError):
        Interval()
