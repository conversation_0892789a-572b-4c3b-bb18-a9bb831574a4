from datetime import datetime
from typing import Optional

from entity_events.event_creators.event_creator_lookup import EVENT_CREATOR_LOOKUP
from fields.enums import FieldStatus
from fields.model import Fields
from helper.pytest_models_factory import ModelsFactory
from programs.enums import BaselineMethod, ProgramTemplate, Protocols
from programs.model import Programs
from projects.model import Projects


async def create_structured_program(mdl_factory_ignore_ids, mdl) -> tuple[Programs, Projects]:
    # Create a Program with a single-field Project, and two Phases
    program = await mdl_factory_ignore_ids(
        Programs,
        ignore_ids=EVENT_CREATOR_LOOKUP.keys(),
        protocol=Protocols.GENERAL_SCOPE_3,
        baseline_method=BaselineMethod.BLENDED,
        crediting_year=2022,
        program_template=ProgramTemplate.legacy,
        reporting_period_start_date=datetime(2023, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2023, 12, 31, 23, 59, 59),
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="regrow_mrv_test",
    )
    project = await mdl.Projects(program_id=program.id)
    return program, project


async def create_structured_program_and_field(
    mdl: ModelsFactory, mdl_factory_ignore_ids, mrv_values_lookup: Optional[dict[str, dict[str, dict[int, str]]]] = None
) -> tuple[int, int, int]:
    """Create a program with Structured Stages, prepopulated with MRV Values.

    Returns:
        (program_id, project_id, field_id)
    """

    program, project = await create_structured_program(mdl_factory_ignore_ids, mdl)
    field = await create_field_with_mrv_values(mdl, mrv_values_lookup, program, project)
    return program.id, project.id, field.id


async def create_field_with_mrv_values(
    mdl, phase_stage_attribute_values_lookup: Optional[dict], program, project
) -> Fields:
    field = await mdl.Fields(parent_project_id=project.id, status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2022, is_returning=False)

    if phase_stage_attribute_values_lookup:
        for phase_type, stage_attribute_values_lookup in phase_stage_attribute_values_lookup.items():
            # Create each Phase's Stages/Attributes/Values by looping through stage_attribute_values_lookup
            phase = await mdl.Phases(program_id=program.id, type_=phase_type, enabled=True)
            for stage_type, attribute_types_dict in stage_attribute_values_lookup.items():
                stage = await mdl.Stage(phase_id=phase.id, type_=stage_type, enabled=True)
                # Create each Stage's Attributes
                for attribute_type, row_dict in attribute_types_dict.items():
                    attribute = await mdl.Attribute(parent_stage_id=stage.id, type=attribute_type, enabled=True)
                    # Create the Values for each Attribute for our Field
                    for row, value in row_dict.items():
                        await mdl.Values(field_id=field.id, attribute_id=attribute.id, row_id=row, value=value)
    return field
