import pytest

from defaults.attribute_options import NO_ADDITIVES_OPTION, TerminationMethods
from phases.enums import AttributeTypes, PhaseTypes, StageTypes


# Mapping of { StageType: { AttributeType: { row_id: value } } }
# Translated from Project 12416 in Prod's E Phase, Field 93221
@pytest.fixture
def comprehensive_mrv_values_lookup() -> dict:
    return {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_TILLAGE: {
                AttributeTypes.tillage_event: {
                    0: "0",
                    1: "1",
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "1",
                    6: "1",
                },
                AttributeTypes.tillage_date: {
                    # 0: XXX, # There would be no value as a result of the tillage_event dependency
                    1: "2021-01-06T08:00:00.000Z",
                    2: "2020-01-06T08:00:00.000Z",
                    3: "2019-01-01T08:00:00.000Z",
                    4: "2018-01-03T08:00:00.000Z",
                    5: "2021-08-15T07:00:00.000Z",
                    6: "2019-08-14T07:00:00.000Z",
                },
                AttributeTypes.tillage_depth: {
                    # 0: XXX, # There would be no value as a result of the tillage_event dependency
                    1: "6",
                    2: "18",
                    3: "25",
                    4: "38.1",
                    5: "3",
                    6: "6",
                },
                AttributeTypes.soil_inversion: {
                    # 0: XXX,  # There would be no value as a result of the tillage_event dependency
                    1: "0",
                    2: "0",
                    3: "1",  # Changed this in Prod to 1
                    4: "1",  # Changed this in Prod to 1
                    5: "0",
                    6: "0",
                },
                AttributeTypes.strip_fraction: {
                    # This attribute isn't usable in the UI, so I'm manually adding this here
                    # 0: XXX,  # There would be no value as a result of the tillage_event dependency
                    1: ".25",
                    2: ".5",
                    3: ".75",
                    4: ".5",
                    5: ".25",
                    6: ".5",
                },
            },
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2022-02-01T08:00:00.000Z",
                    1: "2021-02-03T08:00:00.000Z",
                    2: "2020-02-02T08:00:00.000Z",
                    3: "2019-02-01T08:00:00.000Z",
                    4: "2018-02-01T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2022-07-15T07:00:00.000Z",
                    1: "2021-07-14T07:00:00.000Z",
                    2: "2020-07-06T07:00:00.000Z",
                    3: "2019-07-15T07:00:00.000Z",
                    4: "2018-07-15T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "sorghum",
                    1: "corn",
                    2: "soybeans",
                    3: "wheat_winter",
                    4: "fallow",
                },
                AttributeTypes.crop_yield: {
                    0: "125",
                    # 1: "150",  # There would be no value here because Termination method is Herbicide
                    2: "185.500",
                    3: "20",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.yield_rate_unit: {
                    0: "bu/ac",
                    # 1: "T/ac",  #  # There would be no value here because Termination method is Herbicide
                    2: "bu/ac",
                    3: "bu/ac",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.winter_crop_termination: {
                    0: None,  # Because crop_usage is Commodity
                    1: TerminationMethods.herbicide,
                    2: TerminationMethods.grain_harvest,
                    3: None,  # Because crop_usage is Commodity
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.crop_usage: {
                    0: "Commodity",
                    1: "Cover",
                    2: "Cover",
                    3: "Commodity",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.residue_harvested: {
                    0: "All residue harvested",
                    1: None,  # Because crop_usage is Cover
                    2: None,  # Because crop_usage is Cover
                    3: "75% Harvested",
                    # 4: None, # There would be no value here because crop is fallow
                },
                AttributeTypes.planting_method: {
                    0: "Transplant (wet)",
                    1: "Direct seeding (wet)",
                    2: "Direct seeding (dry)",
                    # 4: None, # There would be no value here because crop is fallow
                },
            },
            StageTypes.NUTRIENT_MGMT: {
                AttributeTypes.nutrient_management_enabled: {
                    0: "1",
                    1: "1",
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "0",
                    # Added this to Prod as an additional row for 2018. As a result, NULL values inserted for other values in row.
                },
                AttributeTypes.application_date: {
                    0: "2022-06-26T07:00:00.000Z",  # Changed this in prod from /2023
                    1: "2021-06-30T07:00:00.000Z",  # Changed this is prod from /2022
                    2: "2020-06-15T07:00:00.000Z",
                    3: "2019-06-18T07:00:00.000Z",
                    4: "2018-06-13T07:00:00.000Z",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_product: {
                    0: "aqamm",
                    1: "tap",
                    2: "ammbic",
                    3: "ammnit",
                    4: "uan",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_rate: {
                    0: "4",
                    1: "15",
                    2: "15",
                    3: "23",
                    4: "30",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_rate_unit: {
                    0: "qt1ac-1",  # Note that this is a frontend-hardcoded-only option
                    1: "lb1ac-1",
                    2: "lb1ac-1",
                    3: "lb1ac-1",  # Changed this in prod from nlb1ac-1, since we aren't going to support it
                    4: "gal1ac-1",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_method: {
                    0: "Injected",
                    1: "Fertigation",
                    2: "Broadcasted",  # Changed this in prod from Fertigation
                    3: "Fertigation",
                    4: "Fertigation",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.application_depth: {
                    0: "11",
                    1: "11",
                    # 2: XXX, # There would be no Row 2 value because of the broadcast dependency
                    3: "18",
                    4: "6",
                    5: None,
                },
                AttributeTypes.water_amount: {
                    # 0: 13, # There would be no Row 0 value because of the injection dependency
                    1: 15,
                    # 2: 15,  # There would be no Row 2 value because of the broadcast dependency
                    3: 15,
                    4: 15,
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.water_amount_unit: {
                    # 0: "gal",  # There would be no Row 0 value because of the injection dependency
                    1: "in",
                    # 2: "mm",  # There would be no Row 2 value because of the broadcast dependency
                    3: "mm",
                    4: "gal",
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
                AttributeTypes.additives: {
                    0: "anvol",
                    1: "N-SERVE",
                    2: "N-SERVE",
                    3: "anvol,N-SERVE",
                    4: NO_ADDITIVES_OPTION,
                    5: None,  # A result of inserting via (+) a new row for 2018 with NM_Enabled=0
                },
            },
            StageTypes.IRRIGATION: {
                AttributeTypes.irrigation_enabled: {
                    0: "1",
                    1: "0",  # Changed this in prod from 1 -> 0
                    2: "1",
                    3: "1",
                    4: "1",
                    5: "1",
                    6: "1",
                },
                AttributeTypes.start_date: {
                    0: "2022-06-01T07:00:00.000Z",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "2020-05-01T07:00:00.000Z",
                    3: "2019-04-13T07:00:00.000Z",
                    4: "2018-05-03T07:00:00.000Z",
                    5: "2020-05-16T07:00:00.000Z",
                    6: "2020-05-27T07:00:00.000Z",
                },
                AttributeTypes.end_date: {
                    0: "2022-06-08T07:00:00.000Z",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "2020-05-08T07:00:00.000Z",
                    3: "2019-04-17T07:00:00.000Z",
                    4: "2018-06-10T07:00:00.000Z",
                    5: "2020-05-20T07:00:00.000Z",
                    6: "2020-05-31T07:00:00.000Z",
                },
                AttributeTypes.irrigation_method: {
                    0: "Furrow",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    2: "Flood",
                    3: "Subsurface drip",
                    4: "Flood",
                    5: "Flood",
                    6: "Sprinkler",  # Changed in prod from Flood -> Sprinkler
                },
                AttributeTypes.subsurface_drip_depth: {
                    # 0: XXX,  # There won't be a value for Row 0 because of the dependency on irrigation_method
                    # 1: XXX, # There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    3: "3",
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
                AttributeTypes.subsurface_drip_depth_unit: {
                    # 0: XXX,  # There won't be a value for Row 0 because of the dependency on irrigation_method
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    3: "cm",
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
                AttributeTypes.flood_pct: {
                    0: "45",
                    # 1: XXX, #  There wouldn't be a value for Row 1 because of the dependency on irrigation_enabled
                    # 2: XXX,  # There won't be a value for Row 2 because of the dependency on irrigation_method
                    # 3: XXX,  # There won't be a value for Row 3 because of the dependency on irrigation_method
                    # 4: XXX,  # There won't be a value for Row 4 because of the dependency on irrigation_method
                    5: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                    6: None,  # A result of inserting via (+) a new row for 2020 with NM_Enabled=0
                },
            },
            StageTypes.INTENDED_COMMODITY_CROPS: {
                AttributeTypes.planting_date: {
                    0: "2023-02-01T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2023-07-15T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "sorghum",
                },
            },
        },
        PhaseTypes.MONITORING: {
            StageTypes.HISTORICAL_TILLAGE: {
                AttributeTypes.tillage_event: {
                    0: "1",
                },
                AttributeTypes.tillage_date: {
                    0: "2023-01-06T08:00:00.000Z",
                },
                AttributeTypes.tillage_depth: {
                    0: "6",
                },
                AttributeTypes.soil_inversion: {
                    0: "0",
                },
                AttributeTypes.strip_fraction: {
                    0: ".25",
                },
            },
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2023-02-03T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2023-07-14T07:00:00.000Z",
                },
                AttributeTypes.crop_type: {
                    0: "corn",
                },
                AttributeTypes.crop_yield: {
                    0: "150",
                },
                AttributeTypes.yield_rate_unit: {
                    0: "bu/ac",
                },
                AttributeTypes.winter_crop_termination: {
                    # 0: "",  # There would be no value here because Crop Usage is Commodity
                },
                AttributeTypes.crop_usage: {
                    0: "Commodity",
                },
                AttributeTypes.residue_harvested: {
                    0: "All residue harvested",
                },
            },
            StageTypes.NUTRIENT_MGMT: {
                AttributeTypes.nutrient_management_enabled: {
                    0: "1",
                },
                AttributeTypes.application_date: {
                    0: "2023-06-26T07:00:00.000Z",
                },
                AttributeTypes.application_product: {
                    0: "aqamm",
                },
                AttributeTypes.application_rate: {
                    0: "4",
                },
                AttributeTypes.application_rate_unit: {
                    0: "qt1ac-1",
                },
                AttributeTypes.application_method: {
                    0: "Injected",
                },
                AttributeTypes.application_depth: {
                    0: "11",
                },
                AttributeTypes.water_amount: {
                    # 0: 13, # There would be no Row 0 value because of the injection dependency
                },
                AttributeTypes.water_amount_unit: {
                    # 0: "gal",  # There would be no Row 0 value because of the injection dependency
                },
                AttributeTypes.additives: {
                    0: "anvol",
                },
            },
            StageTypes.IRRIGATION: {
                AttributeTypes.irrigation_enabled: {
                    0: "1",
                },
                AttributeTypes.start_date: {
                    0: "2023-06-01T07:00:00.000Z",
                },
                AttributeTypes.end_date: {
                    0: "2023-06-08T07:00:00.000Z",
                },
                AttributeTypes.irrigation_method: {
                    0: "Subsurface drip",
                },
                AttributeTypes.subsurface_drip_depth: {
                    0: 10,
                },
                AttributeTypes.subsurface_drip_depth_unit: {
                    0: "cm",
                },
                AttributeTypes.flood_pct: {
                    0: "45",
                },
            },
        },
    }


@pytest.fixture
def fallows_mrv_values_lookup() -> dict:
    return {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_CROP_ROTATION: {
                AttributeTypes.planting_date: {
                    0: "2022-02-01T08:00:00.000Z",
                    1: None,
                    2: "2020-02-02T08:00:00.000Z",
                },
                AttributeTypes.harvest_date: {
                    0: "2022-07-15T07:00:00.000Z",
                    1: "2021-07-14T07:00:00.000Z",
                    2: None,
                },
                AttributeTypes.crop_type: {
                    0: "fallow",
                    1: "no cover",  # equivalent to fallow
                    2: "fallow",
                },
            }
        }
    }
