from sqlalchemy import select

from entity_events.db import (
    delete_raw_field_events,
    get_raw_field_events,
    get_raw_field_events_by_field_ids,
)
from entity_events.events.enums import EntityEventType
from entity_events.model import RawFieldEvents
from helper.helper import run_query
from phases.enums import PhaseTypes, StageTypes


async def set_up_raw_field_events(mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    raw_field_event_1 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_1.id,
        row_id=1,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )
    raw_field_event_2 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_1.id,
        row_id=2,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )
    raw_field_event_3 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_2.id,
        row_id=1,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )
    raw_field_event_4 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_2.id,
        row_id=2,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )
    return {field_1.id: [raw_field_event_1, raw_field_event_2], field_2.id: [raw_field_event_3, raw_field_event_4]}


async def test_get_raw_field_events(mdl, app_request):
    field_to_raw_field_events = await set_up_raw_field_events(mdl)
    for field_id, expected_events in field_to_raw_field_events.items():
        events = await get_raw_field_events(request=app_request, field_id=field_id)
        assert len(events) == len(expected_events)

        event_dicts = [event.to_dict() for event in events]
        for event in expected_events:
            assert event.to_dict() in event_dicts


async def test_get_raw_field_events_by_field_ids(mdl, app_request):
    field_to_raw_field_events = await set_up_raw_field_events(mdl)
    expected_events = [event for events in field_to_raw_field_events.values() for event in events]

    events = await get_raw_field_events_by_field_ids(
        request=app_request, field_ids=list(field_to_raw_field_events.keys())
    )
    assert len(events) == len(expected_events)

    event_dicts = [event.to_dict() for event in events]
    for event in expected_events:
        assert event.to_dict() in event_dicts


async def test_delete_raw_field_events(mdl, app_request, db_session_maker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    raw_field_event_1 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_1.id,
        row_id=1,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )
    raw_field_event_2 = await mdl.RawFieldEvents(
        event_type=EntityEventType.CROPPING_EVENT,
        event_body={},
        field_id=field_1.id,
        row_id=2,
        phase_type=PhaseTypes.ENROLMENT,
        stage_type=StageTypes.CROP_EVENTS,
    )

    await delete_raw_field_events(request=app_request, raw_field_event_ids=[raw_field_event_1.id, raw_field_event_2.id])

    async with db_session_maker() as s:
        query = (
            select(RawFieldEvents)
            .where(RawFieldEvents.id.in_([raw_field_event_1.id, raw_field_event_2.id]))
            .order_by(RawFieldEvents.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        for single_res in res:
            assert single_res.deleted_at is not None
