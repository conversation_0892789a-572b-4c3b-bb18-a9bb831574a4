from datetime import datetime, timezone

from entity_events.data_classes import EventCreationSpecification
from entity_events.methods import get_entity_events_for_project_stages
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from values.enums import EntityTypeChoices


async def test_get_entity_events_for_project_stages(mdl, app_request):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.HISTORICAL_TILLAGE, enabled=True)
    attribute = await mdl.Attribute(parent_stage_id=stage.id, type=AttributeTypes.tillage_date, enabled=True)
    await mdl.Values(
        value="2025-01-01", attribute_id=attribute.id, field_id=field.id, entity_type=EntityTypeChoices.field
    )

    other_stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.HISTORICAL_CROP_ROTATION, enabled=True)
    other_attribute = await mdl.Attribute(parent_stage_id=other_stage.id, type=AttributeTypes.crop_type, enabled=True)
    await mdl.Values(
        value="corn", attribute_id=other_attribute.id, field_id=field.id, entity_type=EntityTypeChoices.field
    )

    res = await get_entity_events_for_project_stages(
        request=app_request,
        project_id=project.id,
        phase_type=phase.type_,
        stage_ids=[stage.id],
        entity_type=EntityTypeChoices.field,
        event_creation_specification=EventCreationSpecification(),
    )
    assert list(res.keys()) == [PhaseTypes.ENROLMENT]
    assert list(res[PhaseTypes.ENROLMENT].keys()) == [StageTypes.HISTORICAL_TILLAGE]
    assert len(res[PhaseTypes.ENROLMENT][StageTypes.HISTORICAL_TILLAGE]) == 1
    event = res[PhaseTypes.ENROLMENT][StageTypes.HISTORICAL_TILLAGE][0]
    assert event.entity_id == field.id
    assert event.entity_type == EntityTypeChoices.field
    assert event.occurred_at == datetime(2025, 1, 1, tzinfo=timezone.utc)
