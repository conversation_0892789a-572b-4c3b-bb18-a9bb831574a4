import hashlib
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field

from defaults.attribute_options import ApplicationRateType
from entity_events.units import AreaUnit, MassUnit, VolumeUnit


class CroppingIDs(BaseModel):
    """
    UUIDs representing all the individual SES activities that make up a cropping event. For example, a soy crop could
    have sowing, harvesting, and termination activities.

    In practice, it won't have both a planting_id and a sowing_id, but it could have both a harvesting_id and a
    termination_id.
    """

    planting_id: UUID | None = None
    sowing_id: UUID | None = None
    harvesting_id: UUID | None = None
    termination_id: UUID | None = None
    fallow_id: UUID | None = None

    @staticmethod
    def _str_or_none(field: UUID | None) -> str | None:
        return str(field) if field else None

    def planting_id_as_str(self) -> str | None:
        return CroppingIDs._str_or_none(self.planting_id)

    def sowing_id_as_str(self) -> str | None:
        return CroppingIDs._str_or_none(self.sowing_id)

    def harvesting_id_as_str(self) -> str | None:
        return CroppingIDs._str_or_none(self.harvesting_id)

    def termination_id_as_str(self) -> str | None:
        return CroppingIDs._str_or_none(self.termination_id)

    def fallow_id_as_str(self) -> str | None:
        return CroppingIDs._str_or_none(self.fallow_id)

    def is_non_fallow_cropping_event(self) -> bool:
        return isinstance(self, CroppingIDs) and self.fallow_id is None

    @staticmethod
    def make_cropping_id(start_id: str | None, termination_id: str | None, harvesting_id: str | None) -> str:
        """
        start_id is either planting or sowing, which shouldn't be defined at the same time
        """
        combined_key_string = f"{start_id}-{termination_id}-{harvesting_id}"
        sha_hash = hashlib.sha256(combined_key_string.encode("utf-8")).hexdigest()
        return sha_hash[:32]

    @property
    def end_id(self) -> UUID | None:
        """
        The last activity in a cropping event. This could be harvesting or termination. It's possible to have both, but
        we choose the first one that's not None.
        """
        return self.harvesting_id or self.termination_id

    def get_ids(self) -> list[str]:
        """
        Return a list of all the UUIDs that are not None.
        """
        return [
            str(event_id)
            for event_id in [self.planting_id, self.sowing_id, self.harvesting_id, self.termination_id, self.fallow_id]
            if event_id is not None
        ]


class ApplicationRate(BaseModel):
    value: Optional[float] = Field(ge=0)
    rate_type: Optional[ApplicationRateType]
    numerator_unit: Optional[MassUnit | VolumeUnit]
    denominator_unit: Optional[AreaUnit]


class ApplicationInput(BaseModel):
    """
    It seems like the NM stuff is still in flux with this PR, so this is a best-attempt stab at the minimal data that
    we ought to need?: https://github.com/regrowag/ses-scratch/pull/26/files
    """

    product_name: Optional[str]
    application_rate: Optional[ApplicationRate]  # MRV don't collect additive amounts rn + MeasureAPI don't care
