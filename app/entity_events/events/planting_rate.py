import enum

from pydantic import BaseModel, Field

from entity_events.units import AreaUnit, MassUnit


class CountType(enum.StrEnum):
    """
    Use this to specify that a type is a simple count, e.g. number of seeds. Need to make this an enum so that it can be
    used as a type in Pydantic models, otherwise parse_obj won't work.
    """

    COUNT = enum.auto()


class PlantingRate(BaseModel):
    value: float = Field(ge=0)
    numerator_unit: MassUnit | CountType
    denominator_unit: AreaUnit
