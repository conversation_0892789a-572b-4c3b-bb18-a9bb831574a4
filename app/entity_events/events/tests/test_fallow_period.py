import uuid
from datetime import datetime, timedelta, timezone

import pytest
from pydantic import ValidationError
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from regrow.ses.pbtype.interval_pb2 import Interval
from ses_client.fallow import FallowPeriod as SESFallowPeriod

from defaults.attribute_options import NoCropType
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.tests.utils import assert_pydantic_validation_error

start = datetime.now(tz=timezone.utc) - timedelta(days=90)
end = datetime.now(tz=timezone.utc) - timedelta(days=1)


def test_fallow_period(create_interval_data, create_fallow_period_data):
    """
    Testing that we can create what should be a valid FallowPeriod
    The test passes if no ValidationErrors are raised
    """
    interval = create_interval_data(start=start, end=end)
    fallow_period = create_fallow_period_data(interval=interval)
    FallowPeriod.parse_obj(fallow_period)


@pytest.mark.skip("Interval dates are no longer required, so we don't get a validation error")
def test_fallow_requires_start_and_end(create_fallow_period_data):
    """
    Testing that we get validation errors if we don't provide either a start or end date
    """
    time = datetime.now()
    with pytest.raises(ValidationError) as e:
        interval = {"end": time}
        fallow_period = create_fallow_period_data(interval=interval)
        FallowPeriod.parse_obj(fallow_period)

    assert_pydantic_validation_error(e, ["field required"])

    with pytest.raises(ValidationError) as e:
        interval = {"start": time}
        fallow_period = create_fallow_period_data(interval=interval)
        FallowPeriod.parse_obj(fallow_period)
    assert_pydantic_validation_error(e, ["field required"])


def test_fallow_period_to_ui_dict(create_interval_data, create_fallow_period_data):
    """
    Testing that a FallowPeriod can be converted to the expected UI dictionary
    """
    interval = create_interval_data(start=start, end=end)
    fallow_period = create_fallow_period_data(interval=interval)
    result = FallowPeriod.parse_obj(fallow_period).to_ui_dict()
    assert result["start_date"] == start.date()
    assert result["end_date"] == end.date()
    assert result["crop_type"] == NoCropType.FALLOW.value


async def test_fallow_period_to_ses_event(create_interval_data, create_fallow_period_data):
    """
    Testing that an SES FallowPeriod can be parsed from an MRV FallowPeriod EntityEvent
    """
    interval = create_interval_data(start=start, end=end)
    fallow_period = create_fallow_period_data(interval=interval)
    created_events, _ = await FallowPeriod.parse_obj(fallow_period).to_ses_events(
        owner_user_id="123", acting_user_id="456", geom="test geom"
    )
    assert len(created_events) == 1
    ses_fallow_period = created_events[0]
    assert ses_fallow_period.pb_event.interval == Interval(start_time=start, end_time=end)
    assert ses_fallow_period.pb_event.id == fallow_period["id"]["fallow_id"]
    assert ses_fallow_period.pb_context.association.get(CONTEXT_KEY_REGROW_ACTING_USER) == "456"
    assert ses_fallow_period.pb_context.association.get(CONTEXT_KEY_REGROW_OWNING_USER) == "123"


def test_fallow_period_from_ses_event():
    """
    Testing that an MRV FallowPeriod EntityEvent can be parsed from an SES FallowPeriod UpsertEventResponse
    """
    event_id = str(uuid.uuid4())
    ses_event = SESFallowPeriod(event_id=event_id, user_id="123")
    ses_event.geom("test geom").start(start).end(end)
    event_response = UpsertEventResponse(event=ses_event.pb_event, context=ses_event.pb_context)
    mrv_fallow_period = FallowPeriod.from_ses_events(events=[event_response], entity_id="123")

    assert isinstance(mrv_fallow_period, FallowPeriod)
    assert mrv_fallow_period.interval.start == start
    assert mrv_fallow_period.interval.end == end
    assert str(mrv_fallow_period.id.fallow_id) == event_id
