from entity_events.events.helpers import measurement_area_to_measurement_unit_string
from entity_events.units import AreaUnit, MassUnit, VolumeUnit


def test_measurement_area_to_measurement_unit_string():
    result = measurement_area_to_measurement_unit_string(MassUnit.KILOGRAM, AreaUnit.HECTARE)
    assert result == "kg1ha-1"

    result = measurement_area_to_measurement_unit_string(MassUnit.KILOGRAM, AreaUnit.SQUARE_METRE)
    assert result == "kg1sqm-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.LITRE, AreaUnit.HECTARE)
    assert result == "l1ha-1"

    result = measurement_area_to_measurement_unit_string(MassUnit.TONNE, AreaUnit.HECTARE)
    assert result == "mt1ha-1"

    result = measurement_area_to_measurement_unit_string(MassUnit.TONNE, AreaUnit.SQUARE_METRE)
    assert result == "mt1sqm-1"

    result = measurement_area_to_measurement_unit_string(MassUnit.POUND, AreaUnit.ACRE)
    assert result == "lb1ac-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.LITRE, AreaUnit.SQUARE_METRE)
    assert result == "l1sqm-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.US_GALLON, AreaUnit.ACRE)
    assert result == "gal1ac-1"

    result = measurement_area_to_measurement_unit_string(MassUnit.SHORT_TON, AreaUnit.ACRE)
    assert result == "tn1ac-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.US_QUART, AreaUnit.ACRE)
    assert result == "qt1ac-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.US_PINT, AreaUnit.ACRE)
    assert result == "pt1ac-1"

    result = measurement_area_to_measurement_unit_string(VolumeUnit.US_FLUID_OUNCE, AreaUnit.ACRE)
    assert result == "flo1ac-1"


def test_measurement_area_to_measurement_unit_string_invalid():
    result = measurement_area_to_measurement_unit_string(VolumeUnit.US_GALLON, AreaUnit.SQUARE_METRE)
    assert result is None
