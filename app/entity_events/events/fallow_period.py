from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from ses_client.event import new_event_id
from ses_client.fallow import FallowPeriod as SESFallowPeriod

from defaults.attribute_options import NoCropType
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.entity_event import EntityEvent, SESEventWithContext
from entity_events.events.schema import CroppingIDs
from entity_events.measures import Interval
from logger import get_logger
from values.enums import EntityTypeChoices

logger = get_logger(__name__)


class FallowPeriod(EntityEvent):
    entity_type: EntityTypeChoices
    interval: Optional[Interval]

    def get_interval_start_or_occurred_at(self) -> Optional[datetime]:
        return self.interval.start if self.interval is not None else None

    def get_interval_end_or_occurred_at(self) -> Optional[datetime]:
        return self.interval.end if self.interval is not None else None

    def to_ui_dict(self) -> dict:
        return {
            "start_date": self.interval.start.date() if self.interval and self.interval.start else None,
            "end_date": self.interval.end.date() if self.interval and self.interval.end else None,
            "crop_type": NoCropType.FALLOW.value,
        }

    def is_ebdc(self) -> bool:
        """
        The id property is populated with mrv_values.row_id in the case of legacy event data, and mrv_values.event_id,
        which is a UUID, in the case of EBDC data. This was done as a bandaid to enable UI development to proceed, and
        the final iteration of EBDC should not continue to do this.
        """
        return isinstance(self.id, UUID)

    async def to_ses_events(
        self, owner_user_id: str, acting_user_id: str, geom: str
    ) -> tuple[list[SESFallowPeriod], set[str]]:
        start = self.get_interval_start_or_occurred_at()
        end = self.get_interval_end_or_occurred_at()

        if (start is None) or (end is None):
            raise ValueError("SES FallowPeriod must have both a start and an end date")

        event_id = None
        to_delete_ids = set()

        if self.id and self.id.fallow_id is not None:
            # If the event is a fallow period, use the fallow_id as the event_id
            event_id = str(self.id.fallow_id)
        elif CroppingIDs.is_non_fallow_cropping_event(self.id):
            # If the event was previously a non fallow CroppingEvent, collect the cropping events IDs to delete
            to_delete_ids = {str(old_id) for old_id in self.id.dict().values() if old_id}
        elif self.id is not None:
            # self.id should be None or non fallow CroppingIDs. If it's anything else, it's invalid
            raise ValueError(f"Invalid ID type for SES FallowPeriod: {self.id}")

        if event_id is None:
            event_id = new_event_id()
        ses_fallow = SESFallowPeriod(event_id=event_id, user_id=owner_user_id)
        ses_fallow.start(start).end(end).geom(geom)
        ses_fallow.association_context(key=CONTEXT_KEY_REGROW_OWNING_USER, val=str(owner_user_id))
        ses_fallow.association_context(key=CONTEXT_KEY_REGROW_ACTING_USER, val=str(acting_user_id))

        return [ses_fallow], to_delete_ids

    @classmethod
    def from_ses_events(
        cls, events: list[SESEventWithContext], entity_id: str, entity_type: EntityTypeChoices = EntityTypeChoices.field
    ) -> "FallowPeriod":
        fallow_event = events[0].event
        interval = fallow_event.interval
        return cls(
            id=CroppingIDs(fallow_id=fallow_event.id),
            entity_id=entity_id,
            entity_type=entity_type,
            interval=Interval(
                start=interval.start_time.ToDatetime(tzinfo=timezone.utc),
                end=interval.end_time.ToDatetime(tzinfo=timezone.utc),
            ),
        )
