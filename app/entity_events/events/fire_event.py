from datetime import datetime

from pydantic import confloat

from entity_events.events.entity_event import EntityEvent


class FireEvent(EntityEvent):
    """
    A FireEvent represents the burning of a field's residue.
    """

    occurred_at: datetime
    burn_area_fraction: confloat(strict=True, ge=0, le=1)  # type: ignore
    # combusted_fraction is the fraction of the field's biomass lost to burning
    combusted_fraction: confloat(strict=True, ge=0, le=1)  # type: ignore

    def to_ui_dict(self) -> dict:
        return {
            "date": self.occurred_at.date(),
            "burn_area_fraction": self.burn_area_fraction if self.burn_area_fraction else None,
            "combusted_fraction": self.combusted_fraction if self.combusted_fraction else None,
        }
