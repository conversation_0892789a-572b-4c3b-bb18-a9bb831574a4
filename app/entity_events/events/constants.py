from types import MappingProxyType
from typing import Tuple, Union

import ses_client.planting
import ses_client.sowing
from regrow.ses.application.v1 import application_pb2
from regrow.ses.crop.v1 import crop_pb2
from regrow.ses.irrigation.v1 import irrigation_pb2
from regrow.ses.pbtype import area_pb2, line_pb2 as _line_pb2, mass_pb2, volume_pb2
from regrow.ses.termination.v1 import termination_pb2
from ses_client import crop as ses_crop, pb_value
from ses_client.application import (
    ApplicationBroadcast,
    ApplicationFertigation,
    ApplicationIncorporation,
    ApplicationInjection,
    ApplicationSubsurface,
)
from ses_client.input import (
    BasicFertiliser,
    EENFFertiliser,
    FertiliserAdditive,
    OrganicAmendment,
)

from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    ImperialUnits,
    IrrigationMethods,
    MetricUnits,
    PlantingMethod,
    ResidueHarvested,
    TerminationMethods,
)
from defaults.schema import NutrientProductType
from entity_events.units import AreaUnit, LengthUnit, MassUnit, VolumeUnit

# we will probably want to add these to the ses-client StructuredEvent
CONTEXT_KEY_REGROW_OWNING_USER = "mrvOwner"
CONTEXT_KEY_REGROW_ACTING_USER = "mrvActor"

# Common mappings

AREA_TO_SES_MAPPING: MappingProxyType[AreaUnit, area_pb2.AreaUnit] = MappingProxyType(
    {
        AreaUnit.HECTARE: area_pb2.AreaUnit.AREA_UNIT_HECTARE,
        AreaUnit.SQUARE_METRE: area_pb2.AreaUnit.AREA_UNIT_SQUARE_METRE,
        AreaUnit.SQUARE_KILOMETER: area_pb2.AreaUnit.AREA_UNIT_SQUARE_KILOMETRE,
        AreaUnit.ACRE: area_pb2.AreaUnit.AREA_UNIT_ACRE,
        AreaUnit.SQUARE_FOOT: area_pb2.AreaUnit.AREA_UNIT_SQUARE_FOOT,
        AreaUnit.SQUARE_YARD: area_pb2.AreaUnit.AREA_UNIT_SQUARE_YARD,
        AreaUnit.SQUARE_MILE: area_pb2.AreaUnit.AREA_UNIT_SQUARE_MILE,
    }
)

VOLUME_TO_SES_MAPPING: MappingProxyType[VolumeUnit, volume_pb2.VolumeUnit] = MappingProxyType(
    {
        VolumeUnit.CUBIC_METRE: volume_pb2.VolumeUnit.VOLUME_UNIT_CUBIC_METRE,
        VolumeUnit.MILLILITRE: volume_pb2.VolumeUnit.VOLUME_UNIT_MILLILITRE,
        VolumeUnit.LITRE: volume_pb2.VolumeUnit.VOLUME_UNIT_LITRE,
        VolumeUnit.MEGALITRE: volume_pb2.VolumeUnit.VOLUME_UNIT_MEGALITRE,
        VolumeUnit.CUBIC_INCH: volume_pb2.VolumeUnit.VOLUME_UNIT_CUBIC_INCH,
        VolumeUnit.CUBIC_FOOT: volume_pb2.VolumeUnit.VOLUME_UNIT_CUBIC_FOOT,
        VolumeUnit.CUBIC_YARD: volume_pb2.VolumeUnit.VOLUME_UNIT_CUBIC_YARD,
        VolumeUnit.ACRE_FOOT: volume_pb2.VolumeUnit.VOLUME_UNIT_ACRE_FOOT,
        VolumeUnit.ACRE_INCH: volume_pb2.VolumeUnit.VOLUME_UNIT_ACRE_INCH,
        VolumeUnit.US_GALLON: volume_pb2.VolumeUnit.VOLUME_UNIT_US_GALLON,
        VolumeUnit.US_QUART: volume_pb2.VolumeUnit.VOLUME_UNIT_US_QUART,
        VolumeUnit.US_PINT: volume_pb2.VolumeUnit.VOLUME_UNIT_US_PINT,
        VolumeUnit.US_FLUID_OUNCE: volume_pb2.VolumeUnit.VOLUME_UNIT_US_FLUID_OUNCE,
        VolumeUnit.BUSHEL: volume_pb2.VolumeUnit.VOLUME_UNIT_BUSHEL,
    }
)

MASS_TO_SES_MAPPING: MappingProxyType[MassUnit, mass_pb2.MassUnit] = MappingProxyType(
    {
        MassUnit.MILLIGRAM: mass_pb2.MassUnit.MASS_UNIT_MILLIGRAM,
        MassUnit.GRAM: mass_pb2.MassUnit.MASS_UNIT_GRAM,
        MassUnit.KILOGRAM: mass_pb2.MassUnit.MASS_UNIT_KILOGRAM,
        MassUnit.TONNE: mass_pb2.MassUnit.MASS_UNIT_TONNE,
        MassUnit.OUNCE: mass_pb2.MassUnit.MASS_UNIT_OUNCE,
        MassUnit.POUND: mass_pb2.MassUnit.MASS_UNIT_POUND,
        MassUnit.SHORT_TON: mass_pb2.MassUnit.MASS_UNIT_SHORT_TON,
    }
)

LENGTH_UNIT_TO_SES_MAPPING: MappingProxyType[LengthUnit, _line_pb2.LineUnit] = MappingProxyType(
    {
        LengthUnit.CENTIMETRE: _line_pb2.LINE_UNIT_CENTIMETRE,
        LengthUnit.INCH: _line_pb2.LINE_UNIT_INCH,
        LengthUnit.MILLIMETRE: _line_pb2.LINE_UNIT_MILLIMETRE,
    }
)


LENGTH_UNIT_FROM_SES_MAPPING: MappingProxyType[_line_pb2.LineUnit, LengthUnit] = MappingProxyType(
    {ses_unit: unit for unit, ses_unit in LENGTH_UNIT_TO_SES_MAPPING.items()}
)

VOLUME_FROM_SES_MAPPING: MappingProxyType[volume_pb2.VolumeUnit, VolumeUnit] = MappingProxyType(
    {ses_unit: unit for unit, ses_unit in VOLUME_TO_SES_MAPPING.items()}
)

MASS_FROM_SES_MAPPING: MappingProxyType[mass_pb2.MassUnit, MassUnit] = MappingProxyType(
    {ses_unit: unit for unit, ses_unit in MASS_TO_SES_MAPPING.items()}
)


AREA_FROM_SES_MAPPING: MappingProxyType[area_pb2.AreaUnit, AreaUnit] = MappingProxyType(
    {ses_unit: unit for unit, ses_unit in AREA_TO_SES_MAPPING.items()}
)

VOLUME_OR_LENGTH_TO_SES_MAPPING = {**LENGTH_UNIT_TO_SES_MAPPING, **VOLUME_TO_SES_MAPPING}
VOLUME_OR_LENGTH_FROM_SES_MAPPING = {**LENGTH_UNIT_FROM_SES_MAPPING, **VOLUME_FROM_SES_MAPPING}
VOLUME_OR_MASS_TO_SES_MAPPING = {**VOLUME_TO_SES_MAPPING, **MASS_TO_SES_MAPPING}


RATE_STRING_TO_UNIT_AND_AREA = {
    "flo1ac-1": (VolumeUnit.US_FLUID_OUNCE, AreaUnit.ACRE),
    "nkg1ha-1": (MassUnit.KILOGRAM, AreaUnit.HECTARE),
    "nlb1ac-1": (MassUnit.POUND, AreaUnit.ACRE),
    "pt1ac-1": (VolumeUnit.US_PINT, AreaUnit.ACRE),
    "bu1ac-1": (VolumeUnit.BUSHEL, AreaUnit.ACRE),
    "t1ac-1": (MassUnit.TONNE, AreaUnit.ACRE),
    ImperialUnits.GAL_AC: (VolumeUnit.US_GALLON, AreaUnit.ACRE),
    ImperialUnits.LBS_AC: (MassUnit.POUND, AreaUnit.ACRE),
    ImperialUnits.QT_AC: (VolumeUnit.US_QUART, AreaUnit.ACRE),
    MetricUnits.KG_HA: (MassUnit.KILOGRAM, AreaUnit.HECTARE),
    MetricUnits.KG_SQM: (MassUnit.KILOGRAM, AreaUnit.SQUARE_METRE),
    MetricUnits.L_HA: (VolumeUnit.LITRE, AreaUnit.HECTARE),
    MetricUnits.L_SQM: (VolumeUnit.LITRE, AreaUnit.SQUARE_METRE),
    ImperialUnits.TN_AC: (MassUnit.SHORT_TON, AreaUnit.ACRE),
    MetricUnits.MT_HA: (MassUnit.TONNE, AreaUnit.HECTARE),
    MetricUnits.T_HA: (MassUnit.TONNE, AreaUnit.HECTARE),
    MetricUnits.MT_SQM: (MassUnit.TONNE, AreaUnit.SQUARE_METRE),
}

UNIT_AND_AREA_TO_RATE_STRING: MappingProxyType[
    Tuple[Union[VolumeUnit, MassUnit], AreaUnit],
    Union[ImperialUnits, MetricUnits],
] = MappingProxyType(
    {
        area_measure: unit_string
        for unit_string, area_measure in RATE_STRING_TO_UNIT_AND_AREA.items()
        # TODO: MRV-5223 T_HA is slated to be deprecated. Ignore T_HA when mapping back to rate string in order to not overwrite MT_HA.
        if unit_string is not MetricUnits.T_HA
    }
)


RATE_STRING_TO_RATE_TYPE = {
    "flo1ac-1": ApplicationRateType.PRODUCT_RATE,
    "nkg1ha-1": ApplicationRateType.NITROGEN_RATE,
    "nlb1ac-1": ApplicationRateType.NITROGEN_RATE,
    "pt1ac-1": ApplicationRateType.PRODUCT_RATE,
    "t/ha": ApplicationRateType.PRODUCT_RATE,
    "bu1ac-1": ApplicationRateType.PRODUCT_RATE,
    "t1ac-1": ApplicationRateType.PRODUCT_RATE,
    ImperialUnits.GAL_AC: ApplicationRateType.PRODUCT_RATE,
    ImperialUnits.LBS_AC: ApplicationRateType.PRODUCT_RATE,
    ImperialUnits.QT_AC: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.T_HA: ApplicationRateType.PRODUCT_RATE,
    ImperialUnits.TN_AC: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.KG_HA: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.KG_SQM: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.L_HA: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.L_SQM: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.MT_HA: ApplicationRateType.PRODUCT_RATE,
    MetricUnits.MT_SQM: ApplicationRateType.PRODUCT_RATE,
}

# Cropping event constants

# Map to tuples of enums: some of these map to multiple SES enums (map to tuple rather than list because it's read-only)
CROP_USAGE_TO_SES_MAPPING: MappingProxyType[CropUsage, tuple[crop_pb2.CropPurpose, ...]] = MappingProxyType(
    {
        CropUsage.COMMODITY: (ses_crop.crop_purpose_commodity_harvest(),),
        CropUsage.COVER: (ses_crop.crop_purpose_cover(),),
        CropUsage.GRAZING: (ses_crop.crop_purpose_pasture(),),
        CropUsage.FORAGE_HARVEST: (ses_crop.crop_purpose_fodder(),),
        CropUsage.FORAGE_HARVEST_AND_GRAZING: (ses_crop.crop_purpose_fodder(), ses_crop.crop_purpose_pasture()),
        CropUsage.CATCH_CROP: (crop_pb2.CropPurpose.CROP_PURPOSE_CATCH_CROP,),
        CropUsage.COMPANION_CROP: (crop_pb2.CropPurpose.CROP_PURPOSE_COMPANION_CROP,),
    }
)

TERMINATION_METHOD_TO_SES_MAPPING: MappingProxyType[TerminationMethods, termination_pb2.TerminationMethod] = (
    MappingProxyType(
        {
            TerminationMethods.mechanical: termination_pb2.TerminationMethod.TERMINATION_METHOD_MOWING,
            TerminationMethods.winterkill: termination_pb2.TerminationMethod.TERMINATION_METHOD_WINTERKILL,
            TerminationMethods.herbicide: termination_pb2.TerminationMethod.TERMINATION_METHOD_HERBICIDE,
            TerminationMethods.tillage: termination_pb2.TerminationMethod.TERMINATION_METHOD_TILLAGE,
            # forage and grain harvest both map to the same ses method. When we're going the other way, we'll need
            # to use the crop purpose to differentiate between the two.
            TerminationMethods.forage_harvest: termination_pb2.TerminationMethod.TERMINATION_METHOD_HARVEST,
            TerminationMethods.grain_harvest: termination_pb2.TerminationMethod.TERMINATION_METHOD_HARVEST,
        }
    )
)

TERMINATION_METHOD_TO_SES_TERMINATION_REASON_MAPPING: MappingProxyType[
    TerminationMethods, termination_pb2.TerminationReason
] = MappingProxyType(
    {
        TerminationMethods.mechanical: termination_pb2.TerminationReason.TERMINATION_REASON_UNSPECIFIED,
        TerminationMethods.winterkill: termination_pb2.TerminationReason.TERMINATION_REASON_UNSPECIFIED,
        TerminationMethods.herbicide: termination_pb2.TerminationReason.TERMINATION_REASON_UNSPECIFIED,
        TerminationMethods.tillage: termination_pb2.TerminationReason.TERMINATION_REASON_UNSPECIFIED,
        # TerminationReason is used to distinguish Forage Harvest and Grain Harvest MRV Termination Methods
        TerminationMethods.forage_harvest: termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST,
        TerminationMethods.grain_harvest: termination_pb2.TerminationReason.TERMINATION_REASON_GRAIN_HARVEST,
    }
)


PLANTING_TO_SES_MAPPING: MappingProxyType[PlantingMethod, int] = MappingProxyType(
    {
        PlantingMethod.TRANSPLANT_WET: ses_client.planting.method_wet(),
        PlantingMethod.DIRECT_SEEDING_WET: ses_client.sowing.method_direct_wet(),
        PlantingMethod.DIRECT_SEEDING_DRY: ses_client.sowing.method_direct_dry(),
    }
)


SES_PURPOSES_TO_CROP_USAGE_MAPPING: MappingProxyType[frozenset[crop_pb2.CropPurpose], CropUsage] = MappingProxyType(
    {frozenset(ses_purposes): usage for usage, ses_purposes in CROP_USAGE_TO_SES_MAPPING.items()}
)

SES_PLANTING_TO_PLANTING_METHOD_MAPPING: MappingProxyType[int, PlantingMethod] = MappingProxyType(
    {ses_client.planting.method_wet(): PlantingMethod.TRANSPLANT_WET}
)

TERMINATION_FROM_SES_MAPPING: MappingProxyType[termination_pb2.TerminationMethod, TerminationMethods] = (
    MappingProxyType({ses_method: method for method, ses_method in TERMINATION_METHOD_TO_SES_MAPPING.items()})
)

SES_SOWING_TO_PLANTING_METHOD_MAPPING: MappingProxyType[int, PlantingMethod] = MappingProxyType(
    {
        ses_client.sowing.method_direct_dry(): PlantingMethod.DIRECT_SEEDING_DRY,
        ses_client.sowing.method_direct_wet(): PlantingMethod.DIRECT_SEEDING_WET,
    }
)

PERCENT_TO_RESIDUE_MAPPING: MappingProxyType[int, ResidueHarvested] = MappingProxyType(
    {
        25: ResidueHarvested.percent_25,
        50: ResidueHarvested.percent_50,
        75: ResidueHarvested.percent_75,
        85: ResidueHarvested.percent_85,
        95: ResidueHarvested.percent_95,
        100: ResidueHarvested.percent_100,
    }
)

USAGE_INVOLVES_HARVEST = frozenset(
    [CropUsage.COMMODITY, CropUsage.FORAGE_HARVEST, CropUsage.FORAGE_HARVEST_AND_GRAZING]
)

HARVEST_TERMINATION_METHODS = frozenset([TerminationMethods.forage_harvest, TerminationMethods.grain_harvest])

# Application event constants
SES_APPLICATION_METHODS_TO_MRV_MAPPING = {
    application_pb2.APPLICATION_METHOD_BROADCAST: ApplicationMethod.BROADCASTED,
    application_pb2.APPLICATION_METHOD_FERTIGATION: ApplicationMethod.FERTIGATION,
    application_pb2.APPLICATION_METHOD_INJECTION: ApplicationMethod.INJECTED,
    application_pb2.APPLICATION_METHOD_INCORPORATION: ApplicationMethod.INCORPORATED,
    application_pb2.APPLICATION_METHOD_SUBSURFACE: ApplicationMethod.SUBSURFACE,
}

SES_APPLICATION_FERTIGATION_METHODS_TO_MRV_MAPPING = {
    application_pb2.FERTIGATION_METHOD_SUB_SURFACE_DRIP: ApplicationMethod.FERTIGATION,
    application_pb2.FERTIGATION_METHOD_FURROW: ApplicationMethod.FERTIGATION_FURROW,
    application_pb2.FERTIGATION_METHOD_SPRINKLER: ApplicationMethod.FERTIGATION_SPRINKLER,
    application_pb2.FERTIGATION_METHOD_DRIP: ApplicationMethod.FERTIGATION_DRIP,
}

APPLICATION_METHOD_TO_SES_CLASS = {
    ApplicationMethod.FERTIGATION: ApplicationFertigation,
    ApplicationMethod.FERTIGATION_FURROW: ApplicationFertigation,
    ApplicationMethod.FERTIGATION_SPRINKLER: ApplicationFertigation,
    ApplicationMethod.FERTIGATION_DRIP: ApplicationFertigation,
    ApplicationMethod.BROADCASTED: ApplicationBroadcast,
    ApplicationMethod.INJECTED: ApplicationInjection,
    ApplicationMethod.INCORPORATED: ApplicationIncorporation,
    ApplicationMethod.SUBSURFACE: ApplicationSubsurface,
}

APPLICATION_METHOD_TO_SES_FERTIGATION_METHOD_MAPPING = {
    ApplicationMethod.FERTIGATION: application_pb2.FERTIGATION_METHOD_SUB_SURFACE_DRIP,
    ApplicationMethod.FERTIGATION_FURROW: application_pb2.FERTIGATION_METHOD_FURROW,
    ApplicationMethod.FERTIGATION_SPRINKLER: application_pb2.FERTIGATION_METHOD_SPRINKLER,
    ApplicationMethod.FERTIGATION_DRIP: application_pb2.FERTIGATION_METHOD_DRIP,
}

APPLICATION_RATE_TYPE_TO_SES_MAPPING = {
    ApplicationRateType.NITROGEN_RATE: application_pb2.FERTILISER_RATE_TYPE_NITROGEN_MASS,
    ApplicationRateType.PRODUCT_RATE: application_pb2.FERTILISER_RATE_TYPE_PRODUCT,
    None: application_pb2.FERTILISER_RATE_TYPE_UNSPECIFIED,
}

SES_FERTILIZER_RATE_TYPE_TO_MRV_MAPPING: MappingProxyType[str, ApplicationRateType] = MappingProxyType(
    {ses_method: rate_type for rate_type, ses_method in APPLICATION_RATE_TYPE_TO_SES_MAPPING.items()}
)

METHODS_ALLOWING_DEPTH = {
    ApplicationMethod.INCORPORATED,
    ApplicationMethod.INJECTED,
    ApplicationMethod.FERTIGATION,
    ApplicationMethod.SUBSURFACE,
}

SES_DEPTH_MAPPINGS = {
    LengthUnit.MILLIMETRE: pb_value.depth_millimetres,
    LengthUnit.CENTIMETRE: pb_value.depth_centimetres,
    LengthUnit.INCH: pb_value.depth_inches,
    VolumeUnit.LITRE: pb_value.litres,
    VolumeUnit.MEGALITRE: pb_value.megalitres,
    VolumeUnit.US_GALLON: pb_value.gallons,
}

PRODUCT_CATEGORY_CLASSES = {
    NutrientProductType.BASIC_INORGANIC: BasicFertiliser,
    NutrientProductType.ADDITIVE: FertiliserAdditive,
    NutrientProductType.EENF_INORGANIC: EENFFertiliser,
    NutrientProductType.ORGANIC: OrganicAmendment,
}

# Irrigation event constants

IRRIGATION_METHOD_TO_SES_MAPPING = {
    IrrigationMethods.furrow: irrigation_pb2.IRRIGATION_METHOD_FURROW,
    IrrigationMethods.sprinkler: irrigation_pb2.IRRIGATION_METHOD_SPRINKLER,
    IrrigationMethods.subsurface_drip: irrigation_pb2.IRRIGATION_METHOD_SUBSURFACE,
    IrrigationMethods.drip: irrigation_pb2.IRRIGATION_METHOD_DRIP,
    IrrigationMethods.flood: irrigation_pb2.IRRIGATION_METHOD_FLOOD,
}

IRRIGATION_METHOD_FROM_SES_MAPPING: MappingProxyType[irrigation_pb2.IrrigationMethod, IrrigationMethods] = (
    MappingProxyType({ses_unit: unit for unit, ses_unit in IRRIGATION_METHOD_TO_SES_MAPPING.items()})
)
