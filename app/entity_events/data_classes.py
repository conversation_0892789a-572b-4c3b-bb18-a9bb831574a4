from enum import Enum

from pydantic import BaseModel


class EventCreatorErrorHandling(Enum):
    SKIP_EVENT = "skip_event"
    RAISE_ERROR = "raise_error"


class EventCreationSpecification(BaseModel):
    # Specifies how event creators should behave when inputs are insufficient to create an event
    error_handling: EventCreatorErrorHandling = EventCreatorErrorHandling.RAISE_ERROR
    # Specifies whether FallowPeriod dates should be created based on record_year attribute
    auto_assign_fallow_dates: bool = False
