from datetime import date, datetime

from entity_events.validation.helpers import convert_datetime


def test_with_datetime_instance():
    """Test that a datetime object is returned as-is."""
    dt = datetime(2025, 1, 22, 12, 30, 45)
    assert convert_datetime(dt) == dt


def test_with_date_instance():
    """Test that a date object is converted to a datetime object."""
    dt = date(2025, 1, 22)
    expected = datetime(2025, 1, 22)
    assert convert_datetime(dt) == expected


def test_with_valid_string_formats():
    """Test valid string inputs matching DATE_FORMATS."""
    test_cases = [
        ("2025-01-22T12:30:45.000Z", datetime(2025, 1, 22, 12, 30, 45)),
        ("2025-01-22T12:30:45", datetime(2025, 1, 22, 12, 30, 45)),
        ("2025-01-22", datetime(2025, 1, 22)),
    ]
    for date_str, expected in test_cases:
        assert convert_datetime(date_str) == expected


def test_with_invalid_string():
    """Test that invalid string inputs raise a ValueError."""
    invalid_date = "22nd January 2025"
    try:
        convert_datetime(invalid_date)
    except ValueError:
        pass
    else:
        raise AssertionError("Expected ValueError for invalid date string")
