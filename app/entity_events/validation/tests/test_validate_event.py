from datetime import datetime, timezone

import pytest

from defaults.attribute_options import (
    ApplicationRateType,
    CropUsage,
    NoCropType,
    ResidueHarvested,
)
from defaults.defaults import defaults_retriever
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.validation.rules_scraper import validation_rules_scraper
from entity_events.validation.validator import AttributeValuesValidator
from fields.enums import FieldStatus
from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate

start_date = datetime(2023, 3, 15, 7, 0, 0, tzinfo=timezone.utc)
end_date = datetime(2023, 3, 16, 7, 0, 0, tzinfo=timezone.utc)
entity_id = 123


@pytest.fixture(scope="module", autouse=True)
async def setup_defaults_and_validations():
    await defaults_retriever.fetch_fertilizer_data(use_file=True)
    if len(await validation_rules_scraper.get_rules()) == 0:
        await validation_rules_scraper.run()


def assert_validation_results(validation_results, expected_all_results, expected_errors):
    assert (
        validation_results["all_results"] == expected_all_results
    ), "The 'all_results' validation output did not match the expected results."
    assert (
        validation_results["errors"] == expected_errors
    ), "The 'errors' validation output did not match the expected results."


async def test_validate_commodity_cropping_event_error(app_request, mdl, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "planting_date": datetime(year=2018, month=3, day=17),
        "harvest_date": datetime(year=2017, month=8, day=20),
        "crop_type": "corn",
        "crop_usage": CropUsage.COMMODITY,
        "residue_harvested": ResidueHarvested.percent_85,
    }
    mock_api_enabled = mocker.patch(
        "entity_events.validation.rules.cropping_event.defaults_translator.are_crops_measure_api_enabled",
        return_value={"corn": True},
    )

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=CroppingEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=True,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "crop_type": [
            {"Invalid crop type.": "passed"},
            {'Crop usage must be "Cover" for Crop types "Basic Cover Crop" and "Premium Cover Crop".': "passed"},
            {
                "Invalid fallow period data.": "passed",
            },
        ],
        "crop_usage": [
            {
                'Crop usage must be "Cover" for Crop types "Basic Cover Crop" and "Premium Cover Crop".': "passed",
            }
        ],
        "harvest_date": [
            {
                "Planting date must be before the harvest/termination date.": "failed",
            },
            {
                "A cropping event cannot be longer than 12 months.": "passed",
            },
        ],
        "planting_date": [
            {
                "Planting date must be before the harvest/termination date.": "failed",
            },
            {
                "A cropping event cannot be longer than 12 months.": "passed",
            },
        ],
    }

    expected_errors = {
        "harvest_date": ["Planting date must be before the harvest/termination date."],
        "planting_date": ["Planting date must be before the harvest/termination date."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)
    mock_api_enabled.assert_called()


async def test_validate_cropping_event_invalid_crop(app_request, mdl, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "planting_date": datetime(year=2017, month=3, day=17),
        "harvest_date": datetime(year=2017, month=8, day=20),
        "crop_type": "cherimoya",
        "crop_usage": CropUsage.COMMODITY,
    }
    mock_api_enabled = mocker.patch(
        "entity_events.validation.rules.cropping_event.defaults_translator.are_crops_measure_api_enabled",
        return_value={"cherimoya": False},
    )

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=CroppingEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )
    assert validation_results["errors"]["crop_type"] == ["Invalid crop type."]
    mock_api_enabled.assert_called()


async def test_validate_fallow_cropping_event_valid(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "start_date": datetime(year=2018, month=3, day=17),
        "end_date": datetime(year=2018, month=8, day=20),
        "crop_type": NoCropType.FALLOW.value,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=CroppingEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )
    assert len(validation_results["errors"]) == 0


async def test_validate_fallow_cropping_event_error(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "start_date": datetime(year=2018, month=3, day=17),
        "crop_type": NoCropType.FALLOW.value,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=CroppingEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "crop_type": [
            {"Invalid crop type.": "passed"},
            {"Invalid fallow period data.": "failed"},
        ],
    }

    expected_errors = {
        "crop_type": ["Invalid fallow period data."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_irrigation_event_error(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "start_date": datetime(year=2018, month=3, day=17),
        "end_date": datetime(year=2017, month=8, day=20),
        "irrigation_method": "furrow",
        "flood_pct": 102,
        "subsurface_drip_depth": -1,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=IrrigationEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "end_date": [
            {"Irrigation end date cannot be before start date.": "failed"},
            {"Irrigation cannot exceed 365 days.": "passed"},
        ],
        "flood_pct": [
            {"Flood percentage should be in range between 0 and 100.": "failed"},
        ],
        "subsurface_drip_depth": [
            {"Subsurface drip depth value should be positive.": "failed"},
        ],
    }

    expected_errors = {
        "end_date": ["Irrigation end date cannot be before start date."],
        "flood_pct": ["Flood percentage should be in range between 0 and 100."],
        "subsurface_drip_depth": ["Subsurface drip depth value should be positive."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_irrigation_event_error_length(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "start_date": datetime(year=2018, month=3, day=17),
        "end_date": datetime(year=2019, month=8, day=20),
        "irrigation_method": "furrow",
        "flood_pct": 102,
        "subsurface_drip_depth": -1,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=IrrigationEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "end_date": [
            {"Irrigation end date cannot be before start date.": "passed"},
            {"Irrigation cannot exceed 365 days.": "failed"},
        ],
        "flood_pct": [
            {"Flood percentage should be in range between 0 and 100.": "failed"},
        ],
        "subsurface_drip_depth": [
            {"Subsurface drip depth value should be positive.": "failed"},
        ],
    }

    expected_errors = {
        "end_date": ["Irrigation cannot exceed 365 days."],
        "flood_pct": ["Flood percentage should be in range between 0 and 100."],
        "subsurface_drip_depth": ["Subsurface drip depth value should be positive."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_tillage_event_error(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "tillage_date": datetime(year=2018, month=3, day=17),
        "record_year": 2017,
        "tillage_depth": 101,
        "tillage_depth_unit": "CENTIMETRE",
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=TillageEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=True,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "record_year": [
            {"Tillage date must match Year.": "failed"},
        ],
        "soil_inversion": [
            "is a required attribute type.",
        ],
        "tillage_date": [
            {"Tillage date must match Year.": "failed"},
        ],
        "tillage_depth": [
            {"Tillage depth should be between 0 and 100 cm or 39.37 inches.": "failed"},
        ],
    }

    expected_errors = {
        "record_year": ["Tillage date must match Year."],
        "soil_inversion": ["is a required attribute type."],
        "tillage_date": ["Tillage date must match Year."],
        "tillage_depth": ["Tillage depth should be between 0 and 100 cm or 39.37 inches."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_tillage_event_error_inches(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "tillage_date": datetime(year=2018, month=3, day=17),
        "record_year": 2017,
        "tillage_depth": 101,
        "tillage_depth_unit": "INCHES",
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=TillageEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=True,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "record_year": [
            {"Tillage date must match Year.": "failed"},
        ],
        "soil_inversion": [
            "is a required attribute type.",
        ],
        "tillage_date": [
            {"Tillage date must match Year.": "failed"},
        ],
        "tillage_depth": [
            {"Tillage depth should be between 0 and 100 cm or 39.37 inches.": "failed"},
        ],
    }

    expected_errors = {
        "record_year": ["Tillage date must match Year."],
        "soil_inversion": ["is a required attribute type."],
        "tillage_date": ["Tillage date must match Year."],
        "tillage_depth": ["Tillage depth should be between 0 and 100 cm or 39.37 inches."],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_application_event_error(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.legacy)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "application_date": datetime(year=2018, month=3, day=17),
        "application_rate": -1,
        "application_depth": -1,
        "application_rate_type": ApplicationRateType.PRODUCT_RATE,
        "application_rate_unit": "kg1ha-1",
        "additives": "ni,ui,ni",
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=ApplicationEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=True,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    expected_all_results = {
        "additives": [
            {"Cannot have more than 2 additives.": "failed"},
            {"Additives must include at most one Nitrification Inhibitor and one Urease Inhibitor.": "failed"},
            {"Additives must be unique. Can't select the same additive twice for the same event.": "failed"},
            {"Must be Measure approved additives.": "passed"},
        ],
        "application_depth": [
            {"Application depth value should be positive.": "failed"},
        ],
        "application_method": [
            "is a required attribute type.",
        ],
        "application_product": [
            "is a required attribute type.",
        ],
        "application_rate": [
            {"Application rate should be in range between 0 and 1000.": "failed"},
        ],
        "application_rate_type": [{"Nitrogen rate cannot be used with liquid measurements": "passed"}],
        "application_rate_unit": [{"Nitrogen rate cannot be used with liquid measurements": "passed"}],
    }

    expected_errors = {
        "application_depth": ["Application depth value should be positive."],
        "application_method": ["is a required attribute type."],
        "application_product": ["is a required attribute type."],
        "application_rate": ["Application rate should be in range between 0 and 1000."],
        "additives": [
            "Cannot have more than 2 additives.",
            "Additives must include at most one Nitrification Inhibitor and " "one Urease Inhibitor.",
            "Additives must be unique. Can't select the same additive twice " "for the same event.",
        ],
    }

    assert_validation_results(validation_results, expected_all_results, expected_errors)


async def test_validate_application_event_rate_type(app_request, mdl):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "application_date": datetime(year=2018, month=3, day=17),
        "application_rate_type": ApplicationRateType.NITROGEN_RATE,
        "application_rate_unit": "bu1ac-1",
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=ApplicationEvent.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )

    assert validation_results["errors"]["application_rate_unit"] == [
        "Nitrogen rate cannot be used with liquid measurements"
    ]
    assert validation_results["errors"]["application_rate_type"] == [
        "Nitrogen rate cannot be used with liquid measurements"
    ]


async def test_validate_fallow_period_invalid_crop(app_request, mdl, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "start_date": datetime(year=2017, month=3, day=17),
        "end_date": datetime(year=2017, month=8, day=20),
        "crop_type": "cherimoya",
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=FallowPeriod.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )
    assert validation_results["errors"]["crop_type"] == ["Invalid crop type."]


async def test_validate_fallow_period_invalid_start_date(app_request, mdl, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "start_date": datetime(year=2018, month=3, day=17),
        "end_date": datetime(year=2017, month=8, day=20),
        "crop_type": NoCropType.FALLOW,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=FallowPeriod.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )
    assert validation_results["errors"]["start_date"] == ["Start date must be before the end date."]


async def test_validate_fallow_period_invalid_interval_length(app_request, mdl, mocker):
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    event = {
        "id": 1,
        "field_id": 58053,
        "start_date": datetime(year=2017, month=3, day=17),
        "end_date": datetime(year=2020, month=8, day=20),
        "crop_type": NoCropType.FALLOW,
    }

    validation_results = await AttributeValuesValidator.validate_event(
        event=event,
        event_id=1,
        entity_event_name=FallowPeriod.__name__,
        run_required_attrs_check=True,
        run_required_values_check=False,
        program_id=program.id,
        request=app_request,
        field_id=field.id,
        phase_type=phase.type_,
    )
    assert validation_results["errors"]["start_date"] == ["A fallow period cannot be longer than 12 months."]
