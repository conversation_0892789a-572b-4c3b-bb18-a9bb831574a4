from fastapi import Request

from defaults.attribute_options import NoCropType
from entity_events.validation import helpers
from entity_events.validation.annotations import StagesValuesData
from entity_events.validation.rule_decorator import validation_rule
from entity_events.validation.schema import ValidationContext
from phases.enums import AttributeTypes


@validation_rule(
    attribute_type=AttributeTypes.start_date,
    error_message="Start date must be before the end date.",
    related_attributes={AttributeTypes.end_date},
    priority=1,  # error message for `end_date` attribute should be first
)
async def validate_start_end_dates(
    row_data: dict[AttributeTypes, str],
    stages_data: StagesValuesData,
    stage_id: int,
    field_id: int,
    row_id: int,
    validation_context: ValidationContext,
    request: Request,
) -> bool:
    """
    start_date - should be before the end date
    """
    return helpers.convert_datetime(row_data[AttributeTypes.start_date]) < helpers.convert_datetime(
        row_data[AttributeTypes.end_date]
    )


@validation_rule(
    attribute_type=AttributeTypes.start_date,
    error_message="A fallow period cannot be longer than 12 months.",
    related_attributes={AttributeTypes.end_date},
    priority=2,  # error message for `start_date` attribute should be second
)
async def validate_fallow_period_length(
    row_data: dict[AttributeTypes, str],
    stages_data: StagesValuesData,
    stage_id: int,
    field_id: int,
    row_id: int,
    validation_context: ValidationContext,
    request: Request,
) -> bool:
    """
    A fallow period cannot be longer than 12 months
    """

    start_date = helpers.convert_datetime(row_data[AttributeTypes.start_date])
    end_date = helpers.convert_datetime(row_data[AttributeTypes.end_date])
    diff = abs((end_date - start_date).days)
    return diff <= 365


@validation_rule(
    attribute_type=AttributeTypes.crop_type,
    error_message="Invalid crop type.",
)
async def validate_crop_type(
    row_data: dict[AttributeTypes, str],
    stages_data: StagesValuesData,
    stage_id: int,
    field_id: int,
    row_id: int,
    validation_context: ValidationContext,
    request: Request,
) -> bool:
    return row_data[AttributeTypes.crop_type] == NoCropType.FALLOW


@validation_rule(
    attribute_type=AttributeTypes.start_date,
    related_attributes={AttributeTypes.end_date},
    error_message="Requires both start_date and end_date",
)
async def validate_fallow_period(
    row_data: dict[AttributeTypes, str],
    stages_data: StagesValuesData,
    stage_id: int,
    field_id: int,
    row_id: int,
    validation_context: ValidationContext,
    request: Request,
) -> bool:
    return (AttributeTypes.start_date in row_data and AttributeTypes.end_date in row_data) or (
        AttributeTypes.planting_date in row_data and AttributeTypes.harvest_date in row_data
    )
