from typing import Any

import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from field_lineage.methods import set_field_baselines


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def set_field_baselines_task(
    self: Any,
    *,
    program_id: int,
    project_id: int,
    field_ids: list[int],
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await set_field_baselines(request=request, program_id=program_id, project_id=project_id, field_ids=field_ids)
