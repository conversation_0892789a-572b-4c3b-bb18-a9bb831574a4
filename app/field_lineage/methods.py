from dataclasses import dataclass

from fastapi import Request

from boundaries_service.client import feature_intersections
from fields.baselines.db import (
    bulk_create_or_update_field_baselines,
    get_field_baselines_by_field_ids,
    update_or_create_field_baseline_data,
)
from fields.db import (
    get_fields_by_program,
    get_fields_orm_by_field_ids,
)
from fields.enums import FieldStatus
from fields.model import Fields
from fields.schema import FieldBaselineUpdateRequest
from helper.async_tools import batch_list
from programs.db import get_program_by_id
from projects.db import get_previous_project_id


@dataclass
class MatchedFields:
    """
    field_id: The ID of the field
    md5: The MD5 of the field
    previous_field_id: The ID of the field in the previous program that the field was matched to
    previous_md5: The MD5 of the field in the previous program that the field was matched to
    percent_intersection: The percent intersection of the field with the previous field
    area_intersection_m2: The area of the intersection in M2 between the field and the previous field
    """

    field_id: int
    md5: str
    previous_field_id: int
    previous_md5: str
    percent_intersection: float
    area_intersection_m2: float


async def set_field_baselines(request: Request, program_id: int, project_id: int, field_ids: list[int]) -> None:
    program = await get_program_by_id(request=request, program_id=program_id)
    program_enrollment_year = program.reporting_period_start_date.year - 1
    if not program.previous_program_id:
        await bulk_create_or_update_field_baselines(
            request=request, field_ids=field_ids, baseline_year=program_enrollment_year, is_returning=False
        )
        return

    previous_fields = [
        field
        for field in await get_fields_by_program(request=request, program_id=program.previous_program_id)
        if field.status == FieldStatus.enrolled
    ]
    if not previous_fields:
        await bulk_create_or_update_field_baselines(
            request=request, field_ids=field_ids, baseline_year=program_enrollment_year, is_returning=False
        )
        return
    md5_to_previous_field = {field.md5: field for field in previous_fields}

    previous_project_id = await get_previous_project_id(
        request=request, project_id=project_id, previous_program_id=program.previous_program_id
    )
    previous_project_field_ids = {
        field.id: True for field in previous_fields if field.parent_project_id == previous_project_id
    }

    fields = await get_fields_orm_by_field_ids(request=request, field_ids=field_ids)
    md5_to_field = {field.md5: field for field in fields}

    unmatched_field_md5s = await _set_field_baselines_by_md5(
        request=request,
        md5_to_field=md5_to_field,
        md5_to_previous_field=md5_to_previous_field,
        previous_project_field_ids=previous_project_field_ids,
    )
    if unmatched_field_md5s:
        unmatched_field_md5s = await _set_field_baselines_by_overlap(
            request=request,
            unmatched_field_md5s=unmatched_field_md5s,
            md5_to_field=md5_to_field,
            md5_to_previous_field=md5_to_previous_field,
            previous_project_field_ids=previous_project_field_ids,
        )
        if unmatched_field_md5s:
            await bulk_create_or_update_field_baselines(
                request=request,
                field_ids=[md5_to_field[md5].id for md5 in unmatched_field_md5s],
                baseline_year=program_enrollment_year,
                is_returning=False,
            )


async def _set_field_baselines_by_md5(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    previous_project_field_ids: dict[int, bool],
) -> list[str]:
    matched_fields, unmatched_field_md5s = _get_previous_fields_by_md5(
        md5_to_field=md5_to_field, md5_to_previous_field=md5_to_previous_field
    )
    await create_field_overlap_results(matched_fields=matched_fields)
    matched_fields, unmatched_field_md5s = _filter_previous_fields(
        matched_fields=matched_fields,
        unmatched_field_md5s=unmatched_field_md5s,
        previous_project_field_ids=previous_project_field_ids,
    )
    if matched_fields:
        await _set_baselines_for_returning_fields(
            request=request,
            matched_fields=matched_fields,
        )
    return unmatched_field_md5s


async def _set_field_baselines_by_overlap(
    request: Request,
    unmatched_field_md5s: list[str],
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    previous_project_field_ids: dict[int, bool],
) -> list[str]:
    matched_fields, unmatched_field_md5s = await _get_previous_fields_by_overlap(
        md5_to_field={md5: field for md5, field in md5_to_field.items() if md5 in unmatched_field_md5s},
        md5_to_previous_field=md5_to_previous_field,
    )
    await create_field_overlap_results(matched_fields=matched_fields)
    matched_fields, unmatched_field_md5s = _filter_previous_fields(
        matched_fields=matched_fields,
        unmatched_field_md5s=unmatched_field_md5s,
        previous_project_field_ids=previous_project_field_ids,
    )
    if matched_fields:
        await _set_baselines_for_returning_fields(
            request=request,
            matched_fields=matched_fields,
        )
    return unmatched_field_md5s


def _get_previous_fields_by_md5(
    md5_to_field: dict[str, Fields], md5_to_previous_field: dict[str, Fields]
) -> tuple[list[MatchedFields], list[str]]:
    matched_fields: list[MatchedFields] = []
    unmatched_field_md5s: list[str] = []
    for md5 in md5_to_field.keys():
        if md5_to_previous_field.get(md5):
            matched_fields.append(
                MatchedFields(
                    field_id=md5_to_field[md5].id,
                    md5=md5,
                    previous_field_id=md5_to_previous_field[md5].id,
                    previous_md5=md5,
                    percent_intersection=100,
                    area_intersection_m2=md5_to_field[md5].area,
                )
            )
        else:
            unmatched_field_md5s.append(md5)
    return matched_fields, unmatched_field_md5s


async def _get_previous_fields_by_overlap(
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
) -> tuple[list[MatchedFields], list[str]]:
    md5s = list(md5_to_field.keys())
    previous_md5s = list(md5_to_previous_field.keys())
    matched_fields = []
    for md5_batch in batch_list(full_list=md5s, batch_size=50):
        all_intersections = await feature_intersections(boundary_ids=md5_batch, comparison_boundary_ids=previous_md5s)
        for md5, intersections in all_intersections.feature_intersections.items():
            max_intersection = max(intersections, key=lambda i: i.percent_intersection_first)
            matched_fields.append(
                MatchedFields(
                    field_id=md5_to_field[md5].id,
                    md5=md5,
                    previous_field_id=md5_to_previous_field[max_intersection.intersecting_id].id,
                    previous_md5=max_intersection.intersecting_id,
                    percent_intersection=max_intersection.percent_intersection_first,
                    area_intersection_m2=max_intersection.area_intersection_m2,
                )
            )
    unmatched_field_md5s = list(set(md5s) - {matched_field.md5 for matched_field in matched_fields})
    return matched_fields, unmatched_field_md5s


def _filter_previous_fields(
    matched_fields: list[MatchedFields], unmatched_field_md5s: list[str], previous_project_field_ids: dict[int, bool]
) -> tuple[list[MatchedFields], list[str]]:
    filtered_matched_fields = []
    filtered_unmatched_field_md5s = unmatched_field_md5s
    for matched_field in matched_fields:
        if matched_field.percent_intersection >= 90 and previous_project_field_ids.get(matched_field.previous_field_id):
            filtered_matched_fields.append(matched_field)
        else:
            filtered_unmatched_field_md5s.append(matched_field.md5)
    return filtered_matched_fields, filtered_unmatched_field_md5s


async def _set_baselines_for_returning_fields(request: Request, matched_fields: list[MatchedFields]) -> None:
    previous_field_baselines = await get_field_baselines_by_field_ids(
        request=request,
        field_ids=[matched_field.previous_field_id for matched_field in matched_fields],
    )
    previous_field_to_baseline_year = {
        baseline.field_id: baseline.baseline_year for baseline in previous_field_baselines
    }
    for matched_field in matched_fields:
        baseline_year = previous_field_to_baseline_year[matched_field.previous_field_id]
        await update_or_create_field_baseline_data(
            request=request,
            update_baseline_data=[
                FieldBaselineUpdateRequest(
                    field_id=matched_field.field_id, baseline_year=baseline_year, is_returning=True
                )
            ],
        )


# TODO: implement and move to DB methods. this method should create an mrv_field_overlap_result record for each MatchedFields.
async def create_field_overlap_results(matched_fields: list[MatchedFields]) -> None:
    return
