from datetime import datetime
from unittest.mock import patch

from sqlalchemy import select

from boundaries_service.schema import FeatureIntersections, Intersection
from field_lineage.methods import (
    _filter_previous_fields,
    _get_previous_fields_by_md5,
    _get_previous_fields_by_overlap,
    _set_baselines_for_returning_fields,
    MatchedFields,
    set_field_baselines,
)
from fields.enums import FieldStatus
from fields.model import FieldsBaseline
from helper.helper import run_query
from projects.enums import ProjectStatus


async def test_set_field_baselines_no_previous_program(mdl, app_request, db_session_maker):
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)

    await set_field_baselines(
        request=app_request, program_id=program.id, project_id=project.id, field_ids=[field_1.id, field_2.id]
    )

    async with db_session_maker() as s:
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2024, is_returning=False).to_dict()
        )


async def test_set_field_baselines_no_previous_fields(mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_1 = await mdl.Fields(parent_project_id=project_1.id)
    field_2 = await mdl.Fields(parent_project_id=project_1.id)

    await set_field_baselines(
        request=app_request, program_id=program_1.id, project_id=project_1.id, field_ids=[field_1.id, field_2.id]
    )

    async with db_session_maker() as s:
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2024, is_returning=False).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_with_previous_program(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user = await mdl.Users()

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # match to previous field by MD5
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md51")
    # match to previous field by overlap
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")
    # no previous field
    field_103 = await mdl.Fields(parent_project_id=project_1.id, md5="md5103")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_102.md5: [
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=1,
                )
            ]
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id, field_102.id, field_103.id],
    )

    async with db_session_maker() as s:
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_101.id, field_102.id, field_103.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_102.id, baseline_year=2022, is_returning=True).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_103.id, baseline_year=2024, is_returning=False).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_multiple_overlaps(mock_feature_intersections, mdl, app_request, db_session_maker):
    user = await mdl.Users()

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=1,
                ),
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=99,
                    percent_intersection_second=99,
                    area_intersection_m2=2,
                ),
            ]
        }
    )

    await set_field_baselines(
        request=app_request, program_id=program_1.id, project_id=project_1.id, field_ids=[field_101.id]
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2022, is_returning=True).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_previous_field_with_less_than_90_percent_overlap(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user = await mdl.Users()

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=10,
                    percent_intersection_second=10,
                    area_intersection_m2=1,
                ),
            ]
        }
    )

    await set_field_baselines(
        request=app_request, program_id=program_1.id, project_id=project_1.id, field_ids=[field_101.id]
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2024, is_returning=False).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_previous_field_from_different_user(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user_0 = await mdl.Users()
    user_1 = await mdl.Users()

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md51")

    mock_feature_intersections.return_value = FeatureIntersections(feature_intersections={})

    await set_field_baselines(
        request=app_request, program_id=program_1.id, project_id=project_1.id, field_ids=[field_101.id]
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2024, is_returning=False).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_update_field_baseline(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user = await mdl.Users()

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    await mdl.FieldsBaseline(field_id=field_101.id, baseline_year=2024, is_returning=False)

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=99,
                    percent_intersection_second=99,
                    area_intersection_m2=1,
                ),
            ]
        }
    )

    await set_field_baselines(
        request=app_request, program_id=program_1.id, project_id=project_1.id, field_ids=[field_101.id]
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )


async def test_get_previous_fields_by_md5(mdl):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51")
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52")

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md51")
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")

    matched_fields, unmatched_field_md5s = _get_previous_fields_by_md5(
        md5_to_field={"md51": field_101, "md5102": field_102}, md5_to_previous_field={"md51": field_1, "md52": field_2}
    )
    assert matched_fields == [
        MatchedFields(
            field_id=field_101.id,
            md5="md51",
            previous_field_id=field_1.id,
            previous_md5="md51",
            percent_intersection=100,
            area_intersection_m2=field_101.area,
        )
    ]
    assert unmatched_field_md5s == ["md5102"]


@patch("field_lineage.methods.feature_intersections")
async def test_get_previous_fields_by_overlap(mock_feature_intersections, mdl):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51")
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52")

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")
    field_103 = await mdl.Fields(parent_project_id=project_1.id, md5="md5103")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            "md5101": [
                Intersection(
                    intersecting_id="md51",
                    percent_intersection_first=99,
                    percent_intersection_second=99,
                    area_intersection_m2=1,
                ),
            ],
            "md5102": [
                Intersection(
                    intersecting_id="md52",
                    percent_intersection_first=10,
                    percent_intersection_second=10,
                    area_intersection_m2=2,
                ),
            ],
        }
    )
    matched_fields, unmatched_field_md5s = await _get_previous_fields_by_overlap(
        md5_to_field={"md5101": field_101, "md5102": field_102, "md5103": field_103},
        md5_to_previous_field={"md51": field_1, "md52": field_2},
    )
    assert matched_fields == [
        MatchedFields(
            field_id=field_101.id,
            md5="md5101",
            previous_field_id=field_1.id,
            previous_md5="md51",
            percent_intersection=99,
            area_intersection_m2=1,
        ),
        MatchedFields(
            field_id=field_102.id,
            md5="md5102",
            previous_field_id=field_2.id,
            previous_md5="md52",
            percent_intersection=10,
            area_intersection_m2=2,
        ),
    ]
    assert unmatched_field_md5s == ["md5103"]


async def test_filter_previous_fields(mdl):
    matched_fields = [
        MatchedFields(
            field_id=101,
            md5="md5101",
            previous_field_id=1,
            previous_md5="md51",
            percent_intersection=99,
            area_intersection_m2=1,
        ),
        MatchedFields(
            field_id=102,
            md5="md5102",
            previous_field_id=2,
            previous_md5="md52",
            percent_intersection=99,
            area_intersection_m2=2,
        ),
    ]
    unmatched_field_md5s = ["md5103"]
    previous_project_field_ids = {1: True}
    filtered_matched_fields, filtered_unmatched_field_md5s = _filter_previous_fields(
        matched_fields=matched_fields,
        unmatched_field_md5s=unmatched_field_md5s,
        previous_project_field_ids=previous_project_field_ids,
    )
    assert filtered_matched_fields == [
        MatchedFields(
            field_id=101,
            md5="md5101",
            previous_field_id=1,
            previous_md5="md51",
            percent_intersection=99,
            area_intersection_m2=1,
        )
    ]
    assert filtered_unmatched_field_md5s == ["md5103", "md5102"]


async def test_set_baselines_for_returning_fields(mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")

    await _set_baselines_for_returning_fields(
        request=app_request,
        matched_fields=[
            MatchedFields(
                field_id=field_101.id,
                md5="md5101",
                previous_field_id=field_1.id,
                previous_md5="md51",
                percent_intersection=99,
                area_intersection_m2=1,
            )
        ],
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )
