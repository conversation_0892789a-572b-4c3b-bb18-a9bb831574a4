from fastapi import APIRouter, Depends, Request, status

import field_lineage.paths as field_lineage_paths
from field_lineage.schema import SetFieldBaselinesRequest
from field_lineage.tasks import set_field_baselines_task
from permissions.enums import Permission
from permissions.resolver import Permissions

tags = ["field_lineage"]
router = APIRouter(prefix=field_lineage_paths.base)


@router.post(
    field_lineage_paths.field_baselines,
    tags=tags,
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.UPDATE_PROJECT_FIELD_VALUES]))],
    description="Sets the field baselines for the fields.",
)
async def set_field_baselines(request: Request, body: SetFieldBaselinesRequest) -> None:
    set_field_baselines_task.delay(
        program_id=body.program_id,
        project_id=body.project_id,
        field_ids=body.field_ids,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )
