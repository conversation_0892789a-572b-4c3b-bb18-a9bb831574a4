import uuid
from datetime import datetime

from sqlalchemy import select

from helper.helper import run_query
from migrate_single_year_entities.db import (
    create_program_migration_run,
    get_latest_program_migration_run_by_dest_program_id,
    get_program_migration_run_by_task_id,
    update_program_migration_run,
)
from migrate_single_year_entities.enums import ProgramMigrationStatus
from migrate_single_year_entities.model import ProgramMigrationRuns
from migrate_single_year_entities.schema import ProgramMigrationRunResponse


async def test_get_latest_program_migration_run_by_dest_program_id(mdl, app_request):
    dest_program_id = 2
    await mdl.ProgramMigrationRuns(
        task_id=str(uuid.uuid4()),
        source_program_id=1,
        destination_program_id=dest_program_id,
        user_id=1,
        status=ProgramMigrationStatus.FAILED,
        created_at=datetime(2025, 1, 1),
    )
    program_migration_run_2 = await mdl.ProgramMigrationRuns(
        task_id=str(uuid.uuid4()),
        source_program_id=1,
        destination_program_id=dest_program_id,
        user_id=1,
        status=ProgramMigrationStatus.IN_PROGRESS,
        created_at=datetime(2025, 1, 2),
    )
    res = await get_latest_program_migration_run_by_dest_program_id(
        request=app_request, dest_program_id=dest_program_id
    )
    assert res == ProgramMigrationRunResponse.from_orm(program_migration_run_2)


async def test_get_program_migration_run_by_task_id(mdl, app_request):
    task_id = str(uuid.uuid4())
    program_migration_run = await mdl.ProgramMigrationRuns(
        task_id=task_id,
        source_program_id=1,
        destination_program_id=2,
        user_id=1,
        status=ProgramMigrationStatus.IN_PROGRESS,
    )
    res = await get_program_migration_run_by_task_id(request=app_request, task_id=task_id)
    assert res == ProgramMigrationRunResponse.from_orm(program_migration_run)


async def test_create_program_migration_run(app_request, db_session_maker):
    task_id = str(uuid.uuid4())
    source_program_id = 1
    dest_program_id = 2
    user_id = 1
    await create_program_migration_run(
        request=app_request,
        task_id=task_id,
        source_program_id=source_program_id,
        dest_program_id=dest_program_id,
        user_id=user_id,
    )

    async with db_session_maker() as s:
        query = select(ProgramMigrationRuns).where(ProgramMigrationRuns.task_id == task_id)
        res = (await run_query(query=query, s=s)).scalars().all()

    assert len(res) == 1
    assert (
        res[0].to_dict()
        == ProgramMigrationRuns(
            id=res[0].id,
            task_id=task_id,
            source_program_id=source_program_id,
            destination_program_id=dest_program_id,
            user_id=user_id,
            status=ProgramMigrationStatus.IN_PROGRESS,
            created_at=res[0].created_at,
            updated_at=res[0].updated_at,
        ).to_dict()
    )


async def test_update_program_migration_run_succeeded(mdl, app_request, db_session_maker):
    task_id = str(uuid.uuid4())
    program_migration_run = await mdl.ProgramMigrationRuns(
        task_id=task_id,
        source_program_id=1,
        destination_program_id=2,
        user_id=1,
        status=ProgramMigrationStatus.IN_PROGRESS,
    )

    await update_program_migration_run(request=app_request, task_id=task_id, status=ProgramMigrationStatus.SUCCEEDED)

    async with db_session_maker() as s:
        query = select(ProgramMigrationRuns).where(ProgramMigrationRuns.task_id == task_id)
        res = (await run_query(query=query, s=s)).scalars().all()

    assert len(res) == 1
    assert (
        res[0].to_dict()
        == ProgramMigrationRuns(
            id=res[0].id,
            task_id=task_id,
            source_program_id=program_migration_run.source_program_id,
            destination_program_id=program_migration_run.destination_program_id,
            user_id=program_migration_run.user_id,
            status=ProgramMigrationStatus.SUCCEEDED,
            created_at=res[0].created_at,
            updated_at=res[0].updated_at,
        ).to_dict()
    )


async def test_update_program_migration_run_failed(mdl, app_request, db_session_maker):
    task_id = str(uuid.uuid4())
    program_migration_run = await mdl.ProgramMigrationRuns(
        task_id=task_id,
        source_program_id=1,
        destination_program_id=2,
        user_id=1,
        status=ProgramMigrationStatus.IN_PROGRESS,
    )

    await update_program_migration_run(
        request=app_request, task_id=task_id, status=ProgramMigrationStatus.FAILED, error_message="error"
    )

    async with db_session_maker() as s:
        query = select(ProgramMigrationRuns).where(ProgramMigrationRuns.task_id == task_id)
        res = (await run_query(query=query, s=s)).scalars().all()

    assert len(res) == 1
    assert (
        res[0].to_dict()
        == ProgramMigrationRuns(
            id=res[0].id,
            task_id=task_id,
            source_program_id=program_migration_run.source_program_id,
            destination_program_id=program_migration_run.destination_program_id,
            user_id=program_migration_run.user_id,
            status=ProgramMigrationStatus.FAILED,
            error_message="error",
            created_at=res[0].created_at,
            updated_at=res[0].updated_at,
        ).to_dict()
    )
