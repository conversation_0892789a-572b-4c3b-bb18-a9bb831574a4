import sqlalchemy as sa

from db.mysql import Base
from migrate_single_year_entities.enums import ProgramMigrationStatus


class ProgramMigrationRuns(Base):
    __tablename__ = "mrv_program_migration_runs"
    id = sa.Column(sa.Integer, primary_key=True, nullable=False)
    task_id = sa.Column(sa.String(36), nullable=False)
    source_program_id = sa.Column(sa.Integer, nullable=False)
    destination_program_id = sa.Column(sa.Integer, nullable=False)
    user_id = sa.Column(sa.Integer, nullable=False)
    status = sa.Column(sa.Enum(ProgramMigrationStatus), nullable=False)
    error_message = sa.Column(sa.String(200), nullable=True)
    created_at = sa.Column(sa.TIMESTAMP, server_default=sa.sql.func.current_timestamp(), nullable=False)
    updated_at = sa.Column(
        sa.TIMESTAMP,
        server_default=sa.sql.func.current_timestamp(),
        onupdate=sa.sql.func.current_timestamp(),
        nullable=False,
    )
