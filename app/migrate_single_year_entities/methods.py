import json

from fastapi import Request
from sqlalchemy import select

from core.model import Users
from docusign.db import get_custom_registration_inputs_for_program, get_project_values
from fields.baselines.db import get_field_baselines_by_field_ids
from fields.db import (
    get_field_history,
    get_fields_orm_by_field_ids,
    get_fields_orm_by_project_ids,
)
from fields.enums import FieldHistoryChangeReason, FieldStatus
from fields.model import Fields, FieldsBaseline, FieldsHistory
from fields.schema import Field
from helper.helper import run_query
from migrate_single_year_entities.schema import MigrationProjectWithUser
from permissions.db import get_program_role_users
from permissions.model import RolesUsers
from programs.db import (
    get_program_permissions_by_program_id,
    get_project_permissions_by_program_id_all_users,
)
from programs.enums import CustomRegInputsTypes
from programs.model import ProgramCustomRegInputs, ProgramPermissions
from programs.schema import ResponseProgramCustomRegInputs
from projects.enums import ProjectStatus
from projects.farms.db import get_farms
from projects.farms.model import Farms
from projects.model import ProjectPermissions, Projects, ProjectValues
from projects.schema import ProjectNoProgramNoDates
from root_crud import create
from user_groups.db import (
    get_user_group_admins_by_program_id,
    get_user_group_projects_by_program_id,
    get_user_groups_by_program_id,
    partial_update_groups,
)
from user_groups.model import UserGroupProjects, UserGroups, UserGroupsAdmins
from user_groups.schema import PatchUserGroupRequest, UserGroup


# this is used to unit test all of the single year migration methods.
async def migrate_single_year_entities(request: Request, source_program_id: int, dest_program_id: int) -> None:
    source_to_dest_program_custom_reg_input_id = await _migrate_program_custom_reg_inputs(
        request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
    )
    await _migrate_user_roles(request=request, source_program_id=source_program_id, dest_program_id=dest_program_id)
    await _migrate_user_program_permissions(
        request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
    )
    source_to_dest_user_group_id = await _migrate_user_groups(
        request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
    )
    await _migrate_user_group_admins(
        request=request,
        source_program_id=source_program_id,
        dest_program_id=dest_program_id,
        source_to_dest_user_group_id=source_to_dest_user_group_id,
    )
    source_to_dest_project_id = await _migrate_projects(
        request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
    )
    await _migrate_user_group_projects(
        request=request,
        source_program_id=source_program_id,
        dest_program_id=dest_program_id,
        source_to_dest_user_group_id=source_to_dest_user_group_id,
        source_to_dest_project_id=source_to_dest_project_id,
    )
    await _migrate_user_project_permissions(
        request=request,
        source_program_id=source_program_id,
        dest_program_id=dest_program_id,
        source_to_dest_project_id=source_to_dest_project_id,
    )
    for source_project_id, dest_project_id in source_to_dest_project_id.items():
        await _migrate_single_project_entities(
            request=request,
            source_project_id=source_project_id,
            dest_project_id=dest_project_id,
            source_to_dest_program_custom_reg_input_id=source_to_dest_program_custom_reg_input_id,
        )


async def _migrate_program_custom_reg_inputs(
    request: Request, source_program_id: int, dest_program_id: int
) -> dict[int, int]:
    source_inputs = [
        reg_input
        for reg_input in (
            await get_custom_registration_inputs_for_program(request=request, program_id=source_program_id)
        ).values()
        if reg_input.type_ != CustomRegInputsTypes.user_group
    ]
    dest_inputs = [
        reg_input
        for reg_input in (
            await get_custom_registration_inputs_for_program(request=request, program_id=dest_program_id)
        ).values()
        if reg_input.type_ != CustomRegInputsTypes.user_group
    ]
    program_id_name_to_dest_input = {(reg_input.program_id, reg_input.name): reg_input for reg_input in dest_inputs}

    # order numbers must be unique
    starting_order_number = max(reg_input.order for reg_input in dest_inputs) + 1 if dest_inputs else 0
    source_to_dest_input_id = {}
    to_create = []
    for source_input in source_inputs:
        dest_input = program_id_name_to_dest_input.get((dest_program_id, source_input.name))
        if dest_input:
            source_to_dest_input_id[source_input.id] = dest_input.id
            continue
        to_create.append(
            ProgramCustomRegInputs(
                program_id=dest_program_id,
                order=source_input.order + starting_order_number,
                name=source_input.name,
                input_key=source_input.input_key,
                type_=source_input.type_,
                validation_rule=source_input.validation_rule,
                config=source_input.config,
                visibility=source_input.visibility,
                deleted_at=None,
            )
        )
    if to_create:
        created = await create.create(
            request=request,
            instances=to_create,
            orm_type=ProgramCustomRegInputs,
            translate=False,
            type=ResponseProgramCustomRegInputs,
        )
        if len(to_create) != len(created):
            raise Exception("Program custom reg inputs could not be migrated.")

        program_id_name_to_dest_input_created = {
            (reg_input.program_id, reg_input.name): reg_input for reg_input in created
        }
        for source_input in source_inputs:
            if source_to_dest_input_id.get(source_input.id):
                continue
            source_to_dest_input_id[source_input.id] = program_id_name_to_dest_input_created[
                (dest_program_id, source_input.name)
            ].id

    return source_to_dest_input_id


async def _migrate_user_roles(request: Request, source_program_id: int, dest_program_id: int) -> None:
    source_user_roles = await get_program_role_users(request=request, program_id=source_program_id)
    dest_user_roles = await get_program_role_users(request=request, program_id=dest_program_id)
    user_id_to_dest_user_roles = {roles_user.fs_user_id: roles_user for roles_user in dest_user_roles}

    to_create = []
    for source_user_role in source_user_roles:
        dest_user_role = user_id_to_dest_user_roles.get(source_user_role.fs_user_id)
        if dest_user_role:
            continue
        to_create.append(
            RolesUsers(
                fs_user_id=source_user_role.fs_user_id, role_id=source_user_role.role_id, program_id=dest_program_id
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=RolesUsers)
        except Exception as e:
            raise Exception(f"User roles could not be migrated: {e}")


async def _migrate_user_program_permissions(request: Request, source_program_id: int, dest_program_id: int) -> None:
    source_permissions = await get_program_permissions_by_program_id(request=request, program_id=source_program_id)
    dest_permissions = await get_program_permissions_by_program_id(request=request, program_id=dest_program_id)
    user_id_to_dest_permissions = {permission.user_id: permission for permission in dest_permissions}

    to_create = []
    for source_permission in source_permissions:
        dest_permission = user_id_to_dest_permissions.get(source_permission.user_id)
        if dest_permission:
            continue
        to_create.append(
            ProgramPermissions(
                user_id=source_permission.user_id,
                program_id=dest_program_id,
                details=source_permission.details,
                deleted_at_unix=0,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=ProgramPermissions)
        except Exception as e:
            raise Exception(f"User program permissions could not be migrated: {e}")


async def _migrate_user_groups(request: Request, source_program_id: int, dest_program_id: int) -> dict[int, int]:
    source_groups = await get_user_groups_by_program_id(request=request, program_id=source_program_id)
    dest_groups = await get_user_groups_by_program_id(request=request, program_id=dest_program_id)
    name_to_dest_group = {group.name: group for group in dest_groups}

    source_to_dest_user_group_id = {}
    to_create = []
    for source_group in source_groups:
        dest_group = name_to_dest_group.get(source_group.name)
        if dest_group:
            source_to_dest_user_group_id[source_group.id] = dest_group.id
            continue
        to_create.append(
            UserGroups(
                name=source_group.name,
                display_name=source_group.display_name,
                program_id=dest_program_id,
                parent_group_id=source_group.parent_group_id,
                color_category_index=source_group.color_category_index,
                path=source_group.path,
                allow_self_assignment=source_group.allow_self_assignment,
            )
        )
    if to_create:
        created = await create.create(
            request=request, instances=to_create, orm_type=UserGroups, translate=False, type=UserGroup
        )
        if len(to_create) != len(created):
            raise Exception("User groups could not be migrated.")

        name_to_dest_group_created = {group.name: group for group in created}
        for source_group in source_groups:
            if source_to_dest_user_group_id.get(source_group.id):
                continue
            source_to_dest_user_group_id[source_group.id] = name_to_dest_group_created[source_group.name].id

        await _update_created_user_groups_parent_and_path(
            request=request, created_user_groups=created, source_to_dest_user_group_id=source_to_dest_user_group_id
        )

    return source_to_dest_user_group_id


async def _migrate_user_group_admins(
    request: Request, source_program_id: int, dest_program_id: int, source_to_dest_user_group_id: dict[int, int]
) -> None:
    source_group_admins = await get_user_group_admins_by_program_id(request=request, program_id=source_program_id)
    dest_group_admins = await get_user_group_admins_by_program_id(request=request, program_id=dest_program_id)
    group_user_to_dest_group_admins = {
        (group_admin.group_id, group_admin.user_id): group_admin for group_admin in dest_group_admins
    }

    to_create = []
    for source_group_admin in source_group_admins:
        dest_group_id = None
        if source_group_admin.group_id:
            dest_group_id = source_to_dest_user_group_id.get(source_group_admin.group_id)
            if not dest_group_id:
                continue
        dest_group_admin = group_user_to_dest_group_admins.get((dest_group_id, source_group_admin.user_id))
        if dest_group_admin:
            continue
        to_create.append(
            UserGroupsAdmins(
                program_id=dest_program_id,
                group_id=dest_group_id,
                user_id=source_group_admin.user_id,
                unrestricted_access=source_group_admin.unrestricted_access,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=UserGroupsAdmins)
        except Exception as e:
            raise Exception(f"User group admins could not be migrated: {e}")


async def _migrate_projects(request: Request, source_program_id: int, dest_program_id: int) -> dict[int, int]:
    source_projects = await get_projects_with_user_by_program_id(
        request=request, program_id=source_program_id, filter_enrolled=True
    )
    dest_projects = await get_projects_with_user_by_program_id(request=request, program_id=dest_program_id)
    user_id_to_dest_project = {project.user_id: project for project in dest_projects}

    source_to_dest_project_id = {}
    for source_project in source_projects:
        dest_project = user_id_to_dest_project.get(source_project.user_id)
        if dest_project:
            source_to_dest_project_id[source_project.id] = dest_project.id
            continue
        to_create = Projects(
            program_id=dest_program_id,
            config=source_project.config,
            send_marketing_data=source_project.send_marketing_data,
            reporting_enabled=source_project.reporting_enabled,
            show_email=source_project.show_email,
        )
        created = await create.create(
            request=request, instances=[to_create], orm_type=Projects, translate=False, type=ProjectNoProgramNoDates
        )
        if not created:
            raise Exception(f"Project {source_project.id} could not be migrated.")
        source_to_dest_project_id[source_project.id] = created[0].id

    return source_to_dest_project_id


async def _migrate_user_group_projects(
    request: Request,
    source_program_id: int,
    dest_program_id: int,
    source_to_dest_user_group_id: dict[int, int],
    source_to_dest_project_id: dict[int, int],
) -> None:
    source_group_projects = await get_user_group_projects_by_program_id(request=request, program_id=source_program_id)
    dest_group_projects = await get_user_group_projects_by_program_id(request=request, program_id=dest_program_id)
    group_project_to_dest_group_projects = {(gp.group_id, gp.project_id): gp for gp in dest_group_projects}

    to_create = []
    for source_group_project in source_group_projects:
        dest_group_id = source_to_dest_user_group_id.get(source_group_project.group_id)
        dest_project_id = source_to_dest_project_id.get(source_group_project.project_id)
        if not dest_group_id or not dest_project_id:
            continue
        dest_group_project = group_project_to_dest_group_projects.get((dest_group_id, dest_project_id))
        if dest_group_project:
            continue
        to_create.append(
            UserGroupProjects(
                group_id=dest_group_id,
                project_id=dest_project_id,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=UserGroupProjects)
        except Exception as e:
            raise Exception(f"User group projects could not be migrated: {e}")


async def _migrate_user_project_permissions(
    request: Request, source_program_id: int, dest_program_id: int, source_to_dest_project_id: dict[int, int]
) -> None:
    source_permissions = [
        ProjectPermissions(**permission)
        for permission in await get_project_permissions_by_program_id_all_users(
            request=request, program_id=source_program_id
        )
    ]
    dest_permissions = [
        ProjectPermissions(**permission)
        for permission in await get_project_permissions_by_program_id_all_users(
            request=request, program_id=dest_program_id
        )
    ]
    user_project_to_dest_permissions = {
        (permission.user, permission.project): permission for permission in dest_permissions
    }
    to_create = []
    for source_permission in source_permissions:
        dest_project_id = source_to_dest_project_id.get(source_permission.project)
        if not dest_project_id:
            continue
        dest_permission = user_project_to_dest_permissions.get((source_permission.user, dest_project_id))
        if dest_permission:
            continue
        to_create.append(
            ProjectPermissions(
                user=source_permission.user,
                project=dest_project_id,
                details=(
                    json.loads(source_permission.details) if source_permission.details else source_permission.details
                ),
                deleted_at_unix=0,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=ProjectPermissions)
        except Exception as e:
            raise Exception(f"User project permissions could not be migrated: {e}")


async def _migrate_single_project_entities(
    request: Request,
    source_project_id: int,
    dest_project_id: int,
    source_to_dest_program_custom_reg_input_id: dict[int, int],
) -> None:
    await _migrate_farms(
        request=request,
        source_project_id=source_project_id,
        dest_project_id=dest_project_id,
    )
    source_to_dest_field_id = await _migrate_fields(
        request=request,
        source_project_id=source_project_id,
        dest_project_id=dest_project_id,
    )
    await _migrate_field_baselines(
        request=request,
        source_to_dest_field_id=source_to_dest_field_id,
    )
    await _create_field_histories(request=request, dest_field_ids=list(source_to_dest_field_id.values()))
    await _migrate_project_values(
        request=request,
        source_project_id=source_project_id,
        dest_project_id=dest_project_id,
        source_to_dest_program_custom_reg_input_id=source_to_dest_program_custom_reg_input_id,
    )


async def _migrate_farms(request: Request, source_project_id: int, dest_project_id: int) -> None:
    source_farms = await get_farms(request=request, project_id=source_project_id)
    dest_farms = await get_farms(request=request, project_id=dest_project_id)
    core_id_to_dest_farm = {farm.core_farm_group_id: farm for farm in dest_farms}

    to_create = []
    for source_farm in source_farms:
        dest_farm = core_id_to_dest_farm.get(source_farm.core_farm_group_id)
        if dest_farm:
            continue
        to_create.append(
            Farms(
                parent_project_id=dest_project_id,
                core_farm_group_id=source_farm.core_farm_group_id,
                farm_name=source_farm.farm_name,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=Farms)
        except Exception as e:
            raise Exception(f"Farms could not be migrated: {e}")


async def _migrate_fields(request: Request, source_project_id: int, dest_project_id: int) -> dict[int, int]:
    source_fields = await get_fields_orm_by_project_ids(request=request, project_ids=[source_project_id])
    dest_fields = await get_fields_orm_by_project_ids(request=request, project_ids=[dest_project_id])
    md5_to_dest_field = {field.md5: field for field in dest_fields}

    source_to_dest_field_id = {}
    to_create = []
    for source_field in source_fields:
        dest_field = md5_to_dest_field.get(source_field.md5)
        if dest_field:
            source_to_dest_field_id[source_field.id] = dest_field.id
            continue
        to_create.append(
            Fields(
                parent_project_id=dest_project_id,
                fs_field_id=source_field.fs_field_id,
                kml_id=source_field.kml_id,
                farm_id=source_field.farm_id,
                md5=source_field.md5,
                area=source_field.area,
                status=FieldStatus.registered,
                core_region_id=source_field.core_region_id,
                field_lineage_id=None,
                deleted_at_unix=0,
            )
        )
    if to_create:
        created = await create.create(
            request=request, instances=to_create, orm_type=Fields, translate=False, type=Field
        )
        if len(to_create) != len(created):
            raise Exception("Fields could not be migrated.")

        md5_to_dest_field_created = {field.md5: field for field in created}
        for source_field in source_fields:
            if source_to_dest_field_id.get(source_field.id):
                continue
            source_to_dest_field_id[source_field.id] = md5_to_dest_field_created[source_field.md5].id

    return source_to_dest_field_id


async def _migrate_field_baselines(request: Request, source_to_dest_field_id: dict[int, int]) -> None:
    source_field_baselines = await get_field_baselines_by_field_ids(
        request=request, field_ids=list(source_to_dest_field_id.keys())
    )
    dest_field_baselines = await get_field_baselines_by_field_ids(
        request=request, field_ids=list(source_to_dest_field_id.values())
    )
    field_id_to_dest_field_baseline = {baseline.field_id: baseline for baseline in dest_field_baselines}

    to_create = []
    for source_baseline in source_field_baselines:
        dest_field_id = source_to_dest_field_id.get(source_baseline.field_id)
        if not dest_field_id:
            continue
        dest_baseline = field_id_to_dest_field_baseline.get(dest_field_id)
        if dest_baseline:
            continue
        to_create.append(
            FieldsBaseline(
                field_id=dest_field_id,
                baseline_year=source_baseline.baseline_year,
                is_returning=True,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(
                request=request,
                instances=to_create,
                orm_type=FieldsBaseline,
            )
        except Exception as e:
            raise Exception(f"Field baselines could not be migrated: {e}")


async def _create_field_histories(request: Request, dest_field_ids: list[int]) -> None:
    dest_fields = await get_fields_orm_by_field_ids(request=request, field_ids=dest_field_ids)
    field_id_to_dest_field_histories = await get_field_history(request=request, field_ids=dest_field_ids)

    to_create = []
    for dest_field in dest_fields:
        if field_id_to_dest_field_histories.get(dest_field.id):
            continue
        to_create.append(
            FieldsHistory(
                field_id=dest_field.id,
                fs_field_id=dest_field.fs_field_id,
                kml_id=dest_field.kml_id,
                group_id=dest_field.farm_id,
                md5=dest_field.md5,
                area=dest_field.area,
                core_region_id=dest_field.core_region_id,
                reason=FieldHistoryChangeReason.api,
                field_lineage_id=None,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(
                request=request,
                instances=to_create,
                orm_type=FieldsHistory,
            )
        except Exception as e:
            raise Exception(f"Field histories could not be created: {e}")


async def _migrate_project_values(
    request: Request,
    source_project_id: int,
    dest_project_id: int,
    source_to_dest_program_custom_reg_input_id: dict[int, int],
) -> None:
    source_to_dest_program_custom_reg_input_id_str = {
        str(key): str(value) for key, value in source_to_dest_program_custom_reg_input_id.items()
    }
    source_values = [
        value
        for value in await get_project_values(request=request, project_id=source_project_id)
        if value.type_ != "survey"
    ]
    dest_values = [
        value
        for value in await get_project_values(request=request, project_id=dest_project_id)
        if value.type_ != "survey"
    ]
    type_key_to_dest_values = {(value.type_, value.key): value for value in dest_values}

    to_create = []
    for source_value in source_values:
        dest_key = source_to_dest_program_custom_reg_input_id_str.get(source_value.key)
        if not dest_key:
            continue
        dest_value = type_key_to_dest_values.get((source_value.type_, dest_key))
        if dest_value:
            continue
        to_create.append(
            ProjectValues(
                type_=source_value.type_,
                project_id=dest_project_id,
                stage_id=None,
                key=dest_key,
                value=source_value.value,
            )
        )
    if to_create:
        try:
            await create.bulk_insert(request=request, instances=to_create, orm_type=ProjectValues)
        except Exception as e:
            raise Exception(f"Project values could not be migrated: {e}")


async def get_projects_with_user_by_program_id(
    request: Request, program_id: int, filter_enrolled: bool = False
) -> list[MigrationProjectWithUser]:
    query = (
        select(Projects, Users.id)
        .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
        .join(Users, ProjectPermissions.user == Users.id)
        .where(Projects.program_id == program_id)
        .where(Projects.status != ProjectStatus.deleted)
        .where(Projects.deleted_at.is_(None))
        .where(ProjectPermissions.deleted_at.is_(None))
        .where(Users.deleted_at.is_(None))
    )
    if filter_enrolled:
        query = query.where(Projects.status == ProjectStatus.enrolled)
    async with request.state.sql_session() as s:
        res = (await run_query(query=query, s=s)).all()
    projects_with_user = []
    for project, user_id in res:
        project_dict = project.to_dict()
        project_dict["user_id"] = user_id
        projects_with_user.append(MigrationProjectWithUser(**project_dict))
    return projects_with_user


async def _update_created_user_groups_parent_and_path(
    request: Request, created_user_groups: list[UserGroup], source_to_dest_user_group_id: dict[int, int]
) -> None:
    patch_user_group_requests = []
    for created_user_group in created_user_groups:
        updated_parent_group_id = source_to_dest_user_group_id.get(created_user_group.parent_group_id)
        path_group_ids = created_user_group.path.split("/")
        updated_path = "/".join(
            [str(source_to_dest_user_group_id.get(int(path_group_id))) for path_group_id in path_group_ids]
        )
        patch_user_group_requests.append(
            PatchUserGroupRequest(id=created_user_group.id, parent_group_id=updated_parent_group_id, path=updated_path)
        )
    await partial_update_groups(request=request, payloads=patch_user_group_requests)
