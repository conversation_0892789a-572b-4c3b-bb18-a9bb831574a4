import uuid
from datetime import timed<PERSON><PERSON>

import elasticapm
from celery import chain, group
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from migrate_single_year_entities.db import (
    create_program_migration_run,
    get_program_migration_run_by_task_id,
    update_program_migration_run,
)
from migrate_single_year_entities.enums import ProgramMigrationStatus
from migrate_single_year_entities.methods import (
    _migrate_program_custom_reg_inputs,
    _migrate_projects,
    _migrate_single_project_entities,
    _migrate_user_group_admins,
    _migrate_user_group_projects,
    _migrate_user_groups,
    _migrate_user_program_permissions,
    _migrate_user_project_permissions,
    _migrate_user_roles,
    get_projects_with_user_by_program_id,
)
from migrate_single_year_entities.schema import (
    MigrationTaskOutput,
    MigrationTaskResponse,
)
from programs.model import ProgramCustomRegInputs
from projects.model import Projects
from slack_integration.enums import SlackBlockType
from slack_integration.integration import post_message
from slack_integration.schema import SlackBlock
from user_groups.model import UserGroups

PROGRAM_CUSTOM_REG_INPUT_ENTITY_TYPE = ProgramCustomRegInputs.__name__
USER_GROUPS_ENTITY_TYPE = UserGroups.__name__
PROJECTS_ENTITY_TYPE = Projects.__name__

settings = get_settings()


def _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
    entity_type: str, previous_task_outputs: list[dict]
) -> dict:
    previous_migration_task_outputs = [MigrationTaskOutput(**output) for output in previous_task_outputs]
    source_to_dest_entity_id_mappings = [
        output.response.id_mapping
        for output in previous_migration_task_outputs
        if output.response and output.response.entity_type == entity_type
    ]
    if not source_to_dest_entity_id_mappings:
        raise Exception(f"Source to dest {entity_type} ID mapping not found.")
    return source_to_dest_entity_id_mappings[0]


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def handle_errors(
    self: DBTask,
    previous_task_outputs: dict | list[dict],
    *,
    task_id: str,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> list[dict]:
    # if there is only one previous task, then previous task outputs will be a tuple
    # instead of a list
    if isinstance(previous_task_outputs, dict):
        previous_task_outputs = [previous_task_outputs]
    for output in previous_task_outputs:
        migration_task_output = MigrationTaskOutput(**output)
        repr_exception = migration_task_output.exception
        if repr_exception:
            await update_program_migration_run(
                request=request, task_id=task_id, status=ProgramMigrationStatus.FAILED, error_message=repr_exception
            )
            await send_slack_notification(
                task_id=task_id, fs_user_id=fs_user_id, fs_impersonator_user_id=fs_impersonator_user_id, request=request
            )
            raise Exception(repr_exception)
    return previous_task_outputs


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_user_roles_and_admins_task(
    self: DBTask,
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        await _migrate_user_roles(request=request, source_program_id=source_program_id, dest_program_id=dest_program_id)
        await _migrate_user_program_permissions(
            request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
        )
        return MigrationTaskOutput(response=None, exception=None).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_user_groups_and_admins_task(
    self: DBTask,
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_user_group_id = await _migrate_user_groups(
            request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
        )
        await _migrate_user_group_admins(
            request=request,
            source_program_id=source_program_id,
            dest_program_id=dest_program_id,
            source_to_dest_user_group_id=source_to_dest_user_group_id,
        )
        return MigrationTaskOutput(
            response=MigrationTaskResponse(
                entity_type=USER_GROUPS_ENTITY_TYPE, id_mapping=source_to_dest_user_group_id
            ),
            exception=None,
        ).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_projects_task(
    self: DBTask,
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_project_id = await _migrate_projects(
            request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
        )
        return MigrationTaskOutput(
            response=MigrationTaskResponse(entity_type=PROJECTS_ENTITY_TYPE, id_mapping=source_to_dest_project_id),
            exception=None,
        ).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_program_custom_reg_inputs_task(
    self: DBTask,
    previous_task_outputs: list[dict],
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_program_custom_reg_input_id = await _migrate_program_custom_reg_inputs(
            request=request, source_program_id=source_program_id, dest_program_id=dest_program_id
        )
        return MigrationTaskOutput(
            response=MigrationTaskResponse(
                entity_type=PROGRAM_CUSTOM_REG_INPUT_ENTITY_TYPE, id_mapping=source_to_dest_program_custom_reg_input_id
            ),
            exception=None,
        ).dict()
    except Exception as e:
        return MigrationTaskOutput(reponse=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_user_group_projects_task(
    self: DBTask,
    previous_task_outputs: list[dict],
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_user_group_id = _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
            entity_type=USER_GROUPS_ENTITY_TYPE, previous_task_outputs=previous_task_outputs
        )
        source_to_dest_project_id = _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
            entity_type=PROJECTS_ENTITY_TYPE, previous_task_outputs=previous_task_outputs
        )
        await _migrate_user_group_projects(
            request=request,
            source_program_id=source_program_id,
            dest_program_id=dest_program_id,
            source_to_dest_user_group_id=source_to_dest_user_group_id,
            source_to_dest_project_id=source_to_dest_project_id,
        )
        return MigrationTaskOutput(
            response=MigrationTaskResponse(entity_type=PROJECTS_ENTITY_TYPE, id_mapping=source_to_dest_project_id),
            exception=None,
        ).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_user_project_permissions_task(
    self: DBTask,
    previous_task_outputs: list[dict],
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_project_id = _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
            entity_type=PROJECTS_ENTITY_TYPE, previous_task_outputs=previous_task_outputs
        )
        await _migrate_user_project_permissions(
            request=request,
            source_program_id=source_program_id,
            dest_program_id=dest_program_id,
            source_to_dest_project_id=source_to_dest_project_id,
        )
        return MigrationTaskOutput(
            response=MigrationTaskResponse(entity_type=PROJECTS_ENTITY_TYPE, id_mapping=source_to_dest_project_id),
            exception=None,
        ).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_single_project_entities_task(
    self: DBTask,
    previous_task_outputs: list[dict],
    *,
    source_project_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> dict:
    try:
        source_to_dest_program_custom_reg_input_id = _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
            entity_type=PROGRAM_CUSTOM_REG_INPUT_ENTITY_TYPE, previous_task_outputs=previous_task_outputs
        )
        source_to_dest_project_id = _get_source_to_dest_entity_id_mapping_from_previous_task_outputs(
            entity_type=PROJECTS_ENTITY_TYPE, previous_task_outputs=previous_task_outputs
        )
        dest_project_id = source_to_dest_project_id[source_project_id]
        await _migrate_single_project_entities(
            request=request,
            source_project_id=source_project_id,
            dest_project_id=dest_project_id,
            source_to_dest_program_custom_reg_input_id=source_to_dest_program_custom_reg_input_id,
        )
        return MigrationTaskOutput(response=None, exception=None).dict()
    except Exception as e:
        return MigrationTaskOutput(response=None, exception=repr(e)).dict()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def complete_migrate_single_year_entities_task(
    self: DBTask,
    previous_task_outputs: list[dict],
    *,
    task_id: str,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await update_program_migration_run(request=request, task_id=task_id, status=ProgramMigrationStatus.SUCCEEDED)
    await send_slack_notification(
        task_id=task_id, fs_user_id=fs_user_id, fs_impersonator_user_id=fs_impersonator_user_id, request=request
    )


async def send_slack_notification(
    task_id: str,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    migration_run = await get_program_migration_run_by_task_id(request=request, task_id=task_id)
    status_emoji = ":white_check_mark:" if migration_run.status == ProgramMigrationStatus.SUCCEEDED else ":x:"
    detail_lines = [
        f"• *Task ID:* `{task_id}`",
        f"• *Source Program ID:* `{migration_run.source_program_id}`",
        f"• *Destination Program ID:* `{migration_run.destination_program_id}`",
        f"• *User ID:* `{migration_run.user_id}`",
        f"• *Started At:* `{migration_run.created_at.isoformat()}`",
        f"• *Duration:* `{str(timedelta(seconds=migration_run.updated_at.timestamp() - migration_run.created_at.timestamp()))}`",
    ]
    if migration_run.status == ProgramMigrationStatus.FAILED:
        detail_lines.append(f"• *Error Message:* `{migration_run.error_message}`")
    slack_blocks = [
        SlackBlock(
            type=SlackBlockType.header,
            text=f":gear: Single Year Entities Migration ({migration_run.source_program_id} -> {migration_run.destination_program_id})  {status_emoji}",
        ),
        SlackBlock(type=SlackBlockType.section, text=f"*Status:* {status_emoji} `{migration_run.status}`"),
        SlackBlock(type=SlackBlockType.section, text="\n".join(detail_lines)),
    ]
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
        dict_={"fs_user_id": fs_user_id, "fs_impersonator_user_id": fs_impersonator_user_id},
        blocks=slack_blocks,
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def migrate_single_year_entities_task(
    self: DBTask,
    *,
    source_program_id: int,
    dest_program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    task_id = str(uuid.uuid4())
    await create_program_migration_run(
        request=request,
        task_id=task_id,
        source_program_id=source_program_id,
        dest_program_id=dest_program_id,
        user_id=fs_user_id,
    )
    source_projects = await get_projects_with_user_by_program_id(
        request=request, program_id=source_program_id, filter_enrolled=True
    )
    source_project_ids = [project.id for project in source_projects]
    chain(
        group(
            migrate_user_roles_and_admins_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
            migrate_user_groups_and_admins_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
            migrate_projects_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
        ),
        handle_errors.s(
            task_id=task_id, fs_user_id=fs_user_id, fs_impersonator_user_id=fs_impersonator_user_id, request=request
        ),
        group(
            migrate_program_custom_reg_inputs_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
            migrate_user_group_projects_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
            migrate_user_project_permissions_task.s(
                source_program_id=source_program_id,
                dest_program_id=dest_program_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            ),
        ),
        handle_errors.s(
            task_id=task_id, fs_user_id=fs_user_id, fs_impersonator_user_id=fs_impersonator_user_id, request=request
        ),
        group(
            migrate_single_project_entities_task.s(
                source_project_id=project_id,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            )
            for project_id in source_project_ids
        ),
        handle_errors.s(
            task_id=task_id, fs_user_id=fs_user_id, fs_impersonator_user_id=fs_impersonator_user_id, request=request
        ),
        complete_migrate_single_year_entities_task.s(
            task_id=task_id,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ).delay()
