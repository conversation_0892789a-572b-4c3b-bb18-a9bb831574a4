from datetime import datetime

from pydantic import BaseModel

from migrate_single_year_entities.enums import ProgramMigrationStatus


class MigrateSingleYearEntitiesRequest(BaseModel):
    """
    source_program_id: The program to migrate single year entities from.
    dest_program_id: The program to migrate single year entities to.
    """

    source_program_id: int
    dest_program_id: int


class MigrationProjectWithUser(BaseModel):
    """
    Contains data used to migrate a project in a single year entity migration.
    """

    id: int  # noqa
    program_id: int
    config: dict | None = None
    send_marketing_data: bool | None = None
    reporting_enabled: bool = True
    show_email: bool = True
    user_id: int


class MigrationTaskResponse(BaseModel):
    """
    entity_type: The type of the entity that was migrated.
    id_mapping: A mapping of the entity IDs in the source program to the
        entity IDs in the destination program.
    """

    entity_type: str
    id_mapping: dict[int, int]


class MigrationTaskOutput(BaseModel):
    """
    response: The response produced by the migration task.
    exception: The exception raised by the migration task.
    """

    response: MigrationTaskResponse | None
    exception: str | None


class ProgramMigrationRunResponse(BaseModel):
    id: int
    task_id: str
    source_program_id: int
    destination_program_id: int
    user_id: int
    status: ProgramMigrationStatus
    error_message: str | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class ProgramMigrationRunUpdate(BaseModel):
    status: ProgramMigrationStatus
    error_message: str | None = None

    class Config:
        orm_mode = True
