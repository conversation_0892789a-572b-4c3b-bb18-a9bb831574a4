from sqlalchemy import select
from starlette.requests import Request

from helper.helper import run_query
from migrate_single_year_entities.enums import ProgramMigrationStatus
from migrate_single_year_entities.model import ProgramMigrationRuns
from migrate_single_year_entities.schema import (
    ProgramMigrationRunResponse,
    ProgramMigrationRunUpdate,
)
from root_crud import create, get, update


async def get_latest_program_migration_run_by_dest_program_id(
    request: Request, dest_program_id: int
) -> ProgramMigrationRunResponse:
    query = (
        select(ProgramMigrationRuns)
        .where(ProgramMigrationRuns.destination_program_id == dest_program_id)
        .order_by(ProgramMigrationRuns.created_at.desc())
        .limit(1)
    )
    async with request.state.sql_session() as s:
        program_migration_runs = (await run_query(query=query, s=s)).scalars().all()
    if not program_migration_runs:
        raise Exception(f"No program migration runs exist for program {dest_program_id}.")
    return ProgramMigrationRunResponse.from_orm(program_migration_runs[0])


async def get_program_migration_run_by_task_id(request: Request, task_id: int) -> ProgramMigrationRunResponse:
    program_migration_runs = await get.generic_get(
        request=request,
        orm_type=ProgramMigrationRuns,
        type_=ProgramMigrationRunResponse,
        filters=[
            get.Filter(id_field=ProgramMigrationRuns.task_id, ids=[task_id]),
        ],
    )
    return program_migration_runs[0]


async def create_program_migration_run(
    request: Request, task_id: str, source_program_id: int, dest_program_id: int, user_id: int
) -> list[ProgramMigrationRunResponse]:
    program_migration_run = ProgramMigrationRuns(
        task_id=task_id,
        source_program_id=source_program_id,
        destination_program_id=dest_program_id,
        user_id=user_id,
        status=ProgramMigrationStatus.IN_PROGRESS,
        error_message=None,
    )
    return await create.create(
        request=request,
        instances=[program_migration_run],
        orm_type=ProgramMigrationRuns,
        translate=False,
        type=ProgramMigrationRunResponse,
    )


async def update_program_migration_run(
    request: Request, task_id: int, status: ProgramMigrationStatus, error_message: str | None = None
) -> None:
    program_migration_run = await get_program_migration_run_by_task_id(request=request, task_id=task_id)
    return await update.partial_update(
        request=request,
        id=program_migration_run.id,
        item=ProgramMigrationRunUpdate(
            status=status,
            error_message=error_message,
        ),
        orm_type=ProgramMigrationRuns,
    )
