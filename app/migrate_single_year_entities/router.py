from datetime import datetime

import elasticapm
from fastapi import APIRouter, Depends, HTTPException, Request, status

from migrate_single_year_entities import tasks
from migrate_single_year_entities.db import (
    get_latest_program_migration_run_by_dest_program_id,
)
from migrate_single_year_entities.schema import (
    MigrateSingleYearEntitiesRequest,
    ProgramMigrationRunResponse,
)
from permissions.enums import Permission
from permissions.resolver import Permissions
from phases.db import get_phase_date_window_by_type
from phases.enums import PhaseTypes

tags = ["migrate_single_year_entities", "super_admin"]
router = APIRouter()


@elasticapm.async_capture_span()
@router.post(
    "/migrate_single_year_entities",
    tags=tags,
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))],
    description="Migrate single year entities from a source program to a destination program.",
)
async def migrate_single_year_entities(request: Request, body: MigrateSingleYearEntitiesRequest) -> None:
    e_phase_date_range = await get_phase_date_window_by_type(
        request=request, program_id=body.source_program_id, phase_type=PhaseTypes.ENROLMENT
    )
    if not e_phase_date_range or e_phase_date_range.end_date > datetime.now().date():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": f"Cannot migrate single year entities for program {body.source_program_id} because E phase has not closed."
            },
        )
    tasks.migrate_single_year_entities_task.delay(
        source_program_id=body.source_program_id,
        dest_program_id=body.dest_program_id,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


@elasticapm.async_capture_span()
@router.get(
    "/migrate_single_year_entities/program/{program_id}/status",
    tags=tags,
    status_code=status.HTTP_200_OK,
    response_model=ProgramMigrationRunResponse | None,
    dependencies=[Depends(Permissions([Permission.MIGRATE_PROGRAM_DATA]))],
    description="Get the status of the migration run by destination program ID.",
)
async def get_migration_status(request: Request, program_id: int) -> ProgramMigrationRunResponse | None:
    try:
        return await get_latest_program_migration_run_by_dest_program_id(request=request, dest_program_id=program_id)
    except Exception:
        # If no program migration runs exist for the program, return None to avoid having error in UI
        return None
