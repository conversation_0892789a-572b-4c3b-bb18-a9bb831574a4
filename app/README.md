## MRV Service /app Directories:

### boundaries_service/
Boundaries Service integrations: fetching field boundaries, running field boundary checks, and fetching fertilizer data for
quantification gapfilling.

### celery_helper/
Celery is used for running offline tasks on a worker. Its configuration lives here.

### core/
Integration with the flurocore service which handles fields, farms, users, and proxies requests to MRV Service.

### cultivation_cycles/
Module for determining cultivation cycles based on field management events.

### data_service/
Integration with the Data Service for surfacing remote sensing analytics during Program Data Review.

### db/
MySQL database configuration

### defaults/
Integration with Defaults Service for retrieving crop and product characteristics. Also contains default
options for MRV data collection attributes.

### documents/
Module for tracking user views of documents such as Terms and Conditions.

### docusign/
Docusign integration for producer agreements (contracts).

### domain_event_bus/
Infrastructure and implementations for application "Domain Events". Assign handlers to events and specify on- or offline
handling logic.

### enrolments/
Program copy hack. Likely deletable.

### entity_events/
Defines MRV Events and the Events Facade which generates events from MRV Values.

### external_integrations/
Cargill ID authentication integration and Hubspot integration.

### features/
Feature flag implementation.

### fields/
Fields, field statuses, baseline years, field lineage/history, soils, mobs, field facts.
Much of this ought to be broken into modules with smaller domains.

### gcloud_client/
Client for storing blobs in GCS.

### helper/
Miscellaneous helper modules. Ideally the size of this directory will go down over time.

### http_requests/
Logic around mrv_http_requests which tracks requests to Monitor and Measure APIs. This may not be needed anymore.

### language/
Language translation module.

### locale/
Static language translation files.

### lookup/
CRUD for Enrollment Estimate Lookup Tables (LUTs).

### middleware/
Python profiler middleware.

### notifications/
Notifications allow the UI to poll for the results of offline processes, specifically Monitor Prefill
and Explore API estimates.

### paperform/
Integration with Paperform for in-app surveys, typically used during Enrollment phase.

### permissions/
Authorization implementation using roles and permissions.

### phases/
Entities and lifecycle functions for program phases (Enrollment and Monitoring).

### phases/stages/
Entities and lifecycle functions for data collection stages.

### program_codes/
Assigns codes to programs for external use.

### programs/
Entities and functions for MRV program configuration.

### projects/
Entities and functions for projects, which represent a particular user enrolling fields in an MRV program.

### projects/classes/
Deprecated Monitor Prefill code

### projects/commercials/
Commercials determine how producers are paid for participating in a project, based on field area or estimated GHG
offset.

### projects/conflicts/
Handles conflicts generated for Program Data Review, where Monitor API data contradicts M Phase data.

### projects/copy_project/
Exposes endpoints for copying a project.

### projects/csv_import/
CSV import into MRV Values.

### projects/eligibility/
Enrollment eligibility logic, including custom implementations for many programs. Determines which practice changes a
field is eligible for based on past practices.

### projects/eligibility/measurement
Measurement Eligibility, which determines whether a field is eligible for final outcomes.

### projects/entity_events/
Deprecated events classes that were used in eligibility.

### projects/farms/
Farm entities, which really belong to Core.

### projects/migration/
Deprecated module for migrating entities between program years.

### projects/monitor/
Deprecated Monitor API pre-fill for MRV Values.

### projects/practice/
Classes used by eligibility to classify practices from field events.

### projects/scripts/
Operational scripts.

### projects/templates/nutrient_management/
Unused, templates for repeating fertilizer regimens onto different crops.

### projects/user_groups/
User groups (really project groups) where an admin user has read-only or edit access to projects in the group.

### regions/
Handles the configuration and enforcement of program boundaries.

### reported_outcomes/
Obtains and persists quantification outcomes from Measure API. Outcomes can be approved in the Simulations UI.

### root_crud/
Reusable functions for create, update, and get of SQLAlchemy models against the MySQL DB.

### scenarios_service/
Contains both the Legacy Cargill Integration and the Generalized Integration. Integrates with Measure API Interventions,
Inventory, and Biofuels, as well as Explore API.

### scripts/
Operational scripts. Many deletable.

### ses_integration/
Integration with Structured Events Service, using ses_client to read and write agricultural management events.

### slack_integration/
Integration with Slack for alerting.

### static/
Static images for icons.

### templates/
HTML templates for generating static webpages.

### ui/
Contains endpoints and types that are specific to the MRV UI.

### user_groups/
User groups allow group admins to impersonate any users in the group, either read-only or write.

### users/
User Recent Programs, which populates the MRV Programs dropdown in the UI. Users are handled by Core.

### values/
Entities and functions for MRV Values, where non-event data from the data collection stages goes.
