from __future__ import annotations

import copy
import csv
import io
from collections import Counter, defaultdict, namedtuple
from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, TYPE_CHECKING

import elasticapm
from async_lru import alru_cache
from asyncache import cached
from cachetools import TTLCache
from cachetools.keys import hashkey
from dateutil import parser
from dateutil.parser import isoparse
from fastapi import Request, status, UploadFile
from fastapi.exceptions import HTTPException
from pydantic.main import BaseModel

from config import get_settings
from core import consts, schema, search_list
from core.enums import MeasurementUnits
from core.schema import PatchUserDetailRequest, UserDetailResponse
from core.tasks import create_mrv_users
from defaults.attribute_options import ApplicationRateType
from helper import external_api
from helper.async_tools import Tasks
from helper.i18n import Locale, translate_message
from logger import get_logger
from permissions.db import get_role_users_by_user_id
from permissions.enums import Permission, RoleTypes
from permissions.methods import (
    get_accessible_admin_users,
    get_accessible_producer_users,
    is_super_admin,
)
from phases.enums import AttributeTypes
from values import schema as values_schema
from values.enums import ImportDataSources, ProgressChoices

if TYPE_CHECKING:
    import httpx

    from annotations import DictStrAny

settings = get_settings()

Range = namedtuple("Range", ["start", "end"])

logger = get_logger(__name__)


# TODO move this to be computed in the db with foreign keys and onupdate()
class AttributeInstanceMeta(BaseModel):
    date_range: Range
    type: AttributeTypes
    stage_id: int
    attribute_id: int


@dataclass
class OperationValue:
    """Represents a value extracted from an operation record."""

    field_id: int
    operation_date: datetime
    attribute_type: AttributeTypes
    attribute_value: Any
    data_source: ImportDataSources
    operation_area: float = 0.0


@dataclass
class SeasonValue:
    """Represents a value extracted from a season record."""

    start_date: datetime
    end_date: datetime
    attribute_type: AttributeTypes
    attribute_value: Any
    data_source: ImportDataSources


@elasticapm.capture_span()
def calculate_date_overlap(r1: Range, r2: Range) -> int:
    latest_start = max(r1.start.date(), r2.start.date())
    earliest_end = min(r1.end.date(), r2.end.date())
    delta = (earliest_end - latest_start).days
    return max(0, delta)


@elasticapm.capture_span()
def date_in_range(date: datetime, range_: Range) -> bool:
    return bool(range_.start.date() <= date.date() <= range_.end.date())


@elasticapm.capture_span()
def maximally_overlapping_attribute(
    att_meta_list: list[AttributeInstanceMeta], date_range: Range
) -> AttributeInstanceMeta | None:
    """Returns the AttributeInstanceMeta value that overlaps most with the specified date range.

    TODO: Fails if range is same day
    """
    tmp_list = []
    for att_meta in att_meta_list:
        date_overlap = calculate_date_overlap(
            date_range,
            Range(start=att_meta.date_range.start, end=att_meta.date_range.end),
        )
        if date_overlap > 0:
            tmp_list.append((date_overlap, att_meta))

    logger.debug("full overlap list: %s", tmp_list)
    if len(tmp_list) == 0:
        return None
    return max(tmp_list, key=lambda i: i[0])[1]


@elasticapm.capture_span()
def find_type(l1: list[AttributeInstanceMeta], input_types: list[AttributeTypes]) -> list[AttributeInstanceMeta]:
    """Returns a list of AttributeInstanceMeta values whose .type field appears on the list of input types."""
    return [i for i in l1 if i.type in input_types]


def prepare_fs_field_id_map(response: httpx.Response, record_ids: set[int]) -> dict[int, list[DictStrAny]]:
    ret = defaultdict(list)
    for item in response.json()["result"]:
        if item["id"] in record_ids:
            fs_field_id = item.get("fieldID") or item.get("field_id")
            if fs_field_id is None:
                logger.error(
                    "No `fieldID` and `field_id` in core response `result` item." " url: %s",
                    response.url,
                )
            else:
                ret[fs_field_id].append(item)
    return ret


def calculate_monitoring_date_range(attributes: list[AttributeInstanceMeta]) -> Range:
    min_date = min(x.date_range.start for x in attributes)
    max_date = max(x.date_range.end for x in attributes)
    logger.debug("Looking for OPERATIONS or SEASONS in range %s - %s", min_date, max_date)
    return Range(start=min_date, end=max_date)


@elasticapm.async_capture_span()
async def get_season_data(
    seasons: list[DictStrAny],
    monitoring_date_range: Range,
    mrv_field_id: int,
    monitored_attributes: list[AttributeInstanceMeta],
    progress_choice: ProgressChoices,
    stage_ids_to_crop_list: dict[int, list[str]] | None,
) -> list[values_schema.ValuesRequestAttributeFieldOrEntity]:
    """
    Extracts and stores relevant values that are relevant to the monitoring stages.
    At this stage only crop type is extracted from season records.


    Parameters
    ----------
    seasons : list[DictStrAny]
        aggregated data for mrv_field_id from the core response.
    monitoring_date_range: Range
        range of min and max date taken from all attributes
    mrv_field_id :
        Field id in the MRV database.
    monitored_attributes : list
        A list of AttributeInstanceMeta values, each of which represents an attribute
        that is being measured / monitored in a particular program stage.
    progress_choice : str
        The monitoring (progress) stage- eg 'enrolment', 'monitoring'
    stage_ids_to_crop_list: dict[int, list[str]]
        Optional dict of stage id -> crop list for skipping crops that are not configured for a stage.
    """

    # Ensure only a single crop type per season year and order by season end date, descending.
    seasons = distinct_ordered_seasons(seasons)

    returning_attributes = []
    row_id = 0
    for season in seasons:
        # sanity check - to create values we need these fields
        ensure_fields = {"id", "cropType", "startDate", "endDate"}
        if missed_keys := ensure_fields.difference(season.keys()):
            logger.error("operation record missing keys: %s", " ,".join(missed_keys))
            continue

        # Skip if BOTH start and end are before the start of the range,
        # or BOTH start and end are AFTER the end of the range.
        # It's ok for the monitoring range to straddle the season date
        # range, or vice-versa - as long as there is some overlap.
        season_start = parser.parse(season["startDate"])
        season_end = parser.parse(season["endDate"])
        season_range = Range(start=season_start, end=season_end)
        if calculate_date_overlap(season_range, monitoring_date_range) < 1:
            continue

        # Returns a list of 2 attribute values, the first of which is always the record_year attribute.
        # This is required to ensure we can assign the right row id.
        sn_vals = attribute_values_from_season_record(season)
        if not sn_vals or len(sn_vals) != 2:
            logger.debug("failed to derive values from season record, skipping...")
            continue

        # Record year can occur in multiple stages, so we'll take it as it is for now
        record_year_val = sn_vals[0]

        crop_type_val, crop_type_in_stage = sn_value_in_stage(sn_vals[1], monitored_attributes, stage_ids_to_crop_list)

        # find the record_year attribute from the same stage as the crop type
        record_year_attribute_id = 0
        for att in monitored_attributes:
            if (
                att is not None
                and crop_type_in_stage is not None
                and att.type == AttributeTypes.record_year
                and att.stage_id == crop_type_in_stage.stage_id
            ):
                record_year_attribute_id = att.attribute_id

        if crop_type_in_stage:
            if record_year_attribute_id:
                returning_attributes.append(
                    values_schema.ValuesRequestAttributeFieldOrEntity(
                        value=record_year_val.attribute_value,
                        locked=False,
                        confirmed=True,
                        progress=progress_choice,
                        attribute_id=record_year_attribute_id,
                        field_id=mrv_field_id,
                        row_id=row_id,
                        source=record_year_val.data_source,
                    )
                )
            returning_attributes.append(
                values_schema.ValuesRequestAttributeFieldOrEntity(
                    value=crop_type_val.attribute_value,
                    locked=False,
                    confirmed=True,
                    progress=progress_choice,
                    attribute_id=crop_type_in_stage.attribute_id,
                    field_id=mrv_field_id,
                    row_id=row_id,
                    source=crop_type_val.data_source,
                )
            )
            row_id += 1

    # Winter crop commitment
    wcc_vals = inferred_winter_crop_commitment(returning_attributes, monitored_attributes)
    returning_attributes.extend(wcc_vals)

    return returning_attributes


def distinct_ordered_seasons(seasons: list[DictStrAny]) -> list[DictStrAny]:
    """Clean up the raw season records (from core) to make processing easier:
    - If there are multiple crop types for a year, keep the one with the highest count
    - Ensure only one crop per calendar year (by season end date)
    - drop 'unknown' or any other anomalies
    - ensure order by date desc
    """

    # map of seasons years to one (or more) season records
    year_seasons: dict[int, list[DictStrAny]] = {}
    for season in seasons:
        ensure_fields = {"cropType", "startDate", "endDate"}
        if ensure_fields.difference(season.keys()) or not season["cropType"] or season["cropType"] == "unknown":
            continue
        yr = parser.parse(season["endDate"]).year
        if yr:
            if yr not in year_seasons.keys():
                year_seasons[yr] = []
            year_seasons[yr].append(season)

    # Within each year's list of crops there can be more than one crop type. We'll keep the type that appears most
    # often, or the first one in the list in the case it's a tie.
    single_crop_seasons = {}
    for year, seasons in year_seasons.items():
        crop_count: dict[str, int] = {}
        for seas in seasons:
            crop_count[seas["cropType"]] = crop_count.get(seas["cropType"], 0) + 1
        count = 0
        crop = ""
        for key, cur_count in crop_count.items():
            if cur_count > count:
                count = cur_count
                crop = key
        # crop = max(crop_count, key=crop_count.get) # mypy complains
        single_crop_seasons[year] = [s for s in seasons if s["cropType"] == crop]

    # Sort in descending year order and return the first one in each set
    return [i[1][0] for i in sorted(single_crop_seasons.items(), reverse=True)]


def attribute_values_from_season_record(
    season_record: dict,
) -> list[SeasonValue]:
    """Extracts the crop_type and year from a season record."""

    logger.debug("Extracting values from season record: %s", season_record)

    if (
        season_record is None
        or not season_record
        or "cropType" not in season_record
        or "startDate" not in season_record
        or "endDate" not in season_record
    ):
        logger.warning("empty or incomplete season record")
        return []

    values: list[SeasonValue] = []
    start_date = isoparse(season_record["startDate"])
    end_date = isoparse(season_record["endDate"])

    def new_seas_val(start: datetime, end: datetime, att: AttributeTypes, att_val: Any) -> SeasonValue:
        return SeasonValue(
            start_date=start,
            end_date=end,
            attribute_type=att,
            attribute_value=att_val,
            data_source=season_record.get("external_service", ImportDataSources.other_fms.value),
        )

    # crop type - not interested if it's empty or "unknown"
    if not season_record["cropType"] or season_record["cropType"].lower() == "unknown":
        return values

    # Ensure record_year is the first in the list!
    values.extend(
        [
            new_seas_val(start_date, end_date, AttributeTypes.record_year, end_date.year),
            new_seas_val(
                start_date,
                end_date,
                AttributeTypes.crop_type,
                season_record["cropType"],
            ),
        ]
    )
    return values


@elasticapm.async_capture_span()
async def get_operation_data(
    operations: list[DictStrAny],
    monitoring_date_range: Range,
    mrv_field_id: int,
    monitored_attributes: list[AttributeInstanceMeta],
    progress_choice: ProgressChoices,
    aggregate_values: bool = True,
    product_skip_list: str | list[str] | None = None,
) -> list[values_schema.ValuesRequestAttributeFieldOrEntity]:
    """
    Looks through list of operations for data that can be matches against monitored_attributes. That is, looks for
    things we need in the operations. Matches are then wrangled into a list of attribute values and returned.

    Parameters
    ----------
    operations : list[DictStrAny]
        aggregated data for mrv_field_id from the core response.
    monitoring_date_range: Range
        range of min and max date taken from all attributes
    mrv_field_id :
        Field id in the MRV database.
    monitored_attributes : list
        A list of AttributeInstanceMeta values, each of which represents an attribute
        that is being measured / monitored in a particular program stage.
    progress_choice : str
        The monitoring (progress) stage- eg 'enrolment', 'monitoring'
    aggregate_values : int, optional, default = 0 (off)
        Combines the same type of field operations into a single, representative value if they
         occur within the specified number of days.
    product_skip_list: str or list of string, optional
        A list of product names to skip when importing. Can be a single string list with one product
        per newline, or a list of product name strings.
    """

    # This returns the operations, ordered by event date descending.
    # This should help to assign row ids correctly.
    operations = ordered_operations(operations)

    op_val_map = {}
    for operation in operations:
        # sanity check - to create values we need these fields
        ensure_fields = {"id", "type", "event_date", "attributes"}
        if missed_keys := ensure_fields.difference(operation.keys()):
            logger.error("operation record missing keys: %s", " ,".join(missed_keys))

        # skip if outside date range
        event_date = parser.parse(operation["event_date"])
        if not date_in_range(event_date, monitoring_date_range):
            continue

        new_ops = attribute_values_from_operation_record(
            operation,
            product_skip_list=product_skip_list,
        )

        # Ensure derived values and things we're monitoring and that we don't get spurious record_year values.
        if valid_operations_set(new_ops, monitored_attributes):
            op_val_map[operation["id"]] = new_ops

    #  Keep ops that are monitored in a stage and store in a dict keyed with attribute_id.
    #  Note that attribute_id is unique per stage so this is like grouping by stage, attribute
    attribute_ops: dict[int, list[OperationValue]] = {}
    for op_vals in op_val_map.values():
        # stage id this operation has been mapped to
        stage_id = None
        # values in operation
        for op_val in op_vals:
            if op_val.attribute_type == AttributeTypes.record_year:
                continue
            op, in_stage = op_value_in_stage(op_val, monitored_attributes)
            if in_stage and in_stage.attribute_id:
                stage_id = in_stage.stage_id
                att_id = in_stage.attribute_id
                op_list = attribute_ops.get(att_id, [])
                op_list.append(op)
                attribute_ops[att_id] = op_list
        # record years need to be match into the same stage with other values
        for op_val in op_vals:
            if op_val.attribute_type != AttributeTypes.record_year:
                continue
            op, in_stage = op_value_in_stage(op_val, [ma for ma in monitored_attributes if ma.stage_id == stage_id])
            if in_stage and in_stage.attribute_id:
                att_id = in_stage.attribute_id
                op_list = attribute_ops.get(att_id, [])
                op_list.append(op)
                attribute_ops[att_id] = op_list

    # Aggregate certain attribute values into a single, representative value.
    if aggregate_values:
        attribute_ops = aggregate_operation_values(attribute_ops)

    # Map to response values
    returning_attributes = []
    for att_id, ops in attribute_ops.items():
        row_id = 0
        for op in ops:
            returning_attributes.append(
                values_schema.ValuesRequestAttributeFieldOrEntity(
                    value=op.attribute_value,
                    locked=False,
                    confirmed=True,
                    progress=progress_choice,
                    attribute_id=att_id,
                    field_id=mrv_field_id,
                    row_id=row_id,
                    source=op.data_source,
                )
            )
            row_id += 1

    # Tillage practices
    till_prac_vals = inferred_tillage_practice_values(returning_attributes, monitored_attributes)
    returning_attributes.extend(till_prac_vals)

    return returning_attributes


def ordered_operations(operations: list[DictStrAny]) -> list[DictStrAny]:
    """Returns the operations in order by descending event date."""
    operations.sort(key=lambda x: x["event_date"], reverse=True)
    return operations


def valid_operations_set(ops: list[OperationValue], monitored_attributes: list[AttributeInstanceMeta]) -> bool:
    """Returns True if the ops list is a 'valid' set of operations, the definition of 'valid' is pretty loose, for now:
    If the ops list contains a value of type record_year, then at least one other type in the list of ops values
    should exist alongside record_year in one of the stages in the monitored_attributes list.

    For example: ops was derived from a core tillage operation so should have record_year, tillage_date, tillage_depth.
    In order for this set to be valid one of the stages in the monitored_attributes must contain at least:
       - record_year + tillage_date, or
       - record_year + tillage_depth

    The main reason this is here is to prevent the capture of operation values for record_year that have no other
    data. This is necessary because of the current shape of the mrv_values table - ie operations are split into
    rows that each contain one component of the operation data. A row with record_year alone is useless.
    """

    def _strip_prefix(att_type: AttributeTypes) -> str:
        prefixes = ["summer_", "winter_", "fall_", "spring_"]
        for pf in prefixes:
            if pf in att_type:
                return att_type.lstrip(pf)
        return att_type

    # list of operation types
    op_types = [_strip_prefix(i.attribute_type) for i in ops if i is not None]
    monitored_op_types = [_strip_prefix(i.type) for i in monitored_attributes if i is not None]

    # If record_year is NOT amongst the op values or monitored values then nothing to do
    if AttributeTypes.record_year not in op_types or AttributeTypes.record_year not in monitored_op_types:
        return True

    # Otherwise, ensure that this op includes something else with record year within the same stage

    # list of attribute types monitored in each stage
    stage_attributes = {
        k.stage_id: [_strip_prefix(i.type) for i in monitored_attributes if i.stage_id == k.stage_id]
        for k in monitored_attributes
    }

    for _stage_id, attribute_types in stage_attributes.items():
        for op_type in op_types:
            # First time we see record year with something else in the same stage, we're happy
            if AttributeTypes.record_year in attribute_types and (
                op_type != AttributeTypes.record_year and op_type in attribute_types
            ):
                return True

    return False


def attribute_values_from_operation_record(
    operation_record: dict,
    to_metric: bool = True,
    product_skip_list: str | list[str] | None = None,
) -> list[OperationValue]:
    """Extracts attribute values from the operation record. For some operations there may be more
    than one viable value, so it returns a list of OperationValue.

    Parameters
    ----------
    operation_record : dict
        Operation data retrieved from core
    to_metric : bool (default True)
        If True convert imperial rates to metric, if False convert metric rates to imperial
    product_skip_list: str or list of string, optional
        A list of product names to skip when importing. Can be a single string list with one product
        per newline, or a list of product name strings.
    """
    if (
        operation_record is None
        or not operation_record
        or "field_id" not in operation_record
        or "type" not in operation_record
        or "attributes" not in operation_record
        or "event_date" not in operation_record
    ):
        logger.warning("empty or incomplete operation record")
        return []

    values = []
    dt = isoparse(operation_record["event_date"])
    event_date = dt.strftime("%Y-%m-%d")
    event_year = dt.strftime("%Y")

    # helper func to reduce code clutter
    def new_op_val(area: float | None, att: AttributeTypes, att_val: Any) -> OperationValue:
        return OperationValue(
            field_id=operation_record["field_id"],
            operation_date=dt,
            operation_area=area or 0.0,
            attribute_type=att,
            attribute_value=att_val,
            data_source=operation_record.get("external_service", ImportDataSources.other_fms.value),
        )

    # Tillage operation - depth in cm and a date
    if operation_record["type"] == "tillage":
        till_depth = None
        op_area = 0.0

        if "depth_target" in operation_record["attributes"]:
            if "depth_avg" in operation_record["attributes"]["depth_target"]:
                till_depth = operation_record["attributes"]["depth_target"]["depth_avg"]
                op_area = operation_record["attributes"]["depth_target"]["area"]

        # prefer depth_result
        if "depth_result" in operation_record["attributes"]:
            if (
                "depth_avg" in operation_record["attributes"]["depth_result"]
                and operation_record["attributes"]["depth_result"]["depth_avg"] > 0
            ):
                till_depth = operation_record["attributes"]["depth_result"]["depth_avg"]
                op_area = operation_record["attributes"]["depth_result"]["area"]

        if till_depth is not None and till_depth > 0:
            if "area" in operation_record["attributes"]:
                op_area = operation_record["attributes"]["area"]

            values.extend(
                [
                    new_op_val(op_area, AttributeTypes.record_year, event_year),
                    new_op_val(op_area, AttributeTypes.tillage_date, event_date),
                    new_op_val(op_area, AttributeTypes.tillage_depth, till_depth),
                ]
            )

    # Planting Operation
    elif operation_record["type"] == "seeding":
        values.extend(
            [
                new_op_val(None, AttributeTypes.record_year, event_year),
                new_op_val(None, AttributeTypes.planting_date, event_date),
            ]
        )

    # Harvest Operation. We don't know anything about summer / winter here,
    # so set 'crop_yield' and let the caller do that work.
    elif operation_record["type"] == "yield":
        dry_volume_avg = None
        op_area = 0.0

        if "dry_volume_avg" in operation_record["attributes"]:
            dry_volume_avg = operation_record["attributes"]["dry_volume_avg"]

        if dry_volume_avg is not None:
            if "area" in operation_record["attributes"]:
                op_area = operation_record["attributes"]["area"]

            values.extend(
                [
                    new_op_val(op_area, AttributeTypes.record_year, event_year),
                    new_op_val(op_area, AttributeTypes.harvest_date, event_date),
                    new_op_val(op_area, AttributeTypes.crop_yield, dry_volume_avg),
                ]
            )

    # Application Operation has a date, area and one or more products applied at a specified rate.
    elif (
        operation_record["type"] == "application"
        and "area_total" in operation_record["attributes"]
        and "products" in operation_record["attributes"]
        and isinstance(operation_record["attributes"]["products"], list)
    ):
        op_area = operation_record["attributes"]["area_total"]

        # For skipping non-fertiliser products
        product_skip = None
        if product_skip_list is not None:
            product_skip = search_list.SearchList(product_skip_list)

        for product in operation_record["attributes"]["products"]:
            # Need all of these to make sense of the product and application rate
            if "product_name" not in product or "rate_unit" not in product or "rate_avg" not in product:
                continue

            name = product["product_name"]
            if product_skip is not None and product_skip.match(name):
                continue

            unit = product["rate_unit"]
            rate = 0.0
            try:
                rate = convert_product_rate(product, to_metric=to_metric)
            except Exception as e:
                logger.error("could not convert product rate: %s", e)

            values.extend(
                [
                    new_op_val(op_area, AttributeTypes.record_year, event_year),
                    new_op_val(op_area, AttributeTypes.application_date, event_date),
                    new_op_val(op_area, AttributeTypes.application_area, op_area),
                    new_op_val(op_area, AttributeTypes.application_product, name),
                    new_op_val(op_area, AttributeTypes.application_rate, rate),
                    new_op_val(op_area, AttributeTypes.application_rate_type, ApplicationRateType.PRODUCT_RATE),
                    new_op_val(op_area, AttributeTypes.application_rate_unit, unit),
                ]
            )
    return values


def aggregate_operation_values(attr_op_vals: dict[int, list[OperationValue]]) -> dict[int, list[OperationValue]]:
    """Groups related operation values and attempts to unify them into a single record that
    represent the group. Note, all of these operations should be for a single field.

    Multiple planting operations:
     - planting date is the date where the highest area of the field was planted
     - the operational area is the sum of all operational areas

    Multiple harvest operations:
    - harvest date is the date of the latest harvest operation (not the highest yield, yet)
    - harvest yield is the average of all yield values in the group
    - the operational area is the sum of all operational areas and is updated for both
       the new (aggregated) date value and the depth value.

    att_ops is a dict keyed by attribute id - each value is a list of ops belonging to that attribute.
    Dates (eg harvest date) and numerical values (eg dry yield) are stored as separate values.
    So, there's a lot of wrangling here to ensure the correct values are aggregated.

    It is quite possible to have (for example) both spring_tillage_depth and fall_tillage_depth
    in the same set of operations. So each scenario is accounted for individually.

    If anything is not right the records should be returned unchanged.
    """

    summer_planting_date_attribute_id = None
    winter_planting_date_attribute_id = None
    planting_date_attribute_id = None
    summer_harvest_date_attribute_id = None
    summer_dry_yield_attribute_id = None
    winter_harvest_date_attribute_id = None
    winter_dry_yield_attribute_id = None
    harvest_date_attribute_id = None
    crop_yield_attribute_id = None

    for att_id, ops in attr_op_vals.items():
        # empty list
        if len(ops) == 0:
            continue

        # These are sets keyed by attribute id, so the first one will do to give us the type
        att_type = ops[0].attribute_type

        if att_type == AttributeTypes.summer_planting_date:
            summer_planting_date_attribute_id = att_id

        if att_type == AttributeTypes.winter_planting_date:
            winter_planting_date_attribute_id = att_id

        if att_type == AttributeTypes.planting_date:
            planting_date_attribute_id = att_id

        if att_type == AttributeTypes.summer_dry_yield:
            summer_dry_yield_attribute_id = att_id
            summer_harvest_date_attribute_id = corresponding_date_attribute_id(att_type, attr_op_vals)

        if att_type == AttributeTypes.winter_dry_yield:
            winter_dry_yield_attribute_id = att_id
            winter_harvest_date_attribute_id = corresponding_date_attribute_id(att_type, attr_op_vals)

        if att_type == AttributeTypes.crop_yield:
            crop_yield_attribute_id = att_id
            harvest_date_attribute_id = corresponding_date_attribute_id(att_type, attr_op_vals)

    # Planting dates have no numerical counterpart so, for a set of plating date ops,
    # pick the best one and replace the set with that best value.
    date_ids = [
        summer_planting_date_attribute_id,
        winter_planting_date_attribute_id,
        planting_date_attribute_id,
    ]
    for date_id in date_ids:
        if date_id is not None:
            date_vals = attr_op_vals.get(date_id)
            if date_vals and len(date_vals) > 1:
                op_val = op_val_largest_area_or_latest(date_vals)
                if op_val is not None:
                    attr_op_vals[date_id] = [op_val]

    # The rest are aggregated as pairs
    yield_date_pairs = [
        (summer_dry_yield_attribute_id, summer_harvest_date_attribute_id),
        (winter_dry_yield_attribute_id, winter_harvest_date_attribute_id),
        (crop_yield_attribute_id, harvest_date_attribute_id),
    ]

    for num_id, date_id in yield_date_pairs:
        if num_id is not None and date_id is not None:
            # extract operation values for each pair of related (numerical and date) attributes
            num_vals = attr_op_vals.get(num_id)
            date_vals = attr_op_vals.get(date_id)

            # if both sets are found, and are equal in length...
            if date_vals and num_vals and len(date_vals) > 1 and len(date_vals) == len(num_vals):
                # ... then fetch the best operation date value (largest area or latest date)
                # This will be the date for our new aggregated value.
                op_val = op_val_largest_area_or_latest(date_vals)
                if op_val is not None:
                    # we're replacing a possible list of operation dates and values at the keys
                    # num_id and date_id with a single value for each.
                    op_date = op_val.operation_date
                    # Now we create a new aggregated operation value (from all the operational values) with the new date
                    agg_val = aggregate_ops_weighted_average(num_vals, op_date=op_date)
                    # Finally, we can replace the original operation values lists with new lists of single values
                    attr_op_vals[date_id] = [op_val]
                    attr_op_vals[num_id] = [agg_val]

    return attr_op_vals


def op_val_largest_area_or_latest(
    values_: list[OperationValue],
) -> OperationValue | None:
    """Returns the value, from the list, that has the largest operation_area. If all operation area
    are equal, return the op with the latest date."""
    if values_ is None or len(values_) == 0:
        return None

    # Try areas, if all the same then we'll look at dates
    if sum([v.operation_area for v in values_ if v.operation_area is not None]) > 0:
        areas = [v.operation_area for v in values_]
        if areas.count(areas[0]) != len(areas):  # not all same
            return sorted(values_, key=lambda x: x.operation_area, reverse=True)[0]

    # Otherwise latest op date
    return sorted(values_, key=lambda x: x.operation_date, reverse=True)[0]


def corresponding_date_attribute_id(
    numerical_type: AttributeTypes, attr_op_vals: dict[int, list[OperationValue]]
) -> int | None:
    """Checks attr_op_vals for an appropriate date type for the specified numerical type.

    For example, if numerical_type is summer_dry_yield the appropriate date type would be summer_harvest_date.
    If a set of values with this date type is found, the key (attribute_id) will be returned.
    """

    date_types = {
        AttributeTypes.spring_tillage_depth: AttributeTypes.spring_tillage_date,
        AttributeTypes.fall_tillage_depth: AttributeTypes.fall_tillage_date,
        AttributeTypes.tillage_depth: AttributeTypes.tillage_date,
        AttributeTypes.summer_dry_yield: AttributeTypes.summer_harvest_date,
        AttributeTypes.winter_dry_yield: AttributeTypes.winter_harvest_date,
        AttributeTypes.crop_yield: AttributeTypes.harvest_date,
    }

    if numerical_type not in date_types.keys():
        raise ValueError("numerical type does not have a corresponding date type")

    seeking_type = date_types[numerical_type]
    for att_id, ops in attr_op_vals.items():
        if len(ops) > 0:
            if ops[0].attribute_type == seeking_type:
                return att_id
    return None


def aggregate_ops_weighted_average(in_vals: list[OperationValue], op_date: datetime | None) -> OperationValue:
    """Returns a single operation value from the list of operation values.

    The supplied list must contain operations that are:
     - of the same type
     - all containing numerical values

    The numerical value will be set to an average weighted by the operation_area of each operation.

    If the op_date is not specified then the latest date in the list will be used.
    """

    # All must be same type
    types_ = [i.attribute_type for i in in_vals]
    if types_.count(types_[0]) != len(types_):
        raise ValueError("all operation objects must have the same attribute type")

    # All values must be numbers
    values_ = [i.attribute_value for i in in_vals]
    for value_ in values_:
        if not isinstance(value_, float) and not isinstance(value_, int):
            raise ValueError("all operation objects must have values that are int or float")

    # If op_date is not supplied, take the max date
    if op_date is None:
        op_date = max([i.operation_date for i in in_vals])

    # weighted average
    areas = [i.operation_area for i in in_vals]
    wt_avg = weighted_average(values_, areas)

    return OperationValue(
        operation_date=op_date,
        operation_area=sum(areas),
        field_id=in_vals[0].field_id,
        attribute_value=wt_avg,
        attribute_type=in_vals[0].attribute_type,
        data_source=in_vals[0].data_source,
    )


@elasticapm.async_capture_span()
async def get_fields_stats(map_: dict) -> dict[str, schema.FieldsStats]:
    if len(map_) > 0:
        logger.debug("getting info from core")
        response = await external_api.core_field_stats_get_api(map_)

        logger.debug(response.url)
        logger.debug(response.status_code)
        logger.debug(response.text)
        logger.debug(response.json())
        results = response.json()["result"]
        if response.status_code == status.HTTP_200_OK:
            ret = {}
            for i in results.keys():
                if i == "_deleted":
                    continue
                ret[i] = schema.FieldsStats(**results[i])
            return ret
        else:
            return {i: schema.FieldsStats(area_ha=0, num_fields=0, num_projects=0) for i in results.keys()}
    else:
        return {}


def user_date_format() -> str:
    return "%b %-d, %Y"


@elasticapm.async_capture_span()
async def get_user_info(user_id: int, allow_missing_user: bool = False) -> schema.User | None:
    """
    If allow_missing_user is True and the user is missing, then this function will return None. Otherwise
    it has to return a user, or raise an exception on any error
    """
    if not user_id:
        return None

    if allow_missing_user:
        response = await external_api.core_user_get_api_allow_error(user_id)
    else:
        response = await external_api.core_user_get_api(user_id)
    if response.status_code != status.HTTP_200_OK:
        # This is a hacky workaround. Currently, a user can be soft-deleted from core but still leave dangling
        # references to it in mrv. In this case, core returns HTTP_401_UNAUTHORIZED (it should probably return 404)
        if not allow_missing_user or response.status_code != status.HTTP_401_UNAUTHORIZED:
            # If we receive any error other than HTTP_401_UNAUTHORIZED, then assume something else went wrong
            # and raise 500
            logger.error(response.text)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "External API call failure"},
            )
        return None

    user = response.json()["result"]["user"]
    # date time conversion from YYYY-mm-ddTHH:MM:SSZ to M d, YYYY
    last_login_parsed = (
        datetime.strptime(user["last_login_time"], "%Y-%m-%dT%H:%M:%SZ") if "last_login_time" in user else None
    )

    # for some reason a zero date gets set to Jan 1, 1 so we need to ignore these cases
    last_login_formatted = (
        last_login_parsed.strftime(user_date_format()) if last_login_parsed and last_login_parsed.year > 1969 else None
    )

    lang_locale = Locale.en_US.value
    if locale := user["settings"]["langLocale"]:
        if locale in Locale.__members__.values():
            lang_locale = locale
        else:
            logger.error("Invalid lang_locale %s for user %s", locale, user_id)

    return schema.User(
        _user_id=user_id,
        first_name=user["name"],
        last_name=user["surname"],
        email=user["email"],
        last_login_time=last_login_formatted,
        lang_locale=lang_locale,
    )


@elasticapm.async_capture_span()
async def get_user_info_batch(user_ids: list[int], allow_missing_user: bool = False) -> list[schema.User]:
    """
    This function will return a list of users for the given user_ids. If a user is missing, it will not be returned.
    """
    if not user_ids:
        return []

    response = await external_api.core_admin_batch_get_users(user_ids)
    if response.status_code != status.HTTP_200_OK:
        if not allow_missing_user:
            logger.error(response.text)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "External API call failure"},
            )
        return []

    result = response.json()["result"] or []

    users: list[schema.User] = []
    for user in result:
        # date time conversion from YYYY-mm-ddTHH:MM:SSZ to M d, YYYY
        last_login_parsed = (
            datetime.strptime(user["last_login_time"], "%Y-%m-%dT%H:%M:%SZ") if "last_login_time" in user else None
        )

        # for some reason a zero date gets set to Jan 1, 1 so we need to ignore these cases
        last_login_formatted = (
            last_login_parsed.strftime(user_date_format())
            if last_login_parsed and last_login_parsed.year > 1969
            else None
        )

        lang_locale = Locale.en_US.value
        if locale := user["settings"]["langLocale"]:
            if locale in Locale.__members__.values():
                lang_locale = locale
            else:
                logger.error("Invalid lang_locale %s for user %s", locale, user["id"])

        users.append(
            schema.User(
                _user_id=user["id"],
                first_name=user["name"],
                last_name=user["surname"],
                email=user["email"],
                last_login_time=last_login_formatted,
                lang_locale=lang_locale,
            )
        )

    if not allow_missing_user:
        # check for missing users
        missing_user_ids = set(user_ids) - {user._user_id for user in users}
        if missing_user_ids:
            logger.error("Missing users: %s", missing_user_ids)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "External API call failure"},
            )
    return users


@elasticapm.async_capture_span()
async def get_user_latest_syncs(user_ids: list[int]) -> list[dict[int, str]] | None:
    response = await external_api.core_users_get_latest_syncs(user_ids)
    logger.debug(
        "response: %s %s %s %s",
        response.url,
        response.status_code,
        response.text,
        response.headers,
    )
    if response.status_code == status.HTTP_200_OK:
        try:
            data = response.json()["result"]
        except KeyError:
            return None
        # format dates for front end
        for user_id in data:
            # date time conversion from YYYY-mm-ddTHH:MM:SSZ to M d, YYYY
            last_login_parsed = (
                datetime.strptime(data[user_id]["last_login_time"], "%Y-%m-%dT%H:%M:%SZ")
                if data[user_id].get("last_login_time")
                else None
            )
            # for some reason a zero date gets set to Jan 1, 1 so we need to ignore these cases
            data[user_id]["last_login_time"] = (
                last_login_parsed.strftime(user_date_format())
                if last_login_parsed and last_login_parsed.year > 1969
                else None
            )

            if data[user_id]["latest_syncs"]:
                for sync in data[user_id]["latest_syncs"]:
                    sync["latest_import"] = (
                        datetime.strptime(sync["latest_import"], "%Y-%m-%dT%H:%M:%SZ").strftime(user_date_format())
                        if "latest_import" in sync and sync["latest_import"]
                        else None
                    )
        return data
    return None


@elasticapm.async_capture_span()
async def get_fields_regions(field_ids: list[int]) -> schema.Regions | None:
    regions = await external_api.core_field_regions_get_api(field_ids)
    logger.debug("regions: %s,%s,%s", regions.url, regions.status_code, regions.text)
    if regions.status_code == status.HTTP_200_OK:
        region = Counter(
            [
                (i["name"], i["country_name"])
                for i in regions.json()["result"]
                if i.get("name") and i.get("country_name")
            ]
        ).most_common(1)
        if not region:
            return None
        reg = region[0][0]
        return schema.Regions(region=reg[0], country=reg[1])
    return None


@elasticapm.capture_span()
def op_value_in_stage(
    op_val: OperationValue, monitored_attributes: list[AttributeInstanceMeta]
) -> tuple[OperationValue, AttributeInstanceMeta | None]:
    """Checks if the specified operation value can be associated with any of the stages.
    If so, it returns the operation value with the associated correct stage id."""

    op = copy.deepcopy(op_val)

    # key is the more generic attribute and values are the possible types that are more specific
    refined_attributes = {
        AttributeTypes.tillage_depth: [
            AttributeTypes.spring_tillage_depth,
            AttributeTypes.fall_tillage_depth,
        ],
        AttributeTypes.tillage_date: [
            AttributeTypes.spring_tillage_date,
            AttributeTypes.fall_tillage_date,
        ],
        AttributeTypes.planting_date: [
            AttributeTypes.summer_planting_date,
            AttributeTypes.winter_planting_date,
        ],
        AttributeTypes.harvest_date: [
            AttributeTypes.summer_harvest_date,
            AttributeTypes.winter_harvest_date,
        ],
        AttributeTypes.crop_yield: [
            AttributeTypes.summer_dry_yield,
            AttributeTypes.winter_dry_yield,
        ],
    }

    refined_candidates = []
    if op.attribute_type in refined_attributes.keys():
        refined_candidates = refined_attributes[op.attribute_type]

    # Check the attributes being monitored should capture the operation
    for att in monitored_attributes:
        # does the operation date fall within the monitoring range?
        d1 = att.date_range.start.date()
        d2 = op.operation_date.date()
        d3 = att.date_range.end.date()
        if not d1 <= d2 <= d3:
            continue

        # does the operation type match exactly?
        if att.type == op.attribute_type:
            return op, att

        # or, does it match one of the more specific types?
        # If yes, change the op to reflect this.
        if att.type in refined_candidates:
            op.attribute_type = att.type
            return op, att

    # No match so return the op with no attribute
    return op, None


@elasticapm.capture_span()
def sn_value_in_stage(
    sn_val: SeasonValue,
    stages: list[AttributeInstanceMeta],
    stage_id_to_crop_list: dict[int, list[str]] | None,
) -> tuple[SeasonValue, AttributeInstanceMeta | None]:
    """This returns a SeasonValue with a corresponding AttributeInstanceMeta value.
    If no matching stages can be found then AttributeInstanceMeta will be None and the caller can deal with it.
    An additional check is done to make sure the specific crop type is configured for the stage.
    """

    if len(stages) == 0:
        return sn_val, None

    sn = copy.deepcopy(sn_val)

    # For season data, only crop type can be further refined.
    at_map = {
        AttributeTypes.crop_type: [
            AttributeTypes.summer_crop_type,
            AttributeTypes.winter_crop_type,
        ],
    }
    attribute_candidates = None
    if sn.attribute_type in at_map.keys():
        attribute_candidates = at_map[sn.attribute_type]

    # filter stages, so we have a short list of possible matches
    # first pass for stages that have the correct attribute type
    stage_candidates = [
        st
        for st in stages
        if (st.type == sn.attribute_type or (attribute_candidates is not None and st.type in attribute_candidates))
    ]
    if len(stage_candidates) == 0:
        return sn_val, None

    # then find the maximally overlapping function - may be None
    best_stage_match = maximally_overlapping_attribute(stage_candidates, Range(start=sn.start_date, end=sn.end_date))
    if best_stage_match is None:
        return sn_val, None

    # if we have a map of staged crops we can check the crop is configured for the stage
    if stage_id_to_crop_list:
        crop_list = stage_id_to_crop_list[best_stage_match.stage_id]
        if crop_list:
            if sn_val.attribute_value not in crop_list:
                return sn_val, None

    # finally, we might be able to refine the attribute type
    if attribute_candidates is not None and best_stage_match.type in attribute_candidates:
        sn.attribute_type = best_stage_match.type

    return sn, best_stage_match


def inferred_tillage_practice_values(
    att_vals: list[values_schema.ValuesRequestAttributeFieldOrEntity],
    monitored_attributes: list[AttributeInstanceMeta],
) -> list[values_schema.ValuesRequestAttributeFieldOrEntity]:
    """Returns a list of tillage practice values that are applicable for the provided list of input values."""

    # Tillage depth can have an associated tillage practice
    till = {
        AttributeTypes.spring_tillage_depth: AttributeTypes.spring_tillage_practice,
        AttributeTypes.fall_tillage_depth: AttributeTypes.fall_tillage_practice,
        AttributeTypes.tillage_depth: AttributeTypes.tillage_practice,
    }

    new_vals = []

    for att_val in att_vals:
        # Fetch the real type
        att_type = att_type_by_id(monitored_attributes, att_val.attribute_id)

        if att_type in till.keys():
            value_ = att_val.value
            type_ = type(value_)

            if (type_ == float or type_ == int) and value_ <= 10.0:
                till_practice = "reduced till"
            else:
                till_practice = "conventional till"

            # set to generic tillage_practice
            # new_att_id = att_id_by_type(till[at])
            new_att_id = att_id_by_type(monitored_attributes, AttributeTypes.tillage_practice)
            if new_att_id is None:
                continue

            nv = values_schema.ValuesRequestAttributeFieldOrEntity(
                value=till_practice,
                locked=True,
                confirmed=True,
                progress=att_val.progress,
                attribute_id=new_att_id,
                field_id=att_val.field_id,
                row_id=att_val.row_id,  # <-- match row id with the value
                source=att_val.source,
            )
            new_vals.append(nv)

    return new_vals


def inferred_winter_crop_commitment(
    att_vals: list[values_schema.ValuesRequestAttributeFieldOrEntity],
    monitored_attributes: list[AttributeInstanceMeta],
) -> list[values_schema.ValuesRequestAttributeFieldOrEntity]:
    """Returns a list of winter_crop_commitment values that are applicable for the provided list of input values."""

    new_vals = []

    for att_val in att_vals:
        # If a winter crop type is present...
        att_type = att_type_by_id(monitored_attributes, att_val.attribute_id)
        if att_type == AttributeTypes.winter_crop_type and att_val.value != "":
            # ... and we're monitoring winter_crop_commitment
            new_att_id = att_id_by_type(monitored_attributes, AttributeTypes.winter_crop_commitment)
            if new_att_id is None:
                continue

            nv = values_schema.ValuesRequestAttributeFieldOrEntity(
                value=True,
                locked=True,
                confirmed=True,
                progress=att_val.progress,
                attribute_id=new_att_id,
                field_id=att_val.field_id,
                row_id=att_val.row_id,  # <-- match row id with the value
                source=att_val.source,
            )
            new_vals.append(nv)
    return new_vals


def att_type_by_id(atts: list[AttributeInstanceMeta], att_id: int) -> AttributeTypes:
    for ma in atts:
        if ma.attribute_id == att_id:
            return ma.type


def att_id_by_type(atts: list[AttributeInstanceMeta], att_type: AttributeTypes) -> int | None:
    for ma in atts:
        if ma.type == att_type:
            return ma.attribute_id
    return None


def weighted_average(values: list[float], weights: list[float], decimal_places: int = 8) -> float:
    """Returns a weighted average from the supplied lists - assumes the values and weights are in the correct order."""

    if len(values) + len(weights) == 0:
        raise ValueError("values and weights list are empty")

    if len(values) != len(weights):
        raise ValueError("values and weights list must be equal length")

    sum_weighted_values: float = 0
    for i, value_ in enumerate(values):
        sum_weighted_values += value_ * weights[i]

    return round(sum_weighted_values / sum(weights), decimal_places)


def convert_product_rate(product: dict, to_metric: bool) -> float:
    """Returns the product application rate as either imperial or metric.

    An applied product may be a solid (kg/ha) or a liquid (l/ha) and we expect the value from
    core to one of these metric rates. However, the rate_unit should be present as well, so we
    double-check and convert if required.

    Parameters
    ----------
    product: dict
        Core data to fetch, either 'seasons' or 'operations'
    to_metric : bool
        If True then return metric rate, else imperial rate
    """

    if "rate_avg" not in product.keys():
        raise ValueError("product does not contain a value for rate_avg")
    rate = product["rate_avg"]

    if "rate_unit" not in product.keys():
        raise ValueError("product does not contain a value for rate_unit")
    unit = product["rate_unit"]

    # units (ADAPT format)
    conversion = {
        "l1ha-1": 1 if to_metric else consts.LITRES_PER_HECTARE_TO_GALLONS_PER_ACRE,
        "kg1ha-1": 1 if to_metric else consts.KILOGRAMS_PER_HECTARE_TO_POUNDS_PER_ACRE,
        "gal1ac-1": consts.GALLONS_PER_ACRE_TO_LITRES_PER_HECTARE if to_metric else 1,
        "lb1ac-1": consts.POUNDS_PER_ACRE_TO_KILOGRAMS_PER_HECTARE if to_metric else 1,
    }

    if unit not in conversion:
        raise ValueError(f"unknown rate_unit = {unit}")

    return rate * conversion[unit]


def has_all_permissions(
    permissions_response: schema.UserPermissionsResponse,
    program_id: int,
    required_permissions: List[str],
    project_id: int | None = None,
) -> bool:
    """Returns True if the permissions_response contains all the required permissions for the specified program_id"""

    # first, ensure that the required permissions actually exist
    valid_permissions = [p.name for p in Permission]
    for permission in required_permissions:
        if permission not in valid_permissions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": f"Invalid permission: {permission}"},
            )

    # Next, if a project id is supplied, ensure the user has access to the project as a project_producer
    if project_id and project_id not in permissions_response.project_producer:
        return False

    # Finally, ensure the user has all the required permissions for the program
    return program_id in permissions_response.program_roles and all(
        [
            permission in permissions_response.program_roles[program_id].permissions
            for permission in required_permissions
        ]
    )


async def bulk_create_users_from_csv(request: Request, program_code: str, file: UploadFile) -> None:
    """
    Creates producers (users and projects) from a csv file. The csv file must have the following columns:

        - Email address
        - First Name
        - Last Name
        - Password
        - Billing address
        - City
        - Country
        - Zip
        - State
        - Phone

    Optionally a `CRI Key` column can be provided. If it is, then `Select Coop` must also be provided.

    `CRI Key` is a reference to which custom registration input the coop should be assigned to, as per
    mrv_program_custom_reg_inputs.key. Otherwise, the coop will not be assigned to a custom registration input

    Another optional column is `Select Competitor`. If this is provided then the following two things happen:
        - A parent group is created/fetched with the name given in this column
        - A child group is created/fetched with the name given in the `Select Coop` column
        - Both groups are assigned to the newly created producer
    """

    if file.filename.endswith(".csv"):
        try:
            content = await file.read()
            csv_reader = csv.DictReader(io.StringIO(content.decode("utf-8")))
            create_mrv_users.delay(
                program_code=program_code,
                src_rows=list(csv_reader),
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
                is_super_user=request.state.is_super_user,
            )
        except Exception as e:
            logger.error("Error creating users from csv: %s", e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Error creating users from csv"},
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "File must be a csv file"},
        )


@alru_cache
async def get_core_crops_with_labels(locale: Locale | None = None) -> list[schema.CropTypes]:
    core_crops_response = await external_api.core_crops_get_api()
    crops_list = core_crops_response.json().get("result")
    for crop in crops_list:
        label = crop.get("label")
        if not label:
            continue
        # if a locale is specified, translate the label and add it
        if locale:
            crop[f"label_{locale.lower()}"] = translate_message(label, locale)
            continue
        # otherwise supply all translations
        for locale_label in Locale:
            crop[f"label_{locale_label.lower()}"] = translate_message(label, locale_label)

    return crops_list


async def patch_core_user_detail(user_id: int, payload: PatchUserDetailRequest) -> None:
    """
    Update user detail in core using the provided payload
    """
    core_user_detail_response = await external_api.core_admin_get_user(user_id=user_id)
    core_user_detail = core_user_detail_response.json()["result"]
    if core_user_detail is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"message": f"User not found for user ID {user_id}"},
        )

    # update user detail
    target_obj = core_user_detail
    if payload.first_name is not None:
        target_obj["name"] = payload.first_name
    if payload.last_name is not None:
        target_obj["surname"] = payload.last_name
    if payload.phone is not None:
        target_obj["phone"] = payload.phone

    target_obj = core_user_detail["settings"]
    if payload.timezone is not None:
        target_obj["timezone"] = payload.timezone
    if payload.lang_locale is not None:
        target_obj["langLocale"] = payload.lang_locale
    if payload.measurement is not None:
        target_obj["measurement"] = payload.measurement

    target_obj = core_user_detail["settings"]["company"]
    if payload.city is not None:
        target_obj["city"] = payload.city
    if payload.country is not None:
        target_obj["country"] = payload.country
    if payload.state is not None:
        target_obj["state"] = payload.state
    if payload.street is not None:
        target_obj["street"] = payload.street
    if payload.zip is not None:
        target_obj["zip"] = payload.zip

    await external_api.core_admin_update_user(user_id=user_id, data=core_user_detail)


async def get_core_user_detail(user_id: int) -> UserDetailResponse:
    core_user_detail_response = await external_api.core_admin_get_user(user_id=user_id)
    core_user_detail = core_user_detail_response.json()["result"]
    if core_user_detail is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"message": f"User not found for user ID {user_id}"},
        )
    return core_user_to_user_detail_response(core_user_detail)


def core_user_to_user_detail_response(core_user_detail: dict) -> UserDetailResponse:
    user_settings = core_user_detail["settings"]
    company = user_settings["company"]

    return UserDetailResponse(
        id=core_user_detail["id"],
        first_name=core_user_detail["name"],
        last_name=core_user_detail["surname"],
        email=core_user_detail["email"],
        phone=user_settings["phone"],
        timezone=user_settings["timezone"],
        lang_locale=user_settings["langLocale"] if user_settings["langLocale"] in Locale.__members__.values() else None,
        measurement=(
            user_settings["measurement"]
            if user_settings["measurement"] in MeasurementUnits.__members__.values()
            else None
        ),
        city=company["city"],
        country=company["country"],
        state=company["state"],
        street=company["street"],
        zip=company["zip"],
    )


@cached(
    TTLCache(maxsize=128, ttl=3 * 60),
    key=lambda request, user_id, exclude_program_id: hashkey(user_id, exclude_program_id),
)
async def get_accessible_users_cached(
    request: Request,
    user_id: int,
    exclude_program_id: int | None,
) -> list[UserDetailResponse]:
    """
    Get all users that the user has access to. If the user is a super admin, return all users.
    This is an expensive operation and maybe called frequently with filtering, so cache the result to optimise the performance.
    """
    caller_role_users = await get_role_users_by_user_id(request=request, user_id=user_id)
    if is_super_admin(caller_role_users):
        core_users_response = await external_api.core_admin_get_all_users()
        return [
            core_user_to_user_detail_response(core_user_detail)
            for core_user_detail in core_users_response.json()["result"]
        ]

    accessible_user_ids = set()

    tasks = Tasks()
    for role_user in caller_role_users:
        # producer have no access to program user
        if role_user.role.role_type == RoleTypes.PRODUCER:
            continue

        # skip excluded program_id if exclude_program_id is specified
        if exclude_program_id is not None and role_user.program_id == exclude_program_id:
            continue

        tasks.add(get_accessible_producer_users(request=request, program_id=role_user.program_id, user_id=user_id))
        tasks.add(get_accessible_admin_users(request=request, program_id=role_user.program_id, user_id=user_id))

    user_id_lists = await tasks.complete_all()
    for user_id_list in user_id_lists:
        accessible_user_ids.update(user_id_list)

    # get all users from core
    core_users_response = await external_api.core_admin_batch_get_users(list(accessible_user_ids))
    result = core_users_response.json()["result"]
    if not result:
        return []
    return [core_user_to_user_detail_response(core_user_detail) for core_user_detail in result]
