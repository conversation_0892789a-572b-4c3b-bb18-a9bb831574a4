from celery_worker import async_to_sync, celery_app
from config import get_settings
from core.schema import UserRow
from helper.external_api import core_user_create_api
from logger import get_logger
from programs.db import get_program_by_program_code
from projects.user_groups.db import update_project_groups
from user_groups.schema import CreateUserGroupRequest, UserGroup
from user_groups.user_groups_controller import UserGroupsController

settings = get_settings()
logger = get_logger(__name__)

from fastapi import Request

from celery_helper.decorator_class import DBTask, State


class UserRowError(ValueError):
    pass


async def _add_coop_group(
    request: Request,
    user_groups_controller: UserGroupsController,
    program_id: int,
    project_id: int,
    parent_group: UserGroup,
    coop_name: str,
) -> None:
    """
    Ensure that a child group of `parent_group` exists with the coop_name and add it to the project.
    """
    if parent_group and coop_name:
        # See if coop_name is a group, if not create it
        if groups := await user_groups_controller.do_get_by_name(program_id=program_id, name=coop_name):
            child_group = groups[0]
        else:
            child_group = await user_groups_controller.do_create(
                CreateUserGroupRequest(
                    name=coop_name, program_id=program_id, parent_group_id=parent_group.id, color_category_index=1
                )
            )
        if child_group:
            await update_project_groups(request, project_id, [parent_group.id, child_group.id])
        else:
            logger.error("Failed to create child group. name: %s", coop_name)


async def _create_producer(program_code: str, user_row: UserRow) -> int:
    """
    Call Core to create a user and add them to the program with the given program_code.

    Return the project_id created
    """
    response = await core_user_create_api(program_code, user_row)
    response_object = response.json()
    if response_object["status"] != "ok":
        raise ValueError("Unexpected response", response_object)
    return response_object["result"]["project"][0]["id"]


async def _create_parent_group(
    src_row: dict, user_groups_controller: UserGroupsController, program_id: int
) -> UserGroup | None:
    """
    If a competitor is specified in the data row, fetch a parent group with this name. Create it if it doesn't exist.
    """
    parent_group: UserGroup | None = None
    if competitor_name := src_row.get("Select Competitor"):
        # If we have a competitor, this should become a parent group for this producer
        if groups := await user_groups_controller.do_get_by_name(name=competitor_name, program_id=program_id):
            parent_group = groups[0]
        else:
            parent_group = await user_groups_controller.do_create(
                CreateUserGroupRequest(name=competitor_name, program_id=program_id, color_category_index=1)
            )
    return parent_group


async def _create_user_row(src_row: dict) -> tuple[UserRow, str | None]:
    """
    Read and check data in CSV row and convert it to a UserRow object.
    """
    if not src_row.get("Email address"):
        logger.error("Email address is missing. row: %s", src_row)
        raise UserRowError("Email address is missing")
    payload = {
        "email": src_row["Email address"],
        "first_name": src_row.get("First Name"),
        "last_name": src_row.get("Last Name"),
        "password": src_row.get("Password"),
        "confirm_password": src_row.get("Password"),
        "address": src_row.get("Billing address"),
        "city": src_row.get("City"),
        "country": src_row.get("Country"),
        "zip": src_row.get("Zip"),
        "state": src_row.get("State"),
        "phone": src_row.get("Phone") if src_row.get("Phone") else "0123456789",
        "phoneFullNumber": src_row.get("Phone") if src_row.get("Phone") else "0123456789",
    }
    coop_name: str | None = None
    if int(src_row.get("CRI Key", 0)) > 0:
        coop_name = src_row.get("Select Coop")
        payload["custom_inputs"] = [{"type_": "Dropdown", "key": src_row.get("CRI Key"), "value": coop_name}]
    try:
        user_row = UserRow(**payload)
    except Exception as e:
        logger.error("Failed to validate user row - error: %s", e)
        raise
    return user_row, coop_name


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
async def create_mrv_users(
    self: DBTask,
    *,
    program_code: str,
    src_rows: list[dict],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | State | None = None,
) -> None:
    """
    Create individual producer and optionally assign them to a group/parent group and coop.

    See bulk_create_users_from_csv for details on the input data.
    """
    report_dict: dict = {"success": [], "failure": []}
    program = await get_program_by_program_code(request, program_code)
    user_groups_controller = UserGroupsController(request)
    for index, src_row in enumerate(src_rows):
        user_row: UserRow | None = None
        try:
            parent_group = await _create_parent_group(src_row, user_groups_controller, program.id)
            user_row, coop_name = await _create_user_row(src_row)
            project_id = await _create_producer(program_code, user_row)
            report_dict["success"].append(user_row.email)
            if parent_group and coop_name:
                await _add_coop_group(request, user_groups_controller, program.id, project_id, parent_group, coop_name)
        except Exception as e:
            email = user_row.email if user_row else ""
            logger.error("Failed to create user. index %d, email: %s, error: %s", index, email, repr(e))
            report_dict["failure"].append({"email": email})
