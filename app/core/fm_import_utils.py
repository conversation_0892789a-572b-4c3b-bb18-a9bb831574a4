import copy
import statistics
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from dateutil.parser import isoparse

from config import get_settings
from core import search_list
from core.consts import (
    GALLONS_PER_ACRE_TO_LITRES_PER_HECTARE,
    <PERSON><PERSON><PERSON>GRAMS_PER_HECTARE_TO_POUNDS_PER_ACRE,
    LITRES_PER_HECTARE_TO_GALLONS_PER_ACRE,
    POUNDS_PER_ACRE_TO_KILOGRAMS_PER_HECTARE,
)
from core.enums import OperationType
from core.methods import AttributeInstanceMeta, Range
from core.schema import Operation, OperationApplicationProduct
from defaults.attribute_options import (
    ApplicationRateType,
    CropUsage,
    TerminationMethods,
)
from logger import get_logger
from phases.enums import AttributeTypes, StageTypes
from phases.model import Attribute, StageWithPhase
from values.constants import CONVENTIONAL_TILL, REDUCED_TILL
from values.enums import ImportDataSources, ProgressChoices
from values.schema import ValuesRequestAttributeFieldOrEntity

logger = get_logger(__name__)
settings = get_settings()

DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%f"
DATE_FORMAT = "%Y-%m-%d"
YEAR_FORMAT = "%Y"
LOCAL_ENV = "local"


@dataclass
class RowData:
    """
    Represents an intermediate state of a row in the MRV table,
    in which the operations have not yet been aggregated.
    Fields:
    - seeding_ops: The seeding operations in the row (to be aggregated).
    - yield_ops: The yield operations in the row (to be aggregated).
    - tillage_ops: The tillage operations in the row (to be aggregated).
    - application_op: The application operation in the row.
    """

    seeding_ops: List[Operation] = field(default_factory=list)
    yield_ops: List[Operation] = field(default_factory=list)
    tillage_ops: List[Operation] = field(default_factory=list)
    application_op: Operation = field(default_factory=dict)


@dataclass
class AggRowData:
    """
    Represents a row in the MRV table. Each row contains ONE of:
    - Seeding and/or yield operation
    - Tillage operation
    - Application operation
    Fields:
    - ops: The operation(s) in the row.
    """

    ops: List[Operation]


@dataclass
class AttributeData:
    """
    Represents an attribute in a row in the MRV table.
    Fields:
    - type_: The type of the attribute.
    - value: The value of the attribute.
    """

    type_: AttributeTypes
    value: Any


def get_validated_operations(operations: List[dict], stages: List[StageWithPhase]) -> List[Operation]:
    # Get the widest date range across all of the stages. This cuts down work because
    # operations outside this range can be excluded immediately.
    monitored_date_range = Range(
        start=min(stage.fmi_import_start_date for stage in stages),
        end=max(stage.fmi_import_end_date for stage in stages),
    )
    validated_operations = []
    for operation in operations:
        operation_date = isoparse(operation["event_date"])
        if not monitored_date_range.start <= operation_date <= monitored_date_range.end:
            continue
        try:
            validated_operation = Operation.from_dict(operation)
            validated_operations.append(validated_operation)
        except Exception as e:
            logger.error(f"operation is invalid: {repr(e)}")
            continue
    return validated_operations


def get_stage_date_range(stage: StageWithPhase) -> Range:
    start_date = stage.fmi_import_start_date
    end_date = stage.fmi_import_end_date
    if not start_date or not end_date:
        start_date = datetime(stage.year_start, 1, 1)
        end_date = datetime(stage.year_end, 12, 31)
    return Range(start=start_date, end=end_date)


def get_attribute_date_range(attribute: Attribute, stages: list[StageWithPhase]) -> Range:
    """Returns a date range for the attribute.

    Use min and max date values for the attribute.
    Fall back to FMI import date range for the stage.
    Fall back to start and end years for the stage."""

    if _is_date(attribute.min_val) and _is_date(attribute.max_val):
        return Range(
            start=isoparse(attribute.min_val),
            end=isoparse(attribute.max_val),
        )
    else:
        for stage in stages:
            if stage.id == attribute.parent_stage_id:
                return get_stage_date_range(stage)


def _is_date(value: Any) -> bool:
    try:
        isoparse(value)
        return True
    except Exception:
        return False


# TODO: add crop type to harvest operation attributes and remove this workaround.
def add_crop_type_to_harvest_operations(operations: List[Dict], seasons: List[Dict]) -> List[Dict]:
    harvest_date_to_crop_type = {}
    for season in seasons:
        end_date = isoparse(season["endDate"]).strftime(DATE_FORMAT)
        crop_type = season["cropType"]
        harvest_date_to_crop_type[end_date] = crop_type
    for op in operations:
        if op["type"] == OperationType.YIELD:
            harvest_date = isoparse(op["event_date"]).strftime(DATE_FORMAT)
            crop_type = harvest_date_to_crop_type.get(harvest_date)
            if not crop_type:
                continue
            op["attributes"]["crop_type"] = crop_type
    return operations


def map_stage_to_operations(
    operations: List[Operation],
    stages: List[StageWithPhase],
) -> Dict[int, List[Dict]]:
    stage_to_operations = defaultdict(list)
    for op in operations:
        stage_id = _get_operation_stage_id(op, stages)
        if stage_id is None:
            continue
        stage_to_operations[stage_id].append(op)
    return stage_to_operations


def _get_operation_stage_id(operation: Operation, stages: List[StageWithPhase]) -> int | None:
    """Returns the stage ID for the operation.

    If there are multiple stage candidates, then selects the best stage candidate."""

    stage_candidates = []
    for stage in stages:
        stage_date_range = get_stage_date_range(stage)
        if stage_date_range.start <= operation.event_date <= stage_date_range.end:
            stage_candidates.append(stage)

    if len(stage_candidates) == 0:
        return None
    elif len(stage_candidates) == 1:
        return stage_candidates[0].id
    else:
        return _select_best_stage_candidate(operation, stage_candidates)


# TODO: confirm best stage logic.
def _select_best_stage_candidate(operation: Operation, stage_candidates: List[StageWithPhase]) -> int:
    """Returns the best stage ID for the operation."""

    operation_to_best_stages = {
        OperationType.SEEDING: [
            StageTypes.HISTORICAL_CROP_ROTATION,
            StageTypes.SUMMER_CROPS,
            StageTypes.WINTER_CROPS,
        ],
        OperationType.YIELD: [
            StageTypes.HISTORICAL_CROP_ROTATION,
            StageTypes.SUMMER_CROPS,
            StageTypes.WINTER_CROPS,
        ],
        OperationType.APPLICATION: [StageTypes.NUTRIENT_MGMT],
        OperationType.TILLAGE: [StageTypes.HISTORICAL_TILLAGE, StageTypes.TILLAGE],
    }
    best_stage_types = operation_to_best_stages.get(operation.type, None)
    if best_stage_types is not None:
        for candidate in stage_candidates:
            for stage_type in best_stage_types:
                if candidate.type_ == stage_type:
                    return candidate.id
    return stage_candidates[0].id


# TODO: determine if we need to account for 2 crop seasons for same crop in same year.
# (ex. corn 2024 seeding 1/1 -> yield 2/1, seeding 3/1 -> yield 4/1)
def generate_rows(operations: List[Operation]) -> List[RowData]:
    """Returns a list of unfinalized row objects. For each row, the operations have not yet been aggregated."""

    operations = sorted(operations, key=lambda x: x.event_date)

    rows: List[RowData] = []
    # seeding and yield ops for a crop type belong in the same row
    crop_type_to_row_idx: Dict[str, int] = {}
    # tillage ops for a date belong in the same row
    tillage_date_to_row_idx: Dict[str, int] = {}

    for op in operations:
        match op.type:
            case OperationType.SEEDING:
                crop_type = op.attributes.crop_type
                row_idx = crop_type_to_row_idx.get(crop_type, None)
                # add seeding op to existing row if (1) start date less than 1 year ago and (2) no harvest ops
                if (
                    row_idx is not None
                    and op.event_date < _get_crop_row_start_date(rows[row_idx]) + timedelta(days=365)
                    and len(rows[row_idx].yield_ops) == 0
                ):
                    row_data = rows[row_idx]
                    row_data.seeding_ops.append(op)
                # add seeding op to new row
                else:
                    row_data = RowData(seeding_ops=[op])
                    rows.append(row_data)
                    crop_type_to_row_idx[crop_type] = len(rows) - 1
            case OperationType.YIELD:
                crop_type = op.attributes.crop_type
                row_idx = crop_type_to_row_idx.get(crop_type, None)
                # add harvest op to existing row if (1) start date less than 1 year ago
                if row_idx is not None and op.event_date < _get_crop_row_start_date(rows[row_idx]) + timedelta(
                    days=365
                ):
                    row_data = rows[row_idx]
                    row_data.yield_ops.append(op)
                # add harvest op to new row
                else:
                    row_data = RowData(yield_ops=[op])
                    rows.append(row_data)
                    crop_type_to_row_idx[crop_type] = len(rows) - 1
            case OperationType.TILLAGE:
                tillage_date = op.event_date.strftime(DATE_FORMAT)
                row_idx = tillage_date_to_row_idx.get(tillage_date, None)
                if row_idx is not None:
                    # add tillage op to existing row
                    row_data = rows[row_idx]
                    row_data.tillage_ops.append(op)
                else:
                    # add tillage op to new row
                    row_data = RowData(tillage_ops=[op])
                    rows.append(row_data)
                    tillage_date_to_row_idx[tillage_date] = len(rows) - 1
            case OperationType.APPLICATION:
                # add each product in application op to new row
                for product in op.attributes.products:
                    op_copy = copy.deepcopy(op)
                    op_copy.attributes.products = [copy.deepcopy(product)]
                    row_data = RowData(application_op=op_copy)
                    rows.append(row_data)
            case _:
                logger.error(f"operation has invalid type: {op.type}")
    return rows


def _get_crop_row_start_date(row: RowData) -> datetime:
    if row.seeding_ops:
        return row.seeding_ops[0].event_date
    else:
        return row.yield_ops[0].event_date


def aggregate_rows(rows: List[RowData]) -> List[AggRowData]:
    """Returns a list of finalized row objects. For each row, the operations are aggregated:
    - Use latest seeding op.
    - Use latest yield op, and use average yield across all yield ops.
    - Use latest tillage op."""

    agg_rows = []
    for row in rows:
        ops = []
        if row.seeding_ops:
            ops.append(row.seeding_ops[-1])
        if row.yield_ops:
            yield_ops_to_include = []
            for op in row.yield_ops:
                if op.attributes.dry_volume_avg is not None:
                    yield_ops_to_include.append(op)
            is_mowing = False
            if len(yield_ops_to_include) == 0:
                # in JD, mowing ops are harvest ops that do not have a yield vol.
                # here, we assume that if ALL ops do not have a yield vol, then they are mowing ops,
                # and we do not skip them. however, we could just have all "bad" harvest ops.
                # TODO: optimize how mowing ops are handled.
                yield_ops_to_include = row.yield_ops
                is_mowing = True
            yield_op = copy.deepcopy(yield_ops_to_include[-1])
            if len(yield_ops_to_include) > 1 and not is_mowing:
                agg_dry_volume_avg = statistics.mean([op.attributes.dry_volume_avg for op in yield_ops_to_include])
                yield_op.attributes.dry_volume_avg = agg_dry_volume_avg
            ops.append(yield_op)
        if row.tillage_ops:
            ops.append(row.tillage_ops[-1])
        if row.application_op:
            ops.append(row.application_op)
        agg_rows.append(AggRowData(ops=ops))
    return agg_rows


def generate_operation_values(
    rows: List[AggRowData],
    monitored_attributes: List[AttributeInstanceMeta],
    progress_choice: ProgressChoices,
    mrv_field_id: int,
    product_skip_list: str | list[str] | None = None,
) -> List[ValuesRequestAttributeFieldOrEntity]:
    """Returns a list of MRV values."""

    all_op_vals = []
    for row_idx, row_data in enumerate(rows):
        # use the year of the earliest op in the row
        record_year_val = None
        for op in row_data.ops:
            external_service = op.external_service.external_service or ImportDataSources.other_fms
            attributes = _generate_attributes(op, product_skip_list)
            if not attributes:
                continue
            op_vals = _generate_values(
                operation_date=op.event_date,
                attributes=attributes,
                monitored_attributes=monitored_attributes,
                row_id=row_idx,
                progress=progress_choice,
                field_id=mrv_field_id,
                source=external_service,
                event_id=op.event_id,
            )
            if not op_vals:
                continue
            all_op_vals.extend(op_vals)
            if record_year_val is None:
                record_year_val = _generate_record_year_value(
                    operation_date=op.event_date,
                    monitored_attributes=monitored_attributes,
                    row_id=row_idx,
                    progress=progress_choice,
                    field_id=mrv_field_id,
                    source=external_service,
                )
        if record_year_val is not None:
            all_op_vals.append(record_year_val)
    return all_op_vals


def _generate_attributes(operation: Operation, product_skip_list: str | List[str] | None = None) -> List[AttributeData]:
    match operation.type:
        case OperationType.SEEDING:
            return _generate_seeding_attributes(operation)
        case OperationType.YIELD:
            return _generate_yield_attributes(operation)
        case OperationType.APPLICATION:
            # FMS sync for nutrient management is not in scope for cargill 2024 measurement phase. temp disable.
            if settings.env == LOCAL_ENV:
                return _generate_application_attributes(operation, product_skip_list)
            else:
                return []
        case OperationType.TILLAGE:
            return _generate_tillage_attributes(operation)
        case _:
            logger.error(f"operation has invalid type: {operation.type}")
            return []


def _generate_seeding_attributes(operation: Operation) -> List[AttributeData]:
    return [
        AttributeData(
            type_=AttributeTypes.planting_date,
            value=_utc_datetime_to_str(operation.event_date),
        ),
        AttributeData(
            type_=AttributeTypes.crop_type,
            value=operation.attributes.crop_type,
        ),
    ]


def _generate_yield_attributes(operation: Operation) -> List[AttributeData]:
    attribute_data = [
        AttributeData(
            type_=AttributeTypes.harvest_date,
            value=_utc_datetime_to_str(operation.event_date),
        ),
        # both seeding and yield ops should include a crop type attribute,
        # so that yield ops can exist without seeding ops, and vice versa.
        # attributes are deduped downstream.
        AttributeData(
            type_=AttributeTypes.crop_type,
            value=operation.attributes.crop_type,
        ),
    ]
    if operation.attributes.dry_volume_avg is not None:
        attribute_data.append(
            AttributeData(
                type_=AttributeTypes.crop_yield,
                value=operation.attributes.dry_volume_avg,
            )
        )
    if operation.attributes.dry_volume_unit is not None:
        attribute_data.append(
            AttributeData(
                type_=AttributeTypes.yield_rate_unit,
                value=_convert_yield_unit(operation.attributes.dry_volume_unit),
            )
        )
    return attribute_data


def _generate_application_attributes(
    operation: Operation,
    product_skip_list: str | List[str] | None = None,
    to_metric: bool = True,
) -> List[AttributeData]:
    op_area = operation.attributes.area_total
    if op_area is None:
        return []

    # used to skip non-fertiliser products
    product_skip = None
    if product_skip_list is not None:
        product_skip = search_list.SearchList(product_skip_list)

    # an application op with multiple products is already split into multiple application ops with a single product each beforehand
    product = operation.attributes.products[0]
    if product.product_name is None or product.rate_unit is None or product.rate_avg is None:
        return []

    if product_skip is not None and product_skip.match(product.product_name):
        return []

    rate = 0.0
    try:
        rate = _convert_product_rate(product, to_metric=to_metric)
    except Exception as e:
        logger.error("could not convert product rate: %s", e)

    return [
        AttributeData(
            type_=AttributeTypes.nutrient_management_enabled,
            value=True,
        ),
        AttributeData(
            type_=AttributeTypes.application_date,
            value=_utc_datetime_to_str(operation.event_date),
        ),
        AttributeData(
            type_=AttributeTypes.application_area,
            value=op_area,
        ),
        AttributeData(
            type_=AttributeTypes.application_product,
            value=product.product_name,
        ),
        AttributeData(
            type_=AttributeTypes.application_rate,
            value=rate,
        ),
        AttributeData(
            type_=AttributeTypes.application_rate_type,
            value=ApplicationRateType.PRODUCT_RATE,
        ),
        AttributeData(
            type_=AttributeTypes.application_rate_unit,
            value=product.rate_unit,
        ),
    ]


def _generate_tillage_attributes(operation: Operation) -> List[AttributeData]:
    depth_target = operation.attributes.depth_target
    depth_result = operation.attributes.depth_result

    if depth_target is None and depth_result is None:
        return []

    till_depth = 0

    if depth_target is not None:
        till_depth = depth_target.depth_avg or till_depth

    # prefer depth result
    if depth_result is not None:
        till_depth = depth_result.depth_avg or till_depth

    return [
        AttributeData(
            type_=AttributeTypes.tillage_date,
            value=_utc_datetime_to_str(operation.event_date),
        ),
        AttributeData(
            type_=AttributeTypes.tillage_depth,
            value=till_depth,
        ),
    ]


def _generate_record_year_value(
    operation_date: datetime,
    monitored_attributes: List[AttributeInstanceMeta],
    row_id: int,
    progress: ProgressChoices,
    field_id: int,
    source: ImportDataSources,
) -> ValuesRequestAttributeFieldOrEntity:
    monitored_attr = _get_monitored_attr(
        operation_date=operation_date,
        attribute_type=AttributeTypes.record_year,
        monitored_attributes=monitored_attributes,
    )
    if not monitored_attr:
        return
    return _generate_value(
        attr_val=operation_date.strftime(YEAR_FORMAT),
        attr_id=monitored_attr.attribute_id,
        row_id=row_id,
        progress=progress,
        field_id=field_id,
        source=source,
        event_id=None,
    )


def _generate_values(
    operation_date: datetime,
    attributes: List[AttributeData],
    monitored_attributes: List[AttributeInstanceMeta],
    row_id: int,
    progress: ProgressChoices,
    field_id: int,
    source: ImportDataSources,
    event_id: str,
) -> List[ValuesRequestAttributeFieldOrEntity]:
    op_vals = []
    for attr in attributes:
        monitored_attr = _get_monitored_attr(operation_date, attr.type_, monitored_attributes)
        if not monitored_attr:
            continue
        op_val = _generate_value(
            attr_val=attr.value,
            attr_id=monitored_attr.attribute_id,
            row_id=row_id,
            progress=progress,
            field_id=field_id,
            source=source,
            event_id=event_id,
        )
        op_vals.append(op_val)

        inferred_vals = _generate_inferred_values(op_val, operation_date, monitored_attr.type, monitored_attributes)
        if inferred_vals:
            op_vals.extend(inferred_vals)
    return op_vals


def _generate_inferred_values(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    attribute_type: AttributeTypes,
    monitored_attributes: List[AttributeInstanceMeta],
) -> List[ValuesRequestAttributeFieldOrEntity]:
    inferred_vals = []
    if attribute_type == AttributeTypes.winter_crop_type:
        if inferred_val := _infer_winter_crop_commitment(op_val, operation_date, monitored_attributes):
            inferred_vals.append(inferred_val)
    elif attribute_type == AttributeTypes.crop_yield:
        if inferred_val := _infer_winter_crop_termination(op_val, operation_date, monitored_attributes):
            inferred_vals.append(inferred_val)
        if inferred_val := _infer_crop_usage(op_val, operation_date, monitored_attributes):
            inferred_vals.append(inferred_val)
    elif attribute_type == AttributeTypes.tillage_date:
        if inferred_val := _infer_tillage_event(op_val, operation_date, monitored_attributes):
            inferred_vals.append(inferred_val)
    return inferred_vals


def _generate_value(
    attr_val: Any,
    attr_id: int,
    row_id: int,
    progress: ProgressChoices,
    field_id: int,
    source: ImportDataSources,
    event_id: Optional[str],
) -> ValuesRequestAttributeFieldOrEntity:
    return ValuesRequestAttributeFieldOrEntity(
        value=attr_val,
        attribute_id=attr_id,
        row_id=row_id,
        locked=False,
        confirmed=True,
        progress=progress,
        field_id=field_id,
        source=source,
        event_id=event_id,
    )


def _get_monitored_attr(
    operation_date: datetime,
    attribute_type: AttributeTypes,
    monitored_attributes: List[AttributeInstanceMeta],
) -> AttributeInstanceMeta | None:
    """Returns the attribute that the operation should be associated with."""

    refined_attributes = {
        AttributeTypes.tillage_depth: [
            AttributeTypes.spring_tillage_depth,
            AttributeTypes.fall_tillage_depth,
        ],
        AttributeTypes.tillage_date: [
            AttributeTypes.spring_tillage_date,
            AttributeTypes.fall_tillage_date,
        ],
        AttributeTypes.planting_date: [
            AttributeTypes.summer_planting_date,
            AttributeTypes.winter_planting_date,
        ],
        AttributeTypes.harvest_date: [
            AttributeTypes.summer_harvest_date,
            AttributeTypes.winter_harvest_date,
        ],
        AttributeTypes.crop_yield: [
            AttributeTypes.summer_dry_yield,
            AttributeTypes.winter_dry_yield,
        ],
        AttributeTypes.crop_type: [
            AttributeTypes.summer_crop_type,
            AttributeTypes.winter_crop_type,
        ],
    }

    refined_candidates = []
    if attribute_type in refined_attributes.keys():
        refined_candidates = refined_attributes[attribute_type]

    for attr in monitored_attributes:
        # does the date range include the operation date?
        if attr.date_range and not attr.date_range.start <= operation_date <= attr.date_range.end:
            continue

        # does the type match the operation attribute type?
        if attr.type == attribute_type:
            return attr

        # or, does the type match one of the more specific attribute types?
        if attr.type in refined_candidates:
            return attr

    # if no match, then return no attribute
    return None


def _infer_tillage_practice(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    attribute_type: AttributeTypes,
    monitored_attributes: List[AttributeInstanceMeta],
) -> ValuesRequestAttributeFieldOrEntity | None:
    """Returns a list of tillage practice values that are applicable for the provided list of input values."""
    till_practice_types = {
        AttributeTypes.spring_tillage_depth: AttributeTypes.spring_tillage_practice,
        AttributeTypes.fall_tillage_depth: AttributeTypes.fall_tillage_practice,
        AttributeTypes.tillage_depth: AttributeTypes.tillage_practice,
    }
    till_practice_attr = _get_monitored_attr(operation_date, till_practice_types[attribute_type], monitored_attributes)
    if not till_practice_attr:
        return None
    if (type(op_val.value) == float or type(op_val.value) == int) and op_val.value <= 10.0:
        till_practice = REDUCED_TILL
    else:
        till_practice = CONVENTIONAL_TILL
    return ValuesRequestAttributeFieldOrEntity(
        value=till_practice,
        attribute_id=till_practice_attr.attribute_id,
        row_id=op_val.row_id,
        locked=True,
        confirmed=True,
        progress=op_val.progress,
        field_id=op_val.field_id,
        source=op_val.source,
    )


def _infer_winter_crop_commitment(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    monitored_attributes: List[AttributeInstanceMeta],
) -> ValuesRequestAttributeFieldOrEntity | None:
    """Returns a list of winter crop commitment values that are applicable for the provided list of input values."""
    if op_val.value == "":
        return None
    wcc_attr = _get_monitored_attr(operation_date, AttributeTypes.winter_crop_commitment, monitored_attributes)
    if not wcc_attr:
        return None
    return ValuesRequestAttributeFieldOrEntity(
        value=True,
        attribute_id=wcc_attr.attribute_id,
        row_id=op_val.row_id,
        locked=True,
        confirmed=True,
        progress=op_val.progress,
        field_id=op_val.field_id,
        source=op_val.source,
    )


def _infer_winter_crop_termination(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    monitored_attributes: List[AttributeInstanceMeta],
) -> ValuesRequestAttributeFieldOrEntity | None:
    if op_val.value <= 0:
        return None
    wct_attr = _get_monitored_attr(operation_date, AttributeTypes.winter_crop_termination, monitored_attributes)
    if not wct_attr:
        return None
    return ValuesRequestAttributeFieldOrEntity(
        value=TerminationMethods.grain_harvest,
        attribute_id=wct_attr.attribute_id,
        row_id=op_val.row_id,
        locked=False,
        confirmed=True,
        progress=op_val.progress,
        field_id=op_val.field_id,
        source=op_val.source,
    )


def _infer_crop_usage(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    monitored_attributes: List[AttributeInstanceMeta],
) -> ValuesRequestAttributeFieldOrEntity | None:
    if op_val.value <= 0:
        return None
    crop_usage_attr = _get_monitored_attr(operation_date, AttributeTypes.crop_usage, monitored_attributes)
    if not crop_usage_attr:
        return None
    return ValuesRequestAttributeFieldOrEntity(
        value=CropUsage.COMMODITY,
        attribute_id=crop_usage_attr.attribute_id,
        row_id=op_val.row_id,
        locked=False,
        confirmed=True,
        progress=op_val.progress,
        field_id=op_val.field_id,
        source=op_val.source,
    )


def _infer_tillage_event(
    op_val: ValuesRequestAttributeFieldOrEntity,
    operation_date: datetime,
    monitored_attributes: List[AttributeInstanceMeta],
) -> ValuesRequestAttributeFieldOrEntity | None:
    tillage_event_attr = _get_monitored_attr(operation_date, AttributeTypes.tillage_event, monitored_attributes)
    if not tillage_event_attr:
        return None
    return ValuesRequestAttributeFieldOrEntity(
        value=True,
        attribute_id=tillage_event_attr.attribute_id,
        row_id=op_val.row_id,
        locked=False,
        confirmed=True,
        progress=op_val.progress,
        field_id=op_val.field_id,
        source=op_val.source,
    )


def _convert_yield_unit(dry_volume_unit: str) -> str:
    conversion = {
        "bu1ac-1": "bu/ac",
        "bale1ac-1": "bale/ac",
    }
    return conversion.get(dry_volume_unit, "")


def _convert_product_rate(product: OperationApplicationProduct, to_metric: bool) -> float:
    conversion = {
        "l1ha-1": 1 if to_metric else LITRES_PER_HECTARE_TO_GALLONS_PER_ACRE,
        "kg1ha-1": 1 if to_metric else KILOGRAMS_PER_HECTARE_TO_POUNDS_PER_ACRE,
        "gal1ac-1": GALLONS_PER_ACRE_TO_LITRES_PER_HECTARE if to_metric else 1,
        "lb1ac-1": POUNDS_PER_ACRE_TO_KILOGRAMS_PER_HECTARE if to_metric else 1,
    }

    if product.rate_unit not in conversion:
        raise ValueError(f"unknown rate_unit = {product.rate_unit}")

    return product.rate_avg * conversion[product.rate_unit]


def _utc_datetime_to_str(dt: datetime) -> str:
    return dt.strftime(DATETIME_FORMAT)[:-3] + "Z"
