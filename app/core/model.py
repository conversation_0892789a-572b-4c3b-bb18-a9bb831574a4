from sqlalchemy import Column, DATETIME, DECIMAL, JSON, String
from sqlalchemy.dialects.mysql import INTEGER

from config import get_settings
from db.mysql import Base
from helper.custom_models_types import Multipolygon

settings = get_settings()

# These models aren't created in MRV - they're part of Core. They won't be created in a local DB, but they'll be
# in the same DB schema as the MRV tables on dev and prod.


class Groups(Base):
    """Represents a farm"""

    __tablename__ = "groups"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    deleted_at = Column(DATETIME, nullable=True)


class KMLGroups(Base):
    """
    Represents a field. There's a many-to-one relationship between kml_groups (fields) and groups (farms), which is
    what you'd expect. What is less intuitive is that there is another table (not shown here) called kml_files, which
    represents field boundaries. The less intuitive part is that there is a many-to-one relationship between
    kml_groups (fields) and kml_files (boundaries). This effectively means that two or more fields can exist in the DB
    which share the same boundary.
    """

    __tablename__ = "kml_groups"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)
    group_id = Column(INTEGER(10, unsigned=True))
    kml_id = Column(INTEGER(10, unsigned=True))
    name = Column(String(255), nullable=False)
    deleted_at = Column(DATETIME, nullable=True)
    created_by = Column(INTEGER(10, unsigned=True))


class KMLFiles(Base):
    __tablename__ = "kml_files"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)
    md5 = Column(String(32), nullable=False)
    # realName has this casing in core so we have to keep it and ignore the
    # corresponding flake8 concern
    realName = Column(String(128), nullable=False)  # noqa: N815
    area = Column(DECIMAL(14, 6), nullable=False)
    region_id = Column(INTEGER(10, unsigned=True), nullable=False)
    deleted_at = Column(DATETIME, nullable=True)
    geometry = Column(Multipolygon, nullable=False)


class Users(Base):
    __tablename__ = "users"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)
    email = Column(String(64), nullable=False)
    name = Column(String(64), nullable=False)
    surname = Column(String(64), nullable=False)
    phone = Column(String(45), nullable=True)
    settings = Column(JSON, nullable=True)
    deleted_at = Column(DATETIME)


class Crops(Base):
    __tablename__ = "crops"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)


class CoreRegions(Base):
    """Represents a region in the core system. Not to be confused
    with MRV region"""

    __tablename__ = "regions"
    __table_args__ = {"info": {"skip_autogenerate": True}}
    id = Column(INTEGER(10, unsigned=True), nullable=False, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    # existing, not currently used by MRV
    country_name = Column(String(255), nullable=False)
    deleted_at = Column(DATETIME, nullable=True)
