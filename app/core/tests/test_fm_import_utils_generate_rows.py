from datetime import datetime

from core.enums import OperationType
from core.fm_import_utils import generate_rows, RowData
from core.schema import (
    ExternalService,
    Operation,
    OperationApplicationAttributes,
    OperationApplicationProduct,
    OperationSeedingAttributes,
    OperationTillageAttributes,
    OperationYieldAttributes,
)


def test_seeding_yield():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [RowData(seeding_ops=[ops[0]], yield_ops=[ops[1]], tillage_ops=[], application_op={})]
    res = generate_rows(ops)
    assert res == expected_res


def test_application():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.APPLICATION,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationApplicationAttributes(
                products=[
                    OperationApplicationProduct(product_name="product1"),
                    OperationApplicationProduct(product_name="product2"),
                ]
            ),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[],
            application_op=Operation(
                id=1,
                field_id=123,
                type=OperationType.APPLICATION,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationApplicationAttributes(
                    products=[
                        OperationApplicationProduct(product_name="product1"),
                    ]
                ),
                external_service=ExternalService(),
            ),
        ),
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[],
            application_op=Operation(
                id=1,
                field_id=123,
                type=OperationType.APPLICATION,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationApplicationAttributes(
                    products=[
                        OperationApplicationProduct(product_name="product2"),
                    ]
                ),
                external_service=ExternalService(),
            ),
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_tillage():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2025, 1, 1, 0, 0, 1),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[ops[0]],
            application_op={},
        ),
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[ops[1]],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_seeding_yield_multiple_crops():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 3, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 4, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="barley", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(seeding_ops=[ops[0]], yield_ops=[ops[2]], tillage_ops=[], application_op={}),
        RowData(seeding_ops=[ops[1]], yield_ops=[ops[3]], tillage_ops=[], application_op={}),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_multiple_seeding_yield():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 3, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 4, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(seeding_ops=[ops[0], ops[1]], yield_ops=[ops[2], ops[3]], tillage_ops=[], application_op={})
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_multiple_seeding_yield_multiple_crops():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 3, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 4, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
        Operation(
            id=5,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 5, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=6,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 6, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="barley", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=7,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 7, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=8,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 8, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="barley", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(seeding_ops=[ops[0], ops[2]], yield_ops=[ops[4], ops[6]], tillage_ops=[], application_op={}),
        RowData(seeding_ops=[ops[1], ops[3]], yield_ops=[ops[5], ops[7]], tillage_ops=[], application_op={}),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_old_seeding_new_seeding():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2023, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[ops[0]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[ops[1]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_old_seeding_new_yield():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2023, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[ops[0]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[],
            yield_ops=[ops[1]],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_old_yield_new_yield():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2023, 1, 1, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[ops[0]],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[],
            yield_ops=[ops[1]],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_existing_crop_row_with_harvest_op():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 3, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 4, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=5,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 5, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[ops[0], ops[1]],
            yield_ops=[ops[2], ops[3]],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[ops[4]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_yield_seeding():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[ops[0]],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[ops[1]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_yield_seeding_multiple_crops():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationSeedingAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[ops[0]],
            tillage_ops=[],
            application_op={},
        ),
        RowData(
            seeding_ops=[ops[1]],
            yield_ops=[],
            tillage_ops=[],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res


def test_multiple_tillage():
    ops = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2024, 1, 1, 0, 0, 1),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2024, 1, 1, 0, 0, 2),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2024, 1, 2, 0, 0, 1),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.TILLAGE,
            event_date=datetime(2024, 1, 2, 0, 0, 2),
            attributes=OperationTillageAttributes(),
            external_service=ExternalService(),
        ),
    ]
    expected_res = [
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[ops[0], ops[1]],
            application_op={},
        ),
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[ops[2], ops[3]],
            application_op={},
        ),
    ]
    res = generate_rows(ops)
    assert res == expected_res
