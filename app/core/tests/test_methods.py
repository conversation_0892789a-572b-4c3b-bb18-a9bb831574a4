import json
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from unittest.mock import patch

import pytest

from core import methods, schema
from core.methods import (
    AttributeInstanceMeta,
    calculate_monitoring_date_range,
    maximally_overlapping_attribute,
    OperationValue,
    Range,
    SeasonValue,
)
from defaults.attribute_options import ApplicationRateType
from helper import external_api, external_api_mocked_responses
from permissions.enums import Permission
from phases.enums import AttributeTypes
from values.enums import ImportDataSources, ProgressChoices


def new_aim(
    att_type: str, start_date: datetime, end_date: datetime, stage_id=1, attribute_id=1
) -> AttributeInstanceMeta:
    """Helper to create AttributeInstanceMeta objects"""
    return AttributeInstanceMeta(
        date_range=Range(start=start_date, end=end_date),
        type=att_type,
        stage_id=stage_id,
        attribute_id=attribute_id,
    )


class MockProgram:
    # 12 months only
    spring_start = datetime(2021, 9, 1)
    summer_end = datetime(2022, 2, 28)
    autumn_start = datetime(2022, 3, 1)
    winter_end = datetime(2022, 8, 31)

    planting_aim = [
        new_aim(
            att_type=AttributeTypes.summer_planting_date,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.winter_planting_date,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]

    harvest_aim = [
        new_aim(
            att_type=AttributeTypes.summer_harvest_date,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.winter_harvest_date,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]

    crop_type_aim = [
        new_aim(
            att_type=AttributeTypes.summer_crop_type,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.winter_crop_type,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]

    tillage_depth_aim = [
        new_aim(
            att_type=AttributeTypes.spring_tillage_depth,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.fall_tillage_depth,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]

    tillage_date_aim = [
        new_aim(
            att_type=AttributeTypes.spring_tillage_date,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.fall_tillage_date,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]

    tillage_practice_aim = [
        new_aim(
            att_type=AttributeTypes.spring_tillage_practice,
            start_date=spring_start,
            end_date=summer_end,
        ),
        new_aim(
            att_type=AttributeTypes.fall_tillage_practice,
            start_date=autumn_start,
            end_date=winter_end,
        ),
    ]


@pytest.mark.methods
def test_maximally_overlapping_attribute():
    @dataclass
    class Case:
        att_meta_list: list[AttributeInstanceMeta]
        date_range: Range
        want_type: str

    cases = [
        Case(
            att_meta_list=MockProgram.planting_aim,
            # Jan only - mid summer AU
            date_range=Range(start=datetime(2022, 1, 1), end=datetime(2022, 1, 31)),
            want_type="summer_planting_date",
        ),
        Case(
            att_meta_list=MockProgram.planting_aim,
            # All of summer and first two months of autumn
            date_range=Range(start=datetime(2021, 12, 1), end=datetime(2022, 4, 30)),
            want_type="summer_planting_date",
        ),
        Case(
            att_meta_list=MockProgram.planting_aim,
            # Last month of summer, all of autumn and winter, plus a month of the next spring
            date_range=Range(start=datetime(2022, 2, 28), end=datetime(2022, 9, 30)),
            want_type="winter_planting_date",
        ),
        Case(
            att_meta_list=MockProgram.harvest_aim,
            # Mostly June so should get winter
            date_range=Range(start=datetime(2022, 5, 20), end=datetime(2022, 6, 30)),
            want_type="winter_harvest_date",
        ),
        Case(
            att_meta_list=MockProgram.tillage_practice_aim,
            # Single day in autumn
            date_range=Range(start=datetime(2022, 3, 9), end=datetime(2022, 4, 9)),
            want_type="fall_tillage_practice",
        ),
    ]
    for _, case in enumerate(cases):
        at = maximally_overlapping_attribute(case.att_meta_list, case.date_range)
        assert at.type == case.want_type


@pytest.mark.methods
def test_attribute_values_from_season_record():
    """Tests that the expected values are returned from a season record."""

    @dataclass
    class Case:
        season_record_json: str
        want_values: [SeasonValue]

    cases = [
        Case(season_record_json="{}", want_values=[]),
        Case(
            season_record_json="""{
                      "fieldID": 101781,
                      "cropType": "soybeans",
                      "startDate": "2020-05-23T00:00:00Z",
                      "endDate": "2020-09-29T00:00:00Z",
                      "external_service": "john_deere",
                      "params": {
                        "springTillage": "",
                        "fallTillage": "",
                        "winterCrop": ""
                      }
                    }
                """,
            want_values=[
                SeasonValue(
                    attribute_type=AttributeTypes.crop_type,
                    attribute_value="soybeans",
                    start_date=datetime(2020, 5, 23, tzinfo=timezone.utc),
                    end_date=datetime(2020, 9, 29, tzinfo=timezone.utc),
                    data_source=ImportDataSources.john_deere,
                ),
                SeasonValue(
                    attribute_type=AttributeTypes.record_year,
                    attribute_value=2020,
                    start_date=datetime(2020, 5, 23, tzinfo=timezone.utc),
                    end_date=datetime(2020, 9, 29, tzinfo=timezone.utc),
                    data_source=ImportDataSources.john_deere,
                ),
            ],
        ),
        # We're now ignoring tillage info from season records
        Case(
            season_record_json="""{
                  "fieldID": 101781,
                  "cropType": "soybeans",
                  "startDate": "2020-05-23T00:00:00Z",
                  "endDate": "2020-09-29T00:00:00Z",
                  "external_service": "john_deere",
                  "params": {
                    "springTillage": "NoTill",
                    "fallTillage": "ConvTill"
                  }
                }
            """,
            want_values=[
                SeasonValue(
                    attribute_type=AttributeTypes.crop_type,
                    attribute_value="soybeans",
                    start_date=datetime(2020, 5, 23, tzinfo=timezone.utc),
                    end_date=datetime(2020, 9, 29, tzinfo=timezone.utc),
                    data_source=ImportDataSources.john_deere,
                ),
                SeasonValue(
                    attribute_type=AttributeTypes.record_year,
                    attribute_value=2020,
                    start_date=datetime(2020, 5, 23, tzinfo=timezone.utc),
                    end_date=datetime(2020, 9, 29, tzinfo=timezone.utc),
                    data_source=ImportDataSources.john_deere,
                ),
            ],
        ),
    ]

    for case in cases:
        season_record = json.loads(case.season_record_json)
        attr_vals = methods.attribute_values_from_season_record(season_record)
        assert len(attr_vals) == len(case.want_values)
        for av in attr_vals:
            assert av in case.want_values


@pytest.mark.methods
def test_tillage_values_from_operation_record():
    """Tests that the expected tillage value(s) are returned from an operation record."""

    @dataclass
    class Case:
        operation_record_json: str
        want_values: [OperationValue]

    def want_val(op_area, att_type, att_val):
        return OperationValue(
            operation_area=op_area,
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=181333,
            operation_date=datetime(2018, 6, 26, 19, 48, 11),
            data_source=ImportDataSources.john_deere,
        )

    cases = [
        # empty
        Case(operation_record_json="{}", want_values=[]),
        # incomplete (missing 'type')
        Case(
            operation_record_json="""{
                  "field_id": 181333,
                  "external_service": "john_deere",
                  "attributes": {
                    "depth_target": {
                        "area": 8.8,
                        "depth_avg": 12.5
                    },
                    "depth_result": {
                        "area": 0,
                        "depth_avg": 0
                    }
                  },
                  "event_date": "2018-06-26T19:48:11"
                }
                """,
            want_values=[],
        ),
        # incomplete (missing 'attributes')
        Case(
            operation_record_json="""{
                  "type": "tillage",
                  "field_id": 181333,
                  "external_service": "john_deere",
                  "event_date": "2018-06-26T19:48:11"
                }
                """,
            want_values=[],
        ),
        # incomplete (empty 'attributes')
        Case(
            operation_record_json="""{
                  "type": "tillage",
                  "field_id": 181333,
                  "external_service": "john_deere",
                  "event_date": "2018-06-26T19:48:11",
                  "attributes": {}
                }
                """,
            want_values=[],
        ),
        # Tillage date and tillage depth
        Case(
            operation_record_json="""{
                      "type": "tillage",
                      "field_id": 181333,
                      "external_service": "john_deere",
                      "attributes": {
                        "depth_target": {
                            "area": 8.8,
                            "depth_avg": 12.5
                        },
                        "depth_result": {
                            "area": 0,
                            "depth_avg": 0
                        }
                      },
                      "event_date": "2018-06-26T19:48:11"
                    }
                    """,
            want_values=[
                want_val(8.8, AttributeTypes.tillage_depth, 12.5),
                want_val(8.8, AttributeTypes.tillage_date, "2018-06-26"),
                want_val(8.8, AttributeTypes.record_year, "2018"),
            ],
        ),
        # Tillage date and tillage depth, but prefer depth_actual_avg over depth_target_avg
        Case(
            operation_record_json="""{
                      "type": "tillage",
                      "field_id": 181333,
                      "external_service": "john_deere",
                      "attributes": {
                        "depth_target": {
                            "area": 8.8,
                            "depth_avg": 12.5
                        },
                        "depth_result": {
                            "area": 10.2,
                            "depth_avg": 11.8
                        }
                      },
                      "event_date": "2018-06-26T19:48:11"
                    }
                    """,
            want_values=[
                want_val(10.2, AttributeTypes.tillage_depth, 11.8),
                want_val(10.2, AttributeTypes.tillage_date, "2018-06-26"),
                want_val(10.2, AttributeTypes.record_year, "2018"),
            ],
        ),
    ]

    for case in cases:
        operation_record = json.loads(case.operation_record_json)
        attr_vals = methods.attribute_values_from_operation_record(operation_record)
        assert len(attr_vals) == len(case.want_values)
        for av in attr_vals:
            assert av in case.want_values


@pytest.mark.methods
def test_seeding_values_from_operation_record():
    """Tests that the expected seeding / planting value(s) are returned from an operation record."""

    @dataclass
    class Case:
        operation_record_json: str
        want_values: [OperationValue]

    def want_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=181333,
            operation_date=datetime(2018, 6, 26, 19, 48, 11),
            operation_area=0.0,
            data_source=ImportDataSources.john_deere,
        )

    cases = [
        Case(
            operation_record_json="""{
                      "type": "seeding",
                      "field_id": 181333,
                      "external_service": "john_deere",
                      "attributes": {
                        "crop_type": "CORN_WET",
                        "crop_variety": "DKC61-79"
                      },
                      "event_date": "2018-06-26T19:48:11"
                    }
                    """,
            want_values=[
                want_val(AttributeTypes.planting_date, "2018-06-26"),
                want_val(AttributeTypes.record_year, "2018"),
            ],
        ),
    ]

    for case in cases:
        operation_record = json.loads(case.operation_record_json)
        attr_vals = methods.attribute_values_from_operation_record(operation_record)
        assert len(attr_vals) == len(case.want_values)
        for av in attr_vals:
            assert av in case.want_values


@pytest.mark.methods
def test_harvest_values_from_operation_record():
    """Tests that the expected yield value(s) are returned from an operation record."""

    @dataclass
    class Case:
        operation_record_json: str
        want_values: [OperationValue]

    def want_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=180249,
            operation_date=datetime(2009, 10, 18, 15, 34, 46),
            operation_area=10.21023451569171,
            data_source=ImportDataSources.john_deere,
        )

    cases = [
        Case(
            operation_record_json="""{
                      "field_id": 180249,
                      "external_service": "john_deere",
                      "type": "yield",
                      "attributes": {
                        "area": 10.21023451569171,
                        "speed_avg": 7.612178200000001,
                        "yield_total": 5776.9,
                        "wet_mass_avg": 15647.850595,
                        "dry_volume_avg": 228.97,
                        "wet_mass_total": 159768.3002059312,
                        "moisture_pct_avg": 21.93
                      },
                      "event_date": "2009-10-18T15:34:46"
                    }
                    """,
            want_values=[
                want_val(AttributeTypes.harvest_date, "2009-10-18"),
                want_val(AttributeTypes.record_year, "2009"),
                want_val(AttributeTypes.crop_yield, 228.97),
            ],
        ),
    ]

    for case in cases:
        operation_record = json.loads(case.operation_record_json)
        attr_vals = methods.attribute_values_from_operation_record(operation_record)
        assert len(attr_vals) == len(case.want_values)
        for av in attr_vals:
            assert av in case.want_values


@pytest.mark.methods
def test_application_values_from_operation_record():
    """Tests that the expected application value(s) are returned from an operation record."""

    @dataclass
    class Case:
        operation_record_json: str
        want_values: [OperationValue]
        want_count: int
        product_skip_list: [str]

    def want_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=181372,
            operation_date=datetime(2013, 7, 22, 20, 37, 2),
            operation_area=0.13714448,
            data_source=ImportDataSources.john_deere,
        )

    cases = [
        # Empty product list
        Case(
            operation_record_json="""{
                              "id": 38,
                              "field_id": 181372,
                              "external_service": "john_deere",
                              "type": "application",
                              "attributes": {
                                "products": [],
                                "area_total": 0.13714448
                              },
                              "event_date": "2013-07-22T20:37:02"
                            }
                            """,
            want_values=[],
            want_count=0,
            product_skip_list=[],
        ),
        Case(
            operation_record_json="""{
                          "id": 38,
                          "field_id": 181372,
                          "external_service": "john_deere",
                          "type": "application",
                          "attributes": {
                            "products": [
                              {
                                "product_name": "Some Liquid Fertiliser",
                                "rate_avg": 4.314074,
                                "rate_unit": "l1ha-1"
                              },
                              {
                                "product_name": "Some Solid Fertiliser",
                                "rate_avg": 2.8760495,
                                "rate_unit": "kg1ha-1"
                              }
                            ],
                            "area_total": 0.13714448
                          },
                          "event_date": "2013-07-22T20:37:02"
                        }
                        """,
            want_values=[
                # First application
                want_val(AttributeTypes.application_date, "2013-07-22"),
                want_val(AttributeTypes.record_year, "2013"),
                want_val(AttributeTypes.application_area, 0.13714448),
                want_val(AttributeTypes.application_product, "Some Liquid Fertiliser"),
                want_val(AttributeTypes.application_rate, 4.314074),
                want_val(AttributeTypes.application_rate_type, ApplicationRateType.PRODUCT_RATE),
                want_val(AttributeTypes.application_rate_unit, "l1ha-1"),
                # Second application
                want_val(AttributeTypes.application_date, "2013-07-22"),
                want_val(AttributeTypes.record_year, "2013"),
                want_val(AttributeTypes.application_area, 0.13714448),
                want_val(AttributeTypes.application_product, "Some Solid Fertiliser"),
                want_val(AttributeTypes.application_rate, 2.8760495),
                want_val(AttributeTypes.application_rate_type, ApplicationRateType.PRODUCT_RATE),
                want_val(AttributeTypes.application_rate_unit, "kg1ha-1"),
                # Third application - should get skipped
                want_val(AttributeTypes.application_date, "2013-07-22"),
                want_val(AttributeTypes.record_year, "2013"),
                want_val(AttributeTypes.application_area, 0.13714448),
                want_val(AttributeTypes.application_product, "SKIP THIS PRODUCT"),
                want_val(AttributeTypes.application_rate, 2.8760495),
                want_val(AttributeTypes.application_rate_type, ApplicationRateType.PRODUCT_RATE),
                want_val(AttributeTypes.application_rate_unit, "kg1ha-1"),
            ],
            want_count=14,
            product_skip_list=["SKIP THIS PRODUCT", "AND THIS ONE TOO"],
        ),
    ]

    for case in cases:
        operation_record = json.loads(case.operation_record_json)
        # set to_metric as True to simplify this test as conversion is tested below
        attr_vals = methods.attribute_values_from_operation_record(
            operation_record, to_metric=True, product_skip_list=case.product_skip_list
        )
        assert len(attr_vals) == case.want_count
        for av in attr_vals:
            assert av in case.want_values


@pytest.mark.methods
async def test_get_operation_data_tillage():
    """Test that the expected tillage data is extracted from an operations payload, based on
    a specified attribute monitoring stages."""

    # Monitoring Stages
    def _monitor(att_type, att_id):
        return new_aim(
            att_type=att_type,
            attribute_id=att_id,
            stage_id=1,
            start_date=datetime(2013, 9, 1),
            end_date=datetime(2014, 2, 28),
        )

    monitored_attributes = [
        _monitor(AttributeTypes.record_year, 1),
        _monitor(AttributeTypes.spring_tillage_date, 2),
        _monitor(AttributeTypes.spring_tillage_depth, 3),
    ]
    monitoring_date_range = calculate_monitoring_date_range(monitored_attributes)

    operations = [
        {
            "id": 48,
            "field_id": 180247,
            "external_service": "john_deere",
            "type": "tillage",
            "attributes": {
                "depth_target": {"area": 17.0724, "depth_avg": 5.08},
            },
            "event_date": "2013-11-14T17:13:06",
        },
        {
            "id": 49,
            "field_id": 180247,
            "external_service": "john_deere",
            "type": "tillage",
            "attributes": {
                "depth_target": {"area": 25.0724, "depth_avg": 5.16},
            },
            "event_date": "2013-11-15T17:13:06",
        },
        # Note: external service value missing, so expect 'other_fms'
        {
            "id": 50,
            "field_id": 180247,
            "type": "tillage",
            "attributes": {
                "depth_target": {"area": 8.2765, "depth_avg": 5.18},
            },
            "event_date": "2013-11-17T17:13:06",
        },
        # Neither of these should get picked up due to 0 and negative values
        {
            "id": 51,
            "field_id": 180247,
            "type": "tillage",
            "attributes": {
                "depth_target": {"area": 8.2765, "depth_avg": 0.00},
            },
            "event_date": "2013-11-18T17:13:06",
        },
        {
            "id": 52,
            "field_id": 180247,
            "type": "tillage",
            "attributes": {
                "depth_target": {"area": 8.2765, "depth_avg": -5.5},
            },
            "event_date": "2013-11-17T19:13:06",
        },
    ]

    def want_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=180247,
            operation_area=17.0724,
            operation_date=datetime(2013, 11, 14),
            data_source=ImportDataSources.other_fms,
        )

    want = [
        want_val(AttributeTypes.record_year, "2013"),
        want_val(AttributeTypes.spring_tillage_date, "2013-11-14"),
        want_val(AttributeTypes.spring_tillage_depth, 5.08),
        want_val(AttributeTypes.record_year, "2013"),
        want_val(AttributeTypes.spring_tillage_date, "2013-11-15"),
        want_val(AttributeTypes.spring_tillage_depth, 5.16),
        want_val(AttributeTypes.record_year, "2013"),
        want_val(AttributeTypes.spring_tillage_date, "2013-11-17"),
        want_val(AttributeTypes.spring_tillage_depth, 5.18),
    ]

    do_no_want = [
        want_val(AttributeTypes.spring_tillage_date, "2013-11-18"),
        want_val(AttributeTypes.spring_tillage_depth, 0.0),
        want_val(AttributeTypes.spring_tillage_date, "2013-11-19"),
        want_val(AttributeTypes.spring_tillage_depth, -5.5),
    ]

    # No aggregation - expect a tillage date and depth for each record
    got = await methods.get_operation_data(
        operations=operations,
        monitoring_date_range=monitoring_date_range,
        mrv_field_id=1,
        monitored_attributes=monitored_attributes,
        progress_choice=ProgressChoices.monitoring,
    )

    for value in [values.attribute_value for values in want]:
        assert value in [values.value for values in got]

    for value in [values.attribute_value for values in do_no_want]:
        assert value not in [values.value for values in got]


@pytest.mark.methods
async def test_get_operation_data_yield():
    """Test that the expected harvest data is extracted from an operations payload. Harvest data is aggregated,
    and a weighted average is taken based on the area of each harvest operation."""

    operations = [
        {
            "id": 1,
            "field_id": 123,
            "external_service": "john_deere",
            "type": "yield",
            "attributes": {
                "area": 48.34,
                "dry_volume_avg": 228.97,
            },
            "event_date": "2020-11-03T10:36:00",  # <-- expect this date
        },
        {
            "id": 2,
            "field_id": 123,
            "external_service": "john_deere",
            "type": "yield",
            "attributes": {
                "area": 46.21,
                "dry_volume_avg": 245.23,
            },
            "event_date": "2020-11-06T06:23:00",
        },
        {
            "id": 3,
            "field_id": 123,
            "external_service": "john_deere",
            "type": "yield",
            "attributes": {
                "area": 16.45,
                "dry_volume_avg": 210.78,
            },
            "event_date": "2020-11-06T07:23:00",
        },
    ]

    def _monitor(att_type, att_id):
        return new_aim(
            att_type=att_type,
            attribute_id=att_id,
            stage_id=1,
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2020, 12, 31),
        )

    monitored_attributes = [
        _monitor(AttributeTypes.summer_harvest_date, 1),
        _monitor(AttributeTypes.summer_dry_yield, 2),
    ]
    monitoring_date_range = calculate_monitoring_date_range(monitored_attributes)

    sum_area = 48.34 + 46.21 + 16.45
    weighted_average = ((48.34 * 228.97) + (46.21 * 245.23) + (16.45 * 210.78)) / sum_area

    def want_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=123,
            operation_area=sum_area,
            operation_date=datetime(2020, 11, 3),
            data_source=ImportDataSources.john_deere,
        )

    want = [
        # sum area and date of op with most area covered
        want_val(AttributeTypes.summer_harvest_date, "2020-11-03"),
        # sum area, same date as above and weighted average
        want_val(AttributeTypes.summer_dry_yield, round(weighted_average, 8)),
    ]

    got = await methods.get_operation_data(
        operations=operations,
        monitoring_date_range=monitoring_date_range,
        mrv_field_id=1,
        monitored_attributes=monitored_attributes,
        progress_choice=ProgressChoices.monitoring,
    )

    for value_ in [value_.attribute_value for value_ in want]:
        assert value_ in [value_.value for value_ in got]


@pytest.mark.methods
async def test_get_operation_data_application():
    """Test that the expected application data is extracted from an operations payload."""

    operation_area = 10.51

    operations = [
        {
            "id": 1,
            "field_id": 123,
            "external_service": "john_deere",
            "type": "application",
            "attributes": {
                "products": [
                    {
                        "rate_avg": 116.77,
                        "rate_unit": "l1ha-1",
                        "product_name": "32-0-0 UAN",
                    },
                    # {"rate_avg": 1.53, "rate_unit": "l1ha-1", "product_name": "Outlook"},
                    # {"rate_avg": 1.16, "rate_unit": "l1ha-1", "product_name": "Touchdown Total"}
                ],
                "area_total": operation_area,
            },
            "event_date": "2020-11-03T10:36:00",  # <-- expect this date
        },
    ]

    def _monitor(att_type, att_id):
        return new_aim(
            att_type=att_type,
            attribute_id=att_id,
            stage_id=1,
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2020, 12, 31),
        )

    monitored_attributes = [
        _monitor(AttributeTypes.application_date, 1),
        _monitor(AttributeTypes.application_product, 2),
        _monitor(AttributeTypes.application_rate, 3),
        _monitor(AttributeTypes.application_rate_unit, 4),
        _monitor(AttributeTypes.application_area, 5),
        # todo: test application type
    ]
    monitoring_date_range = calculate_monitoring_date_range(monitored_attributes)

    def _op_val(att_type, att_val):
        return OperationValue(
            attribute_type=att_type,
            attribute_value=att_val,
            field_id=123,
            operation_area=operation_area,
            operation_date=datetime(2020, 11, 3),
            data_source=ImportDataSources.john_deere,
        )

    want = [
        # date
        _op_val(AttributeTypes.application_date, "2020-11-03"),
        # product
        _op_val(AttributeTypes.application_product, "32-0-0 UAN"),
        # rate
        _op_val(AttributeTypes.application_rate, 116.77),
        # unit
        _op_val(AttributeTypes.application_rate_unit, "l1ha-1"),
        # area
        _op_val(AttributeTypes.application_area, operation_area),
        # type
        # want_val(AttributeTypes.application_type, ""),
    ]

    got = await methods.get_operation_data(
        operations=operations,
        monitoring_date_range=monitoring_date_range,
        mrv_field_id=1,
        monitored_attributes=monitored_attributes,
        progress_choice=ProgressChoices.monitoring,
    )

    for value_ in [value_.attribute_value for value_ in want]:
        assert value_ in [value_.value for value_ in got]


@pytest.mark.methods
async def test_get_season_data():
    def _monitor(att_type, att_id):
        return new_aim(
            att_type=att_type,
            attribute_id=att_id,
            stage_id=1,
            start_date=datetime(2021, 10, 1),
            end_date=datetime(2022, 6, 20),
        )

    # Monitoring Stages
    monitoring_crop_type = [
        _monitor(AttributeTypes.record_year, 1),
        _monitor(AttributeTypes.summer_crop_type, 2),
    ]
    monitoring_date_range = calculate_monitoring_date_range(monitoring_crop_type)

    # Note there are a bunch of season records that have same crop type
    # but slightly different dates. This emulates a current issue with fetching
    # seasons from core and these are here to ensure we only get one value from them.
    seasons = [
        # some winter cover with end date before the monitoring period starts
        {
            "id": 1,
            "fieldID": 99,
            "cropType": "rye",
            "startDate": "2021-04-01T00:00:00Z",
            "endDate": "2021-09-30T00:00:00Z",
            "external_service": "john_deere",
        },
        # two corn season records, both have end date in the monitoring period
        {
            "id": 2,
            "fieldID": 99,
            "cropType": "corn",
            "startDate": "2021-10-01T00:00:00Z",
            "endDate": "2022-02-28T00:00:00Z",
            "external_service": "john_deere",
        },
        {
            "id": 3,
            "fieldID": 99,
            "cropType": "corn",
            "startDate": "2021-11-01T00:00:00Z",
            "endDate": "2022-03-28T00:00:00Z",
            "external_service": "john_deere",
        },
        # two soybean season records, only ONE has end date in the monitoring period
        {
            "id": 4,
            "fieldID": 99,
            "cropType": "soybean",
            "startDate": "2021-10-01T00:00:00Z",
            "endDate": "2022-02-28T00:00:00Z",
            "external_service": "john_deere",
        },
        {
            "id": 5,
            "fieldID": 99,
            "cropType": "soybean",
            "startDate": "2022-02-01T00:00:00Z",
            "endDate": "2022-10-30T00:00:00Z",
            "external_service": "john_deere",
        },
    ]

    # corn season records are in the majority, so should be returned
    stage_crop_types = {1: ["corn", "soybeans"]}
    values = await methods.get_season_data(
        seasons=seasons,
        monitoring_date_range=monitoring_date_range,
        mrv_field_id=99,
        monitored_attributes=monitoring_crop_type,
        progress_choice=ProgressChoices.monitoring,
        stage_ids_to_crop_list=stage_crop_types,
    )
    assert len(values) == 2
    assert values[0].attribute_id == 1
    assert values[0].value == 2022
    assert values[1].attribute_id == 2
    assert values[1].value == "corn"

    # try again, but this time we're monitoring different crop types so should expect no results
    stage_crop_types = {1: ["apples", "oranges", "watermelons"]}
    values = await methods.get_season_data(
        seasons=seasons,
        monitoring_date_range=monitoring_date_range,
        mrv_field_id=99,
        monitored_attributes=monitoring_crop_type,
        progress_choice=ProgressChoices.monitoring,
        stage_ids_to_crop_list=stage_crop_types,
    )
    assert len(values) == 0

    # todo: have introduced a very subtle bug with stage crop types... currently the crop type that has the majority
    # of season records will prevail, but if it is NOT in the list then we don't fall back to the next in line.
    # This would be almost never an issue but still need to sort it out.


@pytest.mark.methods
def test_weighted_average():
    @dataclass
    class Case:
        values: list[float]
        weights: list[float]
        want: float

    cases = [
        Case(
            values=[1, 2, 3],
            weights=[1, 1, 1],
            want=2,
        ),
        Case(
            values=[134.0, 234.0, 186.7],
            weights=[26.23, 22.75, 12.56],
            want=181.72362691,  # <-- default rounding is 8 decimal places
        ),
    ]
    for case in cases:
        assert methods.weighted_average(case.values, case.weights) == case.want


@pytest.mark.methods
def test_convert_product_rate():
    lha = "l1ha-1"
    kgha = "kg1ha-1"
    galac = "gal1ac-1"
    lbac = "lb1ac-1"

    # pre-converted equivalent volume rates
    x_l_per_ha = 426.5
    x_gal_per_ac = 45.6

    # pre-converted equivalent mass rates
    x_kg_per_ha = 10.8
    x_lb_per_ac = 9.6

    prod_metric_vol = {"rate_avg": x_l_per_ha, "rate_unit": lha}
    prod_metric_mass = {"rate_avg": x_kg_per_ha, "rate_unit": kgha}
    prod_imperial_vol = {"rate_avg": x_gal_per_ac, "rate_unit": galac}
    prod_imperial_mass = {"rate_avg": x_lb_per_ac, "rate_unit": lbac}

    @dataclass
    class Case:
        product: dict
        to_metric: bool
        want_err: bool
        want_rate: float

    cases = [
        # error cases - func doesn't care about 'product_name' so not required here
        Case(
            product={},  # nada
            to_metric=True,
            want_err=True,
            want_rate=0,
        ),
        Case(
            product={"rate_avg": x_l_per_ha},  # missing rate_unit
            to_metric=True,
            want_err=True,
            want_rate=0,
        ),
        Case(
            product={"rate_unit": lha},  # missing rate_avg
            to_metric=True,
            want_err=True,
            want_rate=0,
        ),
        Case(
            product={
                "rate_avg": x_l_per_ha,
                "rate_unit": "mg1ha-1",
            },  # unhandled rate_unit
            to_metric=True,
            want_err=True,
            want_rate=0,
        ),
        # Success cases - volumes
        Case(
            product=prod_metric_vol,
            to_metric=True,
            want_err=False,
            want_rate=x_l_per_ha,
        ),
        Case(
            product=prod_metric_vol,
            to_metric=False,  # <-- convert to gal/acre
            want_err=False,
            want_rate=x_gal_per_ac,
        ),
        Case(
            product=prod_imperial_vol,
            to_metric=False,
            want_err=False,
            want_rate=x_gal_per_ac,
        ),
        Case(
            product=prod_imperial_vol,
            to_metric=True,  # Convert to l/ha
            want_err=False,
            want_rate=x_l_per_ha,
        ),
        # Success cases - mass
        Case(
            product=prod_metric_mass,
            to_metric=True,
            want_err=False,
            want_rate=x_kg_per_ha,
        ),
        Case(
            product=prod_metric_mass,
            to_metric=False,  # <-- convert to lb/acre
            want_err=False,
            want_rate=x_lb_per_ac,
        ),
        Case(
            product=prod_imperial_mass,
            to_metric=False,
            want_err=False,
            want_rate=x_lb_per_ac,
        ),
        Case(
            product=prod_imperial_mass,
            to_metric=True,  # convert to kg/ha
            want_err=False,
            want_rate=x_kg_per_ha,
        ),
    ]

    for i, case in enumerate(cases):
        if case.want_err:
            with pytest.raises(ValueError) as _:
                methods.convert_product_rate(product=case.product, to_metric=case.to_metric)
        else:
            got = round(
                methods.convert_product_rate(product=case.product, to_metric=case.to_metric),
                1,
            )
            want = round(case.want_rate, 1)
            assert got == want, f"case[{i}]"


@pytest.mark.methods
def test_aggregate_operation_values():
    # aggregated planting dates
    summer_planting_date = AttributeTypes.summer_planting_date
    winter_planting_date = AttributeTypes.winter_planting_date
    planting_date = AttributeTypes.planting_date

    # Aggregated in pairs, ie harvest_date + crop_yield
    summer_harvest_date = AttributeTypes.summer_harvest_date
    summer_dry_yield = AttributeTypes.summer_dry_yield
    winter_harvest_date = AttributeTypes.winter_harvest_date
    winter_dry_yield = AttributeTypes.winter_dry_yield
    harvest_date = AttributeTypes.harvest_date
    crop_yield = AttributeTypes.crop_yield

    # a timeline of three days
    dt0 = datetime.today() - timedelta(days=2)
    dt1 = datetime.today() - timedelta(days=1)
    dt2 = datetime.today()

    # Helper to create an op val
    def op_val(date: datetime, area: float, att: AttributeTypes, att_val: any) -> OperationValue:
        return OperationValue(
            field_id=1111,
            operation_date=date,
            attribute_type=att,
            attribute_value=att_val,
            data_source=ImportDataSources.user,
            operation_area=area,
        )

    @dataclass
    class Case:
        arg: dict[int, list[OperationValue]]
        want: dict[int, list[OperationValue]]

    cases = [
        Case(
            arg={1: []},
            want={1: []},
        ),
        # 3 planting dates - want op with the largest area
        Case(
            arg={
                1: [
                    op_val(date=dt0, area=10.5, att=summer_planting_date, att_val=dt0),
                    op_val(date=dt1, area=22.3, att=summer_planting_date, att_val=dt1),
                    op_val(date=dt2, area=12.8, att=summer_planting_date, att_val=dt2),
                ],
            },
            want={
                1: [op_val(date=dt1, area=22.3, att=summer_planting_date, att_val=dt1)],
            },
        ),
        # 3 planting dates with same area, want the latest date
        Case(
            arg={
                1: [
                    op_val(date=dt0, area=10.5, att=winter_planting_date, att_val=dt0),
                    op_val(date=dt1, area=10.5, att=winter_planting_date, att_val=dt1),
                    op_val(date=dt2, area=10.5, att=winter_planting_date, att_val=dt2),
                ],
            },
            want={
                1: [op_val(date=dt2, area=10.5, att=winter_planting_date, att_val=dt2)],
            },
        ),
        # 3 planting ops with same date, want the largest area
        Case(
            arg={
                1: [
                    op_val(date=dt0, area=10.5, att=planting_date, att_val=dt0),
                    op_val(date=dt0, area=10.7, att=planting_date, att_val=dt0),
                    op_val(date=dt0, area=10.6, att=planting_date, att_val=dt0),
                ],
            },
            want={
                1: [op_val(date=dt0, area=10.7, att=planting_date, att_val=dt0)],
            },
        ),
        # Unmatched harvest tuples should be returned without modification
        Case(
            arg={
                1: [op_val(date=dt0, area=10.5, att=summer_planting_date, att_val=dt0)],
                2: [op_val(date=dt0, area=10.5, att=summer_harvest_date, att_val=dt0)],
                # for aggregation this would have to be summer_dry_yield,
                3: [
                    op_val(date=dt0, area=10.5, att=winter_dry_yield, att_val=60.8),
                    op_val(date=dt1, area=20.5, att=winter_dry_yield, att_val=113.4),
                    op_val(date=dt1, area=15.5, att=winter_dry_yield, att_val=86.7),
                ],
            },
            want={
                1: [op_val(date=dt0, area=10.5, att=summer_planting_date, att_val=dt0)],
                2: [op_val(date=dt0, area=10.5, att=summer_harvest_date, att_val=dt0)],
                3: [
                    op_val(date=dt0, area=10.5, att=winter_dry_yield, att_val=60.8),
                    op_val(date=dt1, area=20.5, att=winter_dry_yield, att_val=113.4),
                    op_val(date=dt1, area=15.5, att=winter_dry_yield, att_val=86.7),
                ],
            },
        ),
        # Aggregated summer crop
        Case(
            arg={
                1: [
                    # this planting date op should be selected as it has the biggest area
                    op_val(date=dt0, area=86.5, att=summer_planting_date, att_val=dt0),
                    op_val(date=dt0, area=45.5, att=summer_planting_date, att_val=dt0),
                    op_val(date=dt1, area=47.7, att=summer_planting_date, att_val=dt1),
                    op_val(date=dt1, area=10.5, att=summer_planting_date, att_val=dt1),
                    op_val(date=dt2, area=65.7, att=summer_planting_date, att_val=dt2),
                    op_val(date=dt2, area=12.2, att=summer_planting_date, att_val=dt2),
                ],
                2: [
                    op_val(date=dt1, area=12.5, att=summer_harvest_date, att_val=dt1),
                    op_val(date=dt1, area=20.5, att=summer_harvest_date, att_val=dt1),
                    # expect this harvest date op with largest area
                    op_val(date=dt2, area=36.5, att=summer_harvest_date, att_val=dt2),
                    op_val(date=dt2, area=10.5, att=summer_harvest_date, att_val=dt2),
                ],
                3: [
                    op_val(date=dt1, area=12.5, att=summer_dry_yield, att_val=60.8),
                    op_val(date=dt1, area=20.5, att=summer_dry_yield, att_val=113.4),
                    op_val(date=dt2, area=36.5, att=summer_dry_yield, att_val=125.6),
                    op_val(date=dt2, area=10.5, att=summer_dry_yield, att_val=58.8),
                ],
            },
            want={
                1: [op_val(date=dt0, area=86.5, att=summer_planting_date, att_val=dt0)],
                2: [op_val(date=dt2, area=36.5, att=summer_harvest_date, att_val=dt2)],
                # total area, weighted average of yield values which are mass/area
                3: [
                    op_val(
                        date=dt2,
                        area=80,
                        att=summer_dry_yield,
                        att_val=methods.weighted_average(
                            values=[60.8, 113.4, 125.6, 58.8],  # yield, eg kg/ha
                            weights=[12.5, 20.5, 36.5, 10.5],  # harvest op areas
                        ),
                    )
                ],
            },
        ),
        # Aggregated winter crop - as above, but with winter attributes
        Case(
            arg={
                1: [
                    # this planting date op should be selected as it has the biggest area
                    op_val(date=dt0, area=86.5, att=winter_planting_date, att_val=dt0),
                    op_val(date=dt0, area=45.5, att=winter_planting_date, att_val=dt0),
                    op_val(date=dt1, area=47.7, att=winter_planting_date, att_val=dt1),
                    op_val(date=dt1, area=10.5, att=winter_planting_date, att_val=dt1),
                    op_val(date=dt2, area=65.7, att=winter_planting_date, att_val=dt2),
                    op_val(date=dt2, area=12.2, att=winter_planting_date, att_val=dt2),
                ],
                2: [
                    op_val(date=dt1, area=12.5, att=winter_harvest_date, att_val=dt1),
                    op_val(date=dt1, area=20.5, att=winter_harvest_date, att_val=dt1),
                    # expect this harvest date op with largest area
                    op_val(date=dt2, area=36.5, att=winter_harvest_date, att_val=dt2),
                    op_val(date=dt2, area=10.5, att=winter_harvest_date, att_val=dt2),
                ],
                3: [
                    op_val(date=dt1, area=12.5, att=winter_dry_yield, att_val=60.8),
                    op_val(date=dt1, area=20.5, att=winter_dry_yield, att_val=113.4),
                    op_val(date=dt2, area=36.5, att=winter_dry_yield, att_val=125.6),
                    op_val(date=dt2, area=10.5, att=winter_dry_yield, att_val=58.8),
                ],
            },
            want={
                1: [op_val(date=dt0, area=86.5, att=winter_planting_date, att_val=dt0)],
                2: [op_val(date=dt2, area=36.5, att=winter_harvest_date, att_val=dt2)],
                # total area, weighted average of yield values which are mass/area
                3: [
                    op_val(
                        date=dt2,
                        area=80,
                        att=winter_dry_yield,
                        att_val=methods.weighted_average(
                            values=[60.8, 113.4, 125.6, 58.8],  # yield, eg kg/ha
                            weights=[12.5, 20.5, 36.5, 10.5],  # harvest op areas
                        ),
                    )
                ],
            },
        ),
        # Aggregated crop - as above, but with non-season attributes
        Case(
            arg={
                1: [
                    # this planting date op should be selected as it has the biggest area
                    op_val(date=dt0, area=86.5, att=planting_date, att_val=dt0),
                    op_val(date=dt0, area=45.5, att=planting_date, att_val=dt0),
                    op_val(date=dt1, area=47.7, att=planting_date, att_val=dt1),
                    op_val(date=dt1, area=10.5, att=planting_date, att_val=dt1),
                    op_val(date=dt2, area=65.7, att=planting_date, att_val=dt2),
                    op_val(date=dt2, area=12.2, att=planting_date, att_val=dt2),
                ],
                2: [
                    op_val(date=dt1, area=12.5, att=harvest_date, att_val=dt1),
                    op_val(date=dt1, area=20.5, att=harvest_date, att_val=dt1),
                    # expect this harvest date op with largest area
                    op_val(date=dt2, area=36.5, att=harvest_date, att_val=dt2),
                    op_val(date=dt2, area=10.5, att=harvest_date, att_val=dt2),
                ],
                3: [
                    op_val(date=dt1, area=12.5, att=crop_yield, att_val=60.8),
                    op_val(date=dt1, area=20.5, att=crop_yield, att_val=113.4),
                    op_val(date=dt2, area=36.5, att=crop_yield, att_val=125.6),
                    op_val(date=dt2, area=10.5, att=crop_yield, att_val=58.8),
                ],
            },
            want={
                1: [op_val(date=dt0, area=86.5, att=planting_date, att_val=dt0)],
                2: [op_val(date=dt2, area=36.5, att=harvest_date, att_val=dt2)],
                # total area, weighted average of yield values which are mass/area
                3: [
                    op_val(
                        date=dt2,
                        area=80,
                        att=crop_yield,
                        att_val=methods.weighted_average(
                            values=[60.8, 113.4, 125.6, 58.8],  # yield, eg kg/ha
                            weights=[12.5, 20.5, 36.5, 10.5],  # harvest op areas
                        ),
                    )
                ],
            },
        ),
    ]

    for i, case in enumerate(cases):
        got = methods.aggregate_operation_values(case.arg)
        assert got == case.want, f"case[{i}]"


@patch.object(
    external_api,
    "core_users_get_latest_syncs",
    external_api_mocked_responses.mocked_core_users_get_latest_syncs,
)
@pytest.mark.methods
async def test_core_users_get_user_latest_syncs():
    core_response = await external_api.core_users_get_latest_syncs([2])
    assert datetime.strptime(core_response.json()["result"]["2"]["last_login_time"], "%Y-%m-%dT%H:%M:%SZ")
    assert datetime.strptime(
        core_response.json()["result"]["2"]["latest_syncs"][0]["latest_import"],
        "%Y-%m-%dT%H:%M:%SZ",
    )
    assert core_response.json()["result"]["2"]["latest_syncs"][0]["external_service"] == "productionwise"


@pytest.mark.methods
async def test_distinct_ordered_seasons():
    arg = [
        {
            "cropType": "soybeans",
            "startDate": "2017-06-01T00:00:00Z",
            "endDate": "2017-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2017-06-01T00:00:00Z",
            "endDate": "2017-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2018-06-01T00:00:00Z",
            "endDate": "2018-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2018-06-01T00:00:00Z",
            "endDate": "2018-09-30T00:00:00Z",
        },
        {
            "cropType": "fallow",
            "startDate": "2019-06-01T00:00:00Z",
            "endDate": "2019-09-30T00:00:00Z",
        },
        {
            "cropType": "fallow",
            "startDate": "2019-06-01T00:00:00Z",
            "endDate": "2019-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2020-05-31T00:00:00Z",
            "endDate": "2020-09-29T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2020-06-01T00:00:00Z",
            "endDate": "2020-09-30T00:00:00Z",
        },
        {
            "cropType": "corn",
            "startDate": "2020-06-01T00:00:00Z",
            "endDate": "2020-09-30T00:00:00Z",
        },
        {
            "cropType": "corn",
            "startDate": "2020-06-01T00:00:00Z",
            "endDate": "2020-09-30T00:00:00Z",
        },
        {
            "cropType": "corn",
            "startDate": "2020-06-01T00:00:00Z",
            "endDate": "2020-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2021-05-30T00:00:00Z",
            "endDate": "2021-09-28T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2021-06-01T00:00:00Z",
            "endDate": "2021-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2021-06-01T00:00:00Z",
            "endDate": "2021-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2021-06-01T00:00:00Z",
            "endDate": "2021-09-30T00:00:00Z",
        },
    ]

    want = [
        {
            "cropType": "soybeans",
            "startDate": "2021-05-30T00:00:00Z",
            "endDate": "2021-09-28T00:00:00Z",
        },
        {
            "cropType": "corn",
            "startDate": "2020-06-01T00:00:00Z",
            "endDate": "2020-09-30T00:00:00Z",
        },
        {
            "cropType": "fallow",
            "startDate": "2019-06-01T00:00:00Z",
            "endDate": "2019-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2018-06-01T00:00:00Z",
            "endDate": "2018-09-30T00:00:00Z",
        },
        {
            "cropType": "soybeans",
            "startDate": "2017-06-01T00:00:00Z",
            "endDate": "2017-09-30T00:00:00Z",
        },
    ]

    got = methods.distinct_ordered_seasons(arg)
    assert got == want


@pytest.mark.methods
async def test_ordered_operations():
    arg = [
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 6, "depth_avg": 6},
            },
            "event_date": "2018-06-26T19:48:11",
        },
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 7, "depth_avg": 7},
            },
            "event_date": "2018-07-26T19:48:11",
        },
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 5, "depth_avg": 5},
            },
            "event_date": "2018-05-26T19:48:11",
        },
    ]

    want = [
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 7, "depth_avg": 7},
            },
            "event_date": "2018-07-26T19:48:11",
        },
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 6, "depth_avg": 6},
            },
            "event_date": "2018-06-26T19:48:11",
        },
        {
            "field_id": 123,
            "external_service": "john_deere",
            "attributes": {
                "depth_result": {"area": 5, "depth_avg": 5},
            },
            "event_date": "2018-05-26T19:48:11",
        },
    ]

    got = methods.ordered_operations(arg)
    assert got == want


@pytest.mark.methods
def test_has_all_permissions():
    @dataclass
    class Case:
        permissions_response: schema.UserPermissionsResponse
        program_id: int
        required_permissions: list[str]
        project_id: int | None
        want: bool

    def prog_roles_obj(prog_id, perms):
        return {
            "program_id": prog_id,
            "program_name": "Test Program",
            "permissions": perms,
            "role": "PRODUCER",
            "role_name": "Producer",
            "role_info": {
                "id": 1,
                "name": "Producer",
                "role_type": "PRODUCER",
                "program_id": None,
                "restricted": False,
                "group_management": False,
            },
        }

    test_permissions = {
        "A": Permission.CREATE_ROLES.name,
        "B": Permission.CREATE_PROGRAMS.name,
        "C": Permission.ADD_ASSETS.name,
    }

    user_permissions = {
        "project_producer_111": schema.UserPermissionsResponse(
            program_admin=[],
            program_producer=[],
            project_producer=[111],
            program_roles={
                1: prog_roles_obj(1, [test_permissions["A"], test_permissions["B"], test_permissions["C"]]),
                2: prog_roles_obj(2, [test_permissions["A"], test_permissions["B"]]),
            },
            original_user_program_roles={
                1: prog_roles_obj(1, [test_permissions["A"], test_permissions["B"], test_permissions["C"]]),
                2: prog_roles_obj(2, [test_permissions["A"], test_permissions["B"]]),
            },
        ),
    }

    cases = [
        # program-level permissions check - ie no project id
        Case(
            permissions_response=user_permissions["project_producer_111"],
            program_id=1,
            required_permissions=[test_permissions["A"], test_permissions["B"], test_permissions["C"]],
            project_id=None,
            want=True,
        ),
        Case(
            permissions_response=user_permissions["project_producer_111"],
            program_id=2,
            required_permissions=[test_permissions["A"], test_permissions["B"]],
            project_id=None,
            want=True,
        ),
        Case(
            permissions_response=user_permissions["project_producer_111"],
            program_id=2,
            required_permissions=[test_permissions["A"], test_permissions["B"], test_permissions["C"]],  # missing "C"
            project_id=None,
            want=False,
        ),
        # permissions check including project id
        Case(
            permissions_response=user_permissions["project_producer_111"],
            program_id=1,
            required_permissions=[test_permissions["A"], test_permissions["B"], test_permissions["C"]],
            project_id=111,  # must also have explicit producer access to this project, which is True
            want=True,
        ),
        Case(
            permissions_response=user_permissions["project_producer_111"],
            program_id=1,
            required_permissions=[test_permissions["A"], test_permissions["B"], test_permissions["C"]],
            project_id=222,  # do NOT have explicit project access to this one
            want=False,
        ),
    ]

    for i, case in enumerate(cases):
        got = methods.has_all_permissions(
            permissions_response=case.permissions_response,
            program_id=case.program_id,
            required_permissions=case.required_permissions,
            project_id=case.project_id,
        )
        assert got == case.want, f"case[{i}]"
