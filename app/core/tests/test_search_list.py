from core.methods import search_list


def test_match_product_skip_list_match_exact():

    skip_list_str = """GLYPHOSATE\n2,4-D\nGLUFOSINATE\nDICAMBA\nATRAZINE\nXTENDIMAX\n"""

    haystack = search_list.SearchList(skip_list_str)

    cases = [
        ("AGL<PERSON><PERSON><PERSON><PERSON><PERSON>", False),
        ("G<PERSON>YPH<PERSON>", False),
        ("GLYPHOSATE", True),
        ("Glyphosate", True),
        ("glyphosate", True),
        (" ##$$%% --- G L y -- %$^%$*^%$^ ph O--SAT    e ----", True),
        ("2, 4 - D", True),
        ("24d", True),
        ("24DD", False),
        ("XtendiMax™ with VaporGrip™ Technology", False),  # using exact matches only
        ("XTENDIMAX", True),
    ]

    for i, case in enumerate(cases):
        assert haystack.match(case[0]) == case[1], f"cases[{i}]"


def test_match_product_skip_list_match_start():

    skip_list = [
        "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "2,4-D",
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>AM<PERSON>",
        "ATRAZINE",
        "XTENDIMAX",
    ]

    haystack = search_list.SearchList(skip_list, match_start_chars=5)

    cases = [
        ("AGLYPHOSATE", False),  # start does not match
        ("GLYP", False),  # too short for substring match
        ("GLYPHO", True),  # should match
        ("GLYPHOSATE", True),
        ("Glyphosate", True),
        ("glyphosate", True),
        (" ##$$%% --- G L y -- %$^%$*^%$^ ph O--SAT    e ----", True),
        ("2, 4 - D", True),
        ("24d", True),
        ("24DD", False),
        ("XtendiMax™ with VaporGrip™ Technology", True),  # should match start
        ("XTENDIMAX", True),
    ]

    for i, case in enumerate(cases):
        assert haystack.match(case[0]) == case[1], f"cases[{i}]"
