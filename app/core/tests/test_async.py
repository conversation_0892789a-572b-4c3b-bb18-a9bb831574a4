from unittest.mock import patch

import httpx
from fastapi import status
from sqlalchemy import and_

from core import paths
from helper import (
    crop_type_selections,
    crop_type_selections_mocked,
    external_api,
    external_api_mocked_responses,
)
from permissions.enums import DefaultRoles, Permission, RoleTypes
from permissions.model import Roles
from programs import paths as programs_paths
from projects import paths as projects_paths
from projects.model import ProjectPermissions, Projects
from root_crud import delete, get


@patch.object(
    crop_type_selections,
    "get_summer_crops",
    crop_type_selections_mocked.mocked_get_summer_crops,
)
@patch.object(
    crop_type_selections,
    "get_winter_crops",
    crop_type_selections_mocked.mocked_get_winter_crops,
)
@patch.object(
    external_api,
    "core_crops_get_api",
    external_api_mocked_responses.mocked_core_crops_get_api,
)
async def test_users_permission(
    async_client: httpx.AsyncClient, grant_all_permissions_factory, create_all_regular_roles_factory
):
    await create_all_regular_roles_factory()
    response = await async_client.post(
        f"{programs_paths.base}",
        json=[
            {
                "name": "string",
                "crediting_year": "2021",
                "reporting_period_start_date": "2022-01-01 00:00:00",
                "reporting_period_end_date": "2022-12-31 23:59:59",
                "version": 0,
                "description": "string",
                "carbon_price": 0,
            }
        ],
        params={"create_monitoring": False},
    )

    assert response.status_code == status.HTTP_201_CREATED, response.content
    program_id = response.json()[0]["id"]

    response = await async_client.post(
        f"{programs_paths.base}/{program_id}{programs_paths.users}",
        json=[{"user_id": 101}],
    )
    assert response.status_code == status.HTTP_201_CREATED, response.content

    response = await async_client.post(f"{programs_paths.base}/{program_id}/project")
    assert response.status_code == status.HTTP_201_CREATED, response.content
    project_id = response.json()["id"]

    response = await async_client.post(
        f"{projects_paths.base}/{project_id}{projects_paths.users}",
        json=[{"user": 101}],
    )
    assert response.status_code == status.HTTP_409_CONFLICT, response.content

    response = await async_client.post(
        f"{projects_paths.base}/{project_id}{projects_paths.users}",
        json=[{"user": 111}],
    )
    assert response.status_code == status.HTTP_201_CREATED, response.content

    response = await async_client.post(
        f"{programs_paths.base}/{program_id}{programs_paths.users}",
        json=[{"user_id": 111}],
    )
    assert response.status_code == status.HTTP_409_CONFLICT, response.content

    await grant_all_permissions_factory()
    response = await async_client.get(f"{paths.user}/101", headers={"fs-super-admin": "true"})

    assert response.status_code == status.HTTP_200_OK, response.content
    assert program_id in response.json()["program_admin"]
    assert project_id not in response.json()["project_producer"]

    response = await async_client.get(f"{paths.user}/111", headers={"fs-super-admin": "true"})

    assert response.status_code == status.HTTP_200_OK
    assert program_id not in response.json()["program_admin"]
    assert project_id in response.json()["project_producer"]


async def test_user_fs_impersonator_program_id(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    """
    Tests that when impersonations are done, that users are only allowed to see the impersonated program.
    """
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)
    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    program2 = await mdl.Programs()
    project2 = await mdl.Projects(program_id=program2.id)
    await mdl.ProjectPermissions(user=user_id, project=project1.id)
    perm2 = await mdl.ProjectPermissions(user=user_id, project=project2.id)
    # Make request with impersonation header
    response = await async_client.get(
        f"{paths.user}/{user_id}",
        headers={"fs-impersonator-program-id": str(program1.id)},
    )
    assert response.status_code == status.HTTP_200_OK, response.content
    # Impersonated project present
    assert project1.id in response.json()["project_producer"]
    # Other project not present
    assert project2.id not in response.json()["project_producer"]
    # No impersonation
    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content
    # Both projects present
    assert project1.id in response.json()["project_producer"]
    assert project2.id in response.json()["project_producer"]

    # Delete the project and the other project permission
    await delete.soft(request=app_request, ids=[project1.id], orm_type=Projects, id_field=Projects.id)
    await delete.soft(
        request=app_request,
        ids=[perm2.id],
        orm_type=ProjectPermissions,
        id_field=ProjectPermissions.id,
    )
    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content
    # Check all permissions revoked
    assert project1.id not in response.json()["project_producer"]
    assert project2.id not in response.json()["project_producer"]


async def test_users_permission_with_roles(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)
    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    program2 = await mdl.Programs()
    project2 = await mdl.Projects(program_id=program2.id)
    await mdl.ProjectPermissions(user=user_id, project=project1.id)
    await mdl.ProjectPermissions(user=user_id, project=project2.id)

    # assign roles and permissions
    role1 = await mdl.Roles(name=DefaultRoles.PRODUCER.value, role_type=RoleTypes.PRODUCER)
    role2 = await mdl.Roles(name=DefaultRoles.PROGRAM_ADMIN.value, role_type=RoleTypes.ADMIN)
    await mdl.RolesUsers(fs_user_id=user_id, role_id=role1.id, program_id=program1.id)
    await mdl.RolesUsers(fs_user_id=user_id, role_id=role2.id, program_id=program2.id)
    await mdl.RolesPermissions(role_id=role1.id, permission=Permission.GET_PERMISSIONS.name)
    await mdl.RolesPermissions(role_id=role2.id, permission=Permission.GET_ASSETS.name)

    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content

    assert response.json()["program_roles"] == {
        str(program1.id): {
            "program_id": program1.id,
            "program_name": program1.name,
            "permissions": [Permission.GET_PERMISSIONS.name],
            "role": DefaultRoles.PRODUCER.name,
            "role_name": DefaultRoles.PRODUCER.value,
            "role_info": role1.to_dict(),
        },
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [Permission.GET_ASSETS.name],
            "role": DefaultRoles.PROGRAM_ADMIN.name,
            "role_name": DefaultRoles.PROGRAM_ADMIN.value,
            "role_info": role2.to_dict(),
        },
    }
    assert response.json()["original_user_program_roles"] == response.json()["program_roles"]


async def test_users_permission_with_impersonation(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)
    impersonator_user_id = faker.random_number(3)
    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    program2 = await mdl.Programs()
    project2 = await mdl.Projects(program_id=program2.id)
    await mdl.ProjectPermissions(user=user_id, project=project1.id)
    await mdl.ProjectPermissions(user=user_id, project=project2.id)

    await mdl.ProjectPermissions(user=impersonator_user_id, project=project2.id)

    # assign roles and permissions
    role1 = await mdl.Roles(name=DefaultRoles.PRODUCER.value, role_type=RoleTypes.PRODUCER)
    role2 = await mdl.Roles(name=DefaultRoles.PROGRAM_ADMIN.value, role_type=RoleTypes.ADMIN)
    role3 = await mdl.Roles(name=DefaultRoles.EDIT_PROGRAM_ADMIN.value, role_type=RoleTypes.ADMIN)

    await mdl.RolesUsers(fs_user_id=user_id, role_id=role1.id, program_id=program1.id)
    await mdl.RolesUsers(fs_user_id=user_id, role_id=role2.id, program_id=program2.id)
    await mdl.RolesUsers(fs_user_id=impersonator_user_id, role_id=role3.id, program_id=program2.id)
    await mdl.RolesPermissions(role_id=role1.id, permission=Permission.GET_PERMISSIONS.name)
    await mdl.RolesPermissions(role_id=role2.id, permission=Permission.GET_ASSETS.name)
    await mdl.RolesPermissions(role_id=role3.id, permission=Permission.CREATE_PROGRAM_USERS.name)

    response = await async_client.get(
        f"{paths.user}/{user_id}",
        headers={"fs-impersonator-user-id": str(impersonator_user_id)},
    )
    assert response.status_code == status.HTTP_200_OK, response.content

    assert response.json()["program_roles"] == {
        str(program1.id): {
            "program_id": program1.id,
            "program_name": program1.name,
            "permissions": [Permission.GET_PERMISSIONS.name],
            "role": DefaultRoles.PRODUCER.name,
            "role_name": DefaultRoles.PRODUCER.value,
            "role_info": role1.to_dict(),
        },
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [Permission.GET_ASSETS.name],
            "role": DefaultRoles.PROGRAM_ADMIN.name,
            "role_name": DefaultRoles.PROGRAM_ADMIN.value,
            "role_info": role2.to_dict(),
        },
    }
    assert response.json()["original_user_program_roles"] == {
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [Permission.CREATE_PROGRAM_USERS.name],
            "role": DefaultRoles.EDIT_PROGRAM_ADMIN.name,
            "role_name": DefaultRoles.EDIT_PROGRAM_ADMIN.value,
            "role_info": role3.to_dict(),
        },
    }


async def test_users_permission_with_super_admin(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)
    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    program2 = await mdl.Programs()
    project2 = await mdl.Projects(program_id=program2.id)
    await mdl.ProjectPermissions(user=user_id, project=project1.id)
    await mdl.ProjectPermissions(user=user_id, project=project2.id)

    # assign roles and permissions
    role = await mdl.Roles(
        name=DefaultRoles.SUPER_ADMIN.value,
        role_type=RoleTypes.SUPER_ADMIN,
        program_id=None,
    )
    await mdl.RolesUsers(fs_user_id=user_id, role_id=role.id)
    await mdl.RolesPermissions(role_id=role.id, permission=Permission.DELETE_PROGRAM.name)

    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content

    # super admin has permissions for all programs
    assert response.json()["program_roles"] == {
        str(program1.id): {
            "program_id": program1.id,
            "program_name": program1.name,
            "permissions": [Permission.DELETE_PROGRAM.name],
            "role": DefaultRoles.SUPER_ADMIN.name,
            "role_name": DefaultRoles.SUPER_ADMIN.value,
            "role_info": role.to_dict(),
        },
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [Permission.DELETE_PROGRAM.name],
            "role": DefaultRoles.SUPER_ADMIN.name,
            "role_name": DefaultRoles.SUPER_ADMIN.value,
            "role_info": role.to_dict(),
        },
    }

    assert response.json()["original_user_program_roles"] == response.json()["program_roles"]


async def test_delete_user(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)

    program1 = await mdl.Programs()
    project1 = await mdl.Projects(program_id=program1.id)
    await mdl.ProjectPermissions(user=user_id, project=project1.id)

    program2 = await mdl.Programs()
    await mdl.ProgramPermissions(user_id=user_id, program_id=program2.id)

    # assign roles and permissions (producer in program 1)
    producer_role = await mdl.Roles(
        name=DefaultRoles.PRODUCER.value,
        role_type=RoleTypes.PRODUCER,
        program_id=None,
    )
    await mdl.RolesUsers(fs_user_id=user_id, role_id=producer_role.id, program_id=program1.id)
    await mdl.RolesPermissions(role_id=producer_role.id, permission=Permission.GET_COMMERCIALS.name)

    # assign roles and permissions (admin in program 2)
    admin_role = await mdl.Roles(
        name=DefaultRoles.PROGRAM_ADMIN.value,
        role_type=RoleTypes.ADMIN,
        program_id=None,
    )
    await mdl.RolesUsers(fs_user_id=user_id, role_id=admin_role.id, program_id=program2.id)
    await mdl.RolesPermissions(role_id=admin_role.id, permission=Permission.DELETE_PROGRAM.name)

    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content

    # assert permissions has been assigned
    assert response.json()["program_roles"] == {
        str(program1.id): {
            "program_id": program1.id,
            "program_name": program1.name,
            "permissions": [Permission.GET_COMMERCIALS.name],
            "role": DefaultRoles.PRODUCER.name,
            "role_name": DefaultRoles.PRODUCER.value,
            "role_info": producer_role.to_dict(),
        },
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [Permission.DELETE_PROGRAM.name],
            "role": DefaultRoles.PROGRAM_ADMIN.name,
            "role_name": DefaultRoles.PROGRAM_ADMIN.value,
            "role_info": admin_role.to_dict(),
        },
    }

    assert response.json()["original_user_program_roles"] == response.json()["program_roles"]

    # delete user
    response = await async_client.delete(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_204_NO_CONTENT, response.content

    # assert user has been deleted
    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content
    assert response.json()["program_roles"] == {}


async def test_create_super_admin(
    async_client: httpx.AsyncClient,
    mdl,
    faker,
    grant_all_permissions_factory,
    app_request,
):
    await grant_all_permissions_factory()
    user_id = faker.random_number(3)

    program1 = await mdl.Programs()
    program2 = await mdl.Programs()

    super_admin_role = (
        await get.generic_get(
            request=app_request,
            orm_type=Roles,
            filter_sql_expr=and_(Roles.role_type == RoleTypes.SUPER_ADMIN),
            empty_return=True,
        )
    )[0]

    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content

    # assert user has no permission
    assert response.json()["program_roles"] == {}
    assert response.json()["original_user_program_roles"] == response.json()["program_roles"]

    # assign super admin role to user
    response = await async_client.post(f"{paths.super_admin}/{user_id}")
    assert response.status_code == status.HTTP_201_CREATED, response.content

    # # assert user is now super admin
    response = await async_client.get(f"{paths.user}/{user_id}")
    assert response.status_code == status.HTTP_200_OK, response.content
    assert response.json()["program_roles"] == {
        str(program1.id): {
            "program_id": program1.id,
            "program_name": program1.name,
            "permissions": [p.name for p in Permission],
            "role": DefaultRoles.SUPER_ADMIN.name,
            "role_name": DefaultRoles.SUPER_ADMIN.value,
            "role_info": super_admin_role.to_dict(),
        },
        str(program2.id): {
            "program_id": program2.id,
            "program_name": program2.name,
            "permissions": [p.name for p in Permission],
            "role": DefaultRoles.SUPER_ADMIN.name,
            "role_name": DefaultRoles.SUPER_ADMIN.value,
            "role_info": super_admin_role.to_dict(),
        },
    }
