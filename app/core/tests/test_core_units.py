import datetime

import pytest

from config import get_settings
from core import methods

settings = get_settings()


@pytest.mark.parametrize(
    "r1,r2,output",
    [
        (
            methods.Range(start=datetime.datetime.now(), end=datetime.datetime.now()),
            (methods.Range(start=datetime.datetime.now(), end=datetime.datetime.now())),
            0,
        ),
        (
            methods.Range(
                start=datetime.datetime.now() - datetime.timedelta(days=365),
                end=datetime.datetime.now(),
            ),
            (
                methods.Range(
                    start=datetime.datetime.now() - datetime.timedelta(days=364),
                    end=datetime.datetime.now() - datetime.timedelta(days=1),
                )
            ),
            363,
        ),
        (
            methods.Range(
                start=datetime.datetime.now() - datetime.timedelta(days=364),
                end=datetime.datetime.now() - datetime.timedelta(days=1),
            ),
            methods.Range(
                start=datetime.datetime.now() - datetime.timedelta(days=365),
                end=datetime.datetime.now(),
            ),
            363,
        ),
    ],
)
def test_date_range_overlap(r1, r2, output):
    assert methods.calculate_date_overlap(r1, r2) == output


@pytest.mark.parametrize(
    "range_,date,output",
    [
        (
            methods.Range(start=datetime.datetime.now(), end=datetime.datetime.now()),
            datetime.datetime.now(),
            True,
        ),
        (
            methods.Range(
                start=datetime.datetime.now(),
                end=datetime.datetime.now() + datetime.timedelta(days=1),
            ),
            datetime.datetime.now() + datetime.timedelta(days=2),
            False,
        ),
        (
            methods.Range(
                start=datetime.datetime.now(),
                end=datetime.datetime.now() + datetime.timedelta(days=2),
            ),
            datetime.datetime.now() + datetime.timedelta(days=1),
            True,
        ),
    ],
)
def test_date_in_range(date, range_, output):
    assert methods.date_in_range(date, range_) == output
