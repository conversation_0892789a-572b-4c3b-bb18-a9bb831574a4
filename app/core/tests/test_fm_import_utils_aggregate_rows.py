from datetime import datetime

from core.enums import OperationType
from core.fm_import_utils import aggregate_rows, AggRowData, RowData
from core.schema import (
    ExternalService,
    Operation,
    OperationApplicationAttributes,
    OperationSeedingAttributes,
    OperationTillageAttributes,
    OperationYieldAttributes,
)


def test_agg_seeding():
    rows = [
        RowData(
            seeding_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
            ],
            yield_ops=[
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
            ],
            tillage_ops=[],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_agg_yield():
    rows = [
        RowData(
            seeding_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
            ],
            yield_ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=2, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
            ],
            tillage_ops=[],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(
                        crop_type="corn", dry_volume_avg=1.5, dry_volume_unit="bu1ac-1"
                    ),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_agg_yield_one_no_yield_volume():
    rows = [
        RowData(
            seeding_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
            ],
            yield_ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
            ],
            tillage_ops=[],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_agg_yield_all_no_yield_volume():
    rows = [
        RowData(
            seeding_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="alfalfa"),
                    external_service=ExternalService(),
                ),
            ],
            yield_ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="alfalfa"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="alfalfa"),
                    external_service=ExternalService(),
                ),
            ],
            tillage_ops=[],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="alfalfa"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="alfalfa"),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_agg_seeding_yield():
    rows = [
        RowData(
            seeding_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
            ],
            yield_ops=[
                Operation(
                    id=3,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=4,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 4, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=2, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                ),
            ],
            tillage_ops=[],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=4,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 4, 0, 0, 1),
                    attributes=OperationYieldAttributes(
                        crop_type="corn", dry_volume_avg=1.5, dry_volume_unit="bu1ac-1"
                    ),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_agg_tillage():
    rows = [
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.TILLAGE,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationTillageAttributes(),
                    external_service=ExternalService(),
                ),
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.TILLAGE,
                    event_date=datetime(2024, 1, 1, 0, 0, 2),
                    attributes=OperationTillageAttributes(),
                    external_service=ExternalService(),
                ),
            ],
            application_op={},
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=2,
                    field_id=123,
                    type=OperationType.TILLAGE,
                    event_date=datetime(2024, 1, 1, 0, 0, 2),
                    attributes=OperationTillageAttributes(),
                    external_service=ExternalService(),
                ),
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res


def test_application():
    rows = [
        RowData(
            seeding_ops=[],
            yield_ops=[],
            tillage_ops=[],
            application_op=Operation(
                id=1,
                field_id=123,
                type=OperationType.APPLICATION,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationApplicationAttributes(),
                external_service=ExternalService(),
            ),
        )
    ]
    expected_res = [
        AggRowData(
            ops=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.APPLICATION,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationApplicationAttributes(),
                    external_service=ExternalService(),
                )
            ]
        )
    ]
    res = aggregate_rows(rows)
    assert res == expected_res
