from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List
from uuid import uuid4

from core.fm_import_utils import (
    _generate_application_attributes,
    _generate_record_year_value,
    _generate_seeding_attributes,
    _generate_tillage_attributes,
    _generate_values,
    _generate_yield_attributes,
    _get_monitored_attr,
    _get_operation_stage_id,
    _infer_crop_usage,
    _infer_tillage_event,
    _infer_tillage_practice,
    _infer_winter_crop_commitment,
    _infer_winter_crop_termination,
    add_crop_type_to_harvest_operations,
    AggRowData,
    AttributeData,
    generate_operation_values,
    get_attribute_date_range,
    get_stage_date_range,
    get_validated_operations,
    map_stage_to_operations,
    OperationType,
)
from core.methods import AttributeInstanceMeta, Range
from core.schema import (
    ExternalService,
    Operation,
    OperationApplicationAttributes,
    OperationApplicationProduct,
    OperationSeedingAttributes,
    OperationTillageAttributes,
    OperationTillageDepth,
    OperationYieldAttributes,
)
from defaults.attribute_options import (
    ApplicationRateType,
    CropUsage,
    TerminationMethods,
)
from phases.enums import AttributeTypes, StageTypes
from phases.model import Attribute, StageWithPhase
from values.enums import ImportDataSources, ProgressChoices
from values.schema import ValuesRequestAttributeFieldOrEntity


def test_get_stage_date_range():
    @dataclass
    class Case:
        stage: StageWithPhase
        expected_res: Range

    cases = [
        Case(
            stage=StageWithPhase(
                fmi_import_start_date=datetime(2024, 1, 1),
                fmi_import_end_date=datetime(2025, 1, 1),
                year_start=2020,
                year_end=2030,
            ),
            expected_res=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
        ),
        Case(
            stage=StageWithPhase(
                year_start=2020,
                year_end=2030,
            ),
            expected_res=Range(start=datetime(2020, 1, 1), end=datetime(2030, 12, 31)),
        ),
    ]
    for case in cases:
        res = get_stage_date_range(case.stage)
        assert res == case.expected_res


def test_get_attribute_date_range():
    @dataclass
    class Case:
        attribute: Attribute
        stages: List[StageWithPhase]
        expected_res: Range

    cases = [
        # use attribute min and max date values
        Case(
            attribute=Attribute(id=1, parent_stage_id=1, min_val="2024-01-01", max_val="2024-06-30"),
            stages=[
                StageWithPhase(
                    id=1,
                    fmi_import_start_date=datetime(2024, 1, 1),
                    fmi_import_end_date=datetime(2025, 1, 1),
                    year_start=2020,
                    year_end=2030,
                ),
            ],
            expected_res=Range(start=datetime(2024, 1, 1), end=datetime(2024, 6, 30)),
        ),
        # use stage fmi import date range
        Case(
            attribute=Attribute(id=1, parent_stage_id=1),
            stages=[
                StageWithPhase(
                    id=1,
                    fmi_import_start_date=datetime(2024, 1, 1),
                    fmi_import_end_date=datetime(2025, 1, 1),
                    year_start=2020,
                    year_end=2030,
                ),
            ],
            expected_res=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
        ),
        # use stage start and end year values
        Case(
            attribute=Attribute(id=1, parent_stage_id=1),
            stages=[
                StageWithPhase(id=1, year_start=2020, year_end=2030),
            ],
            expected_res=Range(start=datetime(2020, 1, 1), end=datetime(2030, 12, 31)),
        ),
    ]
    for case in cases:
        res = get_attribute_date_range(case.attribute, case.stages)
        assert res == case.expected_res


def test_add_crop_type_to_harvest_operations():
    @dataclass
    class Case:
        operations: List[Dict]
        seasons: List[Dict]
        expected_res: List[Dict]

    cases = [
        Case(
            operations=[
                {
                    "id": "1",
                    "type": "seeding",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"crop_type": "corn"},
                    "field_id": 123,
                },
                {
                    "id": "2",
                    "type": "yield",
                    "event_date": "2024-01-02 00:00:01",
                    "attributes": {"dry_volume_avg": 1, "dry_volume_unit": "bu1ac-1"},
                    "field_id": 123,
                },
                {
                    "id": "3",
                    "type": "seeding",
                    "event_date": "2024-01-03 00:00:01",
                    "attributes": {"crop_type": "barley"},
                    "field_id": 123,
                },
                {
                    "id": "4",
                    "type": "yield",
                    "event_date": "2024-01-04 00:00:01",
                    "attributes": {"dry_volume_avg": 1, "dry_volume_unit": "bu1ac-1"},
                    "field_id": 123,
                },
            ],
            seasons=[
                {
                    "cropType": "corn",
                    "startDate": "2024-01-01 00:00:00",
                    "endDate": "2024-01-02 00:00:00",
                },
                {
                    "cropType": "barley",
                    "startDate": "2024-01-03 00:00:00",
                    "endDate": "2024-01-04 00:00:00",
                },
            ],
            expected_res=[
                {
                    "id": "1",
                    "type": "seeding",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"crop_type": "corn"},
                    "field_id": 123,
                },
                {
                    "id": "2",
                    "type": "yield",
                    "event_date": "2024-01-02 00:00:01",
                    "attributes": {"crop_type": "corn", "dry_volume_avg": 1, "dry_volume_unit": "bu1ac-1"},
                    "field_id": 123,
                },
                {
                    "id": "3",
                    "type": "seeding",
                    "event_date": "2024-01-03 00:00:01",
                    "attributes": {"crop_type": "barley"},
                    "field_id": 123,
                },
                {
                    "id": "4",
                    "type": "yield",
                    "event_date": "2024-01-04 00:00:01",
                    "attributes": {"crop_type": "barley", "dry_volume_avg": 1, "dry_volume_unit": "bu1ac-1"},
                    "field_id": 123,
                },
            ],
        )
    ]
    for case in cases:
        res = add_crop_type_to_harvest_operations(case.operations, case.seasons)
        assert res == case.expected_res


def test_get_validated_operations():
    @dataclass
    class Case:
        operations: List[dict]
        stages: List[StageWithPhase]
        expected_res: bool

    stages = [
        StageWithPhase(id=1, fmi_import_start_date=datetime(2024, 1, 1), fmi_import_end_date=datetime(2024, 1, 2)),
        StageWithPhase(id=2, fmi_import_start_date=datetime(2024, 2, 1), fmi_import_end_date=datetime(2024, 2, 2)),
    ]
    cases = [
        # valid seeding operation
        Case(
            operations=[
                {
                    "id": 1,
                    "field_id": 123,
                    "type": "seeding",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"crop_type": "corn"},
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(external_service="test_service"),
                )
            ],
        ),
        # valid yield operation
        Case(
            operations=[
                {
                    "id": 1,
                    "field_id": 123,
                    "type": "yield",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"crop_type": "corn", "dry_volume_avg": 1, "dry_volume_unit": "bu1ac-1"},
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(external_service="test_service"),
                )
            ],
        ),
        # valid tillage operation
        Case(
            operations=[
                {
                    "id": 1,
                    "field_id": 123,
                    "type": "tillage",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"depth_target": {"area": 1, "depth_avg": 2}},
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.TILLAGE,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationTillageAttributes(depth_target=OperationTillageDepth(area=1, depth_avg=2)),
                    external_service=ExternalService(external_service="test_service"),
                )
            ],
        ),
        # valid application operation
        Case(
            operations=[
                {
                    "id": 1,
                    "field_id": 123,
                    "type": "application",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {
                        "products": [{"product_name": "product1"}, {"product_name": "product2"}],
                        "area_total": 1,
                    },
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[
                Operation(
                    id=1,
                    field_id=123,
                    type=OperationType.APPLICATION,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationApplicationAttributes(
                        products=[
                            OperationApplicationProduct(product_name="product1"),
                            OperationApplicationProduct(product_name="product2"),
                        ],
                        area_total=1,
                    ),
                    external_service=ExternalService(external_service="test_service"),
                )
            ],
        ),
        # missing field_id
        Case(
            operations=[
                {
                    "id": 1,
                    "type": "seeding",
                    "event_date": "2024-01-01 00:00:01",
                    "attributes": {"crop_type": "corn"},
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[],
        ),
        # outside of monitored date range
        Case(
            operations=[
                {
                    "id": 1,
                    "field_id": 123,
                    "type": "seeding",
                    "event_date": "2024-03-01 00:00:01",
                    "attributes": {"crop_type": "corn"},
                    "external_service": "test_service",
                }
            ],
            stages=stages,
            expected_res=[],
        ),
    ]
    for case in cases:
        res = get_validated_operations(case.operations, case.stages)
        assert res == case.expected_res


def test_map_stage_to_operations():
    operations = [
        Operation(
            id=1,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 1, 1),
            attributes=OperationSeedingAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=2,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 1, 2),
            attributes=OperationYieldAttributes(crop_type="corn"),
            external_service=ExternalService(),
        ),
        Operation(
            id=3,
            field_id=123,
            type=OperationType.SEEDING,
            event_date=datetime(2024, 7, 1),
            attributes=OperationSeedingAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
        Operation(
            id=4,
            field_id=123,
            type=OperationType.YIELD,
            event_date=datetime(2024, 7, 2),
            attributes=OperationYieldAttributes(crop_type="barley"),
            external_service=ExternalService(),
        ),
    ]
    stages = [
        StageWithPhase(
            id=1,
            name="Summer Crops",
            fmi_import_start_date=datetime(2024, 1, 1),
            fmi_import_end_date=datetime(2024, 6, 30),
            type_=StageTypes.SUMMER_CROPS,
        ),
        StageWithPhase(
            id=2,
            name="Winter Crops",
            fmi_import_start_date=datetime(2024, 7, 1),
            fmi_import_end_date=datetime(2024, 12, 31),
            type_=StageTypes.WINTER_CROPS,
        ),
    ]
    res = map_stage_to_operations(operations, stages)
    expected_res = {1: [operations[0], operations[1]], 2: [operations[2], operations[3]]}
    assert res == expected_res


def test_get_operation_stage_id():
    @dataclass
    class Case:
        operation: Operation
        expected_res: int | None

    stages = [
        StageWithPhase(
            id=1,
            name="Summer Crops",
            fmi_import_start_date=datetime(2024, 1, 1),
            fmi_import_end_date=datetime(2024, 6, 30),
            type_=StageTypes.SUMMER_CROPS,
        ),
        StageWithPhase(
            id=2,
            name="Winter Crops",
            fmi_import_start_date=datetime(2024, 7, 1),
            fmi_import_end_date=datetime(2024, 12, 31),
            type_=StageTypes.WINTER_CROPS,
        ),
        StageWithPhase(
            id=3,
            name="Nutrient Management",
            fmi_import_start_date=datetime(2024, 1, 1),
            fmi_import_end_date=datetime(2024, 6, 30),
            type_=StageTypes.NUTRIENT_MGMT,
        ),
        StageWithPhase(
            id=4,
            name="Tillage",
            fmi_import_start_date=datetime(2024, 7, 1),
            fmi_import_end_date=datetime(2024, 12, 31),
            type_=StageTypes.TILLAGE,
        ),
    ]
    cases = [
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.SEEDING,
                event_date=datetime(2024, 7, 1),
                attributes=OperationSeedingAttributes(crop_type="corn"),
                external_service=ExternalService(),
            ),
            expected_res=2,
        ),
        # select nutrient management over summer crop stage
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.APPLICATION,
                event_date=datetime(2024, 1, 1),
                attributes=OperationApplicationAttributes(),
                external_service=ExternalService(),
            ),
            expected_res=3,
        ),
        # select tillage stage over winter crop stage
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.TILLAGE,
                event_date=datetime(2024, 7, 1),
                attributes=OperationTillageAttributes(),
                external_service=ExternalService(),
            ),
            expected_res=4,
        ),
    ]
    for case in cases:
        res = _get_operation_stage_id(case.operation, stages)
        assert res == case.expected_res


# TODO: test tillage ops, application ops.
def test_generate_operation_values_simple():
    event_id_1 = str(uuid4())
    event_id_2 = str(uuid4())
    event_id_3 = str(uuid4())
    event_id_4 = str(uuid4())
    monitored_attributes = [
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.record_year,
            stage_id=1,
            attribute_id=1,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.crop_type,
            stage_id=1,
            attribute_id=2,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.planting_date,
            stage_id=1,
            attribute_id=3,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.harvest_date,
            stage_id=1,
            attribute_id=4,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.crop_yield,
            stage_id=1,
            attribute_id=5,
        ),
    ]
    progress_choice = ProgressChoices.monitoring
    mrv_field_id = 123
    product_skip_list = None
    rows = [
        AggRowData(
            ops=[
                Operation(
                    id=1,
                    field_id=mrv_field_id,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 1, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="corn"),
                    external_service=ExternalService(),
                    event_id=event_id_1,
                ),
                Operation(
                    id=2,
                    field_id=mrv_field_id,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 2, 0, 0, 1),
                    attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
                    external_service=ExternalService(),
                    event_id=event_id_2,
                ),
            ],
        ),
        AggRowData(
            ops=[
                Operation(
                    id=3,
                    field_id=mrv_field_id,
                    type=OperationType.SEEDING,
                    event_date=datetime(2024, 1, 3, 0, 0, 1),
                    attributes=OperationSeedingAttributes(crop_type="barley"),
                    external_service=ExternalService(),
                    event_id=event_id_3,
                ),
                Operation(
                    id=4,
                    field_id=mrv_field_id,
                    type=OperationType.YIELD,
                    event_date=datetime(2024, 1, 4, 0, 0, 1),
                    attributes=OperationYieldAttributes(
                        crop_type="barley", dry_volume_avg=1, dry_volume_unit="bu1ac-1"
                    ),
                    external_service=ExternalService(),
                    event_id=event_id_4,
                ),
            ],
        ),
    ]

    res = generate_operation_values(
        rows=rows,
        monitored_attributes=monitored_attributes,
        progress_choice=progress_choice,
        mrv_field_id=mrv_field_id,
        product_skip_list=product_skip_list,
    )
    expected_res = [
        _generate_value(
            value="2024-01-01T00:00:01.000Z",
            attribute_id=3,
            row_id=0,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_1,
        ),
        _generate_value(
            value="corn", attribute_id=2, row_id=0, progress=progress_choice, field_id=mrv_field_id, event_id=event_id_1
        ),
        _generate_value(
            value="2024-01-02T00:00:01.000Z",
            attribute_id=4,
            row_id=0,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_2,
        ),
        _generate_value(
            value="corn", attribute_id=2, row_id=0, progress=progress_choice, field_id=mrv_field_id, event_id=event_id_2
        ),
        _generate_value(
            value=1, attribute_id=5, row_id=0, progress=progress_choice, field_id=mrv_field_id, event_id=event_id_2
        ),
        _generate_value(
            value="2024", attribute_id=1, row_id=0, progress=progress_choice, field_id=mrv_field_id, event_id=None
        ),
        _generate_value(
            value="2024-01-03T00:00:01.000Z",
            attribute_id=3,
            row_id=1,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_3,
        ),
        _generate_value(
            value="barley",
            attribute_id=2,
            row_id=1,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_3,
        ),
        _generate_value(
            value="2024-01-04T00:00:01.000Z",
            attribute_id=4,
            row_id=1,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_4,
        ),
        _generate_value(
            value="barley",
            attribute_id=2,
            row_id=1,
            progress=progress_choice,
            field_id=mrv_field_id,
            event_id=event_id_4,
        ),
        _generate_value(
            value=1, attribute_id=5, row_id=1, progress=progress_choice, field_id=mrv_field_id, event_id=event_id_4
        ),
        _generate_value(
            value="2024", attribute_id=1, row_id=1, progress=progress_choice, field_id=mrv_field_id, event_id=None
        ),
    ]
    assert res == expected_res


def test_generate_record_year_value():
    operation_date = datetime(2024, 1, 1)
    monitored_attributes = [
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.record_year,
            stage_id=1,
            attribute_id=1,
        ),
    ]
    row_idx = 0
    progress = ProgressChoices.monitoring
    field_id = 123
    source = ImportDataSources.other_fms
    res = _generate_record_year_value(operation_date, monitored_attributes, row_idx, progress, field_id, source)
    expected_res = ValuesRequestAttributeFieldOrEntity(
        value="2024",
        attribute_id=1,
        row_id=row_idx,
        locked=False,
        confirmed=True,
        progress=progress,
        field_id=field_id,
        source=source,
    )
    assert res == expected_res


def test_generate_seeding_attributes():
    operation = Operation(
        id=1,
        field_id=123,
        type=OperationType.SEEDING,
        event_date=datetime(2024, 1, 1, 0, 0, 1),
        attributes=OperationSeedingAttributes(crop_type="corn"),
        external_service=ExternalService(),
    )
    res = _generate_seeding_attributes(operation)
    expected_res = [
        AttributeData(
            type_=AttributeTypes.planting_date,
            value="2024-01-01T00:00:01.000Z",
        ),
        AttributeData(
            type_=AttributeTypes.crop_type,
            value="corn",
        ),
    ]
    assert res == expected_res


def test_generate_yield_attributes():
    operation = Operation(
        id=1,
        field_id=123,
        type=OperationType.YIELD,
        event_date=datetime(2024, 1, 1, 0, 0, 1),
        attributes=OperationYieldAttributes(crop_type="corn", dry_volume_avg=1, dry_volume_unit="bu1ac-1"),
        external_service=ExternalService(),
    )
    res = _generate_yield_attributes(operation)
    expected_res = [
        AttributeData(
            type_=AttributeTypes.harvest_date,
            value="2024-01-01T00:00:01.000Z",
        ),
        AttributeData(
            type_=AttributeTypes.crop_type,
            value="corn",
        ),
        AttributeData(
            type_=AttributeTypes.crop_yield,
            value=1,
        ),
        AttributeData(
            type_=AttributeTypes.yield_rate_unit,
            value="bu/ac",
        ),
    ]
    assert res == expected_res


def test_generate_tillage_attributes():
    @dataclass
    class Case:
        operation: Dict
        expected_res: List[AttributeData]

    cases = [
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.TILLAGE,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationTillageAttributes(depth_target=OperationTillageDepth(depth_avg=1, area=2), area=3),
                external_service=ExternalService(),
            ),
            expected_res=[
                AttributeData(
                    type_=AttributeTypes.tillage_date,
                    value="2024-01-01T00:00:01.000Z",
                ),
                AttributeData(
                    type_=AttributeTypes.tillage_depth,
                    value=1,
                ),
            ],
        ),
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.TILLAGE,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationTillageAttributes(
                    depth_target=OperationTillageDepth(depth_avg=1, area=2),
                    depth_result=OperationTillageDepth(depth_avg=3, area=4),
                    area=5,
                ),
                external_service=ExternalService(),
            ),
            expected_res=[
                AttributeData(
                    type_=AttributeTypes.tillage_date,
                    value="2024-01-01T00:00:01.000Z",
                ),
                AttributeData(
                    type_=AttributeTypes.tillage_depth,
                    value=3,
                ),
            ],
        ),
        Case(
            operation=Operation(
                id=1,
                field_id=123,
                type=OperationType.TILLAGE,
                event_date=datetime(2024, 1, 1, 0, 0, 1),
                attributes=OperationTillageAttributes(
                    depth_target=OperationTillageDepth(depth_avg=1, area=2),
                    depth_result=OperationTillageDepth(depth_avg=0, area=4),
                    area=5,
                ),
                external_service=ExternalService(),
            ),
            expected_res=[
                AttributeData(
                    type_=AttributeTypes.tillage_date,
                    value="2024-01-01T00:00:01.000Z",
                ),
                AttributeData(
                    type_=AttributeTypes.tillage_depth,
                    value=1,
                ),
            ],
        ),
    ]
    for case in cases:
        res = _generate_tillage_attributes(case.operation)
        assert res == case.expected_res


def test_generate_application_attributes():
    operation = Operation(
        id=1,
        field_id=123,
        type=OperationType.APPLICATION,
        event_date=datetime(2024, 1, 1, 0, 0, 1),
        attributes=OperationApplicationAttributes(
            area_total=1,
            products=[OperationApplicationProduct(product_name="product1", rate_unit="l1ha-1", rate_avg=1.0)],
        ),
        external_service=ExternalService(),
    )
    res = _generate_application_attributes(operation)
    expected_res = [
        AttributeData(
            type_=AttributeTypes.nutrient_management_enabled,
            value=True,
        ),
        AttributeData(
            type_=AttributeTypes.application_date,
            value="2024-01-01T00:00:01.000Z",
        ),
        AttributeData(
            type_=AttributeTypes.application_area,
            value=1,
        ),
        AttributeData(
            type_=AttributeTypes.application_product,
            value="product1",
        ),
        AttributeData(
            type_=AttributeTypes.application_rate,
            value=1.0,
        ),
        AttributeData(
            type_=AttributeTypes.application_rate_type,
            value=ApplicationRateType.PRODUCT_RATE,
        ),
        AttributeData(
            type_=AttributeTypes.application_rate_unit,
            value="l1ha-1",
        ),
    ]
    assert res == expected_res


def test_get_monitored_attr():
    @dataclass
    class Case:
        operation_date: datetime
        attribute_type: AttributeTypes
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: AttributeInstanceMeta | None

    cases = [
        # matches attribute type
        Case(
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.planting_date,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.planting_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=AttributeInstanceMeta(
                date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                type=AttributeTypes.planting_date,
                stage_id=1,
                attribute_id=1,
            ),
        ),
        # matches refined attribute type
        Case(
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.planting_date,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.summer_planting_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=AttributeInstanceMeta(
                date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                type=AttributeTypes.summer_planting_date,
                stage_id=1,
                attribute_id=1,
            ),
        ),
        # does not match date range
        Case(
            operation_date=datetime(2024, 3, 1),
            attribute_type=AttributeTypes.planting_date,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.planting_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
        # does not match attribute type
        Case(
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.planting_date,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.harvest_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _get_monitored_attr(case.operation_date, case.attribute_type, case.monitored_attributes)
        assert res == case.expected_res


def test_infer_tillage_practice():
    @dataclass
    class Case:
        value: ValuesRequestAttributeFieldOrEntity
        operation_date: datetime
        attribute_type: AttributeTypes
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: ValuesRequestAttributeFieldOrEntity

    cases = [
        # reduced till
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.tillage_depth,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_depth,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_practice,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value="reduced till",
                attribute_id=2,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # conventional till
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=100,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.tillage_depth,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_depth,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_practice,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value="conventional till",
                attribute_id=2,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # spring tillage practice
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.spring_tillage_depth,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.spring_tillage_depth,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.spring_tillage_practice,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value="reduced till",
                attribute_id=2,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # tillage practice not monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            attribute_type=AttributeTypes.tillage_depth,
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_depth,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _infer_tillage_practice(case.value, case.operation_date, case.attribute_type, case.monitored_attributes)
        assert res == case.expected_res


def test_infer_winter_crop_commitment():
    @dataclass
    class Case:
        value: ValuesRequestAttributeFieldOrEntity
        operation_date: datetime
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: ValuesRequestAttributeFieldOrEntity

    cases = [
        # wcc monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="corn",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_type,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_commitment,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value=True,
                attribute_id=2,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # wcc not monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="corn",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_type,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
        # invalid value
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_type,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_commitment,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _infer_winter_crop_commitment(case.value, case.operation_date, case.monitored_attributes)
        assert res == case.expected_res


def test_infer_winter_crop_termination():
    @dataclass
    class Case:
        value: ValuesRequestAttributeFieldOrEntity
        operation_date: datetime
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: ValuesRequestAttributeFieldOrEntity

    cases = [
        # wct monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_termination,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value=TerminationMethods.grain_harvest,
                attribute_id=2,
                row_id=0,
                locked=False,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # wct not monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
        # invalid value
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=0,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.winter_crop_termination,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _infer_winter_crop_termination(case.value, case.operation_date, case.monitored_attributes)
        assert res == case.expected_res


def test_infer_crop_usage():
    @dataclass
    class Case:
        value: ValuesRequestAttributeFieldOrEntity
        operation_date: datetime
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: ValuesRequestAttributeFieldOrEntity

    cases = [
        # crop usage monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_usage,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value=CropUsage.COMMODITY,
                attribute_id=2,
                row_id=0,
                locked=False,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # crop usage not monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=1,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
        # invalid value
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value=0,
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_yield,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.crop_usage,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _infer_crop_usage(case.value, case.operation_date, case.monitored_attributes)
        assert res == case.expected_res


def test_infer_tillage_event():
    @dataclass
    class Case:
        value: ValuesRequestAttributeFieldOrEntity
        operation_date: datetime
        monitored_attributes: List[AttributeInstanceMeta]
        expected_res: ValuesRequestAttributeFieldOrEntity

    cases = [
        # tillage event monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="2024-01-01T00:00:01.000Z",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_date,
                    stage_id=1,
                    attribute_id=1,
                ),
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_event,
                    stage_id=1,
                    attribute_id=2,
                ),
            ],
            expected_res=ValuesRequestAttributeFieldOrEntity(
                value=True,
                attribute_id=2,
                row_id=0,
                locked=False,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
        ),
        # tillage event not monitored
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="2024-01-01T00:00:01.000Z",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2024, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
        # tillage event not in date range
        Case(
            value=ValuesRequestAttributeFieldOrEntity(
                value="2023-01-01T00:00:01.000Z",
                attribute_id=1,
                row_id=0,
                locked=True,
                confirmed=True,
                progress=ProgressChoices.monitoring,
                field_id=123,
                source=ImportDataSources.other_fms,
            ),
            operation_date=datetime(2023, 1, 1),
            monitored_attributes=[
                AttributeInstanceMeta(
                    date_range=Range(start=datetime(2024, 1, 1), end=datetime(2024, 2, 1)),
                    type=AttributeTypes.tillage_date,
                    stage_id=1,
                    attribute_id=1,
                ),
            ],
            expected_res=None,
        ),
    ]
    for case in cases:
        res = _infer_tillage_event(case.value, case.operation_date, case.monitored_attributes)
        assert res == case.expected_res


def test_generate_values():
    operation_date = datetime(2024, 1, 1)
    attributes = [
        AttributeData(
            type_=AttributeTypes.planting_date,
            value="2024-01-01T00:00:01.000Z",
        ),
        AttributeData(
            type_=AttributeTypes.crop_type,
            value="corn",
        ),
    ]
    monitored_attributes = [
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.record_year,
            stage_id=1,
            attribute_id=1,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.planting_date,
            stage_id=1,
            attribute_id=2,
        ),
        AttributeInstanceMeta(
            date_range=Range(start=datetime(2024, 1, 1), end=datetime(2025, 1, 1)),
            type=AttributeTypes.crop_type,
            stage_id=1,
            attribute_id=3,
        ),
    ]
    row_id = 0
    progress = ProgressChoices.monitoring
    field_id = 123
    source = ImportDataSources.other_fms
    event_id = str(uuid4())
    res = _generate_values(
        operation_date, attributes, monitored_attributes, row_id, progress, field_id, source, event_id
    )
    expected_res = [
        ValuesRequestAttributeFieldOrEntity(
            value="2024-01-01T00:00:01.000Z",
            attribute_id=2,
            row_id=0,
            locked=False,
            confirmed=True,
            progress=ProgressChoices.monitoring,
            field_id=123,
            source=ImportDataSources.other_fms,
            event_id=event_id,
        ),
        ValuesRequestAttributeFieldOrEntity(
            value="corn",
            attribute_id=3,
            row_id=0,
            locked=False,
            confirmed=True,
            progress=ProgressChoices.monitoring,
            field_id=123,
            source=ImportDataSources.other_fms,
            event_id=event_id,
        ),
    ]
    assert res == expected_res


def _generate_value(
    value: Any,
    attribute_id: int,
    row_id: int,
    progress: ProgressChoices,
    field_id: int,
    event_id: str,
) -> ValuesRequestAttributeFieldOrEntity:
    return ValuesRequestAttributeFieldOrEntity(
        value=value,
        attribute_id=attribute_id,
        row_id=row_id,
        locked=False,
        confirmed=True,
        progress=progress,
        field_id=field_id,
        source=ImportDataSources.other_fms,
        event_id=event_id,
    )
