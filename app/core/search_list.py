import re


class SearchList:
    def __init__(self, source: list | str, match_start_chars: int | None = None) -> None:
        """A simple search list class.
        source:  can be a list string, ie a string with items denoted by newlines, or a list of strings.
        match_start_chars:  returns True if the first n chars match.
        """
        self.match_start_chars = match_start_chars  # number of starting characters to consider a fair match
        self.__haystack: list = []
        if isinstance(source, str):
            self.__from_list_string(source)
        elif isinstance(source, list):
            self.__from_list(source)
        else:
            raise ValueError("source should be a list-as-a-string or a list of string")

    def __from_list_string(self, ls: str) -> None:
        self.__from_list(ls.strip().split("\n"))

    def __from_list(self, ls: list) -> None:
        self.__haystack = [self.only_alpha_numeric(s) for s in ls]

    @staticmethod
    def only_alpha_numeric(ss: str) -> str:
        return "".join(re.findall("\\w", ss)).lower()

    def has_items(self) -> bool:
        return len(self.__haystack) > 0

    def match(self, search_str: str) -> bool:

        search_str = self.only_alpha_numeric(search_str)

        # Exact match
        if search_str in self.__haystack:
            return True

        # Match first n chars
        if self.match_start_chars is not None and self.match_start_chars > 0:

            if len(search_str) < self.match_start_chars:
                return False

            for item in self.__haystack:
                str1 = item[: self.match_start_chars]
                str2 = search_str[: self.match_start_chars]
                if str1 == str2:
                    return True

        return False
