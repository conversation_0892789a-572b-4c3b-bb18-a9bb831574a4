from __future__ import annotations

import re
from datetime import datetime
from enum import StrEnum
from typing import TYPE_CHECKING

from dateutil.parser import isoparse
from pydantic import BaseModel, Field, PrivateAttr, validator

from core.enums import MeasurementUnits, OperationType
from helper.i18n import Locale
from permissions.schema import Role

if TYPE_CHECKING:
    from annotations import DictStrAny


class AttributeTypes(StrEnum):
    season = "season"
    operation = "operation"


class FieldsStats(BaseModel):
    area_ha: float
    num_fields: int
    num_projects: int | None = None


class User(BaseModel):
    _user_id: int | None = PrivateAttr(None)
    first_name: str
    last_name: str
    email: str
    last_login_time: str | None
    lang_locale: Locale | None

    def __init__(self, **data: DictStrAny) -> None:
        super().__init__(**data)
        self._user_id = data.get("_user_id")


class Regions(BaseModel):
    region: str
    country: str


class UserProgramRolePermissions(BaseModel):
    program_id: int
    program_name: str | None
    permissions: list[str] = Field(default_factory=list)
    role: str
    role_name: str
    role_info: Role


class UserPermissionsResponse(BaseModel):
    program_admin: list[int] = Field(default_factory=list)
    project_producer: list[int] = Field(default_factory=list)
    program_roles: dict[int, UserProgramRolePermissions] = Field(default_factory=dict)
    original_user_program_roles: dict[int, UserProgramRolePermissions] = Field(default_factory=dict)


class CropTypes(BaseModel):
    id: int
    value: str
    label: str
    color: str
    icon: str | None
    label_en_us: str | None
    label_en_gb: str | None
    label_fr: str | None
    label_pl_pl: str | None
    label_pt_br: str | None
    label_ro_ro: str | None
    label_ru_ru: str | None
    label_de_de: str | None
    label_vi_vn: str | None
    label_hu_hu: str | None
    label_uk_ua: str | None


class UserRow(BaseModel):
    email: str
    first_name: str
    last_name: str
    password: str
    confirm_password: str
    address: str
    city: str
    country: str
    zip: str = Field(default="1234")
    state: str
    phone: str = Field(default="0123456789")
    phone_full_number: str = Field(default="0123456789", alias="phoneFullNumber")
    custom_inputs: list[dict]
    group: str | None = None

    @validator("email")
    def validate_email(cls, value: str) -> str:
        if not value:
            raise ValueError("Email is required")
        if not re.match(r"[^@]+@[^@]+\.[^@]+", value):
            raise ValueError("Invalid email")
        return value


class UserDetailResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone: str
    timezone: str
    lang_locale: Locale | None
    measurement: MeasurementUnits | None
    city: str
    country: str
    state: str
    street: str
    zip: str


class PatchUserDetailRequest(BaseModel):
    first_name: str | None
    last_name: str | None
    phone: str | None
    timezone: str | None
    lang_locale: Locale | None
    measurement: MeasurementUnits | None
    city: str | None
    country: str | None
    state: str | None
    street: str | None
    zip: str | None


class OperationSeedingAttributes(BaseModel):
    crop_type: str
    crop_variety: str | None = None


class OperationYieldAttributes(BaseModel):
    crop_type: str
    crop_variety: str | None = None
    area: float | None = None
    yield_total: float | None = None
    dry_volume_avg: float | None = None
    dry_volume_unit: str | None = None
    moisture_pct_avg: float | None = None
    wet_mass_total: float | None = None
    wet_mass_avg: float | None = None
    speed_avg: float | None = None


class OperationTillageDepth(BaseModel):
    area: float | None = None
    depth_avg: float | None = None


class OperationTillageAttributes(BaseModel):
    depth_target: OperationTillageDepth | None = None
    depth_result: OperationTillageDepth | None = None


class OperationApplicationProduct(BaseModel):
    product_name: str | None = None
    rate_avg: float | None = None
    rate_unit: str | None = None


class OperationApplicationAttributes(BaseModel):
    products: list[OperationApplicationProduct] = []
    area_total: float | None = None


class ExternalService(BaseModel):
    external_service: str | None = None
    external_service_id: str | None = None
    external_service_sync_account: str | None = None


class Operation(BaseModel):
    id: int
    field_id: int
    type: OperationType
    event_date: datetime
    attributes: (
        OperationSeedingAttributes
        | OperationYieldAttributes
        | OperationTillageAttributes
        | OperationApplicationAttributes
        | None
    )
    external_service: ExternalService
    event_id: str | None = None

    class Config:
        # used to prevent type coercion
        smart_union = True

    @classmethod
    def from_dict(cls, operation_dict: dict) -> "Operation":
        operation_type = OperationType[operation_dict["type"].upper()]
        attributes = None
        match operation_type:
            case OperationType.SEEDING:
                attributes = OperationSeedingAttributes(**operation_dict["attributes"])
            case OperationType.YIELD:
                attributes = OperationYieldAttributes(**operation_dict["attributes"])
            case OperationType.TILLAGE:
                attributes = OperationTillageAttributes(**operation_dict["attributes"])
            case OperationType.APPLICATION:
                attributes = OperationApplicationAttributes(**operation_dict["attributes"])
        return cls(
            id=operation_dict["id"],
            field_id=operation_dict["field_id"],
            type=operation_type,
            event_date=isoparse(operation_dict["event_date"]),
            event_id=operation_dict.get("event_id"),
            attributes=attributes,
            external_service=ExternalService(
                external_service=operation_dict.get("external_service"),
                external_service_id=operation_dict.get("external_service_id"),
                external_service_sync_account=operation_dict.get("external_service_sync_account"),
            ),
        )
