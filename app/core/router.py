from copy import deepcopy
from typing import Annotated

import elasticapm
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    Request,
    status,
    UploadFile,
)

from core import paths, schema
from core.methods import (
    bulk_create_users_from_csv,
    get_accessible_users_cached,
    has_all_permissions,
)
from core.schema import UserDetailResponse
from logger import get_logger
from permissions.db import (
    get_role_permissions,
    get_role_users_by_user_id,
    get_super_admin_role,
)
from permissions.enums import DefaultRoles, Permission
from permissions.methods import attach_role_to_users, is_super_admin
from permissions.model import RolesUsers
from permissions.resolver import Permissions
from programs.db import (
    get_program_by_project_id,
    get_program_id_names,
    get_program_id_project_permission,
)
from programs.model import ProgramPermissions
from programs.schema import ProgramID
from projects.model import ProjectPermissions, Projects
from root_crud import delete, get
from users.db import delete_user_recent_program_by_user_id

logger = get_logger(__name__)
tags = ["core"]
router = APIRouter()


@elasticapm.async_capture_span()
@router.get(
    f"{paths.user}{paths.specific_user}",  # users/{user_id}
    response_model=schema.UserPermissionsResponse,
    tags=["user"],
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_USER_PERMISSIONS]))],
)
async def get_user_permissions(request: Request, user_id: int) -> schema.UserPermissionsResponse:
    caller_role_users = await get_role_users_by_user_id(request, request.state.fs_user_id)

    if (not is_super_admin(caller_role_users)) and user_id != int(request.state.fs_user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"message": "No required permissions"},
        )

    filters = [get.Filter(id_field=ProgramPermissions.user_id, ids=[user_id])]

    if fs_impersonator_program_id := request.state.fs_impersonator_program_id:
        filters.append(
            get.Filter(id_field=ProgramPermissions.program_id, ids=[fs_impersonator_program_id]),
        )

    program_ids = await get.generic_get(
        request=request,
        orm_type=ProgramPermissions,
        type_=ProgramID,
        filters=filters,
        empty_return=True,
    )

    rows = await get_program_id_project_permission(request=request, user_id=user_id)
    if fs_impersonator_program_id:
        project_ids = {x.project for x in rows if x.program_id == fs_impersonator_program_id}
    else:
        project_ids = {x.project for x in rows}
    logger.debug(project_ids)
    logger.debug(program_ids)

    # list user's roles and permissions
    program_roles = await get_user_role_permissions(request=request, user_id=user_id)

    # list original user's roles and permissions
    original_user_program_roles = deepcopy(program_roles)
    if request.state.fs_impersonator_user_id:
        original_user_program_roles = await get_user_role_permissions(
            request=request, user_id=request.state.fs_impersonator_user_id
        )

    return schema.UserPermissionsResponse(
        program_admin=[i.program_id for i in program_ids if i.program_id is not None],
        project_producer=list(project_ids),
        program_roles=program_roles,
        original_user_program_roles=original_user_program_roles,
    )


@elasticapm.async_capture_span()
@router.get(
    f"{paths.user}{paths.specific_user}/permissions/check",
    tags=["user"],
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.GET_USER_PERMISSIONS]))],
)
async def check_user_permissions(
    request: Request,
    user_id: int,
    permissions: Annotated[list[str], Query()],
    program_id: int | None = None,
    project_id: int | None = None,
) -> str:
    """
    Checks that the user has all the specified permissions for the given program id.
    Must provide either program_id or project_id, can provide both. If there's a mismatch, will return 400.
    If the project_id is provided, will fetch the program_id from the project_id and check the required permissions
    against the permissions the user has for the program. It will also ensure that the project_id appears in the list of
    projects the user has access to for the given program.
    Success is a 200, failure is a 403.
    """
    # We must have either program_id or project_id
    if not program_id and not project_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "must specify either program_id or project_id"},
        )

    # If we have a project_id, we will fetch the program_id regardless of whether it was passed in.
    if project_id:
        try:
            program = await get_program_by_project_id(request=request, project_id=project_id)
        except IndexError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": f"failed to fetch program for project_id {project_id}"},
            )
        if program_id and program_id != program.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "program_id does not match project_id"},
            )
        else:
            program_id = program.id

    user_permissions = await get_user_permissions(request=request, user_id=user_id)
    if not has_all_permissions(user_permissions, program_id, permissions, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"message": "insufficient permissions"},
        )
    return "ok"


async def get_user_role_permissions(request: Request, user_id: int) -> dict[int, schema.UserProgramRolePermissions]:
    program_roles: dict[int, schema.UserProgramRolePermissions] = {}
    role_users = await get_role_users_by_user_id(request=request, user_id=user_id)
    if is_super_admin(role_users):
        # super admin has access to all programs
        super_admin_role_user = [
            role_user for role_user in role_users if role_user.role.name == DefaultRoles.SUPER_ADMIN.value
        ][0]
        role_permissions = await get_role_permissions(request=request, role_id=super_admin_role_user.role_id)
        all_program_id_names = await get_program_id_names(request=request)
        for program_id, program_name in all_program_id_names.items():
            program_roles[program_id] = schema.UserProgramRolePermissions(
                program_id=program_id,
                program_name=program_name,
                permissions=[p.permission.name for p in role_permissions],
                role=DefaultRoles.SUPER_ADMIN.name,
                role_name=DefaultRoles.SUPER_ADMIN.value,
                role_info=super_admin_role_user.role,
            )
    else:
        role_users = await get_role_users_by_user_id(request=request, user_id=user_id)
        program_id_names = await get_program_id_names(request, [role_user.program_id for role_user in role_users])
        for role_user in role_users:
            role_permissions = await get_role_permissions(request, role_user.role_id)
            program_roles[role_user.program_id] = schema.UserProgramRolePermissions(
                program_id=role_user.program_id,
                program_name=program_id_names.get(role_user.program_id),
                permissions=[p.permission.name for p in role_permissions],
                role=(DefaultRoles(role_user.role.name).name if role_user.role.name in DefaultRoles.values() else None),
                role_name=role_user.role.name,
                role_info=role_user.role,
            )
    logger.debug(program_roles)
    return program_roles


@elasticapm.async_capture_span()
@router.delete(
    f"{paths.user}{paths.specific_user}",  # users/{user_id}
    response_model=None,
    tags=tags + ["user"],
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(Permissions([Permission.DELETE_USER]))],
)
async def delete_user(request: Request, user_id: int) -> None:
    """
    Delete user (either program admin or producer or super admin) from MRV across all programs
    Normally should only be invoked by Core
    """
    # delete related projects
    project_permissions = await get.get(
        request=request,
        orm_type=ProjectPermissions,
        id_field=ProjectPermissions.user,
        ids=[user_id],
        empty_return=True,
    )
    if len(project_permissions) > 0:
        await delete.soft(
            request=request,
            orm_type=Projects,
            id_field=Projects.id,
            ids=[pp.project for pp in project_permissions],
        )

    # delete related permissions
    await delete.soft(
        request=request,
        orm_type=ProgramPermissions,
        id_field=ProgramPermissions.user_id,
        ids=[user_id],
    )
    await delete.soft(
        request=request,
        orm_type=ProjectPermissions,
        id_field=ProjectPermissions.user,
        ids=[user_id],
    )
    await delete.soft(
        request=request,
        orm_type=RolesUsers,
        id_field=RolesUsers.fs_user_id,
        ids=[user_id],
        filters=[get.Filter(id_field=RolesUsers.deleted_at, ids=[None])],
    )
    await delete_user_recent_program_by_user_id(request=request, user_id=user_id)


@elasticapm.async_capture_span()
@router.post(
    f"{paths.super_admin}{paths.specific_user}",  # super_admin/users/{user_id}
    response_model=None,
    tags=tags + ["user"],
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(Permissions([Permission.CREATE_SUPER_ADMIN]))],
)
async def create_super_admin(request: Request, user_id: int) -> None:
    """
    Create super admin user. Normally should only be invoked by Core
    """
    super_admin_role = await get_super_admin_role(request)
    await attach_role_to_users(
        request=request,
        role_id=super_admin_role.id,
        program_id=None,
        user_ids=[user_id],
    )


@elasticapm.async_capture_span()
@router.post(
    f"{paths.user}{paths.bulk_user}",  # users/bulk_users
    response_model=None,
    tags=tags + ["user"],
    dependencies=[Depends(Permissions([Permission.CREATE_PROJECT_USERS]))],
)
async def bulk_create_users(request: Request, program_code: str, file: UploadFile) -> None:
    """
    Creates producers (users and projects) from a csv file. The csv file must have the following columns:

        - Email address
        - First Name
        - Last Name
        - Password
        - Billing address
        - City
        - Country
        - Zip
        - State
        - Phone

    Optionally a `CRI Key` column can be provided. If it is, then `Select Coop` must also be provided.

    `CRI Key` is a reference to which custom registration input the coop should be assigned to, as per
    mrv_program_custom_reg_inputs.key. Otherwise, the coop will not be assigned to a custom registration input

    Another optional column is `Select Competitor`. If this is provided then the following two things happen:
        - A parent group is created/fetched with the name given in this column
        - A child group is created/fetched with the name given in the `Select Coop` column
        - Both groups are assigned to the newly created producer
    """
    return await bulk_create_users_from_csv(request, program_code, file)


@elasticapm.async_capture_span()
@router.get(
    paths.accessible_users,  # /accessible_users
    response_model=list[UserDetailResponse],
    tags=["user"],
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.CREATE_PROJECT_USERS]))],
)
async def get_accessible_users(
    request: Request,
    exclude_program_id: int | None = Query(None),
    search_str: str = Query("", description="Search for email"),
) -> list[UserDetailResponse]:
    user_id = request.state.fs_user_id
    users = await get_accessible_users_cached(request=request, user_id=user_id, exclude_program_id=exclude_program_id)
    return [user for user in users if search_str.lower() in user.email.lower()]
