from contextlib import suppress
from datetime import datetime
from functools import partial
from uuid import uuid4

import pytest
from pydantic import BaseModel
from sqlalchemy.exc import StatementError
from sqlalchemy.orm import relationship
from sqlalchemy.orm.exc import DetachedInstanceError

from db.tasks import create_audit_logs
from db.transaction import Transacted
from helper.context import ctx
from programs.model import Programs
from programs.reporting_dashboards.dashboards.schema import (
    ReportingDashboardsFullResponse,
)
from programs.reporting_dashboards.model import (
    ReportingDashboards,
    ReportingDashboardSections,
)
from projects.model import Projects
from root_crud.enums import ActionChoices
from root_crud.model import AuditLogs


@pytest.fixture(scope="module")
def err_msg():
    return "You must initialize OrmSerializer with `session` to query lazy relations"


async def test_to_dict_full_one_to_one(err_msg, db_session_maker, mdl, orm_select):
    class PatchedReportingDashboards(ReportingDashboards):
        sections = relationship("ReportingDashboardSections", lazy="select", uselist=False)  # one-to-one

    dashboard = await mdl.ReportingDashboards()
    dashboards = await orm_select(PatchedReportingDashboards)
    assert len(dashboards) == 1
    patched_dash = dashboards[0]
    with pytest.raises(ValueError) as e:
        await patched_dash.to_dict_full()
        assert e.value == err_msg

    async with db_session_maker() as s:
        as_dict = await patched_dash.to_dict_full(s)
        assert as_dict["sections"] is None
        await s.refresh(patched_dash)  # refresh to force lazy load next time

    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    async with db_session_maker() as s:
        s.add(patched_dash)
        as_dict = await patched_dash.to_dict_full(s)
        assert as_dict["sections"]["id"] == section.id


async def test_to_dict_full_one_to_many(err_msg, db_session_maker, mdl):
    dashboard = await mdl.ReportingDashboards()
    with pytest.raises(ValueError) as e:
        await dashboard.to_dict_full()
        assert e.value == err_msg

    async with db_session_maker() as s:
        as_dict = await dashboard.to_dict_full(s)
        assert as_dict["sections"] == []
        await s.refresh(dashboard)  # refresh to force lazy load next time

    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    async with db_session_maker() as s:
        as_dict = await dashboard.to_dict_full(s)
        assert as_dict["sections"][0]["id"] == section.id


async def test_to_dict_full_with_schema(db_session_maker, mdl):
    class Schema(BaseModel):
        id: int  # noqa: VNE003, A003

    class ChildSchema(BaseModel):
        id: int  # noqa: VNE003, A003

    class SchemaWithChildren(Schema):
        sections: list[ChildSchema]

    dashboard = await mdl.ReportingDashboards()
    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)

    async with db_session_maker() as s:
        res = await dashboard.to_dict_full(s, schema=Schema)
        assert "sections" not in res

        res = await dashboard.to_dict_full(s, schema=SchemaWithChildren)
        assert res["sections"][0]["id"] == section.id


async def test_to_dict_full_exclude_deleted(db_session_maker, mdl, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    group = await mdl.Groups()
    farm = await mdl.Farms(parent_project_id=project.id, core_farm_group_id=group.id)
    farm_deleted = await mdl.Farms(
        parent_project_id=project.id, deleted_at=datetime.utcnow(), core_farm_group_id=group.id
    )

    projects = await orm_select(Projects, load_lazy=True)
    assert len(projects) == 1
    assert len(projects[0].farms_list) == 1
    projects[0].farms_list.append(farm_deleted)

    async with db_session_maker() as s:
        rows = (await projects[0].to_dict_full(s, exclude_deleted=True))["farms_list"]
        assert len(rows) == 1
        assert rows[0]["id"] == farm.id

        rows = (await projects[0].to_dict_full(s, exclude_deleted=False))["farms_list"]
        assert len(rows) == 2
        assert rows[1]["id"] == farm_deleted.id


async def test_to_schema(db_session_maker, reporting_dashboard_factory):
    objs = await reporting_dashboard_factory()  # noqa: VNE002
    async with db_session_maker() as s:
        schema = await objs.dashboard.to_schema(schema=ReportingDashboardsFullResponse, s=s)

    assert schema.id == objs.dashboard.id == objs.section.dashboard_id
    assert schema.sections[0].id == objs.section.id == objs.row.section_id
    assert schema.sections[0].rows[0].id == objs.row.id == objs.chart.row_id
    assert schema.sections[0].rows[0].charts[0].id == objs.chart.id


async def test_load_lazy_relations_one_to_one(err_msg, db_session_maker, mdl, orm_select):
    class PatchedReportingDashboards(ReportingDashboards):
        sections = relationship("ReportingDashboardSections", lazy="select", uselist=False)  # one-to-one

    dashboard = await mdl.ReportingDashboards()
    dashboards = await orm_select(PatchedReportingDashboards)
    assert len(dashboards) == 1
    patched_dash = dashboards[0]
    with pytest.raises(DetachedInstanceError):  # lazy relationship not loaded
        patched_dash.sections

    with pytest.raises(ValueError) as e:
        await patched_dash.load_lazy_relations(s=None)
        assert e.value == err_msg

    async with db_session_maker() as s:
        patched_dash = await patched_dash.load_lazy_relations(s)
        assert patched_dash.sections is None
        await s.refresh(patched_dash)  # refresh to force lazy load next time

    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    async with db_session_maker() as s:
        patched_dash = await patched_dash.load_lazy_relations(s)
        assert patched_dash.sections.id == section.id


async def test_load_lazy_relations_one_to_many(err_msg, db_session_maker, mdl):
    dashboard = await mdl.ReportingDashboards()
    with pytest.raises(DetachedInstanceError):  # lazy relationship not loaded
        dashboard.sections

    with pytest.raises(ValueError) as e:
        await dashboard.load_lazy_relations(s=None)
        assert e.value == err_msg

    async with db_session_maker() as s:
        dashboard = await dashboard.load_lazy_relations(s)
        assert dashboard.sections == []
        await s.refresh(dashboard)  # refresh to force lazy load next time

    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)
    async with db_session_maker() as s:
        dashboard = await dashboard.load_lazy_relations(s)
        assert dashboard.sections[0].id == section.id


async def test_load_lazy_relations_full_with_schema(db_session_maker, mdl):
    class Schema(BaseModel):
        id: int  # noqa: VNE003, A003

    class ChildSchema(BaseModel):
        id: int  # noqa: VNE003, A003

    class SchemaWithChildren(Schema):
        sections: list[ChildSchema]

    dashboard = await mdl.ReportingDashboards()
    section = await mdl.ReportingDashboardSections(dashboard_id=dashboard.id)

    with pytest.raises(DetachedInstanceError):  # lazy relationship not loaded
        dashboard.sections

    async with db_session_maker() as s:
        res = await dashboard.load_lazy_relations(s, schema=Schema)
        with pytest.raises(StatementError):  # lazy relationship still not loaded
            res.sections

        res = await dashboard.load_lazy_relations(s, schema=SchemaWithChildren)
        assert res.sections[0].id == section.id


async def test_recursion_stops(db_session_maker, mdl):
    program = await mdl.Programs()
    dashboard = await mdl.ReportingDashboards()
    dash_to_prog = await mdl.ReportingDashboardsToPrograms(program_id=program.id, dashboard=dashboard)

    async with db_session_maker() as s:
        dash_to_prog_ = await dash_to_prog.load_lazy_relations(s)
        assert dash_to_prog_.dashboard
        with pytest.raises(StatementError):
            dash_to_prog_.dashboard.dash_to_progs

    async with db_session_maker() as s:
        await dashboard.load_lazy_relations(s)  # for some reason, this doesn't raise an error

    async with db_session_maker() as s:
        dashboard_ = await dashboard.to_dict_full(s)
        assert not dashboard_["dash_to_progs"][0]["dashboard"]["dash_to_progs"]

    async with db_session_maker() as s:
        dash_to_prog_ = await dash_to_prog.to_dict_full(s)
        assert dash_to_prog_["dashboard"]["dash_to_progs"][0]["dashboard"] is None


async def test_transacted_context_manager(app_request, async_client, mdl, orm_select):
    program = await mdl.Programs(name="FhQJ7JeQrGL")

    # ---Without request commited---
    async with Transacted() as request, request.state.sql_session() as s:
        s.add(program)
        program.name = "uaFzmdxT4c"
        await s.commit()
        await s.refresh(program)
    assert program.name == "uaFzmdxT4c"

    # ---Without request rolled back---
    with pytest.raises(KeyError):
        async with Transacted() as request:
            async with request.state.sql_session() as s:
                s.add(program)
                program.name = "y2qFh9h"
                await s.commit()
            raise KeyError
    assert (await orm_select(Programs))[0].name == "uaFzmdxT4c"

    # ---With request commited---
    async with Transacted(app_request) as request, request.state.sql_session() as s:
        s.add(program)
        program.name = "LEhNKlX"
        await s.commit()
        await s.refresh(program)
    assert program.name == "LEhNKlX"
    assert id(app_request.state.sql_session) != id(request.state.sql_session)
    assert app_request.state.fs_user_id == request.state.fs_user_id == 1

    # ---With request rolled back---
    with pytest.raises(KeyError):
        async with Transacted(app_request) as request:
            async with request.state.sql_session() as s:
                s.add(program)
                program.name = "LqK7jusS"
                await s.commit()
            raise KeyError
    assert (await orm_select(Programs))[0].name == "LEhNKlX"


async def test_audit_logs(allow_audit_log, app_request, db_session_maker, faker, mdl, monkeypatch, orm_select):
    monkeypatch.setattr("db.tasks.create_audit_logs.delay", partial(create_audit_logs, request=app_request))

    # ---Test ctx.request is None---
    async with db_session_maker() as s:
        s.add(ReportingDashboards(title=faker.word(), key=str(uuid4())))
        await s.commit()
    assert not (await orm_select(AuditLogs))

    # ---Test ctx.no_audit_logs is True---
    ctx.no_audit_logs = True
    async with db_session_maker() as s:
        s.add(ReportingDashboards(title=faker.word(), key=str(uuid4())))
        await s.commit()
    assert not (await orm_select(AuditLogs))

    ctx.no_audit_logs = False
    ctx.request = app_request

    # ---Test no audit logs after rollback()---
    with suppress(AssertionError):
        async with Transacted(app_request) as request:
            async with request.state.sql_session() as s:
                s.add(ReportingDashboards(title=faker.word(), key=str(uuid4())))
                await s.commit()
            raise AssertionError("Trigger rollback")

    assert not (await orm_select(AuditLogs))

    # ---Test audit logs after a transaction commit()---
    async with Transacted(app_request) as request:
        async with request.state.sql_session() as s:
            dash_1 = ReportingDashboards(title=faker.word(), key=str(uuid4()))
            s.add(dash_1)
            await s.commit()

    audits = await orm_select(AuditLogs)
    assert len(audits) == 1
    assert audits[0].action == ActionChoices.create
    assert audits[0].table_name == ReportingDashboards.__tablename__
    assert audits[0].data["id"] == dash_1.id
    assert audits[0].data["title"] == dash_1.title

    # ---Test after create with child---
    async with db_session_maker() as s:
        dash = ReportingDashboards(
            title=faker.word(),
            key=str(uuid4()),
            sections=[section := ReportingDashboardSections(title=faker.word(), key=str(uuid4()))],
        )
        s.add(dash)
        await s.commit()

    audits = await orm_select(AuditLogs)
    assert len(audits) == 3
    assert audits[1].action == ActionChoices.create
    assert audits[1].table_name == ReportingDashboards.__tablename__
    assert audits[1].data["id"] == dash.id
    assert audits[1].data["title"] == dash.title
    assert audits[2].action == ActionChoices.create
    assert audits[2].table_name == ReportingDashboardSections.__tablename__
    assert audits[2].data["id"] == section.id
    assert audits[2].data["title"] == section.title

    # ---Test after update---
    async with db_session_maker() as s:
        s.add(dash_1)
        dash_1.title = faker.word()
        await s.commit()

    audits = await orm_select(AuditLogs)
    assert len(audits) == 4
    assert audits[3].action == ActionChoices.update
    assert audits[3].data["title"] == dash_1.title

    # ---Test after delete---
    async with db_session_maker() as s:
        await s.delete(dash_1)
        await s.commit()

    audits = await orm_select(AuditLogs)
    assert len(audits) == 5
    assert audits[4].action == ActionChoices.delete
    assert audits[4].data["title"] == dash_1.title
