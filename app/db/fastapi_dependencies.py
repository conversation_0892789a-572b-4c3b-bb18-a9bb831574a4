from typing import Async<PERSON>terator

from fastapi import Request
from sqlalchemy.ext.asyncio import async_sessionmaker

from db.db_instance import DB
from logger import get_logger

logger = get_logger(__name__)


async def transacted(request: Request) -> AsyncIterator[None]:
    """
    Wraps the request in a transaction. Add `dependencies=[Depends(transacted)]` to the route to use it.
    Catch all the exceptions (including HTTPException) and commit or rollback transaction before response is sent.
    Very important to await all the DB-related tasks before!
    """
    connection = await DB.engine.connect()
    transaction = await connection.begin()
    try:
        request.state.sql_session = async_sessionmaker(connection, expire_on_commit=False)
        yield
    except Exception as e:
        logger.exception(e)
        await transaction.rollback()
        raise e
    else:
        await transaction.commit()
    finally:
        await transaction.close()
        await connection.close()


async def read_committed(request: Request) -> AsyncIterator[None]:
    """
    Start a new connection for this request to execute sqls and set the connection's isolation level to READ COMMITTED.
    Add `dependencies=[Depends(read_committed)]` to the route to use it.
    Very important to await all the DB-related tasks before!
    """
    connection = await DB.engine.connect()
    await connection.execution_options(isolation_level="READ COMMITTED")
    try:
        request.state.sql_session = async_sessionmaker(connection, expire_on_commit=False)
        yield
    finally:
        await connection.close()
