from __future__ import annotations

from typing import TYPE_CHECKING

import elasticapm

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from db.consts import CREATED_AT_KEY, TABLE_NAME_KEY
from helper.context import ctx
from logger import get_logger
from root_crud.enums import ActionChoices
from root_crud.model import AuditLogs

if TYPE_CHECKING:
    from fastapi import Request

    from annotations import DictStrAny


logger = get_logger(__name__)


@celery_app.task(
    base=DBTask,
    bind=True,
)
@async_to_sync
@elasticapm.async_capture_span()
async def create_audit_logs(
    self: DBTask,
    *,
    created: list[DictStrAny],
    updated: list[DictStrAny],
    deleted: list[DictStrAny],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    """Creates `AuditLogs` for the created, updated, and deleted records. Writes them to the database."""

    ctx.no_audit_logs = True

    async with request.state.sql_session() as s:  # type: ignore
        audit_logs = []
        for action, orms_as_dicts in (
            (ActionChoices.create, created),
            (ActionChoices.update, updated),
            (ActionChoices.delete, deleted),
        ):
            for orm_as_dict in orms_as_dicts:
                table_name = orm_as_dict.pop(TABLE_NAME_KEY)
                created_at = orm_as_dict.pop(CREATED_AT_KEY)
                audit_log = AuditLogs(
                    data=orm_as_dict,
                    action=action,
                    table_name=table_name,
                    created_at=created_at,
                    fs_user_id=fs_user_id,
                    fs_impersonator_user_id=fs_impersonator_user_id,
                )
                audit_logs.append(audit_log)

        s.add_all(audit_logs)
        await s.commit()
