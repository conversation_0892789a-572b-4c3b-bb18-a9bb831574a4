from __future__ import annotations

import enum
from copy import deepcopy
from datetime import datetime, timezone
from typing import Any, Dict, Iterable, Self, TYPE_CHECKING

import elasticapm
import pydantic
from sqlalchemy import Engine, JSON, String, text, TIMESTAMP
from sqlalchemy.event import listens_for
from sqlalchemy.exc import InvalidRequestError, MissingGreenlet, StatementError
from sqlalchemy.ext.asyncio import (
    async_sessionmaker,
    AsyncAttrs,
    AsyncSession,
    create_async_engine,
)
from sqlalchemy.future import select
from sqlalchemy.orm import DeclarativeBase, RelationshipProperty, Session
from sqlalchemy.orm.exc import DetachedInstanceError

from db.consts import (
    AUDIT_LOG_DISABLED_TABLES,
    AUDIT_LOG_ORMS_KEY,
    CREATED_AT_KEY,
    NO_AUDIT_LOGS_KEY,
    TABLE_NAME_KEY,
)
from helper.context import ctx
from logger import get_logger
from root_crud.serializer import _pydantic_custom_json_serializer

if TYPE_CHECKING:
    from pydantic import BaseModel
    from sqlalchemy import Connection
    from sqlalchemy.ext.asyncio import AsyncEngine
    from sqlalchemy.orm import ColumnProperty, UOWTransaction
    from sqlalchemy.util import ImmutableProperties

    from annotations import DictStrAny, OrmT, PydanticT
    from config import Settings

logger = get_logger(__name__)


class OrmSerializer:
    """
    The `OrmSerializer` class is responsible for serializing SQLAlchemy ORM objects into dictionaries and loading lazy
    relations.
    """

    def __init__(
        self,
        orm_obj: Base,
        session: AsyncSession | None = None,
        schema: type[BaseModel] | None = None,
        exclude_deleted: bool = True,
    ) -> None:
        """
        Args:
            orm_obj: The SQLAlchemy ORM object to serialize.
            session: An optional `AsyncSession` object to use for querying lazy relations. Defaults to `None`.
            schema: An optional `BaseModel` subclass to use for serialization. Defaults to `None`.
            exclude_deleted: A boolean indicating whether to exclude children having "deleted_at" not None during
                serialization. Defaults to `True`.
        """
        self.orm_obj = orm_obj
        self.s = session
        self.schema = schema
        self.exclude_deleted = exclude_deleted
        self.value_error_msg = f"You must initialize {self.__class__.__name__} with `session` to query lazy relations"
        self.processed_orm_ids: set[tuple[str, int]] = set()  # avoid infinite recursion

    @staticmethod
    def _serialize_value(orm_obj: Base, attr: ColumnProperty) -> Any:
        value = getattr(orm_obj, attr.key)
        if value is not None:
            if isinstance(value, enum.Enum):  # value is Enum instance
                value = value.value
            # value is str, that actually is Enum name
            elif issubclass((python_type := getattr(orm_obj.__class__, attr.key).type.python_type), enum.Enum):
                value = getattr(python_type, value)
        return value

    async def _get_children(self, orm_obj: OrmT, attr_name: str) -> OrmT | list[OrmT] | None:
        try:
            children = getattr(orm_obj, attr_name)
        except (DetachedInstanceError, StatementError, MissingGreenlet):
            if not self.s:
                raise ValueError(self.value_error_msg)

            if orm_obj not in self.s:
                self.s.add(orm_obj)

            # the same as: `children = await self.s.run_sync(lambda sess: getattr(orm_obj, attr_name))`
            children = await getattr(orm_obj.awaitable_attrs, attr_name)

        return children

    async def _serialize_children_recursive(
        self, orm_obj: Base, schema: type[BaseModel] | None, attr: RelationshipProperty
    ) -> DictStrAny | list[DictStrAny] | None:
        children = await self._get_children(orm_obj, attr.key)

        if not attr.uselist:  # one-to-one case
            if children is None:
                return None

            if isinstance(children, Base):
                if (key := (attr.key, id(children))) in self.processed_orm_ids:
                    return None
                self.processed_orm_ids.add(key)
                return await self._to_dict_full_recursive(orm_obj=children, schema=schema, is_child=True)

        if children is None:
            return []

        ret = []
        for item in children:
            if (key := (attr.key, id(item))) in self.processed_orm_ids:
                continue
            self.processed_orm_ids.add(key)
            if child := await self._to_dict_full_recursive(orm_obj=item, schema=schema, is_child=True):
                ret.append(child)
        return ret

    @staticmethod
    def _is_schema_allowed(schema: type[BaseModel] | None, attr_name: str) -> bool:
        if not schema:
            return True

        return attr_name in schema.__fields__

    async def _to_dict_full_recursive(
        self, orm_obj: Base, schema: type[BaseModel] | None, is_child: bool
    ) -> DictStrAny | None:
        data: ImmutableProperties = orm_obj.__mapper__.attrs

        if self.exclude_deleted and is_child and "deleted_at" in data and orm_obj.deleted_at is not None:
            return None

        ret: DictStrAny = {}
        for attr_name, attr in data.items():
            if attr_name.startswith("_") or not self._is_schema_allowed(schema, attr_name):
                continue
            if isinstance(attr, RelationshipProperty):
                ret[attr_name] = await self._serialize_children_recursive(
                    orm_obj, schema.__fields__[attr_name].type_ if schema else None, attr
                )
            else:
                ret[attr_name] = self._serialize_value(orm_obj, attr)
        return ret

    async def to_dict_full_(self) -> DictStrAny:
        """
        Converts the SQLAlchemy object to a dictionary representation, including all related objects.

        Returns:
            The dictionary representation of the SQLAlchemy object, including all related objects.
        """
        return await self._to_dict_full_recursive(orm_obj=self.orm_obj, schema=self.schema, is_child=False)

    async def _load_lazy_relations_recursive(self, orm_obj: Base, schema: type[BaseModel] | None = None) -> None:
        for attr_name, attr in orm_obj.__mapper__.attrs.items():
            if isinstance(attr, RelationshipProperty):
                if schema is not None and attr_name not in schema.__fields__:
                    continue

                children = await self._get_children(orm_obj, attr.key)

                if attr.uselist and isinstance(children, Iterable):
                    for child in children:
                        await self._load_lazy_relations_recursive(
                            child, schema.__fields__[attr_name].type_ if schema else None
                        )

    async def load_lazy_relations_(self) -> Base:
        """
        Loads lazy relations for the given SQLAlchemy ORM object.

        Returns:
            The modified SQLAlchemy ORM object with loaded lazy relations.

        Raises:
            ValueError: If the `self.s` attribute is not set.
        """
        if not self.s:
            raise ValueError(self.value_error_msg)

        if self.orm_obj not in self.s:
            self.s.add(self.orm_obj)
        await self._load_lazy_relations_recursive(self.orm_obj, self.schema)
        return self.orm_obj


class Base(AsyncAttrs, DeclarativeBase):
    __abstract__ = True
    __mapper_args__ = {"eager_defaults": True}
    # https://docs.sqlalchemy.org/en/20/orm/mapping_api.html#sqlalchemy.orm.registry.params.type_annotation_map
    type_annotation_map = {  # the default map is here: sqlalchemy.sql.sqltypes._type_map
        Dict[str, Any]: JSON,  # dict[] didn't work, had to use Dict[] instead
        datetime: TIMESTAMP,
        list: JSON,
        str: String(255),
    }

    def to_dict(self) -> DictStrAny:
        return {c.name: getattr(self, c.name, None) for c in self.__table__.columns}

    async def to_dict_full(
        self, s: AsyncSession | None = None, schema: type[BaseModel] | None = None, exclude_deleted: bool = True
    ) -> DictStrAny:
        """
        Converts the SQLAlchemy ORM object to a dictionary representation with all related objects included.

        Args:
            s: Optional asynchronous session to use for querying lazy-related objects. Default is None.
            schema: Optional Pydantic model schema to use for filtering output dictionary. Default is None.
            exclude_deleted: Optional flag to exclude deleted objects ("deleted_at" not None) from the dictionary.
                Default is True.

        Returns:
            A dictionary representation of the SQLAlchemy ORM object with all related objects included.
        """
        return await OrmSerializer(
            orm_obj=self, session=s, schema=schema, exclude_deleted=exclude_deleted
        ).to_dict_full_()

    async def to_schema(
        self, schema: type[PydanticT], s: AsyncSession | None = None, exclude_deleted: bool = True
    ) -> PydanticT:
        """
        Converts the current instance to an instance of the provided Pydantic model class including all related objects.

        Args:
            schema: The Pydantic model class that represents the desired schema.
            s: Optional SQLAlchemy AsyncSession object used for loading lazy relations (if any). Default is None.
            exclude_deleted: Flag indicating whether to exclude children having "deleted_at" not None from the schema.
                Default is True.

        Returns:
            An instance of the provided Pydantic model class representing the schema for the current instance.
        """
        return schema(**(await self.to_dict_full(s=s, schema=schema, exclude_deleted=exclude_deleted)))

    async def load_lazy_relations(self, s: AsyncSession, schema: type[BaseModel] | None = None) -> Self:
        """
        Loads lazy relations for the current instance.

        Args:
            s (AsyncSession): The async session used for loading lazy relations.
            schema (type[BaseModel] | None): Optional Pydantic schema used for loading only defined relations.
                Default is None.

        Returns:
            The current instance with loaded lazy relations.
        """
        return await OrmSerializer(orm_obj=self, session=s, schema=schema).load_lazy_relations_()


class Database:
    def __init__(self) -> None:
        self.engine: AsyncEngine | None = None
        self.session: async_sessionmaker | None = None

    async def start_db(self, settings: Settings) -> None:
        self.engine = create_async_engine(
            settings.db_url,
            json_serializer=_pydantic_custom_json_serializer,
            echo=settings.DEBUG_DB_QUERIES,
            pool_size=settings.MYSQL_POOL_SIZE,
            max_overflow=settings.MYSQL_POOL_MAX_OVERFLOW,
        )
        self.session = async_sessionmaker(self.engine, expire_on_commit=False)

    async def close_db(self) -> None:
        await self.engine.dispose()  # type: ignore


async def check_liveness(session_maker: async_sessionmaker) -> None:
    async with session_maker() as s:
        await s.execute(select(text("1")))


class AuditLogOrms(pydantic.BaseModel):
    """A class to store ORM objects for audit logging."""

    created: list[OrmT] = pydantic.Field(default_factory=list)
    updated: list[OrmT] = pydantic.Field(default_factory=list)
    deleted: list[OrmT] = pydantic.Field(default_factory=list)


@elasticapm.capture_span()
@listens_for(Session, "after_flush")
def receive_after_flush_event(session: Session, flush_context: UOWTransaction) -> None:
    """Receives the SQLAlchemy `after_flush` event and stores the ORM objects for audit logging. The ORM objects are
    stored in a Connection of the current Session (in `session.connection().info[AUDIT_LOG_ORMS_KEY]`). The ORM objects
    in this receiver may be not commited at all, so it's too early to create audit logs. That's why `Connection.info` is
    used to pass these objects to the `commit` event receiver. If an ORM object has `info` attribute with
    `NO_AUDIT_LOGS_KEY` key set to `True`, it will be excluded from audit logs.
    """
    try:
        if ctx.no_audit_logs:
            return

        def filter_orms(orm_objs: Iterable[OrmT]) -> list[OrmT]:
            if not orm_objs:
                return []
            return [x for x in orm_objs if not getattr(x, "info", {}).get(NO_AUDIT_LOGS_KEY, False)]

        audit_log_orms = session.connection().info.setdefault(AUDIT_LOG_ORMS_KEY, AuditLogOrms())
        audit_log_orms.created += filter_orms(session.new)
        audit_log_orms.updated += filter_orms(session.dirty)
        audit_log_orms.deleted += filter_orms(session.deleted)
    except Exception as e:
        logger.exception(e)


@listens_for(Engine, "commit")
def receive_commit_event(conn: Connection) -> None:
    """Receives the SQLAlchemy `commit` event and sends audit logs for the committed ORM objects to Celery. This
    receiver doesn't have access to the committed ORM objects directly, so it uses the `Connection.info` dictionary to
    get them. The ORM objects are converted to dictionaries with the additional keys: `TABLE_NAME_KEY` and
    `CREATED_AT_KEY`. `fs_user_id` and `fs_impersonator_user_id` are taken from the Contextvar `request` as there is no
    direct access to the FastAPI request object. The `Connection.info[AUDIT_LOG_ORMS_KEY]` is cleared in this receiver.
    """
    try:
        if ctx.no_audit_logs or not (audit_log_orms := conn.info.pop(AUDIT_LOG_ORMS_KEY, None)):
            return

        request = ctx.request
        if not request:
            logger.error("no ctx.request")
            return

        if any((audit_log_orms.created, audit_log_orms.updated, audit_log_orms.deleted)):
            from db.tasks import create_audit_logs

            now = datetime.now(timezone.utc)  # set here for more accurate time, rather than in the task
            session: Session | None = None

            def orms_to_dicts(orms: list[Base]) -> list[DictStrAny]:
                nonlocal session
                orms_as_dicts = []
                for orm in orms:
                    try:
                        orm_as_dict = orm.to_dict()
                    except InvalidRequestError:
                        # A workaround for the case when the event receiver is run inside Celery. `orm.to_dict()` may
                        # raise "InvalidRequestError: This session is in 'prepared' state; no further SQL can be emitted
                        # within this transaction". Adding orm (not its copy) to a new session also raises an error.
                        orm_copy = deepcopy(orm)

                        # Fix SonarCloud analysis stuck on chained attribute access
                        # session = session or ctx.request.state.sql_session().sync_session
                        if session is None:
                            if ctx.request is None or not hasattr(ctx.request, "state"):
                                raise ValueError("Invalid context: request or request.state is None")
                            sql_session = getattr(ctx.request.state, "sql_session", None)
                            if sql_session is None:
                                raise ValueError("sql_session not found in request.state")
                            sync_session = getattr(sql_session(), "sync_session", None)
                            if sync_session is None:
                                raise ValueError("sync_session not found in sql_session")
                            session = sync_session
                        session.add(orm_copy)
                        orm_as_dict = orm_copy.to_dict()

                    if orm.__tablename__ in AUDIT_LOG_DISABLED_TABLES:
                        continue
                    orm_as_dict[TABLE_NAME_KEY] = orm.__tablename__
                    orm_as_dict[CREATED_AT_KEY] = now
                    orms_as_dicts.append(orm_as_dict)
                return orms_as_dicts

            create_audit_logs.delay(
                created=orms_to_dicts(audit_log_orms.created),
                updated=orms_to_dicts(audit_log_orms.updated),
                deleted=orms_to_dicts(audit_log_orms.deleted),
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            )

            if session:
                session.close()
    except Exception as e:
        logger.exception(e)


@listens_for(Engine, "rollback")
def receive_rollback_event(conn: Connection) -> None:
    """Receives the SQLAlchemy `rollback` event and removes the ORM objects from the
    `Connection.info[AUDIT_LOG_ORMS_KEY]`.
    """
    try:
        if ctx.no_audit_logs:
            return

        conn.info.pop(AUDIT_LOG_ORMS_KEY, None)
    except Exception as e:
        logger.exception(e)
