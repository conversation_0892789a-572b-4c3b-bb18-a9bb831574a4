AUDIT_LOG_ORMS_KEY = "audit_log_orms"
CREATED_AT_KEY = "created_at"
NO_AUDIT_LOGS_KEY = "no_audit_logs"
TABLE_NAME_KEY = "__tablename__"
AUDIT_LOG_DISABLED_TABLES = {
    "mrv_http_requests",
    "mrv_dndc_tasks",
    "mrv_dndc_simulation_requests",
    "mrv_dndc_results",
    "mrv_field_overlap_result",
    "mrv_boundaries_service_result",
    "mrv_project_overlap_result",
    "mrv_program_overlap_request",
    "mrv_region_overlap_result",
    "mrv_region_overlap_request",
    "mrv_project_overlap_request",
    "mrv_program_overlap_result",
    "mrv_determine_overlap_task",
    "mrv_dndc_tasks",
    "mrv_dndc_simulation_requests",
    "mrv_crop_level_outcomes",
    "mrv_field_level_outcomes",
    "mrv_boundary_rule_config",
    "mrv_boundary_rule_deviation",
    "mrv_project_completion",
    "mrv_monitor_request_group",
    "mrv_monitor_request_store",
    "mrv_monitor_response_store",
    "mrv_notifications",
    "mrv_values_history",
    "mrv_program_modeling_configuration",
    "mrv_explore_results",
    "mrv_dndc_contracted_results",
    "mrv_project_contract_line_items",
    "mrv_reporting_chart_presets",
    "mrv_program_export_runs",
    "mrv_monitor_events",
    "mrv_confidence_events",
    "mrv_attribute_events_mapping",
    "mrv_soils_override",
    "mrv_ses_migration_results",
}
