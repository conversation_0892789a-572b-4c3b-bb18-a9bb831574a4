from __future__ import annotations

from typing import TYPE_CHECKING

from fastapi import Request
from sqlalchemy.ext.asyncio import async_sessionmaker

from celery_helper.decorator_class import State
from config import get_settings
from db.db_instance import DB
from logger import get_logger

if TYPE_CHECKING:
    from types import TracebackType

    from annotations import ExcT

logger = get_logger(__name__)
settings = get_settings()


class Transacted:
    """This context manager creates and begins a transaction on entering. Create a fake request object. If the
    `request` argument is provided, each item of `request.state` will be copied to the fake request.  On exiting
    without exceptions the transaction is committed. Otherwise - rolled back. In any case, the transaction and
    connection will be closed.

    WARNING! If the real request object is changed, this won't be reflected in the fake request object. And vice versa.

    >>> async with Transacted(real_request) as fake_request:
    ...     pass

    >>> async with Transacted() as fake_request, fake_request.state.sql_session() as s:
    ...     pass
    """

    def __init__(self, request: Request | None = None) -> None:
        self.request = request

        fake_request = Request(scope={"type": "http"})
        if request:
            state = request.state
            attr_names = state.__dict__.keys() if isinstance(state, State) else state._state.keys()
            for attr_name in attr_names:
                setattr(fake_request.state, attr_name, getattr(request.state, attr_name))
        self.fake_request = fake_request

    async def __aenter__(self) -> Request:
        if not DB.engine:
            await DB.start_db(settings=settings)

        self.connection = await DB.engine.connect()
        self.transaction = await self.connection.begin()
        new_sessionmaker = async_sessionmaker(self.connection, expire_on_commit=False)
        self.fake_request.state.sql_session = new_sessionmaker
        return self.fake_request

    async def __aexit__(self, exc_type: type[ExcT] | None, exc_val: ExcT | None, exc_tb: TracebackType | None) -> None:
        try:
            if exc_type is not None and exc_val is not None:
                await self.transaction.rollback()
                raise exc_val
            else:
                await self.transaction.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            await self.transaction.close()
            await self.connection.close()
