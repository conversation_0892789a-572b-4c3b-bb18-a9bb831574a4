from __future__ import annotations

import asyncio
import time
from functools import lru_cache

import redis.asyncio as redis

from config import get_settings


@lru_cache
def get_redis() -> redis.Redis:
    return redis.from_url(get_settings().REDIS_URL, decode_responses=True)


async def acquire_lock(redis_client: redis.Redis, lock_name: str, timeout: int = 10) -> bool:
    """
    the setnx command tries to set the lock. If successful, it sets an expiration time using expire to prevent deadlocks in case the process holding the lock crashes.
    """
    end_time = time.time() + timeout
    while time.time() < end_time:
        if await redis_client.setnx(lock_name, 1):
            # Successfully acquired the lock
            await redis_client.expire(lock_name, timeout)
            return True
        await asyncio.sleep(0.1)  # Wait for 100ms before trying again
    return False


async def release_lock(redis_client: redis.Redis, lock_name: str) -> None:
    await redis_client.delete(lock_name)
