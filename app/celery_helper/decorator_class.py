import asyncio
import sys
from contextlib import suppress
from typing import Any, cast, Self, Unpack

import aiosmtplib
from celery import Task
from celery.app.registry import _unpickle_task_v2
from celery.exceptions import WorkerLostError
from celery.signals import worker_process_init, worker_process_shutdown
from pymysql.err import OperationalError as PyMySQLOperationalError
from sqlalchemy.exc import OperationalError

from celery_worker import async_to_sync
from config import get_settings
from db.db_instance import DB
from defaults.defaults import defaults_retriever
from gcloud_client.session import GcloudStorage
from helper.context import ctx
from helper.i18n import get_locale_templates_map
from http_client import HttpClient
from logger import get_logger
from mta_service import SMTP

settings = get_settings()
gcloud_storage_session = GcloudStorage()
logger = get_logger(__name__)
http_client = HttpClient()


@worker_process_init.connect
@async_to_sync
async def start_db(**kwargs: dict[str, Any]) -> None:
    await DB.start_db(settings=settings)
    get_locale_templates_map()  # cache all email templates
    tasks = [
        # The load_defaults method is not defined, I don't know how this was working before
        # defaults_retriever.load_defaults(),
        defaults_retriever.fetch_all_crops(),  # cache all crops
        defaults_retriever.fetch_fertilizer_data(),
    ]
    logger.debug("ran defaults tasks")

    if settings.MTA_SERVICE_HOST:
        tasks.append(SMTP.connect())
    http_client.start()
    gcloud_storage_session.initialise(http_client())

    await asyncio.gather(*tasks)


@worker_process_shutdown.connect
@async_to_sync
async def shutdown(**kwargs: dict[str, Any]) -> None:
    await DB.close_db()
    if settings.MTA_SERVICE_HOST:
        with suppress(aiosmtplib.SMTPServerDisconnected):
            await SMTP.quit()


class State:  # noqa: PIE793
    sql_session: Any | None = None
    fs_user_id: int
    fs_impersonator_user_id: int | None = None
    smtp_session: Any | None = None
    gcloud_storage_client: Any | None = None
    gcloud_session: Any | None = None
    is_super_user: bool | None = None


class DBTask(Task):
    _state = None
    # Configuration for retrying failed tasks because of a DB connection error
    # Move this to consul if needed.
    # To know the usage of these parameters, check here:
    # https://docs.celeryq.dev/en/master/userguide/tasks.html#Task.autoretry_for
    autoretry_for = (OperationalError, PyMySQLOperationalError, WorkerLostError)
    max_retries = 5
    retry_backoff = True
    retry_backoff_max = 120
    retry_jitter = True

    def __reduce__(self) -> Any:
        mod: str | None = type(self).__module__
        mod = mod if mod and mod in sys.modules else None
        return (
            _unpickle_db_task,
            (self.name, mod, self.state.fs_user_id, self.state.fs_impersonator_user_id, self.state.is_super_user),
            None,
        )

    @property
    def state(self) -> State:
        if self._state is None:
            self._state = State()
            self._state.gcloud_storage_client = gcloud_storage_session
            self._state.gcloud_session = http_client
        if self._state.sql_session is None:
            self._state.sql_session = DB.session
        return self._state

    def update_state(
        self, fs_user_id: int, fs_impersonator_user_id: int | None = None, is_super_user: bool | None = None
    ) -> "DBTask":
        if self._state is None:
            self._state = State()
        if self._state.sql_session is None:
            self._state.sql_session = DB.session
        self._state.fs_user_id = fs_user_id
        self._state.fs_impersonator_user_id = fs_impersonator_user_id
        self._state.is_super_user = is_super_user
        if settings.MTA_SERVICE_HOST:
            self._state.smtp_session = SMTP
        self._state.gcloud_storage_client = gcloud_storage_session
        self._state.gcloud_session = http_client

        ctx.request = self
        return self

    def _add_request(self, kwargs: dict[str, Any]) -> None:
        # HACK!!! This is a hack to add is_super_user to the request state. The reason it's a hack is that it relies
        # on the caller to provide it. This is not ideal, but it's easier than always calculating it.
        is_super_user: bool | None = kwargs.pop("is_super_user") if "is_super_user" in kwargs else None
        if not kwargs.get("request") and "fs_user_id" in kwargs and "fs_impersonator_user_id" in kwargs:
            kwargs["request"] = self.update_state(
                fs_user_id=cast(int, kwargs["fs_user_id"]),
                fs_impersonator_user_id=cast(int | None, kwargs["fs_impersonator_user_id"]),
                is_super_user=is_super_user,
            )

    def delay(self, *args: Unpack[tuple[Self]], **kwargs: dict[str, Any]) -> Any:
        self._add_request(kwargs)
        return super().delay(*args, **kwargs)

    def apply_async(
        self, args: tuple | None = None, kwargs: dict[str, Any] | None = None, **options: dict[str, Any]
    ) -> Any:
        if kwargs:
            self._add_request(kwargs)
        return super().apply_async(args=args, kwargs=kwargs, **options)


def _unpickle_db_task(
    name: str | None,
    mod: str | None,
    fs_user_id: int | None,
    fs_impersonator_user_id: int | None,
    is_super_user: bool | None,
) -> "DBTask":
    """
    We need this to correctly unpickle DBTask objects. The reason is that on the celery side, the default unpickling
    code will get a task object that has self.state == None. This ensures that the state is recreated correctly.
    """
    # HACK! This assumes Task.__reduce__() uses _unpickle_task_v2. It does at the time of writing, but this is relying
    # on the internals of celery not changing.
    task = _unpickle_task_v2(name, mod)
    task.update_state(fs_user_id, fs_impersonator_user_id, is_super_user)
    return task
