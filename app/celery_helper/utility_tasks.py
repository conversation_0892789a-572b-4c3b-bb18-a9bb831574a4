import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def log_exception_task(
    self: DBTask,
    *,
    action_type: str,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        raise ValueError(
            f"This is a test exception raised by the task log_exception_task. "
            f"SENTRY_ENABLED: {settings.SENTRY_ENABLED} and settings.env: {settings.env}."
        )
    except ValueError as e:
        if action_type == "log_exception":
            logger.exception(e)
        elif action_type == "log_error":
            logger.error(e)
        elif action_type == "raise_exception":
            raise e
        else:
            raise NotImplementedError(f"Unknown action_type: {action_type}")
