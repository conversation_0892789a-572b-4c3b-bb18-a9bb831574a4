from fastapi import APIRouter, Depends
from starlette import status
from starlette.requests import Request

from celery_helper import utility_tasks
from permissions.enums import Permission
from permissions.resolver import Permissions

router = APIRouter()


@router.post(
    "/test-celery-task",
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.CREATE_PROGRAMS]))],  # Not really appropriate but filters to internal
)
async def start_log_exception_task(request: Request, action_type: str = "log_exception") -> None:
    utility_tasks.log_exception_task.delay(
        action_type=action_type,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )
