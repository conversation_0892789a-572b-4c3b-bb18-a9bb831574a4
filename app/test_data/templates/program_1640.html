<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
    <head>
        <meta charset="utf-8"/>
        <meta name="generator" content="pandoc"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <title>Regrow - Contract program 1640 Germany</title>
        <style>
            html {
                line-height: 1.5;
                font-family: Georgia, serif;
                font-size: 20px;
                color: #1a1a1a;
                background-color: #fdfdfd;
            }

            body {
                margin: 0 auto;
                max-width: 48em;
                padding: 50px;
                hyphens: auto;
                overflow-wrap: break-word;
                text-rendering: optimizeLegibility;
                font-kerning: normal;
            }

            @media (max-width: 600px) {
                body {
                    font-size: 0.9em;
                    padding: 1em;
                }

                h1 {
                    font-size: 1.8em;
                }
            }

            @media print {
                body {
                    background-color: transparent;
                    color: black;
                    font-size: 12pt;
                }

                h2,
                h3,
                p {
                    orphans: 3;
                    widows: 3;
                }

                h2,
                h3,
                h4 {
                    page-break-after: avoid;
                }
            }

            p {
                margin: 1em 0;
                text-align: justify;
            }

            a {
                color: #1a1a1a;
            }

            a:visited {
                color: #1a1a1a;
            }

            img {
                max-width: 100%;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 1.4em;
            }

            h5,
            h6 {
                font-size: 1em;
                font-style: italic;
            }

            h6 {
                font-weight: normal;
            }

            ol,
            ul {
                padding-left: 1.7em;
                margin-top: 1em;
            }

            li > ol,
            li > ul {
                margin-top: 0;
            }

            blockquote {
                margin: 1em 0 1em 1.7em;
                padding-left: 1em;
                border-left: 2px solid #e6e6e6;
                color: #606060;
            }

            code {
                font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
                font-size: 85%;
                margin: 0;
            }

            pre {
                margin: 1em 0;
                overflow: auto;
            }

            pre code {
                padding: 0;
                overflow: visible;
                overflow-wrap: normal;
            }

            .sourceCode {
                background-color: transparent;
                overflow: visible;
            }

            hr {
                background-color: #1a1a1a;
                border: none;
                height: 1px;
                margin: 1em 0;
            }

            table {
                margin: 1em 0;
                border-collapse: collapse;
                width: 100%;
                overflow-x: auto;
                font-variant-numeric: lining-nums tabular-nums;
            }

            table caption {
                margin-bottom: 0.75em;
            }

            tbody {
                margin-top: 0.5em;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }

            th {
                border-top: 1px solid #1a1a1a;
                padding: 0.25em 0.5em;
            }

            td {
                padding: 0.125em 0.5em 0.25em;
            }

            header {
                margin-bottom: 4em;
                text-align: center;
            }

            #TOC li {
                list-style: none;
            }

            #TOC ul {
                padding-left: 1.3em;
            }

            #TOC > ul {
                padding-left: 0;
            }

            #TOC a:not(:hover) {
                text-decoration: none;
            }

            code {
                white-space: pre-wrap;
            }

            span.smallcaps {
                font-variant: small-caps;
            }

            span.underline {
                text-decoration: underline;
            }

            div.column {
                display: inline-block;
                vertical-align: top;
                width: 50%;
            }

            div.hanging-indent {
                margin-left: 1.5em;
                text-indent: -1.5em;
            }

            ul.task-list {
                list-style: none;
            }

            .display.math {
                display: block;
                text-align: center;
                margin: 0.5rem auto;

            }
            li::marker {
                width: 2.4cm;
                text-align: left !important;
            }
            .dash-list {
                list-style-type: none;
                padding-left: 1em;
            }
            .dash-list li::before {
                content: "- ";
                margin-right: 0.5em;
            }
        </style>
    </head>
    <body>
        <!-- custom program inputs. -->
        {% set ns1 = namespace(company_name_key="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith("Juristischer Firmenname") %}
                {% set ns1.company_name_key = key %}
            {% endif %}
        {% endfor %}
        {% set legal_name = custom_inputs[ns1.company_name_key] | default("") %}
        {% if legal_name == "" %}
            {% set legal_name = user_fname + " " + user_lname %}
        {% endif %}

        {% set company_number = custom_inputs["Amtliche Registriernummer"] | default("") %}

        {% set ns2 = namespace(cicoop="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith('Genossenschaft oder Handel') %}
                {% set ns2.cicoop = key %}
            {% endif %}
        {% endfor %}

        {# Template Variables for CO-OP #}
        {% set coop = custom_inputs[ns2.cicoop] | default("") %}
        {% set coop_data = lookups['eu_coop'][coop] | default({}) %}
        {% set coop_cargill_id = coop_data["ID"] | default("") %}
        {% set coop_name = coop_data["Name_contract"] | default("") %}
        {% set coop_addr1 = coop_data["House_Number"] | default("") %}
        {% set coop_addr2 = coop_data["Street"] | default("") %}
        {% set coop_addr3 = coop_data["City"] | default("") %}
        {% set coop_addr4 = coop_data["Postal_Code"] | default("") %}
        {% set coop_RCS   = coop_data["RCS"] | default("") %}
        {% set coop_addr = coop_addr1 + " " + coop_addr2 + " " + coop_addr3 + " " + coop_addr4 %}
        {% set ns = namespace(has_pollinator_engagement=false, has_agroforestry=false) %}
        {% for field in fields %}
            {% if field.note.ASSIGN_PRACTICES and ("pollinator" in field.note.ASSIGN_PRACTICES|lower or "hotspots für bestäuber" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_pollinator_engagement = true %}
            {% endif %}
            {% if field.note.ASSIGN_PRACTICES and ("agroforestry" in field.note.ASSIGN_PRACTICES|lower or "agroforstwirtschaft" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_agroforestry = true %}
            {% endif %}
        {% endfor %}
        {% set has_pollinator_engagement = ns.has_pollinator_engagement %}
        {% set has_agroforestry = ns.has_agroforestry %}

        {% set CARGILL_ACCOUNT_ID_CUSTOM_INPUT = custom_inputs["Cargill-Kennung"] | default("") %}
        {% if CARGILL_ACCOUNT_ID_CUSTOM_INPUT != "" %}
            {% set cargill_account_id =  CARGILL_ACCOUNT_ID_CUSTOM_INPUT %}
        {% endif %}

        <table cellspacing="0" cellpadding="0" style="border: none; border-collapse: separate;">
            <tr>
                <!-- Top right corner -->
                <td style="text-align: right;">
                    {{ user_fname }} {{ user_lname }}<br>
                    {{ user_email }}<br>
                    {{ cargill_account_id }}<br>
                    {% if user_phone_number %}
                    {{ user_phone_number }}<br />
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;" >
                    <!-- Contract heading -->
                    <p><strong>Anlage A zur Cargill RegenConnect</strong><sup>®</sup><strong>Landwirte Vereinbarung</strong></p>
                </td>
            </tr>
        </table>

        <!-- Contract 1st paragraph -->
        <p>
            Diese Anlage A, gültig ab dem {{ current_date.strftime('%d') }}  {{ current_date.strftime('%m') }} Tag des Jahres {{ current_date.strftime('%Y') }} ist ein integraler Bestandteil der Cargill RegenConnect Landwirte-Vereinbarung <strong>(„Vereinbarung“)</strong> und gilt zwischen {{ legal_name }}, mit Geschäftsadresse in {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }}, registriert unter der folgenden Nummer {{ company_number }}<strong>(„Teilnehmer“)</strong> und {{ coop_name }}, mit Geschäftsadresse in {{ coop_addr }}, registriert unter der folgenden Nummer {{ coop_RCS }} <strong>(„Erfasser“).</strong>
        </p>

        <!-- Contract 2nd paragraph -->
        <p>
            <strong>Präambel:</strong><br>
            <p>Alle großgeschriebenen Begriffe, die hier nicht anders definiert sind, haben die in der Vereinbarung festgelegte Bedeutung.</p>
        </p>

        <!-- Contract 3rd paragraph  -->
        <p>
            Das Portal ist das Hauptinstrument zur Messung, Quantifizierung und Verifizierung der Veränderungen in den landwirtschaftlichen Praktiken des Teilnehmers und ihrer Auswirkungen auf die Umwelt. Regrow ist der Drittanbieter, der das Portal verwaltet und eng mit dem Teilnehmer, dem Erfasser und Cargill zusammenarbeiten wird, um (i) die Umsetzung der regenerativen landwirtschaftlichen Praktiken auf den angemeldeten Feldern des Teilnehmers in Übereinstimmung mit den Konditionen dieser Vereinbarung zu überprüfen und (ii) die verbesserte Kohlenstoffbindung, die verbesserte Bodengesundheit und andere positive Umweltauswirkungen zu ermitteln.
        </p>

        <!-- Contract 4th paragraph -->
        <p>Dies vorausgeschickt vereinbaren der Teilnehmer und der Erfasser Folgendes:</p>

        <ol type="1">

            <li><!-- 1. Term -->
                <p>In dieser Anlage A werden die Einzelheiten der Verpflichtung des Teilnehmers für das Erntejahr 2026 festgelegt.</p>
            </li>

            <li style="page-break-before:always"><!-- 2. Appendix A heading-->
            </li>

            <!-- Regenerative practices selection-->
            <!-- <blockquote> -->
                <!-- <p>Pratiques Régénératives sélectionnées</p> -->
                <p><strong>Regenerative Praxis fur Erntejahr 2025–2026</strong></p>
            <!-- </blockquote> -->

            <!-- Table of regenerative practices -->
            <table width="100%" style="table-layout: fixed;">
                <thead style="vertical-align: middle;">
                    <tr class="header">
                        <th scope="col" style="padding-left:10px;text-align:left;">Name des Bauernhofes
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Bezeichnung des Felds
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Referenz-Erntejahr
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Größe (ha)
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Veränderung der Praxis
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Geschätzte Zahlung (€)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in fields %}
                    <tr>
                        <td style="padding-top:10px;">{{ row.note["farm_name"]}}</td>
                        <td style="padding-top:10px;">{{ row.note["field_name"]}}</td>
                        <td style="padding-top:10px;">{{ "" if row.note["baseline_year"] == None else row.note["baseline_year"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                        <td style="padding-top:10px;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.value == None else "%.2f"|format(row.value) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- total estimation-->
            <!-- <blockquote> -->
                <div style="text-align:left;">
                    <div style="margin-bottom:7px;">
                        Geschätzte Gesamtsequestrierung
                    </div>

                    <div style="font-size:12px;color:#6f6f6f;margin:7px 0;">
                        für Grundstücke, {{ "%.2f"|format(total_area) }} {{ user_units }}
                    </div>

                    <table cellpadding="0" cellspacing="0" border="0" style="padding:10px;text-align:left;line-height:1;width:100%;box-sizing:border-box;border:1px solid #e0e2e3;">
                        <tbody>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Erklärtes Interesse an Agroforstwirtschaft
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Ja" if has_agroforestry else "Nein" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Bestäuber-Engagement
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Ja (250" + currency_char + ")" if has_pollinator_engagement else "Nein" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="padding-top:10px; width: 70%;">
                                    Geschätzte Gesamtzahlung (EUR 37 / t CO2 / Jahr)
                                </td>
                                <td style="padding-top:10px;text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "%.2f"|format(payment) }}{{ currency_char }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <!-- </blockquote> -->

            <ol start="3" type="1">
                <li><!-- 3. Historical data -->
                    <p><u>Hochladen auf das Portal :</u>Der Teilnehmer wird (i) die Felder und (ii) historische Bewirtschaftungsdaten für jedes Feld auf das Portal hochladen. Zu den historischen Bewirtschaftungsdaten gehören die folgenden Informationen für die fünf (5) Erntejahre vor dem Erntejahr 2026: Hauptkulturen (Anpflanzungs- und Erntedatum), ggf. Zwischenfrüchte (Anpflanzungs- und Beendigungsdatum) und Art der Bodenbearbeitung. Das Portal erstellt dann für jedes Feld Kohlenstoffbindungs- und Zahlungsschätzungen.</p>
                </li>

                <li><!-- 4. Measurements-->
                    <p><u>Messung: </u> Am Ende der Vegetationsperiode wird der Teilnehmer über das Portal die vollständige und ordnungsgemäße Umsetzung und Vervollständigung der in Abschnitt 2 oben dargelegten landwirtschaftlichen Praktiken bestätigen.</p>
                </li>

                <li><!-- 5. Verification-->
                    <p><u>Überprüfung:</u> Cargill/Regrow wird Fernerkundung (d. h. über Satellitentechnologie) einsetzen, um zu überprüfen, ob die vereinbarten landwirtschaftlichen Praktiken vom Teilnehmer auf den angemeldeten Feldern umgesetzt wurden. Konflikte auf Feldebene werden durch Überprüfung der Teilnehmerdatensätze gelöst. Der Teilnehmer ist dafür verantwortlich, auf Anfrage von Cargill Dokumente oder Aufzeichnungen bereitzustellen, um zu bestätigen, dass die vertraglich vereinbarten landwirtschaftlichen Praktiken stattgefunden haben, wenn eine landwirtschaftliche Praxis nicht durch Fernerkundung bestätigt werden kann</p>
                </li>

                <li><!-- 6. Final results-->
                    <p><u>Endgültige Ergebnisse:</u> Auf der Grundlage der obigen Punkte 2-5 berechnen Regrow und Cargill die finale Nettoveränderung der Sequestrierungsrate von organischem Kohlenstoff im Boden sowie der Methan- und N20-Emissionen zwischen dem Referenzniveau zu Beginn des Programms und den neuen Maßnahmen.</p>
                </li>

                <li><!-- 7. Measurements requirements-->
                    <p><u>Anforderungen für die Messung:</u> Während der Messung wird der Teilnehmer Informationen über die Saison des Teilnehmers auf dem Portal sowie zusätzliche landwirtschaftliche Managementinformationen über die Winter- und Frühlingspraktiken des Teilnehmers hochladen.</p>
                    <ul>
                        <li>
                            <p>Bestätigung der Marktfrüchte (cash crop)</p>
                        </li>
                        <li>
                            <p>Bestätigung der durchgeführten Praxisänderungen (Bodenbearbeitung und Zwischenfruchtanbau)</p>
                        </li>
                        <li>
                            <p>Ertrag der Hauptkultur</p>
                        </li>
                        <li>
                            <p>Beendigung (Management von Ernterückeständen)</p>
                        </li>
                        <li>
                            <p>Weitere Informationen zur Bodenbearbeitung (Bodenbearbeitungstiefe)</p>
                        </li>
                        <li>
                            <p>Bewässerungsmethode</p>
                        </li>
                    </ul>
                </li>
                <li><!-- 8 -->
                    <p><u>Zusätzliche Vorteile:</u> Während der Messung und Überprüfung der vereinbarten regenerativen landwirtschaftlichen Praktiken wird Cargill eine Berechnung der potenziellen zusätzlichen Umweltvorteile wie Pflanzenvielfalt, Bodenbedeckung und Auswirkungen der Direktsaat auf die Wasserrückhaltung in Bereiche mit Wasserknappheitsrisiko gemäß der Definition von Cargill in einem bestimmten Jahr durchführen <strong>(„Zusätzliche Vorteile“)</strong>. Auf Grundlage dieser Berechnungen kann Cargill entscheiden, dem Teilnehmer eine zusätzliche Vergütung  pro Hektar für die Zusätzliche Vorteile  zu gewähren <strong>(„Zusätzliche Vergütung“)</strong>.</p>
                    <p>Für das Erntejahr 2026 umfassen die Zusätzliche Vorteile die folgenden Programme:
                        <ul class="dash-list">
                            <li><u>Pflanzenvielfaltsbonus:</u> abhängig von der Artenanzahl und gemessen an der Gesamtfläche bis zu 2 €/ha als Zusätzliche Vergütung;</li>
                            <li><u>Bodenbedeckungssatzbonus:</u> je nach Anzahl der Tage unter grüner Pflanzenbedeckung für eine bestimmte Parzelle während der 365 Tage vor der Ernte der Kampagne 2026, bis zu 1 €/ha als Zusätzliche Vergütung;</li>
                            <li><u>Wasserrückhaltungbonus:</u> je nach Standort des Feldes bei kontrollierter Direktsaatpraxis (ohne Pflügen) bis zu 1 €/ha als Zusätzliche Vergütung.</li>
                        </ul>
                    </p>
                </li>

                <li><!-- 9 -->
                    <p><u>Bestäubungsprämie:</u> Ab der Kampagne 2026 kann sich der Teilnehmer verpflichten, eine zusätzliche regenerative Praxis umzusetzen, die gezielt die Bestäuberpopulationen (einschließlich wilder Bestäuber) in Feldfrüchten unterstützt. Diese Maßnahme umfasst unter anderem die Zusammenarbeit mit lokal zugelassenen Berufsimkern – etwa durch die Ermöglichung des Zugangs zu Feldern sowie die Bereitstellung von Unterständen, Schattenplätzen und ähnlichen Einrichtungen. Im Rahmen dieser Praxis ist der Teilnehmer verpflichtet, die Registrierungs-ID des jeweiligen Imkers anzugeben. Auf der Grundlage dieser Registrierungs-ID zahlt Cargill dem Teilnehmer eine Prämie von 250 EUR als jährliche Pauschalzahlung pro teilnehmendem Bauernhof <strong>(„Bestäubungsprämie“)</strong>.</p>
                </li>

                <li><!-- 10 -->
                    <p><u>Erntejahr 2026:</u> Die Zusätzliche Vergütung und die Bestäubungsprämie fallen zusätzlich zur Vergütung (wie im Vertrag definiert) an und sind Teil der Gesamtvergütung. Sowohl die Zusätzliche Vergütung als auch die Bestäubungsprämie gelten nur für das Erntejahr 2026. Cargill behält sich das Recht vor, zu überprüfen, ob in zukünftigen Erntejahren eine Zusätzlichen Vergütung und/oder eine Bestäubungsprämie gezahlt wird.</p>
                </li>

                <li><!-- 11 -->
                    <p><u>Agroforstwirtschaft:</u> Im Rahmen der Erklärung seiner Praktiken kann der Teilnehmer seine Bereitschaft zum Ausdruck bringen, zusätzliche regenerative Praktiken in Form von Agroforstwirtschaft umzusetzen. Durch diese Interessenbekundung entsteht dem Teilnehmer keine Prämie oder Zusätzliche Vergütung. Cargill ist bestrebt, Beziehungen zu anderen interessierten Drittparteien aufzubauen, die zu einer gemeinsamen Umsetzung der Praktiken führen können, die in einem separaten Vertrag festgelegt wird.</p>
                </li>

                <li><!-- 12 -->
                    <p><u>Regel für adaptive Bodenbearbeitung:</u> Abweichend von Klausel 3 der Vereinbarung gelten hinsichtlich der Messung für das Erntejahr 2025 und die nachfolgenden Erntejahre die folgenden Regeln für die konventionelle Bodenbearbeitung:
                        <ul>
                            <li>Die konventionelle Bodenbearbeitung darf nicht als beabsichtigte Praxis für ein bestimmtes Erntejahr deklariert werden (ein Feld mit einer im Voraus beabsichtigten konventionellen Bodenbearbeitung wird nicht registriert), jedoch</li>
                            <li>kann die Berichterstattung über die konventionelle Bodenbearbeitung, wenn dies von Cargill bestätigt wird, in der Messphase (d. h. nach der Ernte in dem betreffenden Erntejahr) einmal pro 3-Jahres-Zeitraum für ein bestimmtes Feld ausnahmsweise akzeptiert werden (sogenannt: adaptive Bodenbearbeitung), wenn eine Ausnahme (wie nachfolgend beschrieben) zutrifft.</li>
                            <li>Der Ausnahmekatalog sieht folgende Punkte vor: widrige Witterungsbedingungen wie Überschwemmungen oder übermäßige Niederschläge, Notwendigkeit des Pflanzenschutzes wie übermäßiges Jäten, widrige Bedingungen bei der Ernte der vorherigen Ernte (z. B. erhebliche Bodenverdichtung);</li>
                            <li>Wnn die konventionelle Bodenbearbeitungsmethode für ein drittes Erntejahr der Teilnahme des Landwirts gemeldet wird, erklärt sich der Teilnehmer damit einverstanden, die Überwachungsrechte für Cargill (wie in der Vereinbarung beschrieben) für ein weiteres Erntejahr auszudehnen;</li>
                            <li>Der Teilnehmer ist verpflichtet, Cargill jedes Mal unverzüglich schriftlich oder per E-Mail zu benachrichtigen, wenn die Notwendigkeit einer ausnahmsweisen konventionellen Bodenbearbeitung entsteht.</li>
                        </ul>
                        Die Anwendung ausnahmsweiser konventioneller Bodenbearbeitung kann zu niedrigeren oder negativen Werten der Gesamtvergütung für ein bestimmtes Erntejahr führen.
                    </p>
                </li>

                <li><!-- 13 -->
                    <p>Soweit nicht vorstehend ausdrücklich etwas anderes geregelt ist, gelten alle in der Vereinbarung enthaltenen Bestimmungen fort. Bei Widersprüchen zwischen dieser Anlage A und der Vereinbarung gelten die Bestimmungen dieser Anlage A.</p>
                </li>

            </ol>

        </ol>

        <table width="100%">
            <tbody style="border: none;">
            <tr>
                <td >
                    <strong>Teilnehmer</strong>
                    <p>Vorname/Name: {{ user_fname }} {{ user_lname }}</p>
                    <p>Adresse: {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }}</p>
                    <p>Unterschrift: <span style="display: inline-block; color: white;">/sn1/</span></p>
                </td>
            </tr>
            <tr>
                <td >
                    <strong>Erfasser</strong>
                    <p>Erfassername: {{ coop_name }}</p>
                    <p>Adresse: {{ coop_addr }}</p>
                    <!-- No co-signer for this program. but we show the signature field for manual signning, without docusign signature for co-signer. -->
                    <p>Unterschrift: </p>
                </td>
            </tr>
            </tbody>
        </table>
    </body>
</html>
