<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
    <head>
        <meta charset="utf-8"/>
        <meta name="generator" content="pandoc"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <title>Regrow - Contract program 1642 Romania</title>
        <style>
            html {
                line-height: 1.5;
                font-family: Georgia, serif;
                font-size: 20px;
                color: #1a1a1a;
                background-color: #fdfdfd;
            }

            body {
                margin: 0 auto;
                max-width: 48em;
                padding: 50px;
                hyphens: auto;
                overflow-wrap: break-word;
                text-rendering: optimizeLegibility;
                font-kerning: normal;
            }

            @media (max-width: 600px) {
                body {
                    font-size: 0.9em;
                    padding: 1em;
                }

                h1 {
                    font-size: 1.8em;
                }
            }

            @media print {
                body {
                    background-color: transparent;
                    color: black;
                    font-size: 12pt;
                }

                h2,
                h3,
                p {
                    orphans: 3;
                    widows: 3;
                }

                h2,
                h3,
                h4 {
                    page-break-after: avoid;
                }
            }

            p {
                margin: 1em 0;
                text-align: justify;
            }

            a {
                color: #1a1a1a;
            }

            a:visited {
                color: #1a1a1a;
            }

            img {
                max-width: 100%;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 1.4em;
            }

            h5,
            h6 {
                font-size: 1em;
                font-style: italic;
            }

            h6 {
                font-weight: normal;
            }

            ol,
            ul {
                padding-left: 1.7em;
                margin-top: 1em;
            }

            li > ol,
            li > ul {
                margin-top: 0;
            }

            blockquote {
                margin: 1em 0 1em 1.7em;
                padding-left: 1em;
                border-left: 2px solid #e6e6e6;
                color: #606060;
            }

            code {
                font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
                font-size: 85%;
                margin: 0;
            }

            pre {
                margin: 1em 0;
                overflow: auto;
            }

            pre code {
                padding: 0;
                overflow: visible;
                overflow-wrap: normal;
            }

            .sourceCode {
                background-color: transparent;
                overflow: visible;
            }

            hr {
                background-color: #1a1a1a;
                border: none;
                height: 1px;
                margin: 1em 0;
            }

            table {
                margin: 1em 0;
                border-collapse: collapse;
                width: 100%;
                overflow-x: auto;
                font-variant-numeric: lining-nums tabular-nums;
            }

            table caption {
                margin-bottom: 0.75em;
            }

            tbody {
                margin-top: 0.5em;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }

            th {
                border-top: 1px solid #1a1a1a;
                padding: 0.25em 0.5em;
            }

            td {
                padding: 0.125em 0.5em 0.25em;
            }

            header {
                margin-bottom: 4em;
                text-align: center;
            }

            #TOC li {
                list-style: none;
            }

            #TOC ul {
                padding-left: 1.3em;
            }

            #TOC > ul {
                padding-left: 0;
            }

            #TOC a:not(:hover) {
                text-decoration: none;
            }

            code {
                white-space: pre-wrap;
            }

            span.smallcaps {
                font-variant: small-caps;
            }

            span.underline {
                text-decoration: underline;
            }

            div.column {
                display: inline-block;
                vertical-align: top;
                width: 50%;
            }

            div.hanging-indent {
                margin-left: 1.5em;
                text-indent: -1.5em;
            }

            ul.task-list {
                list-style: none;
            }

            .display.math {
                display: block;
                text-align: center;
                margin: 0.5rem auto;

            }
            li::marker {
                width: 2.4cm;
                text-align: left !important;
            }
        </style>
    </head>
    <body>
        {% set ns1 = namespace(company_name_key="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith('Numele companiei') or key.startswith('Denumirea completă a entității legale')%}
                {% set ns1.company_name_key = key %}
            {% endif %}
        {% endfor %}
        {% set legal_name = custom_inputs[ns1.company_name_key] | default("") %}
        {% if legal_name == "" %}
            {% set legal_name = user_fname + " " + user_lname %}
        {% endif %}

        {% set CUI = custom_inputs["CUI"] | default("") %}

        {% set ns = namespace(has_pollinator_engagement=false, has_agroforestry=false) %}
        {% for field in fields %}
            {% if field.note.ASSIGN_PRACTICES and ("pollinator" in field.note.ASSIGN_PRACTICES|lower or "zone-cheie pentru polenizatori" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_pollinator_engagement = true %}
            {% endif %}
            {% if field.note.ASSIGN_PRACTICES and ("agroforestry" in field.note.ASSIGN_PRACTICES|lower or "agrosilvicultură" in field.note.ASSIGN_PRACTICES|lower or "agroforest" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_agroforestry = true %}
            {% endif %}
        {% endfor %}
        {% set has_pollinator_engagement = ns.has_pollinator_engagement %}
        {% set has_agroforestry = ns.has_agroforestry %}

        {% set CARGILL_ACCOUNT_ID_CUSTOM_INPUT = custom_inputs["Numărul de identificare Cargill"] | default("") %}
        {% if CARGILL_ACCOUNT_ID_CUSTOM_INPUT != "" %}
            {% set cargill_account_id =  CARGILL_ACCOUNT_ID_CUSTOM_INPUT %}
        {% endif %}

        <table cellspacing="0" cellpadding="0" style="border: none; border-collapse: separate;">
            <tr>
                <td style="text-align: right;">
                    {{ user_fname }} {{ user_lname }}<br>
                    {{ user_email }}<br>
                    {{ cargill_account_id }}<br>
                    {% if user_phone_number %}
                    {{ user_phone_number }}<br />
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;" >
                    <p><strong>Anexa A la Contractul pentru fermieri Cargill RegenConnect</strong><sup>®</sup></p>
                </td>
            </tr>
        </table>

        <p>
            Prezenta Anexă A, în vigoare din ziua de [{{ current_date.strftime('%d') }}], luna [{{ current_date.strftime('%m') }}], anul 2025, face parte integrantă din Contractul pentru Fermieri Cargill RegenConnect<sup>®</sup> („<strong>Contractul</strong>"), încheiat între {{ legal_name }} cu sediul social înregistrat la {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }}, CUI {{ CUI }}, reprezentant legal {{ user_fname }} {{ user_lname }}, numărul de identificare Cargill SAP ID {{ cargill_account_id }} („<strong>Participantul</strong>") și CARGILL AGRICULTURA SRL, cu sediul în București, Strada Aviator Popișteanu nr. 54A, clădirea 2, etaj 5, Sector 1, înregistrată la Oficiul Registrului Comerțului sub nr.J1996009120405, CUI RO8915840, („<strong>Cargill</strong>").
        </p>

        <p>
            <strong>ÎNTRUCÂT</strong>, toți termenii scriși cu majuscule care nu sunt altfel definiți în prezentul document au înțelesul stabilit în Contract.
        </p>

        <p>
            <strong>ÎNTRUCÂT,</strong> Portalul este mijlocul cheie de măsurare, cuantificare și verificare a schimbărilor în practicile agricole ale Participantului și a impactului acestora asupra mediului, iar Regrow este administratorul terț al Portalului care va colabora îndeaproape cu Participantul și Cargill pentru a verifica implementarea practicilor agricole regenerative pe parcelele agricole participante ale Participantului în conformitate cu termenii Contractului și pentru a determina o mai bună sechestrare a carbonului, o mai bună sănătate a solului și alte rezultate pozitive pentru mediu.</td>
        </p>

        <p><strong>PRIN URMARE</strong>, Participantul și Cargill convin următoarele:</p>

        <ol type="1">

            <li><!-- 1. Term -->
                <p>Această Anexă A prezintă detaliile angajamentului Participantului pentru Anul Agricol –2026.</p>
            </li>

            <li style="page-break-before:always"><!-- 2. Appendix A heading-->
                <!-- <p><strong>Anexa A - Practici agricole și specificații ale parcelei/lor</strong></p> -->
            </li>

            <!-- Regenerative practices selection-->
            <!-- <blockquote> -->
                <!-- <p>Practici regenerative selectate</p> -->
                <p><strong>Practici regenerative utilizate în timpul sezonului agricol 2024-2025</strong></p>
            <!-- </blockquote> -->

            <!-- Table of regenerative practices -->
            <table width="100%" style="table-layout: fixed;">
                <thead style="vertical-align: middle;">
                    <tr class="header">
                        <th scope="col" style="padding-left:10px;text-align:left;">Fermă
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Parcela
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Primul an de înscriere
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Suprafaţă (ha)
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Practici regenerative
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Plată estimată (€)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in fields %}
                    <tr>
                        <td style="padding-top:10px;">{{ row.note["farm_name"]}}</td>
                        <td style="padding-top:10px;">{{ row.note["field_name"]}}</td>
                        <!-- todo: Fix the below line with the baseline year from backend. -->
                        <td style="padding-top:10px;">{{ "" if row.note["baseline_year"] == None else row.note["baseline_year"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                        <td style="padding-top:10px;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.value == None else "%.2f"|format(row.value) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- total estimation-->
            <!-- <blockquote> -->
                <div style="text-align:left;">
                    <div style="margin-bottom:7px;">
                        Sechestrarea totală estimată
                    </div>

                    <div style="font-size:12px;color:#6f6f6f;margin:7px 0;">
                        <!-- Todo:  Get the string for total area in the correct language. -->
                        Pentru, {{ "%.2f"|format(total_area) }} {{ user_units }}
                    </div>

                    <table cellpadding="0" cellspacing="0" border="0" style="padding:10px;text-align:left;line-height:1;width:100%;box-sizing:border-box;border:1px solid #e0e2e3;">
                        <tbody>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Interes manifestat pentru agroforesterie
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Da" if has_agroforestry else "Nu" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Angajarea polenizatorilor
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Da (250" + currency_char + ")" if has_pollinator_engagement else "Nu" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="padding-top:10px; width: 70%;">
                                    Plată totala estimată (35 EUR / t CO2 / an)
                                </td>
                                <td style="padding-top:10px;text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "%.2f"|format(payment) }}{{ currency_char }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <!-- </blockquote> -->
        </ol>

        <ol type="1" start="3">
            <li><!-- 3. Historical data -->
                <p>Încărcare date în Portal: Participantul va încărca pe Portal (i) Parcelele și (ii) datele istorice de gestionare pentru fiecare parcela. Datele istorice de gestionare includ următoarele informații pentru ultimii cinci (5) ani agricoli anterior –Anului Agricol 2026: culturile principale (data semănatului și recoltării), dacă sunt covoare vegetale (data semănatului și data de terminare) și tipul de lucrare a solului. Portalul generează apoi estimări ale sechestrării carbonului și ale plăților pentru fiecare parcelă agricolă.</p>
            </li>

            <li><!-- 4. Measurements-->
                <p>Măsurători: La sfârșitul sezonului de vegetație, Participantul confirmă prin intermediul Portalului punerea în aplicare și finalizarea integrală și adecvată a practicilor agricole prevăzute la clauza 2 de mai sus.</p>
            </li>

            <li><!-- 5. Verification-->
                <p>Verificare: Cargill/Regrow va aplica teledetecția (spre exemplu prin intermediul tehnologiei prin satelit) pentru a verifica dacă practicile agricole convenite au fost implementate de către Participant pe parcelele înscrise. Conflictele la nivel de parcelă agricolă sunt rezolvate prin verificarea documentelor Participantului. Participantul este responsabil pentru furnizarea de documente sau înregistrări care să confirme că Practicile Agricole contractate au avut loc, la cererea Cargill, în cazul în care o Practică Agricolă nu poate fi confirmată prin teledetecție.</p>
            </li>

            <li><!-- 6. Final results-->
                <p>Rezultate Finale: Pe baza punctelor 2-4-5 de mai sus, Regrow și Cargill vor calcula schimbarea netă totală finală a ratei de captare a carbonului organic din sol, a emisiilor de metan și N2O între scenariul de referință al proiectului și noile intervenții.</p>
            </li>

            <li><!-- 7. Measurements requirements-->
                <p>Cerințe pentru Măsurare: În timpul etapelor de măsurare, se vor colecta informații despre primul sezon al Participantului pe Portal, precum și informații suplimentare privind gestionarea fermei, referitoare la practicile de iarnă și de primăvară ale Participantului. Aceasta include, dar nu se limitează la următoarele:</p>
                <ul style="list-style-type: disc;">
                    <li>
                        <p>Confirmarea culturii principale</p>
                    </li>
                    <li>
                        <p>Confirmarea implementării schimbărilor de practici (lucrările solului și covoare vegetale)</p>
                    </li>
                    <li>
                        <p>Producțiile obținute</p>
                    </li>
                    <li>
                        <p>Recoltare (gestionarea resturilor vegetale) </p>
                    </li>
                    <li>
                        <p>Informații suplimentare privind lucrările solului (adâncimea de lucru)</p>
                    </li>
                    <li>
                        <p>Metoda de irigare</p>
                    </li>
                </ul>
            </li>
            <li><!-- 8 -->
                <p><u>Beneficii Suplimentare</u>: Ca parte a Măsurării şi verificării Practicilor Regenerative Angajate, Cargill va efectua un calcul al potențialelor beneficii suplimentare pentru mediu în ceea ce privește diversitatea culturilor, durata de acoperire a solului şi impactul practicii de retenţie a apei în zonele cu risc de deficit de precipitații, aşa cum sunt definite de Cargill într-un anumit an <strong>(“Beneficii Suplimentare”)</strong>. Pe baza acestor calcule, Cargill poate decide să ofere Participantului compensații suplimentare pentru Beneficiile Suplimentare pe hectar <strong>(“Compensație Suplimentară”)</strong>.</p>
                <p>În ceea ce privește Anul Agricol 2026, Beneficiile Suplimentare includ următoarele scheme:</p>
                <ul style="list-style-type: disc;">
                    <li>
                       <strong>prima de diversitate a culturilor</strong>: în funcție de numărul de specii înregistrate  pe Parcelele înscrise, până la 2 EUR/ha de Compensație Suplimentară;
                    </li>
                    <li>
                       <strong>prima de acoperire în verde</strong>: în funcție de numărul de zile în care Parcelele înscrise sunt păstrate sub acoperire verde, măsurată în funcție de perioada de 365 de zile anterioară datei de recoltare a culturii 2026, până la 1 EUR/ha de Compensație Suplimentară;
                    </li>
                    <li>
                       <strong>prima de retenție a apei</strong>: în funcție de locația Parcelelor aflate în practică măsurată, până la 1 EUR/ha de Compensație Suplimentară.
                    </li>

                </ul>

            </li>
            <li><!-- 9-->
                <p><u>Primă de Polenizare</u>: Pentru Anul Agricol 2026, Participantul se poate angaja la o Practică Regenerativă suplimentară care vizează sprijinirea populației de polenizatori (inclusiv polenizatori sălbatici) în cultivarea culturilor în rânduri. Practica poate include stabilirea cooperării cu apicultori profesioniști, înregistrați la nivel local, în jurul activităților precum facilitarea accesului la parcele, instalarea de adăposturi  verzi, zone naturale de umbrire etc. Ca parte a Măsurării, Participantului i se va cere să furnizeze ID-ul de înregistrare al unui apicultor acreditat. Pe baza unui astfel de ID de înregistrare, Cargill va plăti Participantului o primă de 250 EUR ca plată anuală forfetară per fermă participantă <strong>(„Primă de Polenizare”)</strong>.</p>
            </li>
            <li><!-- 10-->
                <p><u>Anul Agricol 2026</u>: Compensația Suplimentară și Prima de Polenizare sunt suplimentare față de Compensație (așa cum este definită în Acord) și sunt plătibile ca parte a Sumei Totale a Compensației. Atât Compensația Suplimentară, cât și Prima de Polenizare se aplică numai pentru Anul Agricol 2026, iar Cargill își rezervă dreptul de a revizui dacă o Compensație Suplimentară și/sau o Primă de Polenizare vor fi plătite în anii agricoli viitori.</p>
            </li>
            <li><!-- 11-->
                <p><u>Agro-silvicultură</u>: Ca parte a Înscrierii, Participantul poate declara dorința de a implementa Practici Regenerative suplimentare sub formă de agro-silvicultură. Exprimarea interesului nu va genera nicio primă suplimentară sau Compensație pentru Participant. Cargill se va strădui să stabilească legături cu alte terțe părți interesate, ceea ce poate duce la implementarea în colaborare a practicii, care urmează să fie definită într-un contract separat.</p>
            </li>
            <li><!-- 12-->
                <p><u>Regula de Lucrare Adaptivă a Solului</u>: Prin derogare de la Clauza 3 a Acordului, în ceea ce privește Măsurarea pentru Anul Agricol 2025 și pentru anii agricoli următori, se vor aplica următoarele reguli pentru lucrarea convențională a solului:</p>
                <ul style="list-style-type: disc;">
                    <li>practica de lucrare convențională a solului nu va fi declarată ca practică intenționată pentru un anumit An Agricol (Parcela pentru care se declară din start intenția de a aplica lucrarea convențională a solului nu va fi înscrisă), însă</li>
                    <li>practica de raportare a lucrării convenționale a solului poate fi, dacă este confirmată de Cargill, acceptată în mod excepțional (denumită: „lucrare adaptivă a solului”) în faza de Măsurare (adică după recoltarea Anului Agricol respectiv), o singură dată într-o perioadă de 3 ani pentru o anumită Parcelă, dacă se aplică o excepție (conform celor de mai jos);</li>
                    <li>catalogul de excepții este următorul: condiții meteorologice nefavorabile, cum ar fi inundații sau precipitații excesive; necesitatea protecției plantelor, cum ar fi infestarea severă cu buruieni; condiții nefavorabile la recoltarea culturii anterioare (de exemplu, compactare semnificativă a solului);</li>
                    <li>dacă practica de lucrare convențională a solului este raportată pentru al 3-lea An Agricol de participare a fermierului, Participantul este de acord să extindă drepturile de monitorizare (așa cum sunt descrise în Acord) pentru Cargill pentru încă un An Agricol;</li>
                    <li>Participantul este obligat să notifice prompt Cargill în scris sau prin e-mail de fiecare dată când apare necesitatea aplicării excepționale a lucrării convenționale a solului.</li>
                </ul>
                <p>Aplicarea lucrării convenționale excepționale a solului poate duce la valori reduse sau negative ale Compensației Totale pentru Anul Agricol respectiv.</p>
            </li>
            <li><!-- 13-->
                <p>Cu excepția celor menționate mai sus, toți termenii și condițiile cuprinse în Contract rămân în vigoare. În cazul în care există un conflict între prezenta anexă A și Contract, se aplică termenii prezentei anexe A.</p>
            </li>
        </ol>

        <table width="100%">
            <tbody style="border: none;">
            <tr>
                <td >
                    <strong>Participant</strong>
                    <p>Prin: {{ user_fname }} {{ user_lname }}</p>
                    <p>Semnătura: <span style="display: inline-block; height:60px; color: white;">/sn1/</span></p>
                </td>
            </tr>
            <tr>
                <td >
                    <strong>Cargill</strong>
                    <p>Prin: {{ co_signer_name if co_signer_name else "Mădălina Munteanu" }}</p>
                    <p>Semnătura: <span style="display: inline-block; height:60px; color: white; position: relative; top: -40px;">/co_signature/</span></p>
                </td>
            </tr>
            </tbody>
        </table>

    </body>
</html>
