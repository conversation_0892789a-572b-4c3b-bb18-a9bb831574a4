<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
    <head>
        <meta charset="utf-8"/>
        <meta name="generator" content="pandoc"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <title>Regrow - Contract program 1643 Hungary</title>
        <style>
            html {
                line-height: 1.5;
                font-family: Georgia, serif;
                font-size: 20px;
                color: #1a1a1a;
                background-color: #fdfdfd;
            }

            body {
                margin: 0 auto;
                max-width: 48em;
                padding: 50px;
                hyphens: auto;
                overflow-wrap: break-word;
                text-rendering: optimizeLegibility;
                font-kerning: normal;
            }

            @media (max-width: 600px) {
                body {
                    font-size: 0.9em;
                    padding: 1em;
                }

                h1 {
                    font-size: 1.8em;
                }
            }

            @media print {
                body {
                    background-color: transparent;
                    color: black;
                    font-size: 12pt;
                }

                h2,
                h3,
                p {
                    orphans: 3;
                    widows: 3;
                }

                h2,
                h3,
                h4 {
                    page-break-after: avoid;
                }
            }

            p {
                margin: 1em 0;
                text-align: justify;
            }

            a {
                color: #1a1a1a;
            }

            a:visited {
                color: #1a1a1a;
            }

            img {
                max-width: 100%;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 1.4em;
            }

            h5,
            h6 {
                font-size: 1em;
                font-style: italic;
            }

            h6 {
                font-weight: normal;
            }

            ol,
            ul {
                padding-left: 1.7em;
                margin-top: 1em;
            }

            li > ol,
            li > ul {
                margin-top: 0;
            }

            blockquote {
                margin: 1em 0 1em 1.7em;
                padding-left: 1em;
                border-left: 2px solid #e6e6e6;
                color: #606060;
            }

            code {
                font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
                font-size: 85%;
                margin: 0;
            }

            pre {
                margin: 1em 0;
                overflow: auto;
            }

            pre code {
                padding: 0;
                overflow: visible;
                overflow-wrap: normal;
            }

            .sourceCode {
                background-color: transparent;
                overflow: visible;
            }

            hr {
                background-color: #1a1a1a;
                border: none;
                height: 1px;
                margin: 1em 0;
            }

            table {
                margin: 1em 0;
                border-collapse: collapse;
                width: 100%;
                overflow-x: auto;
                font-variant-numeric: lining-nums tabular-nums;
            }

            table caption {
                margin-bottom: 0.75em;
            }

            tbody {
                margin-top: 0.5em;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }

            th {
                border-top: 1px solid #1a1a1a;
                padding: 0.25em 0.5em;
            }

            td {
                padding: 0.125em 0.5em 0.25em;
            }

            header {
                margin-bottom: 4em;
                text-align: center;
            }

            #TOC li {
                list-style: none;
            }

            #TOC ul {
                padding-left: 1.3em;
            }

            #TOC > ul {
                padding-left: 0;
            }

            #TOC a:not(:hover) {
                text-decoration: none;
            }

            code {
                white-space: pre-wrap;
            }

            span.smallcaps {
                font-variant: small-caps;
            }

            span.underline {
                text-decoration: underline;
            }

            div.column {
                display: inline-block;
                vertical-align: top;
                width: 50%;
            }

            div.hanging-indent {
                margin-left: 1.5em;
                text-indent: -1.5em;
            }

            ul.task-list {
                list-style: none;
            }

            .display.math {
                display: block;
                text-align: center;
                margin: 0.5rem auto;

            }
            li::marker {
                width: 2.4cm;
                text-align: left !important;
            }
        </style>
    </head>
    <body>
        {% set ns1 = namespace(company_name_key="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith('Cégnév') %}
                {% set ns1.company_name_key = key %}
            {% endif %}
        {% endfor %}
        {% set legal_name = custom_inputs[ns1.company_name_key] | default("") %}
        {% if legal_name == "" %}
            {% set legal_name = user_fname + " " + user_lname %}
        {% endif %}

        {% set tax_number = custom_inputs["Adószám"] | default("") %}

        {% set ns = namespace(has_pollinator_engagement=false, has_agroforestry=false) %}
        {% for field in fields %}
            {% if field.note.ASSIGN_PRACTICES and ("pollinator" in field.note.ASSIGN_PRACTICES|lower or "beporzó pontok" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_pollinator_engagement = true %}
            {% endif %}
            {% if field.note.ASSIGN_PRACTICES and ("agroforestry" in field.note.ASSIGN_PRACTICES|lower or "agroerdészet" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_agroforestry = true %}
            {% endif %}
        {% endfor %}
        {% set has_pollinator_engagement = ns.has_pollinator_engagement %}
        {% set has_agroforestry = ns.has_agroforestry %}

        {% set CARGILL_ACCOUNT_ID_CUSTOM_INPUT = custom_inputs["Cargill azonosító"] | default("") %}
        {% if CARGILL_ACCOUNT_ID_CUSTOM_INPUT != "" %}
            {% set cargill_account_id =  CARGILL_ACCOUNT_ID_CUSTOM_INPUT %}
        {% endif %}

        <table cellspacing="0" cellpadding="0" style="border: none; border-collapse: separate;">
            <tr>
                <td style="text-align: right;">
                    {{ user_fname }} {{ user_lname }}<br>
                    {{ user_email }}<br>
                    {{ cargill_account_id }}<br>
                    {% if user_phone_number %}
                    {{ user_phone_number }}<br />
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;" >
                    <p><strong>A Cargill RegenConnect<sup>®</sup> Mezőgazdasági Termelői Megállapodás A. melléklete</strong></p>
                </td>
            </tr>
        </table>

        <p>
            Jelen A. melléklet, amely elválaszthatatlan részét képezi a Cargill RegenConnect Mezőgazdasági Termelői Megállapodásnak (a továbbiakban: <strong>„Megállapodás”</strong>), {{ current_date.strftime('%Y') }}. {{ current_date.strftime('%d') }} hó {{ current_date.strftime('%m') }} napján jött létre a {{ legal_name }}  (székhelye: {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }}; adószáma: {{tax_number}}; Cargill SAP ID száma: {{cargill_account_id}}; a továbbiakban: <strong>„Résztvevő”</strong>) és a Cargill Magyarország Zrt. (székhelye: 1087 Budapest, Hungária körút 30.; cégjegyzékszáma a Fővárosi Törvényszék Cégbíróságán: 01-10-042527; adószáma: ********-2-44; a továbbiakban: <strong>„Cargill”</strong>) között.
        </p>

        <p>
            Az itt másként meg nem határozott nagybetűs kifejezések a Megállapodásban meghatározott jelentéssel bírnak.
        </p>

        <p>
            A Portál a kulcsfontosságú eszköze annak, hogy a Résztvevő mezőgazdasági gyakorlatában bekövetkezett változásokat és azok környezetre gyakorolt hatásait mérjék, számszerűsítsék és ellenőrizzék, és a Portál harmadik fél általi üzemeltetője, a Regrow szorosan együttműködik a Résztvevővel és a Cargill-lal annak ellenőrzése érdekében, hogy a Résztvevőnek a programmal érintett Földterületein a regeneratív mezőgazdasági gyakorlatokat a Megállapodás feltételeivel összhangban hajtották-e végre, valamint annak érdekében, hogy a szén-dioxid-megkötés, a talaj egészségének javítása és más pozitív környezeti eredmények meghatározására sor került-e.        </p>

        <p><strong>EZÉRT</strong>, a Résztvevő és a Cargill az alábbi feltételekben állapodnak meg:</p>

        <ol type="1">

            <li><!-- 1. Term -->
                <p>Jelen A. melléklet tartalmazza a Résztvevő 2026-os Termésévre vonatkozó kötelezettségvállalásának részleteit.</p>
            </li>

            <li style="page-break-before:always"><!-- 2. Appendix A heading-->
                <br>
                <p><strong>A. melléklet - Gazdálkodási Gyakorlatok és Földterületi Információk</strong></p>
                <p>Kiválasztott regeneratív gyakorlatok</p>
                <p>2024 ősz -2025 közötti időszakban elfogadott Gazdálkodási Gyakorlatok</>
            </li>
            <!-- Regenerative practices selection -->
            <!-- Todo: confirm if blockquote needed. -->
            <!-- <blockquote> -->

            <!-- </blockquote> -->

            <!-- Table of regenerative practices -->
            <table style="width: 100%; table-layout: fixed;">
                <thead style="vertical-align: middle;">
                    <tr class="header">
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px;">Cégnév
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 15px">Földterület
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">A programba való felvétel éve
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Terület (ha)
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Regeneratív gyakorlatok
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Becsült kifizetés (€)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in fields %}
                    <tr>
                        <td style="padding-top:10px;">{{ row.note["farm_name"]}}</td>
                        <td style="padding-top:10px;">{{ row.note["field_name"]}}</td>
                        <td style="padding-top:10px;">{{ "" if row.note["baseline_year"] == None else row.note["baseline_year"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                        <td style="padding-top:10px;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.value == None else "%.2f"|format(row.value) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- total estimation-->
            <!-- Todo: confirm if blockquote needed -->
            <!-- <blockquote> -->
                <div style="text-align:left;">
                    <div style="margin-bottom:7px;">
                        <strong>Becsült teljes szekvenciakivonás</strong>
                    </div>

                    <div style="font-size:12px;margin:7px 0;">
                        a parcellák esetében, {{ "%.2f"|format(total_area) }} {{ user_units }}
                    </div>

                    <table cellpadding="0" cellspacing="0" border="0" style="padding:10px;text-align:left;line-height:1;width:100%;box-sizing:border-box;border:1px solid #e0e2e3;">
                        <tbody>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Nyilatkozott érdeklődés az agroerdészet iránt
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all; ">
                                    <b>{{ "Igen" if has_agroforestry else "Nem" }}</b>
                                </td>
                            </tr>
                                <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Beporzó eljegyzés
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all; ">
                                    <b>{{ "Igen (250" + currency_char + ")" if has_pollinator_engagement else "Nem" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="padding-top:10px; width: 70%;">
                                    Teljes kifizetés becsült értéke (35€ / tCO2 / év)
                                </td>
                                <td style="padding-top:10px;text-align:right; width: 30%; word-wrap: break-word; word-break: break-all; ">
                                    <b>{{ "%.2f"|format(payment) }}{{ currency_char }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <!-- </blockquote> -->
        </ol>

        <ol type="1" start="3">
            <li><!-- 3. Historical data -->
                <p><u>Portál feltöltés</u>: A Résztvevő feltölti (i) a Földterületeit és (ii) az egyes Földterületekre vonatkozó korábbi (historikus) gazdálkodási adatokat a Portálra. A korábbi gazdálkodási adatok a következő információkat tartalmazzák a 2026-os Termésévet megelőző öt (5) Termésévre vonatkozóan: a főnövényeket (vetés és betakarítás dátuma), amennyiben van, a takarónövényeket (vetés és betakarítás dátuma), valamint a talajművelés módját. A Portál ezután minden egyes Földterületre vonatkozóan elkészíti a szén-dioxid-megkötési és kifizetési becsléseket.  </p>
            </li>

            <li><!-- 4. Measurements-->
                <p><u>Mérés:</u> A vegetációs időszak végén a Résztvevő igazolja a Portálon a fenti 2. pontban meghatározott Gazdálkodási Gyakorlatok teljes körű és megfelelő végrehajtását és teljesítését. </p>
            </li>

            <li><!-- 5. Verification-->
                <p><u>Ellenőrzés</u>: A Cargill/Regrow távérzékeléssel (pl. műholdas technológiával) ellenőrzi, hogy a Résztvevő végrehajtotta-e az elfogadott Gazdálkodási Gyakorlatokat a regisztrált Földterületeken. A Résztvevő által vállalt Gazdálkodási Gyakorlatok teljesítését érintő vita esetén a Résztvevő köteles a Cargill részére bemutatni a vállalt Gazdálkodási Gyakorlatok teljesítését igazoló dokumentumokat. A Résztvevő köteles a Cargill kérésére a Megállapodásban foglalt Gazdálkodási Gyakorlatok végrehajtását dokumentumokkal vagy nyilvántartással igazolni, amennyiben az adott Gazdálkodási Gyakorlatot nem lehet távérzékeléssel ellenőrizni.</p>
            </li>

            <li><!-- 6. Final results-->
                <p><u>Végeredmények</u>: A fenti 2-5. pont alapján a Regrow és a Cargill kiszámítja a talaj szerves szén-dioxid-megkötésének, a metán- és N2O-kibocsátásnak a projekt kiindulási állapota és az új beavatkozások közötti időszakra eső végső teljes nettó változását. </p>
            </li>

            <li><!-- 7. Measurements requirements-->
                <p><u>A Mérésre vonatkozó követelmények:</u>A mérés során a Résztvevőnek fel kell töltenie a Portálra a Résztvevő szezonjára vonatkozó információkat, valamint a Portálon szerepeltetnie kell további gazdaságirányítási információkat a Résztvevő téli és tavaszi gyakorlatáról. Ez többek között a következőket foglalja magában:</p>
                <ul style="list-style-type: disc;">
                    <li>
                        <p>A főnövény megerősítése </p>
                    </li>
                    <li>
                        <p>A végrehajtott termesztéstechnológiai elemek gyakorlati változtatásának megerősítése (talajművelés és takarónövények).</p>
                    </li>
                    <li>
                        <p>Terméshozam</p>
                    </li>
                    <li>
                        <p>Termesztés befejezése (növényimaradványok kezelése)</p>
                    </li>
                    <li>
                        <p>További talajművelési információk (talajművelési mélység)</p>
                    </li>
                    <li>
                        <p>Öntözési módszer</p>
                    </li>
                </ul>
            </li>
            <li><!-- 8 -->
                <p><u>Járulékos Előnyök</u>: A vállalt Gazdálkodási Gyakorlatok Mérése és Ellenőrzése részeként a Cargill kiszámítja a növényi kultúra sokféleségével, a talajtakarás időtartamával és a talajművelés nélküli gazdálkodás vízvisszatartó képességre gyakorolt hatásával kapcsolatos esetleges járulékos környezeti előnyöket a Cargill által egy adott évben meghatározott vízhiányos területeken <strong>(„Járulékos Előnyök”)</strong>. Ezen számítások alapján a Cargill dönthet úgy, hogy a Résztvevőnek hektáronként további kompenzációt fizet a Járulékos Előnyökért <strong>(„Kiegészítő Kompenzáció”)</strong>.</p>
                <p>A 2026. Termésévre vonatkozóan a Járulékos Előnyök a következő intézkedéseket tartalmazzák:</p>
                <ul style="list-style-type: disc;">
                    <li>
                       <strong>növénydiverzitási prémium:</strong> a nyilvántartásba vett Földterületeken azonosított növényfajok számától függően, legfeljebb 2 EUR/ha Kiegészítő Kompenzáció;
                    </li>
                    <li>
                       <strong>zöldtakaró prémium:</strong> a nyilvántartásba vett Földterületeken a 2026. évi termés betakarítását megelőző 365 napos időszakban mért zöldtakaró napok számától függően, legfeljebb 1 EUR/ha Kiegészítő Kompenzáció;
                    </li>
                    <li>
                       <strong>vízvisszatartási prémium:</strong> a mért talajművelés nélküli Földterületek fekvésétől függően, legfeljebb 1 EUR/ha Kiegészítő Kompenzáció.
                    </li>

                </ul>

            </li>
            <li><!-- 9-->
                <p><u>Beporzási Prémium</u>: A 2026-os Termésévre a Résztvevő elkötelezheti magát egy további Gazdálkodási Gyakorlat mellett, amelynek célja a beporzók (ideértve a vadon élő beporzókat is) populációjának növelése a soros növénytermesztésben. A gyakorlat magában foglalhatja a helyi bejegyzett méhész szakemberekkel való együttműködés kialakítását olyan tevékenységek keretében, mint a Földterületekhez való hozzáférés megkönnyítése, zöld menedékhelyek, természetes árnyékoló zónák kialakítása stb. A Mérési eljárás részeként a Résztvevőnek meg kell adnia az érintett méhész nyilvántartási azonosítószámát, amely alapján a Cargill a Résztvevőnek 250 EUR átalányösszegű éves prémiumot fizet a szerződött gazdaság után <strong>(„Beporzási Prémium”)</strong>.</p>
            </li>
            <li><!-- 10-->
                <p><u>2026-os Termésév</u>: A Kiegészítő Kompenzáció és a Beporzási Prémium a Kompenzáció (a Megállapodásban meghatározottak szerint) felett, a Teljes Kompenzációs Összeg részeként fizetendő. A Kiegészítő Kompenzáció és a Beporzási Prémium kizárólag a 2026-os Termésévre vonatkozik, és a Cargill fenntartja a jogot, hogy a jövőbeni Termésévekben újra megvizsgálja, hogy Kiegészítő Kompenzációt és/vagy Beporzási Prémiumot fizet-e.</p>
            </li>
            <li><!-- 11-->
                <p><u>Erdőgazdálkodás</u>: A regisztráció részeként a Résztvevő nyilatkozhat arról, hogy hajlandó további Gazdálkodási Gyakorlatokat bevezetni erdőgazdálkodás keretében. Az ilyen szándéknyilatkozat nem jár további prémiummal vagy Kompenzációval a Résztvevő számára. A Cargill törekszik arra, hogy más érdekelt harmadik felekkel olyan együttműködést alakítson ki, aminek eredményeként a gyakorlatok közös megvalósítására kerülhet sor, melynek feltételeit külön szerződésben kell meghatározni.</p>
            </li>
            <li><!-- 12-->
                <p><u>Adaptív talajművelési szabály</u>: A megállapodás 3. pontjától eltérően, a 2025-ös Termésév és az azt követő Termesévekre vonatkozó Mérések tekintetében a hagyományos talajművelésre a következő szabályok alkalmazandók:</p>
                <ul style="list-style-type: disc;">
                    <li>hagyományos talajművelési gyakorlat nem jelölhető meg tervezett Gazdálkodási Gyakorlatként egy adott Termésévre (a hagyományos talajművelési gyakorlatra előre tervezett Földterület nem regisztrálható a programba), azonban</li>
                    <li>a hagyományos talajművelési gyakorlat (értsd: adaptív talajművelés), amennyiben azt a Cargill jóváhagyja, háromévenként egyszer egy adott Földterület vonatkozásában kivételesen elfogadható a Mérési szakaszban (azaz egy adott Termésév utáni betakarítást követően), ha az alábbi kivételek valamelyike fennáll;</li>
                    <li>a kivételek köre a következő: kedvezőtlen időjárási körülmények, például árvíz vagy túlzott csapadék, növényvédelmi intézkedések szükségessége, például túlzott gyomosodás, a korábbi termés betakarításakor fennálló kedvezőtlen körülmények (azaz jelentős talajtömörödés);</li>
                    <li>ha a hagyományos talajművelési gyakorlatot a gazdálkodó részvételének 3. Termésévében jelenti be, a Résztvevő egyúttal vállalja, hogy a Cargill számára a Megállapodásban meghatározott ellenőrzéssel kapcsolatos jogosultságait egy további Termésévre meghosszabbítja;</li>
                    <li>a Résztvevő köteles haladéktalanul írásban vagy e-mailben értesíteni a Cargillt, ha rendkívüli hagyományos talajművelés elvégzésére van szükség.</li>
                </ul>
                <p>A rendkívüli hagyományos talajművelés alkalmazása az adott Termésévre vonatkozó Teljes Kompenzáció csökkentését vagy negatív értékét eredményezheti.</p>
            </li>
            <li><!-- 13-->
                <p>A fentiek kivételével a Megállapodásban foglalt valamennyi feltétel teljes mértékben hatályban marad. Ha a jelen A. melléklet és a Megállapodás között ellentmondás van, a jelen A. melléklet feltételei az irányadóak.</p>
            </li>
        </ol>
    </body>
</html>
