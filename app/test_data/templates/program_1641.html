<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
    <head>
        <meta charset="utf-8"/>
        <meta name="generator" content="pandoc"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <title>Regrow - Contract program 1641 Poland</title>
        <style>
            html {
                line-height: 1.5;
                font-family: Georgia, serif;
                font-size: 20px;
                color: #1a1a1a;
                background-color: #fdfdfd;
            }

            body {
                margin: 0 auto;
                max-width: 48em;
                padding: 50px;
                hyphens: auto;
                overflow-wrap: break-word;
                text-rendering: optimizeLegibility;
                font-kerning: normal;
            }

            @media (max-width: 600px) {
                body {
                    font-size: 0.9em;
                    padding: 1em;
                }

                h1 {
                    font-size: 1.8em;
                }
            }

            @media print {
                body {
                    background-color: transparent;
                    color: black;
                    font-size: 12pt;
                }

                h2,
                h3,
                p {
                    orphans: 3;
                    widows: 3;
                }

                h2,
                h3,
                h4 {
                    page-break-after: avoid;
                }
            }

            p {
                margin: 1em 0;
                text-align: justify;
            }

            a {
                color: #1a1a1a;
            }

            a:visited {
                color: #1a1a1a;
            }

            img {
                max-width: 100%;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 1.4em;
            }

            h5,
            h6 {
                font-size: 1em;
                font-style: italic;
            }

            h6 {
                font-weight: normal;
            }

            ol,
            ul {
                padding-left: 1.7em;
                margin-top: 1em;
            }

            li > ol,
            li > ul {
                margin-top: 0;
            }

            blockquote {
                margin: 1em 0 1em 1.7em;
                padding-left: 1em;
                border-left: 2px solid #e6e6e6;
                color: #606060;
            }

            code {
                font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
                font-size: 85%;
                margin: 0;
            }

            pre {
                margin: 1em 0;
                overflow: auto;
            }

            pre code {
                padding: 0;
                overflow: visible;
                overflow-wrap: normal;
            }

            .sourceCode {
                background-color: transparent;
                overflow: visible;
            }

            hr {
                background-color: #1a1a1a;
                border: none;
                height: 1px;
                margin: 1em 0;
            }

            table {
                margin: 1em 0;
                border-collapse: collapse;
                width: 100%;
                overflow-x: auto;
                font-variant-numeric: lining-nums tabular-nums;
            }

            table caption {
                margin-bottom: 0.75em;
            }

            tbody {
                margin-top: 0.5em;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }

            th {
                border-top: 1px solid #1a1a1a;
                padding: 0.25em 0.5em;
            }

            td {
                padding: 0.125em 0.5em 0.25em;
            }

            header {
                margin-bottom: 4em;
                text-align: center;
            }

            #TOC li {
                list-style: none;
            }

            #TOC ul {
                padding-left: 1.3em;
            }

            #TOC > ul {
                padding-left: 0;
            }

            #TOC a:not(:hover) {
                text-decoration: none;
            }

            code {
                white-space: pre-wrap;
            }

            span.smallcaps {
                font-variant: small-caps;
            }

            span.underline {
                text-decoration: underline;
            }

            div.column {
                display: inline-block;
                vertical-align: top;
                width: 50%;
            }

            div.hanging-indent {
                margin-left: 1.5em;
                text-indent: -1.5em;
            }

            ul.task-list {
                list-style: none;
            }

            .display.math {
                display: block;
                text-align: center;
                margin: 0.5rem auto;

            }
            li::marker {
                width: 2.4cm;
                text-align: left !important;
            }
        </style>
    </head>
    <body>
        {% set ns1 = namespace(company_name_key="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith('Nazwa firmy') %}
                {% set ns1.company_name_key = key %}
            {% endif %}
        {% endfor %}
        {% set legal_name = custom_inputs[ns1.company_name_key] | default("") %}
        {% if legal_name == "" %}
            {% set legal_name = user_fname + " " + user_lname %}
        {% endif %}

        {% set PESL = custom_inputs["PESEL"] | default("") %}
        {% if PESL == "" %}
            {% set PESL = "nie podano" %}
        {% endif %}

        {% set NIP = custom_inputs["NIP"] | default("") %}
        {% if NIP == "" %}
            {% set NIP = "nie podano" %}
        {% endif %}

        {% set REGON = custom_inputs["REGON"] | default("") %}
        {% if REGON == "" %}
            {% set REGON = "nie podano" %}
        {% endif %}

        {% set ns = namespace(has_pollinator_engagement=false, has_agroforestry=false) %}
        {% for field in fields %}
            {% if field.note.ASSIGN_PRACTICES and ("pollinator" in field.note.ASSIGN_PRACTICES|lower or "azyle dla zapylaczy" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_pollinator_engagement = true %}
            {% endif %}
            {% if field.note.ASSIGN_PRACTICES and ("agroforestry" in field.note.ASSIGN_PRACTICES|lower or "agroleśnictwo" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_agroforestry = true %}
            {% endif %}
        {% endfor %}
        {% set has_pollinator_engagement = ns.has_pollinator_engagement %}
        {% set has_agroforestry = ns.has_agroforestry %}

        {% set CARGILL_ACCOUNT_ID_CUSTOM_INPUT = custom_inputs["numer klienta Cargill"] | default("") %}
        {% if CARGILL_ACCOUNT_ID_CUSTOM_INPUT != "" %}
            {% set cargill_account_id =  CARGILL_ACCOUNT_ID_CUSTOM_INPUT %}
        {% endif %}

        <table cellspacing="0" cellpadding="0" style="border: none; border-collapse: separate;">
            <tr>
                <td style="text-align: right;">
                    {{ user_fname }} {{ user_lname }}<br>
                    {{ user_email }}<br>
                    {{ cargill_account_id }}<br>
                    {% if user_phone_number %}
                    {{ user_phone_number }}<br />
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;" >
                    <p><strong>Załącznik A do Umowy Uczestnictwa w programie Cargill RegenConnect</strong><sup>®</sup></p>
                </td>
            </tr>
        </table>

        <p>
            Niniejszy Załącznik A wchodzący w życie z dniem {{ current_date.strftime('%d/%m/%Y') }} r. stanowi integralną część Umowy Uczestnictwa w programie Cargill RegenConnect („<strong>Umowa</strong>”) zawartej pomiędzy {{ legal_name }} zamieszkałym/zamieszkałą pod adresem {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }}, numer PESEL {{ PESL }} NIP: {{ NIP }}, numer SAP Cargill {{ cargill_account_id }} („<strong>Uczestnik</strong>”) oraz Cargill Poland Sp. z o. o. z siedzibą w Warszawie, przy ul. Wołoskiej 22, 02-675 Warszawa, Polska, wpisaną do rejestru przedsiębiorców prowadzonego przez Sąd Rejonowy dla m.st. Warszawy - XIII Wydział Gospodarczy Krajowego Rejestru Sądowego pod numerem KRS **********, NIP **********, REGON **************, z kapitałem zakładowym w wysokości 554.661.500 PLN, mającą status dużego przedsiębiorcy w rozumieniu Ustawy z dnia 8 marca 2013 r. o przeciwdziałaniu nadmiernym opóźnieniom w transakcjach handlowych (t.j. Dz. U. z 2019 r., poz. 118, ze zm.) („<strong>Cargill</strong>”).
        </p>

        <p>
            <strong>ZWAŻYWSZY, ŻE</strong> wszystkie terminy pisane z wielkiej litery nie zdefiniowane inaczej w niniejszym Załączniku mają znaczenie nadane im w Umowie.
        </p>

        <p>
            <strong>ZWAŻYWSZY, ŻE</strong> najważniejszym elementem pomiaru, kwantyfikacji i weryfikacji zmian w praktykach rolniczych Uczestnika oraz ich wpływu na środowisko będzie Portal. Portal jest administrowany przez spółkę Regrow, która będzie ściśle współpracować z Uczestnikiem i Cargill w celu weryfikowania wdrożenia działań regeneratywnych na zgłoszonych polach uprawnych Uczestnika zgodnie z warunkami niniejszej Umowy, oraz w celu ustalenia poprawy w zakresie sekwestracji dwutlenku węgla, poprawy stanu gleby i innych pozytywnych wyników środowiskowych.
        </p>

        <p><strong>W ZWIĄZKU Z TYM,</strong> Uczestnik i Cargill umawiają się jak następuje:</p>

        <ol type="1">

            <li><!-- 1. Term -->
                <p>Niniejszy Załącznik A przedstawia szczegółowe informacje dotyczące działalności Uczestnika w Roku Zbiorów 2026.</p>
            </li>

            <li style="page-break-before:always"><!-- 2. Appendix A heading-->
                <p><strong>Załącznik A – Praktyki regeneratywne i charakterystyki Pól</strong></p>
            </li>
            <!-- Regenerative practices selection -->
            <!-- Todo: confirm if blockquote needed. -->
            <!-- <blockquote> -->

            <!-- </blockquote> -->

            <!-- Table of regenerative practices -->
            <p>Praktyki regeneratywne zadeklarowane w Roku Zbiorów 2026</p>
            <table style="width: 100%; table-layout: fixed;">
                <thead style="vertical-align: middle;">
                    <tr class="header">
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px;">Działanie
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 15px">Pole
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Powierzchnia (ha)
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Praktyki regeneracyjne
                        </th>
                        <th scope="col" style="text-align:left; word-wrap: break-word; font-size: 13px">Płatność EUR
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in fields %}
                    <tr>
                        <td style="padding-top:10px; word-wrap: break-word; word-break: break-all;">{{ row.note["farm_name"]}}</td>
                        <td style="padding-top:10px; word-wrap: break-word; word-break: break-all;">{{ row.note["field_name"]}}</td>
                        <td style="padding-top:10px; word-wrap: break-word; word-break: break-all;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                        <td style="padding-top:10px; word-wrap: break-word; word-break: break-all;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
                        <td style="padding-top:10px; padding-bottom: 10px; word-wrap: break-word; word-break: break-all;">{{ "" if row.value == None else "%.2f"|format(row.value) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- total estimation-->
            <!-- Todo: confirm if blockquote needed -->
            <!-- <blockquote> -->
                <div style="text-align:left;">
                    <div style="margin-bottom:7px;">
                        Szacowana całkowita sekwestracja
                    </div>

                    <div style="font-size:12px;margin:7px 0;">
                        dla {{ "pól" if fields|length > 1 else "pole" }}, {{ "%.2f"|format(total_area) }} {{ user_units }}
                    </div>

                    <table cellpadding="0" cellspacing="0" border="0" style="padding:10px;text-align:left;line-height:1;width:100%;box-sizing:border-box;border:1px solid #e0e2e3;">
                        <tbody>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Zadeklarowane zainteresowanie agroleśnictwem
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Tak" if has_agroforestry else "Nie" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Zaangażowanie zapylaczy
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Tak (250" + currency_char + ")" if has_pollinator_engagement else "NIE" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="padding-top:10px; width: 70%;">
                                    Szacowana suma wypłat (35 EUR / t CO2 / rok)
                                </td>
                                <td style="padding-top:10px;text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "%.2f"|format(payment) }}{{ currency_char }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <!-- </blockquote> -->
        </ol>

        <ol type="1" start="3">
            <li><!-- 3. Historical data -->
                <p><u>Wprowadzanie danych do Portalu</u>: Uczestnik dokona na Portalu zgłoszenia (i) Pól i (ii) dla każdego Pola wprowadzi dane historyczne. Dane historyczne obejmują następujące informacje dotyczące pięciu (5) Lat Zbiorów poprzedzających Rok Zbiorów 2026: uprawy główne (termin sadzenia/wysiewu i zbioru), w przypadku międzyplonów termin sadzenia/wysiewu i termin rozwiązania) oraz rodzaj uprawy roli. Następnie Portal wygeneruje dla każdego Pola szacunki dotyczące sekwestracji dwutlenku węgla i płatności. </p>
            </li>

            <li><!-- 4. Measurements-->
                <p><u>Pomiary</u>: Na koniec sezonu wegetacyjnego Uczestnik potwierdzi na Portalu całkowite i prawidłowe wdrożenie Działań Regeneratywnych  wyszczególnionych w powyższym pkt. 2. </p>
            </li>

            <li><!-- 5. Verification-->
                <p><u>Weryfikacja</u>: Sprawdzenie wykonania Działań Regeneratywnych na zgłoszonych polach będzie realizowane metodą teledetekcji (z wykorzystaniem technologii satelitarnej). Rozbieżności na poziomie pól będą rozstrzygane poprzez sprawdzenie ewidencji prowadzonej przez Uczestnika. Uczestnik będzie odpowiedzialny za przekazanie na żądanie Cargill dokumentów lub ewidencji potwierdzających zakontraktowane Działania Regeneratywne jeśli wykonanie Działań Regeneratywnych nie będzie mogło zostać potwierdzone za pomocą teledetekcji.</p>
            </li>

            <li><!-- 6. Final results-->
                <p><u>Wyniki Końcowe</u>: W oparciu o powyższe pkt. 1-5, Regrow i Cargill obliczą końcową całkowitą zmianę netto wskaźnika sekwestracji węgla organicznego w glebie, emisji metanu i N2O względem poziomu odniesienia projektu i nowych interwencji. </p>
            </li>

            <li><!-- 7. Measurements requirements-->
                <p><u>Wymagania dotyczące Pomiaru:</u></p>
                <p>W fazie pomiarów na Portalu gromadzone będą informacje na temat sezonu Uczestnika, a także dodatkowe informacje na temat prowadzenia gospodarstwa w okresie zimowym i wiosennym. Obejmuje to między innymi następujące elementy:</p>
                <ul style="list-style-type: disc;">
                    <li>
                        <p>Potwierdzenie uprawy głównej (komercyjnej)</p>
                    </li>
                    <li>
                        <p>Potwierdzenie wdrożonych zmian metod uprawy (uprawa roli i międzyplon)</p>
                    </li>
                    <li>
                        <p>Plonowanie</p>
                    </li>
                    <li>
                        <p>Sposób zbioru, niszczenia międzyplonu i uprawy głównej</p>
                    </li>
                    <li>
                        <p>Dodatkowe informacje o uprawie (głębokość uprawy)</p>
                    </li>
                    <li>
                        <p>Metoda nawadniania</p>
                    </li>
                </ul>
            </li>
            <li><!-- 8 -->
                <p><u>Dodatkowe korzyści</u>: W ramach Pomiarów i Weryfikacji zadeklarowanych Działań Regeneratywnych, Cargill wyliczy potencjalne dodatkowe korzyści środowiskowe w zakresie dywersyfikacji upraw, okresu okrywy glebowej i wpływu uprawy zerowej (siewu bezpośredniego) na retencję wodną na zdefiniowanych w danym roku przez Cargill obszarach szczególnie narażonych na niedobór wody <strong>(„Dodatkowe Korzyści”)</strong>. Na podstawie tych wyliczeń, Cargill może przyznać Uczestnikowi dodatkowe wynagrodzenie na hektar z tytułu Dodatkowych Korzyści <strong>(„Dodatkowe Wynagrodzenie”)</strong>.</p>
                <p>W odniesieniu do Roku Zbiorów 2026, Dodatkowe Korzyści obejmują następujące schematy:</p>
                <ul style="list-style-type: disc;">
                    <li>
                       <strong>premia dywersyfikacyjna:</strong> w zależności od liczby gatunków zmierzonych na Polach zgłoszonych do Programu, do wysokości 2 EUR/ha Dodatkowego Wynagrodzenia;
                    </li>
                    <li>
                       <strong>premia z tytułu okrywy zielonej:</strong> w zależności od liczby dni, w trakcie których zgłoszone do Programu Pola objęte są zieloną okrywą w okresie 365 dni poprzedzających dzień zbioru uprawy 2026, do wysokości 1 EUR/ha Dodatkowego Wynagrodzenia;
                    </li>
                    <li>
                       <strong>premia retencyjna:</strong> w zależności od lokalizacji Pól objętych praktyką uprawy zerowej (siewu bezpośredniego), do wysokości 1 EUR/ha Dodatkowego Wynagrodzenia.
                    </li>

                </ul>

            </li>
            <li><!-- 9-->
                <p><u>Wsparcie Zapylaczy</u>: Dla Roku Zbiorów 2026, Uczestnik może zadeklarować dodatkowe Działanie Regeneratywne mające na celu wsparcie populacji zapylaczy (w tym tak zwanych dzikich zapylaczy) w uprawach rzędowych na gruntach ornych. Działanie może obejmować rozpoczęcie lub kontynuowanie współpracy z profesjonalnymi, zarejestrowanymi w danym regionie pszczelarzami w zakresie poprawy dostępu do Pól, zakładania azyli, naturalnych stanowisk zacienionych itp. W ramach Pomiarów, Uczestnik zobowiązany będzie do przedstawienia numeru rejestracyjnego pszczelarza, z którym współpracuje. Na tej podstawie Cargill przyzna Uczestnikowi premię 250 EUR, jako jednorazowe, roczne wynagrodzenie na gospodarstwo <strong>(„Wsparcie Zapylaczy”)</strong>.</p>
            </li>
            <li><!-- 10-->
                <p>Rok Zbioru 2026: Dodatkowe Wynagrodzenie i Wsparcie Zapylaczy stanowią uzupełnienie Wynagrodzenia (zdefiniowanego w Umowie Uczestnictwa) i wypłacane są jako część Całkowitej Kwoty Wynagrodzenia. Zarówno Dodatkowe Wynagrodzenie, jak i Wsparcie Zapylaczy odnoszą się wyłącznie do Roku Zbiorów 2026. Cargill zastrzega sobie możliwość dokonania przeglądu, czy Dodatkowe Wynagrodzenie i/lub Wsparcie Zapylaczy będzie wypłacane w przyszłych Latach Zbiorów.</p>
            </li>
            <li><!-- 11-->
                <p>Agro-leśnictwo: W ramach Zgłoszenia, Uczestnik może zadeklarować zamiar wdrożenia dodatkowych Działań Regeneratywnych w postaci agro-leśnictwa. Wyrażenie zamiaru wdrożenia tych działań nie generuje automatycznie dodatkowego wsparcia lub Wynagrodzenia dla Uczestnika. Cargill podejmie działania na rzecz zaangażowania innych podmiotów zainteresowanych wspólnym wdrożeniem działań agro-leśnictwa, którego zasady objęte zostaną odrębną umową.</p>
            </li>
            <li><!-- 12-->
                <p>Uprawa Adaptacyjna: W ramach wyjątku od Punktu 3 Umowy, w odniesieniu do Pomiarów od Roku Zbiorów 2025 i lat następnych, następujące postanowienia stosują się do uprawy konwencjonalnej: </p>
                <ul style="list-style-type: disc;">
                    <li>uprawa konwencjonalna nie może być z góry zadeklarowana jako praktyka dla danego Roku Zbiorów (Pole z zadeklarowaną praktyką uprawy konwencjonalnej nie zostanie objęte programem), jednakże</li>
                    <li>uprawa konwencjonalna może być na zasadzie wyjątku zgłoszona i zaakceptowana w fazie Pomiarów (tj. po zbiorach w danym Roku Zbiorów) przez Cargill (uprawa adaptacyjna), przy czym takiego zgłoszenia Uczestnik może dokonać tylko raz w okresie 3 lat obowiązywania Umowy Uczestnictwa w odniesieniu do danego Pola, jeśli poniższe warunki zostaną spełnione;</li>
                    <li>katalog wyjątków obejmuje: niekorzystne warunki pogodowe, takie jak powódź lub ulewne deszcze, konieczność wykonania zabiegu ochrony roślin wskutek nadmiernego zachwaszczenia, trudne warunki panujące w trakcie zbiorów poprzedniego plonu (m.in. nadmierne zagęszczenie);</li>
                    <li>w przypadku zgłoszenia uprawy konwencjonalnej w 3-cim roku obowiązywania Umowy Uczestnictwa, Uczestnik wyraża zgodę na przedłużenie uprawnień Cargill w zakresie Monitoringu (określonych w Umowie Uczestnictwa) o kolejny Rok Zbiorów;</li>
                    <li>Uczestnik zobowiązany jest do niezwłocznego powiadomienia Cargill na piśmie lub elektronicznie za każdym razem, gdy zachodzi sytuacja konieczności zastosowania uprawy konwencjonalnej.</li>
                </ul>
                <p>Zastosowanie uprawy konwencjonalnej może prowadzić do obniżonej lub negatywnej wartości Całkowitego Wynagrodzenia dla danego Roku Zbiorów.</p>
            </li>
            <li><!-- 13-->
                <p>Z zastrzeżeniem powyższych postanowień, wszystkie postanowienia Umowy pozostają w mocy. W przypadku rozbieżności między niniejszym Załącznikiem A i Umową, obowiązują postanowienia Załącznika A</p>
            </li>
        </ol>

        <table width="100%">
            <tbody style="border: none;">
            <tr>
                <td >
                    <strong>Uczestnik</strong>
                    <p>Podpis: {{ user_fname }} {{ user_lname }}</p>
                    <p>Stanowisko: <span style="display: inline-block; height:60px; color: white;">/sn1/</span></p>
                </td>
            </tr>
            <tr>
                <td >
                    <strong>Cargill</strong>
                    <p>Podpis: {{ co_signer_name if co_signer_name else "" }}</p>
                    <p>Stanowisko: <span style="display: inline-block; height:60px; color: white; position: relative; top: -40px;">/co_signature/</span></p>
                </td>
            </tr>
            </tbody>
        </table>

    </body>
</html>
