[tool.bandit]
exclude = ['app/scripts/*.py', 'app/scripts/match_ecoregions_with_fields/*.py', "alembic", "terraform", ".pytest_cache", '**test*.py']
skips = ["B101", "B106", "B311", "B104", "B105"]

[tool.coverage.run]
concurrency = ["greenlet", "thread"]

[tool.isort]
multi_line_output = 3
include_trailing_comma = true
profile = "black"
combine_as_imports = true
force_alphabetical_sort_within_sections = true
lines_between_types = 0
src_paths = ["./", "./app/"]
known_third_party = []
known_first_party = []

[tool.mypy]
exclude = ['alembic/', '.*conftest.py', '.*/test.*', '.*/pytest_models_factory.py']
follow_imports = "silent"  # TODO: "normal"
ignore_missing_imports = true  # TODO: false
warn_return_any = false  # TODO: true
namespace_packages = true
explicit_package_bases = true
disallow_untyped_defs = true

[tool.poetry]
name = "MRV"
version = "0.1.0"
description = "Measurement, Reporting & Verification"
license = "Proprietary"
authors = [
    "Regrow Ag <<EMAIL>>",
]
readme = "README.md"
package-mode = false

[[tool.poetry.source]]
name = "regrow-python"
url = "https://us-west1-python.pkg.dev/flurosat-154904/regrow-python/simple"
priority = "explicit"

[tool.poetry.dependencies]  # dependencies for app work (group name: main)
python = "~3.11.0"
aiomysql = "0.2.0"
aiosmtplib = "4.0.0"
alembic = "1.15.2"
anytree = "2.12.1"
async-lru = "2.0.5"
async-property = "0.2.2"
asyncache = "0.3.1"
babel = "2.17.0"
backoff = "2.2.1"
cachetools = "5.5.1"
celery = { extras = ["redis"], version = "5.5.3" }
certifi = "2024.2.2"
cryptography = "44.0.0"
curlify2 = "2.0.0"
defaults-service-client = { version = "5.16.0", source = "regrow-python" }
docusign-esign = "4.0.0"
elastic-apm = "6.23.0"
fastapi = "0.115.12"
fastapi-pagination = "0.12.34"
flower = "2.0.1"
gcloud-aio-storage = "9.3.0"
GeoAlchemy2 = "0.17.0"
geojson-pydantic = "0.7.0"
google-auth = "2.38.0"
google-cloud-storage = "3.0.0"
greenlet = "3.1.1"
gunicorn = "23.0.0"
httpx = "0.27.2"
jinja2 = "3.1.6"
matplotlib = "3.10.1"
mysqlclient = "2.2.7"
nest-asyncio = "1.6.0"
oauth2client = "4.1.3"
pandas = "2.2.3"
pdfkit = "1.0.0"
pint = "0.24.4"
pydantic-geojson = "0.2.0"
PyExcelerate = "0.12.0"
python-dateutil = "2.9.0.post0"
python-dotenv = "1.0.1"
python-multipart = "0.0.20"
redis = "5.2.1"
regrow-api-translators = { version = ">=24.1.0", source = "regrow-python" } # to support scenarios-service-client dependency
regrow-bigtable = { version = "0.6.0",  source = "regrow-python" } # to support scenarios-service-client dependency
regrow-protobuf-python = { version = "1.56.1",  source = "regrow-python" }
regrow-kafka = { version = "^2.0.0", source = "regrow-python" }
regrow-ses = { version = "^0.1.62", source = "regrow-python" }
requests = "2.32.3"
requests-cache = "1.2.1"
scenarios-service-client = { version = "11.15.0", source = "regrow-python" }
scenarios-service-schema = { version = "11.15.0", source = "regrow-python" }
sentry-sdk = "2.25.0"
ses-client = {version = "v1.2.9", source = "regrow-python"}
setuptools = ">71.0.2"
shapely = "2.0.6"
sqlalchemy = "2.0.37"
SQLAlchemy-Utils = "0.41.2"
sqlfluff = "3.3.1"
tabulate = "0.9.0"
uvicorn = "0.34.0"
XlsxWriter = "3.2.2"
timezonefinder = "^6.5.9"  # Remove this after the last SES Program Migration
python-json-logger = "^3.3.0"
google-cloud-bigquery = "^3.31.0"
db-dtypes = "^1.4.3"
google-cloud-bigquery-storage = "^2.31.0"
utm = "^0.7.0"

[tool.poetry.group.dev.dependencies]  # dependencies for CI (group name: dev)
black = "25.1.0"  # the version should be the same as in the .pre-commit-config.yaml
deepdiff = "8.1.1"
isort = "6.0.1"  # the version should be the same as in the .pre-commit-config.yaml
mixer = "7.2.2"
override-pydantic-settings = "1.0.1"
pytest = "8.3.4"
pytest-alembic = "0.11.1"
pytest-asyncio = "0.25.3"  # TODO: MRV-1514
pytest-cache = "1.0"
pytest-cov = "6.0.0"
pytest-env = "1.1.5"
pytest-httpx = "0.34.0"
pytest-mock = "3.14.0"
pytest-subtests = "0.14.1"
pytest-xdist = { extras = ["psutil"], version = "3.6.1" }
python-calamine = "0.3.1"
time-machine = "2.16.0"
pyinstrument = "*"

[tool.poetry.group.sql_dump.dependencies]
cachetools = "5.5.1"
certifi = "2024.2.2"
charset-normalizer = "3.4.1"
click = "8.1.3"
colorama = "0.4.6"
google-api-core = "2.24.1"
google-api-python-client = "2.160.0"
google-auth-httplib2 = "0.2.0"
google-cloud-core = "2.4.1"
google-crc32c = "1.6.0"
google-resumable-media = "2.7.2"
googleapis-common-protos = "1.66.0"
httplib2 = "0.22.0"
idna = "3.10"
oauth2client = "4.1.3"
protobuf = "5.29.3"
pyasn1 = "0.6.1"
pyasn1-modules = "0.4.0"
pymysql = "1.1.1"
pyparsing = "3.2.1"
python-dateutil = "2.9.0.post0"
pytzdata = "2020.1"
requests = "2.32.3"
rsa = "4.9"
six = "1.17.0"
tqdm = "4.67.1"
types-pymysql = "*"
uritemplate = "4.1.1"
urllib3 = "2.3.0"

[tool.poetry.group.local.dependencies]  # dependencies for local development (group name: local)
djlint = "*"
mypy = "1.15.0"  # the version should be the same as in the .pre-commit-config.yaml
pre-commit = "*"
pytest-pycharm = "*"
types-cachetools = "^5.4.0.20240717"
types-pymysql = "*"
types-python-dateutil = "*"
types-redis = "*"
types-requests = "*"

[tool.pytest.ini_options]
addopts = "-p no:warnings --ignore=src --ignore=integration"
asyncio_mode = "auto"
markers = ["methods: only run methods tests", "imports: only run imports tests"]
# See https://pytest-asyncio.readthedocs.io/en/stable/how-to-guides/change_default_fixture_loop.html#id2
asyncio_default_fixture_loop_scope = "session"

[tool.pytest_env]
REDIS_URL = "redis://localhost:6380/1"  # used by Celery
LOGGING_LEVEL = { value = "INFO", skip_if_set = true }
GCLOUD_SERVICE_ACCOUNT = ""
DNDC_OVERRIDE_PATH = "dndc_override.json"
ENABLE_DNDC = true
MTA_SERVICE_HOST = ""
PAPERFORM_API_KEY = "foo"

[tool.djlint]
ignore = "H021,H006,T032,H014"

[tool.black]
line-length = 120  # Line length discussion: https://regrowag.slack.com/archives/C0302UM9S1J/p1686897987363889
