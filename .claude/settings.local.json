{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(poetry run pytest:*)", "<PERSON><PERSON>(comm:*)", "WebFetch(domain:simplelocalize.io)", "<PERSON><PERSON>(poetry run alembic:*)", "<PERSON><PERSON>(python:*)", "Bash(make test_no_cov:*)", "Bash(msgfmt --version)", "Bash(msgfmt:*)", "Bash(LANG=C msgfmt /Users/<USER>/IdeaProjects/mrv-service/app/locale/fr/LC_MESSAGES/messages.po -o /Users/<USER>/IdeaProjects/mrv-service/app/locale/fr/LC_MESSAGES/messages.mo)", "Bash(echo \"Exit code: $?\")", "<PERSON><PERSON>(poetry run:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__browser-mcp__browser_navigate", "mcp__browser-mcp__browser_screenshot", "mcp__browser-mcp__browser_press_key", "mcp__browser-mcp__browser_click", "Bash(osascript:*)", "Bash(/Users/<USER>/focus-chrome.sh)", "mcp__playwright__browser_navigate", "mcp__sequential-thinking__sequentialthinking", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_press_key", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_close", "mcp__zen__challenge", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_press_key", "mcp__zen__analyze", "mcp__zen__codereview", "<PERSON><PERSON>(poetry lock:*)", "mcp__zen__chat"], "deny": []}, "enableAllProjectMcpServers": false}