name: PR Validation
on:
  workflow_dispatch:
  pull_request:
    types:
    - opened
    - synchronize
    - reopened
    - ready_for_review
permissions:
  contents: write
  checks: write
  pull-requests: write

jobs:
  precommit-and-unit-tests:
    name: Static Analysis/Linting/Prettifier and Unit Tests
    runs-on: [ubuntu-22.04-16core]    # Containers must run in Linux based operating systems
    if: ${{ github.event.pull_request.draft == false }}
    steps:
    - uses: actions/checkout@v4.1.2
      with:
        fetch-depth: 0
    - uses: actions/setup-python@v5
      with:
        python-version: 3.11.10
    - uses: pre-commit/action@v3.0.1
      with:
        extra_args: --color=always --from-ref ${{ github.event.pull_request.base.sha }} --to-ref ${{ github.event.pull_request.head.sha }}
      env:
        SKIP: refresh-mdl    # refresh-mdl required Poetry and python deps to be installed
    - name: Install poetry
      run: |
        python -m pip install --upgrade poetry==1.8.3 wheel
    - uses: actions/setup-python@v5
      with:
        python-version: 3.11.10
        architecture: x64
        cache: poetry
        cache-dependency-path: |
          poetry.lock
    - uses: webfactory/ssh-agent@v0.9.0
      with:
        ssh-private-key: ${{ secrets.GH_ACTIONS_SSH_KEY }}
    - name: Auth with GCP
      id: GCP_auth
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_REGISTRY_JSON_KEY }}
    - name: Install Deps
        # The self-hosted runner has a GCP Service Account attached to it which we rely on for Artifact Registry auth
      run: |
        poetry self add "keyrings.google-artifactregistry-auth"
        sudo apt-get update && sudo apt-get -y install --no-install-recommends pkg-config  # required for mysqlclient
        make deps_poetry_dev
    - run: poetry run pip list
    - uses: pre-commit/action@v3.0.1
      with:
        extra_args: --color=always --from-ref ${{ github.event.pull_request.base.sha }} --to-ref ${{ github.event.pull_request.head.sha }}
      env:
          # run refresh-mdl only
        SKIP: update-migrations, isort, black, bandit, flake8, check-added-large-files, trailing-whitespace, check-merge-conflict, check-case-conflict, check-symlinks, end-of-file-fixer, check-json, pretty-format-json, check-yaml, check-toml, pretty-format-yaml, mypy, djlint-jinja, remove-print-statements
    - name: Test alembic head, history, migrations == models
      run: |
        make alembic_tests
      env:
        RUN_MIGRATION_TESTS: true
    - name: Run DB Migration
      run: |
        make alembic_head_local
    - name: Run Tests
      run: |
        make gh_test
    - name: Pytest coverage comment
      id: coverageComment
      uses: MishaKav/pytest-coverage-comment@main
      with:
        pytest-xml-coverage-path: coverage.xml
        junitxml-path: pytest.xml
        hide-comment: true
          # Service containers to run with `container-job`
    - run: sed -i 's@'$GITHUB_WORKSPACE'@/github/workspace/@g' coverage.xml
    services:
      # Label used to access the service container
      mysql:
        # Docker Hub image
        image: mysql:8.1
        ports:
        - 3307:3306
        env:
          MYSQL_ROOT_PASSWORD: my-secret-pw
          MYSQL_DATABASE: mrv_service
      redis:
        image: redis:7.0
        ports:
        - 6380:6379

  sonarcube-analysis:
    name: SonarCloud Analysis
    runs-on: ubuntu-22.04
    if: ${{ github.event.pull_request.draft == false }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: SonarCloud Scan
      uses: sonarsource/sonarqube-scan-action@master
      timeout-minutes: 10
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  sync-migration-sql-script:
    name: Sync migration.sql script in mrv-db-ops repo
    runs-on: ubuntu-22.04
    if: ${{ github.event.pull_request.draft == false }}
    needs: precommit-and-unit-tests
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check if sql-dump/migration.sql has changed
      id: check_migration_sql
      run: |
        git fetch origin main:main
        if git diff --quiet main -- sql-dump/migration.sql; then
          echo "migration_sql_changed=false" >> $GITHUB_OUTPUT
          echo "No changes detected in sql-dump/migration.sql"
        else
          echo "migration_sql_changed=true" >> $GITHUB_OUTPUT
          echo "Changes detected in sql-dump/migration.sql:"
          git diff main -- sql-dump/migration.sql
        fi
    - name: Generate GitHub App token
      if: steps.check_migration_sql.outputs.migration_sql_changed == 'true'
      id: generate_token
      uses: actions/create-github-app-token@v1
      with:
        app-id: ${{ secrets.REGROW_BOT_APP_ID }}
        private-key: ${{ secrets.REGROW_BOT_APP_PRIVATE_KEY }}
        repositories: mrv-db-ops,mrv-service
        owner: ${{ github.repository_owner }}

    - name: Checkout mrv-db-ops repo
      if: steps.check_migration_sql.outputs.migration_sql_changed == 'true'
      uses: actions/checkout@v4
      with:
        repository: regrowag/mrv-db-ops
        token: ${{ steps.generate_token.outputs.token }}
        path: mrv-db-ops
        persist-credentials: false

    - name: Install GitHub CLI
      if: steps.check_migration_sql.outputs.migration_sql_changed == 'true'
      run: |
        type -p curl >/dev/null || (sudo apt update && sudo apt install curl -y)
        curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
        && sudo chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
        && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
        && sudo apt update \
        && sudo apt install gh -y

    - name: Generate PR for mrv-db-ops
      if: steps.check_migration_sql.outputs.migration_sql_changed == 'true'
      env:
        GH_TOKEN: ${{ steps.generate_token.outputs.token }}
      run: |
        cd mrv-db-ops
        branch_name="update-migration-sql-$(date +%Y%m%d%H%M%S)"
        git checkout -b $branch_name
        cp ../sql-dump/migration.sql dev-sql-dump/

        # Commit and push changes
        git remote remove origin
        git remote add origin https://x-access-token:${GH_TOKEN}@github.com/regrowag/mrv-db-ops
        git config user.name "Regrow Bot"
        git config user.email "<EMAIL>"
        git add dev-sql-dump/migration.sql
        git commit -m "Update migration.sql from MRV service"
        git push origin $branch_name

        # Create pull request using GitHub CLI
        pr_url=$(gh pr create --title "Update migration.sql from MRV service" \
          --body "This PR updates the migration.sql file based on changes in the MRV service.

          cc @${{ github.event.pull_request.user.login }}

          Related PR: ${{ github.event.pull_request.html_url }}" \
          --base main \
          --head $branch_name)

        echo "Pull request created: $pr_url"

        # Comment on the current PR
        gh pr comment ${{ github.event.pull_request.number }} --repo regrowag/mrv-service --body "A corresponding PR has been created in the mrv-db-ops repository to update the migration.sql file. \
        Please review and merge that PR as well.

        mrv-db-ops PR: $pr_url"

        # Clean up
        cd ..
        rm -rf mrv-db-ops

  metrics-collector:
    uses: regrowag/github-action-metrics-collectors/.github/workflows/metrics-collector.yaml@main
    secrets: inherit
    with:
      APP: mrv-service
      TYPE: test
      ENVIRONMENT: dev
      TEAM: mrv
