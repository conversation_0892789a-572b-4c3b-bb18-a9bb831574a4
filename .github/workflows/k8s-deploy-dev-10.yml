on:
  workflow_dispatch:
    inputs:
      BUILD_REF:
        default: main
        description: Branch, tag or commit SHA1 to build - default is main
        required: true
        type: string

name: k8s-deploy-dev-10

env:
  IS_ADDITIONAL: 'true'
  APP_ADDITIONAL_ID: '10'
  CONFIG_REPO: mrv-service-config

jobs:
  build_and_push:
    permissions:
      contents: read
      id-token: write
    secrets: inherit
    uses: regrowag/github-actions/.github/workflows/gitops-build-push.yml@main
    with:
      APP_NAME: mrv-service
      BUILD_REF: ${{ inputs.BUILD_REF }}
      ENVIRONMENT: dev
  gitops_updater:
    needs: build_and_push
    runs-on: ubuntu-latest
    environment: dev10
    steps:
    - uses: actions/create-github-app-token@v1
      id: app-token
      with:
        app-id: ${{ secrets.REGROW_BOT_APP_ID }}
        private-key: ${{ secrets.REGROW_BOT_APP_PRIVATE_KEY }}
        owner: ${{ github.repository_owner }}
    - uses: convictional/trigger-workflow-and-wait@v1.6.5
      with:
        owner: regrowag
        repo: ${{ env.CONFIG_REPO }}
        github_token: ${{ steps.app-token.outputs.token }}
        workflow_file_name: gitops-updater.yml
        ref: main
        wait_interval: 5
        client_payload: '{"APP_NAME": "${{ needs.build_and_push.outputs.APP_NAME }}", "IMAGE_TAG": "${{ needs.build_and_push.outputs.IMAGE_TAG }}", "APP_ENV": "${{ needs.build_and_push.outputs.APP_ENV }}", "IS_ADDITIONAL": "${{ env.IS_ADDITIONAL }}", "APP_ADDITIONAL_ID": "${{ env.APP_ADDITIONAL_ID }}"}'
        propagate_failure: true
        trigger_workflow: true
        wait_workflow: true
