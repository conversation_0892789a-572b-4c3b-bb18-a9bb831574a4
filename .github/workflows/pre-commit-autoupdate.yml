name: Pre-commit auto-update
on:
  schedule:
  - cron: 0 0 1 * *   # every first day of month at midnight
jobs:
  auto-update:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4.1.2
    - uses: actions/setup-python@v5
    - uses: browniebroke/pre-commit-autoupdate-action@main
    - uses: peter-evans/create-pull-request@v7
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        branch: update-pre-commit
        title: Update pre-commit hooks version
        commit-message: 'chore: update pre-commit hooks version'
        body: Update versions of pre-commit hooks to latest version.
