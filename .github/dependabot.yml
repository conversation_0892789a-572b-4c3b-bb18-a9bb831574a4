# there are 2 pip updates to work around "Dependabot timed out during its update"
version: 2
updates:
  - package-ecosystem: "pip"
    directory: "/"
    insecure-external-code-execution: allow  # allow to execute code from registries (regrow_py)
    schedule:
      interval: "monthly"
      time: "03:00"
      timezone: "UTC"
    open-pull-requests-limit: 25
    registries:
      - regrow_py
    reviewers:
      - "regrowag/mrv-backend"
    labels:
      - "dependency"
    groups:
      python-deps-half-1:
        patterns:  # package names start with letters C, c, G, g, P, p
          - "C*"
          - "c*"
          - "G*"
          - "g*"
          - "P*"
          - "p*"
    allow:
      - dependency-type: "direct"  # only update explicitly defined dependencies
    ignore:
      - dependency-name: "defaults-service*"  # ignore packages starting "defaults-service"
      - dependency-name: "scenarios-service*"  # ignore packages starting "scenarios-service"

  - package-ecosystem: "pip"
    directory: "/"
    target-branch: "main"  # specifying branch is a workaround to have 2 pip updates
    insecure-external-code-execution: allow  # allow to execute code from registries (regrow_py)
    schedule:
      interval: "monthly"
      time: "06:00"
      timezone: "UTC"
    open-pull-requests-limit: 25
    registries:
      - regrow_py
    reviewers:
      - "regrowag/mrv-backend"
    labels:
      - "dependency"
    groups:
      python-deps-half-2:
        exclude-patterns:  # package names start with any letter except C, c, G, g, P, p
          - "C*"
          - "c*"
          - "G*"
          - "g*"
          - "P*"
          - "p*"
    allow:
      - dependency-type: "direct"  # only update explicitly defined dependencies
    ignore:
      - dependency-name: "defaults-service*"  # ignore packages starting "defaults-service"
      - dependency-name: "scenarios-service*"  # ignore packages starting "scenarios-service"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
      time: "02:50"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "regrowag/mrv-backend"
    labels:
      - "ci"
      - "dependency"
    groups:
      github-actions-deps:
        patterns:
          - "*"  # all

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "monthly"
      time: "02:55"
      timezone: "UTC"
    reviewers:
      - "regrowag/mrv-backend"
    labels:
      - "ci"
      - "dependency"
    groups:
      docker-deps:
        patterns:
          - "*"  # all
        exclude-patterns:
          - "python*"  # ignore packages starting with "python"

registries:
  regrow_py:
    type: python-index
    url: https://us-west1-python.pkg.dev/flurosat-154904/regrow-python/simple
    username: _json_key_base64
    password: ${{secrets.GCLOUD_CREDS_JSON}}
    replaces-base: true
